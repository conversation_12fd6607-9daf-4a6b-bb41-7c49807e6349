/* Error toast styling */
.error-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #f8d7da;
    color: #721c24;
    padding: 12px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    max-width: 400px;
    font-size: 14px;
    line-height: 1.5;
    border-left: 4px solid #f5c6cb;
    animation: slide-in 0.3s ease-out forwards;
}

.error-toast.fade-out {
    animation: fade-out 0.5s ease-out forwards;
}

@keyframes slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fade-out {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}