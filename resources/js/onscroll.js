console.log("Hello from onscroll")
const fadeIn = document.querySelectorAll(".js-show-on-scroll");
const slideIn = document.querySelectorAll(".js-slide-on-scroll");


// Callback for IntersectionObserver
const fadeInCallback = function (entries) {
  entries.forEach((entry) => {
    // Is the element in the viewport?
    if (entry.isIntersecting) {
      // Add the fadeIn class:
      entry.target.classList.add("motion-safe:animate-fadeIn");
    } else {
      // Otherwise remove the fadein class
      entry.target.classList.remove("motion-safe:animate-fadeIn");
    }
  });
}

// Callback for IntersectionObserver
const slideInCallback = function (entries) {
  entries.forEach((entry) => {
    // Is the element in the viewport?
    if (entry.isIntersecting) {
      // Add the fadeIn class:
      entry.target.classList.add("motion-safe:animate-translateX");
    } else {
      // Otherwise remove the fadein class
      entry.target.classList.remove("motion-safe:animate-translateX");
    }
  });
}

// Set up a new observer
const fadeInObserver = new IntersectionObserver(fadeInCallback);
const slideInObserver = new IntersectionObserver(slideInCallback);


// Loop through each of the target
fadeIn.forEach(function (target) {
  // Hide the element
  target.classList.add("opacity-0");

  // Add the element to the watcher
  fadeInObserver.observe(target);
});

slideIn.forEach(function (target) {
  // Hide the element
  target.classList.add("translateX(-100%)");

  // Add the element to the watcher
  slideInObserver.observe(target);
});

document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
      // Check if the target section exists
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
          // Prevent default scroll behavior for links pointing to sections within the same page
          e.preventDefault();

          // Smooth scroll to the target section
          window.scrollTo({
              top: target.offsetTop,
              behavior: 'smooth'
          });
      }
  });
});


