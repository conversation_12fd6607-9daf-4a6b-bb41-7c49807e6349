/**
 * Modal Manager for improved modal state management
 * Handles proper modal mounting/unmounting and portal-based management
 */
class ModalManager {
    constructor() {
        this.modals = new Map();
        this.activeModal = null;
        this.portalContainer = null;
        this.bodyScrollPosition = 0;
        
        this.init();
    }
    
    init() {
        this.createPortalContainer();
        this.bindGlobalEvents();
        this.setupAccessibility();
    }
    
    createPortalContainer() {
        // Create or get existing portal container
        this.portalContainer = document.getElementById('modal-portal');
        if (!this.portalContainer) {
            this.portalContainer = document.createElement('div');
            this.portalContainer.id = 'modal-portal';
            this.portalContainer.className = 'modal-portal';
            this.portalContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1000;
            `;
            document.body.appendChild(this.portalContainer);
        }
    }
    
    bindGlobalEvents() {
        // ESC key handling
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeModal(this.activeModal.id);
            }
        });
        
        // Click outside handling
        this.portalContainer.addEventListener('click', (e) => {
            if (e.target === e.currentTarget && this.activeModal) {
                this.closeModal(this.activeModal.id);
            }
        });
        
        // Handle browser back button
        window.addEventListener('popstate', () => {
            if (this.activeModal) {
                this.closeModal(this.activeModal.id);
            }
        });
    }
    
    setupAccessibility() {
        // Ensure focus management and screen reader support
        this.portalContainer.setAttribute('role', 'dialog');
        this.portalContainer.setAttribute('aria-modal', 'true');
        this.portalContainer.setAttribute('aria-hidden', 'true');
    }
    
    registerModal(id, config = {}) {
        const modalConfig = {
            id,
            element: null,
            content: null,
            closeOnEscape: true,
            closeOnClickOutside: true,
            removeOnClose: true,
            restoreBodyScroll: true,
            focusTrap: true,
            animationDuration: 300,
            ...config
        };
        
        this.modals.set(id, modalConfig);
        return modalConfig;
    }
    
    openModal(id, content = null, options = {}) {
        // Close any existing modal first
        if (this.activeModal) {
            this.closeModal(this.activeModal.id);
        }
        
        let modal = this.modals.get(id);
        if (!modal) {
            modal = this.registerModal(id, options);
        }
        
        // Update modal configuration
        Object.assign(modal, options);
        
        // Set content if provided
        if (content) {
            modal.content = content;
        }
        
        // Create modal element
        modal.element = this.createModalElement(modal);
        
        // Store body scroll position and prevent scrolling
        this.bodyScrollPosition = window.pageYOffset;
        document.body.style.cssText = `
            position: fixed;
            top: -${this.bodyScrollPosition}px;
            left: 0;
            right: 0;
            overflow: hidden;
        `;
        
        // Mount modal to portal
        this.portalContainer.appendChild(modal.element);
        this.portalContainer.style.pointerEvents = 'auto';
        this.portalContainer.setAttribute('aria-hidden', 'false');
        
        // Trigger animation
        requestAnimationFrame(() => {
            modal.element.classList.add('modal-open');
            this.setupFocusTrap(modal);
        });
        
        // Set as active modal
        this.activeModal = modal;
        
        // Emit open event
        this.emitModalEvent('modalOpen', modal);
        
        return modal;
    }
    
    closeModal(id) {
        const modal = this.modals.get(id);
        if (!modal || !modal.element) return;
        
        // Start close animation
        modal.element.classList.remove('modal-open');
        modal.element.classList.add('modal-closing');
        
        // Emit closing event
        this.emitModalEvent('modalClosing', modal);
        
        // Wait for animation to complete
        setTimeout(() => {
            this.destroyModal(modal);
        }, modal.animationDuration);
    }
    
    destroyModal(modal) {
        if (!modal.element) return;
        
        // Remove from DOM
        if (modal.element.parentNode) {
            modal.element.parentNode.removeChild(modal.element);
        }
        
        // Restore body scroll
        if (modal.restoreBodyScroll) {
            document.body.style.cssText = '';
            window.scrollTo(0, this.bodyScrollPosition);
        }
        
        // Update portal state
        this.portalContainer.style.pointerEvents = 'none';
        this.portalContainer.setAttribute('aria-hidden', 'true');
        
        // Clear modal references
        modal.element = null;
        this.activeModal = null;
        
        // Remove from registry if configured
        if (modal.removeOnClose) {
            this.modals.delete(modal.id);
        }
        
        // Emit closed event
        this.emitModalEvent('modalClosed', modal);
    }
    
    createModalElement(modal) {
        const modalEl = document.createElement('div');
        modalEl.className = 'modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 transition-opacity duration-300 opacity-0';
        modalEl.id = `modal-${modal.id}`;
        
        const modalContent = document.createElement('div');
        modalContent.className = `modal-content bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-hidden transform transition-all duration-300 scale-95 ${modal.className || ''}`;
        modalContent.setAttribute('role', 'dialog');
        modalContent.setAttribute('aria-labelledby', `modal-title-${modal.id}`);
        
        if (typeof modal.content === 'string') {
            modalContent.innerHTML = modal.content;
        } else if (modal.content instanceof HTMLElement) {
            modalContent.appendChild(modal.content);
        } else if (modal.content) {
            modalContent.innerHTML = modal.content.toString();
        }
        
        modalEl.appendChild(modalContent);
        
        // Add close button if not present
        if (!modalContent.querySelector('.modal-close')) {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'modal-close absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors';
            closeBtn.innerHTML = `
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            `;
            closeBtn.addEventListener('click', () => this.closeModal(modal.id));
            modalContent.appendChild(closeBtn);
        }
        
        return modalEl;
    }
    
    setupFocusTrap(modal) {
        if (!modal.focusTrap) return;
        
        const focusableElements = modal.element.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length === 0) return;
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        // Focus first element
        firstElement.focus();
        
        // Trap focus within modal
        const handleTabKey = (e) => {
            if (e.key !== 'Tab') return;
            
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                }
            } else {
                if (document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        };
        
        modal.element.addEventListener('keydown', handleTabKey);
        modal.focusTrapHandler = handleTabKey;
    }
    
    emitModalEvent(eventName, modal) {
        const event = new CustomEvent(eventName, {
            detail: { 
                modal,
                modalId: modal.id,
                timestamp: Date.now()
            }
        });
        document.dispatchEvent(event);
    }
    
    isModalOpen(id) {
        return this.activeModal && this.activeModal.id === id;
    }
    
    getActiveModal() {
        return this.activeModal;
    }
    
    closeAllModals() {
        if (this.activeModal) {
            this.closeModal(this.activeModal.id);
        }
    }
    
    updateModalContent(id, content) {
        const modal = this.modals.get(id);
        if (!modal || !modal.element) return;
        
        const contentEl = modal.element.querySelector('.modal-content');
        if (contentEl) {
            if (typeof content === 'string') {
                contentEl.innerHTML = content;
            } else if (content instanceof HTMLElement) {
                contentEl.innerHTML = '';
                contentEl.appendChild(content);
            }
        }
    }
    
    destroy() {
        this.closeAllModals();
        if (this.portalContainer && this.portalContainer.parentNode) {
            this.portalContainer.parentNode.removeChild(this.portalContainer);
        }
        this.modals.clear();
    }
}

// Create global instance
window.modalManager = new ModalManager();

// Add CSS styles for modal animations
const modalStyles = document.createElement('style');
modalStyles.textContent = `
    .modal-backdrop.modal-open {
        opacity: 1;
    }
    
    .modal-backdrop.modal-open .modal-content {
        transform: scale(1);
    }
    
    .modal-backdrop.modal-closing {
        opacity: 0;
    }
    
    .modal-backdrop.modal-closing .modal-content {
        transform: scale(0.95);
    }
    
    .modal-content {
        position: relative;
    }
    
    .modal-portal {
        contain: layout style paint;
    }
`;
document.head.appendChild(modalStyles);