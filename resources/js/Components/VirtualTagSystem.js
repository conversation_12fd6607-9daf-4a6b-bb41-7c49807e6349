/**
 * Virtual Tag System for Requirements Filter
 * Handles large numbers of filter tags with virtualization and overflow management
 */
class VirtualTagSystem {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            maxVisibleTags: 8,
            tagHeight: 32,
            animationDuration: 200,
            showMoreText: 'Show More',
            showLessText: 'Show Less',
            ...options
        };
        
        this.tags = [];
        this.visibleTags = [];
        this.isExpanded = false;
        this.searchQuery = '';
        
        this.init();
    }
    
    init() {
        this.createStructure();
        this.bindEvents();
        this.render();
    }
    
    createStructure() {
        this.container.innerHTML = `
            <div class="tag-system-wrapper">
                <div class="tag-search-container mb-2" style="display: none;">
                    <input 
                        type="text" 
                        class="tag-search-input px-3 py-1 border rounded-md text-sm w-full" 
                        placeholder="Search requirements..."
                    >
                </div>
                <div class="tag-container flex flex-wrap gap-2 transition-all duration-200">
                    <!-- Tags will be rendered here -->
                </div>
                <div class="tag-controls mt-2" style="display: none;">
                    <button class="toggle-btn text-sm text-blue-600 hover:text-blue-800 transition-colors">
                        Show More
                    </button>
                    <span class="tag-count text-sm text-gray-500 ml-2"></span>
                </div>
            </div>
        `;
        
        this.tagContainer = this.container.querySelector('.tag-container');
        this.searchContainer = this.container.querySelector('.tag-search-container');
        this.searchInput = this.container.querySelector('.tag-search-input');
        this.controlsContainer = this.container.querySelector('.tag-controls');
        this.toggleBtn = this.container.querySelector('.toggle-btn');
        this.tagCount = this.container.querySelector('.tag-count');
    }
    
    bindEvents() {
        // Toggle expand/collapse
        this.toggleBtn.addEventListener('click', () => {
            this.toggle();
        });
        
        // Search functionality with debouncing
        let searchTimeout;
        this.searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.search(e.target.value);
            }, 300);
        });
        
        // Tag click events (delegated)
        this.tagContainer.addEventListener('click', (e) => {
            const tag = e.target.closest('.filter-tag');
            if (tag) {
                this.handleTagClick(tag);
            }
        });
    }
    
    setTags(tags) {
        this.tags = tags.map((tag, index) => ({
            id: tag.id || index,
            text: tag.text || tag.name || tag,
            active: tag.active || false,
            count: tag.count || 0,
            category: tag.category || 'default'
        }));
        
        this.updateVisibility();
        this.render();
    }
    
    updateVisibility() {
        const shouldShowControls = this.tags.length > this.options.maxVisibleTags;
        
        this.controlsContainer.style.display = shouldShowControls ? 'block' : 'none';
        this.searchContainer.style.display = shouldShowControls ? 'block' : 'none';
        
        if (shouldShowControls) {
            this.tagCount.textContent = `${this.tags.length} total`;
        }
    }
    
    getVisibleTags() {
        let filteredTags = this.tags;
        
        // Apply search filter
        if (this.searchQuery) {
            filteredTags = this.tags.filter(tag => 
                tag.text.toLowerCase().includes(this.searchQuery.toLowerCase())
            );
        }
        
        // Apply visibility limit
        if (!this.isExpanded && !this.searchQuery) {
            filteredTags = filteredTags.slice(0, this.options.maxVisibleTags);
        }
        
        return filteredTags;
    }
    
    render() {
        const visibleTags = this.getVisibleTags();
        
        // Use DocumentFragment for efficient DOM manipulation
        const fragment = document.createDocumentFragment();
        
        visibleTags.forEach(tag => {
            const tagElement = this.createTagElement(tag);
            fragment.appendChild(tagElement);
        });
        
        // Batch DOM update
        requestAnimationFrame(() => {
            this.tagContainer.innerHTML = '';
            this.tagContainer.appendChild(fragment);
            
            // Update controls
            this.updateControls();
        });
    }
    
    createTagElement(tag) {
        const tagEl = document.createElement('button');
        tagEl.className = `filter-tag inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-all duration-150 ${
            tag.active 
                ? 'bg-blue-600 text-white shadow-sm' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`;
        tagEl.dataset.tagId = tag.id;
        
        tagEl.innerHTML = `
            <span class="tag-text">${this.escapeHtml(tag.text)}</span>
            ${tag.count > 0 ? `<span class="tag-count ml-1 text-xs opacity-75">${tag.count}</span>` : ''}
            ${tag.active ? '<svg class="ml-1 w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg>' : ''}
        `;
        
        return tagEl;
    }
    
    updateControls() {
        const totalTags = this.tags.length;
        const visibleCount = this.getVisibleTags().length;
        
        if (this.searchQuery) {
            this.toggleBtn.style.display = 'none';
            this.tagCount.textContent = `${visibleCount} of ${totalTags}`;
        } else {
            this.toggleBtn.style.display = totalTags > this.options.maxVisibleTags ? 'inline' : 'none';
            this.toggleBtn.textContent = this.isExpanded ? this.options.showLessText : this.options.showMoreText;
            this.tagCount.textContent = this.isExpanded ? `${totalTags} total` : `${visibleCount} of ${totalTags}`;
        }
    }
    
    toggle() {
        this.isExpanded = !this.isExpanded;
        this.render();
        
        // Emit custom event
        this.container.dispatchEvent(new CustomEvent('tagSystemToggle', {
            detail: { expanded: this.isExpanded }
        }));
    }
    
    search(query) {
        this.searchQuery = query.trim();
        this.render();
        
        // Emit custom event
        this.container.dispatchEvent(new CustomEvent('tagSystemSearch', {
            detail: { query: this.searchQuery }
        }));
    }
    
    handleTagClick(tagElement) {
        const tagId = tagElement.dataset.tagId;
        const tag = this.tags.find(t => t.id == tagId);
        
        if (tag) {
            tag.active = !tag.active;
            this.render();
            
            // Emit custom event
            this.container.dispatchEvent(new CustomEvent('tagClick', {
                detail: { tag, active: tag.active }
            }));
        }
    }
    
    getActiveTags() {
        return this.tags.filter(tag => tag.active);
    }
    
    setActiveTagsById(tagIds) {
        this.tags.forEach(tag => {
            tag.active = tagIds.includes(tag.id);
        });
        this.render();
    }
    
    clearAllTags() {
        this.tags.forEach(tag => tag.active = false);
        this.render();
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    destroy() {
        this.container.innerHTML = '';
        this.tags = [];
        this.visibleTags = [];
    }
}

// Export for use in other modules
window.VirtualTagSystem = VirtualTagSystem;