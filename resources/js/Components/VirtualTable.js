/**
 * Virtual Scrolling Table Component
 * Handles large datasets efficiently by only rendering visible rows
 */
class VirtualTable {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            itemHeight: 60, // Height of each row in pixels
            bufferSize: 5, // Number of extra rows to render outside viewport
            threshold: 100, // Threshold for loading more data
            ...options
        };
        
        this.data = [];
        this.filteredData = [];
        this.startIndex = 0;
        this.endIndex = 0;
        this.scrollTop = 0;
        this.containerHeight = 0;
        this.visibleCount = 0;
        
        this.init();
    }
    
    init() {
        this.setupContainer();
        this.bindEvents();
        this.calculateVisibleRange();
    }
    
    setupContainer() {
        this.container.style.position = 'relative';
        this.container.style.overflow = 'auto';
        this.container.style.height = this.options.maxHeight || '400px';
        
        // Create viewport
        this.viewport = document.createElement('div');
        this.viewport.style.position = 'absolute';
        this.viewport.style.top = '0';
        this.viewport.style.left = '0';
        this.viewport.style.right = '0';
        this.viewport.style.minHeight = '100%';
        
        // Create spacer for total height
        this.spacer = document.createElement('div');
        this.spacer.style.height = '1px';
        
        this.container.appendChild(this.viewport);
        this.container.appendChild(this.spacer);
        
        this.containerHeight = this.container.clientHeight;
        this.visibleCount = Math.ceil(this.containerHeight / this.options.itemHeight);
    }
    
    bindEvents() {
        this.container.addEventListener('scroll', this.handleScroll.bind(this));
        window.addEventListener('resize', this.handleResize.bind(this));
    }
    
    handleScroll() {
        this.scrollTop = this.container.scrollTop;
        this.calculateVisibleRange();
        this.render();
        
        // Check if we need to load more data
        const scrollBottom = this.scrollTop + this.containerHeight;
        const totalHeight = this.filteredData.length * this.options.itemHeight;
        
        if (scrollBottom > totalHeight - this.options.threshold) {
            this.onLoadMore && this.onLoadMore();
        }
    }
    
    handleResize() {
        this.containerHeight = this.container.clientHeight;
        this.visibleCount = Math.ceil(this.containerHeight / this.options.itemHeight);
        this.calculateVisibleRange();
        this.render();
    }
    
    calculateVisibleRange() {
        this.startIndex = Math.max(0, Math.floor(this.scrollTop / this.options.itemHeight) - this.options.bufferSize);
        this.endIndex = Math.min(
            this.filteredData.length - 1,
            this.startIndex + this.visibleCount + (this.options.bufferSize * 2)
        );
    }
    
    setData(data) {
        this.data = data;
        this.filteredData = [...data];
        this.updateSpacer();
        this.calculateVisibleRange();
        this.render();
    }
    
    appendData(newData) {
        this.data = [...this.data, ...newData];
        this.filteredData = [...this.filteredData, ...newData];
        this.updateSpacer();
        this.render();
    }
    
    filter(predicate) {
        this.filteredData = this.data.filter(predicate);
        this.updateSpacer();
        this.container.scrollTop = 0;
        this.scrollTop = 0;
        this.calculateVisibleRange();
        this.render();
    }
    
    updateSpacer() {
        const totalHeight = this.filteredData.length * this.options.itemHeight;
        this.spacer.style.height = `${totalHeight}px`;
    }
    
    render() {
        const visibleItems = this.filteredData.slice(this.startIndex, this.endIndex + 1);
        
        // Clear viewport
        this.viewport.innerHTML = '';
        
        // Create container for visible items
        const itemsContainer = document.createElement('div');
        itemsContainer.style.transform = `translateY(${this.startIndex * this.options.itemHeight}px)`;
        
        // Render visible items
        visibleItems.forEach((item, index) => {
            const element = this.renderItem(item, this.startIndex + index);
            if (element) {
                element.style.height = `${this.options.itemHeight}px`;
                itemsContainer.appendChild(element);
            }
        });
        
        this.viewport.appendChild(itemsContainer);
    }
    
    renderItem(item, index) {
        // This should be overridden by the implementation
        const element = document.createElement('div');
        element.className = 'virtual-table-row';
        element.innerHTML = `
            <div class="flex items-center p-4 border-b hover:bg-gray-50 cursor-pointer">
                <div class="flex-1">
                    <div class="font-medium">${item.first_name} ${item.last_name}</div>
                    <div class="text-sm text-gray-500">${item.latest_role}</div>
                </div>
                <div class="flex-1 text-sm text-gray-500">${item.company_name}</div>
                <div class="flex-shrink-0">
                    <button class="text-blue-600 hover:text-blue-800 text-sm" onclick="showCandidateProfile(${item.id})">
                        View Profile
                    </button>
                </div>
            </div>
        `;
        return element;
    }
    
    scrollToIndex(index) {
        const targetScrollTop = index * this.options.itemHeight;
        this.container.scrollTop = targetScrollTop;
    }
    
    destroy() {
        this.container.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleResize);
    }
}

// Export for use in Alpine.js components
window.VirtualTable = VirtualTable;