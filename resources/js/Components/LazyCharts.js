/**
 * Lazy Chart Loading Component
 * Handles progressive chart loading and rendering optimizations
 */
class LazyCharts {
    constructor() {
        this.charts = new Map();
        this.observer = null;
        this.chartQueue = [];
        this.isProcessing = false;
        
        this.init();
    }
    
    init() {
        this.setupIntersectionObserver();
        this.setupResizeObserver();
    }
    
    setupIntersectionObserver() {
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const chartContainer = entry.target;
                    const chartType = chartContainer.dataset.chart;
                    
                    if (chartType && !this.charts.has(chartType)) {
                        this.queueChart(chartType, chartContainer);
                    }
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });
    }
    
    setupResizeObserver() {
        if (window.ResizeObserver) {
            this.resizeObserver = new ResizeObserver(entries => {
                entries.forEach(entry => {
                    const chartType = entry.target.dataset.chart;
                    if (chartType && this.charts.has(chartType)) {
                        this.debounce(() => this.resizeChart(chartType), 300)();
                    }
                });
            });
        }
    }
    
    observe(element) {
        this.observer.observe(element);
        if (this.resizeObserver) {
            this.resizeObserver.observe(element);
        }
    }
    
    queueChart(chartType, container) {
        this.chartQueue.push({ chartType, container });
        this.processQueue();
    }
    
    async processQueue() {
        if (this.isProcessing || this.chartQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        // Process charts one by one to avoid blocking the main thread
        while (this.chartQueue.length > 0) {
            const { chartType, container } = this.chartQueue.shift();
            
            try {
                await this.loadChart(chartType, container);
                // Add small delay to prevent blocking
                await new Promise(resolve => setTimeout(resolve, 50));
            } catch (error) {
                console.error(`Failed to load chart: ${chartType}`, error);
            }
        }
        
        this.isProcessing = false;
    }
    
    async loadChart(chartType, container) {
        // Show loading skeleton
        this.showChartSkeleton(container);
        
        try {
            // Fetch chart data
            const data = await this.fetchChartData(chartType);
            
            // Render chart
            const chart = await this.renderChart(chartType, container, data);
            
            // Store chart instance
            this.charts.set(chartType, {
                instance: chart,
                container: container,
                data: data
            });
            
            // Hide skeleton
            this.hideChartSkeleton(container);
            
        } catch (error) {
            this.showChartError(container, error);
        }
    }
    
    showChartSkeleton(container) {
        const skeleton = document.createElement('div');
        skeleton.className = 'chart-skeleton animate-pulse';
        skeleton.innerHTML = `
            <div class="w-full h-64 bg-gray-200 rounded-lg mb-4"></div>
            <div class="space-y-2">
                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
        `;
        container.appendChild(skeleton);
    }
    
    hideChartSkeleton(container) {
        const skeleton = container.querySelector('.chart-skeleton');
        if (skeleton) {
            skeleton.remove();
        }
    }
    
    showChartError(container, error) {
        this.hideChartSkeleton(container);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'chart-error text-center p-8 text-gray-500';
        errorDiv.innerHTML = `
            <div class="mb-2">⚠️</div>
            <div>Failed to load chart</div>
            <button class="mt-2 text-blue-600 hover:text-blue-800 text-sm" onclick="location.reload()">
                Retry
            </button>
        `;
        container.appendChild(errorDiv);
    }
    
    async fetchChartData(chartType) {
        // This should be called via Livewire
        return new Promise((resolve) => {
            if (window.Livewire) {
                // Dispatch Livewire event to load chart data
                window.Livewire.dispatch('loadChartData', { chartType });
                
                // Listen for response
                const handler = (event) => {
                    if (event.detail.chartType === chartType) {
                        document.removeEventListener('chartDataLoaded', handler);
                        resolve(event.detail.data);
                    }
                };
                
                document.addEventListener('chartDataLoaded', handler);
            } else {
                // Fallback for testing
                resolve(this.getMockData(chartType));
            }
        });
    }
    
    async renderChart(chartType, container, data) {
        const chartCanvas = document.createElement('canvas');
        chartCanvas.style.maxHeight = '300px';
        container.appendChild(chartCanvas);
        
        // Use Chart.js or ApexCharts based on preference
        if (window.Chart) {
            return this.renderChartJS(chartType, chartCanvas, data);
        } else if (window.ApexCharts) {
            return this.renderApexChart(chartType, container, data);
        } else {
            throw new Error('No charting library available');
        }
    }
    
    renderChartJS(chartType, canvas, data) {
        const config = this.getChartJSConfig(chartType, data);
        return new Chart(canvas, config);
    }
    
    renderApexChart(chartType, container, data) {
        const options = this.getApexChartOptions(chartType, data);
        const chart = new ApexCharts(container, options);
        chart.render();
        return chart;
    }
    
    getChartJSConfig(chartType, data) {
        const baseConfig = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        };
        
        switch (chartType) {
            case 'company':
                return {
                    type: 'bar',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            label: 'Companies',
                            data: data.data,
                            backgroundColor: '#3B82F6',
                            borderColor: '#2563EB',
                            borderWidth: 1
                        }]
                    },
                    options: baseConfig
                };
                
            case 'gender':
                return {
                    type: 'doughnut',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            data: data.data.map(item => item.y),
                            backgroundColor: data.data.map(item => item.fillColor)
                        }]
                    },
                    options: baseConfig
                };
                
            case 'function':
                return {
                    type: 'bar',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            label: 'Functions',
                            data: data.data,
                            backgroundColor: '#10B981',
                            borderColor: '#059669',
                            borderWidth: 1
                        }]
                    },
                    options: baseConfig
                };
                
            default:
                return {
                    type: 'bar',
                    data: {
                        labels: data.labels || [],
                        datasets: [{
                            label: 'Data',
                            data: data.data || [],
                            backgroundColor: '#6366F1'
                        }]
                    },
                    options: baseConfig
                };
        }
    }
    
    resizeChart(chartType) {
        const chartData = this.charts.get(chartType);
        if (chartData && chartData.instance) {
            if (chartData.instance.resize) {
                chartData.instance.resize();
            } else if (chartData.instance.updateOptions) {
                chartData.instance.updateOptions({});
            }
        }
    }
    
    updateChart(chartType, newData) {
        const chartData = this.charts.get(chartType);
        if (chartData && chartData.instance) {
            if (chartData.instance.data) {
                // Chart.js
                chartData.instance.data = newData;
                chartData.instance.update();
            } else if (chartData.instance.updateOptions) {
                // ApexCharts
                chartData.instance.updateOptions(this.getApexChartOptions(chartType, newData));
            }
        }
    }
    
    getMockData(chartType) {
        // Mock data for testing
        switch (chartType) {
            case 'company':
                return {
                    labels: ['Microsoft', 'Oracle', 'IBM'],
                    data: [15, 22, 8]
                };
            case 'gender':
                return {
                    labels: ['Male', 'Female'],
                    data: [
                        { x: 'Male', y: 35, fillColor: '#3B82F6' },
                        { x: 'Female', y: 15, fillColor: '#FFA347' }
                    ]
                };
            default:
                return { labels: [], data: [] };
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        
        this.charts.forEach(chart => {
            if (chart.instance && chart.instance.destroy) {
                chart.instance.destroy();
            }
        });
        
        this.charts.clear();
    }
}

// Initialize lazy charts when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.lazyCharts = new LazyCharts();
    
    // Observe all chart containers
    document.querySelectorAll('[data-chart]').forEach(element => {
        window.lazyCharts.observe(element);
    });
});

// Export for use in Alpine.js
window.LazyCharts = LazyCharts;