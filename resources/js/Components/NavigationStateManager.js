/**
 * Navigation State Manager
 * Handles navigation state persistence and optimization for better UX
 */
class NavigationStateManager {
    constructor(options = {}) {
        this.options = {
            storageKey: 'navigation_state',
            maxHistoryLength: 10,
            debounceDelay: 300,
            enablePrefetch: true,
            cacheSize: 50,
            ...options
        };
        
        this.state = {
            currentPage: null,
            history: [],
            filters: {},
            scrollPositions: {},
            tableStates: {},
            expandedSections: new Set(),
            lastUpdate: Date.now()
        };
        
        this.cache = new Map();
        this.prefetchQueue = new Set();
        this.debounceTimers = new Map();
        
        this.init();
    }
    
    init() {
        this.loadState();
        this.bindEvents();
        this.setupIntersectionObserver();
        this.startPeriodicSave();
    }
    
    loadState() {
        try {
            const stored = localStorage.getItem(this.options.storageKey);
            if (stored) {
                const parsedState = JSON.parse(stored);
                // Merge with default state, converting Sets properly
                this.state = {
                    ...this.state,
                    ...parsedState,
                    expandedSections: new Set(parsedState.expandedSections || [])
                };
            }
        } catch (error) {
            console.warn('Failed to load navigation state:', error);
        }
    }
    
    saveState() {
        try {
            const stateToSave = {
                ...this.state,
                expandedSections: Array.from(this.state.expandedSections)
            };
            localStorage.setItem(this.options.storageKey, JSON.stringify(stateToSave));
        } catch (error) {
            console.warn('Failed to save navigation state:', error);
        }
    }
    
    bindEvents() {
        // Save scroll position before navigation
        window.addEventListener('beforeunload', () => {
            this.saveScrollPosition();
            this.saveState();
        });
        
        // Handle popstate for browser back/forward
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.navigationState) {
                this.restoreState(e.state.navigationState);
            }
        });
        
        // Handle page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.saveState();
            }
        });
        
        // Handle focus events for prefetching
        document.addEventListener('focusin', (e) => {
            const link = e.target.closest('a[href]');
            if (link && this.options.enablePrefetch) {
                this.queuePrefetch(link.href);
            }
        });
        
        // Handle filter changes
        document.addEventListener('filterChange', (e) => {
            this.updateFilters(e.detail);
        });
    }
    
    setupIntersectionObserver() {
        // Observer for detecting section visibility
        this.sectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const sectionId = entry.target.id || entry.target.dataset.section;
                if (sectionId) {
                    if (entry.isIntersecting) {
                        this.state.expandedSections.add(sectionId);
                    }
                }
            });
        }, { threshold: 0.1 });
        
        // Observe all sections
        document.querySelectorAll('[data-section], section[id]').forEach(section => {
            this.sectionObserver.observe(section);
        });
    }
    
    setCurrentPage(pageId, pageData = {}) {
        // Add to history
        if (this.state.currentPage && this.state.currentPage !== pageId) {
            this.state.history.unshift({
                pageId: this.state.currentPage,
                timestamp: Date.now(),
                ...this.getCurrentPageState()
            });
            
            // Limit history length
            if (this.state.history.length > this.options.maxHistoryLength) {
                this.state.history = this.state.history.slice(0, this.options.maxHistoryLength);
            }
        }
        
        this.state.currentPage = pageId;
        this.state.lastUpdate = Date.now();
        
        // Update browser history
        const currentState = { navigationState: { ...this.state } };
        history.replaceState(currentState, '', window.location.href);
        
        this.debouncedSave();
    }
    
    updateFilters(filters) {
        this.state.filters = { ...this.state.filters, ...filters };
        this.state.lastUpdate = Date.now();
        this.debouncedSave();
        
        // Emit event for components to react
        document.dispatchEvent(new CustomEvent('navigationStateUpdate', {
            detail: { type: 'filters', filters: this.state.filters }
        }));
    }
    
    updateTableState(tableId, tableState) {
        this.state.tableStates[tableId] = {
            ...this.state.tableStates[tableId],
            ...tableState,
            lastUpdate: Date.now()
        };
        this.debouncedSave();
    }
    
    saveScrollPosition(elementId = 'main') {
        const element = elementId === 'main' ? window : document.getElementById(elementId);
        if (element) {
            const scrollTop = element === window ? window.pageYOffset : element.scrollTop;
            this.state.scrollPositions[elementId] = scrollTop;
        }
    }
    
    restoreScrollPosition(elementId = 'main') {
        const scrollTop = this.state.scrollPositions[elementId];
        if (typeof scrollTop === 'number') {
            const element = elementId === 'main' ? window : document.getElementById(elementId);
            if (element) {
                if (element === window) {
                    window.scrollTo(0, scrollTop);
                } else {
                    element.scrollTop = scrollTop;
                }
            }
        }
    }
    
    toggleSection(sectionId) {
        if (this.state.expandedSections.has(sectionId)) {
            this.state.expandedSections.delete(sectionId);
        } else {
            this.state.expandedSections.add(sectionId);
        }
        this.debouncedSave();
    }
    
    isSectionExpanded(sectionId) {
        return this.state.expandedSections.has(sectionId);
    }
    
    getCurrentPageState() {
        return {
            filters: { ...this.state.filters },
            scrollPositions: { ...this.state.scrollPositions },
            tableStates: { ...this.state.tableStates },
            expandedSections: Array.from(this.state.expandedSections)
        };
    }
    
    restoreState(stateData) {
        this.state = {
            ...this.state,
            ...stateData,
            expandedSections: new Set(stateData.expandedSections || [])
        };
        
        // Restore scroll positions
        Object.keys(this.state.scrollPositions).forEach(elementId => {
            this.restoreScrollPosition(elementId);
        });
        
        // Emit restoration event
        document.dispatchEvent(new CustomEvent('navigationStateRestore', {
            detail: { state: this.state }
        }));
    }
    
    queuePrefetch(url) {
        if (this.prefetchQueue.has(url) || this.cache.has(url)) return;
        
        this.prefetchQueue.add(url);
        
        // Debounce prefetch execution
        const timerId = this.debounceTimers.get('prefetch');
        if (timerId) clearTimeout(timerId);
        
        this.debounceTimers.set('prefetch', setTimeout(() => {
            this.executePrefetch(url);
        }, 100));
    }
    
    async executePrefetch(url) {
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: { 'X-Prefetch': 'true' }
            });
            
            if (response.ok) {
                const data = await response.text();
                this.addToCache(url, data);
            }
        } catch (error) {
            console.debug('Prefetch failed for:', url, error);
        } finally {
            this.prefetchQueue.delete(url);
        }
    }
    
    addToCache(key, data) {
        // Implement LRU cache
        if (this.cache.size >= this.options.cacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }
    
    getFromCache(key, maxAge = 300000) { // 5 minutes default
        const cached = this.cache.get(key);
        if (cached && (Date.now() - cached.timestamp) < maxAge) {
            return cached.data;
        }
        this.cache.delete(key);
        return null;
    }
    
    debouncedSave() {
        const timerId = this.debounceTimers.get('save');
        if (timerId) clearTimeout(timerId);
        
        this.debounceTimers.set('save', setTimeout(() => {
            this.saveState();
        }, this.options.debounceDelay));
    }
    
    startPeriodicSave() {
        // Save state every 30 seconds
        setInterval(() => {
            this.saveState();
        }, 30000);
    }
    
    getHistory() {
        return [...this.state.history];
    }
    
    goBack() {
        if (this.state.history.length > 0) {
            const previousState = this.state.history.shift();
            this.restoreState(previousState);
            window.history.back();
        }
    }
    
    clearHistory() {
        this.state.history = [];
        this.saveState();
    }
    
    clearState() {
        this.state = {
            currentPage: null,
            history: [],
            filters: {},
            scrollPositions: {},
            tableStates: {},
            expandedSections: new Set(),
            lastUpdate: Date.now()
        };
        this.saveState();
    }
    
    // Public API for components
    getFilters() {
        return { ...this.state.filters };
    }
    
    getTableState(tableId) {
        return this.state.tableStates[tableId] || {};
    }
    
    destroy() {
        this.saveState();
        this.sectionObserver?.disconnect();
        this.debounceTimers.forEach(timerId => clearTimeout(timerId));
        this.cache.clear();
        this.prefetchQueue.clear();
    }
}

// Create global instance
window.navigationStateManager = new NavigationStateManager();

// Export for module use
window.NavigationStateManager = NavigationStateManager;