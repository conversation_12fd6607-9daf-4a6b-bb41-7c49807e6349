/**
 * Frontend error handling utilities
 */
const errorHandler = {
    /**
     * Initialize the error handler
     */
    init() {
        // Add global error handling for JS errors
        window.addEventListener('error', this.handleGlobalError.bind(this));
        
        // Add promise rejection handling
        window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));
        
        // Patch fetch to handle API errors
        this.patchFetch();
        
        // Patch axios if available
        if (window.axios) {
            this.patchAxios();
        }
    },
    
    /**
     * Handle global JS errors
     * @param {ErrorEvent} event 
     */
    handleGlobalError(event) {
        const error = {
            message: event.message,
            source: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error?.stack,
            timestamp: new Date().toISOString()
        };
        
        // Log client-side error to server
        this.logErrorToServer('js_error', error);
        
        // Optionally show user-friendly message for critical errors
        // if (isCriticalError(error)) {
        //     this.showErrorMessage('Something went wrong. Please try refreshing the page.');
        // }
    },
    
    /**
     * Handle unhandled promise rejections
     * @param {PromiseRejectionEvent} event 
     */
    handlePromiseRejection(event) {
        const error = {
            message: event.reason?.message || 'Promise rejection',
            stack: event.reason?.stack,
            timestamp: new Date().toISOString()
        };
        
        // Log client-side error to server
        this.logErrorToServer('promise_rejection', error);
    },
    
    /**
     * Patch the fetch API to handle errors
     */
    patchFetch() {
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                
                // Handle API error responses
                if (!response.ok) {
                    let errorData;
                    try {
                        errorData = await response.clone().json();
                    } catch (e) {
                        errorData = { message: 'API error' };
                    }
                    
                    // Log API errors to server
                    this.logErrorToServer('api_error', {
                        status: response.status,
                        url: response.url,
                        errorData,
                        timestamp: new Date().toISOString()
                    });
                    
                    // If it has a user-friendly message, show it
                    if (errorData.message) {
                        this.showErrorMessage(errorData.message);
                    }
                }
                
                return response;
            } catch (error) {
                // Log network errors
                this.logErrorToServer('network_error', {
                    message: error.message,
                    stack: error.stack,
                    url: args[0],
                    timestamp: new Date().toISOString()
                });
                
                // Show user-friendly message
                this.showErrorMessage('Network error. Please check your connection and try again.');
                
                throw error;
            }
        };
    },
    
    /**
     * Patch axios to handle errors
     */
    patchAxios() {
        // Add a response interceptor
        window.axios.interceptors.response.use(
            response => response,
            error => {
                // Log API errors to server
                this.logErrorToServer('axios_error', {
                    message: error.message,
                    status: error.response?.status,
                    url: error.config?.url,
                    data: error.response?.data,
                    timestamp: new Date().toISOString()
                });
                
                // Show user-friendly message if available
                if (error.response?.data?.message) {
                    this.showErrorMessage(error.response.data.message);
                } else {
                    this.showErrorMessage('An error occurred. Please try again.');
                }
                
                return Promise.reject(error);
            }
        );
    },
    
    /**
     * Log error to server
     * @param {string} type Error type
     * @param {object} data Error data
     */
    logErrorToServer(type, data) {
        // Send error to server logging endpoint
        try {
            navigator.sendBeacon('/api/log-client-error', JSON.stringify({
                type,
                data,
                url: window.location.href,
                userAgent: navigator.userAgent
            }));
        } catch (e) {
            console.error('Failed to log error to server', e);
        }
    },
    
    /**
     * Show user-friendly error message
     * @param {string} message Error message to display
     */
    showErrorMessage(message) {
        // Use toastr if available
        if (window.toastr) {
            toastr.error(message);
            return;
        }
        
        // Fallback to alert if needed (or implement a custom solution)
        // alert(message);
        
        // Or create a simple toast notification
        const toast = document.createElement('div');
        toast.className = 'error-toast';
        toast.textContent = message;
        document.body.appendChild(toast);
        
        // Remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('fade-out');
            setTimeout(() => toast.remove(), 500);
        }, 5000);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    errorHandler.init();
});

export default errorHandler;