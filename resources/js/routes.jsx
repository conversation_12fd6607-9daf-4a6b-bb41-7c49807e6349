import React, { lazy, Suspense } from 'react';

// Loading component for Suspense
const LoadingComponent = () => (
  <div className="w-full h-screen flex items-center justify-center">
    <div className="animate-pulse text-blue-500">
      <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
      <p className="mt-4 text-gray-700">Loading...</p>
    </div>
  </div>
);

// Lazy loaded components
const MyOrganization = lazy(() => import('./Pages/MyOrganization'));
const Plan = lazy(() => import('./Pages/Plan'));
const Home = lazy(() => import('./Pages/Home'));
const Builder = lazy(() => import('./Pages/Builder'));
const DataCenter = lazy(() => import('./Pages/DataCenter'));
const Company = lazy(() => import('./Pages/Company'));
const Recruitment = lazy(() => import('./Pages/Recruitment'));

// Export wrapped components with Suspense
export const LazyMyOrganization = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <MyOrganization {...props} />
  </Suspense>
);

export const LazyPlan = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <Plan {...props} />
  </Suspense>
);

export const LazyHome = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <Home {...props} />
  </Suspense>
);

export const LazyBuilder = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <Builder {...props} />
  </Suspense>
);

export const LazyDataCenter = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <DataCenter {...props} />
  </Suspense>
);

export const LazyCompany = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <Company {...props} />
  </Suspense>
);

export const LazyRecruitment = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <Recruitment {...props} />
  </Suspense>
);