import './bootstrap'; // Include any global JavaScript dependencies like Bootstrap
import { createRoot } from 'react-dom/client';
import { createInertiaApp } from '@inertiajs/inertia-react';
import React, { lazy, Suspense } from 'react';

// Register service worker for offline capabilities
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/service-worker.js')
      .then(registration => {
        console.log('ServiceWorker registration successful with scope: ', registration.scope);
      })
      .catch(error => {
        console.log('ServiceWorker registration failed: ', error);
      });
  });
}

// Loading component for route transitions
const LoadingComponent = () => (
  <div className="w-full h-screen flex items-center justify-center">
    <div className="animate-pulse text-blue-500">
      <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
      <p className="mt-4 text-gray-700">Loading...</p>
    </div>
  </div>
);

// Code-split routes with dynamic imports
const pages = {
  MyOrganization: lazy(() => import('./Pages/MyOrganization')),
  Home: lazy(() => import('./Pages/Home')),
  Recruitment: lazy(() => import('./Pages/Recruitment')),
};

createInertiaApp({
  id: 'app',
  resolve: name => {
    // Return lazy-loaded component if it exists
    if (pages[name]) {
      return pages[name]; // Return component directly
    }

    // Fallback to dynamic import for other pages
    return import(`./Pages/${name}.jsx`).then(module => module.default);
  },
  setup({ el, App, props }) {
    createRoot(el).render(
      <Suspense fallback={<LoadingComponent />}>
        <App {...props} />
      </Suspense>
    );
  },
});
