import React, { useEffect, useState } from 'react';
import Notifications from './components/Notifications';
import CalendarView from './components/CalendarView';
import GenderDistribution from './components/GenderDistribution';
import CandidateLocation from './components/CandidateLocation';
import ToastNotification from './components/ToastNotification';

const Home = ({ assetBaseUrl,notifications,genderCount,topCountries,Interviewcandidates }) => {
    return (
        <div className="homepagesection">
            <ToastNotification />
            <div className="flex items-center px-4 py-4 justify-between border-b bg-white">
                <h1 className="whitespace-nowrap text-3xl font-medium">Home</h1>
            </div>
            <div className="bg-gray-50 space-y-4 max-h-[100%] overflow-y-auto p-4 space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <Notifications notifications={notifications} />
                    <CalendarView assetBaseUrl={assetBaseUrl} Interviewcandidates={Interviewcandidates} />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <GenderDistribution genderCount={genderCount} />
                    <CandidateLocation topCountries={topCountries} />
                </div>
            </div>
        </div>
    );
};

export default Home;
