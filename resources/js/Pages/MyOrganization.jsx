import React from 'react';
import { CombinedProvider } from './contexts/CombinedProvider';
import ToastNotification from "./components/ToastNotification";
import OrgIndex from './components/MyOrganization/OrgIndex';

const MyOrg = ({organisations, pagination = {}, userId, isAdminUser, isMasterUser,gridLabels,assetBaseUrl ,countries,shareWithUsers}) => {

    return (
        <CombinedProvider>
            <ToastNotification />
            <OrgIndex isAdminUser={isAdminUser} isMasterUser={isMasterUser} gridLabelsdata={gridLabels} assetBaseUrl={assetBaseUrl} countries={countries} shareWithUsers={shareWithUsers} organisations={organisations} userId={userId}/>
        </CombinedProvider>
    );
  };
  
  export default MyOrg;