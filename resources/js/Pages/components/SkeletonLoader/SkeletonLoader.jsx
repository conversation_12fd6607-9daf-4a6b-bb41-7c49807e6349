import React from 'react';

export const SkeletonBox = ({ width, height, className = '' }) => {
  return (
    <div 
      className={`animate-pulse bg-gray-200 rounded ${className}`}
      style={{ width, height }}
    />
  );
};

export const SkeletonText = ({ lines = 1, width = '100%', className = '' }) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {Array(lines).fill(0).map((_, i) => (
        <SkeletonBox 
          key={i} 
          width={typeof width === 'function' ? width(i) : width} 
          height="1rem" 
        />
      ))}
    </div>
  );
};

export const SkeletonProfile = () => {
  return (
    <div className="border border-gray-200 rounded-md p-4 w-full">
      <div className="flex items-center space-x-4">
        <SkeletonBox width="50px" height="50px" className="rounded-full" />
        <div className="space-y-2 flex-1">
          <SkeletonBox width="70%" height="1rem" />
          <SkeletonBox width="40%" height="0.75rem" />
        </div>
      </div>
      <div className="mt-4 space-y-3">
        <SkeletonBox width="100%" height="0.75rem" />
        <SkeletonBox width="90%" height="0.75rem" />
        <SkeletonBox width="80%" height="0.75rem" />
      </div>
    </div>
  );
};

export const SkeletonCard = () => {
  return (
    <div className="border border-gray-200 rounded-md p-4 w-full">
      <SkeletonBox width="60%" height="1.5rem" className="mb-3" />
      <div className="space-y-2">
        <SkeletonBox width="100%" height="0.75rem" />
        <SkeletonBox width="100%" height="0.75rem" />
        <SkeletonBox width="70%" height="0.75rem" />
      </div>
      <div className="mt-4">
        <SkeletonBox width="100%" height="2.5rem" className="rounded-md" />
      </div>
    </div>
  );
};

export const SkeletonTable = ({ rows = 5, columns = 3 }) => {
  return (
    <div className="w-full">
      {/* Header */}
      <div className="flex border-b border-gray-200 pb-2 mb-2">
        {Array(columns).fill(0).map((_, i) => (
          <div key={i} className="flex-1 px-2">
            <SkeletonBox width="80%" height="1.25rem" />
          </div>
        ))}
      </div>
      
      {/* Rows */}
      {Array(rows).fill(0).map((_, rowIndex) => (
        <div key={rowIndex} className="flex py-2 border-b border-gray-100">
          {Array(columns).fill(0).map((_, colIndex) => (
            <div key={colIndex} className="flex-1 px-2">
              <SkeletonBox width="90%" height="1rem" />
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export const SkeletonOrgChart = () => {
  return (
    <div className="w-full">
      <div className="flex justify-center mb-10">
        <SkeletonBox width="200px" height="80px" className="rounded-md" />
      </div>
      <div className="flex justify-center space-x-20 mb-10">
        {Array(3).fill(0).map((_, i) => (
          <SkeletonBox key={i} width="150px" height="70px" className="rounded-md" />
        ))}
      </div>
      <div className="flex justify-center space-x-14">
        {Array(5).fill(0).map((_, i) => (
          <SkeletonBox key={i} width="120px" height="60px" className="rounded-md" />
        ))}
      </div>
    </div>
  );
};