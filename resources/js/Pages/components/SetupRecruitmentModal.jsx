import React, { useState } from "react";
import { Inertia } from '@inertiajs/inertia';

const SetupRecruitmentModal = ({ assetBaseUrl, showModal, closeModal }) => {
  if (!showModal) return null;

  const [numberOfStages, setNumberOfStages] = useState(3);
  const [stageNames, setStageNames] = useState({}); // Store stage names dynamically

  const handleIncrement = () => {
    setNumberOfStages((prev) => prev + 1);
  };

  const handleDecrement = () => {
    setNumberOfStages((prev) => (prev > 1 ? prev - 1 : prev));
  };

  const handleInputChange = (e) => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value) && value > 0) {
      setNumberOfStages(value);
    }
  };

  const handleStageNameChange = (index, value) => {
    setStageNames((prev) => ({
      ...prev,
      [index]: value,
    }));
  };


const handleSubmit = () => {
  const recruitmentTitle = document.getElementById('recruitment_title').value;

  const stages = Array.from({ length: numberOfStages }).map((_, index) => ({
    stage_name: document.getElementById(`name${index}`).value,
    stage_number: index + 1,
  }));

  if (!recruitmentTitle.trim()) {
    alert("Recruitment title cannot be empty.");
    return;
  }

  if (stages.some(stage => !stage.stage_name.trim())) {
    alert("All stage names must be filled.");
    return;
  }


 Inertia.post('/recruitment/save', { recruitmentTitle, stages }, {
      onSuccess: () => {
      closeModal();  // Close modal after success
    },
    onError: (error) => {
      console.log('Error:', error);
    },
  });

};


  return (
    <div className="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
      <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
      <div className="modal-content step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
        <img
          className="absolute right top-2 w-auto cursor-pointer"
          src={`${assetBaseUrl}images/cross.svg`}
          alt="Close"
          onClick={closeModal}
        />
        <h2 className="font-semibold px-4 w-full">Set Up Recruitment</h2>
        <div className="w-full border-t mt-3 border-gray-200"></div>
        <div className="h-full w-full">
          <div className="h-5/6 flex items-center">
            <div className="w-full">
              <div className="relative py-2 bg-white">
                <div
                  className="absolute inset-0 flex items-center px-1"
                  aria-hidden="true"
                ></div>
              </div>
              <div className="modalscroll px-4">
			  <div className="recruitment-title">
				   <div className="mb-3 flex">
					  <label
						className="self-center w-64 text-sm text-gray-600"
					  >
						Recruitment Name
					  </label>
					  <input
						type="text"
						id="recruitment_title"
						placeholder="Enter the Title"
						name="recruitment_title"
						className="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md"
					  />
					</div>
                </div>
			  
                <div
                  className="text-[14px] font-normal leading-[20px] text-[#101828] p-0 m-0 mb-[10px]"
                  aria-hidden="true"
                >
                  Select how many interviews are in this recruitment process.
                </div>
                <div className="mb-3 flex">
                  <label
                    htmlFor="stages"
                    className="self-center w-40 text-sm text-gray-600"
                  >
                    Number of Stages
                  </label>
                  <div className="flex items-center gap-3 mt-1">
                    <button
                      onClick={handleDecrement}
                      className="border border-blue-500 rounded-lg w-8 h-8 bg-transparent justify-center flex items-center text-xl"
                    >
                      -
                    </button>
                    <input
                      type="number"
                      id="stages"
                      value={numberOfStages}
                      onChange={handleInputChange}
                      className="w-16 h-8 rounded-md p-2 text-center border border-blue-500 bg-transparent justify-center flex items-center text-sm"
                      min="1"
                    />
                    <button
                      onClick={handleIncrement}
                      className="border border-blue-500 rounded-lg w-8 h-8 bg-transparent justify-center flex items-center text-xl"
                    >
                      +
                    </button>
                  </div>
                </div>
                <div className="w-full divider-border border-t mt-4 border-gray-200"></div>
                <div className="Interview-section">
                  <div className="text-[14px] font-normal leading-[20px] text-[#101828] p-0 m-0 mb-2 mt-3">
                    Provide a name for each interview in this process.
                  </div>
                  {Array.from({ length: numberOfStages }).map((_, index) => (
                    <div className="mb-3 flex" key={index}>
                      <label
                        htmlFor={`name${index}`}
                        className="self-center w-64 text-sm text-gray-600"
                      >
                        Interview {index + 1}
                      </label>
                      <input
                        type="text"
                        id={`name${index}`}
                        placeholder="Enter the name"
                        name={`name${index}`}
                        onChange={(e) =>
                          handleStageNameChange(index + 1, e.target.value)
                        }
                        className="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md"
                      />
                    </div>
                  ))}
                </div>
              </div>
              <div className="w-full border-t mt-4 border-gray-200"></div>
              <div className="flex gap-2 w-full px-4 mt-4">
                <button
                  type="button"
                  className="bg-white w-full text-black border p-2 rounded-md"
                  onClick={closeModal}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md"
                  onClick={handleSubmit}
                >
                  <span className="block">Save</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SetupRecruitmentModal;
