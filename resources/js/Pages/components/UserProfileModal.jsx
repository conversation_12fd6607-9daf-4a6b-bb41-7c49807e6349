import React, { useState } from "react";
import { Inertia } from '@inertiajs/inertia';
import { FaChevronDown } from "react-icons/fa";
import Summary from "./Summary/Summary";
import CareerHistory from "./CareerHistory/CareerHistory";
import Email from "./Email/Email";
import PersonalDetails from "./PersonalDetails/PersonalDetails";
import Task from "./Task&Notes/Task";
import Activity from "./Activity/Activity";
import Skills from "./Skills/Skills.jsx";
import ProfileCard from "./ProfileCard/ProfileCard.jsx";
import TaskButton from "./TaskButton/TaskButton.jsx";
import TabButton from "./TabButton/TabButton.jsx";
import Dropdown from "./Dropdown/Dropdown.jsx";
import ButtonWithIcon from "./ButtonWithIcon/ButtonWithIcon.jsx";
import InfoWithIcon from "./InfoWithIcon/InfoWithIcon.jsx";

const CandidateDetailsModal = ({ assetBaseUrl, showModal, closeModal }) => {
  if (!showModal) return null;
    const [candidatedetails] = useState("Candidate details");
	const [activeSection, setActiveSection] = useState("summary");
	const [activeTaskActivity, setActiveTaskActivity] = useState("task");
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);


const profileDetails = [
  { icon: `${assetBaseUrl}images/Briefcase.svg`, text: "Chief Financial Officer" },
  { icon: `${assetBaseUrl}images/Buildings.svg`, text: "Company Name LTD" },
  {
    icon: `${assetBaseUrl}images/LinkedIn.svg`,
    text: "LinkedIn Profile",
    link: "https://www.linkedin.com/",
  },
];


  const toggleDropdown = () => setIsDropdownOpen((prev) => !prev);

  const CANDIDATE_TABS = [
    { id: "summary", label: "Summary", component: <Summary /> },
    { id: "career", label: "Career History", component: <CareerHistory /> },
    { id: "skills", label: "Skills", component: <Skills /> },
    {
      id: "personaldetails",
      label: "Personal Details",
      component: <PersonalDetails />,
    },
    { id: "email", label: "Email", component: <Email /> },
  ];

  const TASK_ACTIVITY_TABS = [
    { id: "task", label: "Tasks & notes", component: <Task /> },
    { id: "activity", label: "Activity", component: <Activity profileName="James" profileAvatar={`${assetBaseUrl}images/Avatar.svg`} /> },
  ];
	
	
	
const handleSubmit = () => {

alert('sdasda');

 // Inertia.post('/recruitment/save', { recruitmentTitle, stages }, {
      // onSuccess: () => {
      // closeModal();  // Close modal after success
    // },
    // onError: (error) => {
      // console.log('Error:', error);
    // },
  // });

};


  return (
    <div className="userprofile fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
      <div
        className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"
        onClick={closeModal}
      ></div>
      <div className="modal-content text-black step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
        <img
          className="absolute right top-2 w-auto cursor-pointer"
          src={`${assetBaseUrl}images/cross.svg`}
          alt="Close"
          onClick={closeModal}
        />
        {candidatedetails && <h2 className="font-semibold px-4 w-full">{candidatedetails}</h2>}
        <div className="w-full border-t mt-3 border-gray-200"></div>
        <div className="h-full w-full">
          <div className="h-5/6 flex items-center">
            <div className="w-full">
              <div className="relative py-2 bg-white">
                <div
                  className="absolute inset-0 flex items-center px-1"
                  aria-hidden="true"
                ></div>
              </div>
              <div className="modalscroll px-4">
					
					
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						{/* Left Section */}
						<div className="p-4 rounded-md">
						  <ProfileCard avatar={`${assetBaseUrl}images/Avatar.svg`} profileDetails={profileDetails} assetBaseUrl={assetBaseUrl} InfoWithIcon={InfoWithIcon} />

						  <div className="border border-gray-light rounded-[8px] p-3 mt-3">
							<div className="flex items-center">
							  {/* Left Side: Icon and Text */}
							  <div className="w-1/2 flex text-left">
								<InfoWithIcon
								  icon={`${assetBaseUrl}images/Vector.svg`}
								  assetBaseUrl={assetBaseUrl}
								  text="Country Manager - Netherlands"
								/>
							  </div>
							  <div className="w-1/2 flex justify-end space-x-2">
								<ButtonWithIcon
								  className="remove-btn"
								  text="Remove"
								  assetBaseUrl={assetBaseUrl}
								  icon={`${assetBaseUrl}images/TrashSimple.svg`}
								/>
								<Dropdown
								  isOpen={isDropdownOpen}
								  toggleDropdown={toggleDropdown}
								  assetBaseUrl={assetBaseUrl}
								  FaChevronDown={FaChevronDown}
								  items={[
									{ icon: `${assetBaseUrl}images/Check.png`, text: "Yes" },
									{ icon: `${assetBaseUrl}images/X.png`, text: "No" },
								  ]}
								/>
							  </div>
							</div>
							<InfoWithIcon icon={`${assetBaseUrl}images/FloppyDisk.svg`} text="HR Interview" assetBaseUrl={assetBaseUrl}/>
						  </div>

						  {/* Tabs Section */}
						  <div className="flex border-b border-gray-light space-x-0 sm:space-x-4 mt-3">
							{CANDIDATE_TABS.map((tab) => (
							  <TabButton
								key={tab.id}
								assetBaseUrl={assetBaseUrl}
								label={tab.label}
								isActive={activeSection === tab.id}
								onClick={() => setActiveSection(tab.id)}
							  />
							))}
						  </div>
						  <div className="border border-gray-light rounded-[8px] p-4 mt-4 h-[350px] overflow-y-auto">
							{CANDIDATE_TABS.find((tab) => tab.id === activeSection)?.component}
						  </div>
						</div>

						{/* Right Section */}
						<div className="p-4 rounded-md">
						  {/* Tabs for Task & Activity */}
						  <div className="flex">
							{TASK_ACTIVITY_TABS.map((tab) => (
							  <TaskButton
								key={tab.id}
								assetBaseUrl={assetBaseUrl}
								label={tab.label}
								isActive={activeTaskActivity === tab.id}
								onClick={() => setActiveTaskActivity(tab.id)}
							  />
							))}
						  </div>
						  <div className="border border-gray-light rounded-[8px] mt-4 p-4 h-[576px] overflow-y-auto">
							{
							  TASK_ACTIVITY_TABS.find((tab) => tab.id === activeTaskActivity)
								?.component
							}
						  </div>
						</div>
					  </div>
					
					
					
					
              </div>
              <div className="w-full border-t mt-4 border-gray-200"></div>
              <div className="flex gap-2 w-full px-4 mt-4">
                <button
                  type="button"
                  className="bg-white w-full text-black border p-2 rounded-md"
                  onClick={closeModal}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md"
                  onClick={handleSubmit}
                >
                  <span className="block">Save</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CandidateDetailsModal;
