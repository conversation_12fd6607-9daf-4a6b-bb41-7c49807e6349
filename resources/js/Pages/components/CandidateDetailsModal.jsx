import React, { useState, useEffect, lazy, Suspense, memo } from 'react';
import { Inertia } from '@inertiajs/inertia';
import { FaChevronDown } from "react-icons/fa";
// Lazy load components for better performance
const Summary = lazy(() => import("./Summary/Summary"));
const CareerHistory = lazy(() => import("./CareerHistory/CareerHistory"));
const Email = lazy(() => import("./Email/Email"));
const PersonalDetails = lazy(() => import("./PersonalDetails/PersonalDetails"));
const Task = lazy(() => import("./Task&Notes/Task"));
const Activity = lazy(() => import("./Activity/Activity"));
const Skills = lazy(() => import("./Skills/Skills.jsx"));
import ProfileCard from "./ProfileCard/ProfileCard.jsx";
import TaskButton from "./TaskButton/TaskButton.jsx";
import TabButton from "./TabButton/TabButton.jsx";
import ButtonWithIcon from "./ButtonWithIcon/ButtonWithIcon.jsx";
import InfoWithIcon from "./InfoWithIcon/InfoWithIcon.jsx";
import Dropdown from "./Dropdown/Dropdown.jsx";


const CandidateDetailsModal = memo(({ assetBaseUrl, showModal, closeModal,id,candidateName,candidateDetails,skills_list,newStageName,interviewerList,recruitment_id,Pipelinedata,notesdata,user_role,userlist,groupedPeoplesCareer,recruitment_name }) => {
  if (!showModal) return null;
    const [candidatedetailsTitle] = useState("Candidate details");
	const [activeSection, setActiveSection] = useState("summary");
	const [activeTaskActivity, setActiveTaskActivity] = useState("task");
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [getemail, setEmail] = useState(Pipelinedata.Email || "");
    const [NoteSave, setNoteSave] = useState(null);

// console.log(notesdata);
	const [formData, setFormData] = useState({
		email: '',
		phone: '',
		relocation: '',
		expectedSalary: '',
		address: '',
	});
	
	const selectedInterviewer = interviewerList.find(
	(interviewer) => interviewer.id === Pipelinedata.interviewer_id
	);
	const getinterviewerName = selectedInterviewer ? selectedInterviewer.name : null;
	
	const [interviewerName, setInterviewerNames] = useState(getinterviewerName || "Country Manager");
	
	// console.log(interviewerName);
	const [countryManager, setCountryManager] = useState("");

	
	
	 useEffect(() => {
		if (Pipelinedata?.country_manager) {
		  setCountryManager(Pipelinedata.country_manager);
		}
		
		  if (Pipelinedata) {
			  setFormData({
				email: Pipelinedata.Email || '',
				phone: Pipelinedata.Phone_number || '',
				relocation: Pipelinedata.relocation || '',
				expectedSalary: Pipelinedata.expected_salary || '',
				address: Pipelinedata.Address || '',
			  });
		 }
		
	  }, [Pipelinedata]);


	const handleChange = (e) => {
		const { name, value, type, checked } = e.target;
		setFormData({
		  ...formData,
		  [name]: type === 'checkbox' ? checked : value,
		});
		
	  if (name === "email") {
		setEmail(value);
	  }
	};
	
	const handleCountryManagerChange = (e) => {
		setCountryManager(e.target.value);
	};
	
  const handleUpdateNotes = (value) => {
	 setNoteSave(value);
  };

	const handleSubmit = () => {
	 Inertia.post('/recruitment/candidatedetails/save', { id,recruitment_id,countryManager, formData,NoteSave }, {
		  onSuccess: () => {
		  closeModal();  // Close modal after success
		},
		onError: (error) => {
		  console.log('Error:', error);
		},
	  });

	};


	const profileDetails = [
	  { 
		icon: `${assetBaseUrl}images/Briefcase.svg`, 
		colorclass: "",
		text: candidateDetails.latest_role || "N/A" 
	  },
	  { 
		icon: `${assetBaseUrl}images/Buildings.svg`, 
		colorclass: "",
		text: candidateDetails.company_name || "N/A" 
	  },
	  {
		icon: `${assetBaseUrl}images/LinkedIn.svg`,
		text: "LinkedIn Profile",
		colorclass: "text-[#06B6D4] underline",
		link: candidateDetails.linkedinURL || null, 
	  },
	];



  const toggleDropdown = () => setIsDropdownOpen((prev) => !prev);

	const CANDIDATE_TABS = [
	  { id: "summary", label: "Summary", component: <Summary summary={candidateDetails.summary} /> },
	  { id: "career", label: "Career History", component: <CareerHistory career_history={groupedPeoplesCareer} /> },
	  { id: "skills", label: "Skills", component: <Skills skills={skills_list}/> },
	  {
		id: "personaldetails",
		label: "Personal Details",
		component: <PersonalDetails formData={formData} handleChange={handleChange}   user_role={user_role}  />,
	  },
	  ...(user_role !== 'Viewer' ? [
		{ id: "email", label: "Email", component: <Email pipelinedata={Pipelinedata} name={candidateName} getemail={getemail} assetBaseUrl={assetBaseUrl} /> },
	  ] : []),
	];

  const TASK_ACTIVITY_TABS = [
    { id: "task", label: "Tasks & notes", component: <Task candidateName={candidateName} id={id} notesdata={notesdata} userlist={userlist} handleUpdateNotes={handleUpdateNotes} /> },
    // { id: "activity", label: "Activity", component: <Activity profileName="James" profileAvatar={`${assetBaseUrl}images/Avatar.svg`} /> },
  ];
	
	// console.log(Pipelinedata.country_manager);

	function removeHandler(){
		  Swal.fire({
		html: `<div class="px-5 w-full flex justify-center mb-3">
				  <img class="h-10 w-10" src="${assetBaseUrl}images/redTrashIcon.svg" alt="">
				</div>
				<h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Delete ${candidateName}</h2>
				<p class="px-5 font-normal">Are you sure you want to delete the recruitment <b>${candidateName}</b>?</p>
				<div class="w-full border-t mt-5 border-gray-200"></div>`,
		showDenyButton: true,
		showCancelButton: false,
		confirmButtonText: 'Delete',
		denyButtonText: 'Cancel',
		reverseButtons: true,
		buttonsStyling: false,
		customClass: {
		  confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md',
		  denyButton: 'bg-white btnsWidth btnsmargin text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md'
		},
		showCloseButton: true
	  }).then((result) => {
		if (result.isConfirmed) {
		  // Proceed with deletion if confirmed
		  Inertia.post('/recruitment/candidate/remove', { id,recruitment_id}, {
			onSuccess: () => {
			  Swal.fire('Deleted!', 'The Candidate Deleted successfully!', 'success');
			  closeModal(); 
			},
			onError: (error) => {
			  Swal.fire('Error!', 'Something went wrong, please try again.', 'error');
			  console.error('Error:', error);
			},
		  });
		}
	  });
		
		
		
		 // Inertia.post('/recruitment/candidate/remove', { id,recruitment_id}, {
			  // onSuccess: () => {
			  // closeModal();  // Close modal after success
			// },
			// onError: (error) => {
			  // console.log('Error:', error);
			// },
		  // });
	}

  return (
    <div className="userprofile fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
	
      <div
        className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"
        onClick={closeModal}
      ></div>
      <div className="modal-content text-black step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
        <img
          className="absolute right top-2 w-auto cursor-pointer"
          src={`${assetBaseUrl}images/cross.svg`}
          alt="Close"
          onClick={closeModal}
        />
        {candidatedetailsTitle && <h2 className="font-semibold px-4 w-full">{candidatedetailsTitle}</h2>}
        <div className="w-full border-t mt-3 border-gray-200"></div>
        <div className="h-full w-full">
          <div className="h-5/6 flex items-center">
            <div className="w-full">
              <div className="relative py-2 bg-white">
                <div
                  className="absolute inset-0 flex items-center px-1"
                  aria-hidden="true"
                ></div>
              </div>
              <div className="modalscroll px-4">
					<div className="candidateDetails-wrapper">
					
					<div className="grid grid-cols-2 lg:grid-cols-2 gap-4">
						{/* Left Section */}
						<div className="rounded-md">
						  <ProfileCard avatar={`${assetBaseUrl}images/Avatar.svg`} profileDetails={profileDetails} assetBaseUrl={assetBaseUrl} InfoWithIcon={InfoWithIcon} id={id} candidateName={candidateName} interviewerList={interviewerList} recruitment_id={recruitment_id} interviewer_id={Pipelinedata.interviewer_id} interview_date={Pipelinedata.interview_date} DownloadCV={Pipelinedata.Link_cv} user_role={user_role}  setInterviewerNames={setInterviewerNames} />

						  <div className="border border-gray-light rounded-[8px] p-3 mt-3">
							<div className="flex items-center">
							  {/* Left Side: Icon and Text */}
							  <div className="w-9/12 flex text-left">
								<div className="flex items-center space-x-2 mt-2">
									<div className="flex flex-row">
										<div className="text-[14px] self-center">{recruitment_name}</div>
									</div>
								</div>
							  </div>
							  <div className="w-3/12 flex justify-end space-x-2">
							  {user_role !== 'Viewer' && (
								<>
								<ButtonWithIcon
								  className="remove-btn rounded-md font-semibold text-red-600"
								  text="Remove"
								  assetBaseUrl={assetBaseUrl}
								  removeHandler={removeHandler}
								  icon={`${assetBaseUrl}images/TrashSimple.svg`}
								/>
								</>
							  )}
							  </div>
							</div>
							<InfoWithIcon icon={`${assetBaseUrl}images/FloppyDisk.svg`} text={newStageName} assetBaseUrl={assetBaseUrl}/>
						  </div>

						  {/* Tabs Section */}
						  <div className="flex border-b border-gray-light space-x-0 sm:space-x-4 mt-3">
							{CANDIDATE_TABS.map((tab) => (
							  <TabButton
								key={tab.id}
								assetBaseUrl={assetBaseUrl}
								label={tab.label}
								isActive={activeSection === tab.id}
								onClick={() => setActiveSection(tab.id)}
							  />
							))}
						  </div>
						  <div className="border border-gray-light rounded-[8px] p-4 mt-4 h-[350px] overflow-y-auto">
						  <Suspense fallback={<div className="flex items-center justify-center h-full">Loading...</div>}>
						   {CANDIDATE_TABS.find((tab) => tab.id === activeSection)?.component}
							</Suspense>
						  </div>
						</div>
	
						{/* Right Section */}
						<div className="rounded-md">
						  {/* Tabs for Task & Activity */}
							  {/*<div className="flex">
							{TASK_ACTIVITY_TABS.map((tab) => (
							  <TaskButton
								key={tab.id}
								assetBaseUrl={assetBaseUrl}
								label={tab.label}
								isActive={activeTaskActivity === tab.id}
								onClick={() => setActiveTaskActivity(tab.id)}
							  />
							))}
							  </div>*/}
						  <div className="border border-gray-light rounded-[8px] p-4 h-[576px] overflow-y-auto">
						  <Suspense fallback={<div className="flex items-center justify-center h-full">Loading...</div>}>
						  {
						   TASK_ACTIVITY_TABS.find((tab) => tab.id === activeTaskActivity)
						   ?.component
						  }
							</Suspense>
						  </div>
						</div>
					  </div>
					  
					  </div>
					
					
					
					
              </div>
              <div className="w-full border-t mt-4 border-gray-200"></div>
              <div className="flex gap-2 w-full px-4 mt-4">
			{user_role !== 'Viewer' && (
				<>
                <button
                  type="button"
                  className="bg-white w-full text-black border p-2 rounded-md"
                  onClick={closeModal}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md"
                  onClick={handleSubmit}
                >
                  <span className="block">Save</span>
                </button>
				</>
			)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

export default CandidateDetailsModal;
