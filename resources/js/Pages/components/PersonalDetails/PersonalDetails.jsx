import React from 'react';

const PersonalDetails = ({ formData, handleChange, user_role }) => {
	// console.log(user_role);
  const isViewer = user_role === 'Viewer'; 

  return (
    <div className="text-left">
      <h3 className="text-[16px] font-semibold mb-4">Personal Details</h3>
      <form className="w-full max-w-md mx-auto p-4 border rounded-md shadow-md bg-white">
        <div className="mb-4">
          <label htmlFor="email" className="block text-sm text-gray-600 mb-1">
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            className="w-full p-2 border rounded-md outline-none focus:ring focus:ring-indigo-300"
            disabled={isViewer} // Disable field if user role is 'viewer'
          />
        </div>

        <div className="mb-4">
          <label htmlFor="phone" className="block text-sm text-gray-600 mb-1">
            Phone Number
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            required
            className="w-full p-2 border rounded-md outline-none focus:ring focus:ring-indigo-300"
            disabled={isViewer} // Disable field if user role is 'viewer'
          />
        </div>

        <div className="mb-4">
          <label htmlFor="relocation" className="block text-sm text-gray-600 mb-1">
            Relocation
          </label>
          <input
            type="text"
            id="relocation"
            name="relocation"
            value={formData.relocation}
            onChange={handleChange}
            required
            className="w-full p-2 border rounded-md outline-none focus:ring focus:ring-indigo-300"
            disabled={isViewer} // Disable field if user role is 'viewer'
          />
        </div>

        <div className="mb-4">
          <label htmlFor="expectedSalary" className="block text-sm text-gray-600 mb-1">
            Expected Salary
          </label>
          <input
            type="number"
            id="expectedSalary"
            name="expectedSalary"
            value={formData.expectedSalary}
            onChange={handleChange}
            required
            className="w-full p-2 border rounded-md outline-none focus:ring focus:ring-indigo-300"
            disabled={isViewer} // Disable field if user role is 'viewer'
          />
        </div>

        <div className="mb-4">
          <label htmlFor="address" className="block text-sm text-gray-600 mb-1">
            Address
          </label>
          <textarea
            id="address"
            name="address"
            value={formData.address}
            onChange={handleChange}
            required
            rows="3"
            className="w-full p-2 border rounded-md outline-none focus:ring focus:ring-indigo-300"
            disabled={isViewer} // Disable field if user role is 'viewer'
          ></textarea>
        </div>
      </form>
    </div>
  );
};

export default PersonalDetails;
