import React from "react";

const CareerHistory = ({ career_history }) => {

  // Ensure career_history is not null or undefined
  if (!career_history || career_history.length === 0) {
    return (
      <div className="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
        <h4 className="text-lg text-gray-700 font-semibold chart-heading">Career History</h4>
        <p className="text-sm">Not Applicable</p>
      </div>
    );
  }

  return (
    <div className="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
      <h4 className="text-lg text-gray-700 font-semibold chart-heading">Career History</h4>
      <div className="flex flex-col items-start w-full">
        {career_history.map((careerHistory, index) => {
          const startDate = new Date(careerHistory.start_date);
          const endDate = careerHistory.end_date ? new Date(careerHistory.end_date) : null;
          const formattedStartDate = startDate.toLocaleDateString('en-GB', { month: 'short', year: 'numeric' });
		  
          const formattedEndDate = endDate ? endDate.toLocaleDateString('en-GB', { month: 'short', year: 'numeric' }) : "Present";

          return (
            <div key={index} className="flex h-max items-start justify-center mb-1">
              <div className="flex flex-col mt-1 items-center justify-center h-full w-2">
                <div className="rounded-full blueBalls bg-mainBlue"></div>
                <div className="flex-grow border-l border-mainBlue"></div>
              </div>
              <div className="flex flex-col items-start justify-start pl-4">
                <h4 className="text-sm font-semibold text-gray-900">{careerHistory.role}</h4>
                <div className="flex gap-x-2">
                  <span className="text-sm font-base text-gray-700">{careerHistory.company_name}</span>
                </div>
                <div className="flex gap-x-2 mb-4">
                  <span className="text-xs font-base GrayText">
                    {formattedStartDate} - {formattedEndDate}
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CareerHistory;
