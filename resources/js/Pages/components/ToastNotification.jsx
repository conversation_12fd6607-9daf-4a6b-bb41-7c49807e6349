import React, { useEffect } from 'react';
import { usePage } from '@inertiajs/inertia-react';

const ToastrNotification = () => {
  const { toast } = usePage().props;
  useEffect(() => {
    if (toast) {
      const { type, message } = toast;
      switch (type) {
        case 'success':
          toastr.success(message);
          break;
        case 'info':
          toastr.info(message);
          break;
        case 'warning':
          toastr.warning(message);
          break;
        case 'error':
          toastr.error(message);
          break;
        default:
          console.error(`Unknown toast type: ${type}`);
          break;
      }
    }
  }, [toast]);

  return null;
};

export default ToastrNotification;
