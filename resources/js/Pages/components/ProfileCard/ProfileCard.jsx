import { useState, useEffect, memo } from "react";
import { FaChevronDown } from "react-icons/fa";
import Dropdown from "../Dropdown/Dropdown.jsx";
import Modal from "../Modal";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Inertia } from '@inertiajs/inertia';

const ProfileCard = memo(({ avatar, profileDetails, assetBaseUrl, InfoWithIcon, id, candidateName, interviewerList, recruitment_id, interviewer_id, interview_date, DownloadCV, user_role, setInterviewerNames }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const toggleDropdown = () => setIsDropdownOpen((prev) => !prev);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const closeModal = () => setIsModalOpen(false);
  const [isUploadModal, setIsUploadModalOpen] = useState(false);
  const closeUploadModal = () => setIsUploadModalOpen(false);
   const [isdownloadModal, setisdownloadModal] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [formattedDate, setFormattedDate] = useState(null);
  const [interviewerID, setinterviewerID] = useState(interviewer_id || ""); 
  const [files, setFiles] = useState([]);
  const [dragActive, setDragActive] = useState(false);

  // Parsing DownloadCV data on component mount or when DownloadCV changes
  useEffect(() => {
    if (DownloadCV) {
      const parsedData = JSON.parse(DownloadCV); 
      if (parsedData && Array.isArray(parsedData.file_urls) && Array.isArray(parsedData.file_names)) {
        setFiles(parsedData.file_urls.map((url, index) => ({
          name: parsedData.file_names[index],
          url
        })));
      }
    }
  }, [DownloadCV]);

  useEffect(() => {
    if (interview_date) {
      const date = new Date(interview_date);
      setFormattedDate(date.toLocaleDateString());
      setStartDate(date);
    }
  }, [interview_date]);

  const scheduleInterviewer = () => {
    setIsModalOpen(true);
  };

  const handleInterviewerChange = (e) => {
    setinterviewerID(e.target.value);
  };

  const handleSaveInterviewer = () => {
    Inertia.post('/recruitment/interviewschedule/save', { formattedDate, id, recruitment_id, interviewerID }, {
      onSuccess: () => {
        const selectedInterviewer = interviewerList.find((interviewer) => interviewer.id === Number(interviewerID));
        const interviewerName = selectedInterviewer ? selectedInterviewer.name : null;
        setInterviewerNames(interviewerName);
        closeModal();
      },
      onError: (error) => {
        console.log('Error:', error);
      }
    });
  };

  const handleUploadItem = (action) => {
    if (action === "uploadCV") {
      setIsUploadModalOpen(true);
	  setisdownloadModal(false);
    } else if (action === "downloadCV") {
		
      // Handle download of each file from DownloadCV
      // files.forEach((file) => {
        // const downloadLink = document.createElement('a');
        // downloadLink.href = file.url;
        // downloadLink.download = file.name;
        // downloadLink.target = "_blank";
        // downloadLink.style.display = 'none';
        // document.body.appendChild(downloadLink);
        // downloadLink.click();
        // document.body.removeChild(downloadLink);
      // });
	  setIsUploadModalOpen(true);
	  setisdownloadModal(true);
    }
    setIsDropdownOpen(false);
  };

const handleFiles = (newFiles,updatesfiles) => {
  const formData = new FormData();
  formData.append("recruitment_pipeline_id", id);
	if(updatesfiles){
		updatesfiles.forEach((file) => {
		if (file.url) { 
		  formData.append("existing_files[]", file.url); 
		  formData.append("existing_names[]", file.name); 
		} else {
		  console.error("Invalid file detected in existing files:", file);
		}
	  });
	  formData.append("action", 'delete'); 
	}else{
	files.forEach((file) => {
		if (file.url) { 
		  formData.append("existing_files[]", file.url); 
		  formData.append("existing_names[]", file.name); 
		} else {
		  console.error("Invalid file detected in existing files:", file);
		}
	  });
	}

  

  newFiles.forEach((file) => {
    if (file instanceof File) {
      formData.append("files[]", file); 
    } else {
      console.error("Invalid file detected in new files:", file);
    }
  });

  for (let pair of formData.entries()) {
   // console.log(pair[0] + ": " + pair[1]); 
  }

  setFiles((prevFiles) => {
    return [...prevFiles, ...newFiles];
  });

  Inertia.post("/recruitment/candidate/uploadcv", formData, {
    onSuccess: () => {
      // console.log("CV uploaded successfully.");
    },
    onError: (error) => {
      console.error("Error uploading File:", error);
    }
  });
};


  function showCalendar() {
	document.getElementById(id+'-date').click();
  }
  const handleDrop = (e) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  };

  const handleBrowse = (e) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
	  e.target.value = '';
    }
  };


  const handleRemoveFile = (index) => {
  setFiles((prevFiles) => {
    const updatedFiles = prevFiles.filter((_, i) => i !== index);
    handleFiles([],updatedFiles); 
    return updatedFiles;
  });
};
  
function formatDate(selectedDate) {
	const date = new Date(selectedDate);
	const format = date.toISOString().split("T")[0];
	setStartDate(format);
	setFormattedDate(format);
	// saveDate(format);

  } 

  return (
    <div className="border border-gray-light rounded-[8px] p-3">
      <div className="flex space-x-4">
        <div className="flex items-center justify-center cursor-pointer h-8 w-8 bg-color bg-opacity-50 rounded-full text-xs font-semibold text-[#667085]">
          {candidateName.slice(0, 2).toUpperCase()}
        </div>

        <div className="profile-info w-full">
          <div className="flex items-center justify-between">
            <div className="w-1/2 text-left">
              <div className="profile-name text-left text-base font-semibold">{candidateName}</div>
            </div>

            <div className="w-1/2 flex justify-end space-x-4">
              {user_role !== 'Viewer' && (
                <>
                  <img
                    onClick={scheduleInterviewer}
                    src={`${assetBaseUrl}images/calendarblank.svg`}
                    alt="CalenderLogo"
                    className="w-[20px] h-[20px] cursor-pointer"
                  />
                </>
              )}
              <img
                src={`${assetBaseUrl}images/DotsThreeVertival.svg`}
                alt="ThreeDotsLogo"
                className="w-[20px] h-[20px] cursor-pointer"
                onClick={toggleDropdown}
              />

              <Dropdown
                isOpen={isDropdownOpen}
                assetBaseUrl={assetBaseUrl}
                FaChevronDown={FaChevronDown}
                onItemClick={handleUploadItem}
                items={[
					...(user_role !== 'Viewer' ? [{ icon: `${assetBaseUrl}images/upload-icon.svg`, text: "Upload File", action: "uploadCV" }] : []),
					{ icon: `${assetBaseUrl}images/download.png`, text: "Download File", action: "downloadCV" }
				  ]}
              />
            </div>
          </div>
          {profileDetails.map(({ icon, text, link, colorclass }, index) => (
            <InfoWithIcon key={index} icon={icon} text={text} link={link} colorclass={colorclass} />
          ))}
        </div>
      </div>

      {/* Modal for scheduling interview */}
      {user_role !== 'Viewer' && (
        <>
          <Modal
            classnames='scheduleinterview'
            isOpen={isModalOpen}
            onClose={closeModal}
            title="Schedule Interview"
            assetBaseUrl={assetBaseUrl}
            handleSubmit={handleSaveInterviewer}
          >
            <div className="flex flex-row gap-2 mb-3">
              <label htmlFor="choosedate" className="text-gray-600 w-32 text-sm">
                Choose Date
              </label>
              <img
                onClick={showCalendar}
                src={`${assetBaseUrl}images/calendar.svg`}
                alt="Calendar"
                width="20"
                height="20"
                className="cursor-pointer"
              />
              {formattedDate ?? "N/A"}
              <DatePicker
                className="hidden"
                selected={startDate}
                onChange={(date) => formatDate(date)}
                id={`${id}-date`}
              />
            </div>

            <div className="flex flex-row gap-2">
              <label htmlFor="SelectInterviewer" className="text-gray-600 self-center w-32 text-sm">
                Select Interviewer
              </label>
              <select
                id="SelectInterviewer"
                className="text-black block w-56 p-2 outline-none border border-gray-300 rounded-md"
                value={interviewerID}
                onChange={handleInterviewerChange}
              >
                <option value="" disabled>
                  Choose an interviewer
                </option>
                {interviewerList && interviewerList.length > 0 ? (
                  interviewerList.map((interviewer) => (
                    <option key={interviewer.id} value={interviewer.id}>
                      {interviewer.name}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>
                    No interviewers available
                  </option>
                )}
              </select>
            </div>
          </Modal>
        </>
      )}

      {/* Modal for uploading files */}
      <Modal
        classnames={!isdownloadModal ? "uploadfiles-section" : "downloadfile-section"}
        isOpen={isUploadModal}
        onClose={closeUploadModal}
	    title={isdownloadModal ? "Download File" : "Upload File"}
        assetBaseUrl={assetBaseUrl}
      >
        <div className="flex gap-6">
          {!isdownloadModal && (
				<>
				  <div
					className={`border-dashed border-2 border-gray-300 rounded p-6 w-1/2 text-center ${dragActive ? "bg-gray-100" : ""}`}
					onDragOver={(e) => {
					  e.preventDefault();
					  setDragActive(true);
					}}
					onDragLeave={() => setDragActive(false)}
					onDrop={handleDrop}
				  >
					<input
					  type="file"
					  multiple
					  className="hidden"
					  id="file-upload"
					  onChange={handleBrowse}
					/>
					<label htmlFor="file-upload" className="cursor-pointer">
					  <p className="text-gray-500">Drop your files here or click to browse</p>
					</label>
				  </div>
				</>
		  )}
     
	 
          <div className={isdownloadModal ? "w-full" : "w-1/2"}>
            <h4 className="font-semibold mb-2">Uploaded Files</h4>
            <ul>
			  {files?.map((file, index) => (
				<li key={index} className="flex justify-between items-center mb-2">
				  <span className="text-sm text-gray-700 w-7/12 break-words">
					{file.name} {/* Display the corresponding file name */}
				  </span>
				  <a
					href="#"
					target="_blank"
					rel="noopener noreferrer"
					className="text-blue-500 mr-4"
					onClick={(e) => {
					  e.preventDefault(); // Prevent default anchor behavior
              window.open(`/download-file/${encodeURIComponent(file.url.replace('upload/', ''))}`, '_blank');
					}}
				  >
					Download
				  </a>
				   {!isdownloadModal && (
					<>
					  <button
						type="button"
						onClick={() => handleRemoveFile(index)}
						className="text-red-500"
					  >
						✕
					  </button>
				   </>
				   )}
				</li>
			  ))}
			</ul>

          </div>
        </div>
      </Modal>
    </div>
  );
});

export default ProfileCard;
