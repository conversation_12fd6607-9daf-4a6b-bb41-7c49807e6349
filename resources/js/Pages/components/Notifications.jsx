import React, { useState, useEffect } from 'react';
import { Inertia } from "@inertiajs/inertia";

const Notifications = ({ notifications }) => {
    const [notificationList, setNotificationList] = useState(notifications);
    if (!notificationList || notificationList.length === 0) {
        return (
            <div className="bg-white rounded-lg shadow p-4 home-notifications">
                <h1 className="chart-heading">No new notifications</h1>
            </div>
        );
    }
	
    const handleDeleteNotification = (notificationId) => {
        setNotificationList(notificationList.filter(notification => notification.id !== notificationId));
        Inertia.post('/home/<USER>/notification', {
            id: notificationId,
        }, {
            onSuccess: () => {
                //console.log(`Deleted notification ID: ${notificationId}`);
            },
            onError: (error) => {
                console.error('Error:', error);
            },
        });
    };

const HandlerDeleteAllNotification = () => {
	
       Inertia.post('/home/<USER>/all/notification', {}, {
            onSuccess: () => {
                setNotificationList([]);
            },
            onError: (error) => {
                console.error('Error:', error);
            },
        });
};

function truncateDescription(description, charLimit) {
  return description.length > charLimit 
    ? description.substring(0, charLimit) + '...' 
    : description;
}


    return (  
        <div className="bg-white rounded-lg shadow p-4 home-notifications">
            <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Notifications</h3>
                <button className="border px-2 py-1.5 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-200 mr-2" onClick={() => HandlerDeleteAllNotification()} >
                    Mark all as read
                </button>
            </div>
            <ul className="space-y-4 max-h-96 overflow-y-auto">
                {notificationList.map((notification, index) => (
  ['Plan_Created', 'Plan_Shared', 'Job_Shared'].includes(notification.type) ? (
    <li
      key={notification.id || index}
      className="flex justify-between items-start hover:bg-gray-200 px-2 py-1.5 gap-2 rounded-md"
      type={notification.type}
      id={`notification-${notification.id}`}
    >
      
	  <div className="flex space-x-4">
		
                                <div>
                                    <h4 className="font-medium"> 
                                        {notification.type === 'Plan_Created'
                                            ? 'New Plan Added'
                                            : notification.type === 'Plan_Shared'
                                            ? 'Plan Shared'
                                            : 'Job Shared'}
                                    </h4>
                                    <p className="text-gray-500 text-sm">
                                        {notification.entity_name}: 
                                        <span className="font-normal whitespace-wrap">
                                            {truncateDescription(notification.description, 200)}
                                        </span>
                                    </p>
                                </div>
                            </div>
							
						<button 
                            onClick={() => handleDeleteNotification(notification.id)} 
                            className="flex self-center text-gray-400 hover:text-red-600"
                        >
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="0.5" y="0.5" width="31" height="31" rx="5.5" fill="white"/>
                                <rect x="0.5" y="0.5" width="31" height="31" rx="5.5" stroke="#EAECF0"/>
                                <path d="M22.875 9.75H9.125C8.95924 9.75 8.80027 9.81585 8.68306 9.93306C8.56585 10.0503 8.5 10.2092 8.5 10.375C8.5 10.5408 8.56585 10.6997 8.68306 10.8169C8.80027 10.9342 8.95924 11 9.125 11H9.75V22.25C9.75 22.5815 9.8817 22.8995 10.1161 23.1339C10.3505 23.3683 10.6685 23.5 11 23.5H21C21.3315 23.5 21.6495 23.3683 21.8839 23.1339C22.1183 22.8995 22.25 22.5815 22.25 22.25V11H22.875C23.0408 11 23.1997 10.9342 23.3169 10.8169C23.4342 10.6997 23.5 10.5408 23.5 10.375C23.5 10.2092 23.4342 10.0503 23.3169 9.93306C23.1997 9.81585 23.0408 9.75 22.875 9.75ZM21 22.25H11V11H21V22.25ZM12.25 7.875C12.25 7.70924 12.3158 7.55027 12.4331 7.43306C12.5503 7.31585 12.7092 7.25 12.875 7.25H19.125C19.2908 7.25 19.4497 7.31585 19.5669 7.43306C19.6842 7.55027 19.75 7.70924 19.75 7.875C19.75 8.04076 19.6842 8.19973 19.5669 8.31694C19.4497 8.43415 19.2908 8.5 19.125 8.5H12.875C12.7092 8.5 12.5503 8.43415 12.4331 8.31694C12.3158 8.19973 12.25 8.04076 12.25 7.875Z" fill="#F04438"/>
                            </svg>
                        </button>	
	  
    </li>
  ) : notification.type === 'External_Added_to_plan' ? (
    <li
      key={notification.id || index}
      className="flex justify-between items-start hover:bg-gray-200 px-2 py-1.5 gap-2 rounded-md"
      type={notification.type}
      id={`notification-${notification.id}`}
    >
      <div className="flex space-x-4">
							 
							
							
                                <div>
                                    <h4 className="font-medium">
                                        {notification.entity_name} added to Plan
                                    </h4>
                                    <p className="text-gray-500 text-sm">
                                        <time dateTime={new Date(notification.created_at).toISOString()}>
                                            {new Date(notification.created_at).toLocaleDateString()}
                                        </time>
                                    </p>
                                </div>
                            </div>
							
						<button 
                            onClick={() => handleDeleteNotification(notification.id)} 
                            className="flex self-center text-gray-400 hover:text-red-600"
                        >
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="0.5" y="0.5" width="31" height="31" rx="5.5" fill="white"/>
                                <rect x="0.5" y="0.5" width="31" height="31" rx="5.5" stroke="#EAECF0"/>
                                <path d="M22.875 9.75H9.125C8.95924 9.75 8.80027 9.81585 8.68306 9.93306C8.56585 10.0503 8.5 10.2092 8.5 10.375C8.5 10.5408 8.56585 10.6997 8.68306 10.8169C8.80027 10.9342 8.95924 11 9.125 11H9.75V22.25C9.75 22.5815 9.8817 22.8995 10.1161 23.1339C10.3505 23.3683 10.6685 23.5 11 23.5H21C21.3315 23.5 21.6495 23.3683 21.8839 23.1339C22.1183 22.8995 22.25 22.5815 22.25 22.25V11H22.875C23.0408 11 23.1997 10.9342 23.3169 10.8169C23.4342 10.6997 23.5 10.5408 23.5 10.375C23.5 10.2092 23.4342 10.0503 23.3169 9.93306C23.1997 9.81585 23.0408 9.75 22.875 9.75ZM21 22.25H11V11H21V22.25ZM12.25 7.875C12.25 7.70924 12.3158 7.55027 12.4331 7.43306C12.5503 7.31585 12.7092 7.25 12.875 7.25H19.125C19.2908 7.25 19.4497 7.31585 19.5669 7.43306C19.6842 7.55027 19.75 7.70924 19.75 7.875C19.75 8.04076 19.6842 8.19973 19.5669 8.31694C19.4497 8.43415 19.2908 8.5 19.125 8.5H12.875C12.7092 8.5 12.5503 8.43415 12.4331 8.31694C12.3158 8.19973 12.25 8.04076 12.25 7.875Z" fill="#F04438"/>
                            </svg>
                        </button>	
							
    </li>
  ) : null // Return null for any other type
))}

            </ul>
        </div>
    );
};

export default Notifications;
