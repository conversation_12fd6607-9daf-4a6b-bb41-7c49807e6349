import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useState } from "react";
import { Inertia } from "@inertiajs/inertia";
import CandidateDetailsModal from './CandidateDetailsModal';

export default function Card({ showCalender, assetBaseUrl, id, candidateName,recruitment_id,candidateDetails,skills_list,Pipelinedata,newStageName,interviewerList,notesdata,user_role,userlist,groupedPeoplesCareer,recruitment_name }) {
	console.log({
		assetBaseUrl,
		id,
		candidateName,
		recruitment_id,
		candidateDetails,
		Pipelinedata,
		newStageName,
		interviewerList,
		notesdata,
		user_role,
		userlist,
		groupedPeoplesCareer,
		recruitment_name
	});
	// console.log(groupedPeoplesCareer);
	
  const [startDate, setStartDate] = useState(new Date());
  const [formattedDate, setFormattedDate] = useState(null);
  // const [calendarVisible, setCalendarVisible] = useState(false);
	const [showModal, setShowModal] = useState(false);
	const profileHandler = () => setShowModal(true);
	const closeModal = () => setShowModal(false);

  function clickHandler() {
	   // setCalendarVisible(!calendarVisible);
	document.getElementById(id).click();
  }

  function formatDate(selectedDate) {
	const date = new Date(selectedDate);

	// Format the date to YYYY-MM-DD
	const format = date.toISOString().split("T")[0];
	setStartDate(format);
	setFormattedDate(format);
	// saveDate(format);

  }
  
  // function saveDate(date) {
    // Inertia.post(
      // "/recruitment/saveinterviewdate",
      // {recruitment_id, id, date },
      // {
        // onSuccess: () => {
          // console.log("Date saved successfully");
        // },
        // onError: (error) => {
          // console.error("Error saving date:", error);
        // },
      // }
    // );
  // }
  
  

  return (
	<div className="bg-white px-3 py-2 rounded-lg shadow-md">
	  <div className="flex justify-between text-slate-400">
		<div className="flex items-center">
		  <span className="text-xs mr-2 font-bold">
		    <div className="flex items-center justify-center cursor-pointer h-8 w-8 bg-color bg-opacity-50 rounded-full text-xs font-semibold text-[#667085]">
				{candidateName.slice(0, 2).toUpperCase()}
			  </div>
		  </span>
		  <span className="ms-1 text-black text-sm font-bold">{candidateName}</span>
		</div>
		<div className="flex">
		{ /*<span className="flex items-center mr-2 cursor-pointer">
			{showCalender && (
			  <img
				onClick={clickHandler}
				src={`${assetBaseUrl}images/calendar.svg`}
				alt="Calendar"
				width="20"
				height="20"
			  />
			)}
			<DatePicker
			  className="hidden"
			  selected={startDate}
			  onChange={(date) => formatDate(date)}
			  id={id}
			/>
</span>*/}
		  <span onClick={profileHandler} className="ms-2 flex items-center cursor-pointer">
			<img className="h-5 w-5"
				  src={`${assetBaseUrl}images/eye.svg`}
				  alt="View"
				/>
			
		  </span>
		</div>
	  </div>
	  <div className="text-slate-400 flex mt-3">
		<div className="flex">
		  <span className="flex mr-1 items-center">
			<svg
			  xmlns="http://www.w3.org/2000/svg"
			  width="20"
			  height="20"
			  viewBox="0 0 24 24"
			  fill="none"
			  stroke="currentColor"
			  strokeWidth="2"
			  strokeLinecap="round"
			  strokeLinejoin="round"
			  className="lucide lucide-calendar"
			>
			  <path d="M8 2v4" />
			  <path d="M16 2v4" />
			  <rect width="18" height="18" x="3" y="4" rx="2" />
			  <path d="M3 10h18" />
			</svg>
		  </span>
		  <span className="ms-1 mr-4 text-xs flex items-center">
			{Pipelinedata.interview_date ?? "N/A"}
		  </span>
		</div>
		<div className="flex ms-3">
		  <span className="flex items-center mr-1">
			<svg
			  xmlns="http://www.w3.org/2000/svg"
			  width="20"
			  height="20"
			  viewBox="0 0 24 24"
			  fill="none"
			  stroke="currentColor"
			  strokeWidth="2"
			  strokeLinecap="round"
			  strokeLinejoin="round"
			  className="lucide lucide-message-square"
			>
			  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
			</svg>
		  </span>
		  <span className="ms-1 text-xs flex items-center">{notesdata.length}</span>
		</div>
	  </div>
	  	 <CandidateDetailsModal
			assetBaseUrl={assetBaseUrl}
			showModal={showModal}
			closeModal={closeModal}
			id={id}
			candidateName={candidateName}
			candidateDetails={candidateDetails}
			skills_list={skills_list}
			newStageName={newStageName}
			interviewerList={interviewerList}
			recruitment_id={recruitment_id}
			Pipelinedata={Pipelinedata}
			notesdata={notesdata}
			user_role={user_role}
			userlist={userlist}
			groupedPeoplesCareer={groupedPeoplesCareer}
			recruitment_name={recruitment_name}
		  />
	</div>
  );
}
