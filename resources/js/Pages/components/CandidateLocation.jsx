import React from 'react';

const CandidateLocation = ({ topCountries }) => {
    return (
        <div className="bg-white rounded-[12px] shadow p-4 CandidateLocation">
            <h3 className="font-inter text-[16px] font-semibold leading-[24px] text-left mb-[20px]">
                Top 5 locations of candidates
            </h3>
            <ul className="space-y-3">
                {topCountries.map((location, index) => (
                    <li key={index} className="flex items-center space-x-2">
                        <span className="w-[136px] font-inter text-[12px] font-normal leading-[16px] text-left text-text-secondary">
                            {location.country}
                        </span>
                        <div className="w-full bg-gray-200 rounded-[4px] h-[20px] overflow-hidden">
                            <div
                                className="bg-custom-orange h-full"
                                style={{ width: `${location.percentage}%` }}
                            />
                        </div>
                        <span className="w-8 font-inter text-[12px] font-normal text-text-secondary leading-[16px] text-left">
                            {location.percentage}%
                        </span>
                    </li>
                ))}
            </ul>
        </div>
    );
};

export default CandidateLocation;
