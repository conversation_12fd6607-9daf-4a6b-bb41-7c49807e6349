import React from "react";

const Dropdown = ({ isOpen, items, assetBaseUrl, FaChevronDown, onItemClick }) => {
  return (
    <div className="relative mt-6">
      {isOpen && (
        <div className="absolute right-0 mt-2 w-40 bg-white border border-gray-light rounded-md shadow-lg">
          {items.map(({ icon, text, action }, index) => (
            <div
              key={index}
              className="px-4 py-2 flex items-center space-x-2 cursor-pointer hover:bg-gray-100"
              onClick={() => onItemClick(action)} // Call the action handler
            >
              <img src={icon} alt={text} className="w-[20px] h-[20px]" />
              <span className="text-sm">{text}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Dropdown;
