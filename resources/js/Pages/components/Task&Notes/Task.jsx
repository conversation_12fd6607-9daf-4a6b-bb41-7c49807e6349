import React, { useState, useEffect } from "react";
import { Inertia } from '@inertiajs/inertia';

const Task = ({ candidateName, id, notesdata,userlist,handleUpdateNotes }) => {
  const [notes, setNotes] = useState([]);  // Set initial notes from props
  const [newNote, setNewNote] = useState("");
// console.log(notesdata);
  useEffect(() => {
    // Ensure notesdata is an array and contains multiple nodes
    if (Array.isArray(notesdata)) {
      // Map through the notes and check for 'Notes' and 'created_at'
      const updatedNotes = notesdata.map(note => ({
        ...note,
        timestamp: note.created_at ? new Date(note.created_at).toLocaleDateString() : new Date().toLocaleDateString(),
        text: note.Notes || 'No content',
      }));
      setNotes(updatedNotes);
    } else {
      setNotes([]);  // Default to an empty array if notesdata is not an array
    }
  }, [notesdata]);

  const handleSaveNote = () => {
    if (newNote.trim() !== "") {
      const newActivity = {
        text: newNote,
        timestamp: new Date().toLocaleDateString(),
      };
      setNotes((prevNotes) => [...prevNotes, newActivity]);
      setNewNote("");

      // Send note data to the server
      Inertia.post('/recruitment/candidate/savenote', { 
        id: id, 
        note: newNote,
      });
    }
  };
  
  const handlesavenotes = (value) => {
	 // setNewNote("");
     setNewNote(value);
	 handleUpdateNotes(value);
  };



  const handleCancel = () => {
    setNewNote("");
  };

  return (
    <div className="text-left">
      {/* Note Input Box */}
      <div className="text-box">
        <h3 className="text-[14px] font-semibold text-[#667085] mb-2">Notes</h3>
        
        <textarea
          className="w-full border border-[#EAECF0] rounded-md p-2 text-[14px]"
          rows="4"
          placeholder="Enter your note here"
          value={newNote}
          onChange={(e) => handlesavenotes(e.target.value)}
        />
      </div>

      {/* Display Notes */}
      {notes.length > 0 ? (
        <div className="mt-4 space-y-4">
          {notes.map((note, index) => (
            <div key={index} className="rounded-md p-4">
              <div className="flex items-center justify-between space-x-2">
                {/* Profile Info */}
                <div className="flex items-center space-x-2">
                  <div className="flex items-center justify-center cursor-pointer h-8 w-8 bg-color bg-opacity-50 rounded-full text-xs font-semibold text-[#667085]">
				  {userlist[note.author] && typeof userlist[note.author] === 'string' 
					? userlist[note.author].slice(0, 2).toUpperCase() 
					: ''}  {/* Render empty string if the value is undefined or not a string */}
				</div>

                  <p className="text-[14px] font-semibold">{userlist[note.author]}</p>
                </div>

                {/* Timestamp on the right */}
                <p className="text-[12px] text-gray-500">{note.timestamp}</p>
              </div>

              {/* Note Content */}
              <p className="text-[14px] mt-2">{note.text}</p>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-[14px] mt-4 text-gray-500">
          No activity notes available.
        </p>
      )}
    </div>
  );
};

export default Task;