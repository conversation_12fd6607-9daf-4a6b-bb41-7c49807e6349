import React from 'react';

const CandidateListItem = ({ candidate, handleClick, stageName }) => {
  return (
    <li
      key={candidate.id}
      className="p-2 border-b cursor-pointer hover:bg-gray-100"
      onClick={() => handleClick(candidate, stageName)}
    >
      {candidate.candidate_details.first_name} {candidate.candidate_details.last_name}
    </li>
  );
};

export default CandidateListItem;