import React from "react";

const TaskButton = ({ label, isActive, onClick,assetBaseUrl }) => (
  <button
    className={`task-activity-btn py-1.5 bg-gray-100 px-2 border-0 rounded-md mr-1 text-center w-6/12 cursor-pointer text-sm text-gray-600 font-semibold ${isActive ? "active border-cyan-500 border border-solid" : ""}`}
    onClick={onClick}
  >
    {label}
  </button>
);


export default TaskButton;
