import React from 'react';
// import './LoadingSpinner.css'; // Import the CSS file

const LoadingSpinner = ({ isLoading }) => {
    return (
        <>
            {isLoading && ( // Conditionally render based on isLoading prop
                <div className="fixed inset-0 flex items-center justify-center bg-gray-10 bg-opacity-50 z-50">
                    <div>
                        <div className="loader-overlay">
                            <div className="spinner"></div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default LoadingSpinner;