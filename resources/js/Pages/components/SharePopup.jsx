import React, { useState, useEffect } from 'react';
import { Inertia } from '@inertiajs/inertia';


const SharePopup = ({ assetBaseUrl, showModal, closeModal, editorUsersList = [], viewerUsersList = [],recruitment_id,viewer_shared_with,editor_shared_with }) => {
    if (!showModal) return null;

    const [isFocused, setIsFocused] = useState(false);
    const [firstFocus, setFirstFocus] = useState(false);
    const [query, setQuery] = useState('');
    const [selectedEditorUsers, setSelectedEditorUsers] = useState({}); // Track selected editors
    const [selectedViewerUsers, setSelectedViewerUsers] = useState({}); // Track selected viewers
    const [focusedField, setFocusedField] = useState(null); // Track focused field

useEffect(() => {
  const initializeSelection = (sharedWith, setter) => {
    const initialSelected = {};

    if (Array.isArray(sharedWith)) {
      sharedWith.forEach(userId => {
        initialSelected[userId] = true;
      });
    }

    setter(initialSelected);
  };

  initializeSelection(editor_shared_with, setSelectedEditorUsers);
  initializeSelection(viewer_shared_with, setSelectedViewerUsers);

}, [editor_shared_with, viewer_shared_with]);


    const filterUsers = (users, query) => {
        if (!query) return users;

        const lowerCaseQuery = query.toLowerCase();
        return users.filter(user => user.name.toLowerCase().includes(lowerCaseQuery));
    };

    const handleUserSelection = (userId, type) => {
        if (type === 'editor') {
            setSelectedEditorUsers(prev => ({
                ...prev,
                [userId]: !prev[userId],
            }));
        } else if (type === 'viewer') {
            setSelectedViewerUsers(prev => ({
                ...prev,
                [userId]: !prev[userId],
            }));
        }
    };

const shareBtnHandler = () => {
    // Rename these variables to ensure there's no conflict
    const selectedEditors = Object.keys(selectedEditorUsers || {}).filter(userId => selectedEditorUsers[userId]);
    const selectedViewers = Object.keys(selectedViewerUsers || {}).filter(userId => selectedViewerUsers[userId]);

    // Convert user IDs to integers
    const editorUserIds = selectedEditors.map(userId => parseInt(userId));  
    const viewerUserIds = selectedViewers.map(userId => parseInt(userId));

    // Send the selected users to the server
    Inertia.post('/recruitment/shareproject', { 
        recruitment_id, 
        selectedEditorUsers: editorUserIds, 
        selectedViewerUsers: viewerUserIds, 
    }, {
        onSuccess: () => {
            closeModal();
        },
        onError: (error) => {
            console.error('Error:', error);
        },
    });
};




    useEffect(() => {
        // window.Echo.channel('organisation-share').listen('sharePopupData', (data) => {
            // const selected = {};
            // data[0].forEach(id => {
                // selected[id] = true;
            // });
            // setSelectedsharePopupPeoples(selected);
        // });
    }, []);

    return (
        <div className="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
            <div
                className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"
                onClick={closeModal}
            ></div>
            <div className="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
                <div className="flex justify-between">
                    <h2 className="text-black-900 text-xl font-semibold">Share Recruitment Project</h2>
                    <button type="button" onClick={closeModal} className="text-gray-500 hover:text-gray-800">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div className="flex justify-center items-center mt-2">
                    <div className="bg-white border selectPeople p-2 rounded-lg">
                        <div className="search-container-r items-center mt-2">
                            <label className="text-black-100 text-base font-semibold">Edit</label>
                            <div className="search-container flex items-center mt-2">
                                <img className="search-icon h-4 w-auto" src="/images/MagnifyingGlass.svg" alt="Search Icon" />
                                <input
                                    onFocus={() => {
                                        setIsFocused(true);
                                        setFirstFocus(true);
                                        setFocusedField('editor'); // Set focused field to editor
                                    }}
                                    onBlur={() => setIsFocused(false)}
                                    type="text"
                                    value={query}
                                    onChange={(e) => setQuery(e.target.value)}
                                    className="w-full bordermainBlue outline-none block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-500 sm:text-md sm:leading-6"
                                    placeholder="Search"
                                />
                            </div>
                        </div> 
                        
                        <div className="search-container-r items-center mt-2">
                            <label className="text-black-100 text-base font-semibold">View</label>
                            <div className="search-container flex items-center mt-2">
                                <img className="search-icon h-4 w-auto" src="/images/MagnifyingGlass.svg" alt="Search Icon" />
                                <input
                                    onFocus={() => {
                                        setIsFocused(true);
                                        setFirstFocus(true);
                                        setFocusedField('viewer'); // Set focused field to viewer
                                    }}
                                    onBlur={() => setIsFocused(false)}
                                    type="text"
                                    value={query}
                                    onChange={(e) => setQuery(e.target.value)}
                                    className="w-full bordermainBlue outline-none block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-500 sm:text-md sm:leading-6"
                                    placeholder="Search"
                                />
                            </div>
                        </div>

                        {/* Show message before focus */}
                        {!isFocused && !query && !firstFocus && (
                            <div className="searchBox flex justify-center flex-col items-center">
                                <img src="/images/Illustration.png" alt="Search Icon" />
                                <p className="text-black text-base font-semibold">Start entering share peoples</p>
                            </div>
                        )}

                        {/* Editors List */}
                        {focusedField === 'editor' && (
                            <ul className="mt-4 adddeduser space-y-6 border py-4" style={{ display: (isFocused && query.length > 1) || firstFocus ? 'block' : 'none' }}>
                                <h3 className="text-black-900 font-semibold mb-2">Editors</h3>
                                {filterUsers(editorUsersList, query).map(user => (
                                    <li key={user.id} className="flex justify-between">
                                        <div className="flex items-center gap-2 pl-4">
                                            <div className="border rounded-full w-10 h-10 text-white flex justify-center items-center text-center bg-cyan-500">
                                                <span>{user.name?.charAt(0).toUpperCase() || '?'}</span>
                                            </div>
                                            <div className="space-y-1">
                                                <span className="text-sm font-semibold block">{user.name || 'Unknown'}</span>
                                            </div>
                                        </div>
                                        <input
                                            type="checkbox"
                                            checked={selectedEditorUsers[user.id] || false}  // Adjust based on the state
                                            onChange={() => handleUserSelection(user.id, 'editor')}  // Pass correct 'type'
                                            id={`sharePopupPeople-${user.id}`}
                                            className="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out"
                                        />
                                    </li>
                                ))}
                            </ul>
                        )}

                        {/* Viewers List */}
                        {focusedField === 'viewer' && (
                            <ul className="mt-4 adddeduser space-y-6 border py-4" style={{ display: (isFocused && query.length > 1) || firstFocus ? 'block' : 'none' }}>
                                <h3 className="text-black-900 font-semibold mb-2">Viewers</h3>
                                {filterUsers(viewerUsersList, query).map(user => (
                                    <li key={user.id} className="flex justify-between">
                                        <div className="flex items-center gap-2 pl-4">
                                            <div className="border rounded-full w-10 h-10 text-white flex justify-center items-center text-center bg-cyan-500">
                                                <span>{user.name?.charAt(0).toUpperCase() || '?'}</span>
                                            </div>
                                            <div className="space-y-1">
                                                <span className="text-sm font-semibold block">{user.name || 'Unknown'}</span>
                                            </div>
                                        </div>
                                        <input
                                            type="checkbox"
                                            checked={selectedViewerUsers[user.id] || false}  // Adjust based on the state
                                            onChange={() => handleUserSelection(user.id, 'viewer')}  // Pass correct 'type'
                                            id={`sharePopupPeople-${user.id}`}
                                            className="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out"
                                        />
                                    </li>
                                ))}
                            </ul>
                        )}

                        <div className="flex justify-end mt-4">
                            <button type="button" className="p-2 rounded-lg" onClick={shareBtnHandler}>Share</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SharePopup;
