import React, { useState } from "react";
import { Inertia } from '@inertiajs/inertia';

const Email = ({ pipelinedata = {}, name,getemail,assetBaseUrl }) => {
  const [emailContent, setEmailContent] = useState(`Dear ${name},\n\n`);
  const [title, setTitle] = useState("Right to Rectification"); // Initial value for the title

  const handleTitleChange = (event) => {
    setTitle(event.target.value); // Update title based on user input
  };

  const handleContentChange = (event) => {
    setEmailContent(event.target.value); // Update content as the user types
  };


  const handleSend = () => {
		Swal.fire({
		html: `<h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Right to Rectification for ${name}</h2>
				<p class="px-5 font-normal">Are you sure you want to proceed with the rectification request for <b>${name}</b>?</p>
				<div class="w-full border-t mt-5 border-gray-200"></div>`,
		showDenyButton: true,
		showCancelButton: false,
		confirmButtonText: 'Proceed',
		denyButtonText: 'Cancel',
		reverseButtons: true,
		buttonsStyling: false,
		customClass: {
		  confirmButton: 'bg-white btnsWidth text-blue-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md',
		  denyButton: 'bg-white btnsWidth btnsmargin text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md'
		},
		showCloseButton: true
	}).then((result) => {
		if (result.isConfirmed) {
			Inertia.post('/recruitment/candidate/sendemail', { getemail, title, emailContent }, {
				onSuccess: () => {
					// Handle success
				},
				onError: (error) => {
					console.log('Error:', error);
				},
			});
		}
	});
		
  };
  
 
  return (
    <div>
      {pipelinedata && Object.keys(pipelinedata).length > 0 ? (
        <div
          key={pipelinedata.id}
          className="text-left border border-[#EAECF0] rounded-[8px] p-4 mt-4"
        >
          {/* Input field for editable title */}
          <div className="flex justify-between items-center">
            <input
              type="text"
              value={title}
              onChange={handleTitleChange}
              className="text-[16px] font-semibold p-2 border border-[#EAECF0] rounded-[8px] w-4/5"
            />
			
			<button
              onClick={handleSend}
              className="px-2 py-1 rounded-[6px] text-[14px] border border-[#EAECF0] font-semibold"
            >
              Send
            </button>
          </div>

          {/* Textarea for the user to write content with dynamic name */}
          <div className="mt-4 w-4/5">
            <textarea
              value={emailContent}
              onChange={handleContentChange}
              rows="6"
              className="w-full p-2 border border-[#EAECF0] rounded-[8px]"
              placeholder="Write your message here..."
            />
          </div>
        </div>
      ) : (
        <p className="text-[14px] mt-4 text-center">No data available</p>
      )}
    </div>
  );
};

export default Email;
