import React from "react";

export default function Modal({
  classnames,
  isOpen,
  onClose,
  title,
  assetBaseUrl,
  children,
  handleSubmit,
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
      <div
        className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"
        onClick={onClose}
      ></div>
      <div className={`modal-content step1 relative border flex flex-col justify-center items-center border-gray-300 bg-white shadow-lg rounded-xl py-4 z-50 w-[30%] ${classnames}`}>
        <img
          className="absolute right top-2 w-auto cursor-pointer"
          src={`${assetBaseUrl}images/cross.svg`}
          alt="Close"
          onClick={onClose}
        />
        {title && <h2 className="font-semibold px-4 w-full">{title}</h2>}
        <div className="w-full border-t mt-3 border-gray-200"></div>
        <div className="h-full w-full">
          <div className="h-5/6 flex items-center">
            <div className="w-full">
              <div className="relative py-2 bg-white">
                <div
                  className="absolute inset-0 flex items-center px-1"
                  aria-hidden="true"
                ></div>
              </div>
              <div className="modalscroll px-4">
                {children}
              </div>
              <div className="w-full border-t mt-4 border-gray-200"></div>
              <div className="flex gap-2 w-full px-4 mt-4">
                <button
                  type="button"
                  className="bg-white w-full text-black border p-2 rounded-md"
                  onClick={onClose}
                >
				{handleSubmit ? 'Cancel' : 'Close'}
                </button>
			
                {handleSubmit && (
					  <button
						type="button"
						className="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md"
						onClick={handleSubmit}
					  >
						<span className="block">Save</span>
					  </button>
					)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
