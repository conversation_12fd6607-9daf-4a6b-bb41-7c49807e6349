import React, { useState } from "react";
import { Droppable, Draggable } from "react-beautiful-dnd";
import Modal from "./Modal";
import Card from "./Card";
import { Inertia } from "@inertiajs/inertia";

export default function Section({
  totalCard,
  title,
  stagecolumnID,
  recruitment_id,
  showCalender,
  assetBaseUrl,
  bgcolor,
  candidatesArray,
  updateStageName,
  interviewer<PERSON>ist,
  addNewStageColumn,
  user_role,
  userlist,
  recruitment_name,
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isNewStageModalOpen, setIsNewStageModalOpen] = useState(false);
  const [newStageName, setNewStageName] = useState("");
  const [newStageColumnName, setNewStageColumnName] = useState("");
  const [stageId, setStageId] = useState(null);
  const [recruitmentId, setRecruitmentId] = useState(null);
  const [currentstagecolumnId, setcolumnStageId] = useState(null);
  const openModal = (currentStageName, id, recruitment_id) => {
    setNewStageName(currentStageName);
    setStageId(id);
    setRecruitmentId(recruitment_id);
    setIsModalOpen(true);
  };

  const closeModal = () => setIsModalOpen(false);

  const openNewStageModal = (id, recruitment_id) => {
    setIsNewStageModalOpen(true);
	setcolumnStageId(id);
	setRecruitmentId(recruitment_id);
  };

  const closeNewStageModal = () => {
    setIsNewStageModalOpen(false);
    setNewStageColumnName(""); // Reset the input field
  };

  const handleUpdate = () => {
    Inertia.post(
      "/recruitment/stagename",
      { stageId, newStageName, recruitmentId },
      {
        onSuccess: () => {
          // Update the state in the parent component after success
          updateStageName({ stagecolumnID, stage_name: newStageName });
          closeModal(); // Close modal after success
        },
        onError: (error) => {
          console.log("Error:", error);
        },
      }
    );
  };

  const handleAddNewStage = () => {
	
    // Call the function to add a new stage column in the parent component
    addNewStageColumn({ newStageColumnName, afterStageColumnId: currentstagecolumnId });
    closeNewStageModal(); // Close the modal after creating the new column
  };

  return (
    <Droppable droppableId={stagecolumnID}>
      {(provided) => (
        <div
          className={`${bgcolor} p-4 text-white rounded-lg min-w-96`}
          {...provided.droppableProps}
          ref={provided.innerRef}
        >
          <div className="flex justify-between text-slate-400 ">
            <div>
              <span className="text-xs text-gray-600 mr-2 font-bold">
                {title}
              </span>
              <span className="bg-white px-2 rounded-md ms-1 shadow-md text-xs font-bold">
                {totalCard}
              </span>
            </div>
            <div className="flex">
              {/* On click of this icon, open the modal to update the stage name */}
			  {user_role !== 'Viewer' && (
			  <>
              <span
                className="cursor-pointer"
                onClick={() => openModal(title, stagecolumnID, recruitment_id)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-ellipsis"
                >
                  <circle cx="12" cy="12" r="1" />
                  <circle cx="19" cy="12" r="1" />
                  <circle cx="5" cy="12" r="1" />
                </svg>
              </span>
              {/* On click of this icon, open the modal to create a new stage column */}
              <span
                className="cursor-pointer"
				onClick={() => openNewStageModal(stagecolumnID, recruitment_id)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-plus"
                >
                  <path d="M5 12h14" />
                  <path d="M12 5v14" />
                </svg>
              </span>
			  </>
			)}
            </div>
          </div>

          <div className="mt-3 grid grid-cols-1 gap-4">
            {candidatesArray.map((candidate, index) => (
              <Draggable
                key={candidate.id}
                draggableId={candidate.id.toString()}
                index={index}
              >
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <Card
                      id={candidate.id}
                      showCalender={showCalender}
                      assetBaseUrl={assetBaseUrl}
                      recruitment_id={recruitment_id}
                      candidateName={candidate.name || "Default Name"}
                      candidateDetails={candidate.candidate_details}
                      skills_list={candidate.skills_list}
                      Pipelinedata={candidate.recruitmentPipelinedata}
                      newStageName={title}
                      interviewerList={interviewerList}
                      notesdata={candidate.notesdata}
                      groupedPeoplesCareer={candidate.groupedPeoplesCareer}
                      user_role={user_role}
                      userlist={userlist}
                      recruitment_name={recruitment_name}
                    />
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
	{user_role !== 'Viewer' && (
			  <>
          {/* Modal to update existing stage name */}
          <Modal
            isOpen={isModalOpen}
            onClose={closeModal}
            title="Update Stage Name"
            assetBaseUrl={assetBaseUrl}
            handleSubmit={handleUpdate}
          >
            <div className="flex flex-col gap-2">
              <label htmlFor="stageName" className="text-gray-600 text-sm">
                New Stage Name
              </label>
              <input
                id="stageName"
                type="text"
                value={newStageName}
                onChange={(e) => setNewStageName(e.target.value)}
                placeholder="Enter new stage name"
                className="text-black block w-full p-2 outline-none border border-gray-300 rounded-md"
              />
            </div>
          </Modal>

          {/* Modal to create a new stage column */}
          <Modal
            isOpen={isNewStageModalOpen}
            onClose={closeNewStageModal}
            title="Create New Stage Column"
            assetBaseUrl={assetBaseUrl}
            handleSubmit={handleAddNewStage}
          >
            <div className="flex flex-col gap-2">
              <label htmlFor="newStageColumn" className="text-gray-600 text-sm">
                New Stage Column Name
              </label>
              <input
                id="newStageColumn"
                type="text"
                value={newStageColumnName}
                onChange={(e) => setNewStageColumnName(e.target.value)}
                placeholder="Enter new stage column name"
                className="text-black block w-full p-2 outline-none border border-gray-300 rounded-md"
              />
            </div>
          </Modal>
		  </>
	)}
        </div>
      )}
    </Droppable>
  );
}
