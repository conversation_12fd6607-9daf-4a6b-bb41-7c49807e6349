import React, { useState } from "react";

const Activity = ({ profileName, profileAvatar }) => {
  const [notes, setNotes] = useState([]);
  const [newNote, setNewNote] = useState("");

  const handleSaveNote = () => {
    if (newNote.trim() !== "") {
      const newActivity = {
        text: newNote,
        timestamp: new Date().toLocaleDateString(),
      };
      setNotes([...notes, newActivity]);
      setNewNote("");
    }
  };

  const handleCancel = () => {
    setNewNote("");
  };

  return (
    <div className="text-left">
      {/* Note Input Box */}

      <div className="text-box">
        <h3 className="text-[14px] font-semibold text-[#667085]">Notes</h3>
        <div className="flex justify-end space-x-2 mb-2">
          <button
            className="px-2 py-1 border border-[#EAECF0] rounded-md font-semibold"
            onClick={handleCancel}
          >
            Cancel
          </button>
          <button
            className="px-2 py-1 bg-mainBlue text-white rounded-md font-semibold"
            onClick={handleSaveNote}
          >
            Save
          </button>
        </div>
        <textarea
          className="w-full border border-[#EAECF0] rounded-md p-2 text-[14px]"
          rows="4"
          placeholder="Enter your note here"
          value={newNote}
          onChange={(e) => setNewNote(e.target.value)}
        />
      </div>

      {/* Display Notes */}
      {notes.length > 0 ? (
        <div className="mt-4 space-y-4">
          {notes.map((note, index) => (
            <div key={index} className=" rounded-md p-4">
              <div className="flex items-center justify-between space-x-2">
                {/* Profile Info */}
                <div className="flex items-center space-x-2">
                  <img
                    src={profileAvatar}
                    alt="Profile"
                    className="w-8 h-8 rounded-full object-cover"
                  />
                  <p className="text-[14px] font-semibold">{profileName}</p>
                </div>

                {/* Timestamp on the right */}
                <p className="text-[12px] text-gray-500">{note.timestamp}</p>
              </div>

              {/* Note Content */}
              <p className="text-[14px] mt-2">{note.text}</p>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-[14px] mt-4 text-gray-500">
          No activity notes available.
        </p>
      )}
    </div>
  );
};

export default Activity;
