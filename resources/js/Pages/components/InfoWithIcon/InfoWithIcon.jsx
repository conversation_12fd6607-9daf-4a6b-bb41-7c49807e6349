import React from "react";

const InfoWithIcon = ({ icon, text, link, assetBaseUrl, type,countryManager,handleCountryManagerChange,user_role,colorclass }) => (
  <div className="flex items-center space-x-2 mt-2">
    <img src={icon} alt={text} className="w-[20px] h-[20px]" />
    {link ? (
      <a
        href={link}
        target="_blank"
        rel="noopener noreferrer"
        className={`text-primary text-[14px] ${colorclass}`}
      >
        {text}
      </a>
    ) : type === "input" ? (
      <div className="flex flex-row ">
        <div className="text-[14px] self-center">{text} - </div>
		{user_role !== 'Viewer' ? (
			  <>
				 <input
				  type="text"
				  placeholder="Enter the country name"
				  className="interviewername-p border ml-2 border-gray-300 rounded px-2 py-1 text-[14px] mt-1"
				  value={countryManager}
				  onChange={handleCountryManagerChange}
				/>
			  </>
			) : (
			  <div className="flex flex-row ">
				<div className="text-[14px] self-center"> - {countryManager}</div>
			  </div>
		)}

      </div>
    ) : (
      <div className="text-[14px]">{text}</div>
    )}
  </div>
);

export default InfoWithIcon;
