import React from "react";

const Skills = ({ skills }) => {
  // Ensure `skills` is an array and not empty
  const skillsList = Array.isArray(skills) && skills.length > 0 ? skills : [];

  return (
    <div className="text-left">
      <h3 className="text-[14px] font-semibold text-[#667085]">Skills</h3>
      <ul className="list-disc text-[14px] flex py-2 flex-wrap gap-x-4 gap-y-2">
        {skillsList.length > 0 ? (
          skillsList.map((skill, index) => (
            <li
              key={index}
              className={`flex items-center text-sm p-2 rounded text-white 
                ${skill.skill_type === "AI Generated" ? "bg-purple-500" : "bg-green-500"}
              `}
            >
              {skill.skill_name}
            </li>
          ))
        ) : (
          <li>N/A</li>
        )}
      </ul>
    </div>
  );
};

export default Skills;
