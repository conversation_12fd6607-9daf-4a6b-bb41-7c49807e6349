import React from 'react';
import { Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  Title,
} from 'chart.js';

ChartJS.register(ArcElement, Tooltip, Legend, Title);

const GenderDistribution = ({ genderCount }) => {
  // Extract gender data from the props
  const maleCount = genderCount.find(item => item.gender === 'Male')?.gender_count || 0;
  const femaleCount = genderCount.find(item => item.gender === 'Female')?.gender_count || 0;
  const unspecifiedCount = genderCount.find(item => item.gender === 'Not Applicable')?.gender_count || 0;

  const data = {
    labels: ['Male', 'Female', 'Unspecified'],
    datasets: [
      {
        data: [maleCount, femaleCount, unspecifiedCount],
        backgroundColor: ['#3B82F6', '#FFA347', '#8B5CF6'],
      },
    ],
  };

  const total = data.datasets[0].data.reduce((sum, current) => sum + current, 0);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    cutout: '75%',
  };

  return (
    <div className="bg-white rounded-[12px] shadow p-4 GenderDistribution">
      <h3 className="font-inter text-[16px] font-semibold leading-[24px] text-left mb-[20px]">
        Total number with gender distribution
      </h3>
      <div className="flex justify-center">
        <div className="flex justify-center items-center h-52 w-52 relative">
          <Doughnut data={data} options={options} />

          <div className="absolute inset-0 flex items-center justify-center text-2xl font-semibold right-[190px]">
            {total}
          </div>

          <div className="mt-4">
            <div className="flex justify-between gap-2 w-36">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-secondary-blue rounded-[50px] mr-2"></div>
                <span className="text-sm leading-5">Male</span>
              </div>
              <div className="flex items-center">
                <span className="text-sm leading-5">
                  {maleCount} ({((maleCount / total) * 100).toFixed(2)}%)
                </span>
              </div>
            </div>
            <div className="flex justify-between gap-2 w-36">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-custom-orange rounded-[50px] mr-2"></div>
                <span className="text-sm leading-5">Female</span>
              </div>
              <div className="flex items-center">
                <span className="text-sm leading-5">
                  {femaleCount} ({((femaleCount / total) * 100).toFixed(2)}%)
                </span>
              </div>
            </div>
            <div className="flex justify-between gap-2 w-36">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-secondary-voilet rounded-[50px] mr-2"></div>
                <span className="text-sm leading-5">Unspecified</span>
              </div>
              <div className="flex items-center">
                <span className="text-sm leading-5">
                  {unspecifiedCount} ({((unspecifiedCount / total) * 100).toFixed(2)}%)
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenderDistribution;
