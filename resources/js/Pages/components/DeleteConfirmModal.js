import React, { useState } from 'react';
import { Inertia } from '@inertiajs/inertia';


export const DeleteConfirmModal = ({ data, message, endpoint, assetBaseUrl, successCallback }) => {
  return Swal.fire({
    html: `
      <div class="px-5 w-full flex justify-center mb-3">
        <img class="h-10 w-10" src="${assetBaseUrl}images/redTrashIcon.svg" alt="Delete Icon" />
      </div>
      <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Delete</h2>
      <p class="px-5 font-normal">${message}</p>
      <div class="w-full border-t mt-5 border-gray-200"></div>
    `,
    showDenyButton: true,
    confirmButtonText: "Delete",
    denyButtonText: "Cancel",
    reverseButtons: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: 'bg-white text-red-500 font-semibold border p-2 rounded-md',
      denyButton: 'bg-white text-black font-semibold border p-2 rounded-md'
    },
    showCloseButton: true
  }).then((result) => {
    if (result.isConfirmed) {
      // Proceed with the delete operation for the passed data (id, name, email, etc.)
      Inertia.post(endpoint, { data }, {
        onSuccess: () => {
          // Call the success callback after the delete is successful
          successCallback();
        },
        onError: (error) => {
          console.log('Error during deletion:', error);
        },
      });
    }
  });
};


