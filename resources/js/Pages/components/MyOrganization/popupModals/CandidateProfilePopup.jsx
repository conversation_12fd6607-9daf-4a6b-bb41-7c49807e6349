import React, {useState, useContext, useEffect} from 'react';
import ReactDOM from 'react-dom';
import { OrganizationContext } from '../../../../Pages/OrganizationContext';
import ToggleSwitch from './ToggleSwitch';
import { Inertia } from "@inertiajs/inertia";
import CompetenciesPopup from './CompetenciesPopup';
import ProfileNineBoxGrid from './ProfileNineBoxGrid';
import ShowCareerHistory from './ShowCareerHistory';
import ShowNotesSection from './ShowNotesSection';
import Skills from '../../Skills/Skills';
import ShowPlansSection from './ShowPlansSection';
import UpdateRecordPopup from './UpdateRecordPopup';
import Choices from 'choices.js';
import 'choices.js/public/assets/styles/choices.min.css';

const CandidateProfilePopup = ({ showModal, closeModal, candidateData, candidate_id, assetBaseUrl }) => {
if (!showModal) return null;
const [isOpen, setIsOpen] = useState(false);
const {orgCompetencies, setCompetenciesPopup, competenciesPopup,
	 addedPeoplesInOrganisation, setAddedPeoplesInOrganisation, findUserinOrgchart } = useContext(OrganizationContext);
const [activeTab, setActiveTab] = useState("summary");
const [assessmentTab, setAssessmentTab] = useState("competencies");
const [candidateState, setCandidateState] = useState(candidateData);

const [isReadyDropdownOpen, setIsReadyDropdownOpen] = useState(false);
const [selectedReadyOption, setSelectedReadyOption] = useState(candidateData.readiness || 'Ready Future');
const [readinessColorClass, setReadinessColorClass] = useState(candidateData.readinessColorClass || 'bg-blue-500');
const [performance, setPerformance] = useState(candidateData.performance || "Select");
const [updateRecordPopup, setUpdateRecordPopup] = useState(false);
const [Potential, setPotential] = useState(candidateData.potential || "Select");
const [isPotentialOpen, setisPotentialOpen] = useState(false);
const [newKeyStrength, setNewKeyStrength] = useState("");
const [keyStrengths, setKeyStrengths] = useState([]);
const [newDevelopmentArea, setNewDevelopmentArea] = useState("");
const [developmentAreas, setDevelopmentAreas] = useState([]);


const toggleReadyDropdown = () => {
	setIsReadyDropdownOpen(!isReadyDropdownOpen);
};

useEffect(() => {
	if (candidateData) {
		setSelectedReadyOption(candidateData.readiness);
		setReadinessColorClass(candidateData.readinessColorClass || 'bg-blue-500');
	}
}, [candidateData]);

const handleReadyOptionClick = (option) => {
    let newColorClass, updatenewStatus;
    if (option === 'Ready Now') {
        newColorClass = 'node-readiness-green';
        updatenewStatus = 1;
    } else if (option === 'Nearly Ready') {
        newColorClass = 'bg-[#FFF6ED] text-[#FFA347]';
        updatenewStatus = 2;
    } else if (option === 'Not Ready') {
        newColorClass = 'node-potential-grey';
        updatenewStatus = 3;
    } else {
        newColorClass = 'node-readiness-red';
        updatenewStatus = 0;
	}
	
setSelectedReadyOption(option);
setReadinessColorClass(newColorClass);
setIsReadyDropdownOpen(false);
Inertia.post("/my-organization/save/ready_future", { id: candidate_id, selected_option: updatenewStatus }, {
onSuccess: () => {
	candidateData.readiness=option;
	candidateData.readinessColorClass=newColorClass;
	setCandidateState(candidateData);
	// console.log(`${field} updated successfully to ${newValue}`);
},
onError: (error) => {
	// console.error(`Failed to update ${field}:`, error);

},
});
};

const handlePotentialToggle = (value) => {
	let potentialColor= value === "High" ? "node-potential-blue" :
	value === "Medium" ? "node-potential-orange" : "node-potential-grey";

	setPotential(value);
	setisPotentialOpen(false);

	Inertia.post("/my-organization/save/potential", { id: candidate_id, selected_option: value }, {
		onSuccess: () => {

			candidateData.potential=value;
			candidateData.potentialColorClass=potentialColor;
			setCandidateState(candidateData);
		
		},
		onError: (error) => {
			// console.error(`Failed to update ${field}:`, error);

		},
		});

};

const handlePerformanceToggle = (value) => {
        setPerformance(value);
        setIsOpen(false);
		Inertia.post("/my-organization/save/performance", { id: candidate_id, selected_option: value }, {
			onSuccess: () => {
				candidateData.performance=value;
				setCandidateState(candidateData);
			
			},
			onError: (error) => {
				// console.error(`Failed to update ${field}:`, error);

			},
			});
		
    };
const PotentialOptions = ["High", "Medium", "Low"];
const filteredPotential = PotentialOptions.filter(option => option !== Potential);
const PotentialButtonClass = Potential === "High" ? "node-readiness-green" :
							Potential === "Medium" ? "bg-[#FFF6ED] text-[#FFA347]" :
							Potential === "Low" ? "node-readiness-red" : "border"; 

					

const Performanceoptions = ["High", "Medium", "Low"];

const filteredOptions = Performanceoptions.filter(option => option !== performance);
const PerformanceButtonClass = performance === "High" ? "node-readiness-green" :
					performance === "Medium" ? "bg-[#FFF6ED] text-[#FFA347]" :
					performance === "Low" ? "node-readiness-red" :
					"border"; 
					

const formatDate = (dateString) => {
if (!dateString) return "N/A"; // Handle empty or invalid dates
const options = { year: "numeric", month: "short", day: "numeric" }; // Format: Jan 1, 2023
return new Date(dateString).toLocaleDateString("en-US", options);
};

const toggleDropdown = () => {
	setIsOpen(!isOpen);
};

const togglePotentialDropdown = () => {
	setisPotentialOpen(!isPotentialOpen);
};


const handleToggle = (field) => {
	const newValue = candidateState[field] === 1 ? 0 : 1;
	// Update UI immediately by setting state
	setCandidateState((prev) => ({ ...prev, [field]: newValue }));

	// Make the backend call using Inertia
	Inertia.post("/api/update-internal-people", { id: candidate_id, [field]: newValue }, {
		onSuccess: () => {
			candidateData[field]=newValue;
			setCandidateState(candidateData);
			//console.log(`${field} updated successfully to ${newValue}`);
		},
		onError: (error) => {
			console.error(`Failed to update ${field}:`, error);

			// Revert the state if the API call fails
			// setCandidateState((prev) => ({ ...prev, [field]: candidateState[field] }));
		},
	});
};

if (!showModal) {
	return null; // Don't render anything if the modal is not shown
}

const foundedUser = findUserinOrgchart(addedPeoplesInOrganisation, candidate_id); 
const candidateCompetencies = foundedUser.competencies || [];

const skillsString = candidateData.skills.map(skill => skill.skill_name).join("\n");

const [formData, setFormData] = useState({
    country_manager: '',
    corporate_level: '',
    key_strengths: [],
    development_areas: []
  });

//console.log('candidateData',candidateData);
  useEffect(() => {
    if (candidateData) {
		const parseJSONSafely = (str) => {
			try {
			  return JSON.parse(str);
			} catch (e) {
			  console.error('Invalid JSON:', str);
			  return [];
			}
		  };
		  
		  
		  setFormData({
			country_manager: candidateData.country_manager || '',
			corporate_level: candidateData.corporate_level || '',
			key_strengths: candidateData.key_strengths ? parseJSONSafely(candidateData.key_strengths) : [],
			development_areas: candidateData.development_areas ? parseJSONSafely(candidateData.development_areas) : [],
		  });
	  
		  setKeyStrengths(candidateData.key_strengths ? parseJSONSafely(candidateData.key_strengths) : []);
		  setDevelopmentAreas(candidateData.development_areas ? parseJSONSafely(candidateData.development_areas) : []);
    }
  }, [candidateData]);

const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const updated = { ...prev, [name]: value };
      return updated;
    });
  };

  const SubmitProfileHander = () => {
	//console.log('formData',formData);
	Inertia.post("/my-organization/save/candidatepopup", { id: candidate_id, formData: formData }, {
		onSuccess: () => {
			candidateData.corporate_level = formData.corporate_level;
            candidateData.country_manager = formData.country_manager;
			candidateData.key_strengths = JSON.stringify(formData.key_strengths);
			candidateData.development_areas = JSON.stringify(formData.development_areas);
			setCandidateState(candidateData);
			//setKeyStrengths(formData.key_strengths || []);  // Update state
            //setDevelopmentAreas(formData.development_areas || []); 
			closeModal();
	},
	onError: (error) => {
		// console.error(`Failed to update ${field}:`, error);

	},
	});
  
  };
  

  const handleAddKeyStrength = () => {
    if (newKeyStrength.trim() !== "") {
      setKeyStrengths(prev => [...prev, newKeyStrength.trim()]);
      setFormData(prev => ({ 
        ...prev, 
        key_strengths: [...(prev.key_strengths || []), newKeyStrength.trim()] 
      }));
      setNewKeyStrength(""); // Clear input field
    }
  };


  const handleAddDevelopmentArea = () => {
    if (newDevelopmentArea.trim() !== "") {
      setDevelopmentAreas(prev => [...prev, newDevelopmentArea.trim()]);
      setFormData(prev => ({ 
        ...prev, 
        development_areas: [...(prev.development_areas || []), newDevelopmentArea.trim()] 
      }));
      setNewDevelopmentArea(""); // Clear input field
    }
  };


return ReactDOM.createPortal(
<div className="userprofile fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
<div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40" onClick={closeModal}></div>
<div className="modal-content text-black step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
	<img
		className="absolute right top-2 w-auto cursor-pointer"
		src={`/images/cross.svg`}
		alt="Close"
		onClick={closeModal}
	/>
	<h2 className="font-semibold px-4 w-full">Candidate Details</h2>
	<div className="w-full border-t mt-3 border-gray-200"></div>
	<div className="h-full w-full">
		<div className="h-5/6 flex items-center">
			<div className="w-full">
				<div className="relative py-2 bg-white">
					<div
					className="absolute inset-0 flex items-center px-1"
					aria-hidden="true"
					></div>
				</div>
				<div className="modalscroll px-4">
					<div className="candidateDetails-wrapper">
						<div className="grid gap-4">
							{/* Left Section */}
							<div className="rounded-md">
								<div className="flex space-x-4">
									<div className="flex items-center justify-center cursor-pointer h-10 w-10 bg-color bg-opacity-50 rounded-full text-xs font-semibold text-[#667085]">{candidateData.name.slice(0, 2).toUpperCase()}</div>
									{/* <img src="images/Avatar.png" alt="Avatar" class="block rounded-full cursor-pointer w-7 h-7" /> */}
									<div className="profile-info w-full">
										<div className="flex items-center justify-between">
											<div className="w-1/2 text-left">
											<div className="profile-name text-left text-base font-semibold">{candidateData.name}</div>
											</div>
										  
										 <div className="w-1/2 readyfeature flex justify-end space-x-4 w-fit relative">
										 <button
												onClick={() => setUpdateRecordPopup(true)}
												className="border flex justify-center items-center font-semibold text-sm rounded-lg duration-100  py-2 px-6"
											>
												Update
											</button>

											{updateRecordPopup && (
												<UpdateRecordPopup 
												updateRecordPopup={updateRecordPopup} 
													setUpdateRecordPopup={setUpdateRecordPopup}  
													candidate_id={candidate_id} 
													candidateData={candidateData}
												/>
											)}
										
										</div>
										
										
										
										</div>
										<div className="flex items-center space-x-2">
											<img src="/images/MapPin.png" alt="location" className="w-[20px] h-[20px]" />
											<div className="text-[14px]">{candidateData.location}</div>
										</div>
										<div className="flex items-center space-x-2">
											<ToggleSwitch
												label="Flight Risk"
												checked={candidateState.flightRisk === 1}
												onChange={() => handleToggle("flightRisk")}
											/>
											<ToggleSwitch
												label="Relocatable"
												checked={candidateState.is_relocatable === 1}
												onChange={() => handleToggle("is_relocatable")}
											/>
											<div className="flex items-center relative">
												<span className="mr-2 text-[14px]">Performance:</span>
												<button
													onClick={toggleDropdown}
													className={`font-medium text-[12px] py-[4px] px-[8px] rounded-md flex items-center ${PerformanceButtonClass}`}
												>
													{performance}
													<svg
														className="ml-2 w-4 h-4 transform transition-transform"
														xmlns="http://www.w3.org/2000/svg"
														fill="none"
														viewBox="0 0 24 24"
														stroke="currentColor"
													>
														<path
															strokeLinecap="round"
															strokeLinejoin="round"
															strokeWidth="2"
															d="M19 9l-7 7-7-7"
														/>
													</svg>
												</button>


												{isOpen && (
													<div className="performancedrop absolute mt-2 bg-white border border-gray-200 shadow-lg rounded-md w-40 z-10">
														<ul className="text-gray-700">
															{filteredOptions.map((option) => (
																<li
																	key={option}
																	className={`px-2 py-2 hover:bg-gray-100 cursor-pointer font-medium text-[12px] rounded-md flex items-center ${
																		performance === option ? "bg-gray-200" : ""
																	}`}
																	onClick={() => handlePerformanceToggle(option)}
																>
																	{option}
																</li>
															))}
														</ul>
													</div>
												)}
											</div>


											<div className="flex items-center relative">
												<span className="mr-2 text-[14px]">Potential:</span>
												<button
													onClick={togglePotentialDropdown}
													className={`font-medium text-[12px] py-[4px] px-[8px] rounded-md flex items-center ${PotentialButtonClass}`}
												>
													{Potential}
													<svg
														className="ml-2 w-4 h-4 transform transition-transform"
														xmlns="http://www.w3.org/2000/svg"
														fill="none"
														viewBox="0 0 24 24"
														stroke="currentColor"
													>
														<path
															strokeLinecap="round"
															strokeLinejoin="round"
															strokeWidth="2"
															d="M19 9l-7 7-7-7"
														/>
													</svg>
												</button>


												{isPotentialOpen && (
													<div className="Potentialdrop absolute mt-2 bg-white border border-gray-200 shadow-lg rounded-md w-40 z-10">
														<ul className="text-gray-700">
															{filteredPotential.map((option) => (
																<li
																	key={option}
																	className={`px-2 py-2 hover:bg-gray-100 cursor-pointer font-medium text-[12px] rounded-md flex items-center ${
																		Potential === option ? "bg-gray-200" : ""
																	}`}
																	onClick={() => handlePotentialToggle(option)}
																>
																	{option}
																</li>
															))}
														</ul>
													</div>
												)}
											</div>


											<div className="flex items-center relative">
													<span className="mr-2 text-[14px]">Readiness:</span>
													<button
														onClick={toggleReadyDropdown}
														className={`flex justify-center items-center gap-2 font-semibold transition ease-in-out delay-100 text-sm rounded-lg py-2 ${readinessColorClass} duration-100 p-2`}
													>
														{selectedReadyOption}
														<svg
														className="ml-2 w-4 h-4 transform transition-transform"
														xmlns="http://www.w3.org/2000/svg"
														fill="none"
														viewBox="0 0 24 24"
														stroke="currentColor"
														>
														<path
															strokeLinecap="round"
															strokeLinejoin="round"
															strokeWidth="2"
															d="M19 9l-7 7-7-7"
														/>
														</svg>
													</button>

													{isReadyDropdownOpen && (
														<div className="readynowdrop absolute mt-2 bg-white border border-gray-200 shadow-lg rounded-md w-40 z-10">
															
														{selectedReadyOption === 'Ready Now' ? (
														<>
														<button onClick={() => handleReadyOptionClick('Nearly Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Nearly Ready
														</button>
														<button onClick={() => handleReadyOptionClick('Future Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Future Ready
														</button>
														<button onClick={() => handleReadyOptionClick('Not Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Not Ready
														</button>
														</>
														) : selectedReadyOption === 'Nearly Ready' ? (
														<>
														<button onClick={() => handleReadyOptionClick('Ready Now')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Ready Now
														</button>
														<button onClick={() => handleReadyOptionClick('Future Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Future Ready
														</button>
														<button onClick={() => handleReadyOptionClick('Not Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Not Ready
														</button>
														</>
														) : selectedReadyOption === 'Future Ready' ? (
														<>
														<button onClick={() => handleReadyOptionClick('Ready Now')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Ready Now
														</button>
														<button onClick={() => handleReadyOptionClick('Nearly Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Nearly Ready
														</button>
														<button onClick={() => handleReadyOptionClick('Not Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Not Ready
														</button>
														</>
														) : selectedReadyOption === 'Not Ready' ? (
														<>
														<button onClick={() => handleReadyOptionClick('Ready Now')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Ready Now
														</button>
														<button onClick={() => handleReadyOptionClick('Nearly Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Nearly Ready
														</button>
														<button onClick={() => handleReadyOptionClick('Future Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Future Ready
														</button>
														</>
														) : (
														<>
														<button onClick={() => handleReadyOptionClick('Ready Now')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Ready Now
														</button>
														<button onClick={() => handleReadyOptionClick('Nearly Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Nearly Ready
														</button>
														<button onClick={() => handleReadyOptionClick('Future Ready')} className="w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100">
														Future Ready
														</button>
														</>
														)}



														</div>
													)}
												</div>



										</div>
									</div>
								</div>
								<div className="border border-gray-300 rounded-[8px] p-3 mt-3">
									<div className="grid grid-cols-6 gap-4 text-sm text-gray-700">
										{/* Role */}
										<div>
										<div className="font-medium">Role</div>
										<div>{candidateData.role}</div>
										</div>

										{/* Company */}
										<div>
										<div className="font-medium">Company</div>
										<div>{candidateData.company}</div>
										</div>

										{/* Manager */}
										<div>
										<div className="font-medium">Manager</div>
										<div><input id="updatecountry_manager" type="text" name="country_manager" value={formData.country_manager} onChange={handleInputChange} placeholder="Country Manager"  className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"/></div>
										</div>

										{/* Tenure in Role */}
										<div>
										<div className="font-medium">Tenure in Role</div>
										<div>{candidateData.tenure} years</div>
										</div>

										{/* Corporate Level */}
										<div>
										<div className="font-medium">Corporate Level</div>
										<div><input id="updatecorporate_level" type="text" name="corporate_level" value={formData.corporate_level} onChange={handleInputChange} placeholder="Corporate Level"  className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"/></div>
										</div>

										{/* Potential */}
										
									</div>
								</div>
							</div>
						</div>
						<div className='grid grid-cols-2 lg:grid-cols-2 gap-4'>
							<div className='rounded-md'>
								<div className="border border-gray-300 rounded-[8px] p-3 mt-3">
									<div className="text-sm text-gray-700">
										{/* Key Strengths */}
										<div className="mb-2">
											<div className="font-medium">Key Strengths</div>
											<input
												type="text"
												className="block w-full border p-2 rounded-md"
												placeholder="Enter key strengths"
												value={newKeyStrength}
												onChange={(e) => setNewKeyStrength(e.target.value)}
												onKeyDown={(e) => e.key === "Enter" && handleAddKeyStrength()}
												onBlur={handleAddKeyStrength}
											/>
											<div className="mt-2 flex flex-wrap">
												{keyStrengths.map((strength, index) => (
												<span key={index} className="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
													{strength}
													<button type="button" className="ml-1" onClick={() => {
													setKeyStrengths(prev => {
														const updatedStrengths = prev.filter((_, i) => i !== index);
														setFormData(prev => ({
														...prev,
														key_strengths: updatedStrengths
														}));
														return updatedStrengths;
													});
													}}>&times;</button>
												</span>
												))}
											</div>
										</div>
										{/* Development Areas */}
										<div>
											<div className="font-medium">Development Areas</div>
											
											<input
												type="text"
												className="block w-full border p-2 rounded-md"
												placeholder="Enter development areas"
												value={newDevelopmentArea}
												onChange={(e) => setNewDevelopmentArea(e.target.value)}
												onKeyDown={(e) => e.key === "Enter" && handleAddDevelopmentArea()}
												onBlur={handleAddDevelopmentArea}
											/>
											<div className="mt-2 flex flex-wrap">
												{developmentAreas.map((area, index) => (
												<span key={index} className="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
													{area}
													<button type="button" className="ml-1"  onClick={() => {
													setDevelopmentAreas(prev => {
														const updatedAreas = prev.filter((_, i) => i !== index);
														setFormData(prev => ({
														...prev,
														development_areas: updatedAreas
														}));
														return updatedAreas;
													});
													}}>&times;</button>
												</span>
												))}
											</div>

										</div>
									</div>
								</div>
								<div className="flex border-b border-gray-light space-x-0 sm:space-x-4 mt-3">
									<button
										className={`py-2 px-5 text-[14px] ${
										activeTab === "summary" ? "border-cyan-500 text-cyan-500 border-b-2" : "text-gray-dark"
										}`}
										onClick={() => setActiveTab("summary")}
									>
										Summary
									</button>
									<button
										className={`py-2 px-5 text-[14px] ${
										activeTab === "career_history" ? "border-cyan-500 text-cyan-500 border-b-2" : "text-gray-dark"
										}`}
										onClick={() => setActiveTab("career_history")}
									>
										Career History
									</button>
									<button
										className={`py-2 px-5 text-[14px] ${
										activeTab === "skills" ? "border-cyan-500 text-cyan-500 border-b-2" : "text-gray-dark"
										}`}
										onClick={() => setActiveTab("skills")}
									>
										Skills
									</button>
									<button
										className={`py-2 px-5 text-[14px] ${
										activeTab === "notes" ? "border-cyan-500 text-cyan-500 border-b-2" : "text-gray-dark"
										}`}
										onClick={() => setActiveTab("notes")}
									>
										Notes
									</button>
									<button
										className={`py-2 px-5 text-[14px] ${
										activeTab === "plans" ? "border-cyan-500 text-cyan-500 border-b-2" : "text-gray-dark"
										}`}
										onClick={() => setActiveTab("plans")}
									>
										Plans
									</button>
								</div>

								<div class="border border-gray-light rounded-[8px] p-4 mt-4 h-[350px] overflow-y-auto">
									{activeTab == "summary" &&
										<div class="text-left">
											<h3 class="text-[14px] font-semibold text-[#667085]">Summary</h3>
											<p class="text-[14px] mt-2">{candidateData.summary}
											</p>
										</div>
									}
									{activeTab == "career_history" &&
										<div class="text-left">
											<h3 class="text-[14px] font-semibold text-[#667085]">Career History</h3>
											{candidateData.career_history && candidateData.career_history.length > 0 && (
												<ShowCareerHistory careerHistory={candidateData.career_history}/>
											)}
										</div>
									}
									{activeTab == "skills" &&
										<Skills skills={skillsString}/>
									}
									{activeTab == "notes" &&
										<ShowNotesSection notes={candidateData.notes} />
									}
									{activeTab == "plans" &&
										<ShowPlansSection keyStrengths={keyStrengths} developmentAreas={developmentAreas} plans={candidateData.plan} candID={candidate_id} candidateData={candidateData}/>
									}
								</div>
							</div>
							
							<div className="rounded-md p-4 shadow-md">
								{/* Tabs */}
								<div className="flex border-b border-gray-300 bg-gray-100 py-1 rounded-lg px-3">
									<button
									className={`flex-1 py-2 text-center ${
										assessmentTab === "competencies"
										? "border-b-2 border-cyan-500 text-cyan-500 bg-white rounded-lg font-medium"
										: "text-gray-600"
									}`}
									onClick={() => setAssessmentTab("competencies")}
									>
									Competencies
									</button>
									<button
									className={`flex-1 py-2 text-center ${
										assessmentTab === "grid"
										? "border-b-2 border-cyan-500 text-cyan-500 bg-white rounded-lg font-medium"
										: "text-gray-600"
									}`}
									onClick={() => setAssessmentTab("grid")}
									>
									9 Box Grid
									</button>
								</div>

								{/* Content */}
								<div className="mt-4">
									{assessmentTab === "competencies" &&
									 <div className="space-y-4">

										<button
											className="flex items-center bg-cyan-500 text-white py-2 px-4 rounded-lg hover:bg-cyan-600 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 transition duration-200"
											onClick={() => setCompetenciesPopup(true)}
										>
											{candidateCompetencies.length === 0 ? "Add" : "Edit"}
										</button>

										{candidateCompetencies.map((item, index) => (
											!item.deleted && (
											<div key={index} className="flex items-center">
												<span className="w-40 text-sm font-normal text-gray-600">{item.criteria}</span>
												<div className="flex-1 h-4 bg-gray-200 rounded-full relative">
												<div
													className="absolute top-0 left-0 h-full bg-blue-500 rounded"
													style={{ width: `${(item.score / orgCompetencies[index].score) * 100}%` }}
												></div>
												</div>
												<span className="ml-4 text-sm font-normal text-gray-600">{item.score}</span>
											</div>
											)
										))}
									 </div>
									 }
									{assessmentTab === "grid" && 
										<ProfileNineBoxGrid candidate_id={candidate_id} />                                                    
									}
								</div>
								{competenciesPopup && (
									<CompetenciesPopup competenciesPopup={competenciesPopup} setCompetenciesPopup={setCompetenciesPopup} type="profile" candidate_id={candidate_id} />
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div className="flex gap-2 w-full p-4 text-sm border-t">
            <button
              onClick={closeModal}
              type="button"
              className="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md"
            >
              <span className="block font-medium">Cancel</span>
            </button>
            <button
              onClick={SubmitProfileHander}
              type="button"
              className="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md"
            >
              <span className="block font-semibold">Save Changes</span>
            </button>
          </div>


	</div>
</div>
</div>,
document.body
);
};

export default CandidateProfilePopup;