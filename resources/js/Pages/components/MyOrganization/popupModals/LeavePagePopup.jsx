import React from "react";

const LeavePagePopup = ({ isLeavePopup, handleDiscard, onSave }) => {
  if (!isLeavePopup) return null;

return (
    <div
        className="fixed inset-0 flex items-center justify-center z-50"
        role="dialog"
        aria-modal="true"
    >
        <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        {/* Modal Content */}
        <div className="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50 float-left"
            onClick={(e) => e.stopPropagation()}>
            <div className="flex justify-between">
                <h2 className="text-black-900 text-xl font-semibold"></h2>
                <button type="button" onClick={handleDiscard} className="text-gray-500 hover:text-gray-800 ">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M6 18L18 6M6 6l12 12"
                        />
                    </svg>
                </button>
            </div>
            <p className="font-normal">Do you want to save your changes before leaving?</p>
            <div className="border-t mt-5 border-gray-200"></div>
            <div className="flex justify-between mt-4">
                <button
                    onClick={onSave}
                    className="px-4 py-2 bg-cyan-500 text-white rounded-md hover:bg-cyan-600"
                >
                    Save
                </button>
                <button
                    onClick={handleDiscard}
                    className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                >
                    Discard
                </button>
            </div>
        </div>
    </div>
);
};

export default LeavePagePopup;
