import React, { useState, useContext, useEffect } from 'react';
import { Inertia } from "@inertiajs/inertia";

const UpdateCareerHistoryPopup = ({ 
  updateCareerHistoryPopup, 
  setCareerHistoryPopup,
  candidate_id,
  candidateData
}) => {
  const [careerHistory, setCareerHistory] = useState([
    { role: '', company: { name: '' }, start_date: '', end_date: '', errors: {} }
  ]);


  useEffect(() => {
    if (candidateData && candidateData.career_history && candidateData.career_history.length > 0) {
      // If career_history exists, update the state with the data
      setCareerHistory(candidateData.career_history.map(item => ({
        role: item.role,
        company: { name: item.company.name },
        start_date: item.start_date,
        end_date: item.end_date,
        errors: {} // or populate with error if available
      })));
    }
  }, [candidateData]);


  // Function to validate a specific field
  const validateField = (field, index) => {
    const updatedCareerHistory = [...careerHistory];
    const career = updatedCareerHistory[index];

    // Check if the field is empty and update the error message accordingly
    if (!career[field] || (field === 'company' && !career.company.name)) {
      career.errors[field] = `${field} is required`;
    } else {
      delete career.errors[field]; // remove error if field is valid
    }

    // Update the state with the new error object
    setCareerHistory(updatedCareerHistory);
  };

  // Validate all fields for all entries on form submit
  const validateAllFields = () => {
    const updatedCareerHistory = [...careerHistory];
    updatedCareerHistory.forEach((career, index) => {
      if (!career.role) {
        career.errors.role = 'Role is required';
      } else {
        delete career.errors.role;
      }

      if (!career.company.name) {
        career.errors.company = 'Company is required';
      } else {
        delete career.errors.company;
      }

      if (!career.start_date) {
        career.errors.start_date = 'Start Date is required';
      } else {
        delete career.errors.start_date;
      }

     
    });

    setCareerHistory(updatedCareerHistory);
    return updatedCareerHistory.every((career) => !Object.keys(career.errors).length); // Return true if no errors
  };
  const addCareerHistoryRow = () => {
    setCareerHistory([
      ...careerHistory,
      { role: '', company: { name: '' }, start_date: '', end_date: '', errors: {} }
    ]);
  };
  // Handle form submission
  const updateCareerHistoryData = () => {
    if (validateAllFields()) {
        Inertia.post("/my-organization/save/careerhistory", { id: candidate_id, careerHistory: careerHistory }, {
        onSuccess: () => {
          candidateData.career_history=careerHistory;
          setCareerHistoryPopup(false)
        },
        onError: (error) => {
        
        },
        }); 
      
    }
  };
  const removeCareerHistoryRow = (index) => {
    const updatedCareerHistory = careerHistory.filter((_, i) => i !== index);
    setCareerHistory(updatedCareerHistory);
  };
  // Return the JSX
  return (
    updateCareerHistoryPopup && (
      <div className="fixed inset-0 flex items-center justify-center z-50" style={{ zIndex: 99 }}>
        <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div className="modal-content border updatecareerrecords addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50">
          <div className="flex justify-between items-center p-4">
            <h2 className="font-semibold px-4">
              Edit {candidateData.name} Career History
            </h2>
            <button
              type="button"
              onClick={() => setCareerHistoryPopup(false)}
              className="text-gray-500 hover:text-gray-800"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="w-full border-t border-gray-200"></div>

          <div className="p-4" style={{ height: "100%", maxHeight: "400px" }}>
            <div className="overflow-y-auto max-h-96">
              {careerHistory.map((career, index) => (
                <div key={index} className="grid grid-cols-4 gap-4 items-start">
                  <div>
                    <label htmlFor={`editRole${index}`} className="text-base font-medium labelcolor">Role</label>
                    <input
                      value={career.role}
                      onChange={(e) => {
                        const updatedCareerHistory = [...careerHistory];
                        updatedCareerHistory[index].role = e.target.value;
                        setCareerHistory(updatedCareerHistory);
                        validateField('role', index);
                      }}
                      type="text"
                      className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"
                      placeholder="Role"
                    />
                    {career.errors?.role && <span className="text-red-500 text-sm">{career.errors.role}</span>}
                  </div>
                  <div>
                    <label htmlFor={`editCompany${index}`} className="text-base font-medium labelcolor">Company</label>
                    <input
                      value={career.company.name}
                      onChange={(e) => {
                        const updatedCareerHistory = [...careerHistory];
                        updatedCareerHistory[index].company.name = e.target.value;
                        setCareerHistory(updatedCareerHistory);
                        validateField('company', index);
                      }}
                      type="text"
                      className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"
                    />
                    {career.errors?.company && <span className="text-red-500 text-sm">{career.errors.company}</span>}
                  </div>
                  <div>
                    <label htmlFor={`editStartDate${index}`} className="text-base font-medium labelcolor">Start Date</label>
                    <input
                      value={career.start_date}
                      onChange={(e) => {
                        const updatedCareerHistory = [...careerHistory];
                        updatedCareerHistory[index].start_date = e.target.value;
                        setCareerHistory(updatedCareerHistory);
                        validateField('start_date', index);
                      }}
                      type="date"
                      className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"
                    />
                    {career.errors?.start_date && <span className="text-red-500 text-sm">{career.errors.start_date}</span>}
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <div className="careerHistoryEndDate">
                      <label htmlFor={`editEndDate${index}`} className="text-base font-medium labelcolor">End Date</label>
                      <input
                        value={career.end_date}
                        onChange={(e) => {
                          const updatedCareerHistory = [...careerHistory];
                          updatedCareerHistory[index].end_date = e.target.value;
                          setCareerHistory(updatedCareerHistory);
                        }}
                        type="date"
                        className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"
                      />
                    </div>
                    <div className="flex justify-start items-center" style={{ position: 'relative', top: '9px' }}>
                      <button
                        onClick={() => removeCareerHistoryRow(index)}
                        type="button"
                        className="text-red"
                      >
                        <i className="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {/* Add New Entry Button */}
              <div className="flex justify-start items-center mt-5 mb-3">
                <button
                  onClick={addCareerHistoryRow}
                  type="button"
                  className="text-green-600 py-3 px-4 rounded-lg bg-cyan-500 font-medium text-white hover:text-green-800"
                >
                  <i className="fas fa-plus-circle"></i> Add New Entry
                </button>
              </div>
            </div>
          </div>

          <div className="flex gap-2 w-full p-4 text-sm border-t">
            <div className="mt-4 flex justify-start">
              <button
                onClick={updateCareerHistoryData}
                type="button"
                className="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              >
                Update Career History
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  );
};

export default UpdateCareerHistoryPopup;
