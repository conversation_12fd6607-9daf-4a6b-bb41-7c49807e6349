import React, { useState, useContext, useEffect } from 'react';
import { OrganizationContext } from '../../../OrganizationContext';
import UpdateCareerHistoryPopup from './UpdateCareerHistoryPopup';
import { Inertia } from "@inertiajs/inertia";


const UpdateRecordPopup = ({ 
  selectedItem = {}, 
  updateRecordPopup, 
  setUpdateRecordPopup,
  candidate_id,
  candidateData
}) => {
  const { addedPeoplesInOrganisation, setAddedPeoplesInOrganisation,findUserinOrgchart } = useContext(OrganizationContext);
  const [updateCareerHistoryPopup, setCareerHistoryPopup] = useState(false);
  // State for form fields
  const [formData, setFormData] = useState({
    function: '',
    division: '',
   // readiness: '',
    linkedinURL: '',
    summary: '',
    other_tags: '',
    internal_skills: {
      Specialised: [],
      Common: [],
      Certification: []
    }
  });
  

  useEffect(() => {
    if (candidateData) {
      setFormData({
        function: candidateData.function || '',
        division: candidateData.division || '',
       // readiness: candidateData.readiness || '',
        linkedinURL: candidateData.linkedinURL || '',
        summary: candidateData.summary || '',
        other_tags: candidateData.other_tags || '',
        internal_skills: {
          Specialised: candidateData.skills?.filter(skill => skill.skill_type === "Specialised").map(skill => skill.skill_name) || [],
          Common: candidateData.skills?.filter(skill => skill.skill_type === "Common").map(skill => skill.skill_name) || [],
          Certification: candidateData.skills?.filter(skill => skill.skill_type === "Certification").map(skill => skill.skill_name) || []
        }
      });
    }
  }, [candidateData]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const updated = { ...prev, [name]: value };
      return updated;
    });
  };

  // Save changes to organization list
  const updateInternalPeopleData = () => {
      Inertia.post("/my-organization/update/profile", { id: candidate_id, formData: formData }, {
      onSuccess: () => {
       candidateData.function=formData.function;
       candidateData.division=formData.division;
       //candidateData.readiness=formData.readiness;
       candidateData.linkedinURL=formData.linkedinURL;
       candidateData.other_tags=formData.other_tags;
       candidateData.summary=formData.summary;
       candidateData.skills = Object.entries(formData.internal_skills).flatMap(([skill_type, skill_names], index) =>
        skill_names.map((skill_name, i) => ({
            id: index * 10 + i + 1, 
            skill_name,
            skill_type
        }))
    );
        setUpdateRecordPopup(false)
      },
      onError: (error) => {
      
      },
      }); 
  };

  return (
    updateRecordPopup && (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div className="modal-content border updatepersonrecords addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50">
          <div className="flex justify-between items-center p-4">
            <h2 className="font-semibold px-4">
              Edit {candidateData.name}
            </h2>
            <button
              type="button"
              onClick={() => setUpdateRecordPopup(false)}
              className="text-gray-500 hover:text-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="w-full border-t border-gray-200"></div>

          <div className="p-4 overflow-y-auto" style={{ height: "100%", maxHeight: "400px" }}>
            <div className="grid grid-cols-3 gap-4 ">
              {/* Function Input */}
              <div>
                <label htmlFor="updateProfileFunction" className="text-base font-medium labelcolor">
                  Function
                </label>
                <input id="updateProfileFunction" type="text" name="function" value={formData.function} onChange={handleInputChange} placeholder="Function"
                  className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"/>
              </div>

              {/* Division Input */}
              <div>
                <label htmlFor="updateProfileDivision" className="text-base font-medium labelcolor">
                  Division
                </label>
                <input type="text" name="division" value={formData.division} onChange={handleInputChange} id="updateProfileDivision" 
                  placeholder="Division"
                  className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"/>
              </div>

              <div>
                <label htmlFor="Certification" className="text-base font-medium labelcolor">
                  Certification
                </label>
                <input type="text" name="certification" value={formData.internal_skills.Certification.join(', ')} onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    internal_skills: {
                      ...prev.internal_skills,
                      Certification: e.target.value.split(', ')
                    }
                  }));
                }} 
                  placeholder="Enter certifications"
                  className="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                />
              </div>
              {/* Readiness Dropdown 
              <div>
                <label htmlFor="updateReadiness" className="text-base font-medium labelcolor">
                  Readiness
                </label>
                <fieldset>
                  <ul className="readiness mt-1">
                    {['Ready', 'Not Ready', 'Nearly Ready'].map((status) => (
                      <li key={status} className={status.charAt(0).toLowerCase() + status.slice(1).replace(' ', '')}>
                        <input
                          type="radio"
                          id={status.replace(' ', '')}
                          name="readiness"
                          value={status}
                          checked={formData.readiness === status} // Ensures proper selection tracking
                          onChange={(e) => {
                            setFormData((prev) => ({ ...prev, readiness: e.target.value })); // Update local state
                          }}
                        />
                        <label
                          htmlFor={status.replace(' ', '')}
                          className="text-center font-semibold labelcolor"
                        >
                          
                        </label>
                      </li>
                    ))}
                  </ul>
                </fieldset>
              </div>*/}
            </div>

            {/* Skills Section */}
            <div className="grid grid-cols-3 gap-4 mt-4">
              <div>
                <label htmlFor="Specialised" className="text-base font-medium labelcolor">
                  Specialised
                </label>
                <input type="text" name="specialised" value={formData.internal_skills.Specialised.join(', ')} onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    internal_skills: {
                      ...prev.internal_skills,
                      Specialised: e.target.value.split(', ')
                    }
                  }));
                }} 
                  placeholder="Enter specialised skills"
                  className="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                />
              </div>

              {/* Common Skills */}
              <div>
                <label htmlFor="Common" className="text-base font-medium labelcolor">
                  Common
                </label>
                <input type="text" name="commonskills" value={formData.internal_skills.Common.join(', ')} onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    internal_skills: {
                      ...prev.internal_skills,
                      Common: e.target.value.split(', ')
                    }
                  }));
                }} 
                  placeholder="Enter common skills"
                  className="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                />
              </div>

              {/* Certifications */}
              
            </div>

            {/* Additional Fields */}
            <div className="grid grid-cols-3 gap-4 mt-4">
              {/* LinkedIn URL */}
              <div>
                <label htmlFor="updateProfileLinkedInUrl" className="text-base font-medium labelcolor">
                  LinkedIn Url
                </label>
                <input type="text" name="linkedinURL" value={formData.linkedinURL} onChange={handleInputChange} 
                  id="updateProfileLinkedInUrl" 
                  placeholder="LinkedIn Url"
                  className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"
                />
              </div>

              {/* Registrations */}
              <div>
                <label htmlFor="updateProfileOtherTags" className="text-base font-medium labelcolor">
                  Registrations
                </label>
                <input type="text" name="other_tags" value={formData.other_tags} onChange={handleInputChange} 
                  id="updateProfileOtherTags" 
                  placeholder="Registrations"
                  className="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md"
                />
              </div>
            </div>

            {/* Summary */}
            <div className="grid grid-cols-1 gap-4 mt-4">
              <div>
                <label htmlFor="summary" className="text-base font-medium labelcolor">
                  Summary
                </label>
                  <textarea 
                  id="summary"
                  name="summary"  // Add this line
                  className="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                  placeholder="Enter a brief summary"
                  rows={4}
                  value={formData.summary}
                  onChange={handleInputChange}
                  />
              </div>
            </div>
          </div>

          <div className="flex gap-2 w-full p-4 text-sm border-t">
            <div className="mt-4 flex justify-start">
              <button 
                onClick={updateInternalPeopleData}
                type="button" 
                className="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              >
                Update Profile
              </button>
            
              <button 
                onClick={() => setCareerHistoryPopup(true)}
                type="button" 
                className="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              >
                Update Career History
              </button>
        
              {updateCareerHistoryPopup && (
                  <UpdateCareerHistoryPopup 
                  updateCareerHistoryPopup={updateCareerHistoryPopup} 
                  setCareerHistoryPopup={setCareerHistoryPopup}  
                    candidate_id={candidate_id} 
                    candidateData={candidateData}
                  />
                )}
            </div>


          </div>
        </div>
      </div>
    )
  );
};

export default UpdateRecordPopup;