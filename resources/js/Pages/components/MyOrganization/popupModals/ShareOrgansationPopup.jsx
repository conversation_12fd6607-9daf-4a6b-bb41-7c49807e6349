import React, { useState } from "react";

const ShareOrganisationPopup = ({
  shareOrgansationPopup,
  setShareOrgansationPopup,
  saveOrgansationPopupCallback,
  shareWithUsers
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [firstFocus, setFirstFocus] = useState(false);
  const [query, setQuery] = useState("");
  const [selectedshareOrgansationPeoples, setSelectedshareOrgansationPeoples] =
    useState({});

  const filteredUsers = () => {
    const users = shareWithUsers;
    if (query.length > 1) {
      return users.filter((user) =>
        user.label.toLowerCase().includes(query.toLowerCase())
      );
    }
    return users;
  };

  const handleCheckboxChange = (userValue) => {
    setSelectedshareOrgansationPeoples((prev) => ({
      ...prev,
      [userValue]: !prev[userValue],
    }));
  };

  const shareOrganisation = () => {
    saveOrgansationPopupCallback(selectedshareOrgansationPeoples);
  };

  return (
    shareOrgansationPopup && (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div className="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50">
          <div className="flex justify-between items-center p-4">
            <h2 className="text-sm font-bold chart-heading">Share Organisation</h2>
            <button
              type="button"
              onClick={() => setShareOrgansationPopup(false)}
              className="text-gray-500 hover:text-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="w-full border-t border-gray-200"></div>

          <div className="p-4 overflow-y-auto" >
            <div className="flex justify-center items-center mt-2">
              <div className="bg-white border selectPeople p-2 rounded-lg">
                <div className="search-container flex items-center mt-2">
                  <img
                    className="search-icon h-4 w-auto"
                    src="/images/MagnifyingGlass.svg" // Adjust path as needed
                    alt="Search Icon"
                  />
                  <input
                    onFocus={() => {
                      setIsFocused(true);
                      setFirstFocus(true);
                    }}
                    onBlur={() => setIsFocused(false)}
                    type="text"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    className="w-full bordermainBlue outline-none block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-500 sm:text-md sm:leading-6"
                    placeholder="Search"
                  />
                </div>

                {!firstFocus && (
                  <div className="searchBox flex justify-center flex-col items-center">
                    <img src="/images/Illustration.png" alt="Illustration" />
                    <p className="text-black text-base font-semibold">
                      Start entering share peoples
                    </p>
                  </div>
                )}

                <ul
                  className="mt-4 adddeduser space-y-6 border py-4"
                  style={{ display: (isFocused && query.length > 1) || firstFocus ? "block" : "none" }}
                >
                  {filteredUsers().map((user) => (
                    <li className="flex justify-between" key={user.value}>
                      <div className="flex items-center gap-2 pl-4">
                        <div className="border rounded-full w-10 h-10 text-white flex justify-center items-center text-center bg-cyan-500">
                          <span>{user.label.charAt(0).toUpperCase()}</span>
                        </div>
                        <div className="space-y-1">
                          <span className="text-sm font-semibold block">
                            {user.label}
                          </span>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={!!selectedshareOrgansationPeoples[user.value]}
                        onChange={() => handleCheckboxChange(user.value)}
                        className="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out"
                      />
                    </li>
                  ))}
                </ul>
                <div className="flex justify-end mt-4">
            <button
              onClick={shareOrganisation}
              type="button"
              className="p-2 rounded-lg"
            >
              Share
            </button>
          </div>
              </div>
             
            </div>
          </div>

       
        </div>
      </div>
    )
  );
};

export default ShareOrganisationPopup;
