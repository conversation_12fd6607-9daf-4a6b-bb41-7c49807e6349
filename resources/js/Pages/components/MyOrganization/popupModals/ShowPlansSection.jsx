import React, { useState, useRef, useEffect } from "react"
import PlanDevelopmentModal from "./PlanDevelopmentModal";



const ShowPlansSection = ({ plans,candID,candidateData,developmentAreas,keyStrengths }) => {
  const plansArray = Array.isArray(plans) ? plans : plans ? [plans] : [];
  const [isOpen, setIsOpen] = useState(false)
  if (plansArray.length === 0) {
    return <p className="text-gray-500 text-sm">No plans available</p>;
  }

  return (
    <div className="text-left space-y-2">
      <h3 className="font-semibold text-[14px] text-[#667085]">Plans</h3>
      {plansArray.map((plan) => (
        <div key={plan.id} className="flex justify-between items-center">
          <span className="text-sm text-black font-semibold">{plan.name}</span>
          <div className="flex items-center w-72 gap-3 justify-end">
          <a href={`/plan/${plan.id}`}  className="h-[32px] px-2 py-1 gap-1 rounded-[6px] border border-gray-300 opacity-100 flex items-center justify-center text-sm font-semibold" target="_blank">
            View Plan
          </a>
           <button
            onClick={() => setIsOpen(true)}
            className="h-[32px] px-2 py-1 gap-1 rounded-[6px] border border-gray-300 opacity-100 flex items-center justify-center text-sm font-semibold"
          > Plan Development</button>
            <PlanDevelopmentModal
              isOpen={isOpen}
              onClose={() => setIsOpen(false)}
              planName={plan.name}
              planID={plan.id}
              candID={candID}
              candidateData={candidateData}
              developmentAreas={developmentAreas}
              keyStrengths={keyStrengths}
            />

          </div>
        </div>
      ))}
    </div>
  );
};

export default ShowPlansSection;
