import React, { useState, useEffect, useContext } from "react";
import { OrganizationContext } from "../../../OrganizationContext";

const AddPeoplesToOrganisationPopup = ({
  showPopup,
  onClose,
  selectedPeoples,
  setSelectedPeoples,
  onSearch,
  toggleSelection,
  isSelected,
  users,
  query,
  setQuery
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [firstFocus, setFirstFocus] = useState(false);
  const [filteredUsers, setFilteredUsers] = useState([]);

  const { addedPeoplesInOrganisation, findUserinOrgchart } = useContext(OrganizationContext);

  useEffect(() => {
    if (query.length > 1) {
      const filtered = users.filter((user) => {
        const forename = user.forename || ""; // Handle undefined or null values
        const surname = user.surname || "";
        return `${forename} ${surname}`.toLowerCase().includes(query.toLowerCase());
      });
      setFilteredUsers(filtered);
    } else {
      setFilteredUsers(users); // Show all users when input is empty
    }
  }, [query, users]);
  

  const handleScroll = (event) => {
    const ulElement = event.target;
    const isAtBottom = ulElement.scrollTop + ulElement.clientHeight >= ulElement.scrollHeight - 50;

    if (isAtBottom) {
      onSearch(query); // Call the `onSearch` function for infinite scrolling
    }
  };

  if (!showPopup) return null;

  // Check if user is in organisation
  const isUserInOrganisation = (userId) => {
    //console.log("isUserInOrganisation function", userId);
    return Array.isArray(addedPeoplesInOrganisation) && 
    addedPeoplesInOrganisation.some((existingUser) => existingUser.id === userId);
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      {/* Modal Background */}
      <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50" onClick={onClose}></div>

      {/* Modal Content */}
      <div className="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50"
        onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between">
          <h2 className="text-black-900 text-xl font-semibold">Select people</h2>
          <button type="button" onClick={onClose} className="text-gray-500 hover:text-gray-800">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Select people box with search */}
        <div className="flex justify-center items-center mt-2">
          <div className="bg-white border selectPeople p-2 rounded-lg">
            <p className="text-sm text-gray-500 mt-2">
              Select all the people you would like in your organisation chart
            </p>

            {/* Search Input */}
            <div className="search-container flex items-center mt-2">
              <img
                className="search-icon h-4 w-auto"
                src="/images/MagnifyingGlass.svg"
                alt="Search Icon"
              />
              <input
                type="text"
                value={query}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setQuery(newValue);
                  onSearch(newValue); // Ensure onSearch is called with the latest input value
                }}
                onFocus={() => {
                  setIsFocused(true);
                  setFirstFocus(true);
                }}
                onBlur={() => setIsFocused(false)}
                className="w-full bordermainBlue outline-none block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-500 sm:text-md sm:leading-6"
                placeholder="Search"
              />
              <button
                type="button"
                onClick={() => onSearch(query)}
                className="bg-mainBlue text-white px-4 py-2 rounded-r-md flex items-center justify-center focus:outline-none hover:bg-blue-700"
              >
                <i className="fas fa-search"></i>
              </button>

            </div>

            {/* Placeholder */}
            {!firstFocus && (
              <div className="searchBox flex justify-center flex-col items-center">
                <img src="/images/Illustration.png" alt="Search Icon" />
                <p className="text-black text-base font-semibold">
                  Start entering individuals’ names
                </p>
              </div>
            )}

            {/* User List with Checkboxes */}
            <ul
              className="mt-4 adddeduser space-y-6 border py-4"
              style={{
                display: (isFocused && query.length > 1) || firstFocus ? "block" : "none",
              }}
              onScroll={handleScroll}
            >
             {filteredUsers.map((user, index) => {
                  try {
                    const forenameInitial = user.forename ? user.forename.charAt(0).toUpperCase() : "";
                    const surnameInitial = user.surname ? user.surname.charAt(0).toUpperCase() : "";
                    const fullName = `${user.forename || ""} ${user.surname || ""}`.trim();
                    const isAlreadyAdded = !!findUserinOrgchart(addedPeoplesInOrganisation, user.id);
                    return (
                      <li key={user.id || index} className="flex justify-between">
                        <div className="flex items-center pl-1">
                          <div className="grid grid-cols-8 gap-2 items-center">
                            <div className="border col-span-1 rounded-full w-10 h-10 text-white flex justify-center items-center text-center bg-cyan-500">
                              {forenameInitial}{surnameInitial}
                            </div>
                            <div className="space-y-1 col-span-7 flex-grow">
                              <span className="text-sm font-semibold block">{fullName || "Unknown User"}</span>
                              <span className="text-xs text-gray-500 block">
                                {user.latest_role
                                  ? user.employee_id
                                    ? `${user.latest_role} (${user.employee_id})`
                                    : user.latest_role
                                  : ""}
                              </span>
                            </div>
                          </div>
                        </div>
                        <input
                          type="checkbox"
                          value={user.id}
                          onChange={() => toggleSelection(user)}
                          checked={isSelected(user.id) || isAlreadyAdded}
                          disabled={isAlreadyAdded}
                          className="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out"
                        />
                      </li>
                    );
                  } catch (error) {
                    console.error("Error rendering user:", user, error);
                    return null;
                  }
                })}
            </ul>

            {/* Continue Button */}
            <div className="flex justify-end mt-4">
              <button
                onClick={onClose}
                type="button"
                className="p-2 bg-cyan-500 text-white rounded-lg"
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddPeoplesToOrganisationPopup;