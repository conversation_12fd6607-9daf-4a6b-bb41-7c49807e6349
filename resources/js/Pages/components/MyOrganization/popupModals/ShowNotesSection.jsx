import React, { useState } from "react";
import { FiUploadCloud, FiX, FiFileText } from "react-icons/fi";

const ShowNotesSection = () => {
  const [uploadedFiles, setUploadedFiles] = useState([]);

  // Handle file upload
  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    setUploadedFiles([...uploadedFiles, ...files]);
  };

  // Handle file removal
  const handleRemoveFile = (index) => {
    const updatedFiles = [...uploadedFiles];
    updatedFiles.splice(index, 1);
    setUploadedFiles(updatedFiles);
  };

  return (
    <div>
      <h4 className="text-[14px] font-semibold text-[#667085] mb-2">Notes</h4>

      {/* File Upload Box */}
      <label
        className="flex flex-col items-center justify-center border-2 border-dashed border-blue-300 bg-blue-50 p-6 rounded-lg cursor-pointer hover:bg-blue-100"
      >
        <FiUploadCloud className="text-blue-500 text-3xl mb-2" />
        <span className="text-blue-600 font-medium">Click to upload</span>
        <span className="text-gray-500 text-sm">
          or drag and drop (SVG, PNG, JPG, GIF, PDF)
        </span>
        <input
          type="file"
          className="hidden"
          multiple
          accept=".svg, .png, .jpg, .gif, .pdf"
          onChange={handleFileUpload}
        />
      </label>

      {/* Uploaded Files List */}
      <div className="mt-3">
        {uploadedFiles.length > 0 &&
          uploadedFiles.map((file, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-2 border rounded-lg mt-2 bg-gray-50"
            >
              <div className="flex items-center gap-2">
                <FiFileText className="text-gray-500 text-xl" />
                <span className="text-gray-700 text-sm">{file.name}</span>
              </div>
              <button onClick={() => handleRemoveFile(index)}>
                <FiX className="text-red-500 text-lg" />
              </button>
            </div>
          ))}
      </div>
    </div>
  );
};

export default ShowNotesSection;
