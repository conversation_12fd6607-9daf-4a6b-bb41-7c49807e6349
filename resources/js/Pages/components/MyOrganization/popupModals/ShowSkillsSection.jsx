import React from "react";

const ShowSkillsSection = ({ skills }) => {
  // Group skills by their `skill_type`
  const groupedSkills = skills.reduce((acc, skill) => {
    if (!acc[skill.skill_type]) {
      acc[skill.skill_type] = [];
    }
    acc[skill.skill_type].push(skill);
    return acc;
  }, {});

  return (
    <div className="h-96 overflow-x-auto flex flex-col gap-y-2 text-start p-2">
        <h3 class="text-[16px] font-semibold">Skills</h3>
      {Object.entries(groupedSkills).map(([skillType, skillsArr]) => (
        <div key={skillType}>
          <h4 className="text-sm text-gray-700 font-medium chart-heading">
            {`${skillType} Skills`}
          </h4>
          <div className="flex py-2 flex-wrap gap-x-4 gap-y-2">
            {skillsArr.map((skill) => (
              <div
                key={skill.id}
                className="flex items-center text-xs p-2 rounded-xl skill-container chart-heading"
              >
                <span>{skill.skill_name}</span>
                <span className="ml-2 text-gray-500">({skill.skill_rating}/5)</span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ShowSkillsSection;
