import React, { useState } from "react";
import axios from "axios";
import Switch from "@mui/material/Switch";

const ToggleSwitch = ({ label, checked, onChange }) => {
    return (
        <div className="flex items-center">
            <span className="mr-2 text-[14px]">{label}:</span>
            <Switch
                checked={checked}
                color="primary"
                size="small"
                onChange={onChange}
            />
        </div>
    );
};

export default ToggleSwitch;
