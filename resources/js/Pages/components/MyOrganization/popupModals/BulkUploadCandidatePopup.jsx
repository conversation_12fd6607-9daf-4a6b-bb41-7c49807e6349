import React, { useState } from "react";

const BulkUploadCandidatePopup = ({ bulkuploadcandidate, setBulkUploadCandidatePopup, saveBulkUploadCallback,assetBaseUrl }) => {
  const [csvFile, setCsvFile] = useState(null);
  const [errors, setErrors] = useState([]);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setCsvFile(file);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (csvFile) {
      // Implement your file upload logic here
      //console.log("Uploading file:", csvFile);
      saveBulkUploadCallback([csvFile]); // Send file data to the callback
    } else {
      setErrors(["Please select a file before uploading."]);
    }
  };

  const downloadCSV = () => {
    window.location.href = '/my-organization/download-csv';
  };

  return (
    bulkuploadcandidate && (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div className="modal-content2 border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50">
          <div className="flex justify-between items-center p-4">
            <h2 className="text-black font-semibold">Bulk upload candidates</h2>
            <button
              type="button"
              onClick={() => setBulkUploadCandidatePopup(false)}
              className="text-gray-500 hover:text-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="w-full border-t border-gray-200"></div>

          <div className="p-4 overflow-y-auto">
            <form className="mt-3" onSubmit={handleSubmit}>
              {errors.length > 0 && (
                <div className="text-red-500 text-sm text-center">
                  {errors.map((error, index) => (
                    <p key={index}>{error}</p>
                  ))}
                </div>
              )}

              <div className="mt-1 grid grid-cols-2 gap-1">
                <div className="col-span-2">
                  <label htmlFor="internal-candidate" className="block text-sm text-center font-semibold leading-6 text-gray-900">
                    Internal Candidates
                  </label>
                  <div className="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                    <div className="text-center">
                      {!csvFile ? (
                        <>
                          <div className="flex items-center justify-center">
                            <img className="h-10 w-10" src={`${assetBaseUrl}images/upload.png`} alt="Upload icon" />
                          </div>
                          <div className="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                            <label htmlFor="file-upload" className="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                              <span>Upload a file</span>
                              <input
                                id="file-upload"
                                type="file"
                                className="sr-only"
                                accept=".csv, .xlsx, .xls"
                                onChange={handleFileChange}
                              />
                            </label>
                          </div>
                          <p className="text-xs text-gray-600">(CSV or Excel)</p>
                        </>
                      ) : (
                        <p className="text-sm text-gray-600">
                          <span className="font-semibold">Selected File:</span> {csvFile.name}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="col-span-2">
                  <label htmlFor="cover-photo" className="block text-sm font-medium text-center leading-6 text-gray-900">
                    Download Templates
                  </label>
                  <div className="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                    <div className="text-center">
                      <div className="flex items-center justify-center">
                        <img className="h-10 w-10" src={`${assetBaseUrl}images/download.png`} alt="Download icon" />
                      </div>
                      <div className="mt-4 flex text-sm leading-6 text-gray-600">
                        <button
                          type="button"
                          onClick={downloadCSV}
                          className="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 hover:text-cyan-600"
                        >
                          Download Template Files
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-2 flex items-center justify-end gap-x-6">
                <div className="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                  <button
                    type="button"
                    onClick={() => setBulkUploadCandidatePopup(false)}
                    className="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100"
                  >
                    Cancel
                  </button>
                </div>
                <div className="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                  <button
                    type="button"
                    onClick={handleSubmit}
                    className="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100"
                  >
                    Upload
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    )
  );
};

export default BulkUploadCandidatePopup;
