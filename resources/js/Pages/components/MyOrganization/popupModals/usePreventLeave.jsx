import { useEffect } from "react";
import { router } from "@inertiajs/react";

const usePreventLeave = (setIsleavePopup, setNextUrl, addedPeoplesInOrganisation) => {
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (addedPeoplesInOrganisation.length > 0) {
        event.preventDefault();
        // event.returnValue = "";
        setIsleavePopup(true);
      }
    };

    const handleInertiaNavigation = (event) => {
      if (addedPeoplesInOrganisation.length > 0) {
        event.preventDefault();
        setNextUrl(event.detail.visit.url);
        setIsleavePopup(true);
      }
    };

    // Check if Inertia event fires
    const removeListener = router.on("before", (event) => {
      handleInertiaNavigation(event);
    });

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      removeListener();
    };
  }, [addedPeoplesInOrganisation, setIsleavePopup, setNextUrl]);
};

export default usePreventLeave;
