import React, { useState, useEffect, useRef } from "react";
import axios from "axios";

const PlanDevelopmentModal = ({ isOpen, onClose, planName, planID, candID, candidateData,developmentAreas,keyStrengths }) => {
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState([]);
  const [isThinking, setIsThinking] = useState(false);
  const [currentDate, setCurrentDate] = useState("");
  const inputRef = useRef(null);
  const messagesEndRef = useRef(null);
  const [Isresult, setResult] = useState("");
//console.log("candidateData:", candidateData.name);
  // Only initialize greeting once per open
  useEffect(() => { 
    if (isOpen) {
      const greeting = `Hello! Ready to assist you with your CFO Succession Planning in the banking industry. What aspect would you like to start with?`;
      setMessages([{ role: "assistant", content: greeting, timestamp: new Date() }]);

      const today = new Date();
      const options = { weekday: "long", day: "numeric", month: "long" };
      setCurrentDate(today.toLocaleDateString("en-US", options));

      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage = { role: "user", content: input, timestamp: new Date() };
    setMessages((prev) => [...prev, userMessage]);
    setIsThinking(true);

    axios
      .post("/plan-development/submit", {
        content: input,
        planID,
        candID,
        summary: candidateData.summary,
        career_history: candidateData.career_history,
        developmentAreas: developmentAreas,
        keyStrengths: keyStrengths,
        role: candidateData.role,
        skills: candidateData.skills,
        company: candidateData.company,
        name: candidateData.name,
      })
      .then((response) => {
        const aiReply = response.data?.content || `I've noted your request about "${input}".`;
        const aiResponse = { role: "assistant", content: aiReply, timestamp: new Date() };

        setMessages((prev) => [...prev, aiResponse]);
        setIsThinking(false);
        setResult(formatContent(aiReply));
      })
      .catch((error) => {
        console.error("Error submitting plan:", error);
        setIsThinking(false);
      });

    setInput("");
  };


const handleDownloadPDF = () => {
  if (!Isresult.trim()) return;
  axios
    .post("/plan-development/download-word", {
      content: Isresult,
      planID,
    }, {
      responseType: 'blob' // Important for binary data like PDFs
    })
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `plan-${candidateData.name}.docx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch((err) => {
      console.error("PDF download error:", err);
    });
};

function formatContent(text) {
  return text
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // bold
    .replace(/\n/g, '<br/>') // newlines to <br>
    .trim(); // remove leading/trailing spaces
}

  if (!isOpen) return null;

  const closeModal = () => {
    if (onClose) onClose();
  };


  return (
    <div className="plan-development fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
      <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40" onClick={closeModal}></div>

      <div className="modal-content text-black relative border flex flex-col justify-center items-center border-gray-300 bg-white shadow-lg rounded-xl py-4 z-50 w-[90%] sm:w-[70%] md:w-[50%] lg:w-[30%]">
          <img
          className="absolute right-2 top-2 w-5 h-5 cursor-pointer"
          src={`/images/cross.svg`}
          alt="Close"
          onClick={closeModal}
        />

        <h2 className="font-semibold px-4 w-full">Plan Development</h2>
        <div className="w-full border-t mt-3 border-gray-200"></div>

        <div className="w-full">
          <div className="text-center text-sm text-gray-500 mt-2">{currentDate}</div>

          <div className="chat-messages px-4 my-4 max-h-60 overflow-y-auto space-y-4" id="chatWIndow">
           {messages.map((msg, index) => (
              <div key={index} className="flex items-start gap-2">
                {msg.role === "assistant" ? (
                  <div className="w-8 h-8 rounded-full bg-teal-50 flex items-center justify-center">
                    <img src="/images/ai-2.png" alt="SAM" className="w-6 h-6" />
                  </div>
                ) : (
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-500"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                )}
                <div className="flex-1">
                  <div className="font-medium">{msg.role === "assistant" ? "SAM:" : "You:"}</div>
                  <div
                    className="whitespace-pre-wrap text-gray-700"
                    dangerouslySetInnerHTML={{ __html: formatContent(msg.content) }}
                  />
                </div>
              </div>
            ))}


            {isThinking && <div className="italic text-sm text-gray-500">SAM is thinking...</div>}
            <div ref={messagesEndRef} />
          </div>

          <form onSubmit={handleSubmit} className="px-4">
            <textarea
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Describe the plan in detail"
              rows={3}
              className="w-full p-2 border rounded resize-none"
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit(e);
                }
              }}
            />
            <div className="flex justify-end mt-2 gap-3">
              <button
                type="button"
                className="h-[32px] px-2 py-1 rounded border border-gray-300 text-sm font-semibold"
                disabled={!Isresult.trim()}
                onClick={handleDownloadPDF}
              >
                Download Docs
              </button>
              <button
                type="submit"
                className="bg-teal-500 text-white px-4 py-1 rounded disabled:opacity-50"
                disabled={!input.trim()}
              >
                Send
              </button>
            </div>
          </form>

          <p className="text-xs text-gray-500 mt-3 text-center">
            SAM can make mistakes — please verify important information.
          </p>
        </div>
      </div>
    </div>
  );
};

export default PlanDevelopmentModal;
