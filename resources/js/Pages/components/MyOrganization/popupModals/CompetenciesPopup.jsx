import React, { useState, useEffect, useContext } from "react";

import { OrganizationContext } from "../../../OrganizationContext";
import { Inertia } from '@inertiajs/inertia';

const CompetenciesPopup = ({ competenciesPopup, setCompetenciesPopup, type, candidate_id }) => {
  // const [criteriaRows, setCriteriaRows] = useState([]);
  const [errors, setErrors] = useState([]);
  const [addRowClicked, setAddRowClicked] = useState(false);
  const [competenciesArray, setCompetenciesArray] = useState([]);

  const { isVisible, addedPeoplesInOrganisation,
     setAddedPeoplesInOrganisation, orgCompetencies, setOrgCompetencies, findUserinOrgchart, editingOrganisationId} = useContext(OrganizationContext);

  useEffect(() => {
    // if (type === 'profile') {
      setCompetenciesArray(orgCompetencies);
    // }
  }, [type, orgCompetencies]);
  
  useEffect(() => {
    validateCriteria();
  }, [competenciesArray]);

  const addRow = () => {
    setCompetenciesArray((prevRows) => [
      ...prevRows,
      { id: null, criteria: "", score: 0, deleted: false },
    ]);
    setErrors([]);
    setAddRowClicked(true);
    setTimeout(() => setAddRowClicked(false), 50);
  };

  const removeRow = (index) => {
    setCompetenciesArray((prevRows) =>
      prevRows.map((row, i) => (i === index ? { ...row, deleted: true } : row))
    );
  };

  const validateCriteria = () => {
    const newErrors = [];
    if (addRowClicked) {
      competenciesArray.forEach((row, index) => {
        if (!row.criteria) {
          newErrors.push({ index, field: "criteria", message: "The competency field cannot be empty." });
        } else if (row.criteria.length > 255) {
          newErrors.push({ index, field: "criteria", message: "The competency may not be greater than 255 characters." });
        }
        if (type === 'profile' && row.score > orgCompetencies[index]?.score) { 
          newErrors.push({ index, field: "score", message: `Score cannot exceed ${orgCompetencies[index]?.score}` }); 
        } else if (!row.score || row.score < 0) {
          newErrors.push({ index, field: "score", message: "The score field is not valid." });
        }
      });
    } else {
      newErrors.push({ field: "All", message: "No competencies data found!" });
    }
    setErrors(newErrors);
  };

  const saveCriteria = async () => { 
    validateCriteria();
    try {
      // Handle saving logic based on 'type'
      if (type === 'org') {
        // Save competenciesArray to organization's database
        setOrgCompetencies(competenciesArray);
        setCompetenciesPopup(false); 
      } else if (type === 'profile') {
        
        const user = findUserinOrgchart(addedPeoplesInOrganisation, candidate_id); 
        if (user) {
          user.competencies = competenciesArray; 
          setAddedPeoplesInOrganisation([...addedPeoplesInOrganisation]); 
          // Optionally, save the updated user data to the server
          if (editingOrganisationId) {
            Inertia.post('/save-profile-competencies', {
              organisationId: editingOrganisationId,
              candidateId: candidate_id,
              competencies: user.competencies
            }, {
              onSuccess: () => {
                console.log('Competencies saved successfully');
              },
              onError: (error) => {
                console.error('Error saving competencies', error);
              }
            });
          }
          setCompetenciesPopup(false);
        }
      }
      setCompetenciesPopup(false); 
    } catch (error) {
      // Handle potential errors during saving (e.g., network errors)
      console.error("Error saving competencies:", error); 
      // Display an error message to the user
    }
  };

  return (
    competenciesPopup && (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div className="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50">
          <div className="flex justify-between items-center p-4">
            <h2 className="text-sm font-bold chart-heading">Add Competencies</h2>
            <button
              type="button"
              onClick={() => setCompetenciesPopup(false)}
              className="text-gray-500 hover:text-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="w-full border-t border-gray-200"></div>

          <div className="p-4 overflow-y-auto" style={{ height: "100%", maxHeight: "400px" }}>
            {competenciesArray.map((row, index) =>
              !row.deleted && (
                <div key={index} className="flex flex-col gap-2 mb-4">
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={row.criteria}
                      onChange={(e) => {
                        const value = e.target.value;
                        setCompetenciesArray((prevRows) =>
                          prevRows.map((r, i) => (i === index ? { ...r, criteria: value } : r))
                        );
                      }}
                      className="mt-1 placeholder:text-[#667085] placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                      placeholder="Enter competency"
                      disabled={type === 'profile'}
                    />
                    <div className="competency-score">
                      <input 
                        type="number" 
                        value={row.score} 
                        onChange={(e) => {
                          const value = e.target.value;
                          if (type === 'profile' && value > orgCompetencies[index]?.score) { 
                            // Display an error message 
                            alert(`Score cannot exceed ${orgCompetencies[index]?.score}`);
                          } else {
                            setCompetenciesArray((prevRows) =>
                              prevRows.map((r, i) => (i === index ? { ...r, score: value } : r))
                            );
                          }
                        }} 
                        className="mt-1 placeholder:text-[#667085] placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Score"
                      />
                    </div>
                    {type === 'org' && (
                      <div
                        className="border px-3 rounded-lg critreaDelete cursor-pointer"
                        onClick={() => removeRow(index)}
                        disabled={type === 'profile'}
                      >
                      <img
                        className="w-5 h-5"
                        src="/images/red_trash_icon_without_circle.svg"
                        alt="Trash Icon"
                      />
                      </div>
                    )}
                  </div>

                  {errors.some((error) => error.index === index && error.field === "criteria") && !addRowClicked && (
                    <span className="text-red-500 text-xs">
                      {
                        errors.find(
                          (error) => error.index === index && error.field === "criteria"
                        ).message
                      }
                    </span>
                  )}
                </div>
              )
            )}

            {type === 'org' && (
              <button
                onClick={addRow}
                id="assessmentCriteriaButton"
                className="transition mt-3 flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100"
              >
                <img className="h-3 w-auto" src="/images/Plus.svg" alt="Plus Icon" />
                <span>Add more</span>
              </button>
            )}
          </div>

          <div className="flex gap-2 w-full p-4 text-sm border-t">
            <button
              onClick={() => setCompetenciesPopup(false)}
              type="button"
              className="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md"
            >
              <span className="block font-medium">Cancel</span>
            </button>
            <button
              onClick={saveCriteria}
              type="button"
              className="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md"
            >
              <span className="block font-semibold">Save Changes</span>
            </button>
          </div>
        </div>
      </div>
    )
  );
};

export default CompetenciesPopup;
