const ShowCareerHistory = ({ careerHistory }) => {
    const formatDate = (dateString) => {
      if (!dateString) return "N/A";
      const options = { year: "numeric", month: "short", day: "numeric" };
      return new Date(dateString).toLocaleDateString("en-US", options);
    };
  
    return (
      <>
        {careerHistory && careerHistory.length > 0 ? (
          <div className="flex flex-col items-start w-full mt-2">
            {careerHistory.map((career, index) => (
              <div key={index} className="flex h-max items-start justify-center mb-1">
                <div className="flex flex-col mt-1 items-center justify-center h-full w-2">
                  <div className="rounded-full blueBalls bg-mainBlue"></div>
                  <div className="flex-grow border-l border-mainBlue" style={{ height: "62px" }}></div>
                </div>
                <div className="flex flex-col items-start justify-start pl-4">
                  <h4 className="text-sm font-semibold text-gray-900">{career.role}</h4>
                  <div className="flex gap-x-2">
                    <span className="text-sm font-base text-gray-700">
                      {career.company ? career.company.name : "N/A"}
                    </span>
                  </div>
                  <div className="flex gap-x-2 mb-4">
                    <span className="text-xs font-base GrayText">
                      {formatDate(career.start_date)} -{" "}
                      {career.end_date ? formatDate(career.end_date) : "Present"}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-sm text-gray-500 mt-5">No career history available</p>
        )}
      </>
    );
  };
  
  export default ShowCareerHistory;
  