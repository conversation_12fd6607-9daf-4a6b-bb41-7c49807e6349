import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import Choices from 'choices.js';
import 'choices.js/public/assets/styles/choices.min.css';

const CreatePlanPopup = ({ showPlanModal, closeModal }) => {
 const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    taggedRole: null,
    keyword: null,
    colleagues: [],
    targetRoles: [],
    min_exp: "",
    stepUpCandidates: [],
    keywords: [],
    industries: [],
    companies: [],
    gender: "Not Applicable",
    countries: [],
    ethnicity: false,
    qualifications: [],
    skills: [],
  });

  const [errors, setErrors] = useState({});
  const [wordCount, setWordCount] = useState(0);
  const MAX_WORDS = 300;

  // Refs for Choices.js elements
  const taggedRoleRef = useRef(null);
  const colleaguesRef = useRef(null);
  const industriesRef = useRef(null);
  const countriesRef = useRef(null);
  const companiesRef = useRef(null);

  // Store Choices.js instances
  const choicesInstances = useRef({});

 useEffect(() => {
  const refs = {
    taggedRole: taggedRoleRef,
    colleagues: colleaguesRef,
    industries: industriesRef,
    countries: countriesRef,
    companies: companiesRef,
  };

  Object.entries(refs).forEach(([key, ref]) => {
    if (ref.current) {
      choicesInstances.current[key] = new Choices(ref.current, {
        removeItemButton: true,
        shouldSort: false,
      });

      // Manually add multiple classes
      const container = ref.current.closest('.choices');
      if (container) {
        // container.classList.add("rounded-md");
      }
    }
  });

  return () => {
    Object.values(choicesInstances.current).forEach((instance) =>
      instance?.destroy()
    );
  };
}, [showPlanModal]);

  const handleInputChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  // Handle tag input
  const handleTagInput = (field, value) => {
    if (value.trim() && !formData[field].includes(value.trim())) {
      setFormData(prev => ({ ...prev, [field]: [...prev[field], value.trim()] }));
    }
  };

  // Validation rules
  const validations = {
    1: () => {
      const newErrors = {};
      if (!formData.name.trim()) newErrors.name = "Name is required";
      if (!formData.description.trim())
        newErrors.description = "Description is required";
      if (wordCount > MAX_WORDS)
        newErrors.description = `Maximum ${MAX_WORDS} words allowed`;
      return newErrors;
    },
    2: () => {
      const newErrors = {};
      if (formData.targetRoles.length === 0)
        newErrors.targetRoles = "At least one target role required";
      if (!formData.min_exp) newErrors.min_exp = "Minimum tenure is required";
      return newErrors;
    },
    3: () => {
      const newErrors = {};
      if (formData.countries.length === 0)
        newErrors.countries = "At least one country required";
      return newErrors;
    },
  };

  const handleSubmit = () => {
    const stepErrors = validations[currentStep]();
    if (Object.keys(stepErrors).length === 0) {
      if (currentStep === 3) {
        //console.log("Form submitted:", formData);
        closeModal();
      } else {
        setCurrentStep(currentStep + 1);
      }
    } else {
      setErrors(stepErrors);
    }
  };

  const handleSelectChange = (name, e) => {
    handleInputChange(
      name,
      Array.from(e.target.selectedOptions, (option) => option.value)
    );
  };

  useEffect(() => {
    const words = formData.description.trim().split(/\s+/);
    setWordCount(words.length > 1 ? words.length : 0);
  }, [formData.description]);

  if (!showPlanModal) return null;
  
  return ReactDOM.createPortal(
    <div className="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
      <div className="fixed inset-0 bg-slate-400 opacity-50 z-40" onClick={closeModal} />
      
      <div className="modal-content relative border border-gray-300 bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
        <img
          src="/images/cross.svg"
          alt="Close"
          className="absolute right-2 top-2 w-5 h-5 cursor-pointer"
          onClick={closeModal}
        />
        
        <h2 className="font-semibold px-4">Add New Plan</h2>
        
        {/* Step Indicators */}
        <div className="flex justify-between mt-3 px-4">
          {[1, 2, 3].map(step => (
            <button key={step} onClick={() => currentStep >= step && setCurrentStep(step)}>
              <span className={` p-1 rounded-full px-2 text-xs ${
                currentStep >= step ? 'mainBlue text-white BlueBackground' : 'bg-gray-200 text-gray-400'
              }`}>
                {step}
              </span>
			
              <span className="text-xs font-medium ml-1">
                {['Plan Details', 'Current Experience', 'Additional Details'][step - 1]}
              </span>
            </button>
          ))}
        </div>
        
        <div className="border-t mt-3 border-gray-200" />

        {/* Step 1 Content */}
        {currentStep === 1 && (
          <div className="px-4 mt-4">
            <div className="mb-3">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                className="w-full p-2 border rounded-md border-gray-300 "
                value={formData.name}
				placeholder="Enter plan name"
                onChange={(e) => handleInputChange('name', e.target.value)}
              />
              {errors.name && <span className="text-red-500 text-xs">{errors.name}</span>}
            </div>

            <div className="mb-3">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Description <span className="text-red-500">*</span>
              </label>
              <textarea
                className="w-full p-2 border rounded-md border-gray-300 "
                rows="3"
				placeholder="Enter plan description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
              <div className={`flex justify-between text-xs mt-1 ${errors.description ? 'flex-col-reverse' : ''}`}>
                <span className="text-gray-500">Remaining words: {MAX_WORDS - wordCount}</span>
                {errors.description && (
                  <span className="text-red-500 text-xs">{errors.description}</span>
                )}
              </div>
            </div>

            <div className="mb-3">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Tagged Role
              </label>
              <select
                ref={taggedRoleRef}
                onChange={(e) => handleInputChange('taggedRole', e.target.value)}
				className="choices__input"
				aria-label="Select tagged role"
              >
			  <option value="" disabled selected>
				  Select tagged role
				</option>
				{["Software Developer", "UI/UX Designer", "Project Manager", "QA Engineer"].map(
				  (role, index) => (
					<option key={index} value={role}>
					  {role}
					</option>
				  )
				)}
			  </select>
			  
            </div>

            <div className="mb-3">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Add Colleagues to Plan
              </label>
              <select
                ref={colleaguesRef}
                multiple
				className="choices__input"
				aria-label="Add Colleagues to Plan"
                onChange={(e) => handleInputChange(
                  'colleagues',
                  Array.from(e.target.selectedOptions, option => option.value)
                )}
              >
			   <option value="" disabled >
				  Select Colleague(s)
				</option>
				{["Software Developer", "UI/UX Designer", "Project Manager", "QA Engineer"].map(
				  (role, index) => (
					<option key={index} value={role}>
					  {role}
					</option>
				  )
				)}
			  </select>
            </div>
          </div>
        )}

        {/* Step 2 Content */}
        {currentStep === 2 && (
          <div className="px-4 modalscroll ">
		  <p className="text-sm font-normal labelcolor mt-3 mb-3">Enter any requirements you have for your Succession Plan below.</p>
		  
            <div className="flex items-center justify-between mt-2">
            <div className="mb-3 w-63">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Target Roles
              </label>
              <input
                type="text"
                className="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                placeholder="Enter target role"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleTagInput('targetRoles', e.target.value);
                    e.target.value = '';
                  }
                }}
                onBlur={(e) => {
                  handleTagInput('targetRoles', e.target.value);
                  e.target.value = '';
                }}
              />
              <div className="mt-2 flex flex-wrap gap-2">
                {formData.targetRoles.map((role, index) => (
                  <span key={index} className="bg-gray-200 px-2 py-1 rounded-full text-sm">
                    {role}
                    <button
                      type="button"
                      className="ml-1"
                      onClick={() => handleInputChange(
                        'targetRoles', 
                        formData.targetRoles.filter((_, i) => i !== index)
                      )}
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              {errors.targetRoles && <span className="text-red-500 text-xs">{errors.targetRoles}</span>}
            </div>
			
			 <div className="mb-5">
				  <label className="mb-1 block text-xs font-medium labelcolor">
					Minimum Tenure
				  </label>
				  <input
					type="number"
					className="outline-none block text-center bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md w-32"
					value={formData.min_exp}
					onChange={(e) => handleInputChange('min_exp', e.target.value)}
				  />
				  {errors.min_exp && <span className="text-red-500 text-xs">{errors.min_exp}</span>}
				</div>
				
            </div>
			
			
			
			
			 <div className="mb-3 w-63">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Keyword 
              </label>
              <input
                type="text"
                className="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                placeholder="Enter keyword"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleTagInput('keyword', e.target.value);
                    e.target.value = '';
                  }
                }}
                onBlur={(e) => {
                  handleTagInput('keyword', e.target.value);
                  e.target.value = '';
                }}
              />
              <div className="mt-2 flex flex-wrap gap-2">
                {formData.keyword.map((role, index) => (
                  <span key={index} className="bg-gray-200 px-2 py-1 rounded-full text-sm">
                    {role}
                    <button
                      type="button"
                      className="ml-1"
                      onClick={() => handleInputChange(
                        'keyword', 
                        formData.keyword.filter((_, i) => i !== index)
                      )}
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>
			
			
			
			
			
			
            <div className="mb-3">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Industries
              </label>
              <select
                ref={industriesRef}
                multiple
				className="choices__input"
				aria-label="Industries"
                onChange={(e) => handleInputChange(
                  'industries',
                  Array.from(e.target.selectedOptions, option => option.value)
                )}
              >
			   <option value="" disabled >
				  Select Industry(s)
				</option>
				{["Developer", "Banking"].map(
				  (role, index) => (
					<option key={index} value={role}>
					  {role}
					</option>
				  )
				)}
			  </select>
            </div>
			

           
            <div className="mb-3">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Companies
              </label>
              <select
                ref={industriesRef}
                multiple
				className="choices__input"
				aria-label="Companies"
                onChange={(e) => handleInputChange(
                  'companies',
                  Array.from(e.target.selectedOptions, option => option.value)
                )}
              >
			   <option value="" disabled >
				  Select Industry(s)
				</option>
				{["HSBC", "Samsung"].map(
				  (role, index) => (
					<option key={index} value={role}>
					  {role}
					</option>
				  )
				)}
			  </select>
            </div>
			
          </div>
        )}

        {/* Step 3 Content */}
        {currentStep === 3 && (
          <div className="px-4">
            <div className="mb-3">
              <fieldset>
                <legend className="text-xs font-medium text-gray-700 mb-2">Gender</legend>
                <div className="flex gap-4">
                  {['Male', 'Female', 'Not Applicable'].map((gender) => (
                    <label key={gender} className="flex items-center">
                      <input
                        type="radio"
                        name="gender"
                        value={gender}
                        checked={formData.gender === gender}
                        onChange={(e) => handleInputChange('gender', e.target.value)}
                        className="mr-1"
                      />
                      {gender}
                    </label>
                  ))}
                </div>
              </fieldset>
            </div>

            <div className="mb-3">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Countries
              </label>
              <select
                ref={countriesRef}
                multiple
                onChange={(e) => handleInputChange(
                  'countries',
                  Array.from(e.target.selectedOptions, option => option.value)
                )}
              ></select>
              {errors.countries && <span className="text-red-500 text-xs">{errors.countries}</span>}
            </div>

            <div className="mb-3">
              <label className="mb-1 block text-xs font-medium labelcolor">
                Skills
              </label>
              <input
                type="text"
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter skill"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleTagInput('skills', e.target.value);
                    e.target.value = '';
                  }
                }}
                onBlur={(e) => {
                  handleTagInput('skills', e.target.value);
                  e.target.value = '';
                }}
              />
              <div className="mt-2 flex flex-wrap gap-2">
                {formData.skills.map((skill, index) => (
                  <span key={index} className="bg-gray-200 px-2 py-1 rounded-full text-sm">
                    {skill}
                    <button
                      type="button"
                      className="ml-1"
                      onClick={() => handleInputChange(
                        'skills', 
                        formData.skills.filter((_, i) => i !== index)
                      )}
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="border-t mt-4 border-gray-200" />
        <div className="flex gap-2 px-4 mt-4">
		 {currentStep == 1 && (
            <button
              className="bg-white text-black border p-2 rounded-md w-full flex items-center justify-center gap-2"
              onClick={() => closeModal}
            >
              Cancel
            </button>
          )}
          
          {currentStep > 1 && (
            <button
              className="bg-white text-black border p-2 rounded-md w-full flex items-center justify-center gap-2"
              onClick={() => setCurrentStep(currentStep - 1)}
            >
              <img src="/images/BackArrow.svg" className="w-4 h-4" alt="Back" />
              Back
            </button>
          )}
          
          <button
            className="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md"
            onClick={handleSubmit}
          >
            {currentStep === 3 ? 'Create Plan' : 'Continue'}
            {currentStep !== 3 && <img src="/images/right-arrow.svg" className="w-4 h-4" alt="Next" />}
          </button>
        </div>
		
		
      </div>
    </div>,
    document.body
  );
};

export default CreatePlanPopup;