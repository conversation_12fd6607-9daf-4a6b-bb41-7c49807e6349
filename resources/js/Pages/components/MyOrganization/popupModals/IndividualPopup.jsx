import React, { useEffect, useRef, useState } from 'react';
import Choices from 'choices.js';
import 'choices.js/public/assets/styles/choices.min.css';


const IndividualPopup = ({ individualPopup, setIndividualPopup, saveIndividualCallback,assetBaseUrl,getcountries }) => {
  const [step, setStep] = useState(1);
  const [forename, setForename] = useState("");
  const [surname, setSurname] = useState("");
  const [gender, setGender] = useState("");
  const [selectedCountries, setSelectedCountries] = useState("");
  const selectRef = useRef(null);
  const [countries, setCountries] = useState([]);
  const [role, setRole] = useState("");
  const [startDate, setStartDate] = useState("");
  const [linkedInUrl, setLinkedInUrl] = useState("");
  const [functionValue, setFunctionValue] = useState("");
  const [division, setDivision] = useState("");
  const [empid, setEmpid] = useState("");
  const [errors, setErrors] = useState({});
  const [recordsArray, setRecordsArray] = useState([]);
  
  // Step 3 variables
  const [newQualifications, setNewQualifications] = useState("");
  const [newSkills, setNewSkills] = useState("");
  const [newCertifications, setNewCertifications] = useState("");
  const [newSkillData, setNewSkillData] = useState({
    specialised: [],
    common: [],
    certification: []
  });

  useEffect(() => {
    setCountries(getcountries);
  }, []);
  
  useEffect(() => {
    if (selectRef.current && countries.length > 0) {
      new Choices(selectRef.current, {
        searchEnabled: true,  
        itemSelectText: '',  
      });
    }
  }, [countries]); 
  

  const validateStep1 = () => {
    const newErrors = {};
    if (!forename) newErrors.forename = "Forename is required.";
    if (!surname) newErrors.surname = "Surname is required.";
    if (!gender) newErrors.gender = "Please select a gender.";
    setErrors(newErrors);
    if (Object.keys(newErrors).length === 0) {
      setStep(2); // Move to next step
    }
  };

  const validateStep2 = () => {
    const newErrors = {};
    if (!role) newErrors.role = "Role is required.";
    if (!startDate) newErrors.startDate = "Date Joined is required.";
    if (!functionValue) newErrors.functionValue = "Function is required.";
    if (!division) newErrors.division = "Division is required.";
    // if (!empid) newErrors.empid = "Employee ID is required.";
    setErrors(newErrors);
    if (Object.keys(newErrors).length === 0) {
      setStep(3); // Move to next step
    }
  };

const goBack = () => {
  if (step > 1) {
    setStep(step - 1); // Go to the previous step
  }
};

  const handleStep3 = () => {
   // const newRecord = {
      // forename, 
      // surname, 
      // gender, 
      // selectedCountries, 
      // role, 
      // startDate, 
      // linkedInUrl, 
      // functionValue, 
      // division, 
      // empid, 
      // skillData: newSkillData
    // };
	
	// const updatedRecords = [...recordsArray, newRecord];
    // setRecordsArray(updatedRecords); 
	
    saveIndividualCallback({ 
	  forename, 
      surname, 
      gender, 
      selectedCountries, 
      role, 
      startDate, 
      linkedInUrl, 
      functionValue, 
      division, 
      empid, 
      skillData: newSkillData	});
  };
  
const handleAddSpecialised = () => {
  if (newQualifications.trim() !== "") {
    setNewSkillData(prev => ({
      ...prev,
      specialised: [...prev.specialised, newQualifications.trim()]
    }));
    setNewQualifications(""); // Clear input field
  }
};

const handleAddCommonSkill = () => {
  if (newSkills.trim() !== "") {
    setNewSkillData(prev => ({
      ...prev,
      common: [...prev.common, newSkills.trim()]
    }));
    setNewSkills(""); // Clear input field
  }
};

const handleAddCertification = () => {
  if (newCertifications.trim() !== "") {
    setNewSkillData(prev => ({
      ...prev,
      certification: [...prev.certification, newCertifications.trim()]
    }));
    setNewCertifications(""); // Clear input field
  }
};
  
  

  return (
    individualPopup && (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div className="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50">
          <div className="flex justify-between items-center p-4">
            <h2 className="font-semibold px-4">Add Internal Candidate</h2>
            <button
              type="button"
              onClick={() => setIndividualPopup(false)}
              className="text-gray-500 hover:text-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
			<div className="flex justify-between px-4 mb-3">
              <button><span className={`p-1 rounded-full px-2 text-xs ${step === 1 ? "BlueBackground mainBlue" : "GreenBackground mainGreen"}`}>1</span><span className="text-xs font-medium">Personal Details</span></button>
              <button><span className={`p-1 rounded-full px-2 text-xs ${step === 2 ? "BlueBackground mainBlue" : step > 2 ? "GreenBackground mainGreen" : "grayBackground text-gray-400"}`}>2</span><span className="text-xs font-medium">Professional Details</span></button>
              <button><span className={`p-1 rounded-full px-2 text-xs ${step === 3 ? "BlueBackground mainBlue" : step > 3 ? "GreenBackground mainGreen" : "grayBackground text-gray-400"}`}>3</span><span className="text-xs font-medium">Skills</span></button>
            </div>
			
          <div className="w-full border-t border-gray-200"></div>

          <div className="p-4 overflow-y-auto">
            {step === 1 && (
              <div className="mt-2">
                <p className="text-xs font-normal labelcolor mb-3">If the name is not available please use the employee id to ensure the individual may be identified by yourself and other team members in the system.</p>
				 <div className="mt-2">
                <label className="text-xs font-medium labelcolor">Forename</label>
                <input type="text" className="mt-1 placeholder:text-gray-400 placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter forename" value={forename} onChange={(e) => setForename(e.target.value)} />
                {errors.forename && <span className="text-red-500 text-xs">{errors.forename}</span>}
				</div>
				 <div className="mt-2">
                <label className="text-xs font-medium labelcolor">Surname</label>
                <input type="text" className="mt-1 placeholder:text-gray-400 placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter surname" value={surname} onChange={(e) => setSurname(e.target.value)} />
                {errors.surname && <span className="text-red-500 text-xs">{errors.surname}</span>}
				</div>
                <div className="mt-2">
					<fieldset>
					  <legend className="text-xs font-medium labelcolor">Gender</legend>
					  <div className="">
						  <ul className="donate-now mt-1">
								<li>
									<input type="radio" name="gender" value="Male" onChange={(e) => setGender(e.target.value)} />
									<label className="text-center font-semibold labelcolor">Male</label>
								</li>
								<li>
									<input type="radio" name="gender" value="Female" onChange={(e) => setGender(e.target.value)} />
									<label className="text-center font-semibold labelcolor">Female</label>
								</li>
								<li>
									<input type="radio" name="gender" value="Not Applicable" onChange={(e) => setGender(e.target.value)} />
									<label className="text-center font-semibold labelcolor">Not
										Applicable</label>
								</li>
							</ul>
					  </div>
					  {errors.gender && <span className="text-red-500 text-xs">{errors.gender}</span>}
					</fieldset>
                </div>
				<div className="mt-2">
					<label className="text-xs font-medium labelcolor">Country</label>
						<select
						  ref={selectRef}
						  value={selectedCountries}
						  onChange={(e) => setSelectedCountries(e.target.value)}
						  className="choices__input"
						  aria-label="Select a country"
						>
						  <option value="">Select a country</option>
						  {countries.map(({ value, label }) => (
							<option key={value} value={value}>
							  {label}
							</option>
						  ))}
						</select>
				</div>
              </div>
            )}

            {step === 2 && (
              <div>
			  <div className="mt-2">
                <label className="text-xs font-medium labelcolor">Role</label>
                <input type="text" className="mt-1 placeholder:text-gray-400 labelcolor bg-white placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter role" value={role} onChange={(e) => setRole(e.target.value)} />
                {errors.role && <span className="text-red-500 text-xs">{errors.role}</span>}
				</div>
				<div className="mt-2">
                <label className="mt-2 text-xs font-medium labelcolor">Date Joined</label>
                <input type="date" className="block w-full border p-2 rounded-md" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
                {errors.startDate && <span className="text-red-500 text-xs">{errors.startDate}</span>}
				</div>
				<div className="mt-2">
                <label className="mt-2 text-xs font-medium labelcolor">LinkedIn URL</label>
                <input type="text" className="block w-full border p-2 rounded-md" placeholder="Enter LinkedIn URL" value={linkedInUrl} onChange={(e) => setLinkedInUrl(e.target.value)} />
				</div>
				<div className="mt-2">
                <label className="mt-2 text-xs font-medium labelcolor">Function</label>
                <input type="text" className="block w-full border p-2 rounded-md" placeholder="Enter function" value={functionValue} onChange={(e) => setFunctionValue(e.target.value)} />
                {errors.functionValue && <span className="text-red-500 text-xs">{errors.functionValue}</span>}
				</div>
				<div className="mt-2">				
                <label className="mt-2 text-xs font-medium labelcolor">Division</label>
                <input type="text" className="block w-full border p-2 rounded-md" placeholder="Enter division" value={division} onChange={(e) => setDivision(e.target.value)} />
                {errors.division && <span className="text-red-500 text-xs">{errors.division}</span>}
				</div>	
				<div className="mt-2">	
                <label className="mt-2 text-xs font-medium labelcolor">Employee ID</label>
                <input type="text" className="block w-full border p-2 rounded-md" placeholder="Enter employee ID" value={empid} onChange={(e) => setEmpid(e.target.value)} />
                {errors.empid && <span className="text-red-500 text-xs">{errors.empid}</span>}
				</div>
              </div>
            )}

            {step === 3 && (
              <div >
                <label className="text-xs font-medium labelcolor">Specialised</label>
                <input
                  type="text"
                  className="block w-full border p-2 rounded-md"
                  placeholder="Enter specialised skills"
                  value={newQualifications}
                  onChange={(e) => setNewQualifications(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleAddSpecialised()}
				  onBlur={handleAddSpecialised}
                />
                <div className="mt-2 flex flex-wrap">
                  {newSkillData.specialised.map((skill, index) => (
                    <span key={index} className="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                      {skill}
                      <button type="button" className="ml-1" onClick={() => {
                        const newSpecialised = newSkillData.specialised.filter((_, i) => i !== index);
                        setNewSkillData({ ...newSkillData, specialised: newSpecialised });
                      }}>&times;</button>
                    </span>
                  ))}
                </div>

                <label className="mt-2 text-xs font-medium labelcolor">Skills</label>
                <input
                  type="text"
                  className="block w-full border p-2 rounded-md"
                  placeholder="Enter common skills"
                  value={newSkills}
                  onChange={(e) => setNewSkills(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleAddCommonSkill()}
				  onBlur={handleAddCommonSkill}
                />
                <div className="mt-2 flex flex-wrap">
                  {newSkillData.common.map((skill, index) => (
                    <span key={index} className="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                      {skill}
                      <button type="button" className="ml-1" onClick={() => {
                        const newCommonSkills = newSkillData.common.filter((_, i) => i !== index);
                        setNewSkillData({ ...newSkillData, common: newCommonSkills });
                      }}>&times;</button>
                    </span>
                  ))}
                </div>

                <label className="mt-2 text-xs font-medium labelcolor">Certification</label>
                <input
                  type="text"
                  className="block w-full border p-2 rounded-md"
                  placeholder="Enter certifications"
                  value={newCertifications}
                  onChange={(e) => setNewCertifications(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleAddCertification()}
				  onBlur={handleAddCertification}
                />
                <div className="mt-2 flex flex-wrap">
                  {newSkillData.certification.map((cert, index) => (
                    <span key={index} className="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                      {cert}
                      <button type="button" className="ml-1" onClick={() => {
                        const newCertificationsList = newSkillData.certification.filter((_, i) => i !== index);
                        setNewSkillData({ ...newSkillData, certification: newCertificationsList });
                      }}>&times;</button>
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="w-full border-t mt-4 border-gray-200"></div>
			
             <div className="flex gap-2 w-full mt-4 ">
			 
			 {step === 1 && (
			 <>
			    <button
				  className="bg-white w-full text-black border p-2 rounded-md"
				  onClick={() => setIndividualPopup(false)}
				><span className="block font-medium">Cancel</span></button>
			  
			  
              <button
				  className=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md"
				  onClick={validateStep1}
				> <span className="block">Continue</span>
              <img
                className="h-5 w-5"
                src={`${assetBaseUrl}images/right-arrow.svg`}
                alt="Right Arrow"
              /></button>
			  
            </>
          )}

          {step === 2 && (
            <>
			 <button
				  className="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md"
				   onClick={goBack}
				>
              <img
                className="h-5 w-5"
                src={`${assetBaseUrl}images/BackArrow.svg`}
                alt="Back Arrow"
              />
              <span className="block font-medium">Back</span> </button>
			  
			  <button
				  className=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md"
				  onClick={validateStep2}
				>
              <span className="block">Continue</span>
              <img
                className="h-5 w-5"
                src={`${assetBaseUrl}images/right-arrow.svg`}
                alt="Right Arrow"
              />
			   </button>
            </>
          )}
       

        {/* Step 3 button */}
        {step === 3 && (
		<>
			<button
				  className="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md"
				   onClick={goBack}
				>
              <img
                className="h-5 w-5"
                src={`${assetBaseUrl}images/BackArrow.svg`}
                alt="Back Arrow"
              />
              <span className="block font-medium">Back</span> </button>
			  
		
          <button
            className=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md"
            onClick={handleStep3}
          >
            <img
              className="h-5 w-5"
              src={`${assetBaseUrl}images/plus-white-without-circle.svg`}
              alt="Add Candidate"
            />
            <span className="block">Add Candidate</span>
          </button>
		  </>
        )}
			</div>
			
			
          </div>
        </div>
      </div>
    )
  );
};

export default IndividualPopup;
