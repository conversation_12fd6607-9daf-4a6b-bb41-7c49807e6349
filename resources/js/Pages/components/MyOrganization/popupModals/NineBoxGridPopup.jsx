import React, { useState } from "react";

const NineBoxGridPopup = ({ nineboxGridPopup, setNineboxGridPopup, saveNineBoxGridCallback, gridLabelsdata }) => {
  // Filter the gridLabelsdata to get the labels
  const gridLabelsdatafilter = Array.isArray(gridLabelsdata) ? gridLabelsdata.map(item => (typeof item === 'object' ? item.label : item)) : [];



  // Initialize the gridLabels state
  const [gridLabels, setGridLabels] = useState(gridLabelsdatafilter.length > 0 ? gridLabelsdatafilter : ["", "", "", "", "", "", "", "", ""]);
  const [error, setError] = useState("");

  const handleLabelChange = (index, value) => {
    const updatedLabels = [...gridLabels];
    updatedLabels[index] = value;
    setGridLabels(updatedLabels);
  };

  const saveGridLabels = () => {
    const hasEmptyLabels = gridLabels.some(label => label.trim() === "");
    if (hasEmptyLabels) {
      setError("Please fill in all grid labels.");
      return;
    }
    setError("");
    saveNineBoxGridCallback(gridLabels);
  };

  return (
    nineboxGridPopup && (
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div className="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50">
          <div className="flex justify-between items-center p-4">
            <h2 className="text-sm font-bold chart-heading">Add 9 Box Grid</h2>
            <button
              type="button"
              onClick={() => setNineboxGridPopup(false)}
              className="text-gray-500 hover:text-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="w-full border-t border-gray-200"></div>

          <div className="p-4 overflow-y-auto" style={{ height: "100%", maxHeight: "400px" }}>
            <label className="chart-heading text-sm font-medium">Grid Labels</label>
            {error && <div className="text-red-500 text-sm mb-3">{error}</div>}
            <div className="grid grid-cols-3 gap-3 mt-2">
              {gridLabels.map((label, index) => (
                <div key={index} className="gridBorder px-3 py-4 rounded-lg">
                  <textarea
                    value={label}
                    onChange={(e) => handleLabelChange(index, e.target.value)}
                    className="custom-textarea w-full outline-none text-center break-words"
                    placeholder="Enter Label"
                    rows="2"
                  ></textarea>
                </div>
              ))}
            </div>
          </div>

          <div className="flex gap-2 w-full p-4 text-sm border-t">
            <button
              onClick={() => setNineboxGridPopup(false)}
              type="button"
              className="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md"
            >
              <span className="block font-medium">Cancel</span>
            </button>
            <button
              onClick={saveGridLabels}
              type="button"
              className="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md"
            >
              <span className="block font-semibold">Save Changes</span>
            </button>
          </div>
        </div>
      </div>
    )
  );
};

export default NineBoxGridPopup;
