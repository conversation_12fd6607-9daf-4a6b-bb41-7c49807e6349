import React, { useState, useEffect, useContext } from "react";
import { OrganizationContext } from "../../../OrganizationContext";
import { Inertia } from '@inertiajs/inertia';

const ProfileNineBoxGrid = ({candidate_id}) => {

  const { nineBoxGridArr, addedPeoplesInOrganisation, setAddedPeoplesInOrganisation, findUserinOrgchart, editingOrganisationId } = useContext(OrganizationContext);

  const [nineboxGridOpen, setNineboxGridOpen] = useState(false);
  const [isPopupVisible, setIsPopupVisible] = useState(true);
  const [nineBoxGridData, setNineBoxGridData] = useState(nineBoxGridArr);

  const user = findUserinOrgchart(addedPeoplesInOrganisation, candidate_id);
  const [profileUser, setProfileUser] = useState(user);  
  

  const updateResponse = (index) => {
    // Update response values and selectedOption
    if (user) {    
      user.nineBoxData = nineBoxGridData[index];
      setAddedPeoplesInOrganisation([...addedPeoplesInOrganisation]); 
    }
  };

  const handleSave = () => {
    if (editingOrganisationId) {
      Inertia.post('/save-profile-ninebox', {
          organisationId: editingOrganisationId,
          candidateId: candidate_id,
          nineBoxData: user.nineBoxData
      }, {
          onSuccess: () => {
              console.log('Data saved successfully');
          },
          onError: (error) => {
              console.error('Error saving data', error);
          }
      });
    }
    setNineboxGridOpen(false);
};

  const handleCancel = () => {
    setNineboxGridOpen(false);
  };

  return (
    <div className="h-96 border rounded-xl p-2">
      {!nineboxGridOpen ? (
        <div>
          <div className="flex justify-between items-center">
            <div className="text-lg font-semibold">9 Box Grid</div>
            <div
              onClick={() => setNineboxGridOpen(true)}
              className="flex cursor-pointer px-2 h-8 py-2 border border-gray-300 justify-center gap-x-1 items-center bg-white rounded-lg"
            >
              <img className="h-4 w-4 mr-2" src="/images/editicon.svg" alt="" />
              <h2 className="text-sm font-semibold">Update</h2>
            </div>
          </div>
          <div className="p-3 relative">
            <div className="flex gap-5 items-center">
              <img src="/images/yAxis.svg" alt="" />
              {isPopupVisible && nineBoxGridData.length > 0 && (
                <div className="relative grid grid-cols-3 gap-3 w-full">
                  {nineBoxGridData.map((nineBox) => (
                    <div
                      key={nineBox.criteria_id}
                      className={`text-white boxSize text-center rounded ${
                        profileUser?.nineBoxData?.criteria_id === nineBox.criteria_id
                          ? "bg-cyan-500"
                          : "orangeBG"
                      }`}
                    >
                      {nineBox.label}
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex flex-col items-center w-full mt-3 mx-3">
              <div className="relative w-full flex gap-1 items-center">
                <span className="text-xs text-gray-700">LOW</span>
                <img className="xaxiss" src="/images/xAxis.svg" alt="" />
                <span className="text-xs text-gray-700">HIGH</span>
              </div>
              <span className="text-gray-600 mt-2 text-xs">Performance</span>
            </div>
          </div>
        </div>
      ) : (
        <div>
          <div className="flex justify-between items-center">
            <div className="text-lg font-semibold">Update 9 Box Grid</div>
            <div className="flex gap-3">
              <button
                onClick={handleCancel}
                type="button"
                className="flex gap-2 justify-center items-center border p-2 rounded-md text-sm font-bold"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                type="button"
                className="text-white flex justify-center gap-5 items-center bg-mainBlue p-2 rounded-md"
              >
                <span className="block font-semibold text-sm">Save</span>
              </button>
            </div>
          </div>
          <div className="p-3 relative">
            <div className="flex gap-5 items-center">
              <img src="/images/yAxis.svg" alt="" />
              {isPopupVisible && nineBoxGridData.length > 0 && (
                <div className="relative grid grid-cols-3 gap-3 w-full">
                  {nineBoxGridData.map((nineBox, index) => (
                    <label
                      key={nineBox.criteria_id}
                      className={`relative text-black boxSize text-center rounded cursor-pointer ${
                        profileUser?.nineBoxData?.criteria_id === nineBox.criteria_id
                          ? "afterCheckBorder afterBG"
                          : "grayLightBorder"
                      }`}
                    >
                      <input
                        type="radio"
                        name="selection"
                        value={index}
                        className="absolute Blue-radio top-1 right-2"
                        checked={profileUser?.nineBoxData?.criteria_id === nineBox.criteria_id}
                        onChange={() => updateResponse(index)}
                        disabled={profileUser?.nineBoxData?.criteria_id === nineBox.criteria_id}
                      />
                      {nineBox.label}
                    </label>
                  ))}
                </div>
              )}
            </div>
            <img className="w-full mt-3" src="/images/xAxis.svg" alt="" />
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileNineBoxGrid;
