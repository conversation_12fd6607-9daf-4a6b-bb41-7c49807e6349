import React, { useState, useEffect, useContext } from "react";
import '../../../css/organisationChart.css';

import OrganizationChartListing from "./OrganizationChartListing";
import AddPeoplesToOrganisationPopup from "../popupModals/AddPeoplesToOrganisationPopup";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { OrganizationContext } from "../../../OrganizationContext";
import { ScrollableList } from "./ScrollableList";

const OrganizationChart = ({assetBaseUrl}) => {
  const { isVisible, editingOrganisationId, addedPeoplesInOrganisation, setAddedPeoplesInOrganisation, organisationName, setOrganisationName } = useContext(OrganizationContext);
  const [zoomLevel, setZoomLevel] = useState(100); // Default zoom level
  const [selectedPeoplesForOrganisation, setSelectedPeoplesForOrganisation] = useState([]);
  const [individualPopup, setIndividualPopup] = useState(false);
  const [selectedPeoples, setSelectedPeoples] = useState([]);
  const [users, setUsers] = useState([]);
  const [query, setQuery] = useState("");
  const [lastPage, setLastPage] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [peoplesExistsInOrganisation, setPeoplesExistsInOrganisation] = useState([]);
  const [OrgChartusers, SetOrgChartusers] = useState([]);


  // Fetch initial data (mocked here for simplicity)
  useEffect(() => {   
    fetchPaginatedItems();
  }, []);

  useEffect(() => {
    if (editingOrganisationId) {
      const fetchOrganisationDetails = async () => {
        try {
          const response = await fetch(`/organisation-details/${editingOrganisationId}`);
          const data = await response.json();
          setOrganisationName(data.name);
          setAddedPeoplesInOrganisation(data.peoples);
          setPeoplesExistsInOrganisation(data.peoples);
        } catch (error) {
          console.error("Error fetching organisation details:", error);
        }
      };

      fetchOrganisationDetails();
    }
  }, [editingOrganisationId]);

  // Zoom in and out
  const zoomIn = () => setZoomLevel((prevZoom) => Math.min(prevZoom + 10, 200));
  const zoomOut = () => setZoomLevel((prevZoom) => Math.max(prevZoom - 10, 30));

  // Fetch paginated data
  const fetchPaginatedItems = async (page = 1, searchQuery = "") => {
    try {
    setLoading(true);
    const response = await fetch(
        `/paginated-internal-peoples?page=${page}&query=${encodeURIComponent(searchQuery)}`
    );
    const data = await response.json();
    if (page === 1) {
        setUsers(data.data);
        setSelectedPeoples(data.data); // Initialize with first page items
    } else {
        setUsers((prev) => [...prev, ...data.data]);
        setSelectedPeoples((prev) => [...prev, ...data.data]); // Append next page items
    }
    setCurrentPage(data.current_page);
    setLastPage(data.last_page);
    } catch (error) {
      console.error("Error fetching paginated items:", error);
    } finally {
      setLoading(false);
    }
  };

  // Search functionality
  const search = (inputQry) => {
    //console.log("queryyy:", inputQry);
    setQuery(inputQry.toLowerCase());
    fetchPaginatedItems(1, inputQry.toLowerCase());
  };

  // Toggle user selection
  const toggleSelection = (user) => {
      const exists = selectedPeoplesForOrganisation.find(
      (selectedUser) => selectedUser.id === user.id
      );

      if (exists) {
      setSelectedPeoplesForOrganisation((prev) =>
          prev.filter((selectedUser) => selectedUser.id !== user.id)
      );
      } else {
      setSelectedPeoplesForOrganisation((prev) => [...prev, user]);
      }
  };

  const isSelected = (userId) => {
    // Check if the userId exists in the selectedPeoplesForOrganisation array
    return selectedPeoplesForOrganisation.some((selectedUser) => selectedUser.id === userId);
  };

  // Check if user is in organisation
  const isUserInOrganisation = (userId) => {
    return peoplesExistsInOrganisation.some(
    (existingUser) => existingUser.id === userId
    );
  };

  const findUserById = (users, candidate_id) => {
    for (const user of users) {
      // Check the direct id
      if (user.id === candidate_id) {
          return user;
      }
      // Check in descendants recursively
      if (user.descendants && user.descendants.length > 0) {
          const found = findUserById(user.descendants, candidate_id);
          if (found) {
              return found;
          }
      }
    }
    return null; // Return null if no user is found
  };


const handleDragEnd = (event) => {
  const { active, over } = event;

  if (!event.activatorEvent.isTrusted || !active) return;
//console.log(over);
//console.log(active);
//console.log(OrgChartusers);
 let draggedUser = selectedPeoplesForOrganisation.find((user) => user.id === active.id);
 // SetOrgChartusers([...OrgChartusers, draggedUser]); // Corrected state update
  if (!draggedUser) {
    draggedUser = OrgChartusers.find((user) => user.id === active.id);
  }

  if (!draggedUser) return;

  // Add to OrgChartusers only if not already present
  SetOrgChartusers((prev) => {
  const isUserAlreadyAdded = prev.some(user => user.id === draggedUser.id);
  return isUserAlreadyAdded ? prev : [...prev, draggedUser];
});
  
// let activeUser = findUserById(addedPeoplesInOrganisation, active.id); 
// if (activeUser) {   
  // setAddedPeoplesInOrganisation((prev) => prev.filter(user => user.id !== activeUser.id)); 
  // console.log("added people array", addedPeoplesInOrganisation);
// }
  
   // SetOrgChartusers((prev) => prev.filter((user) => user.id !== active.id));
  // setAddedPeoplesInOrganisation((prev) => prev.filter((user) => user.id !== active.id));

  if (!over) {
    setSelectedPeoplesForOrganisation((prev) => prev.filter((user) => user.id !== active.id));
    const updatedTree = updateOrganizationTree(
      addedPeoplesInOrganisation,
      null,
      createUserNode(draggedUser, [], false)
    );
    setAddedPeoplesInOrganisation(updatedTree);
    return;
  }

  const targetNode = findUserById(addedPeoplesInOrganisation, over.id);
  if (!targetNode) return;

  const dropPosition = detectDropPosition(event, over);
// alert(dropPosition);
if (dropPosition === "parent") {
    const newParentNode = createUserNode(draggedUser, [targetNode], false);
    const updatedTree = addedPeoplesInOrganisation.map((node) =>
      node.id === targetNode.id ? draggedUser : node
    );
    setAddedPeoplesInOrganisation(updatedTree);
  }else{
	   const updatedTree = updateOrganizationTree(
      addedPeoplesInOrganisation,
      over.id,
      createUserNode(draggedUser, [], false)
    );

    setAddedPeoplesInOrganisation(updatedTree);
   
  } 
    // Default to child
    // First, remove the user if it already exists, then add it
    setSelectedPeoplesForOrganisation((prev) => prev.filter((user) => user.id !== active.id));

};

const findParentNode = (tree, nodeId, parent = null) => {
  for (const node of tree) {
    if (node.id === nodeId) return parent;
    if (node.descendants?.length > 0) {
      const found = findParentNode(node.descendants, nodeId, node);
      if (found) return found;
    }
  }
  return null;
};
const detectDropPosition = (event, over) => {
  if (!over || !event.over || !event.over.rect) return "child"; // Default to child if rect is missing

  const targetRect = event.over.rect;

  // Compute cursor position using delta values
  const cursorX = targetRect.left + event.delta.x;
  const cursorY = targetRect.top + event.delta.y;

  //console.log("event.over:", event.over);
  //console.log("event:", event);
  //console.log("cursorX:", cursorX);
  //console.log("cursorY:", cursorY);
  //console.log("targetRect:", targetRect);

  const threshold = 0.3; // Adjust this for sensitivity (30% of the height/width)

  // Detect if dropped over top area (parent)
  if (cursorY < targetRect.top + targetRect.height * threshold) {
    //console.log("Drop position: parent");
    return "parent";
  }

  // Detect if dropped over bottom area (child)
  // if (cursorY > targetRect.top + targetRect.height * (1 - threshold)) {
    // console.log("Drop position: child");
    // return "child";
  // }

  // Detect if dropped over right area (sibling)
  // if (cursorX > targetRect.left + targetRect.width * (1 - threshold)) {
    // console.log("Drop position: sibling");
    // return "sibling";
  // }

  // Default to child
  // console.log("Drop position: child");
  return "child";
};

// Helper function to create a user node structure
  const createUserNode = (user, descendants = [], isUpperCircle) => ({
    id: user.id,
    internalPeople: {
        name: `${user.forename ?? ""} ${user.surname ?? ""}`,
        role: user.latest_role ?? "N/A",
        location: user.location ?? "Unknown",
        tenure: user.tenure ?? "N/A",
        flightRisk: user.flight_risk ?? false,
        is_relocatable: user.is_relocatable ?? false,
        plan: user.plan ?? null,
        isUpperCircle: isUpperCircle,
        internalUserPlans: user.internalUserPlans ?? [],
        summary: user.summary ?? "No summary available",
        company: user.company_name ?? "Unknown",
        skills: user.internal_skills ?? [],
        career_history: user.career_history ?? [],
        readiness: user.readiness === "1" ? "Ready Now" : "Ready Future",
        potential: user.potential ? `${user.potential}` : "Medium",
        performance: user.performance ?? "Medium",
        potentialColorClass:
            user.potential === "High" ? "node-potential-blue" :
            user.potential === "Medium" ? "node-potential-orange" : "node-potential-grey",
        readinessColorClass: user.readiness === "1" ? "node-readiness-green" : "node-readiness-red",
    },
    descendants: descendants,
  });
 
   
  // const updateOrganizationTree = (tree, parentId, newNode) => {
  //   if (!tree || tree.length === 0) {
  //     console.log("Empty tree. Adding new node as the root.");
  //     return [{ ...newNode }];
  //   }
  
  //   const addNode = (nodes) => {
  //     return nodes.map((node) => {
  //       if (node.id === parentId) {
  //         // console.log(node);
  //         return {
  //           ...node,
  //           descendants: [...(node.descendants || []), { ...newNode }],
  //         };
  //       }
  
  //       return {
  //         ...node,
  //         descendants: addNode(node.descendants || []),
  //       };
  //     });
  //   };
  
  //   return addNode(tree);
  // };
  
  const updateOrganizationTree = (tree, parentId, newNode) => {
    if (!tree || tree.length === 0) {
      //console.log("Empty tree. Adding new node as the root.");
      return [{ ...newNode }];
    }
  
    // Function to remove the user if it exists
    const removeNode = (nodes, userId) => {
      return nodes
        .map(node => ({
          ...node,
          descendants: removeNode(node.descendants || [], userId)
        }))
        .filter(node => node.id !== userId); // Remove the user from the tree
    };
  
    // First, remove the existing instance of the user
    const cleanedTree = removeNode(tree, newNode.id);
  
    // Now, add the user at the correct position
    const addNode = (nodes) => {
      return nodes.map((node) => {
        if (node.id === parentId) {
          return {
            ...node,
            descendants: [...(node.descendants || []), { ...newNode }],
          };
        }
  
        return {
          ...node,
          descendants: addNode(node.descendants || []),
        };
      });
    };
  
    return addNode(cleanedTree);
  };

  return (
    <form onSubmit={(e) => e.preventDefault()}>
      {isVisible && (
        <div className="bg-white p-2 flex px-3 w-full border organisationTitle">
          <div className="flex gap-2 halfwidth">
            <img
              onClick={zoomIn}
              className="search-icon p-1 rounded-lg cursor-pointer w-10 h-10 border-2 bg-white"
              src="/images/Plus.svg"
              alt="Zoom In"
            />
            <div className="text-sm text-gray-700 flex w-16 h-10 justify-center items-center p-1 rounded-lg border-2 bg-white">
              {zoomLevel}%
            </div>
            <img
              onClick={zoomOut}
              className="search-icon p-1 rounded-lg cursor-pointer border-2 w-10 h-10 bg-white"
              src="/images/Minus.svg"
              alt="Zoom Out"
            />
          </div>
          <div className="flex justify-between items-center w-full">
            <input
              value={organisationName}
              onChange={(e) => setOrganisationName(e.target.value)}
              type="text"
              className=" outline-none block py-1.5 OrganizationInput px-3 HRInfo text-gray-900 shadow-sm text-center rounded-lg placeholder:text-gray-500 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6"
              placeholder="Organization Name"
            />
          </div>
        </div>
      )}

      <div className={`w-full relative ${isVisible ? "contentHeight" : ""}`}>
        <div className="flex">
          <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
            {isVisible && (
              <div className="scrollable-section bg-white w-72 p-2">
                <ScrollableList users={selectedPeoplesForOrganisation} setIndividualPopup={setIndividualPopup} />
              </div>
            )}
            
            {addedPeoplesInOrganisation && addedPeoplesInOrganisation.length > 0 && (
              <div className="my-org border w-full relative outermostdiv flex flex-col items-center treeOuter z-10 overflow-x-auto">
                <OrganizationChartListing 
                  addedPeoplesInOrganisation={addedPeoplesInOrganisation}
                  zoomLevel={zoomLevel} assetBaseUrl={assetBaseUrl}
                /> 
              </div> 
            )}
          </DndContext>

          {individualPopup && (
            <AddPeoplesToOrganisationPopup
              showPopup={true}
              onClose={() => setIndividualPopup(false)}
              selectedPeoples={selectedPeoples}
              setSelectedPeoples={setSelectedPeoples}
              onSearch={search}
              toggleSelection={toggleSelection}
              isSelected={isSelected}
              isUserInOrganisation={isUserInOrganisation}
              users={users}
              query={query}
              setQuery={setQuery}
            />
          )}
        </div>
      </div>
    </form>
  );
};

export default OrganizationChart;
