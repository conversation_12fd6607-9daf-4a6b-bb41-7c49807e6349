import React, { memo, useState } from "react";
import { useDraggable,useDroppable, DragOverlay } from "@dnd-kit/core";
import { FaPlane, FaBriefcase, FaArrowUp, FaChevronDown, FaEllipsisV } from "react-icons/fa";
import CandidateProfilePopup from "../popupModals/CandidateProfilePopup";
import CandidateDetailsModal from "../../CandidateDetailsModal";
import CreatePlanPopup from "../popupModals/CreatePlanPopup";

const OrganizationChartListing = memo(({ addedPeoplesInOrganisation, zoomLevel, assetBaseUrl, activeOrgUser,deleteNode,setAddedPeoplesInOrganisation }) => {  
  return ( 
    <div className="my-org-tree flex justify-between treeWidth w-full org-treeHeight">
      {/* Root Node */}
      <div className="tree" style={{ transform: `scale(${zoomLevel / 100})` }}>  
        <ul>            
          {addedPeoplesInOrganisation.map((node) => (
            <TreeNode key={node.id} node={node} assetBaseUrl={assetBaseUrl} activeOrgUser={activeOrgUser}  deleteNode={deleteNode} setAddedPeoplesInOrganisation={setAddedPeoplesInOrganisation}  />
          ))}
        </ul>
        {/* {activeOrgUser && (
          <DragOverlay>
            <NodeCard node={activeOrgUser.internalPeople} node_id={activeOrgUser.id} assetBaseUrl={assetBaseUrl} isOverlay />
          </DragOverlay>
        )} */}
      </div>
    </div>
  );
});

const TreeNode = ({ node, assetBaseUrl, activeOrgUser,setAddedPeoplesInOrganisation,deleteNode }) => {
  return (
    <li>
      <a href="#">
        <NodeCard key={node.id} node={node.internalPeople} deleteNode={deleteNode} node_id={node.id} assetBaseUrl={assetBaseUrl} />
      </a>
      {node.descendants.length > 0 && <OrganizationChartListing addedPeoplesInOrganisation={node.descendants} deleteNode={deleteNode} setAddedPeoplesInOrganisation={setAddedPeoplesInOrganisation}/>}
    </li>
  );
};

const NodeCard = ({ node, node_id, assetBaseUrl, isOverlay = false,deleteNode }) => {
  const [showModal, setShowModal] = useState(false);
  const closeModal = () => setShowModal(false);

  const [showPlanModal, setPlanModal] = useState(false);
  const closePlanModal = () => setPlanModal(false);

  // Make NodeCard a Droppable Zone
  const { setNodeRef, isOver } = useDroppable({
    id: node_id,
  });

  // Make NodeCard a Draggable Node
  const { attributes, listeners, setNodeRef: setDragRef, transform, isDragging } = useDraggable({
    id: node_id,
  });

  // Merge refs to make the component both Draggable & Droppable
  const combinedRef = (node) => {
    setNodeRef(node);  // Droppable ref
    setDragRef(node);  // Draggable ref
  };

  const style = {
    transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
    opacity: isDragging ? 0.5 : 1,
    border: isOver ? "1px dashed green" : "none", // Highlight when hovered over
    height: 240,
  };
  
  return (
    <div
    ref={combinedRef}
    style={style}
      className={`bg-white border py-2 border-gray-300 rounded-lg shadow-md text-center w-56 relative ${
        isOverlay ? "shadow-lg scale-110 bg-white" : ""
      }`}
    >
      {node && (
        <>
          <div className="flex gap-10 items-center">
          <div className="flex gap-2 items-center ml-3">
            <div>{node.name.slice(0, 2).toUpperCase()}</div>
            <h2 className="font-bold text-sm overflow-hidden whitespace-nowrap w-32 text-ellipsis" title="Name">
                {node.name}</h2>
          </div>
          {/* Dropdown Button */}
          <div className="dropdown-container relative">
            <button
              className="text-gray-400 hover:text-gray-600 focus:outline-none"
              onClick={(e) => {
                e.stopPropagation(); // Prevent click from bubbling up
                const dropdown = e.currentTarget.nextSibling;
                dropdown.classList.toggle("hidden");
              }}
            >
              <img className="h-5 w-5" src="/images/DotsThreeVertival.svg"/>
            </button>

          {/* Dropdown Menu */}
          <div className="dropdown-menu hidden z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44">
              <ul className="py-2 text-sm text-gray-700">
                  <li className="cursor-pointer">
                    <div className="flex gap-5 py-2 px-5 hover:bg-gray-100" onClick={() => setShowModal(true)}>
                        View Profile
                    </div>
                    {node.internalUserPlans && node.internalUserPlans.length > 0 ? (
                      <div
                      onClick={() => (window.location.href = `/plan/${encodeURIComponent(node.internalUserPlans[0].id)}`)}
                      className="flex gap-5 py-2 px-5 hover:bg-gray-100"
                      >
                      View Plan
                      </div>
                    ) : (
                      <div
                      onClick={() => (window.location.href = `/plan/`)}
                      className="flex gap-5 py-2 px-5 hover:bg-gray-100"
                      >
                      Create Plan
                      </div>
                    )}                      
                    <div className="flex gap-5 py-2 px-5 hover:bg-gray-100"  onClick={() => deleteNode(node_id)}>
                        Delete
                    </div>
                  </li>
              </ul>
          </div>
        </div>
      </div>
      <div className="draggable-zone" {...attributes} {...listeners}>
        <div className="flex gap-10 items-center">
          <div className="flex gap-2 items-center ml-4">
            <div className="block w-7 h-7"></div>
            <div className="text-sm text-left whitespace-nowrap w-32 text-ellipsis truncate">
              <p>{node.location}</p>
              <p>{node.tenure} tenure</p>
              <p className="overflow-hidden whitespace-nowrap text-ellipsis">{node.role}</p>
            </div>
          </div>
        </div>

          {/* Icons Section */}
          <div className="flex justify-center gap-3 my-3 text-base py-2">
            {node.flightRisk === 1 && (<img src="/images/AirplaneTilt.png" title="Flight Risk" />)}
            {node.plan !== null && (
              <img src="/images/CheckCircle.png" title="Plan Available" />
            )}
            {node.isUpperCircle === true && (<img src="/images/ArrowCircleUp.png" title="Upper Circle" /> )}
            {node.is_relocatable === 1 && (<img src="/images/Briefcase.png" title="Briefcase" />)}
            {node.internalUserPlans && node.internalUserPlans.length > 0 &&
            (<img src="/images/UsersThree.png" title="Users" /> )}
          </div>

          {/* Status Section */}
          <div className="text-sm">
            <p
              className={`py-1 px-2 rounded-lg font-medium ${node.readinessColorClass}`}
            >
              {node.readiness}
            </p>
            <p
              className={`mt-2 py-1 px-2 rounded-lg font-medium ${node.potentialColorClass}`}
            >
              {node.potential} Potential
            </p>
          </div>
        </div>
      </>
    )}
    <img className="absolute dragIcon left-1/2" src="/images/dragcircle.svg" alt="Drag Icon"></img>
    <CandidateProfilePopup
    showModal={showModal}
    closeModal={closeModal}
    candidateData={node}
    candidate_id={node_id}
    assetBaseUrl={assetBaseUrl}
    />
		
		<CreatePlanPopup
		showPlanModal={showPlanModal}
		closeModal={closePlanModal}
		candidateData={node}
		candidate_id={node_id}
		/>
    </div>
  );
};

export default OrganizationChartListing;