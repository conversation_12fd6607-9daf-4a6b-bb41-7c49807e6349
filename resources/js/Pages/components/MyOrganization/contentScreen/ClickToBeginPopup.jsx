import React, { useContext } from 'react';
import OrganizationChart from './OrganizationChart';
import { OrganizationContext } from '../../../OrganizationContext';

const ClickToBeginPopup = ({assetBaseUrl}) => {

  const {
          isVisible,
          setIsVisible,
          isFirstClick,
          setIsFirstClick,
          clickToBeginPopup,
      } = useContext(OrganizationContext);
  

  const handleClick = () => {  
    setIsVisible(!isVisible);
    setIsFirstClick(false);
  };

  return (
    <div>
    {isFirstClick && (
      <div className={`flex justify-center items-center mt-2 ${clickToBeginPopup ? '' : 'hidden'}`}>
        <div className="border bg-white flex justify-center flex-col items-center Organisation border-gray-200 rounded-xl">
          <div>
            <img src="/images/Organization.png" alt="Organization" />
          </div>
          <div className="space-y-2">
            <h2 className="text-black font-semibold text-center">Welcome to My Organisation</h2>
            <p className="text-sm text-gray-500">
              This is a place for you to build organisation charts, create and manage succession plans and view
              any updates we may have.
            </p>
            <div className="flex justify-center items-center">
              <button
                id="clickToBegin"
                onClick={handleClick}
                className="text-white text-xs bg-mainBlue p-3 rounded-lg font-semibold"
              >
                Click to begin
              </button>
            </div>
          </div>
        </div>
      </div>
    )}
    </div>
  );
};

export default ClickToBeginPopup;