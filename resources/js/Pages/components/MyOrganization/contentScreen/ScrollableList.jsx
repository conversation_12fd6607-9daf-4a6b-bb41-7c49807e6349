import { useDraggable, DragOverlay } from "@dnd-kit/core";
import React, { memo, useState, useEffect } from "react";
import { FixedSizeList as List } from 'react-window';
export const ScrollableList = memo(({ users, setIndividualPopup, activeListUser }) => {
  return (
      <>
      <div className="flex justify-between items-center">
        <h2 className="text-black text-center text-base font-semibold">Selected Individuals</h2>
        <img
            className="search-icon p-1 rounded-lg border-2 bg-white"
            src="/images/Plus.svg"
            alt="Add Individuals"
            onClick={() => setIndividualPopup(true)}
        />
      </div>
      <p className="text-sm text-gray-500 mt-2">Click to add individuals on your org chart</p>
      <div className="mt-4 scrollable-list">
        {users.length > 0 ? (
          <List
            height={400}
            width="100%"
            itemCount={users.length}
            itemSize={60}
            itemData={users}
          >
            {({ index, style, data }) => (
              <div style={style}>
                <DraggableUser key={data[index].id} user={data[index]} />
              </div>
            )}
          </List>
        ) : (
          <p className="text-sm text-gray-400 text-center py-4">No individuals selected</p>
        )}
      </div>
      <DragOverlay dropAnimation={{ duration: 200 }}>
        {activeListUser ? <DraggableUser user={activeListUser} isOverlay /> : null}
      </DragOverlay>
      </>
  );
});

const DraggableUser = ({ user, isOverlay = false }) => {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({ id: user.id });

  // Only apply transform if it's not the overlay
  const style = !isOverlay && transform
  ? { transform: `translate3d(${transform.x}px, ${transform.y}px, 0)` }
  : {};
  
    return (
      <li
        ref={setNodeRef}
        {...listeners}
        {...attributes}
        style={style}
        className={`flex border-2 items-center justify-evenly py-2 draggable-user mb-2 ${
          isOverlay ? "shadow-lg scale-110 bg-white" : "bg-white"
        }`}
      >
        <div className="border rounded-full w-10 h-10 text-white flex justify-center items-center bg-cyan-500">
          {`${user.forename.charAt(0).toUpperCase()}${user.surname.charAt(0).toUpperCase()}`}
        </div>
        <div>
          <span className="text-sm font-semibold block max-w-20 text-ellipsis overflow-hidden whitespace-nowrap">
            {`${user.forename} ${user.surname}`}
          </span>
          <span className="text-xs text-gray-500 block max-w-20 text-ellipsis overflow-hidden whitespace-nowrap">
            {user.latest_role ?? ""} {user.employee_id ? `(${user.employee_id})` : ""}
          </span>
        </div>
      </li>
    );
};
