import React, { useState, useEffect, useContext, useCallback, useMemo } from "react";
import '../../../css/organisationChart.css';

import OrganizationChartListing from "./OrganizationChartListing";
import AddPeoplesToOrganisationPopup from "../popupModals/AddPeoplesToOrganisationPopup";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { OrganizationContext } from "../../../OrganizationContext";
import { ScrollableList } from "./ScrollableList";
import { SkeletonOrgChart, SkeletonProfile } from "../../SkeletonLoader/SkeletonLoader";

const OrganizationChart = ({assetBaseUrl}) => {
  const context = useContext(OrganizationContext);
  
  // Defensive check for context
  if (!context) {
    console.error('OrganizationChart must be used within an OrganizationContext Provider');
    return <div>Loading organization chart...</div>;
  }

  const { isVisible, editingOrganisationId, addedPeoplesInOrganisation,
     setAddedPeoplesInOrganisation, organisationName, setOrganisationName, findUserinOrgchart, findParentId, isLoading } = context;
  const [zoomLevel, setZoomLevel] = useState(90); // Default zoom level
  const [selectedPeoplesForOrganisation, setSelectedPeoplesForOrganisation] = useState([]);
  const [individualPopup, setIndividualPopup] = useState(false);
  const [selectedPeoples, setSelectedPeoples] = useState([]);
  const [users, setUsers] = useState([]);
  const [query, setQuery] = useState("");
  const [lastPage, setLastPage] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [peoplesExistsInOrganisation, setPeoplesExistsInOrganisation] = useState([]);
  const [activeListUser, setActiveListUser] = useState(null);
  const [activeOrgUser, setActiveOrgUser] = useState(null);
  const [OrgChartusers, SetOrgChartusers] = useState([]);
  const [isLoadingPeople, setIsLoadingPeople] = useState(false);

  // Zoom in and out
  const zoomIn = () => setZoomLevel((prevZoom) => Math.min(prevZoom + 10, 200));
  const zoomOut = () => setZoomLevel((prevZoom) => Math.max(prevZoom - 10, 30));

  // Fetch paginated data - memoized with useCallback
  const fetchPaginatedItems = useCallback(async (page = 1, searchQuery = "") => {
    try {
      setIsLoadingPeople(true);
      const response = await fetch(
        `/paginated-internal-peoples?page=${page}&query=${encodeURIComponent(searchQuery)}`
      );
      const data = await response.json();
      if (page === 1) {
        setUsers(data.data);
        setSelectedPeoples(data.data); // Initialize with first page items
      } else {
        setUsers((prev) => [...prev, ...data.data]);
        setSelectedPeoples((prev) => [...prev, ...data.data]); // Append next page items
      }
      setCurrentPage(data.current_page);
      setLastPage(data.last_page);
    } catch (error) {
      console.error("Error fetching paginated items:", error);
    } finally {
      setIsLoadingPeople(false);
    }
  }, []);  // Empty dependency array as this doesn't depend on any props or state that would change

  // Fetch initial data when component mounts
  useEffect(() => {   
    fetchPaginatedItems();
  }, [fetchPaginatedItems]);

  // Search functionality - memoized to prevent recreating on each render
  const search = useCallback((inputQry) => {
    setQuery(inputQry.toLowerCase());
    fetchPaginatedItems(1, inputQry.toLowerCase());
  }, [fetchPaginatedItems]);

  // Toggle user selection - memoized to prevent unnecessary re-renders
  const toggleSelection = useCallback((user) => {
    const exists = selectedPeoplesForOrganisation.find(
      (selectedUser) => selectedUser.id === user.id
    );

    if (exists) {
      setSelectedPeoplesForOrganisation((prev) =>
        prev.filter((selectedUser) => selectedUser.id !== user.id)
      );
    } else {
      setSelectedPeoplesForOrganisation((prev) => [...prev, user]);
    }
  }, [selectedPeoplesForOrganisation]);

  const isSelected = useCallback((userId) => {
    // Check if the userId exists in the selectedPeoplesForOrganisation array
    return selectedPeoplesForOrganisation.some((selectedUser) => selectedUser.id === userId);
  }, [selectedPeoplesForOrganisation]);

  const handleDragEnd = useCallback((event) => {
    const { active, over } = event;
  
    if (!event.activatorEvent.isTrusted || !active) return;

    const delta = {
      x: event.delta.x,
      y: event.delta.y
    };
    const moveDistance = Math.sqrt(Math.pow(delta.x, 2) + Math.pow(delta.y, 2));
 
    if (moveDistance < 5) return; 
  
    let draggedUser = selectedPeoplesForOrganisation.find((user) => user.id === active.id);
   
    if (!draggedUser) {
      draggedUser = OrgChartusers.find((user) => user.id === active.id);
      if(!draggedUser){
        draggedUser = findUserinOrgchart(addedPeoplesInOrganisation, active.id);
        draggedUser = convertNodeToDraggedUser(draggedUser);
      }
    }
  
    if (!draggedUser) return;
  
    // Add to OrgChartusers only if not already present
    SetOrgChartusers((prev) => {
      const isUserAlreadyAdded = prev.some(user => user.id === draggedUser.id);
      return isUserAlreadyAdded ? prev : [...prev, draggedUser];
    });
  
    if (!over) {
      setSelectedPeoplesForOrganisation((prev) => prev.filter((user) => user.id !== active.id));
      const updatedTree = updateOrganizationTree(
        addedPeoplesInOrganisation,
        null,
        createUserNode(draggedUser, [], false, true)
      );
      setAddedPeoplesInOrganisation(updatedTree);
      setActiveListUser(null);
      return;
    }
  
    const targetNode = findUserinOrgchart(addedPeoplesInOrganisation, over.id);
    if (!targetNode) return;

    const findActiveUser = findUserinOrgchart(addedPeoplesInOrganisation, active.id);
    const findParent = findParentId(addedPeoplesInOrganisation, active.id);
    if(findActiveUser !== null){
      if(findParent == null){
        //alert("Root node can not be dragged!!!");
        return;
      }
    }
    

    const dropPosition = detectDropPosition(event, over);
    // alert(dropPosition);
    let updatedTree;
    switch (dropPosition) {
      case "parent":

        const newParentNode = createUserNode(draggedUser, [targetNode], false, true);
        updatedTree = makeParentNodeInTree(
          addedPeoplesInOrganisation,
          targetNode.id,
          newParentNode
        );
        break;
      case "child":
        const childTemp = findUserinOrgchart(addedPeoplesInOrganisation, active.id);
        if(childTemp !== null){
          updatedTree = updateOrganizationTree(
            addedPeoplesInOrganisation,
            over.id,
            childTemp
          );
        }else{
          updatedTree = updateOrganizationTree(
            addedPeoplesInOrganisation,
            over.id,
            createUserNode(draggedUser, [], false, true)
          );
        }
        break;
      case "sibling":
        const parentId = findParentId(addedPeoplesInOrganisation, targetNode.id);

        if (parentId === null) {
          alert("Root node can not have siblings!!!");
          return;
        }
        const siblingTemp = findUserinOrgchart(addedPeoplesInOrganisation, active.id);
        if(siblingTemp !== null){
          updatedTree = updateOrganizationTree(
            addedPeoplesInOrganisation,
            parentId,
            siblingTemp
          );
        }else{
          updatedTree = updateOrganizationTree(
            addedPeoplesInOrganisation,
            parentId,
            createUserNode(draggedUser, [], false, true)
          );
        }
        break;
      case "swap":

        //commented code for swapping nodes with each other on only org chart
        //const tempDragged = findUserinOrgchart(addedPeoplesInOrganisation, active.id);
        // if(tempDragged !== null){
        //   updatedTree = swapNodesWithEachOther(addedPeoplesInOrganisation, active.id, over.id);
        // }else{
          // Add swapped-out node back to draggable list
          
          if (targetNode.internalPeople !== null) {
            const swappedUser = convertNodeToDraggedUser(targetNode);
            setSelectedPeoplesForOrganisation(prev => [
              ...prev,
              swappedUser // Insert temp array
            ]);
          }
          const newNode = createUserNode(draggedUser, targetNode.descendants || [], false, true);
          // Replace target node with the transformed dragged user
          updatedTree = swapNodeInOrgTree(addedPeoplesInOrganisation, over.id, newNode); 
        //}
        break;
      default:
        updatedTree = updateOrganizationTree(
          addedPeoplesInOrganisation,
          over.id,
          createUserNode(draggedUser, [], false, true)
        );
        break;
    }
    setAddedPeoplesInOrganisation(updatedTree);
  
    setSelectedPeoplesForOrganisation((prev) => prev.filter((user) => user.id !== active.id));
    setActiveListUser(null);
    setActiveOrgUser(null);
  }, [addedPeoplesInOrganisation, selectedPeoplesForOrganisation, OrgChartusers, findUserinOrgchart, findParentId]);

  const detectDropPosition = (event, over) => {
    if (!over || !event.over || !event.over.rect) return "child"; // Default to child if rect is missing
  
    const targetRect = event.over.rect; // Target node's rectangle
    const activeRect = event.active.rect.current?.translated; // Dragged node's rectangle (if available)
  
    if (!activeRect) return "child"; // Prevent errors when rect is not available
  
    const cursorX = activeRect.left + activeRect.width / 2; // Center of dragged node
    const cursorY = activeRect.top + activeRect.height / 2; // Center of dragged node

  
    const threshold = 0.3; // Sensitivity (30% of the target's dimensions)
  
    // Parent: If dropped in the top 30% area
    if (cursorY < targetRect.top + targetRect.height * threshold) {
      return "parent";
    }
  
    // Child: If dropped in the bottom 30% area
    if (cursorY > targetRect.top + targetRect.height * (1 - threshold)) {
      return "child";
    }
  
    // Sibling: If dropped in the right 30% area
    if (cursorX > targetRect.left + targetRect.width * (1 - threshold)) {
      return "sibling";
    }

    // Swap: If dropped near the center (between 30% - 70%)
    const isSwapAreaX = cursorX > targetRect.left + targetRect.width * threshold &&
    cursorX < targetRect.left + targetRect.width * (1 - threshold);
    const isSwapAreaY = cursorY > targetRect.top + targetRect.height * threshold &&
    cursorY < targetRect.top + targetRect.height * (1 - threshold);

    if (isSwapAreaX && isSwapAreaY) {
      return "swap";
    }
  
    // Default to child if no other conditions match
    return "child";
  };
  
  

  // Helper function to create a user node structure
  const createUserNode = (user, descendants = [], isUpperCircle, is_internalPeople) => ({
    id: user.id,
    internalPeople: is_internalPeople ? {
      name: `${user.forename ?? ""} ${user.surname ?? ""}`,
      role: user.latest_role ?? "N/A",
      location: user.location ?? "Unknown",
      tenure: user.tenure ?? "N/A",
      flightRisk: user.flight_risk ?? false,
      is_relocatable: user.is_relocatable ?? false,
      plan: user.plan ?? null,
      isUpperCircle: isUpperCircle,
      internalUserPlans: user.internalUserPlans ?? [],
      summary: user.summary ?? "No summary available",
      function: user.function ?? "",
      division: user.division ?? "",
      country_manager: user.country_manager ?? "",
      corporate_level: user.corporate_level ?? "",
      key_strengths: user.key_strengths ?? "",
      development_areas: user.development_areas ?? "",
      linkedinURL: user.linkedinURL ?? "",
      other_tags: user.other_tags ?? "",
      company: user.company_name ?? "Unknown",
      skills: user.internal_skills ?? [],
      career_history: user.career_history ?? [],
      readiness: user.readiness === "1" ? "Ready Now" : user.readiness === "2" ? "Nearly Ready" : user.readiness === "3" ? "Not Ready" : "Future Ready",
      potential: user.potential ? `${user.potential}` : "Medium",
      performance: user.performance ?? "Medium",
      potentialColorClass:
          user.potential === "High" ? "node-potential-blue" :
          user.potential === "Medium" ? "node-potential-orange" : "node-potential-grey",
          readinessColorClass: user.readiness === "1" ? "node-readiness-green" : user.readiness === "2" ? "bg-[#FFF6ED] text-[#FFA347]" : user.readiness === "3" ? "node-potential-grey" : "node-readiness-red",
    }:null,
    descendants: descendants,
  });
 
   
  const updateOrganizationTree = (tree, parentId, newNode) => {
    if (!tree || tree.length === 0) {
      return [{ ...newNode }];
    }
  
    // Function to remove the user if it exists
    const removeNode = (nodes, userId) => {
      return nodes
        .map(node => ({
          ...node,
          descendants: removeNode(node.descendants || [], userId)
        }))
        .filter(node => node.id !== userId); // Remove the user from the tree
    };
  
    // First, remove the existing instance of the user
    const cleanedTree = removeNode(tree, newNode.id);
  
    // Now, add the user at the correct position
    const addNode = (nodes) => {
      return nodes.map((node) => {
        if (node.id === parentId) {
          return {
            ...node,
            descendants: [...(node.descendants || []), { ...newNode }],
          };
        }
  
        return {
          ...node,
          descendants: addNode(node.descendants || []),
        };
      });
    };
  
    return addNode(cleanedTree);
  };

  // Helper function to make a node as parent in the tree
const makeParentNodeInTree = (tree, childId, parentNode) => {

  // Function to remove a node if it exists in the tree
  const removeNode = (nodes, userId) => {
      return nodes
          .map(node => ({
              ...node,
              descendants: removeNode(node.descendants || [], userId)
          }))
          .filter(node => node.id !== userId); // Remove the user from the tree
  };

  // First, remove the parent node from the tree if it already exists
  let cleanedTree = removeNode(tree, parentNode.id);

  // Function to add the parent node above the target child
  const addParentNode = (nodes) => {
      return nodes.map(node => {
          if (node.id === childId) {
              return {
                  ...parentNode,
                  descendants: [node], // Make the existing child a descendant of the new parent
              };
          }

          if (node.descendants && node.descendants.length > 0) {
              return {
                  ...node,
                  descendants: addParentNode(node.descendants),
              };
          }

          return node;
      });
  };

  return addParentNode(cleanedTree);
};

const swapNodeInOrgTree = (users, targetId, newNode) => {
  return users.map(user => {
      if (user.id === targetId) {
          return newNode; // Replace the matched node with newNode
      }
      if (Array.isArray(user.descendants) && user.descendants.length > 0) {
          return {
              ...user,
              descendants: swapNodeInOrgTree(user.descendants, targetId, newNode) // Recursively update descendants
          };
      }
      return user; // Return unchanged node
  });
};

// Helper function to convert organization node back to user format
const convertNodeToDraggedUser = (node) => ({
  id: node.id,
  employee_id: node.internalPeople.employee_id || null, // Default null if missing
  forename: node.internalPeople.name.split(' ')[0] || '',
  surname: node.internalPeople.name.split(' ')[1] || '',
  middle_name: node.internalPeople.middle_name || null,
  other_name: node.internalPeople.other_name || null,
  gender: node.internalPeople.gender || null,
  role_id: node.internalPeople.role_id || null,
  diverse: node.internalPeople.diverse || null,
  location: node.internalPeople.location || null,
  linkedinURL: node.internalPeople.linkedinURL || null,
  reports_to: node.internalPeople.reports_to || null,
  exco: node.internalPeople.exco || null,
  company_id: node.internalPeople.company_id || null,
  company_name: node.internalPeople.company || null,
  start_date: node.internalPeople.start_date || null,
  end_date: node.internalPeople.end_date || null,
  tenure: node.internalPeople.tenure || null,
  tenure_in_company: node.internalPeople.tenure_in_company || null,
  function: node.internalPeople.function || null,
  division: node.internalPeople.division || null,
  seniority: node.internalPeople.seniority || null,
  career_history: node.internalPeople.career_history || [],
  educational_history: node.internalPeople.educational_history || null,
  skills: node.internalPeople.skills || [],
  languages: node.internalPeople.languages || null,
  other_tags: node.internalPeople.other_tags || null,
  readiness: node.internalPeople.readiness || null,
  user_id: node.internalPeople.user_id || null,
  flight_risk: node.internalPeople.flightRisk || null,
  is_relocatable: node.internalPeople.is_relocatable || null,
  potential: node.internalPeople.potential || null,
  performance: node.internalPeople.performance || null,
  corporate_level: node.internalPeople.corporate_level || null,
  created_at: node.internalPeople.created_at || null,
  updated_at: node.internalPeople.updated_at || null,
  summary: node.internalPeople.summary || null,
  latest_role: node.internalPeople.role || null,
  country: node.internalPeople.country || null,
  internalUserPlans: node.internalPeople.internalUserPlans || [],
  internal_skills: node.internalPeople.internal_skills || [],
  notes: node.internalPeople.notes || [],
  plan: node.internalPeople.plan || null
});


function swapNodesWithEachOther(organizationData, draggedId, targetId) { //this function not in use for now
  let draggedNode = null;
  let targetNode = null;

  // Helper function to recursively find nodes
  function findNodes(nodes) {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].id === draggedId) {
        draggedNode = nodes[i];
      } else if (nodes[i].id === targetId) {
        targetNode = nodes[i];
      }

      if (draggedNode && targetNode) return;

      if (nodes[i].descendants && nodes[i].descendants.length > 0) {
        findNodes(nodes[i].descendants);
      }
    }
  }

  // Find the nodes
  findNodes(organizationData);

  if (!draggedNode || !targetNode) {
    console.error("Error: One or both nodes not found.");
    return organizationData; // Return original data if swap isn't possible
  }
  
  // Helper function to swap nodes in the correct location
  function swapInTree(nodes) {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].id === draggedId) {
        nodes[i] = targetNode;
      } else if (nodes[i].id === targetId) {
        nodes[i] = draggedNode;
      }

      if (nodes[i].descendants && nodes[i].descendants.length > 0) {
        swapInTree(nodes[i].descendants);
      }
    }
  }
  // Perform the swap
  swapInTree(organizationData);
  //console.log("swapped function updateTree: ",organizationData);
  return organizationData; //here we need to return 'nodesssss actual' this console data which has actual swapped array
}


const deleteNode = (nodeId) => {
  const updatedData = deleteOrgNode(nodeId, addedPeoplesInOrganisation);
  setAddedPeoplesInOrganisation(updatedData);
};


const deleteOrgNode = (nodeId, nodes) => {
  return nodes
      .map(node => {
          if (node.id === nodeId) {
              if (node.descendants.length > 0) {
                  // If the node has children, only remove its internalPeople data
                  return { ...node, internalPeople: null };
              } else {
                  // If the node has no children, return null to indicate removal
                  return null;
              }
          } else if (node.descendants.length > 0) {
              // Recursively update the descendants
              return { 
                  ...node, 
                  descendants: deleteOrgNode(nodeId, node.descendants).filter(Boolean) 
              };
          }
          return node;
      })
      .filter(Boolean); // Remove null values (nodes that should be deleted)
};


  return (
    <form onSubmit={(e) => e.preventDefault()}>
      {isVisible && (
        <div className="bg-white p-2 flex px-3 w-full border organisationTitle">
          <div className="flex gap-2 halfwidth">
            <img
              onClick={zoomIn}
              className="search-icon p-1 rounded-lg cursor-pointer w-10 h-10 border-2 bg-white"
              src="/images/Plus.svg"
              alt="Zoom In"
            />
            <div className="text-sm text-gray-700 flex w-16 h-10 justify-center items-center p-1 rounded-lg border-2 bg-white">
              {zoomLevel}%
            </div>
            <img
              onClick={zoomOut}
              className="search-icon p-1 rounded-lg cursor-pointer border-2 w-10 h-10 bg-white"
              src="/images/Minus.svg"
              alt="Zoom Out"
            />
          </div>
          <div className="flex justify-between items-center w-full">
            <input
              value={organisationName}
              onChange={(e) => setOrganisationName(e.target.value)}
              type="text"
              className=" outline-none block py-1.5 OrganizationInput px-3 HRInfo text-gray-900 shadow-sm text-center rounded-lg placeholder:text-gray-500 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6"
              placeholder="Organization Name"
            />
          </div>
        </div>
      )}

      <div className={`w-full relative ${isVisible ? "contentHeight" : ""}`}>
        <div className="flex">
          <DndContext collisionDetection={closestCenter}
           onDragStart={(event) => {
            const draggedUser = selectedPeoplesForOrganisation.find((u) => u.id === event.active.id);
            const draggedNode = findUserinOrgchart(addedPeoplesInOrganisation, event.active.id);
            
            if (draggedUser) setActiveListUser(draggedUser);
            if (draggedNode) setActiveOrgUser(draggedNode);
          }}
          onDragEnd={handleDragEnd}
          onDragCancel={() => {
            setActiveListUser(null);
            setActiveOrgUser(null);
          }}
          >
            {isVisible && (
              <div className="scrollable-section bg-white w-72 p-2">
                {isLoadingPeople ? (
                  <div className="space-y-3 p-2">
                    <div className="flex justify-between items-center">
                      <div className="h-6 w-32 bg-gray-200 animate-pulse rounded"></div>
                      <div className="h-8 w-8 bg-gray-200 animate-pulse rounded"></div>
                    </div>
                    <div className="h-4 w-48 bg-gray-200 animate-pulse rounded mt-2"></div>
                    {Array(5).fill(0).map((_, i) => (
                      <div key={i} className="flex border-2 items-center justify-start p-2 bg-gray-100 animate-pulse h-14 rounded">
                        <div className="rounded-full w-10 h-10 bg-gray-200 mr-3"></div>
                        <div className="space-y-2 flex-1">
                          <div className="h-3 w-24 bg-gray-200 rounded"></div>
                          <div className="h-2 w-16 bg-gray-200 rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <ScrollableList users={selectedPeoplesForOrganisation} setIndividualPopup={setIndividualPopup} activeListUser={activeListUser} />
                )}
              </div>
            )}
            
            {addedPeoplesInOrganisation && addedPeoplesInOrganisation.length > 0 ? (
              <div className="my-org border w-full relative outermostdiv flex flex-col items-center treeOuter z-10 overflow-x-auto">
                <OrganizationChartListing 
                  addedPeoplesInOrganisation={addedPeoplesInOrganisation}
                  setAddedPeoplesInOrganisation={setAddedPeoplesInOrganisation}
                  deleteNode={deleteNode}
                  zoomLevel={zoomLevel} assetBaseUrl={assetBaseUrl} activeOrgUser={activeOrgUser}
                /> 
              </div> 
            ) : isVisible && isLoading ? (
              <div className="my-org border w-full relative outermostdiv flex flex-col items-center treeOuter z-10 overflow-x-auto p-8">
                <SkeletonOrgChart />
              </div>
            ) : null}
          </DndContext>

          {individualPopup && (
            <AddPeoplesToOrganisationPopup
              showPopup={true}
              onClose={() => setIndividualPopup(false)}
              selectedPeoples={selectedPeoples}
              setSelectedPeoples={setSelectedPeoples}
              onSearch={search}
              toggleSelection={toggleSelection}
              isSelected={isSelected}
              users={users}
              query={query}
              setQuery={setQuery}
            />
          )}
        </div>
      </div>
    </form>
  );
};

export default OrganizationChart;
