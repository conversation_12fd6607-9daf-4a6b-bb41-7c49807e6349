import React, { useState, useEffect, useContext } from "react";
import '../../../css/organisationChart.css';

import OrganizationChartListing from "./OrganizationChartListing";
import AddPeoplesToOrganisationPopup from "../popupModals/AddPeoplesToOrganisationPopup";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { OrganizationContext } from "../../../OrganizationContext";
import { ScrollableList } from "./ScrollableList";

const OrganizationChart = ({assetBaseUrl}) => {
  const { isVisible, editingOrganisationId, addedPeoplesInOrganisation, setAddedPeoplesInOrganisation, organisationName, setOrganisationName } = useContext(OrganizationContext);
  const [zoomLevel, setZoomLevel] = useState(100); // Default zoom level
  const [selectedPeoplesForOrganisation, setSelectedPeoplesForOrganisation] = useState([]);
  const [individualPopup, setIndividualPopup] = useState(false);
  const [selectedPeoples, setSelectedPeoples] = useState([]);
  const [users, setUsers] = useState([]);
  const [query, setQuery] = useState("");
  const [lastPage, setLastPage] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [peoplesExistsInOrganisation, setPeoplesExistsInOrganisation] = useState([]);


  // Fetch initial data (mocked here for simplicity)
  useEffect(() => {   
    fetchPaginatedItems();
  }, []);

  useEffect(() => {
    if (editingOrganisationId) {
      const fetchOrganisationDetails = async () => {
        try {
          const response = await fetch(`/organisation-details/${editingOrganisationId}`);
          const data = await response.json();
          console.log("Organisation details fetched:", data);
          setOrganisationName(data.name);
          setAddedPeoplesInOrganisation(data.peoples);
          setPeoplesExistsInOrganisation(data.peoples);
        } catch (error) {
          console.error("Error fetching organisation details:", error);
        }
      };

      fetchOrganisationDetails();
    }
  }, [editingOrganisationId]);

  // Zoom in and out
  const zoomIn = () => setZoomLevel((prevZoom) => Math.min(prevZoom + 10, 200));
  const zoomOut = () => setZoomLevel((prevZoom) => Math.max(prevZoom - 10, 30));

  // Fetch paginated data
  const fetchPaginatedItems = async (page = 1, searchQuery = "") => {
    try {
    setLoading(true);
    const response = await fetch(
        `/paginated-internal-peoples?page=${page}&query=${encodeURIComponent(searchQuery)}`
    );
    const data = await response.json();
    if (page === 1) {
        setUsers(data.data);
        setSelectedPeoples(data.data); // Initialize with first page items
    } else {
        setUsers((prev) => [...prev, ...data.data]);
        setSelectedPeoples((prev) => [...prev, ...data.data]); // Append next page items
    }
    setCurrentPage(data.current_page);
    setLastPage(data.last_page);
    } catch (error) {
      console.error("Error fetching paginated items:", error);
    } finally {
      setLoading(false);
    }
  };

  // Search functionality
  const search = () => {
    setQuery(query.trim().toLowerCase());
    fetchPaginatedItems(1, query.trim().toLowerCase());
  };

  // Toggle user selection
  const toggleSelection = (user) => {
      const exists = selectedPeoplesForOrganisation.find(
      (selectedUser) => selectedUser.id === user.id
      );

      if (exists) {
      setSelectedPeoplesForOrganisation((prev) =>
          prev.filter((selectedUser) => selectedUser.id !== user.id)
      );
      } else {
      setSelectedPeoplesForOrganisation((prev) => [...prev, user]);
      }
  };

  const isSelected = (userId) => {
    // Check if the userId exists in the selectedPeoplesForOrganisation array
    return selectedPeoplesForOrganisation.some((selectedUser) => selectedUser.id === userId);
  };

  // Check if user is in organisation
  const isUserInOrganisation = (userId) => {
    return peoplesExistsInOrganisation.some(
    (existingUser) => existingUser.id === userId
    );
  };


  const handleDragEnd = (event) => {
    const { active, over } = event;

    let isUpperCircle = false;
  
    // Ensure the event is triggered by user interaction
    if (!event.activatorEvent.isTrusted) {
      console.log("Event was not triggered by user action. Ignoring.");
      return;
    }
  
    // Ensure valid active node
    if (!active) {
      console.log("Invalid drag: No active item found.");
      return;
    }
  
    const draggedUser = selectedPeoplesForOrganisation.find((user) => user.id === active.id);
    console.log("dragged user", draggedUser);
    if (!draggedUser) {
      console.log("Dragged user not found in the list.");
      return;
    }
  
    // Remove user from the scrollable list
    setSelectedPeoplesForOrganisation((prev) => prev.filter((user) => user.id !== active.id));
  
    // Determine whether to add as root or under a parent node
    let updatedTree;
    if (!over) {
      // Case: Dropping as a root node
      // console.log("Dropping as a root node.");
      updatedTree = updateOrganizationTree([], null, {
        id: draggedUser.id,
        internalPeople: {
          name: `${draggedUser.forename} ${draggedUser.surname}`,
          role: draggedUser.latest_role,
          location: draggedUser.location, // Default values
          tenure: draggedUser.tenure,
          flightRisk: draggedUser.flight_risk,
          is_relocatable: draggedUser.is_relocatable,
          plan: draggedUser.plan,
          isUpperCircle: isUpperCircle,
          internalUserPlans: draggedUser.internalUserPlans,
          summary: draggedUser.summary,
          company: draggedUser.company_name,
          skills: draggedUser.internal_skills,
          career_history: draggedUser.career_history,
          readiness: (draggedUser.readiness === 1) ? "Ready Now" : "Ready Future", //if 1 ready now else ready Future
          potential: `${draggedUser.potential}`+" Potential",
          performance: draggedUser.performance,
          potentialColorClass: (draggedUser.potential === "High") ? "node-potential-blue" : (draggedUser.potential === "Medium") ? "node-potential-orange" : "node-potential-grey", // readiness will be 1 or 0
          readinessColorClass: (draggedUser.readiness === "1") ? "node-readiness-green" : "node-readiness-red",
        },
        descendants: [],
      });
    } else {
      // Case: Dropping under a parent node
      const parentNode = addedPeoplesInOrganisation.find((node) => node.id === over.id);

      // Check if parent has a plan and if dragged user is part of the same plan
      if (parentNode) {
        if (parentNode.internalPeople?.plan?.id && draggedUser.internalUserPlans) {
          const parentPlanId = parentNode.internalPeople.plan.id;
          isUpperCircle = draggedUser.internalUserPlans.some((plan) => plan.id === parentPlanId);
        }
      }

      // console.log(`Dropping under parent node ID: ${over.id}`);
      updatedTree = updateOrganizationTree(
        addedPeoplesInOrganisation,
        over.id, // Over is the drop target node's ID
        {
          id: draggedUser.id,
          internalPeople: {
            name: `${draggedUser.forename} ${draggedUser.surname}`,
            role: draggedUser.latest_role,
            location: draggedUser.location, // Default values
            tenure: draggedUser.tenure,
            flightRisk: draggedUser.flight_risk,
            is_relocatable: draggedUser.is_relocatable,
            plan: draggedUser.plan,
            isUpperCircle: isUpperCircle,
            internalUserPlans: draggedUser.internalUserPlans,
            summary: draggedUser.summary,
            company: draggedUser.company_name,
            skills: draggedUser.internal_skills,
            career_history: draggedUser.career_history,
            readiness: (draggedUser.readiness === "1") ? "Ready Now" : "Ready Future", //if 1 ready now else ready Future
            potential: `${draggedUser.potential}`+" Potential",
            performance: draggedUser.performance,
            potentialColorClass: (draggedUser.potential === "High") ? "node-potential-blue" : (draggedUser.potential === "Medium") ? "node-potential-orange" : "node-potential-grey",
            readinessColorClass: (draggedUser.readiness === "1") ? "node-readiness-green" : "node-readiness-red",
        },
          descendants: [],
        }
      );
    }
    
    // Set the updated tree in state
    setAddedPeoplesInOrganisation(updatedTree);
  };
  
   
  const updateOrganizationTree = (tree, parentId, newNode) => {
    if (!tree || tree.length === 0) {
      console.log("Empty tree. Adding new node as the root.");
      return [{ ...newNode }];
    }
  
    const addNode = (nodes) => {
      return nodes.map((node) => {
        if (node.id === parentId) {
          // console.log(node);
          return {
            ...node,
            descendants: [...(node.descendants || []), { ...newNode }],
          };
        }
  
        return {
          ...node,
          descendants: addNode(node.descendants || []),
        };
      });
    };
  
    return addNode(tree);
  };
  

  return (
    <form onSubmit={(e) => e.preventDefault()}>
      {isVisible && (
        <div className="bg-white p-2 flex px-3 w-full border organisationTitle">
          <div className="flex gap-2 halfwidth">
            <img
              onClick={zoomIn}
              className="search-icon p-1 rounded-lg cursor-pointer w-10 h-10 border-2 bg-white"
              src="/images/Plus.svg"
              alt="Zoom In"
            />
            <div className="text-sm text-gray-700 flex w-16 h-10 justify-center items-center p-1 rounded-lg border-2 bg-white">
              {zoomLevel}%
            </div>
            <img
              onClick={zoomOut}
              className="search-icon p-1 rounded-lg cursor-pointer border-2 w-10 h-10 bg-white"
              src="/images/Minus.svg"
              alt="Zoom Out"
            />
          </div>
          <div className="flex justify-between items-center w-full">
            <input
              value={organisationName}
              onChange={(e) => setOrganisationName(e.target.value)}
              type="text"
              className=" outline-none block py-1.5 OrganizationInput px-3 HRInfo text-gray-900 shadow-sm text-center rounded-lg placeholder:text-gray-500 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6"
              placeholder="Organization Name"
            />
          </div>
        </div>
      )}

      <div className={`w-full relative ${isVisible ? "contentHeight" : ""}`}>
        <div className="flex">
          <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
            {isVisible && (
              <div className="scrollable-section bg-white w-72 p-2">
                <ScrollableList users={selectedPeoplesForOrganisation} setIndividualPopup={setIndividualPopup} />
              </div>
            )}
            
            {addedPeoplesInOrganisation && addedPeoplesInOrganisation.length > 0 && (
              <div className="my-org border w-full relative outermostdiv flex flex-col items-center treeOuter z-10 overflow-x-auto">
                <OrganizationChartListing 
                  addedPeoplesInOrganisation={addedPeoplesInOrganisation}
                  zoomLevel={zoomLevel} assetBaseUrl={assetBaseUrl}
                /> 
              </div> 
            )}
          </DndContext>

          {individualPopup && (
            <AddPeoplesToOrganisationPopup
              showPopup={true}
              onClose={() => setIndividualPopup(false)}
              selectedPeoples={selectedPeoples}
              setSelectedPeoples={setSelectedPeoples}
              onSearch={search}
              toggleSelection={toggleSelection}
              isSelected={isSelected}
              isUserInOrganisation={isUserInOrganisation}
              users={users}
            />
          )}
        </div>
      </div>
    </form>
  );
};

export default OrganizationChart;
