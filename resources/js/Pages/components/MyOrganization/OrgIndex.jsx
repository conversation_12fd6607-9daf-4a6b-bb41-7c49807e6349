import React, {useContext, useState} from 'react';
import OrgButtons from './OrgButtons';
import OrgContent from './OrgContent';
import { OrganizationContext } from '../../OrganizationContext';
import LeavePagePopup from './popupModals/LeavePagePopup';
import usePreventLeave from './popupModals/usePreventLeave';
import { router } from '@inertiajs/react';

const OrgIndex = ({ isAdminUser, isMasterUser, gridLabelsdata, assetBaseUrl, countries, shareWithUsers, organisations, userId }) => {
    const { isLeavePopup, setIsleavePopup, saveOrgansationCallBack, addedPeoplesInOrganisation, } = useContext(OrganizationContext);
    const [nextUrl, setNextUrl] = useState(null); // Store the next route
    //usePreventLeave(setIsleavePopup, setNextUrl, addedPeoplesInOrganisation);

    const handleDiscard = () => {
        setIsleavePopup(false); // Close the popup
        if (nextUrl) {
            router.visit(nextUrl); // Navigate to the stored URL
          }
      };
    return (
        <div className="relative bg-Color">
            <OrgButtons 
                isAdminUser={isAdminUser} 
                isMasterUser={isMasterUser} 
                gridLabelsdata={gridLabelsdata} 
                assetBaseUrl={assetBaseUrl} 
                countries={countries} 
                shareWithUsers={shareWithUsers} 
            />
            <OrgContent 
                organisations={organisations} 
                userId={userId}  
                assetBaseUrl={assetBaseUrl} 
                gridLabelsdata={gridLabelsdata} 
            />
            
            {isLeavePopup && <LeavePagePopup isLeavePopup={isLeavePopup} handleDiscard={handleDiscard} onSave={saveOrgansationCallBack} />}
        </div>
    );
};

export default OrgIndex;