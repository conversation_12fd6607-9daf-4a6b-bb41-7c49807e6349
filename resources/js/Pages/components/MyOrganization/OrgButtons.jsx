import React, { useState, useEffect, useContext } from 'react';
import { OrganizationContext } from '../../OrganizationContext';
import SearchInternalPeople from './headerButtons/SearchInternalPeople';
import ShareButton from './headerButtons/ShareButton';
import AssessmentButton from './headerButtons/AssessmentButton';
import NineBoxGridButton from './headerButtons/NineBoxGridButton';
import SaveOrganizationButton from './headerButtons/SaveOrganizationButton';
import CompetenciesPopup from './popupModals/CompetenciesPopup';
import NineBoxGridPopup from './popupModals/NineBoxGridPopup';
import IndividualPopup from './popupModals/IndividualPopup';
import BulkUploadCandidatePopup from './popupModals/BulkUploadCandidatePopup';
import ShareOrgansationPopup from './popupModals/ShareOrgansationPopup';
import { Inertia } from '@inertiajs/inertia';



const OrgButtons = ({isAdminUser, isMasterUser,gridLabelsdata,assetBaseUrl,countries,shareWithUsers}) => {

    const {
        addOrganisationButton,
        clickToBeginPopup,
        setClickToBeginPopup, // Correctly using the context function
        setShowOranisationListing,
        setAddOrganisationButton,
        setNineboxGridPopup,
        nineboxGridPopup,
        setCompetenciesPopup,
        competenciesPopup,
		individualPopup,
        setIndividualPopup,
        bulkuploadcandidate,
        setBulkUploadCandidatePopup,
        shareOrgansationPopup,
        setShareOrgansationPopup,
        isVisible,
        editingOrganisationId,
        saveOrgansationCallBack,
    } = useContext(OrganizationContext);
       
    const handleAddOrganisationClick = () => {
        setClickToBeginPopup(!clickToBeginPopup);  // Set popup to visible
        setShowOranisationListing(false);
        setAddOrganisationButton(false);
    };

    const saveCriteriaCallback =() => {
        console.log('Save Criteria Callback');
    }
	
    const saveNineBoxGridCallback =(gridLabels) => {
		 Inertia.post('/my-organization/nineboxgrid/save', { gridLabels }, {
			onSuccess: () => {
			setNineboxGridPopup(false)
			},
			onError: (error) => {
			  
			  console.error('Error:', error);
			},
		  });
    } 
	
    const saveBulkUploadCallback =(csvFile) => {
       
        Inertia.post('/my-organization/uploadCSV', { csvFile }, {
            onSuccess: () => {
                setBulkUploadCandidatePopup(false)
            },
            onError: (error) => {
                
                console.error('Error:', error);
            },
            });
    } 

	const saveIndividualCallback =(forename, surname, gender, selectedCountries, role, startDate, linkedInUrl, functionValue, division, empid, skillData) => {
		 Inertia.post('/my-organization/individual/save', { individualdata:forename, surname, gender, selectedCountries, role, startDate, linkedInUrl, functionValue, division, empid, skillData }, {
			onSuccess: () => {
			setIndividualPopup(false)
			},
			onError: (error) => {
			  console.error('Error:', error);
			},
		  });
    }
    const saveOrgansationPopupCallback =(selectedshareOrgansationPeoples) => {
        Inertia.post('/my-organization/sharepeople/save', {selectedshareOrgansationPeoples,editingOrganisationId}, {
           onSuccess: () => {
            setShareOrgansationPopup(false)
           },
           onError: (error) => {
             console.error('Error:', error);
           },
         });
   }

    
    return (
        <div className="bg-white col-span-1 shadow-sm border-b border-gray-200 p-3">
            <div className="flex justify-between items-center gap-x-3">
                <h2 className="text-xl font-semibold text-gray-800 mr-auto"></h2>
                <div className="flex gap-5">
                    <SearchInternalPeople />
                    {editingOrganisationId && (
                        <>
                        <ShareButton onClick={() => setShareOrgansationPopup(true)} />
                        <ShareOrgansationPopup shareOrgansationPopup={shareOrgansationPopup} setShareOrgansationPopup={setShareOrgansationPopup} saveOrgansationPopupCallback={saveOrgansationPopupCallback}  shareWithUsers={shareWithUsers} />
                        </>
                    )}

                    {(isAdminUser || isMasterUser) && (
                        <>
                            <NineBoxGridButton onClick={() => setNineboxGridPopup(true)} />
							<NineBoxGridPopup gridLabelsdata={gridLabelsdata} nineboxGridPopup={nineboxGridPopup} setNineboxGridPopup={setNineboxGridPopup} saveNineBoxGridCallback={saveNineBoxGridCallback} />
							 
                        </>
                    )}
                                     

                    {addOrganisationButton && (
                        <div className='rounded-lg p-px hover:scale-105'>
                            <button id="addOrganisationButton"
                                className="transition flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100"
                                onClick={handleAddOrganisationClick} >
                                <img className="h-3 w-auto" src="images/Plus.svg" alt="Plus Icon" />
                                <span>Add Organisation</span>
                            </button>
                        </div>
                    )}
                    
                    <div className="flex text-right">
                        <div className="rounded-lg p-px hover:scale-105">
                            <button onClick={() => setIndividualPopup(true)}
                                className="transition flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100">
                                <img className="h-3 w-auto" src="images/Plus.svg" alt="Plus Icon" />
                                <span> Add Individual</span>
                            </button>
				
                        </div>
                    </div>
								
					{individualPopup && (
                        <IndividualPopup individualPopup={individualPopup} assetBaseUrl={assetBaseUrl} setIndividualPopup={setIndividualPopup} saveIndividualCallback={saveIndividualCallback} getcountries={countries} />
                    )} 
					
                    {isVisible && (isAdminUser || isMasterUser) && (
                        <div>
                            <button
                                onClick={() => setCompetenciesPopup(true)}
                                className="transition flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100"
                            >
                                <img className="h-3 w-auto" src="/images/Plus.svg" alt="Plus Icon" />
                                <span>Competencies</span>
                            </button>
                        </div>  
                    )}
                    {competenciesPopup && (
                        <CompetenciesPopup competenciesPopup={competenciesPopup} setCompetenciesPopup={setCompetenciesPopup} type="org" candidate_id="" />
                    )} 
                    <div className="w-32">
                        <button
                            onClick={() => setBulkUploadCandidatePopup(true)}
                            className="w-full flex justify-center items-center gap-2 font-semibold transition ease-in-out delay-100 text-sm text-black rounded-lg py-2 hover:bg-cyan-500 hover:text-white duration-100 border rounded-lg p-px shadow-md hover:shadow-lg hover:scale-105">
                            <img className="h-3 w-auto" src="images/CloudArrowUp.svg" alt="Your Company" />
                            <span> Upload</span>
                        </button>
                    </div>
                    {bulkuploadcandidate && (
                            <BulkUploadCandidatePopup bulkuploadcandidate={bulkuploadcandidate} setBulkUploadCandidatePopup={setBulkUploadCandidatePopup} saveBulkUploadCallback={saveBulkUploadCallback} assetBaseUrl={assetBaseUrl} />
                        )} 

                    {isVisible && (
                        <SaveOrganizationButton saveOrgansationCallBack={saveOrgansationCallBack} />
                    )}
                </div>
            </div>
        </div>
    );
  };
  
  export default OrgButtons;