import React, { useState, useEffect, useRef } from "react";
import axios from "axios";

const SearchInternalPeople = () => {
  const [query, setQuery] = useState("");
  const [filteredItems, setFilteredItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const [selectedItem, setSelectedItem] = useState(null);
  const [showSearchList, setShowSearchList] = useState(false);

  const searchInputRef = useRef(null);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        searchInputRef.current !== event.target
      ) {
        setShowSearchList(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  const search = async () => {
    if (query.trim() === "") {
      setFilteredItems([]);
      return;
    }
    setLoading(true);
    setShowSearchList(true);

    try {
      const response = await axios.get(`/paginated-internal-peoples`, {
        params: { query, page: 1 },
      });

      const { data, current_page, last_page } = response.data;
      setFilteredItems(data);
      setCurrentPage(current_page);
      setLastPage(last_page);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleScroll = (event) => {
    const ulElement = event.target;
    const isAtBottom =
      ulElement.scrollTop + ulElement.clientHeight + 50 >= ulElement.scrollHeight;

    if (isAtBottom && currentPage < lastPage && !loading) {
      fetchPaginatedItems(currentPage + 1);
    }
  };

  const fetchPaginatedItems = async (page) => {
    setLoading(true);

    try {
      const response = await axios.get(`/paginated-internal-peoples`, {
        params: { query, page },
      });

      const { data, current_page, last_page } = response.data;
      setFilteredItems((prevItems) => [...prevItems, ...data]);
      setCurrentPage(current_page);
      setLastPage(last_page);
    } catch (error) {
      console.error("Error fetching paginated items:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleItemClick = (item) => {
    setSelectedItem(item);
    setFilteredItems([]);
    setQuery("");
    setShowSearchList(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="flex items-center w-full border border-gray-300 rounded-md">
        <input
          ref={searchInputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search Internal People"
          className="focus:outline-none focus:border-mainBlue focus:ring-1 focus:ring-mainBlue w-full"
        />

        <button
          onClick={search}
          className="bg-mainBlue text-white px-4 py-2 rounded-r-md flex items-center justify-center focus:outline-none hover:bg-blue-700"
        >
          <i className="fas fa-search"></i>
        </button>
      </div>

      {showSearchList && (
        <ul
          onScroll={handleScroll}
          className={`absolute z-50 bg-white overflow-y-auto border border-gray-200 rounded-md shadow-lg mt-1 ${
            filteredItems.length > 5 ? "h-96" : "max-h-60"
          }`}
        >
          {loading && <li className="p-2 text-gray-500">Searching...</li>}

          {!loading && filteredItems.length === 0 && query !== "" && (
            <li className="p-2 text-gray-500">No results found.</li>
          )}

          {filteredItems.map((item) => (
            <li
              key={item.id}
              className="p-2 border-b cursor-pointer hover:bg-gray-100"
              onClick={() => handleItemClick(item)}
            >
              {item.forename} {item.surname}
            </li>
          ))}
        </ul>
      )}

      {selectedItem && (
        <div className="mt-4">
          <h3 className="text-lg font-semibold">Selected Person</h3>
          <p>Name: {selectedItem.forename} {selectedItem.surname}</p>
          {/* Additional details can be displayed here */}
        </div>
      )}
    </div>
  );
};

export default SearchInternalPeople;