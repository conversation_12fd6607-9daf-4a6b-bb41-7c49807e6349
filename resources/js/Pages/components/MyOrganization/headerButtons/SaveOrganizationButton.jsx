import React, {useContext} from "react";
import { OrganizationContext } from "../../../OrganizationContext";
const SaveOrganizationButton = ({ saveOrgansationCallBack }) => {

  const { isVisible, editingOrganisationId } = useContext(OrganizationContext);

  if (!isVisible) return null; // Don't render if `showSaveButton` is false 

  return (
    <div
      className="w-32 rounded-lg p-px shadow hover:scale-105"
      style={{ display: isVisible ? "block" : "none" }} // Similar to x-show
    >
      <button
        onClick={saveOrgansationCallBack} // Trigger the function passed as prop
        className="w-full flex justify-center items-center gap-2 font-semibold transition ease-in-out delay-100 text-sm rounded-lg py-2 bg-cyan-500 text-white duration-100"
      >
        <img
          className="h-3 w-auto"
          src="/images/tick.svg" // Adjust the path if necessary
          alt="Your Company"
        />
        <span>{editingOrganisationId ? "Update" : "Save"}</span>
      </button>
    </div>
  );
};

export default SaveOrganizationButton;