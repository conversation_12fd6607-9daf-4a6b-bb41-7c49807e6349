import React, { useState, useEffect, useContext } from 'react';
import { OrganizationContext } from '../../OrganizationContext';
import ClickToBeginPopup from './contentScreen/ClickToBeginPopup';
import OrganizationChart from './contentScreen/OrganizationChart';
import LoadingSpinner from '../Loader/LoadingSpinner';
import { Inertia } from "@inertiajs/inertia";
import Swal from 'sweetalert2';
import ErrorBoundary from '../ErrorBoundary';

const OrgContent = ({organisations, userId,assetBaseUrl, gridLabelsdata}) => {
    const context = useContext(OrganizationContext);
    
    if (!context) {
        console.error('OrgContent must be used within an OrganizationContext Provider');
        return null;
    }

    const {
            clickToBeginPopup,
            showOranisationListing,// Correctly using the context function
            setShowOranisationListing,
            editingOrganisationId,
            setEditingOrganisationId,
            setIsVisible,
            isVisible,
            addOrganisationButton,
            setAddOrganisationButton,
            setNineBoxGridArr,
            setOrganisationName,
            setAddedPeoplesInOrganisation,
            viewOrganisation,
            isLoading,
        } = context;

        useEffect(() => {
            setNineBoxGridArr(gridLabelsdata);
        }, [gridLabelsdata, setNineBoxGridArr]);

    // Local handler to avoid potential hoisting issues
    const handleViewOrganisation = (organisationId) => {
        if (viewOrganisation && typeof viewOrganisation === 'function') {
            viewOrganisation(organisationId);
        } else {
            console.error('viewOrganisation function not available');
        }
    };

    const confirmDelete = (organisationId, title, message, iconUrl, action) => {
        console.log(`Confirm delete for: ${organisationId}, Title: ${title}`);
            Swal.fire({
                html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                        <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">${title}</h2>
                        <p class="px-5 font-normal">${message}</p>
                        <div class="w-full border-t mt-5 border-gray-200"></div>`,
                showDenyButton: true,
                showCancelButton: false,
                confirmButtonText: "Delete",
                denyButtonText: `Cancel`,
                reverseButtons: true,
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                    denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
                },
                showCloseButton: true
            }).then((result) => {
                if (result.isConfirmed) {
                        Inertia.post('/organisation/delete', { organisationId }, {
                        onSuccess: () => {
                            Swal.fire('Deleted!', 'The Organisation has been deleted.', 'success');
                        },
                        onError: (error) => {
                            Swal.fire('Error!', 'Something went wrong, please try again.', 'error');
                            console.error('Error:', error);
                        },
                        });
                }
            });
    }; 

        // console.log("Organisations list: ", organisations);

    return (
        <>
            {/* Organization List */}
            {showOranisationListing && (
            <div className={`grid grid-cols-3 p-4 gap-4 ${organisations.length > 6 ? 'contentHeight2' : ''}`}>
                {organisations.map((organisation) => (
                    <div key={organisation.organisation_id} className="bg-white containerHeight rounded-lg">
                        <div className="p-3">
                            <div className="flex justify-between">
                                <div className="text-sm">
                                    <span className="font-semibold text-black">
                                        {organisation.organisation_title}
                                    </span>
                                    <span className="font-normal">&nbsp;Organization Chart</span>
                                </div>
                                {organisation.created_by === userId && (
                                    <div className="dropdown-container relative">
                                        <button
                                            className="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm py-2.5 inline-flex items-center"
                                            type="button"
                                        >
                                            <img
                                                className="h-5 w-5"
                                                src="/images/DotsThreeVertival.svg"
                                                alt="Options"
                                            />
                                        </button>
                                        <div className="dropdown-menu hidden z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44">
                                            <ul className="py-2 text-sm text-gray-700">
                                                <li className="cursor-pointer">
                                                    <div
                                                        onClick={() =>
                                                            confirmDelete(
                                                                organisation.organisation_id,
                                                                `Delete ${organisation.organisation_title}`,
                                                                `Are you sure want to delete the organisation <b>${organisation.organisation_title}</b>?`,
                                                                '/images/redTrashIcon.svg',
                                                                'deleteOrganisation'
                                                            )
                                                        }
                                                        className="flex gap-5 py-2 px-5 hover:bg-gray-100"
                                                    >
                                                        <img
                                                            className="h-5 w-5"
                                                            src="/images/deleteIcon.svg"
                                                            alt="Delete"
                                                        />
                                                        <span className="font-semibold text-sm">Delete</span>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div className="flex justify-between mt-3">
                                <div className="flex gap-2">
                                    <img
                                        className="h-5 w-5 cursor-pointer"
                                        src="/images/UsersThree.svg"
                                        alt="People"
                                    />
                                    <span className="font-normal text-sm block">Number of People</span>
                                </div>
                                <span className="block text-sm">{organisation.organisation_people}</span>
                            </div>
                            <div className="flex justify-between mt-3">
                                <div className="flex gap-2 items-center">
                                    <span className="font-normal text-sm block">Female-Ratio</span>
                                    <progress
                                        value={organisation.female_ratio}
                                        max="100"
                                        className="progress-bar progressColor1"
                                    ></progress>
                                </div>
                                <span className="block text-sm">{organisation.female_ratio}%</span>
                            </div>
                            <div className="flex justify-between mt-2">
                                <div className="flex gap-6 items-center">
                                    <span className="font-normal text-sm block">Male-Ratio</span>
                                    <progress
                                        value={organisation.male_ratio}
                                        max="100"
                                        className="progress-bar progressColor2"
                                    ></progress>
                                </div>
                                <span className="block text-sm">{organisation.male_ratio}%</span>
                            </div>
                            <button
                                onClick={() => handleViewOrganisation(organisation.organisation_id)}
                                className="bg-white flex items-center space-x-1 mt-2 border border-gray-300 rounded-lg px-2 py-2 hover:bg-gray-200 font-medium text-black text-md justify-center w-full"
                            >
                                <h1 className="text-md font-semibold">View</h1>
                            </button>
                        </div>
                    </div>
                ))}
            </div>
            )}
            {clickToBeginPopup && (
                <ClickToBeginPopup assetBaseUrl={assetBaseUrl} />
            )}
            {isVisible && (
                <ErrorBoundary>
                    <OrganizationChart assetBaseUrl={assetBaseUrl} />
                </ErrorBoundary>
            )}

            <LoadingSpinner isLoading={isLoading} />
        </>
    );
  };
  
  export default OrgContent;