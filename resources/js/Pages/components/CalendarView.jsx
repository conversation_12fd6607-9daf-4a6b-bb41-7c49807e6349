import React, { useState } from 'react';

const CalendarView = ({ assetBaseUrl, Interviewcandidates }) => {
    const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
    const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
    const [selectedEvents, setSelectedEvents] = useState([]);
    const [showEvents, setShowEvents] = useState(false);

    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const monthNames = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ];
    const today = new Date();
    const firstDay = new Date(currentYear, currentMonth, 1).getDay();
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const daysInPreviousMonth = new Date(currentYear, currentMonth, 0).getDate();

    const goToPreviousMonth = () => {
        if (currentMonth === 0) {
            setCurrentMonth(11);
            setCurrentYear(currentYear - 1);
        } else {
            setCurrentMonth(currentMonth - 1);
        }
    };

    const goToNextMonth = () => {
        if (currentMonth === 11) {
            setCurrentMonth(0);
            setCurrentYear(currentYear + 1);
        } else {
            setCurrentMonth(currentMonth + 1);
        }
    };

    const goToToday = () => {
        setCurrentMonth(today.getMonth());
        setCurrentYear(today.getFullYear());
    };
	
	const normalizeDateKey = (date) => {
		const parsedDate = new Date(date);
		const normalizedMonth = (parsedDate.getMonth() + 1).toString().padStart(2, '0');
		const normalizedDay = parsedDate.getDate().toString().padStart(2, '0');
		const normalizedYear = parsedDate.getFullYear();
		return `${normalizedYear}-${normalizedMonth}-${normalizedDay}`;
	};

	const normalizedInterviewcandidates = Object.keys(Interviewcandidates).reduce((acc, key) => {
		const normalizedKey = normalizeDateKey(key);
		acc[normalizedKey] = Interviewcandidates[key];
		return acc;
	}, {});


    const handleDateClick = (dayNumber) => {
        // Format dayNumber and month to match "YYYY-MM-DD"
        const formattedMonth = (currentMonth + 1).toString().padStart(2, '0');
		const formattedDay = dayNumber.toString().padStart(2, '0');

		const dateKey = `${currentYear}-${formattedMonth}-${formattedDay}`;
		const dateEvents = normalizedInterviewcandidates[dateKey] || [];
       
        if (dateEvents.length > 0) {
            setSelectedEvents(dateEvents);
            setShowEvents(true);
        }
    };

    const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;

    return (
        <div className="bg-white p-4 rounded-lg shadow-md CalendarView">
            <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">
                    {monthNames[currentMonth]} {currentYear}
                </h3>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={goToToday}
                        className="border px-2 py-1.5 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-200"
                    >
                        Today
                    </button>
                    <button
                        onClick={goToPreviousMonth}
                        className="p-1 rounded-md border hover:bg-gray-100"
                    >
                        <img className="w-5 h-5" src={`${assetBaseUrl}images/leftarrow.svg`} alt="Back" />
                    </button>
                    <button
                        onClick={goToNextMonth}
                        className="p-1 rounded-md border hover:bg-gray-100"
                    >
                        <img className="w-5 h-5" src={`${assetBaseUrl}images/rightarrow.svg`} alt="Forward" />
                    </button>
                </div>
            </div>

            <div className="grid grid-cols-7 text-center text-sm font-medium text-gray-500">
                {daysOfWeek.map((day) => (
                    <div key={day} className="py-1 px-6 font-bold border border-b-0">
                        {day}
                    </div>
                ))}
            </div>

            <div className="grid grid-cols-7 gap-px border border-gray-200 bg-gray-200">
                {Array.from({ length: totalCells }).map((_, index) => {
                    let dayNumber;
                    let isToday = false;
                    let className = "h-[3.3rem] bg-white border-gray-100 flex items-center justify-center relative cursor-pointer hover:bg-gray-100";

                    if (index < firstDay) {
                        dayNumber = daysInPreviousMonth - firstDay + index + 1;
                        className += " text-gray-400"; // Previous month
                    } else if (index < firstDay + daysInMonth) {
                        dayNumber = index - firstDay + 1;
                        isToday = dayNumber === today.getDate() &&
                            currentMonth === today.getMonth() &&
                            currentYear === today.getFullYear();
                        if (isToday) className += " bg-slate-900";
                    } else {
                        dayNumber = index - firstDay - daysInMonth + 1;
                        className += " text-gray-400"; // Next month
                    }

                    // Format the dayNumber and month to match "YYYY-MM-DD"
                    const formattedMonth = (currentMonth + 1).toString().padStart(2, '0');
					const formattedDay = dayNumber.toString().padStart(2, '0');

					const dateKey = `${currentYear}-${formattedMonth}-${formattedDay}`;
					const dateEvents = normalizedInterviewcandidates[dateKey] || [];
					//console.log(normalizedInterviewcandidates);
					//console.log(dateKey);
                    return (
                        <div key={index} className={dateEvents.length ? "has-eventsh-[3.3rem]  border-gray-100 flex items-center justify-center relative cursor-pointer bg-blue-500 hover:bg-blue-900 text-white" : className} onClick={() => handleDateClick(dayNumber)}>
                            <div>{dayNumber}</div>
                        </div>
                    );
                })}
            </div>

            {showEvents && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-10">
					<div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 animate__animated animate__fadeIn">
						<h3 className="text-xl font-semibold mb-4 text-gray-800">Scheduled Events</h3>
						<ul className="space-y-3">
							{selectedEvents.map((event, index) => (
								<li key={index} className="flex justify-between items-center p-3 border-b">
									<div className="text-sm text-gray-800">{event.candidate_name}</div>
									<a
										href={`/recruitment/${event.recruitmentID}`} 
										className="text-blue-500 hover:underline text-xs"
										target="_blank" 
										rel="noopener noreferrer"
									>
										View Interview
									</a>
								</li>
							))}
						</ul>
						<button
							onClick={() => setShowEvents(false)}
							className="mt-4 w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
						>
							Close
						</button>
					</div>
				</div>
			)}

        </div>
    );
};

export default CalendarView;
