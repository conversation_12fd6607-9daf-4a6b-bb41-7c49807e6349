import React, { useState } from 'react';
// import ModalComponent from '../Layouts/ModalComponent';
import SetupRecruitmentModal from './components/SetupRecruitmentModal';
import { Inertia } from '@inertiajs/inertia';
import ToastNotification from "./components/ToastNotification";
// import { DeleteConfirmModal } from './components/DeleteConfirmModal'; 

const Recruitment = ({ assetBaseUrl,recruitments,groupedPlanScores,candidateCount,user_role,user_id }) => {
  const [status, setStatus] = useState("All Statuses");
  const [search, setSearch] = useState("");
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const [showModal, setShowModal] = useState(false);
  const openModal = () => setShowModal(true);
  const closeModal = () => setShowModal(false);

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };

  const handleStatusChange = (newStatus) => {
    setStatus(newStatus);
    setDropdownOpen(false); // Close dropdown after selection
  };

  // const handleDelete = (recruitmentId) => {
	  
	 
	  
	  
	  // Inertia.post('/recruitment/delete', {recruitmentId}, {
		  // onSuccess: () => {
			
		// },
		// onError: (error) => {
		  // console.log('Error:', error);
		// },
	  // }); 
  // }; 
  
	const handleDelete = (recruitmentId, name ) => {
	  Swal.fire({
		html: `<div class="px-5 w-full flex justify-center mb-3">
				  <img class="h-10 w-10" src="${assetBaseUrl}images/redTrashIcon.svg" alt="">
				</div>
				<h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Delete ${name}</h2>
				<p class="px-5 font-normal">Are you sure you want to delete the recruitment <b>${name}</b>?</p>
				<div class="w-full border-t mt-5 border-gray-200"></div>`,
		showDenyButton: true,
		showCancelButton: false,
		confirmButtonText: 'Delete',
		denyButtonText: 'Cancel',
		reverseButtons: true,
		buttonsStyling: false,
		customClass: {
		  confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md',
		  denyButton: 'bg-white btnsWidth btnsmargin text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md'
		},
		showCloseButton: true
	  }).then((result) => {
		if (result.isConfirmed) {
		  // Proceed with deletion if confirmed
		  Inertia.post('/recruitment/delete', { recruitmentId }, {
			onSuccess: () => {
			  Swal.fire('Deleted!', 'The recruitment has been deleted.', 'success');
			},
			onError: (error) => {
			  Swal.fire('Error!', 'Something went wrong, please try again.', 'error');
			  console.error('Error:', error);
			},
		  });
		}
	  });
	};

  
  
  
	const handleArchive = (recruitmentId, currentStatus) => {
	  const newStatus = currentStatus === 'archived' ? 'active' : 'archived';

	  Inertia.post('/recruitment/archive', { recruitmentId, status: newStatus }, {
		onSuccess: () => {
		  //console.log(`Recruitment ${recruitmentId} status updated to ${newStatus}`);
		},
		onError: (error) => {
		  console.error('Error:', error);
		},
	  });
	};


function getStatusClasses(status) {
  switch (status) {
    case 'Active':
      return 'bg-green-50 text-green-700 ring-green-600/20';
    case 'Closed':
      return 'bg-red-50 text-red-700 ring-red-600/20';
    case 'archived':
      return 'bg-blue-50 text-cyan-700 ring-cyan-300';
    default:
      return 'bg-green-50 text-green-700 ring-green-600/20'; // Default classes
  }
}

  return (
    <div className="requirement-section">
	<ToastNotification />
      <div className="customHeight overflow-y-scroll">
        <div className="flex items-center px-4 py-4 justify-between border-b bg-white">
          <h1 className="whitespace-nowrap text-3xl font-medium">Recruitment</h1>
          <div className="flex gap-4">
		  {user_role !== 'Viewer' && (
			<>
				<button 
					onClick={openModal}
					className="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium w-24 text-white text-md justify-center"
				><img className="h-5 w-5" src={`${assetBaseUrl}images/plus-white-without-circle.svg`} alt="More options" />
					<span className="text-md font-semibold">Add</span>
				</button>
			</>
		  )}
          </div>
        </div>
		 <SetupRecruitmentModal
			assetBaseUrl={assetBaseUrl}
			showModal={showModal}
			closeModal={closeModal}
		  />
		
      <div className="my-5 px-4">
        <ul role="list" className="grid grid-cols-1 sm:grid-cols-2 gap-6 lg:grid-cols-3 z-30">
          {recruitments
            .filter(recruitment => recruitment.recruitment_name.toLowerCase().includes(search.toLowerCase()))
            .map(recruitment => (
              <li key={recruitment.id} className="col-span-1 divide-gray-200 border-solid border border-grey-200 rounded-xl bg-white shadow-md">
                <div className="flex w-full h-5/6 justify-between mb-3">
                  <div className="flex-1 flex-col flex h-full px-4 pt-2 relative pb-1">
                    <div className="flex justify-between items-center relative">
                      <div className="flex h-1/6 items-center space-x-3 bg-transparent z-10">
                        <h3 className="whitespace-wrap text-lg font-medium text-gray-900">{recruitment.recruitment_name}</h3>
                        <span className={`inline-flex flex-shrink-0 items-center rounded-lg px-3 py-2 text-xs font-medium ${getStatusClasses(recruitment.status)}`}>
                          {recruitment.status && 
								recruitment.status !== undefined 
									? `${recruitment.status.charAt(0).toUpperCase()}${recruitment.status.slice(1)}`
									: 'Active'}
                        </span>
                      </div>
					  	{user_role !== 'Viewer' && (
								<>
                      <div className="dropdown-container relative">
                        <button
                          onClick={toggleDropdown}
                          className="text-white focus:ring-4 focus:outline-none  font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center"
                        >
                          <img className="h-5 w-5" src={`${assetBaseUrl}images/DotsThreeVertival.svg`} alt="More options" />
                        </button>
                        <div className="dropdown-menu absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44">
                          <ul className="py-2 text-sm text-gray-700">
                            <li className="cursor-pointer">
                              <a href={`recruitment/${recruitment.id}`} className="p-0"> 
                                <div className="flex  gap-2 px-4 py-2 justify-start hover:bg-gray-100">
                                  <img className="h-5 w-5" src={`${assetBaseUrl}images/view.svg`} alt="View" />
                                  <span className="font-semibold text-sm text-[#101828]">View</span>
                                </div>
                              </a>
                            </li>
						{user_id == recruitment.user_id && (
								<>
								<li className="cursor-pointer" onClick={() => handleArchive(recruitment.id, recruitment.status)}>
								
									<div className="flex justify-start gap-2 px-4 py-2 hover:bg-gray-100">
									  <img className="h-5 w-5" src={`${assetBaseUrl}images/archive.svg`} alt="Archive" />
									  <span className="font-semibold text-sm text-[#101828]"> {recruitment.status === 'archived' ? 'Restore' : 'Archive'}</span>
									</div>
								
								</li>
								
								<li className="cursor-pointer" onClick={() => handleDelete(recruitment.id,recruitment.recruitment_name)}>
								  <div className="flex gap-2 py-2 px-4 justify-start hover:bg-gray-100">
									<img className="h-5 w-5" src={`${assetBaseUrl}images/deleteIcon.svg`} alt="Delete" />
									<span className="font-semibold text-sm text-[#101828]">Delete</span>
								  </div>
								</li>
								</>
							)}
                          </ul>
                        </div>
                      </div>
					   </>
					)}
                    </div>
               
					<div className="mt-4 items-end grid grid-cols-2 gap-2">
						<div>
							<dl className="text-gray-700 text-md whitespace-nowrap inline-flex">
								<img className="lh-8  w-5 h-5 mr-1 mt-0 object-fill" 
									 src={`${assetBaseUrl}images/peoples.svg`} 
									 alt="Number of Candidates" />
								Number of Candidates
							</dl>
						</div>
						<div class="flex justify-end">
							<dd className="text-gray-700 text-md whitespace-nowrap text-right mr-1 w-[54px]">
								{candidateCount[recruitment.id] && 
								candidateCount[recruitment.id].length !== undefined 
									? `${candidateCount[recruitment.id].length}` 
									: '0'}
							</dd>
						</div>
					</div>
					<div className="mt-2 items-end grid grid-cols-2 gap-2">
						<div>
							<dl className="text-gray-700 text-md whitespace-nowrap inline-flex">
								<img className="lh-8  w-5 h-5 mr-1 mt-0 object-fill" 
									 src={`${assetBaseUrl}images/chaticon.svg`} 
									 alt="Number of Interviews" />
								Number of Interviews
							</dl>
						</div>
						<div class="flex justify-end">
							<dd className="text-gray-700 text-md whitespace-nowrap text-right mr-1 w-[54px]">
								{recruitment.stages_count && 
								recruitment.stages_count !== undefined 
									? `${recruitment.stages_count}` 
									: '0'}
								
								
							</dd>
						</div>
					</div>
				
					<div className="mt-4 items-end grid grid-cols-2 gap-2">
						<div>
							<dl className="text-gray-500 text-sm whitespace-nowrap">Female-Ratio</dl>
						</div>
						<div className="flex">
							<progress 
								className={`${recruitment.id} progress progress-orange`}
								max="100" 
								value={
										groupedPlanScores[recruitment.id] && 
										groupedPlanScores[recruitment.id]['Female-ratio'] !== undefined 
											? groupedPlanScores[recruitment.id]['Female-ratio'] 
											: 0
									}
							></progress>
							<dd className="text-gray-500 text-sm whitespace-nowrap text-left ml-2 w-[54px]">
								{groupedPlanScores[recruitment.id] && 
								groupedPlanScores[recruitment.id]['Female-ratio'] !== undefined 
									? `${groupedPlanScores[recruitment.id]['Female-ratio']}%` 
									: '0%'}
							</dd> 
						</div>
					</div>

					<div className="mt-2 items-end grid grid-cols-2 gap-2">
						<div>
							<dl className="text-gray-500 text-sm whitespace-nowrap">Male-Ratio</dl>
						</div>
						<div className="flex">
							<progress 
								className="progress progress-blue" 
								max="100" 
								 value={
									groupedPlanScores[recruitment.id] && 
									groupedPlanScores[recruitment.id]['Male-Ratio'] !== undefined 
										? groupedPlanScores[recruitment.id]['Male-Ratio'] 
										: 0
								}
							></progress>
							<dd className="text-gray-500 text-sm whitespace-nowrap text-left ml-2 w-[54px]">
								  {groupedPlanScores[recruitment.id] && 
									groupedPlanScores[recruitment.id]['Male-Ratio'] !== undefined 
										? `${groupedPlanScores[recruitment.id]['Male-Ratio']}%` 
										: '0%'}
									
							</dd>
						</div>
					</div>
					
					 <div className="pt-3 pb-0">
						<a 
							href={`recruitment/${recruitment.id}`}
							className="bg-white flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-1 hover:bg-gray-200 font-medium text-black text-md justify-center w-full"
						>
							<h1 className="text-md font-semibold">View</h1>
						</a>
					</div>
					
					
                  </div>
                </div>
              </li>
            ))}
        </ul>
      </div>
	  </div>
    </div>
  );
};

export default Recruitment;
