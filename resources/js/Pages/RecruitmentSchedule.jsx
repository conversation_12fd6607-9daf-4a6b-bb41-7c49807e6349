import React, { useState, useEffect, useRef } from "react";
import { DragDropContext } from "react-beautiful-dnd";
import Section from "./components/Section";
import ToastNotification from "./components/ToastNotification";
import SharePopup from "./components/SharePopup";
import { Inertia } from "@inertiajs/inertia";
import CandidateListItem from "./components/CandidateListItem";
import CandidateDetailsModal from "./components/CandidateDetailsModal";
 

const RecruitmentSchedule = ({
  editorUsersList,
  viewerUsersList,
  interviewerList,
  recruitment_name,
  recruitment_pipeline = {}, // Default to empty object if undefined
  recruitment_stage,
  recruitment_id,
  recruitment_status,
  assetBaseUrl,
  user_role,
  userlist,
  viewer_shared_with,
  editor_shared_with,
}) => {
  
// console.log(recruitment_pipeline);

  const [stages, setStages] = useState(recruitment_stage);
  const [query, setQuery] = useState('');
  const [showShareModal, setShowShareModal] = useState(false);

  const [filteredItems, setFilteredItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showSearchList, setShowSearchList] = useState(false);
  const [lastPage, setLastPage] = useState(1);
 
  const [showCandidateModal, setShowCandidateModal] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [selectedStage, setSelectedStage] = useState(null);
 
  const dropdownRef = useRef(null);

  const getUniquePlanIds = () => {
    const planIds = new Set();

    Object.values(recruitment_pipeline).forEach((items) => {
      items.forEach((item) => {
        if (item.Plan_id !== undefined) {
          planIds.add(item.Plan_id); 
        }
      });
    });

    return [...planIds][0] || null;
  };
  
  const uniquePlanIds = getUniquePlanIds();
  
  const isAllEmpty = Object.values(recruitment_pipeline).every(
    (array) => array.length === 0
  );
  
  // console.log(uniquePlanIds);
  const updateStageName = (updatedStage) => {
    const updatedStages = stages.map((stage) =>
      stage.stagecolumnID === updatedStage.stagecolumnID
        ? { ...stage, stage_name: updatedStage.stage_name }
        : stage
    );

    setStages(updatedStages);
  };

  const handleDragEnd = (result) => {
	  if (user_role === 'Viewer') {
		// console.log('Viewer role - Drag and Drop disabled');
		return; // Exit early, do nothing
	  }  
    const { source, destination } = result;
    if (!destination) return; // If dropped outside any droppable

    const sourceStageIndex = stages.findIndex(
      (stage) => stage.stagecolumnID === source.droppableId
    );
    const destinationStageIndex = stages.findIndex(
      (stage) => stage.stagecolumnID === destination.droppableId
    );

    if (sourceStageIndex === destinationStageIndex) {
      const updatedCandidates = Array.from(
        recruitment_pipeline[stages[sourceStageIndex].stagecolumnID] || []
      );
      const [movedCandidate] = updatedCandidates.splice(source.index, 1);
      updatedCandidates.splice(destination.index, 0, movedCandidate);
      recruitment_pipeline[stages[sourceStageIndex].stagecolumnID] = updatedCandidates;
    } else {
      const sourceCandidates = Array.from(
        recruitment_pipeline[stages[sourceStageIndex].stagecolumnID] || []
      );
      const [movedCandidate] = sourceCandidates.splice(source.index, 1);

      const destinationCandidates = Array.from(
        recruitment_pipeline[stages[destinationStageIndex].stagecolumnID] || []
      );
      destinationCandidates.splice(destination.index, 0, movedCandidate);

      recruitment_pipeline[stages[sourceStageIndex].stagecolumnID] = sourceCandidates;
      recruitment_pipeline[stages[destinationStageIndex].stagecolumnID] = destinationCandidates;

      const sourceStageName = stages[sourceStageIndex].stage_name;
      const destinationStageName = stages[destinationStageIndex].stage_name;
// console.log(stages);
      Inertia.post('/recruitment/saveprogress', {
        droppableId: destination.droppableId,
        ID: movedCandidate.id,
        currentStage: sourceStageName,
        destinationStage: destinationStageName,
      }, {
        onSuccess: () => {
        },
        onError: (error) => {
          console.error('Error:', error);
        },
      });
    }

    setStages([...stages]);
  };

  const handleArchive = (recruitment_id, currentStatus) => {
    const newStatus = currentStatus === 'archived' ? 'active' : 'archived';
    Inertia.post('/recruitment/archive', { recruitmentId: recruitment_id, status: newStatus }, {
      onSuccess: () => {
        // console.log(`Recruitment ${recruitment_id} status updated to ${newStatus}`);
      },
      onError: (error) => {
        console.error('Error:', error);
      },
    });
  };

const handleSearch = () => {
  if (query.trim() === "") {
    setFilteredItems([]);
    setShowSearchList(false);
    return;
  }
  setLoading(true);
  setShowSearchList(true);
 
  // Initialize the final results array
  const results = [];
 
  // Loop through each stage
  stages.forEach((stage) => {
    const stageName = stage.stagecolumnID;
    const stagePipeline = recruitment_pipeline[stageName];
   
    // Check if the stage exists and has candidates
    if (Array.isArray(stagePipeline) && stagePipeline.length > 0) {    
      const filteredCandidates = stagePipeline.filter((candidate) => {
        const fullName = `${candidate.candidate_details.first_name} ${candidate.candidate_details.last_name}`.toLowerCase();
        const skills = candidate.candidate_details.skills?.toLowerCase() || "";
        const location = candidate.candidate_details.location?.toLowerCase() || "";
        const searchQuery = query.toLowerCase();
     
        return (
          fullName.includes(searchQuery) ||
          skills.includes(searchQuery) ||
          location.includes(searchQuery)
        );
      });
     
      // Add filtered candidates to the results array
      if (filteredCandidates.length > 0) {
        results.push({
          stage: stageName,
          candidates: filteredCandidates,
        });
      }
    }
  });
 
  setFilteredItems(results);
  setLoading(false);
};
 

  const addNewStageColumn = ({ newStageColumnName, afterStageColumnId }) => {
    const newStage = {
      stagecolumnID: Date.now(),
      stagenumber: Date.now(),
      stage_name: newStageColumnName,
	  totalCards: 0
    };

    const afterStageIndex = stages.findIndex(stage => stage.stagecolumnID === afterStageColumnId);

    if (afterStageIndex !== -1) {
      const updatedStageColumns = [
        ...stages.slice(0, afterStageIndex + 1),
        newStage,
        ...stages.slice(afterStageIndex + 1),
      ];

      setStages(updatedStageColumns);
	  // console.log(updatedStageColumns);
	Inertia.post('/recruitment/create_new_stage', { updatedStageColumns, recruitment_id }, {
      onSuccess: (page) => {
        const refreshedStages = page.props.recruitment_stage;
        setStages(refreshedStages); 
        updatedStageColumns.length = 0;
        refreshedStages.forEach(stage => updatedStageColumns.push(stage)); // Populate with new data
        // console.log('Updated stages from server:', refreshedStages);
      },
      onError: (error) => {
        console.log('Error:', error);
      },
    });
	

    } else {
      console.error('Stage column ID not found!');
    }
	

  };
  
  
const closeModal = () => {
  setShowCandidateModal(false);
  setSelectedCandidate(null);
  setSelectedStage(null);
}; 
  
const handleInputChange = (e) => {
  setQuery(e.target.value);
  handleSearch();
};

const handleItemClick = (item, stageName) => {
    setSelectedStage(stageName);
    setSelectedCandidate(item);
    setShowCandidateModal(true);
    setQuery(`${item.candidate_details.first_name} ${item.candidate_details.last_name}`);
    setShowSearchList(false);
}; 
 
useEffect(() => {
  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setShowSearchList(false);
    }
  };
  document.addEventListener("mousedown", handleClickOutside);
  return () => {
    document.removeEventListener("mousedown", handleClickOutside);
  };
}, []);




  return (
    <div className="interview-scheduler bg-white">
      <ToastNotification />
      <div className="customHeight">
        <div className="flex items-center px-4 py-4 justify-between border-b bg-white">
          <div className="flex gap-4">
            <a href="/recruitment">
              <button className="flex items-center space-x-1 px-2 py-2 font-medium w-20 text-black text-md justify-between">
                <img
                  className="h-5 w-5"
                  src={`${assetBaseUrl}images/lefbluearrow.svg`}
                  alt="Back"
                />
                <span className="text-md font-semibold text-cyan-500 text-lg ml-2">
                  Back
                </span>
              </button>
            </a>
            <h1 className="whitespace-nowrap text-3xl font-medium">
              {recruitment_name}
            </h1>
          </div>
          <div className="flex gap-4">
            <div className="relative" ref={dropdownRef}>
				  <div className="flex items-center border border-gray-300 rounded-md">
					<button
					  onClick={handleSearch}
					  className="text-black-100 px-4 py-2 rounded-r-md flex items-center justify-center"
					>
					  <i className="fas fa-search"></i>
					</button>
					<input
					  type="text"
					  placeholder="Search"
					  value={query}
					  onChange={handleInputChange}
					  className="focus:outline-none focus:border-mainBlue focus:ring-1 focus:ring-mainBlue w-full px-2 py-2"
					/>
				  </div>
			 
				  {showSearchList && (
					<ul
					  className={`absolute z-50 bg-white overflow-y-auto border border-gray-200 rounded-md shadow-lg mt-1 ${
						filteredItems.length > 5 ? "h-96" : "max-h-60"
					  }`}
					>
					  {loading && <li className="p-2 text-gray-500">Searching...</li>}
			 
					  {!loading && filteredItems.length === 0 && query !== "" && (
						<li className="p-2 text-gray-500">No results found.</li>
					  )}
			 
					  {!loading &&
						filteredItems.map((item) =>
						  item.candidates.map((candidate) => (
							<CandidateListItem
							  key={candidate.id}
							  candidate={candidate}
							  handleClick={handleItemClick}
							  stageName={item.stage}
							/>
						  ))
						)
						}
					</ul>
				  )}
				  {showCandidateModal && selectedCandidate && (
					<CandidateDetailsModal
					  assetBaseUrl={assetBaseUrl}
					  showModal={showCandidateModal}
					  closeModal={closeModal}
					  id={selectedCandidate.id}
					  candidateName={`${selectedCandidate.candidate_details.first_name} ${selectedCandidate.candidate_details.last_name}`}
					  candidateDetails={selectedCandidate.candidate_details}
					  newStageName={selectedStage}
					  interviewerList={interviewerList}
					  recruitment_id={selectedCandidate.recruitmentProjectId}
					  Pipelinedata={selectedCandidate.recruitmentPipelinedata}
					  notesdata={selectedCandidate.notesdata}
					  groupedPeoplesCareer={selectedCandidate.groupedPeoplesCareer}
					  user_role={user_role}
					  userlist={userlist}
					  recruitment_name={recruitment_name}
					/>
					
				  )}
				</div>
            {user_role !== 'Viewer' && (
              <>
                <button
                  onClick={() => setShowShareModal(true)}
                  className="transition flex gap-2 shadow-md hover:shadow-lg items-center border text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100"
                >
                  <img className="h-5 w-5" src={`${assetBaseUrl}images/ShareNetwork.svg`} alt="Share Icon" />
                  <span>Share</span>
                </button>
                <SharePopup
                  assetBaseUrl={assetBaseUrl}
                  showModal={showShareModal}
                  closeModal={() => setShowShareModal(false)}
                  editorUsersList={editorUsersList}
                  viewerUsersList={viewerUsersList}
                  recruitment_id={recruitment_id}
                  viewer_shared_with={viewer_shared_with}
                  editor_shared_with={editor_shared_with}
                />
                <button className="flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 font-medium w-32 text-black text-md justify-center" onClick={() => handleArchive(recruitment_id, recruitment_status)}>
                  <img
                    className="h-5 w-5"
                    src={`${assetBaseUrl}images/archive.svg`} alt="Archive"
                  />
                  <span className="font-semibold text-sm"> {recruitment_status === 'archived' ? 'Restore' : 'Archive'}</span>
                </button>
               {!isAllEmpty &&  <a href={`/plan/${uniquePlanIds}`}>
                  <button className="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium w-32 text-white text-md justify-center">
                    <img
                      className="h-5 w-5"
                      src={`${assetBaseUrl}images/plus-white-without-circle.svg`}
                      alt="More options"
                    />
                    <span className="text-md font-semibold">Pipeline</span>
                  </button>
			</a>}
			
              </>
            )}
          </div>
        </div>

        <div className="my-0 bg-white h-calc-80 overflow-y-scroll">
          <div className="h-full">
            <div className="h-full w-full flex">
              <DragDropContext onDragEnd={handleDragEnd}>
                <div className="flex h-full gap-2 p-4">
                  {stages.map((stage, idx) => {
                    const colors = [
                      "bg-gray-100",
                      "bg-blue-50",
                      "bg-amber-light",
                      "bg-custom-green-50",
                    ];

                    const color = colors[idx % colors.length];

                    return (
                      <Section
                        key={stage.stagecolumnID}
                        stagecolumnID={stage.stagecolumnID}
                        showCalender={idx === 0}
                        assetBaseUrl={assetBaseUrl}
                        title={stage.stage_name}
                        recruitment_id={recruitment_id}
                        totalCard={
                          recruitment_pipeline[stage.stagecolumnID]?.length || 0
                        }
                        bgcolor={color}
                        candidatesArray={recruitment_pipeline[stage.stagecolumnID] || []}
                        updateStageName={updateStageName}
                        interviewerList={interviewerList}
                        addNewStageColumn={addNewStageColumn} 
                        user_role={user_role} 
                        userlist={userlist} 
                        recruitment_name={recruitment_name} 
                      />
                    );
                  })}
                </div>
              </DragDropContext>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecruitmentSchedule;
