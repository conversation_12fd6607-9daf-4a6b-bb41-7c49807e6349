import React from 'react';
import { AppProvider } from '../OrganizationContext';
import { ChartProvider } from './ChartContext';
import { PeopleProvider } from './PeopleContext';

// Combined Provider that wraps all context providers
export const CombinedProvider = ({ children }) => {
  return (
    <AppProvider>
      <PeopleProvider>
        <ChartProvider>
          {children}
        </ChartProvider>
      </PeopleProvider>
    </AppProvider>
  );
};