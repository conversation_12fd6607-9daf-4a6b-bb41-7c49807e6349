import React, { createContext, useState, useContext } from 'react';
import { OrganizationContext } from '../OrganizationContext';

// Create Chart Context
export const ChartContext = createContext();

// Chart Provider for organization chart related state
export const ChartProvider = ({ children }) => {
  const { findUserinOrgchart, findParentId } = useContext(OrganizationContext);
  const [zoomLevel, setZoomLevel] = useState(90);
  const [activeListUser, setActiveListUser] = useState(null);
  const [activeOrgUser, setActiveOrgUser] = useState(null);
  const [OrgChartusers, SetOrgChartusers] = useState([]);

  // Zoom functions
  const zoomIn = () => setZoomLevel((prevZoom) => Math.min(prevZoom + 10, 200));
  const zoomOut = () => setZoomLevel((prevZoom) => Math.max(prevZoom - 10, 30));

  return (
    <ChartContext.Provider value={{
      zoomLevel,
      setZoomLevel,
      zoomIn,
      zoomOut,
      activeListUser,
      setActiveListUser,
      activeOrgUser,
      setActiveOrgUser,
      OrgChartusers,
      SetOrgChartusers,
      findUserinOrgchart,
      findParentId
    }}>
      {children}
    </ChartContext.Provider>
  );
};