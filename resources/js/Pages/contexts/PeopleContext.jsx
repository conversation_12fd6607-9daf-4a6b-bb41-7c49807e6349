import React, { createContext, useState, useCallback } from 'react';

// Create People Context
export const PeopleContext = createContext();

// People Provider for managing people/candidate data
export const PeopleProvider = ({ children }) => {
  const [selectedPeoplesForOrganisation, setSelectedPeoplesForOrganisation] = useState([]);
  const [selectedPeoples, setSelectedPeoples] = useState([]);
  const [users, setUsers] = useState([]);
  const [query, setQuery] = useState("");
  const [lastPage, setLastPage] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingPeople, setIsLoadingPeople] = useState(false);

  // Fetch paginated data
  const fetchPaginatedItems = useCallback(async (page = 1, searchQuery = "") => {
    try {
      setIsLoadingPeople(true);
      const response = await fetch(
        `/paginated-internal-peoples?page=${page}&query=${encodeURIComponent(searchQuery)}`
      );
      const data = await response.json();
      if (page === 1) {
        setUsers(data.data);
        setSelectedPeoples(data.data); // Initialize with first page items
      } else {
        setUsers((prev) => [...prev, ...data.data]);
        setSelectedPeoples((prev) => [...prev, ...data.data]); // Append next page items
      }
      setCurrentPage(data.current_page);
      setLastPage(data.last_page);
    } catch (error) {
      console.error("Error fetching paginated items:", error);
    } finally {
      setIsLoadingPeople(false);
    }
  }, []);

  // Search functionality
  const search = useCallback((inputQry) => {
    setQuery(inputQry.toLowerCase());
    fetchPaginatedItems(1, inputQry.toLowerCase());
  }, [fetchPaginatedItems]);

  // Toggle user selection
  const toggleSelection = useCallback((user) => {
    const exists = selectedPeoplesForOrganisation.find(
      (selectedUser) => selectedUser.id === user.id
    );

    if (exists) {
      setSelectedPeoplesForOrganisation((prev) =>
        prev.filter((selectedUser) => selectedUser.id !== user.id)
      );
    } else {
      setSelectedPeoplesForOrganisation((prev) => [...prev, user]);
    }
  }, [selectedPeoplesForOrganisation]);

  // Check if user is selected
  const isSelected = useCallback((userId) => {
    return selectedPeoplesForOrganisation.some((selectedUser) => selectedUser.id === userId);
  }, [selectedPeoplesForOrganisation]);

  return (
    <PeopleContext.Provider value={{
      selectedPeoplesForOrganisation,
      setSelectedPeoplesForOrganisation,
      selectedPeoples,
      setSelectedPeoples,
      users,
      setUsers,
      query,
      setQuery,
      lastPage,
      currentPage,
      isLoadingPeople,
      fetchPaginatedItems,
      search,
      toggleSelection,
      isSelected
    }}>
      {children}
    </PeopleContext.Provider>
  );
};