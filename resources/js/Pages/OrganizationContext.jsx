import React, { createContext, useState, useEffect } from "react";
import { Inertia } from "@inertiajs/inertia";

// Create Context
export const OrganizationContext = createContext();

// Create Provider
export const AppProvider = ({ children }) => {
  // Define all the shared state variables
  const [isVisible, setIsVisible] = useState(false);
  const [isFirstClick, setIsFirstClick] = useState(true);
  const [individualPopup, setIndividualPopup] = useState(false);
  const [showShareButton, setShowShareButton] = useState(false);
  const [clickToBeginPopup, setClickToBeginPopup] = useState(false);
  const [addOrganisationButton, setAddOrganisationButton] = useState(true);
  const [showOranisationListing, setShowOranisationListing] = useState(true);
  const [deleteOrganisationPeoplePopup, setDeleteOrganisationPeoplePopup] = useState(false);
  const [userId, setUserId] = useState(null);
  const [addSkillsPopup, setAddSkillsPopup] = useState(false);
  const [addCareerHistroyPopup, setAddCareerHistroyPopup] = useState(false);
  const [shareOrgansationPopup, setShareOrgansationPopup] = useState(false);
  const [competenciesPopup, setCompetenciesPopup] = useState(false);
  const [viewNotesPopup, setViewNotesPopup] = useState(false);
  const [nineboxGridPopup, setNineboxGridPopup] = useState(false);
  const [editingOrganisationId, setEditingOrganisationId] = useState(false);
  const [bulkuploadcandidate, setBulkUploadCandidatePopup] = useState(false);
  const [addedPeoplesInOrganisation, setAddedPeoplesInOrganisation] = useState([]);
  const [orgCompetencies, setOrgCompetencies] = useState([]);
  const [nineBoxGridArr, setNineBoxGridArr] = useState([]);
  const [organisationName, setOrganisationName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLeavePopup, setIsleavePopup] = useState(false);

  const viewOrganisation = (organisationId) => {
    setEditingOrganisationId(organisationId);
    setShowOranisationListing(false);
    setIsVisible(true);
    setAddOrganisationButton(false);
};

useEffect(() => {
  if (editingOrganisationId) {
      const fetchOrganisationDetails = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(`/organisation-details/${editingOrganisationId}`);
            
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Check if response has error
            if (data.error) {
              throw new Error(data.message || 'Failed to fetch organisation details');
            }
            
            setOrganisationName(data.organisation_title || '');

            // Only update state if data actually changed to prevent unnecessary re-renders
            if (data.organisation_people && Array.isArray(data.organisation_people)) {
              if (JSON.stringify(data.organisation_people) !== JSON.stringify(addedPeoplesInOrganisation)) {
                setAddedPeoplesInOrganisation([...data.organisation_people]);
              }
            } else {
              console.warn('Organisation people data is not an array:', data.organisation_people);
              setAddedPeoplesInOrganisation([]);
            }
            
            if (data.orgCompetencies && Array.isArray(data.orgCompetencies)) {
              if (JSON.stringify(data.orgCompetencies) !== JSON.stringify(orgCompetencies)) {
                setOrgCompetencies(data.orgCompetencies);
              }
            } else {
              setOrgCompetencies([]);
            }
        } catch (error) {
            console.error("Error fetching organisation details:", error);
            // Reset state on error
            setOrganisationName('');
            setAddedPeoplesInOrganisation([]);
            setOrgCompetencies([]);
        } finally {
          setIsLoading(false); // Hide the spinner
        }
      };

      fetchOrganisationDetails();
  }
}, [editingOrganisationId]);

  const findUserinOrgchart = (users, candidate_id) => {
    if (!Array.isArray(users) || users.length === 0) return null;

    for (const user of users) {
        // Direct match
        if (user.id === candidate_id) {
            return user;
        }
        // Recursively search in descendants
        if (Array.isArray(user.descendants) && user.descendants.length > 0) {
            const found = findUserinOrgchart(user.descendants, candidate_id);
            if (found) return found; // Early return on match
        }
    }
    return null; // Return null if not found
  };

  function findParentId(nodeList, targetId, parentId = null) {
    for (const node of nodeList) {
        if (node.id === targetId) {
            return parentId; // Return the parent ID when the target is found
        }
        if (node.descendants.length > 0) {
            const foundParentId = findParentId(node.descendants, targetId, node.id);
            if (foundParentId !== null) {
                return foundParentId; // Return parent ID if found in descendants
            }
        }
    }
    return null; // Return null if not found
}

const saveOrgansationCallBack = async () => {
  setIsLoading(true);
  try {
       Inertia.post('/my-organization/save', {
          addedPeoplesInOrganisation,
          organisationName,
          orgCompetencies,
          editingOrganisationId
      }, {
          onSuccess: () => {
              // setTimeout(() => {
              //     window.location.reload();
              // }, 2000);
              console.log('Organization saved successfully');
          }
      });
  } catch (error) {
      console.error('Error:', error);
  } finally {
      setIsLoading(false);
  }
};

  // Context value to be shared across components
  const contextValue = {
    isVisible,
    setIsVisible,
    isFirstClick,
    setIsFirstClick,
    individualPopup,
    setIndividualPopup,
    showShareButton,
    setShowShareButton,
    clickToBeginPopup,
    setClickToBeginPopup,
    addOrganisationButton,
    setAddOrganisationButton,
    showOranisationListing,
    setShowOranisationListing,
    deleteOrganisationPeoplePopup,
    setDeleteOrganisationPeoplePopup,
    userId,
    setUserId,
    addSkillsPopup,
    setAddSkillsPopup,
    addCareerHistroyPopup,
    setAddCareerHistroyPopup,
    shareOrgansationPopup,
    setShareOrgansationPopup,
    competenciesPopup,
    setCompetenciesPopup,
    viewNotesPopup,
    setViewNotesPopup,
    nineboxGridPopup,
    setNineboxGridPopup,
    editingOrganisationId,
    setEditingOrganisationId,
    bulkuploadcandidate,
    setBulkUploadCandidatePopup,
    addedPeoplesInOrganisation,
    setAddedPeoplesInOrganisation,
    orgCompetencies,
    setOrgCompetencies,
    nineBoxGridArr,
    setNineBoxGridArr,
    organisationName,
    setOrganisationName,
    viewOrganisation,
    isLoading,
    setIsLoading,
    findUserinOrgchart,
    findParentId,
    isLeavePopup,
    setIsleavePopup,
    saveOrgansationCallBack,
  };

  return <OrganizationContext.Provider value={contextValue}>{children}</OrganizationContext.Provider>;
};
