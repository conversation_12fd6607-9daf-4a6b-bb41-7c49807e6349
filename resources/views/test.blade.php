<!DOCTYPE html>
<html>
<head>
    <!-- ... other meta tags, stylesheets, etc. ... -->
</head>
<body>
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold mb-4">ChatGPT Clone</h1>

        <div>
            @if(isset($finalAnswer))
                <div class="bg-green-200 p-4 rounded-lg">
                    <p>{!! $finalAnswer !!}</p>
                </div>
                <h2 class="text-xl font-bold mt-4">SQL Query Result:</h2>
                @if(isset($sqlResult))
                    <table class="table-auto w-full mt-4">
                        <thead>
                            <tr>
                                @foreach($sqlResult[0] as $key => $value)
                                    <th class="border px-4 py-2">{{ $key }}</th>
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($sqlResult as $row)
                                <tr>
                                    @foreach($row as $value)
                                        <td class="border px-4 py-2">{{ $value }}</td>
                                    @endforeach
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @endif
            @endif
        </div>

        <form class="p-4 flex space-x-4 justify-center items-center" action="/test" method="post">
            @csrf
            <label for="message">Laravel Question:</label>
            <input id="message" type="text" name="message" autocomplete="off" class="border rounded-md p-2 flex-1" />
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Send
            </button>
        </form>
    </div>

    <!-- ... other scripts ... -->
</body>
</html>
