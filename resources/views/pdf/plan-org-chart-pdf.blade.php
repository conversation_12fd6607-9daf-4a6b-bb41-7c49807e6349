<!DOCTYPE html>
<html>
<head>
    <title>Plan PDF</title>
    <style>
        body {
            margin: 0;
            line-height: inherit;
            font-family: sans-serif; 
        }

        .my-org {
                background-image: radial-gradient(circle, #e0e0e0 2px, transparent 2px);
                background-size: 20px 20px; 
            }

            .tree {
    /* min-width: 100%; */
    white-space: nowrap;
    margin: 0;
    padding: 0;
}

.tree ul {
    padding-top: 20px;
    position: relative;
    transition: all 0.5s;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    height: 100%;
}

.tree li {
    text-align: center;
    list-style-type: none;
    position: relative;
    padding: 20px 5px 0 5px;
    transition: all 0.5s;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    /*added for long names*/

    float: none;
    display: inline-block;
    vertical-align: top;
    white-space: nowrap;
    margin: 0 -2px 0 -2px;
    
}

.fromleft > li:first-child {
    left: 7.8rem;
}

.fromright li:not(:first-child) {
    right: 7.8rem;
}

/*We will use ::before and ::after to draw the connectors*/

.tree li::before,
.tree li::after {
    content: '';
    position: absolute;
    top: 0;
    right: 50%;
    border-top: 2px solid #06B6D4;
    width: 50%;
    height: 35px;
}

.tree li::after {
    right: auto;
    left: 50%;
    border-left: 2px solid #06B6D4;
}

/*We need to remove left-right connectors from elements without 
any siblings*/

.tree li:only-child::after,
.tree li:only-child::before {
    display: none;
}

/*Remove space from the top of single children*/

.tree li:only-child {
    padding-top: 0;
}

/*Remove left connector from first child and 
right connector from last child*/

.tree li:first-child::before,
.tree li:last-child::after {
    border: 0 none;
}

/*Adding back the vertical connector to the last nodes*/

.tree li:last-child::before {
    border-right: 2px solid #06B6D4;
    border-radius: 0 5px 0 0;
    -webkit-border-radius: 0 5px 0 0;
    -moz-border-radius: 0 5px 0 0;
}

.tree li:first-child::after {
    border-radius: 5px 0 0 0;
    -webkit-border-radius: 5px 0 0 0;
    -moz-border-radius: 5px 0 0 0;
}

/*Time to add downward connectors from parents*/

.tree ul ul::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    border-left: 2px solid #06B6D4;
    width: 0;
    height: 35px;
}

.tree li a {
    /* border: 1px solid #ccc; */
    padding: 5px 10px;
    text-decoration: none;
    color: #666;
    font-family: arial, verdana, tahoma;
    font-size: 11px;
    display: inline-block;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    transition: all 0.5s;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
}

/*Time for some hover effects*/
/*We will apply the hover effect the the lineage of the element also*/

.tree li a:hover,
.tree li a:hover+ul li a {
    /* background: #c8e4f8;
    color: #000;
    border: 1px solid #94a0b4; */
}

/*Connector styles on hover*/

.tree li a:hover+ul li::after,
.tree li a:hover+ul li::before,
.tree li a:hover+ul::before,
.tree li a:hover+ul ul::before {
    /* border-color: #94a0b4; */
}

.husband {
    float: left;
}

.wife {
    margin-left: 10px;
}

.wife::before {
    /* pseudo CSS, will need to be modified */

    content: '';
    position: absolute;
    top: 0;
    right: 50%;
    border-top: 2px solid #06B6D4;
    width: 50%;
    height: 20px;
}
    </style>
</head>
<body>
    <div>
    <div x-data="{ show: {} }" class="my-org">
        <div class="flex flexRow flex-col lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
            <div class="flex gap-4 items-center">
                <a href="{{ route('plan.show', ['plan' => $plan->id]) }}" class="flex gap-2 mainBlue items-center">
                    <img class="" src="{{ asset('images/ArrowLeftBlue.svg') }}">
                    <span>Back to Plan</span>
                </a>
                <h1 class="whitespace-nowrap font-medium">{{$plan->name}} Org Chart</h1>
            </div>
            <div class="ml-auto">
                <a href="{{ route('download.pdf', $plan->id) }}" class="flex items-center border border-gray-300 rounded-lg px-2 py-2 gap-2 hover:bg-gray-100 transition ml-auto">
                    <img src="{{ asset('images/DownloadSimple.png') }}" alt="Download" class="w-5 h-5" />
                    <span class="text-gray-700 font-medium">Download</span>
                </a>
            </div>
        </div>
        <div class="customHeight tree overflow-y-scroll flex justify-center items-center" x-bind:style="'transform: scale(' + (zoomLevel / 100) + ')'">
            <ul>
                <li>
                    <a href="#">
                        <div wire:key="randomKey" class="w-56 HRInfo rounded-lg shadow-xl bg-white relative marginClass">
                            <div class="flex gap-10 items-center borderTop">
                                <!-- Display parent information -->
                                <div class="flex gap-2 items-center ml-3">
                                    <img class="block cursor-pointer" src="{{ asset('images/CheckCircle.png') }}" alt="Delete Person">
                                    <h2 class="font-semibold text-sm overflow-hidden whitespace-nowrap w-32 text-ellipsis"
                                        title="{{$plan->latest_role}}">
                                        {{$plan->latest_role}}
                                    </h2>
                                </div>
                                <!-- Dropdown menu for parent actions -->
                                <div class="dropdown-container">
                                    <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown"
                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                        type="button">
                                        <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                    </button>
                                    <!-- Dropdown menu options for parent -->
                                    <div id="dropdown2"
                                        class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                        <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                            <div class="cursor-pointer li">
                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                    <span class="font-semibold text-sm">View Profile</span>
                                                </div>
                                            </div>
                                            <div class="cursor-pointer li" @click="confirmRemovePerson({{$plan->tagged_individual}}, '{{ htmlentities($plan->forename) }} {{ htmlentities($plan->surname) }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                    <span class="font-semibold textRed text-sm">Delete</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Parent node border -->
                            <div class="border"></div>

                            <!-- Display parent name and ID -->
                            <div class="p-2 ml-3 flex flex-col text-left">
                                <span class="text-sm overflow-hidden whitespace-nowrap w-48 text-ellipsis" title="Leslie Alexander">
                                    {{$plan->forename}} {{$plan->surname}}
                                </span>
                                <span class="text-xs text-gray-500">Company Name: {{$plan->company_name}}</span>
                            </div>

                            <!-- Drag icon for parent -->
                            <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">
                        </div>
                    </a>
                    <ul>
                        <li>
                            @if (!empty($successPeople))
                                @foreach ($successPeople as $externalP)
                                    @if ($externalP->type === 'External')
                                    <a href="#">
                                        <div wire:key="randomKey" class="w-56 HRInfo rounded-lg shadow-xl bg-white relative marginClass">
                                            <div class="flex gap-10 items-center borderTop">
                                                <!-- Display parent information -->
                                                <div class="flex gap-2 items-center ml-3">
                                                    <img class="block cursor-pointer" src="{{ asset('images/redCrossIcon.svg') }}" alt="Delete Person">
                                                    <h2 class="text-sm overflow-hidden whitespace-nowrap w-32 text-ellipsis"
                                                        title="{{$externalP->latest_role}}">
                                                        {{$externalP->latest_role}}
                                                    </h2>
                                                </div>
                                                <!-- Dropdown menu for parent actions -->
                                                <div class="dropdown-container">
                                                    <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown"
                                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                        type="button">
                                                        <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                                    </button>
                                                    <!-- Dropdown menu options for parent -->
                                                    <div id="dropdown2"
                                                        class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                                        <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                                            <div class="cursor-pointer li">
                                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                    <span class="font-semibold text-sm">View Profile</span>
                                                                </div>
                                                            </div>
                                                            <div class="cursor-pointer li"  @click="confirmRemovePerson({{ $externalP->id }}, '{{ htmlentities($externalP->first_name) }} {{ htmlentities($externalP->last_name) }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                    <span class="font-semibold textRed text-sm">Delete</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Parent node border -->
                                            <div class="border"></div>

                                            <!-- Display parent name and ID -->
                                            <div class="p-2 ml-3 flex flex-col text-left">
                                                <span class="text-sm overflow-hidden whitespace-nowrap w-48 text-ellipsis" title="{{$externalP->first_name}} {{$externalP->last_name}}">
                                                    {{$externalP->first_name}} {{$externalP->last_name}}
                                                </span>
                                                <span class="text-xs text-gray-500 overflow-hidden">Company Name: {{$externalP->company_name}}</span>
                                            </div>

                                            <!-- Drag icon for parent -->
                                            <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">

                                        </div>
                                    </a>
                                    @endif                                    
                                @endforeach
                            @endif
                        </li>
                        <li>
                            @if (!empty($successPeople))
                                @foreach ($successPeople as $internalP)
                                    @if($internalP->type === 'Internal')
                                    <a href="#">
                                        <div wire:key="randomKey" class="w-56 HRInfo-red rounded-lg shadow-xl bg-white relative marginClass">

                                            <div class="flex gap-10 items-center borderTop-red">
                                                <!-- Display parent information -->
                                                <div class="flex gap-2 items-center ml-3">
                                                    <img class="block cursor-pointer" src="{{ asset('images/redCrossIcon.svg') }}" alt="Delete Person">
                                                    <h2 class="text-sm overflow-hidden whitespace-nowrap w-32 text-ellipsis"
                                                        title="{{$internalP->latest_role}}">
                                                        {{$internalP->latest_role}}
                                                    </h2>
                                                </div>
                                                <!-- Dropdown menu for parent actions -->
                                                <div class="dropdown-container">
                                                    <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown"
                                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                        type="button">
                                                        <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                                    </button>
                                                    <!-- Dropdown menu options for parent -->
                                                    <div id="dropdown2"
                                                        class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                                        <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                                            <div class="cursor-pointer li">
                                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                    <span class="font-semibold text-sm">View Profile</span>
                                                                </div>
                                                            </div>
                                                            <div class="cursor-pointer li" @click="confirmRemovePerson({{ $internalP->id }}, '{{ htmlentities($internalP->first_name) }} {{ htmlentities($internalP->last_name) }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                    <span class="font-semibold textRed text-sm">Delete</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Parent node border -->
                                            <div class="border"></div>

                                            <!-- Display parent name and ID -->
                                            <div class="p-2 ml-3 flex flex-col text-left">
                                                <span class="text-sm overflow-hidden whitespace-nowrap w-48 text-ellipsis" title="{{$internalP->first_name}} {{$internalP->last_name}}">
                                                    {{$internalP->first_name}} {{$internalP->last_name}}
                                                </span>
                                                <span class="text-xs text-gray-500 overflow-hidden">Company Name: {{$internalP->company_name}}</span>
                                            </div>

                                            <!-- Drag icon for parent -->
                                            <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">

                                        </div>
                                    </a>
                                    @endif
                                @endforeach
                            @endif
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
    </div>
</body>
</html>
