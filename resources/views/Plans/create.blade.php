@extends('layouts.setupapp')

@section('content')
<div class="flex items-center justify-center">
    <div class="bg-white mt-2 p-4 rounded-md w-3/6 border-solid border-2 border-grey-200 shadow-md">
        <form method="POST" action="{{ route('plan.store') }}">
            @csrf
            <h1 class="text-2xl font-bold leading-7">New Plan Form</h1>
            <p class="mt-1 text-sm leading-6">Fill out the form below to create a new plan.</p>
            <div class= "mt-3 grid grid-cols-2 grid gap-2">
                <div class="mt-2">
                    <label for="planName" class="block text-sm font-medium leading-6 text-gray-900">Plan Name</label>
                    <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input type="text" name="title" id="title" autocomplete="name" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" placeholder="Department-Role Name-Date-Plan" required>
                    </div>
                    @error('name')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                <div class="mt-2">
                    <label for="planName" class="block text-sm font-medium leading-6 text-gray-900">What role(s) are you planning for?</label>
                    <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input type="text" name="roles" id="roles" autocomplete="roles" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" placeholder="Head of Risk" required>
                    </div>
                    @error('roles')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                <div class="mt-2">
                    <label for="planName" class="block text-sm font-medium leading-6 text-gray-900">Enter any Locations you are interested in</label>
                    <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input type="text" name="location" id="location" autocomplete="location" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" placeholder="Country-City" required>
                    </div>
                    @error('roles')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                <div class="mt-2">
                    <label for="planName" class="block text-sm font-medium leading-6 text-gray-900">Enter any Genders you are targetting for the plan</label>
                    <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input type="text" name="gender" id="gender" autocomplete="gender" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" placeholder="Male or Female or NA or All" required>
                    </div>
                    @error('gender')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                <div class="mt-2">
                    <label for="planName" class="block text-sm font-medium leading-6 text-gray-900">What is the least amount of experience you need (years)?</label>
                    <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input type="number" name="minimum_Experience" id="minimum_Experience" autocomplete="minimum_Experience" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" placeholder="10" required>
                    </div>
                    @error('minimum_Experience')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                <div class="mt-2">
                    <label for="planName" class="block text-sm font-medium leading-6 text-gray-900">Are there any qualifications you are looking for?</label>
                    <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input type="text" name="education" id="education" autocomplete="gender" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" placeholder="CFA-Level 3">
                    </div>
                </div>
                <div class="mt-5 col-span-full">
                    <label for="about" class="block text-sm font-medium leading-6 text-gray-900">Description</label>
                    <div class="mt-2">
                        <textarea id="description" name="description" rows="3" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" required></textarea>
                    </div>
                    @error('description')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="mt-3 text-sm leading-6 text-gray-600">Write a few sentences about the plan</p>
                </div>
            </div>
            <div class="mt-5 border-t-2 border-double border-gray-400">
                <h2 class="mt-3 text-base font-semibold leading-7 text-gray-900">Additional Details</h2>
                <div class="mt-3 space-y-10">
                    <fieldset>
                        <legend class="text-sm font-semibold leading-6 text-gray-900">Would you consider step-up candidates?</legend>
                        <p class="mt-1 text-sm leading-6 text-gray-600">Having step-up candidates can improve the sustainability of your Succession Plan</p>
                        <div class="mt-6 space-y-6">
                            <div class="flex items-center gap-x-3">
                                <input id="push-everything" name="step_up" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600" value="1">
                                <label for="push-everything" class="block text-sm font-medium leading-6 text-gray-900">Yes</label>
                            </div>
                            <div class="flex items-center gap-x-3">
                                <input id="push-email" name="step_up" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600" value="0">
                                <label for="push-email" class="block text-sm font-medium leading-6 text-gray-900">No</label>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="mt-5 space-y-10">
                    <fieldset>
                        <legend class="text-sm font-semibold leading-6 text-gray-900">Is Ethnicity important?</legend>
                        <p class="mt-1 text-sm leading-6 text-gray-600">Please note profiling through ethnicity has a low accuracy only select if this is absolutely neccessary</p>
                        <div class="mt-6 space-y-6">
                            <div class="flex items-center gap-x-3">
                                <input id="push-yes" name="ethnicity" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600" value="1">
                                <label for="push-yes" class="block text-sm font-medium leading-6 text-gray-900">Yes</label>
                            </div>
                            <div class="flex items-center gap-x-3">
                                <input id="push-no" name="ethnicity" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600" value="0">
                                <label for="push-no" class="block text-sm font-medium leading-6 text-gray-900">No</label>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="mt-2 flex items-center justify-end gap-x-6">
                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                    <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection