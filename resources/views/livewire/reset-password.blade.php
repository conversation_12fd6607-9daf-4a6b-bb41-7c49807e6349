<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    @include('livewire.flashMessage')

    <div class="w-full flex">
        <div class="fixed inset-0 flex items-center justify-center  z-40">
            <!-- Modal background with a higher z-index -->
            <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

            <!-- Modal content with a lower z-index -->

            <div
                class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">

                <div class="flex flex-col justify-center items-center">

                    <img class="w-20 h-20" src="{{ asset('images/LogoSmall.png') }}">
                    <h2 class="mt-4 text-center text-2xl font-bold text-gray-900">Reset your password </h2>
                    <p class="text-sm mt-4 grayText">Enter the new password for successionplan account</p>
                </div>

                <form wire:submit.prevent="resetPassword" class="mt-4 px-4 space-y-4">

                    <div>
                        <label for="password" class="text-xs font-medium labelcolor">Password</label>
                        <input wire:model="password" id="password" name="password" type="password"
                            class="mt-1 placeholder:text-gray-400 labelcolor bg-white  @error('password') is-invalid @enderror  placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                            placeholder="New Password">
                        <i class="fa-regular showImg fa-eye eyeIcon" data-target="password"></i>
                        @error('password')
                            <span class="redColor text-sm">{{ $message }}</span>
                        @enderror
                    </div>
                    <div>
                        <label for="password_confirmation" class="text-xs font-medium labelcolor">Password</label>
                        <input wire:model="password_confirmation" id="password_confirmation" name="password_confirmation"
                            type="password"
                            class="mt-1 placeholder:text-gray-400 labelcolor bg-white  @error('password_confirmation') is-invalid @enderror  placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                            placeholder="Confirm Password">
                        <i class="fa-regular showImg fa-eye eyeIcon" data-target="confirmPassword"></i>
                        @error('password_confirmation')
                            <span class="redColor text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="mt-2">
                        <button type="submit"
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white mainblueBG  ">
                            Save new password
                        </button>
                    </div>
                    <div wire:click="backToLogin" class="my-5 flex justify-center">
                        <button type="button" class="text-sm text-center mainblue">Back to Log In</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @include('livewire.loading')
</div>

<script>
    $(document).ready(function() {
        $('.showImg').click(function() {
            var targetInputId = $(this).data('target');
            var inputField = $('#' + targetInputId);
            var inputType = inputField.attr('type');

            if (inputType === 'password') {
                inputField.attr('type', 'text');
                $(this).removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                inputField.attr('type', 'password');
                $(this).removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });
    });
</script>
