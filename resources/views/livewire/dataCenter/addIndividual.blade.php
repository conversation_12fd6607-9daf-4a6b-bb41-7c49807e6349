<div x-data="{
    step: @entangle('step'),
    forename: @entangle('forename'), 
    surname: @entangle('surname'),
    country: @entangle('country'), 
    city: @entangle('city'),
    peopleSummary: @entangle('peopleSummary'), 
    latest_role: @entangle('latest_role'), 
    company: @entangle('company'),
    seniority: @entangle('seniority'), 
    exco: @entangle('exco'),
    start_date: @entangle('start_date'), 
    end_date: @entangle('end_date'),
    registration_status: @entangle('registration_status'), 
    linkedinURL: @entangle('linkedinURL'),
    skills: @entangle('skills'), 
    languages: @entangle('languages'),
    selectedSectorsArr: [],
    selectedIndustriesArr: [],
    errors: {},
    maxWords: 200,
    constraints: {
        forename: {
            presence: { allowEmpty: false, message: '^Forename is required.' }
        },
        surname: {
            presence: { allowEmpty: false, message: '^Surname is required.' }
        },
        country: {
            presence: { allowEmpty: false, message: '^Country is required.' }
        },
        city: {
            presence: { allowEmpty: false, message: '^City is required.' }
        }
    },
    step2constraints: {
        latest_role: {
            presence: { allowEmpty: false, message: '^Latest role is required.' }
        },
        company: {
            presence: { allowEmpty: false, message: '^Company is required.' }
        },
        seniority: {
            presence: { allowEmpty: false, message: '^Seniority is required.' }
        },
        exco: {
            presence: { allowEmpty: false, message: '^Exco is required.' }
        },
        start_date: {
            presence: { allowEmpty: false, message: '^Start date is required.' }
        }
    },
    step3constraints: {
        registration_status: {
            presence: { allowEmpty: false, message: '^Registration Status role is required.' }
        },
        linkedinURL: {
            presence: { allowEmpty: false, message: '^linkedinURL is required.' }
        },
        skills: {
            presence: { allowEmpty: false, message: '^Skills is required.' }
        },
        languages: {
            presence: { allowEmpty: false, message: '^Languages is required.' }
        },
    },
    wordCount(text) {
        return text ? text.trim().split(/\s+/).length : 0;
    },
    get remainingWords() {
    console.log('here');
        const currentWordCount = this.wordCount(this.peopleSummary);
        return currentWordCount <= this.maxWords ? this.maxWords - currentWordCount : null;
    },

    get wordLimitError() {
        const currentWordCount = this.wordCount(this.peopleSummary);
        console.log(currentWordCount);
        return this.wordCount(this.peopleSummary) > this.maxWords 
            ? `Summary should not exceed ${this.maxWords} words. (Current count: ${currentWordCount})` 
            : null;
    },

    closeModal() {
        this.open = false;
        this.$dispatch('modalClosed');
    },

    validateStep1() {
        let validationErrors = validate({ forename: this.forename, surname: this.surname,  country: this.country,  city: this.city }, this.constraints);

        if (this.wordLimitError) {
            validationErrors = validationErrors || {};
            validationErrors.peopleSummary = [this.wordLimitError];
        }

        this.errors = Object.fromEntries(
            Object.entries(validationErrors || {}).map(([field, messages]) => [field, messages[0]])
        );
        console.log(this.errors);

        if (!Object.keys(this.errors).length) {
            this.step = 2;
        }
    },
     validateStep2() {
        let validationErrors = validate({ latest_role: this.latest_role, company: this.company,  seniority: this.seniority,  exco: this.exco, start_date: this.start_date}, this.step2constraints);

        this.errors = Object.fromEntries(
            Object.entries(validationErrors || {}).map(([field, messages]) => [field, messages[0]])
        );
        console.log(this.errors);

        if (!Object.keys(this.errors).length) {
            this.step = 3;
        }
    },
    validateStep3() {
        let validationErrors = validate({ registration_status: this.registration_status, linkedinURL: this.linkedinURL,  skills: this.skills,  languages: this.languages}, this.step3constraints);

        this.errors = Object.fromEntries(
            Object.entries(validationErrors || {}).map(([field, messages]) => [field, messages[0]])
        );
        console.log(this.errors);

        if (!Object.keys(this.errors).length) {
            this.step = 4;
        }
    }
}">

    <div x-show="addInd" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-40" style="display:none">
        <!-- Modal background with a higher z-index -->
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
        <!-- Modal content with a lower z-index -->
        <div class="w-[40%] bg-white shadow-md relative rounded-lg border border-gray-300 p-4 z-50" x-show="addInd" x-transition>
            <!-- Modal content -->
            <img wire:click.prevent="closeModal('person')" @click="addInd = false" class="absolute right top-2 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Search Icon">

            <h2 class="font-semibold px-4">Add Individual</h2>
            <div class="flex justify-between mt-3 px-4">
                <button>
                    <span class="p-1 rounded-full px-2 text-xs" :class="step === 1 ? 'BlueBackground mainBlue' : (step > 1 ? 'GreenBackground mainGreen' : 'grayBackground text-gray-400')">1</span>
                    <span class="text-xs font-medium">Personal Details</span>
                </button>
                <button>
                    <span class="p-1 rounded-full px-2 text-xs" :class="step === 2 ? 'BlueBackground mainBlue' : (step > 2 ? 'GreenBackground mainGreen' : 'grayBackground text-gray-400')">2</span>
                    <span class="text-xs font-medium">Professional Details</span>
                </button>
                <button>
                    <span class="p-1 rounded-full px-2 text-xs" :class="step === 3 ? 'BlueBackground mainBlue' : (step > 3 ? 'GreenBackground mainGreen' : 'grayBackground text-gray-400')">3</span>
                    <span class="text-xs font-medium">Additional Details</span>
                </button>
                <button>
                    <span class="p-1 rounded-full px-2 text-xs" :class="step === 4 ? 'BlueBackground mainBlue' : 'grayBackground text-gray-400'">4</span>
                    <span class="text-xs font-medium">Career History</span>
                </button>
            </div>
            <div class="w-full border-t mt-3 border-gray-200"></div>
            <p class="whitespace-normal text-xs text-gray-600 px-4 py-2 text-center">Review the personal details provided below. If there are any mistakes please fill in the input form otherwise leave it blank</p>
            <template x-if="step == 1">
                <div class="h-full">
                    <div class="h-5/6 flex items-center">
                        <div class="w-full">
                            <div class="relative py-2 bg-white">
                                <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                            </div>
                            <div class="modalscroll px-4">
                                <div class="flex justify-between w-full gap-5 items-start mt-2">
                                    <div class="w-full">
                                        <label for="forename" class="block text-xs font-medium labelcolor">Forname <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                        <input wire:model="forename" type="text" id="forename" placeholder="Enter forename" name="forename" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                        <span x-show="errors.forename" class="text-red-500 text-xs" x-text="errors.forename"></span>

                                    </div>
                                    <div class="w-full">
                                        <label for="othername" class="block text-xs font-medium labelcolor">Other Name</label>
                                        <input wire:model="othername" type="text" id="othername" placeholder="Enter other name" name="othername" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                        @error('othername')
                                        <span class="text-red-500 text-xs">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="flex justify-between w-full gap-5 items-start mt-2">
                                    <div class="w-full">
                                        <label for="surname" class="block text-xs font-medium labelcolor">Surname <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                        <input wire:model="surname" type="text" id="surname" placeholder="Enter surname" name="surname" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                        <span x-show="errors.surname" class="text-red-500 text-xs" x-text="errors.surname"></span>

                                    </div>
                                    <div class="w-full">
                                        <label for="middlename" class="block text-xs font-medium labelcolor">Middle Name</label>
                                        <input wire:model="middlename" type="text" id="middlename" placeholder="Enter middle name" name="middlename" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                        @error('middlename')
                                        <span class="text-red-500 text-xs">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="flex justify-between w-full gap-5 items-start mt-2">
                                    <div class="w-full">
                                        <label for="country" class="block text-xs font-medium labelcolor">Country <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                        <input wire:model="country" type="text" id="country" placeholder="Enter country" name="country" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                        <span x-show="errors.country" class="text-red-500 text-xs" x-text="errors.country"></span>

                                    </div>
                                    <div class="w-full">
                                        <label for="city" class="block text-xs font-medium labelcolor">City <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                        <input wire:model="city" type="text" id="city" placeholder="Enter city" name="city" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                        <span x-show="errors.city" class="text-red-500 text-xs" x-text="errors.city"></span>

                                    </div>

                                </div>

                                <div class="flex justify-between w-full gap-5 items-start mt-2">
                                    <div class="w-full">
                                        <label for="city" class="block text-xs font-medium labelcolor">Summary</label>
                                        <textarea x-on:input="remainingWords" wire:model="peopleSummary" rows="4"
                                            class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                            placeholder="Enter summary"></textarea>
                                        <span x-show="wordLimitError" class="text-red-500 text-xs" x-text="wordLimitError"></span>
                                        <span x-show="errors.peopleSummary && !wordLimitError" class="text-red-500 text-xs" x-text="errors.peopleSummary"></span>
                                        <span x-show="errors.peopleSummary || wordLimitError"><br></span> <!-- Conditional line break -->
                                        <template x-if="!wordLimitError">
                                            <span class="text-gray-500 text-xs">Remaining words: <span x-text="remainingWords"></span></span>
                                        </template>
                                    </div>

                                </div>



                                <div class="py-4 flex w-full items-start justify-start gap-x-4">
                                    <div class="flex items-center gap-x-3">
                                        <input id="gender-male" type="radio" wire:model="gender" class="h-4 w-4 rounded-full border-2 checked:border border-gray-500 checked:bg-cyan-500" value="1">
                                        <label for="gender-male" class="block text-sm leading-6 text-gray-900">Male</label>
                                    </div>
                                    <div class="flex items-center gap-x-3">
                                        <input id="gender-female" type="radio" wire:model="gender" class="h-4 w-4 rounded-full border-2 checked:border border-gray-500 checked:bg-cyan-500" value="2">
                                        <label for="gender-female" class="block text-sm text-gray-900">Female</label>
                                    </div>
                                    <div class="flex items-center gap-x-3">
                                        <input id="gender-not-applicable" type="radio" wire:model="gender" class="h-4 w-4 rounded-full border-2 checked:border border-gray-500 checked:bg-cyan-500" value="3">
                                        <label for="gender-not-applicable" class="block text-sm text-gray-900">Not Applicable</label>
                                    </div>
                                </div>
                            </div>
                            <div class="w-full border-t mt-4 border-gray-200"></div>

                            <!-- buttons wrapper -->
                            <div class="flex gap-2 w-full px-4 mt-4">
                                <button wire:click.prevent="closeModal('person')" @click="addInd = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                <button @click="validateStep1()" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                    <span class="block"> Next</span>
                                    <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template x-if="step == 2">
                <div class="h-full">
                    <div class="h-5/6 flex items-center">
                        <div class="w-full">
                            <div class="flex justify-between w-full gap-5 items-start mt-2">
                                <div class="w-full">
                                    <label for="latest_role" class="block text-xs font-medium labelcolor">Current/Latest Role <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                    <input wire:model="latest_role" type="text" id="latest_role" placeholder="Enter latest role" name="latest_role" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    <span x-show="errors.latest_role" class="text-red-500 text-xs" x-text="errors.latest_role"></span>

                                </div>
                                <div x-data="currentCompanyHandler" class="w-full">
                                    <label for="company" class="block text-xs font-medium labelcolor">Company <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                    <select
                                        id="company-select"
                                        class="company-select outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                                    </select>
                                    <input type="hidden" id="hidden_company" wire:model="company">
                                    <span x-show="errors.company" class="text-red-500 text-xs" x-text="errors.company"></span>

                                </div>
                            </div>
                            <div class="flex justify-between w-full gap-5 items-start mt-2">
                                <div class="w-full">
                                    <label for="seniority" class="block text-xs font-medium labelcolor">Seniority <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                    <input wire:model="seniority" type="text" id="seniority" placeholder="Enter seniority" name="seniority" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    <span x-show="errors.seniority" class="text-red-500 text-xs" x-text="errors.seniority"></span>

                                </div>
                                <div class="w-full">
                                    <label for="exco" class="block text-xs font-medium labelcolor">Exco <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                    <input wire:model="exco" type="text" id="exco" placeholder="Enter exco" name="exco" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    <span x-show="errors.exco" class="text-red-500 text-xs" x-text="errors.exco"></span>

                                </div>
                            </div>

                            <div class="flex justify-between w-full gap-5 items-start mt-2">
                                <div class="w-full">
                                    <label for="start_date" class="block text-xs font-medium labelcolor">Start Date <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                    <input wire:model="start_date" type="date" id="start_date" placeholder="Enter start date" name="start_date" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    <span x-show="errors.start_date" class="text-red-500 text-xs" x-text="errors.start_date"></span>

                                </div>
                                <div class="w-full">
                                    <label for="end_date" class="block text-xs font-medium labelcolor">End Date</label>
                                    <input wire:model="end_date" type="date" id="end_date" placeholder="Enter end date" name="end_date" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    <span x-show="errors.end_date" class="text-red-500 text-xs" x-text="errors.end_date"></span>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button @click="step = 1" @click="step = 1" class="bg-white w-full text-black border p-2 rounded-md">Back</button>
                        <button @click="validateStep2()" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <span class="block"> Next</span>
                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                        </button>
                    </div>
                </div>
            </template>
            <template x-if="step == 3">
                <div class="h-full">
                    <div class="h-5/6 flex items-center">
                        <div class="w-full">
                            <div class="flex justify-between w-full gap-5 items-start mt-2">
                                <div class="w-full">
                                    <label for="registration_status" class="block text-xs font-medium labelcolor">Regisrations Status <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                    <input wire:model="registration_status" type="text" id="registration_status" placeholder="Enter registeration status" name="registration_status" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    <span x-show="errors.registration_status" class="text-red-500 text-xs" x-text="errors.registration_status"></span>

                                </div>
                                <div class="w-full">
                                    <label for="linkedinURL" class="block text-xs font-medium labelcolor">Linkedin URL <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                    <input wire:model="linkedinURL" type="text" id="linkedinURL" placeholder="Enter linkedin url" name="linkedinURL" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    <span x-show="errors.linkedinURL" class="text-red-500 text-xs" x-text="errors.linkedinURL"></span>
                                </div>
                            </div>

                            <div class="flex justify-between w-full gap-5 items-start mt-2">
                                <div class="w-full">
                                    <label for="skills" class="block text-xs font-medium labelcolor">Skills</label>
                                    <input wire:model="skills" type="text" id="skills" placeholder="Enter skills" name="skills" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    <span x-show="errors.skills" class="text-red-500 text-xs" x-text="errors.skills"></span>

                                </div>
                                <div class="w-full">
                                    <label for="languages" class="block text-xs font-medium labelcolor">Languages</label>
                                    <input wire:model="languages" type="text" id="languages" placeholder="Enter languages" name="languages" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    <span x-show="errors.languages" class="text-red-500 text-xs" x-text="errors.languages"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button @click="step=2" type="button" class="bg-white w-full text-black border p-2 rounded-md">Back</button>
                        <button @click="validateStep3()" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <span class="block"> Next</span>
                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                        </button>
                    </div>
                </div>
            </template>
            <template x-if="step == 4">

                <div x-data="experienceHandler()" class="h-full">
                    <div class="h-5/6 flex items-center">
                        <div class="w-full">
                            <div class="flex justify-center items-center gap-x-10">
                                <div class="flex justify-center items-center gap-x-5">
                                    <label class="text-xs text-gray-700">Add Experience</label>
                                    <button @click.prevent="addExperience">
                                        <img class="h-4 w-auto" src="{{ asset('images/plus.png') }}">
                                    </button>
                                </div>
                            </div>

                            <div class="flex-1 mt-3 w-full modalscroll">
                                <template x-for="(experience, index) in experiences" :key="index">
                                    <div class="mt-2">
                                        <div class="flex justify-between w-full gap-5 items-start">
                                            <div class="w-full">
                                                <label for="role" class="block text-xs font-medium labelcolor">Role <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                                <input
                                                    type="text"
                                                    :id="'role-' + index"
                                                    placeholder="Enter previous role"
                                                    x-model="experience.role"
                                                    class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                    required />
                                                <span x-show="errors[`role-${index}`]" class="text-red-500 text-xs mt-1" x-text="errors[`role-${index}`]"></span>
                                            </div>

                                            <div class="w-full">
                                                <label for="prevCompany" class="block text-xs font-medium labelcolor">Company <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                                <select
                                                    :id="'company-select-' + index"
                                                    x-ref="'companySelect-' + index"
                                                    x-model="experience.company"
                                                    class="company-select outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                                                </select>
                                                <span x-show="errors[`company-${index}`]" class="text-red-500 text-xs mt-1" x-text="errors[`company-${index}`]"></span>
                                            </div>
                                        </div>

                                        <div class="flex justify-between w-full gap-5 items-start mt-2">
                                            <div class="w-full">
                                                <label for="startDate" class="block text-xs font-medium labelcolor">Start Date <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                                <input
                                                    type="date"
                                                    :id="'startDate-' + index"
                                                    x-model="experience.start_date"
                                                    class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                    required />
                                                <span x-show="errors[`start_date-${index}`]" class="text-red-500 text-xs mt-1" x-text="errors[`start_date-${index}`]"></span>
                                            </div>

                                            <div class="w-full">
                                                <label for="endDate" class="block text-xs font-medium labelcolor">End Date <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                                <input
                                                    type="date"
                                                    :id="'endDate-' + index"
                                                    x-model="experience.end_date"
                                                    class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                    required />
                                                <span x-show="errors[`end_date-${index}`]" class="text-red-500 text-xs mt-1" x-text="errors[`end_date-${index}`]"></span>
                                            </div>
                                        </div>

                                        <div class="mt-2 mb-3">
                                            <button @click.prevent="removeExperience(index)">
                                                <img class="h-4 w-auto" src="{{ asset('images/minus.png') }}">
                                            </button>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                    </div>
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button @click="step=3" type="button" class="bg-white w-full text-black border p-2 rounded-md">Back</button>
                        <button type="button" @click="submitForm()" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/plus-white-without-circle.svg') }}">
                            <span class="block"> Individual</span>
                        </button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
    function currentCompanyHandler() {
        return {
            init() {
                this.initializeSelect2();

            },

            initializeSelect2() {

                const selectId = `#company-select`;
                $(selectId).select2({
                    placeholder: 'Select a company',
                    minimumInputLength: 1,
                    ajax: {
                        url: '{{ route("company.options") }}',
                        dataType: 'json',
                        delay: 250,
                        data: function(params) {
                            return {
                                q: params.term,
                                page: params.page || 1
                            };
                        },
                        processResults: function(data, params) {
                            params.page = params.page || 1;
                            return {
                                results: data.data,
                                pagination: {
                                    more: data.has_more
                                }
                            };
                        },
                        cache: true
                    }
                }).on('select2:select', (e) => {
                    const selectedId = e.target.value;
                    console.log(selectedId);
                    document.getElementById('hidden_company').value = selectedId;
                    document.getElementById('hidden_company').dispatchEvent(new Event('input'));
                });

            }
        }
    }

    function experienceHandler() {
        return {
            experiences: [],
            errors: {},

            addExperience() {
                const newExperience = {
                    role: '',
                    company: '',
                    start_date: '',
                    end_date: ''
                };
                this.experiences.push(newExperience);

                // Wait for the DOM to update and initialize Select2
                this.$nextTick(() => {
                    const index = this.experiences.length - 1;
                    this.initializeSelect2(index);
                });
            },

            removeExperience(index) {
                this.experiences.splice(index, 1);
            },

            initializeSelect2(index) {
                const selectId = `#company-select-${index}`;
                $(selectId).select2({
                    placeholder: 'Select a company',
                    minimumInputLength: 1,
                    ajax: {
                        url: '{{ route("company.options") }}',
                        dataType: 'json',
                        delay: 250,
                        data: function(params) {
                            return {
                                q: params.term,
                                page: params.page || 1
                            };
                        },
                        processResults: function(data, params) {
                            params.page = params.page || 1;
                            return {
                                results: data.data,
                                pagination: {
                                    more: data.has_more
                                }
                            };
                        },
                        cache: true
                    }
                }).on('select2:select', (e) => {
                    const selectedData = e.target.value;
                    this.experiences[index].company = selectedData;
                });
            },

            validateExperience() {
                this.errors = {}; // Reset errors
                this.experiences.forEach((experience, index) => {
                    if (!experience.role) {
                        this.errors[`role-${index}`] = 'Role is required.';
                    }
                    if (!experience.company) {
                        this.errors[`company-${index}`] = 'Company is required.';
                    }
                    if (!experience.start_date) {
                        this.errors[`start_date-${index}`] = 'Start date is required.';
                    }
                    if (!experience.end_date) {
                        this.errors[`end_date-${index}`] = 'End date is required.';
                    }
                });

                return Object.keys(this.errors).length === 0; // Return true if no errors
            },

            submitForm() {
                if (this.validateExperience()) {
                    console.log(JSON.stringify(this.previousExperience));
                    Livewire.dispatch('submitPerson', {
                        experience: JSON.stringify(this.experiences)
                    });
                    // Submit the form
                    console.log('Form is valid. Submitting...');
                } else {
                    console.log('Validation failed:', this.errors);
                }
            }
        };
    }
</script>