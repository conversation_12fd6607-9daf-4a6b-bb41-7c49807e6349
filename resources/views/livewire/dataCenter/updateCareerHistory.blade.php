<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<div x-show="editPeopleCareerHistoryPopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

    <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50 w-full max-w-5xl relative">
        <!-- cross icon -->
        <img x-on:click="editPeopleCareerHistoryPopup = false" class="absolute top-2 right-2 w-6 h-6 cursor-pointer"
            src="{{ asset('images/cross.svg') }}" alt="Close Icon">

        <h2 class="font-semibold px-4">Edit ({{$forename}} {{$surname}}) Career History</h2>
        <div class="w-full border-t mt-3 border-gray-200"></div>

        <!-- Scrollable content area with increased height -->
        <div class="overflow-y-auto max-h-96">
            @foreach($editPeopleCareerHistory as $index => $history)
            <div class="grid grid-cols-4 gap-4 mt-4 items-center">
                <div>
                    <label for="editRole" class="text-base font-medium labelcolor">Role</label>
                    <input wire:model.defer="editPeopleCareerHistory.{{$index}}.role" type="text" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md" placeholder="Role">
                </div>
                <div>
                    <label for="editCompany" class="text-base font-medium labelcolor">Company</label>

                    <input wire:model.defer="editPeopleCareerHistory.{{$index}}.company.name" type="text" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md" placeholder="Company">
                </div>
                <div>
                    <label for="editStartDate" class="text-base font-medium labelcolor">Start Date</label>
                    <input wire:model.defer="editPeopleCareerHistory.{{$index}}.start_date" type="date" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                </div>
                <div class="flex flex-wrap gap-4">
                    <div class="careerHistoryEndDate">
                        <label for="editEndDate" class="text-base font-medium labelcolor">End Date</label>
                        <input wire:model.defer="editPeopleCareerHistory.{{$index}}.end_date" type="date" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                    </div>
                    <div class="flex justify-start items-center" style="position: relative;top: 9px;">
                        <button wire:click="removeCareerHistoryRow({{ $index }})" type="button" classs="text-red">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
                <!-- Delete Button -->

            </div>
            @endforeach

            <!-- Add Button -->
            <div class="flex justify-start items-center mt-5 mb-3">
                <button wire:click="addCareerHistoryRow" type="button" class="text-green-600 py-3 px-4 rounded-lg bg-cyan-500 font-medium text-white hover:text-green-800">
                    <i class="fas fa-plus-circle"></i> Add New Entry
                </button>
            </div>
        </div>

        <div class="mt-4 flex justify-start">
            <button wire:click="updateCareerHistory" type="button" class="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                Update
            </button>
        </div>
    </div>
</div>