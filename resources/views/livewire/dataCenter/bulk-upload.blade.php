<div class="" x-data="{ uploadPopup: @entangle('uploadPopup'), duplicateUploadPopup: @entangle('duplicateUploadPopup') }">
    <!-- Trigger button inside the modal -->
        <button @click="uploadPopup = true" class="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium w-48 text-white text-md justify-center">
            <img class="h-6 font-bold w-auto img px-1" src="{{ asset('images/UploadWhite.png') }}" alt="Your Company">
            <span class="text-md font-semibold">Bulk Upload</span>
        </button>
    <!-- Modal container -->
    <div x-show="uploadPopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" style="display:none">
        <!-- Modal background with a higher z-index -->
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
        <!-- Modal content with a lower z-index -->
        <div class="modal-content2 bg-white relative shadow-md rounded-lg p-4 z-40">
            <!-- Modal content -->
            <p class="text-black font-semibold text-center">Bulk Upload</p>
            <img @click="uploadPopup = false" class="absolute bulkCross top-1 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="cross Icon">
            <div x-data="{ 
                tab: 'individual',
                step: @entangle('step'),
                changeStep(step) {
                    this.step = step;
                }
            }" class="flex h-full w-full justify-center items-center flex-col">
                <div class="flex border-b-2 w-full">
                    <button @click="tab = 'individual'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                'individual',
                                            'chart-heading font-semibold': tab != 'individual'
                                        }">
                        Individuals
                    </button>
                    <button @click="tab = 'company'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                'company',
                                            'chart-heading font-semibold': tab !=
                                                'company'
                                        }">
                        Companies
                    </button>
                </div>
                <form x-show="tab == 'company'" class="mt-3 w-full" wire:submit.prevent="uploadCompanyCSV">
                    @csrf
                    @error('companyCsvFile')
                    <div class="text-red-500 text-sm text-center">
                        <p>{{ $message }}</p>
                    </div>
                    @enderror

                    <div class="mt-1 h-full w-full">
                        <div class="col-span-2">
                            <label for="internal-candidate" class="block text-sm text-center leading-6 text-gray-900">Upload companies data into the platform</label>
                            <div class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                <div class="text-center">
                                    {{-- @if ($companyCsvFile) --}}

                                    {{-- @else --}}
                                    <div class="flex items-center justify-center">
                                        <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                    </div>
                                    <div class="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                                        <label for="file-upload-company" class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                                            <span>Upload a file</span>
                                            <input id="file-upload-company" wire:model="companyCsvFile" name="file-upload-company" type="file" class="sr-only" accept=".csv, .xlsx, .xls">

                                    </div>
                                    <p class="text-xs  text-gray-600">(CSV or Excel)</p>
                                    @if ($companyCsvFile && $companyCsvFile->getClientOriginalName())
                                    <p class="text-sm  text-gray-600">
                                        <span class="font-semibold">Selected File:</span>
                                        {{ $companyCsvFile->getClientOriginalName() }}
                                    </p>
                                    @endif
                                    {{-- @endif --}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-2 w-full px-4 mt-4">
                        <button @click="uploadPopup = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                        <button type="submit" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                            <span class="block"> Upload</span>
                        </button>
                    </div>
                    {{-- @include('livewire.jobPeople.duplicateUpload') --}}

                </form>
                <div x-show="tab == 'individual'" class="flex flex-col w-full h-full">
                    <h1 class="text-center text-xs text-gray-500 py-2">Make sure to complete all three steps to ensure data consistency in the platform</h1>
                    <div class="flex justify-between mt-3 px-4 w-full">
                    <button @click="changeStep(1)">
                            <!-- <span class="mainBlue p-1 rounded-full px-2 text-xs 
                                                @if ($step == 1) BlueBackground mainBlue
                                                @elseif($step > 1)
                                                GreenBackground mainGreen @endif">1</span> -->
                            <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs"
                                :class="{
                                    'BlueBackground': step === 1,
                                    'mainBlue': step === 1
                                }"
                            >1</span>
                            <span class="text-xs font-medium">People's Data</span>
                        </button>
                        <button @click="changeStep(2)" id="step2">
                            <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs"
                                    :class="{
                                        'BlueBackground': step === 2,
                                        'mainBlue': step === 2
                                    }"
                            >2</span>
                            <span class="text-xs font-medium">Skills Data</span>
                        </button>
                        <button @click="changeStep(3)" id="step3">
                            <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs"
                                    :class="{
                                        'BlueBackground': step === 3,
                                        'mainBlue': step === 3
                                    }"
                            >3</span>
                            <span class="text-xs font-medium">Career History Data</span>
                        </button>
                    </div>
                    <div class="w-full border-t mt-3 border-gray-200"></div>

                    <template x-if="step == 1">
                        <form class="mt-3 w-full" wire:submit.prevent="uploadPeopleCSV">
                            @csrf
                            @error('peopleCsvFile')
                                <div class="text-red-500 text-sm text-center">
                                    <p>{{ $message }}</p>
                                </div>
                            @enderror

                            <div class="mt-1 h-full w-full">
                                <div class="col-span-2">
                                    <label for="internal-candidate" class="block text-sm text-center leading-6 text-gray-900">Upload people data into the platform</label>
                                    <div class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                        <div class="text-center">
                                            {{-- @if ($peopleCsvFile) --}}

                                            {{-- @else --}}
                                            <div class="flex items-center justify-center">
                                                <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                            </div>
                                            <div class="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                                                <label for="file-upload-people" class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                                                    <span>Upload a file</span>
                                                    <input id="file-upload-people" wire:model="peopleCsvFile" name="file-upload-people" type="file" class="sr-only" accept=".csv, .xlsx, .xls">

                                            </div>
                                            <p class="text-xs  text-gray-600">(CSV or Excel)</p>
                                            @if ($peopleCsvFile && $peopleCsvFile->getClientOriginalName())
                                            <p class="text-sm  text-gray-600">
                                                <span class="font-semibold">Selected File:</span>
                                                {{ $peopleCsvFile->getClientOriginalName() }}
                                            </p>
                                            @endif
                                            {{-- @endif --}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex gap-2 w-full px-4 mt-4">
                                <button @click="uploadPopup = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                <button type="submit" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                    <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                                    <span class="block"> Upload</span>
                                </button>
                            </div>
                            {{-- @include('livewire.jobPeople.duplicateUpload') --}}

                        </form>
                    </template>

                    <template x-if="step == 2">
                        <form class="mt-3 w-full" wire:submit.prevent="uploadSkillsCSV">
                            @csrf
                            @error('skillsCsvFile')
                                <div class="text-red-500 text-sm text-center">
                                    <p>{{ $message }}</p>
                                </div>
                            @enderror

                            <div class="mt-1 h-full w-full">
                                <div class="col-span-2">
                                    <label for="internal-candidate" class="block text-sm text-center leading-6 text-gray-900">Upload skills data into the platform</label>
                                    <div class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                        <div class="text-center">
                                            {{-- @if ($skillsCsvFile) --}}

                                            {{-- @else --}}
                                            <div class="flex items-center justify-center">
                                                <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                            </div>
                                            <div class="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                                                <label for="file-upload-skills" class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                                                    <span>Upload a file</span>
                                                    <input id="file-upload-skills" wire:model="skillsCsvFile" name="file-upload-skills" type="file" class="sr-only" accept=".csv, .xlsx, .xls">

                                            </div>
                                            <p class="text-xs  text-gray-600">(CSV or Excel)</p>
                                            @if ($skillsCsvFile && $skillsCsvFile->getClientOriginalName())
                                            <p class="text-sm  text-gray-600">
                                                <span class="font-semibold">Selected File:</span>
                                                {{ $skillsCsvFile->getClientOriginalName() }}
                                            </p>
                                            @endif
                                            {{-- @endif --}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex gap-2 w-full px-4 mt-4">
                                <button @click="uploadPopup = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                <button type="submit" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                    <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                                    <span class="block"> Upload</span>
                                </button>
                            </div>
                            {{-- @include('livewire.jobPeople.duplicateUpload') --}}

                        </form>
                    </template>

                    <template x-if="step == 3">
                        <form class="mt-3 w-full" wire:submit.prevent="uploadCHCSV">
                            @csrf
                            @error('chCsvFile')
                                <div class="text-red-500 text-sm text-center">
                                    <p>{{ $message }}</p>
                                </div>
                            @enderror

                            <div class="mt-1 h-full w-full">
                                <div class="col-span-2">
                                    <label for="internal-candidate" class="block text-sm text-center leading-6 text-gray-900">Upload career history data into the platform</label>
                                    <div class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                        <div class="text-center">
                                            {{-- @if ($chCsvFile) --}}

                                            {{-- @else --}}
                                            <div class="flex items-center justify-center">
                                                <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                            </div>
                                            <div class="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                                                <label for="file-upload-ch" class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                                                    <span>Upload a file</span>
                                                    <input id="file-upload-ch" wire:model="chCsvFile" name="file-upload-ch" type="file" class="sr-only" accept=".csv, .xlsx, .xls">

                                            </div>
                                            <p class="text-xs  text-gray-600">(CSV or Excel)</p>
                                            @if ($chCsvFile && $chCsvFile->getClientOriginalName())
                                            <p class="text-sm  text-gray-600">
                                                <span class="font-semibold">Selected File:</span>
                                                {{ $chCsvFile->getClientOriginalName() }}
                                            </p>
                                            @endif
                                            {{-- @endif --}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex gap-2 w-full px-4 mt-4">
                                <button @click="uploadPopup = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                <button type="submit" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                    <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                                    <span class="block"> Upload</span>
                                </button>
                            </div>
                            {{-- @include('livewire.jobPeople.duplicateUpload') --}}

                        </form>
                    </template>


                </div>
            </div>
        </div>
    </div>

</div>