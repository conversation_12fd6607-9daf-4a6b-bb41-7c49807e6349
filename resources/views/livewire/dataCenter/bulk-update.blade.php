<div class="" x-data="{ updatePopup: @entangle('updatePopup'), duplicateUploadPopup: @entangle('duplicateUploadPopup') }">
    <!-- Trigger button inside the modal -->
        <button @click="updatePopup = true" class="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium w-48 text-white text-md justify-center">
            <img class="h-6 font-bold w-auto img px-1" src="{{ asset('images/WhitePencil.png') }}" alt="Your Company">
            <span class="text-md font-semibold">Bulk Update</span>
        </button>
    <!-- Modal container -->
    <div x-show="updatePopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" style="display:none">
        <!-- Modal background with a higher z-index -->
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
        <!-- Modal content with a lower z-index -->
        <div class="modal-content2 bg-white relative shadow-md rounded-lg p-4 z-40">
            <!-- Modal content -->
            <p class="text-black font-semibold text-center">Bulk Update</p>
            <img @click="updatePopup = false" class="absolute bulkCross top-1 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="cross Icon">
            <div x-data="{ tab: 'individual' }" class="flex h-full w-full justify-center items-center flex-col">
                <div class="flex border-b-2 w-full">
                    <button @click="tab = 'individual'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                'individual',
                                            'chart-heading font-semibold': tab != 'individual'
                                        }">
                        Individuals
                    </button>
                    <button @click="tab = 'company'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                'company',
                                            'chart-heading font-semibold': tab !=
                                                'company'
                                        }">
                        Companies
                    </button>
                </div>
                <form x-show="tab == 'company'" class="mt-3 w-full" wire:submit.prevent="uploadBulkUpdateCompaniesCSV">
                    @csrf
                    @error('updateCompaniesCsvFile')
                    <div class="text-red-500 text-sm text-center">
                        <p>{{ $message }}</p>
                    </div>
                    @enderror

                    <div class="mt-1 h-full w-full">
                        <div class="col-span-2">
                            <label for="internal-candidate" class="block text-sm text-center leading-6 text-gray-900">Upload companies to update</label>
                            <div class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                <div class="text-center">
                                    {{-- @if ($updateCompaniesCsvFile) --}}

                                    {{-- @else --}}
                                    <div class="flex items-center justify-center">
                                        <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                    </div>
                                    <div class="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                                        <label for="file-update-company" class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                                            <span>Upload a file</span>
                                            <input id="file-update-company" wire:model="updateCompaniesCsvFile" name="file-upload-company" type="file" class="sr-only" accept=".csv, .xlsx, .xls">

                                    </div>
                                    <p class="text-xs  text-gray-600">(CSV or Excel)</p>
                                    @if ($updateCompaniesCsvFile && $updateCompaniesCsvFile->getClientOriginalName())
                                    <p class="text-sm  text-gray-600">
                                        <span class="font-semibold">Selected File:</span>
                                        {{ $updateCompaniesCsvFile->getClientOriginalName() }}
                                    </p>
                                    @endif
                                    {{-- @endif --}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-2 w-full px-4 mt-4">
                        <button @click="updatePopup = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                        <button type="submit" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                            <span class="block"> Upload</span>
                        </button>
                    </div>
                    {{-- @include('livewire.jobPeople.duplicateUpload') --}}

                </form>
                <div x-show="tab == 'individual'" x-data="{ individualTab: 'people' }" class="flex h-full w-full justify-center items-center flex-col">
                <div class="flex border-b-2 w-full">
                    <button @click="individualTab = 'people'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': individualTab ==
                                                'people',
                                            'chart-heading font-semibold': individualTab != 'people'
                                        }">
                        People
                    </button>
                    <button @click="individualTab = 'skills'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': individualTab ==
                                                'skills',
                                            'chart-heading font-semibold': individualTab !=
                                                'skills'
                                        }">
                        Skills
                    </button>
                    <button @click="individualTab = 'career_histories'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': individualTab ==
                                                'career_histories',
                                            'chart-heading font-semibold': individualTab !=
                                                'career_histories'
                                        }">
                        Career Histories
                    </button>
                </div>
                    <form x-show="individualTab == 'people'" class="mt-3 w-full" wire:submit.prevent="uploadBulkUpdatePeopleCSV">
                        @csrf
                    @error('updatePeopleCsvFile')
                    <div class="text-red-500 text-sm text-center">
                        <p>{{ $message }}</p>
                    </div>
                    @enderror

                        <div class="mt-1 h-full w-full">
                            <div class="col-span-2">
                                <label for="internal-candidate" class="block text-sm text-center leading-6 text-gray-900">Upload people data to update</label>
                                <div class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                    <div class="text-center">
                                        {{-- @if ($updatePeopleCsvFile) --}}

                                        {{-- @else --}}
                                        <div class="flex items-center justify-center">
                                            <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                        </div>
                                        <div class="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                                            <label for="file-update-people" class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                                                <span>Upload a file</span>
                                                <input id="file-update-people" wire:model="updatePeopleCsvFile" name="file-update-people" type="file" class="sr-only" accept=".csv, .xlsx, .xls">

                                        </div>
                                        <p class="text-xs  text-gray-600">(CSV or Excel)</p>
                                        @if ($updatePeopleCsvFile && $updatePeopleCsvFile->getClientOriginalName())
                                        <p class="text-sm  text-gray-600">
                                            <span class="font-semibold">Selected File:</span>
                                            {{ $updatePeopleCsvFile->getClientOriginalName() }}
                                        </p>
                                        @endif
                                        {{-- @endif --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex gap-2 w-full px-4 mt-4">
                            <button @click="updatePopup = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                            <button type="submit" class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                                <span class="block"> Upload</span>
                            </button>
                        </div>
                        {{-- @include('livewire.jobPeople.duplicateUpload') --}}

                    </form>
                    <form x-show="individualTab == 'career_histories'" class="mt-3 w-full" wire:submit.prevent="uploadBulkUpdateCareerHistoryCSV">
                        @csrf
                        @error('updateCareerHistoryCsvFile')
                    <div class="text-red-500 text-sm text-center">
                        <p>{{ $message }}</p>
                    </div>
                    @enderror

                        <div class="mt-1 h-full w-full">
                            <div class="col-span-2">
                                <label for="internal-candidate" class="block text-sm text-center leading-6 text-gray-900">Upload career history data to update</label>
                                <div class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                    <div class="text-center">
                                        {{-- @if ($updateCareerHistoryCsvFile) --}}

                                        {{-- @else --}}
                                        <div class="flex items-center justify-center">
                                            <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                        </div>
                                        <div class="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                                            <label for="file-upload-ch" class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                                                <span>Upload a file</span>
                                                <input id="file-upload-ch" wire:model="updateCareerHistoryCsvFile" name="file-upload-ch" type="file" class="sr-only" accept=".csv, .xlsx, .xls">

                                        </div>
                                        <p class="text-xs  text-gray-600">(CSV or Excel)</p>
                                        @if ($updateCareerHistoryCsvFile && $updateCareerHistoryCsvFile->getClientOriginalName())
                                        <p class="text-sm  text-gray-600">
                                            <span class="font-semibold">Selected File:</span>
                                            {{ $updateCareerHistoryCsvFile->getClientOriginalName() }}
                                        </p>
                                        @endif
                                        {{-- @endif --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex gap-2 w-full px-4 mt-4">
                            <button @click="updatePopup = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                            <button type="submit" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                                <span class="block"> Upload</span>
                            </button>
                        </div>
                        {{-- @include('livewire.jobPeople.duplicateUpload') --}}

                    </form>
                    <form x-show="individualTab == 'skills'" class="mt-3 w-full" wire:submit.prevent="uploadBulkUpdateSkillsCSV">
                        @csrf
                        @error('updateSkillsCsvFile')
                    <div class="text-red-500 text-sm text-center">
                        <p>{{ $message }}</p>
                    </div>
                    @enderror

                        <div class="mt-1 h-full w-full">
                            <div class="col-span-2">
                                <label for="internal-candidate" class="block text-sm text-center leading-6 text-gray-900">Upload skills data to update</label>
                                <div class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                    <div class="text-center">
                                        {{-- @if ($updateSkillsCsvFile) --}}

                                        {{-- @else --}}
                                        <div class="flex items-center justify-center">
                                            <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                        </div>
                                        <div class="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                                            <label for="file-upload-skills" class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                                                <span>Upload a file</span>
                                                <input id="file-upload-skills" wire:model="updateSkillsCsvFile" name="file-upload-skills" type="file" class="sr-only" accept=".csv, .xlsx, .xls">

                                        </div>
                                        <p class="text-xs  text-gray-600">(CSV or Excel)</p>
                                        @if ($updateSkillsCsvFile && $updateSkillsCsvFile->getClientOriginalName())
                                        <p class="text-sm  text-gray-600">
                                            <span class="font-semibold">Selected File:</span>
                                            {{ $updateSkillsCsvFile->getClientOriginalName() }}
                                        </p>
                                        @endif
                                        {{-- @endif --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex gap-2 w-full px-4 mt-4">
                            <button @click="updatePopup = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                            <button type="submit" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                                <span class="block"> Upload</span>
                            </button>
                        </div>
                        {{-- @include('livewire.jobPeople.duplicateUpload') --}}

                    </form>
                </div>
            </div>
        </div>
    </div>

</div>