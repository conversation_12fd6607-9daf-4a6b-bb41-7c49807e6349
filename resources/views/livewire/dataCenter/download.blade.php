<div class="relative" x-data="{ show: false}">
    <!-- Trigger button inside the modal -->
    <div class="w-40 border rounded-lg p-px shadow-md">
        <button @click="show = !show" class="w-full flex justify-center items-center gap-2 font-semibold text-sm text-black rounded-lg py-2">
            <img class="h-5 font-bold w-auto img px-1" src="{{ asset('images/BlackDownload.png') }}">
            <span class="text-md font-semibold">Download</span>
        </button>
    </div>
    <div x-show="show" class="absolute w-full bg-white rounded-lg border p-1">
        <ul class="list-none gap-5">
            <li wire:click="downloadPeopleCSV" @click="show = !show" class="text-sm font-medium text-[#667085] cursor-pointer hover:bg-cyan-500 hover:text-white rounded-lg px-2 py-2">People</li>
            <li wire:click="downloadCompaniesCSV" @click="show = !show" class="text-sm font-medium text-[#667085] cursor-pointer hover:bg-cyan-500 hover:text-white rounded-lg px-2 py-2">Companies</li>
            <!-- <li wire:click="downloadSkillsCSV" @click="show = !show" class="text-sm font-medium text-[#667085] cursor-pointer hover:bg-cyan-500 hover:text-white rounded-lg px-2 py-2">Skills</li>
            <li wire:click="downloadCareerHistoriesCSV" @click="show = !show" class="text-sm font-medium text-[#667085] cursor-pointer hover:bg-cyan-500 hover:text-white rounded-lg px-2 py-2">Career Histories</li> -->
        </ul>
    </div>
</div>