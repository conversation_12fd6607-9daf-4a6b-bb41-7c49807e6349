<div x-data="{
        deleteSubmittedPeoplePopup: @entangle('deleteSubmittedPeoplePopup'),
        personIdReplacedWith: @entangle('personIdReplacedWith')
    }">

    @include('livewire.flashMessage')

    <div :key="$refreshKey"
        x-data="{ 
            addInd: @entangle('addInd'), 
            addC: false, 
            addModal: false
        }"
        class="customHeight grayBackground overflow-y-scroll">
        <div class="flex lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
            <div class="flex gap-4 items-center">
                <h1 class="whitespace-nowrap text-3xl font-medium">TSP Center</h1>
            </div>
            <div class="flex gap-5 w-max">
                <div class="w-32 border rounded-lg p-px shadow-md">
                    <div class="flex items-center justify-center w-full text-center rounded-lg px-2">
                        <a href="{{ route('datacenter.people') }}" class="w-full flex gap-2 justify-center items-center text-black py-2 text-center text-sm font-medium">
                            <img class="" src="{{ asset('images/peoplesearchIcon.svg') }}">
                            <span>People</span>
                        </a>
                    </div>
                </div>
                @include('livewire.dataCenter.download')
                @include('livewire.dataCenter.bulk-upload')
                @include('livewire.dataCenter.bulk-update')
                <div class="relative">
                    <button x-on:click="addModal = !addModal" class="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-3 py-3 hover:bg-cyan-600 font-medium text-white text-md justify-center">
                        <img src="{{ asset('images/plus-white-without-circle.svg') }}" alt="pencil">
                    </button>
                    <div x-show="addModal" class="flex w-max flex-col px-2 py-2 bg-white border absolute rounded-lg right-0">
                        <ul class="list-none gap-5">
                            <li @click="addC = true,addModal = !addModal" class="text-sm font-medium text-[#667085] cursor-pointer hover:bg-cyan-500 hover:text-white rounded-lg px-12 py-2">
                                Company </li>
                            <li @click="addInd = true,addModal = !addModal" class="text-sm font-medium text-[#667085] cursor-pointer hover:bg-cyan-500 hover:text-white rounded-lg px-12 py-2">
                                Individual </li>
                        </ul>
                    </div>
                    @include('livewire.dataCenter.addCompany')
                    @include('livewire.dataCenter.addIndividual')
                </div>
            </div>
        </div>
        <div class="my-5 px-4 flex flex-col gap-y-10">
            @include('livewire.loading')

            <div class="grid grid-cols-4 gap-x-4">
                <div class="flex flex-col justify-center gap-y-4 items-center bg-white h-40 border border-gray-300 shadow-md rounded-2xl">
                    <h3 class="whitespace-wrap text-black font-semibold text-center">Our Database Size</h3>
                    <div class="justify-center items-center flex flex-col">
                        <h3 class="text-sky-500 font-semibold text-2xl text-center">{{ $externalcount }}</h3>
                        <p class="text-gray-600  text-xs text-center font-light">Number of People</p>
                    </div>
                </div>
                <div class="flex flex-col justify-center gap-y-4 items-center bg-white h-40 border border-gray-300 shadow-md rounded-2xl">
                    <h3 class="whitespace-wrap text-black font-semibold text-center">Pending Updates</h3>
                    <div class="justify-center items-center flex flex-col">
                        <h3 class="text-sky-500 font-semibold text-2xl text-center">{{ $externalupdatecount }}</h3>
                        <p class="text-gray-600  text-xs text-center font-light">Number of People</p>
                    </div>
                </div>

            </div>
            <div class="dc-table-container bg-white border border-gray-300 rounded-2xl shadow-md p-4 overflow-hidden">
                <div class="flex flex-1 justify-center items-center">
                    <div class="flex flex-1">
                        <h3 class="text-black font-semibold text-xl">Proposed Individuals</h3>
                    </div>
                </div>
                <div class="relative py-2 bg-white">
                    <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                        <div class="w-full border-t-2 border-sky-200"></div>
                    </div>
                </div>
                <div x-data="{ open: {},
                editPeoplePopup: @entangle('editPeoplePopup'),
                editPeopleCareerHistoryPopup: @entangle('editPeopleCareerHistoryPopup'),
                editPeopleSkillsPopup: @entangle('editPeopleSkillsPopup')
                    }"
                    class="h-96 w-full flex flex-col justify-between items-center relative overflow-hidden overflow-y-scroll">
                    <table class="min-w-full divide-y divide-gray-300 rounded-lg border">
                        <thead class="sticky top-0 z-0 rounded-xl border">
                            <tr class="table-header">
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Name</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Role</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Company</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Notes</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Date Added</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white">
                            @foreach($externalPeoples as $Person)
                            <tr>
                                <td class="whitespace-wrap text-center text-xs text-gray-500 p-3">{{ $Person->forename }}</td>
                                <td class="whitespace-wrap text-center text-xs text-gray-500 p-3">{{ $Person->latest_role }}</td>
                                <td class="whitespace-wrap text-center text-xs text-gray-500 p-3">{{ $Person->company_name }}</td>
                                <td class="whitespace-wrap text-center text-xs text-gray-500 p-3">
                                    {{ $Person->notes ? \Illuminate\Support\Str::limit($Person->notes, 50, '...') : "" }}
                                </td>
                                <td class="whitespace-wrap text-center text-xs text-gray-500 p-3">{{ $Person->created_at }}</td>
                                <td class="whitespace-nowrap px-3 py-2">
                                    <!-- Trigger button inside the modal -->
                                    <div class="flex items-center justify-end">
                                        <button wire:click="fetchPeopleDetails({{ $Person->id }})" class="transition ease-in-out delay-100 text-sm text-black rounded-full font-medium hover:scale-105">
                                            <img class="h-4 w-auto " src="{{ asset('images/eye.svg') }}">
                                        </button>
                                        &nbsp;&nbsp;
                                        @if(strtolower($Person->status) == 'reported')
                                        <button @click="confirmDeleteReportedPerson({{ $Person->id }}, '{{ asset('images/redTrashIcon.svg') }}')" class="transition ease-in-out delay-100 text-sm text-black rounded-full font-medium hover:scale-105">
                                            <img class="h-3 w-[75%]" src="{{ asset('images/red_trash_icon_without_circle.svg') }}">
                                        </button>
                                        @elseif(strtolower($Person->status) == 'submitted')
                                        <button wire:click="deleteSubmittedPerson({{ $Person->id }})" class="transition ease-in-out delay-100 text-sm text-black rounded-full font-medium hover:scale-105">
                                            <img class="h-3 w-[75%]" src="{{ asset('images/red_trash_icon_without_circle.svg') }}">
                                        </button>
                                        @endif

                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="py-2 px-2 sticky bottom-0 bg-white w-full">
                        {{ $externalPeoples->links() }}
                    </div>
                    @include('livewire.dataCenter.updateIndividual')
                    @include('livewire.dataCenter.updateCareerHistory')
                    @include('livewire.dataCenter.updateSkills')
                </div>
            </div>

            <div class="dc-table-container bg-white border border-gray-300 rounded-2xl shadow-md p-4 overflow-hidden">
                <div class="flex flex-1 justify-center items-center">
                    <div class="flex flex-1">
                        <h3 class="text-black font-semibold text-xl">Proposed Companies</h3>
                    </div>
                </div>
                <div class="relative py-2 bg-white">
                    <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                        <div class="w-full border-t-2 border-sky-200"></div>
                    </div>
                </div>
                <div x-data="{ open: {} }" class="h-96 w-full flex flex-col justify-between items-center relative overflow-hidden overflow-y-scroll">
                    <table class="min-w-full divide-y divide-gray-300 rounded-lg border">
                        <thead class="sticky top-0 z-0 rounded-xl border">
                            <tr class="table-header">
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Name</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Added By</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Date Added</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white">
                            @foreach($companies as $company)
                            <tr>
                                <td class="whitespace-wrap text-center text-xs text-gray-500 p-3">{{ $company->name }}</td>
                                <td class="whitespace-wrap text-center text-xs text-gray-500 p-3">{{ $user->name }}</td>
                                <td class="whitespace-wrap text-center text-xs text-gray-500 p-3">{{ $company->created_at }}</td>
                                <td class="whitespace-nowrap px-3 py-2">
                                    <div class="flex" x-data="{ copen: false }">
                                        <!-- Trigger button inside the modal -->
                                        <div class="flex items-center justify-end">
                                            <button wire:click="fetchCompanyDetails({{ $company->id }})" x-on:click="copen = true" class="transition ease-in-out delay-100 text-sm text-black rounded-full font-medium hover:scale-105">
                                                <img class="h-4 w-auto " src="{{ asset('images/eye.svg') }}">
                                            </button>
                                        </div>
                                        @include('livewire.dataCenter.updateCompany')
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="py-2 px-2 sticky bottom-0 bg-white w-full">
                        {{ $companies->links() }}
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-2 w-full gap-x-4">
                <div class="dc-table-container bg-white border border-gray-300 rounded-2xl shadow-md p-4 overflow-hidden">
                    <div class="flex flex-1 justify-center items-center">
                        <div class="flex flex-1">
                            <h3 class="text-black font-semibold text-xl">My Plans</h3>
                        </div>
                    </div>
                    <div class="relative py-2 bg-white">
                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                            <div class="w-full border-t-2 border-sky-200"></div>
                        </div>
                    </div>
                    <div x-data="{ open: {} }" class="h-96 w-full flex flex-col justify-between items-center relative overflow-hidden overflow-y-scroll">
                        <table class="min-w-full divide-y divide-gray-300 rounded-lg border">
                            <thead class="sticky top-0 z-0 rounded-xl border">
                                <tr class="table-header">
                                    <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Client</th>
                                    <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Plan Name</th>
                                    <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Status</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @foreach($plans as $plan)
                                <tr class="p-2">
                                    <td class="whitespace-wrap text-center text-xs text-gray-500 py-2">{{ $plan->user_id }}</td>
                                    <td class="whitespace-wrap text-center text-xs text-gray-500 py-2">
                                        <a class="cursor-pointer" href="{{ route('plan.show', ['plan' => $plan->id]) }}">{{ $plan->name }}</a>
                                    </td>
                                    <td class="whitespace-wrap text-center text-xs text-gray-500 py-2">
                                        {{ $plan->status }}

                                        {{--
                                                @if ($plan->status == 'Active') 
                                                    GreenBackground mainGreen
                                                @elseif($plan->status == 'Closed')
                                                    RedBG text-red-500
                                                @elseif($plan->status == 'Draft')
                                                    grayBackground GrayText @endif"
                                            --}}

                                        {{-- css for other statuses is below --}}
                                        {{-- BlueBackground mainBlue, orangeBg --}}

                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="dc-table-container bg-white border border-gray-300 rounded-2xl shadow-md p-4 overflow-hidden">
                    <div class="flex flex-1 justify-center items-center">
                        <div class="flex flex-1">
                            <h3 class="text-black font-semibold text-xl">My Jobs</h3>
                        </div>
                    </div>
                    <div class="relative py-2 bg-white">
                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                            <div class="w-full border-t-2 border-sky-200"></div>
                        </div>
                    </div>
                    <div x-data="{ open: {} }" class="h-96 w-full flex flex-col justify-between items-center relative overflow-hidden overflow-y-scroll">
                        <table class="min-w-full divide-y divide-gray-300 rounded-lg border">
                            <thead class="sticky top-0 z-0 rounded-xl border">
                                <tr class="table-header">
                                    <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Client</th>
                                    <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Job Name</th>
                                    <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Status</th>
                                    <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Longlist</th>
                                    <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Shortlist</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @foreach($jobs as $job)
                                <tr class="p-2">
                                    <td class="whitespace-wrap text-center text-xs text-gray-500 py-2">{{ $job->user_id }}</td>
                                    <td class="whitespace-wrap text-center text-xs text-gray-500 py-2">
                                        <a class="cursor-pointer" href="{{ route('job.long.index', ['job' => $job->id]) }}">{{ $job->name }}</a>
                                    </td>
                                    <td class="whitespace-wrap text-center text-xs font-normal p-2">
                                        <span class="py-2 px-4 rounded-lg font-medium
                                                                    @if ($job->status == 'Active') GreenBackground mainGreen
                                                                    @elseif($job->status == 'Closed')
                                                                        RedBG text-red-500
                                                                    @elseif($job->status == 'Draft')
                                                                        grayBackground GrayText @endif">
                                            {{ $job->status }}
                                        </span>

                                        {{-- css for other statuses is below --}}
                                        {{-- BlueBackground mainBlue, orangeBg --}}

                                    </td>
                                    <td class="whitespace-wrap text-center text-xs font-normal text-gray-500 p-2">
                                        {{ $pipelineCountsMap[$job->id]->pipelinecount ?? 0 }}
                                    </td>
                                    <td class="whitespace-wrap text-center text-xs font-normal text-gray-500 p-2">
                                        {{ $jobCountsMap[$job->id]->jobcount ?? 0 }}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div x-show="deleteSubmittedPeoplePopup" x-cloak class="fixed inset-0 flex items-center justify-center">
        <div class="fixed inset-0 bg-slate-400 opacity-50 backdrop-blur-sm z-40"></div>
        <div
            class="bg-white relative  deleteOrg border-2 p-4 rounded-lg flex flex-col justify-center items-center z-50">
            <img @click="deleteSubmittedPeoplePopup = false"
                class="absolute right-2 crossTop w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}"
                alt="Close Icon">
            <img class="w-6" src="{{ asset('images/sync-alt-solid-red.svg') }}" alt="Trash Icon">
            <h3 class="font-semibold text-base mt-3">Replace Person</h3>
            {{--
                <p class="text-xs text-center text-gray-600 py-3">Are you sure you want to delete the People?
                    <br />
                    This action cannot be undone.
                </p>
            --}}

            <label class="text-md text-gray-600 py-3">Please enter relevant id to replace individuals</label>
            <input wire:model="personIdReplacedWith" type="number" min="1" id="personIdReplacedWith" placeholder="Enter relevant id" name="personIdReplacedWith" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" />
            <div class="flex gap-2 w-full px-4 mt-4">
                <button @click="deleteSubmittedPeoplePopup = false" type="button"
                    class="bg-white font-semibold w-full text-black border p-2 rounded-md">
                    <span class="block">Cancel</span>
                </button>

                <button wire:click="replaceSubmittedPerson()" type="button" x-show="personIdReplacedWith != ''"
                    class="bg-cyan-500 text-white font-semibold w-full border p-2 rounded-md">
                    <span class="block">Replace</span>
                </button>
                <button type="button" x-show="personIdReplacedWith == ''"
                    class="bg-cyan-500 text-white font-semibold w-full border p-2 rounded-md disabled">
                    <span class="block">Replace</span>
                </button>

                <button type="button" x-show="personIdReplacedWith != ''"
                    class="text-red-500 font-semibold w-full bg-white border p-2 rounded-md disabled"
                    disabled>
                    <span class="block">Delete</span>
                </button>
                <button wire:click="deleteSubmittedPersonConfirm()" type="button" x-show="personIdReplacedWith == ''"
                    class="text-red-500 font-semibold w-full bg-white border p-2 rounded-md">
                    <span class="block">Delete</span>
                </button>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/just-validate@4.5.0/dist/just-validate.min.js"></script>

<script>
    document.addEventListener("DOMContentLoaded", function() {

        let attempts = 0;
        const elements = ['file-upload-people', 'file-upload-skills', 'file-upload-ch', 'file-update-people', 'file-update-company'];

        const intervalId = setInterval(function() {
            const allFound = elements.every(id => document.getElementById(id));

            if (allFound) {
                elements.forEach(id => {
                    document.getElementById(id).addEventListener('change', function() {
                        document.getElementById('loading-spinner').classList.remove('hidden');
                    });
                });
                clearInterval(intervalId);
            } else if (++attempts >= 100) {
                clearInterval(intervalId);
                console.log("One or more elements were not found after 100 attempts.");
            }

        }, 500);


    });

    // For step2 and step3 events
    const setupStepListener = (stepId, fileId) => {
        document.getElementById(stepId)?.addEventListener('click', () => {
            setTimeout(() => {
                const fileElement = document.getElementById(fileId);
                if (fileElement) {
                    fileElement.addEventListener('change', function() {
                        document.getElementById('loading-spinner').classList.remove('hidden');
                    });
                }
            }, 500);
        });
    };

    setupStepListener('step2', 'file-upload-skills');
    setupStepListener('step3', 'file-upload-ch');

    document.getElementById("file-upload-company").addEventListener('change', function() {
        document.getElementById('loading-spinner').classList.remove('hidden');
    });

    function confirmDeleteReportedPerson(personId, iconUrl) {
        Swal.fire({
            // title: "Do you want to delete this plan?",
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Delete Person</h2>
                    <p class="px-5 font-normal">Are you sure you want to delete person? Any data that is saved for this person will be permanently erased.</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Delete",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                Livewire.dispatch('deleteReportedPerson', {
                    personId: personId
                });
            }
        });
    }

</script>
