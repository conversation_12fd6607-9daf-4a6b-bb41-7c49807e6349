    <!-- Modal container -->
    <div x-show="uopen" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-40" style="display:none">
        <!-- Modal background with a higher z-index -->
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
        <!-- Modal content with a lower z-index -->
        <div class="w-[40%] bg-white shadow-md relative rounded-lg border border-gray-300 p-4 z-50" x-show="uopen" x-transition>
            <!-- Modal content -->
            <img wire:click.prevent="closeModal('person')" @click="uopen = false" class="absolute right top-2 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Search Icon">

            <h2 class="font-semibold px-4">Update Individual</h2>
            <div class="flex justify-between mt-3 px-4">
                <button>
                    <span class="mainBlue p-1 rounded-full px-2 text-xs 
                                                @if ($step == 1) BlueBackground mainBlue
                                                @else
                                                GreenBackground mainGreen @endif">1</span>
                    <span class="text-xs font-medium">Personal Details</span>
                </button>
                <button>
                    <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                                        @if ($step == 2) BlueBackground mainBlue
                                        @elseif($step > 2)
                                        GreenBackground mainGreen @endif">2</span>
                    <span class="text-xs font-medium">Professional Details</span>
                </button>
                <button>
                    <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                                        @if ($step == 3) BlueBackground mainBlue
                                        @elseif($step > 3)
                                        GreenBackground mainGreen @endif">3</span>
                    <span class="text-xs font-medium">Additional Details</span>
                </button>
                <button>
                    <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                                        @if ($step == 4) BlueBackground mainBlue
                                        @elseif($step > 4)
                                        GreenBackground mainGreen @endif">4</span>
                    <span class="text-xs font-medium">Career History</span>
                </button>
            </div>
            <div class="w-full border-t mt-3 border-gray-200"></div>
            <p class="whitespace-normal text-xs text-gray-600 px-4 py-2 text-center">Review the personal details provided below. If there are any mistakes please fill in the input form otherwise leave it blank</p>
            @if ($step === 1)
            <div class="h-full">
                <div class="h-5/6 flex items-center">
                    <div class="w-full">
                        <div class="relative py-2 bg-white">
                            <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                        </div>
                        <div class="modalscroll px-4">
                            <div class="mb-3">
                                <label for="forename" class="block text-xs font-medium labelcolor">Forname</label>
                                <input wire:model="forename" type="text" id="forename" placeholder="Enter forename" name="forename" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                @error('forename')
                                <span class="text-red-500 text-xs">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label for="surname" class="block text-xs font-medium labelcolor">Surname</label>
                                <input wire:model="surname" type="text" id="surname" placeholder="Enter surname" name="surname" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                @error('surname')
                                <span class="text-red-500 text-xs">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label for="othername" class="block text-xs font-medium labelcolor">Other Name</label>
                                <input wire:model="othername" type="text" id="othername" placeholder="Enter other name" name="othername" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                @error('othername')
                                <span class="text-red-500 text-xs">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label for="middlename" class="block text-xs font-medium labelcolor">Middle Name</label>
                                <input wire:model="middlename" type="text" id="middlename" placeholder="Enter middle name" name="middlename" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                @error('middlename')
                                <span class="text-red-500 text-xs">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="py-4 flex w-full items-center justify-start gap-x-4">
                                <div class="flex items-center gap-x-3">
                                    <input id="gender-male" type="radio" wire:model="gender" class="h-4 w-4 rounded-full appearance-none border-2 checked:border border-gray-500 checked:bg-cyan-500" value="1">
                                    <label for="gender-male" class="block text-sm leading-6 text-gray-900">Male</label>
                                </div>
                                <div class="flex items-center gap-x-3">
                                    <input id="gender-female" type="radio" wire:model="gender" class="h-4 w-4 rounded-full appearance-none border-2 checked:border border-gray-500 checked:bg-cyan-500" value="2">
                                    <label for="gender-female" class="block text-sm text-gray-900">Female</label>
                                </div>
                                <div class="flex items-center gap-x-3">
                                    <input id="gender-not-applicable" type="radio" wire:model="gender" class="h-4 w-4 rounded-full appearance-none border-2 checked:border border-gray-500 checked:bg-cyan-500" value="3">
                                    <label for="gender-not-applicable" class="block text-sm text-gray-900">Not Applicable</label>
                                </div>
                            </div>
                            <div class="flex justify-between w-full gap-5 items-center mt-2">
                                <div class="w-full">
                                    <label for="country" class="block text-xs font-medium labelcolor">Country</label>
                                    <input wire:model="country" type="text" id="country" placeholder="Enter country" name="country" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    @error('country')
                                    <span class="text-red-500 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>
                                <div class="w-full">
                                    <label for="city" class="block text-xs font-medium labelcolor">City</label>
                                    <input wire:model="city" type="text" id="city" placeholder="Enter city" name="city" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    @error('city')
                                    <span class="text-red-500 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="w-full border-t mt-4 border-gray-200"></div>

                        <!-- buttons wrapper -->
                        <div class="flex gap-2 w-full px-4 mt-4">
                            <button wire:click.prevent="closeModal('person')" @click="uopen = false" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                            <button wire:click.prevent="validateStepOne('person')" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                <span class="block"> Continue</span>
                                <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @elseif ($step === 2)
            <div class="h-full">
                <div class="h-5/6 flex items-center">
                    <div class="w-full">
                        <div class="mb-3">
                            <label for="latest_role" class="block text-xs font-medium labelcolor">Current/Latest Role</label>
                            <input wire:model="latest_role" type="text" id="latest_role" placeholder="Enter latest role" name="latest_role" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('latest_role')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="company" class="block text-xs font-medium labelcolor">Company</label>
                            <input wire:model="company" type="text" id="company" placeholder="Enter company" name="company" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('company')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="start_date" class="block text-xs font-medium labelcolor">Start Date</label>
                            <input wire:model="start_date" type="date" id="start_date" placeholder="Enter start date" name="start_date" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('start_date')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="end_date" class="block text-xs font-medium labelcolor">End Date</label>
                            <input wire:model="end_date" type="date" id="end_date" placeholder="Enter end date" name="end_date" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('end_date')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class <div class="mb-3">
                            <label for="seniority" class="block text-xs font-medium labelcolor">Seniority</label>
                            <input wire:model="seniority" type="text" id="seniority" placeholder="Enter seniority" name="seniority" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('seniority')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="exco" class="block text-xs font-medium labelcolor">Exco</label>
                            <input wire:model="exco" type="text" id="exco" placeholder="Enter exco" name="exco" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('exco')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="w-full border-t mt-4 border-gray-200"></div>

                <!-- buttons wrapper -->
                <div class="flex gap-2 w-full px-4 mt-4 ">
                    <button wire:click="previousStep" type="button" class="bg-white w-full text-black border p-2 rounded-md">Back</button>
                    <button wire:click.prevent="validateStepTwo('person')" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                        <span class="block"> Continue</span>
                        <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                    </button>
                </div>
            </div>
            @elseif ($step === 3)
            <div class="h-full">
                <div class="h-5/6 flex items-center">
                    <div class="w-full">
                        <div class="mb-3">
                            <label for="registration_status" class="block text-xs font-medium labelcolor">Regisrations Status</label>
                            <input wire:model="registration_status" type="text" id="registration_status" placeholder="Enter registeration status" name="registration_status" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('registration_status')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="skills" class="block text-xs font-medium labelcolor">Skills</label>
                            <input wire:model="skills" type="text" id="skills" placeholder="Enter skills" name="skills" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('skills')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="languages" class="block text-xs font-medium labelcolor">Languages</label>
                            <input wire:model="languages" type="text" id="languages" placeholder="Enter languages" name="languages" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('languages')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="linkedinURL" class="block text-xs font-medium labelcolor">Linkedin URL</label>
                            <input wire:model="linkedinURL" type="text" id="linkedinURL" placeholder="Enter linkedin url" name="linkedinURL" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('linkedinURL')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="w-full border-t mt-4 border-gray-200"></div>

                <!-- buttons wrapper -->
                <div class="flex gap-2 w-full px-4 mt-4 ">
                    <button wire:click="previousStep" type="button" class="bg-white w-full text-black border p-2 rounded-md">Back</button>
                    <button wire:click.prevent="validateStepThree('person')" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                        <span class="block"> Continue</span>
                        <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                    </button>
                </div>
            </div>
            @elseif ($step === 4)
            <div class="h-full">
                <div class="h-5/6 flex items-center">
                    <div class="w-full">
                        <div class="flex justify-center items-center gap-x-10 ">
                            <div class="flex justify-center items-center gap-x-5">
                                <label class="text-xs text-gray-700">Add Experience</label>
                                <button wire:click.prevent="addPreviousExperience">
                                    <img class="h-4 w-auto" src="{{ asset('images/plus.png') }}">
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 mt-3 w-full modalscroll">
                            @foreach($previousExperience as $index => $pEXP)
                            <div class="mt-2 mb-3">
                                <label for="prevCompany" class="block text-xs font-medium labelcolor">Company</label>
                                <div class="text-xs mt-1" wire:ignore x-data="{
                    multiple: false,
                    value: @entangle('previousExperience.' . $index . '.company'),
                    options: {{ json_encode($refCompany) }},
                    init() {
                        this.$nextTick(() => {
                            let choices = new Choices(this.$refs.select);
                            let refreshChoices = () => {
                                let selection = this.multiple ? this.value : [this.value];
                                choices.clearStore();
                                choices.setChoices(this.options.map(({ value, label }) => ({
                                    value,
                                    label,
                                    selected: selection.includes(value),
                                })));
                            };
                            refreshChoices();
                            this.$refs.select.addEventListener('change', () => {
                                @this.updateCompany({{ $index }}, choices.getValue(true));
                            });
                            this.$watch('value', () => refreshChoices());
                            this.$watch('options', () => refreshChoices());
                        });
                    }
                }" class="bg-white text-xs max-w-sm w-full">
                                    <select class="mt-1" id="prevCompany" x-ref="select" :multiple="multiple"></select>
                                </div>
                                <div class="mb-3">
                                    <label for="role" class="block text-xs font-medium labelcolor">Role</label>
                                    <input wire:model="previousExperience.{{ $index }}.role" type="text" id="role" placeholder="Enter previous role" name="role" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    @error('previousExperience.' . $index . '.role')
                                    <span class="text-red-500 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>
                                <div class="mb-3">
                                    <label for="startDate" class="block text-xs font-medium labelcolor">Start Date</label>
                                    <input type="date" wire:model="previousExperience.{{ $index }}.start_date" type="text" id="startDate" placeholder="Enter start date" name="startDate" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    @error('previousExperience.' . $index . '.start_date')
                                    <span class="text-red-500 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>
                                <div class="mb-3">
                                    <label for="endDate" class="block text-xs font-medium labelcolor">End Date</label>
                                    <input type="date" wire:model="previousExperience.{{ $index }}.end_date" type="text" id="endDate" placeholder="Enter end date" name="endDate" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                    @error('previousExperience.' . $index . '.end_date')
                                    <span class="text-red-500 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>
                                <div class="mt-2">
                                    <button wire:click.prevent="removePreviousExperience({{ $index }})">
                                        <img class="h-4 w-auto" src="{{ asset('images/minus.png') }}">
                                    </button>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full border-t mt-4 border-gray-200"></div>

            <!-- buttons wrapper -->
            <div class="flex gap-2 w-full px-4 mt-4 ">
                <button wire:click="previousStep" type="button" class="bg-white w-full text-black border p-2 rounded-md">Back</button>
                <button wire:click="updatePerson" @click="uopen = false" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                    <span class="block">Update</span>
                </button>
            </div>
        </div>
        @endif
    </div>