<div x-show="addC" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-40" style="display:none">
    <!-- Modal background with a higher z-index -->
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
    <!-- Modal content with a lower z-index -->
    <div class="w-[40%] bg-white shadow-md relative rounded-lg border border-gray-300 p-4 z-50" x-show="addC" x-transition>
        <!-- Modal content -->
        <img wire:click.prevent="closeModal('company')" @click="addC = false" class="absolute right top-2 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Search Icon">

        <h2 class="font-semibold px-4">Add Company</h2>
        <div class="flex justify-between mt-3 px-4">
            <button>
                <span class="mainBlue p-1 rounded-full px-2 text-xs 
                                                @if ($step == 1) BlueBackground mainBlue
                                                @else
                                                GreenBackground mainGreen @endif">1</span>
                <span class="text-xs font-medium">Company Details</span>
            </button>
            <button>
                <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                                        @if ($step == 2) BlueBackground mainBlue
                                        @elseif($step > 2)
                                        GreenBackground mainGreen @endif">2</span>
                <span class="text-xs font-medium">Location</span>
            </button>
            <button>
                <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                                        @if ($step == 3) BlueBackground mainBlue
                                        @elseif($step > 3)
                                        GreenBackground mainGreen @endif">3</span>
                <span class="text-xs font-medium">Financial Information</span>
            </button>
            <button>
                <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                                        @if ($step == 4) BlueBackground mainBlue
                                        @elseif($step > 4)
                                        GreenBackground mainGreen @endif">4</span>
                <span class="text-xs font-medium">Category</span>
            </button>
        </div>
        <div class="w-full border-t mt-3 border-gray-200"></div>
        <p class="whitespace-normal text-xs text-gray-600 px-4 py-2">Review the company details provided below. If there are any mistakes please fill in the input form otherwise leave it blank</p>
        @if ($step === 1)
        <!-- 1st Step -->
        <div class="h-full">
            <div class="h-5/6 flex items-center">
                <div class="w-full">
                    <div class="relative py-2 bg-white">
                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                    </div>
                    <div class="modalscroll px-4">
                        <div class="mb-3">
                            <label for="name" class="block text-xs font-medium labelcolor">Name</label>
                            <input wire:model="name" type="text" id="name" placeholder="Enter company name" name="name" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('name')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="other_name" class="block text-xs font-medium labelcolor">Other Name</label>
                            <input wire:model="other_name" type="text" id="other_name" placeholder="Enter other name" name="other_name" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('other_name')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="parent_name" class="block text-xs font-medium labelcolor">Parent Name</label>
                            <input wire:model="parent_name" type="text" id="parent_name" placeholder="Enter parent name" name="parent_name" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('parent_name')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="name_abbr" class="block text-xs font-medium labelcolor">Name Abbreviation</label>
                            <input wire:model="name_abbr" type="text" id="name_abbr" placeholder="Enter name abbreviation" name="name_abbr" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('name_abbr')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="stock_symbol" class="block text-xs font-medium labelcolor">Stock Symbol</label>
                            <input wire:model="stock_symbol" type="text" id="stock_symbol" placeholder="Enter stock symbol" name="stock_symbol" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('stock_symbol')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="description" class="block text-xs font-medium labelcolor">Description</label>
                            <input wire:model="description" type="text" id="description" placeholder="Enter company description" name="description" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('description')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button @click="addC = false" wire:click.prevent="closeModal('company')" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                        <button wire:click.prevent="validateStepOne('company')" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <span class="block"> Continue</span>
                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @elseif($step === 2)
        <div class="h-full">
            <div class="h-5/6 flex items-center">
                <div class="w-full">
                    <div class="relative py-2 bg-white">
                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                    </div>
                    <div class="modalscroll px-4">
                        <div class="mb-3">
                            <label for="co_company_hq" class="block text-xs font-medium labelcolor">Corparate Country HQ</label>
                            <input wire:model="co_company_hq" type="text" id="co_company_hq" placeholder="Enter headquarter country" name="co_company_hq" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('co_company_hq')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="co_company_add" class="block text-xs font-medium labelcolor">Corporate HQ Address</label>
                            <input wire:model="co_company_add" type="text" id="co_company_add" placeholder="Enter headquarter address" name="co_company_add" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('co_company_add')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="co_company_no" class="block text-xs font-medium labelcolor">Corparate HQ Phone Number</label>
                            <input wire:model="co_company_no" type="text" id="co_company_no" placeholder="Enter headquarter phone number" name="co_company_no" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('co_company_no')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="website" class="block text-xs font-medium labelcolor">Website</label>
                            <input wire:model="website" type="text" id="website" placeholder="Enter company website" name="website" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('website')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button wire:click="previousStep" type="button" class="bg-white w-full text-black border p-2 rounded-md">Back</button>
                        <button wire:click.prevent="validateStepTwo('company')" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <span class="block"> Continue</span>
                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @elseif($step === 3)
        <div class="h-full">
            <div class="h-5/6 flex items-center">
                <div class="w-full">
                    <div class="relative py-2 bg-white">
                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                    </div>
                    <div class="modalscroll px-4">
                        <div class="mb-3">
                            <label for="annual_revenue" class="block text-xs font-medium labelcolor">Annual Revenue</label>
                            <input type="number" wire:model="annual_revenue" type="text" id="annual_revenue" placeholder="Enter headquarter country" name="annual_revenue" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('annual_revenue')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="annual_profit" class="block text-xs font-medium labelcolor">Annual Net Profit Margin</label>
                            <input type="number" wire:model="annual_profit" type="text" id="annual_profit" placeholder="Enter headquarter address" name="annual_profit" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('annual_profit')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="annual_expense" class="block text-xs font-medium labelcolor">Annual Net Expense</label>
                            <input type="number" wire:model="annual_expense" type="text" id="annual_expense" placeholder="Enter headquarter phone number" name="annual_expense" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('annual_expense')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="annual_yoy_change" class="block text-xs font-medium labelcolor">Annual Net YOY Revenue Change</label>
                            <input type="number" wire:model="annual_yoy_change" type="text" id="annual_yoy_change" placeholder="Enter company website" name="annual_yoy_change" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('annual_yoy_change')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button wire:click="previousStep" type="button" class="bg-white w-full text-black border p-2 rounded-md">Back</button>
                        <button wire:click.prevent="validateStepThree('company')" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <span class="block"> Continue</span>
                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @elseif($step === 4)
        <div class="h-full">
            <div class="h-5/6 flex items-center">
                <div class="w-full">
                    <div class="relative py-2 bg-white">
                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                    </div>
                    <div class="modalscroll px-4">
                        <div class="mb-3">
                            <label for="industry" class="block text-xs font-medium labelcolor">Industry</label>
                            <input wire:model="industry" type="text" id="industry" placeholder="Enter headquarter country" name="industry" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('industry')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="sector" class="block text-xs font-medium labelcolor">Sector</label>
                            <input wire:model="sector" type="text" id="sector" placeholder="Enter headquarter address" name="sector" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                            @error('sector')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button wire:click="previousStep" type="button" class="bg-white w-full text-black border p-2 rounded-md">Back</button>
                        <button type="button" wire:click.prevent="submitCompany(null)" @click="addC = false" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <img class="h-4 w-4" src="{{ asset('images/plus-white-without-circle.svg') }}">
                            <span class="block"> Company</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>