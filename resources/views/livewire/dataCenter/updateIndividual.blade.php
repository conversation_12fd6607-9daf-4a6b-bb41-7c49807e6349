<div x-show="editPeoplePopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" x-cloak>
    
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

    <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50 w-full max-w-3xl relative max-h-[96vh] overflow-y-auto">
        <!-- cross icon -->
        <img x-on:click="editPeoplePopup = false" wire:click="closeModal('user')" class="absolute top-2 right-2 w-6 h-6 cursor-pointer"
            src="{{ asset('images/cross.svg') }}" alt="Close Icon">

        <h2 class="font-semibold">Edit {{$forename}} {{$surname}}</h2>
        <div class="w-full border-t mt-3 border-gray-200"></div>

       

        <div class="overflow-y-auto" style="max-height: 40rem;">
             <!-- First Row -->
        <div class="grid grid-cols-4 gap-4 mt-4">
            <div>
                <label for="forename" class="text-base font-medium labelcolor">First Name</label>
                <input type="text" wire:model="forename" id="forename" placeholder="Enter First Name" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="surname" class="text-base font-medium labelcolor">Last Name</label>
                <input type="text" wire:model="surname" id="surname" placeholder="Enter Last Name" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="gender" class="text-base font-medium labelcolor">Gender</label>
                <select id="gender"  wire:model="gender" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                    <option value="" disabled selected>Select Gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Not Applicable">Not Applicable</option>
                </select>
            </div>
            <div>
                <label for="diverse" class="text-base font-medium labelcolor">Diverse</label>
                <input type="text" wire:model="diverse" id="diverse" placeholder="Enter Diversity Status" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="country" class="text-base font-medium labelcolor">Country</label>
                <input type="text" wire:model="country" id="country" placeholder="Enter Country" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="city" class="text-base font-medium labelcolor">City</label>
                <input type="text" wire:model="city" id="city" placeholder="Enter City" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
        </div>

        <!-- Second Row -->
        <div class="grid grid-cols-4 gap-4 mt-4">
            <div>
                <label for="linkedinURL" class="text-base font-medium labelcolor">LinkedIn URL</label>
                <input type="text" wire:model="linkedinURL" id="linkedinURL" placeholder="Enter LinkedIn URL" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="latest_role" class="text-base font-medium labelcolor">Latest Role</label>
                <input type="text" wire:model="latest_role" id="latest_role" placeholder="Enter Latest Role" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="exco" class="text-base font-medium labelcolor">Exco</label>
                <input type="text" wire:model="exco" id="exco" placeholder="Enter Exco Status" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
            <input type="hidden" id="company-id-hidden" wire:model="company_id">
                <div  id="updateCompanyDropdown" x-data="{ company_id: @entangle('company_id') }" >
                <label for="company_id" class="text-base font-medium labelcolor">Company Name</label>


                    <!-- Dropdown with Select2 integration -->
                        <select id="your-select-id" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md" style="width: 100%;">
                        </select>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-4 gap-4 mt-4">
            <div>
                <label for="start_date" class="text-base font-medium labelcolor">Start Date</label>
                <input type="date" wire:model="start_date" id="start_date" placeholder="Enter Start Date" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="end_date" class="text-base font-medium labelcolor">Tenure</label>
                <input type="date" wire:model="end_date" id="end_date" placeholder="Enter Tenure" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="function" class="text-base font-medium labelcolor">Function</label>
                <input type="text" wire:model="function" id="function" placeholder="Enter Function" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="division" class="text-base font-medium labelcolor">Division</label>
                <input type="text" wire:model="division" id="division" placeholder="Enter Division" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
        </div>
        <div class="grid grid-cols-4 gap-4 mt-4">
            <div>
                <label for="other_tags" class="text-base font-medium labelcolor">Other Tags</label>
                <input type="text"  wire:model="other_tags" id="other_tags" placeholder="Enter Other Tags" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
         
            <div>
                <label for="readiness" class="text-base font-medium labelcolor">Readiness</label>
                <input type="text"  wire:model="readiness" id="readiness" placeholder="Enter Readiness" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
        </div>

        <div class="mt-4">
            <div>
                <label for="summary" class="text-base font-medium labelcolor">Summary</label>
                <textarea id="summary"  wire:model="summary" placeholder="Enter Summary" rows="4" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 rounded-md">
                </textarea>
            </div>
        </div>

        <div class="mt-4">
            <div>
                <label for="notes" class="text-base font-medium labelcolor">Notes</label>
                <textarea id="notes"  wire:model="notes" placeholder="Enter Notes" rows="4" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 rounded-md">
                </textarea>
            </div>
        </div>

        <div class="mt-4 flex justify-start">
            <button wire:click="updatePerson" type="button" class="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                Update
            </button>
            <button wire:click="editPeopleCareerHistoryPopup = true" type="button" class="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                Update Career History
            </button>
            <button wire:click="editPeopleSkillsPopup = true" type="button" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                Update Skills
            </button>    
        </div>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>

document.addEventListener('alpine:init', () => {
    Livewire.on('showCompanyDropdown', (data) => {
        let company_id = @entangle('company_id');
        let id = data[0];
        let text = data[1];
        $('#your-select-id').select2({
        placeholder: 'Select an option',
        minimumInputLength: 1,  // Wait until at least 1 character is typed
        ajax: {
            url: '{{ route("company.options") }}',  // Define a route for AJAX call
            dataType: 'json',
            delay: 250,  // Delay to prevent too many requests
            data: function(params) {
                return {
                    q: params.term,  // Search query
                    page: params.page || 1  // Pagination page
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;
                return {
                    results: data.data,  // 'data' should be an array of objects with 'id' and 'text'
                    pagination: {
                        more: data.has_more  // Set to true if there are more results
                    }
                };
            },
            cache: true
        }
    });
    // Set Alpine's selectedCompany when Select2 changes
    $('#your-select-id').on('select2:select', function(e) {
        console.log(e.params.data.id);
        selectedId = e.params.data.id;
        document.getElementById('company-id-hidden').value = selectedId;

// Dispatch an input event to notify Livewire of the change
document.getElementById('company-id-hidden').dispatchEvent(new Event('input'));
    });
    // Set the selected option manually
    if (id) {
            const option = new Option(text, id, true, true);
            $('#your-select-id').append(option).trigger('change');
        }

    });



});




</script>