<div wire:ignore.self wire:on="saved">

@if (session()->has('success'))
    <div x-data="{ show: true, width: 'full' }" x-show="show"
        x-init="setTimeout(() => { show = false; width = '0'; }, 4000)" id="successMessage"
        class="fixed flex gap-2 top-0 right-0 py-4 mt-4 text-white font-semibold px-4 rounded bg-green-500 z-50">
        <img class="w-auto" src="{{ asset('images/WhiteTick.svg') }}" alt="Your Company">
        <span> {{ session('success') }}</span>
        <div class="h-1 bg-green-900 mt-1" :class="width"></div>
    </div>
@endif
</div>

@if (session()->has('error'))
    <style>
        .filter-white {
            filter: invert(1);
        }
    </style>
    <div x-data="{ show: true, width: 'full' }" x-show="show"
        x-init="setTimeout(() => { show = false; width = '0'; }, 8000)" id="errorMessage"
        class="fixed flex gap-2 top-0 right-0 py-4 mt-4 text-white font-semibold px-4 rounded bg-green-500 z-50">
        <img class="w-auto filter-white" src="{{ asset('images/cross.svg') }}" alt="Your Company">
        <span>{{ session('error') }}</span>
        <div class="h-1 bg-green-900 mt-1" :class="width"></div>
    </div>
@endif
