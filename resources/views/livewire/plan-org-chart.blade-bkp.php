@push('style')
    <link rel="stylesheet" href="{{ asset('css/organisationChart.css') }}">
@endpush
<div>
    @include('livewire.flashMessage')
    
    <!-- <pre>{{ print_r($successPeople, true) }}</pre> -->
    <div x-data="{ show: {} }" class="my-org">
        <div class="flex flexRow flex-col lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
            <div class="flex gap-4 items-center">
                <a href="{{ route('plan.show', ['plan' => $plan->id]) }}" class="flex gap-2 mainBlue items-center">
                    <img class="" src="{{ asset('images/ArrowLeftBlue.svg') }}">
                    <span>Back to Plan</span>
                </a>
                <h1 class="whitespace-nowrap font-medium">{{$plan->name}} Org Chart</h1>
            </div>
            <div class="ml-auto">
                <a href="#" class="flex items-center border border-gray-300 rounded-lg px-2 py-2 gap-2 hover:bg-gray-100 transition ml-auto">
                    <img src="{{ asset('images/DownloadSimple.png') }}" alt="Download" class="w-5 h-5" />
                    <span class="text-gray-700 font-medium">Download</span>
                </a>
            </div>
        </div>
        <div class="customHeight tree overflow-y-scroll flex justify-center items-center" x-bind:style="'transform: scale(' + (zoomLevel / 100) + ')'">
            <ul>
                <li>
                    <a href="#">
                        <div wire:key="randomKey" class="w-56 HRInfo rounded-lg shadow-xl bg-white relative marginClass">
                            <div class="flex gap-10 items-center borderTop">
                                <!-- Display parent information -->
                                <div class="flex gap-2 items-center ml-3">
                                    <img class="block cursor-pointer" src="{{ asset('images/CheckCircle.png') }}" alt="Delete Person">
                                    <h2 class="font-semibold text-sm overflow-hidden whitespace-nowrap w-32 text-ellipsis"
                                        title="{{$plan->latest_role}}">
                                        {{$plan->latest_role}}
                                    </h2>
                                </div>
                                <!-- Dropdown menu for parent actions -->
                                <div class="dropdown-container">
                                    <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown"
                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                        type="button">
                                        <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                    </button>
                                    <!-- Dropdown menu options for parent -->
                                    <div id="dropdown2"
                                        class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                        <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                            <div class="cursor-pointer li">
                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                    <span class="font-semibold text-sm">View Profile</span>
                                                </div>
                                            </div>
                                            <div class="cursor-pointer li" @click="confirmRemovePerson({{$plan->tagged_individual}}, '{{ htmlentities($plan->forename) }} {{ htmlentities($plan->surname) }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                    <span class="font-semibold textRed text-sm">Delete</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Parent node border -->
                            <div class="border"></div>

                            <!-- Display parent name and ID -->
                            <div class="p-2 ml-3 flex flex-col text-left">
                                <span class="text-sm overflow-hidden whitespace-nowrap w-48 text-ellipsis" title="Leslie Alexander">
                                    {{$plan->forename}} {{$plan->surname}}
                                </span>
                                <span class="text-xs text-gray-500">Company Name: {{$plan->company_name}}</span>
                            </div>

                            <!-- Drag icon for parent -->
                            <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">
                        </div>
                    </a>
                    <ul>
                        <li>
                            @if (!empty($successPeople))
                                @foreach ($successPeople as $externalP)
                                    @if ($externalP->type === 'External')
                                    <a href="#">
                                        <div wire:key="randomKey" class="w-56 HRInfo rounded-lg shadow-xl bg-white relative marginClass">
                                            <div class="flex gap-10 items-center borderTop">
                                                <!-- Display parent information -->
                                                <div class="flex gap-2 items-center ml-3">
                                                    <img class="block cursor-pointer" src="{{ asset('images/redCrossIcon.svg') }}" alt="Delete Person">
                                                    <h2 class="text-sm overflow-hidden whitespace-nowrap w-32 text-ellipsis"
                                                        title="{{$externalP->latest_role}}">
                                                        {{$externalP->latest_role}}
                                                    </h2>
                                                </div>
                                                <!-- Dropdown menu for parent actions -->
                                                <div class="dropdown-container">
                                                    <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown"
                                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                        type="button">
                                                        <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                                    </button>
                                                    <!-- Dropdown menu options for parent -->
                                                    <div id="dropdown2"
                                                        class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                                        <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                                            <div class="cursor-pointer li">
                                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                    <span class="font-semibold text-sm">View Profile</span>
                                                                </div>
                                                            </div>
                                                            <div class="cursor-pointer li"  @click="confirmRemovePerson({{ $externalP->id }}, '{{ htmlentities($externalP->first_name) }} {{ htmlentities($externalP->last_name) }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                    <span class="font-semibold textRed text-sm">Delete</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Parent node border -->
                                            <div class="border"></div>

                                            <!-- Display parent name and ID -->
                                            <div class="p-2 ml-3 flex flex-col text-left">
                                                <span class="text-sm overflow-hidden whitespace-nowrap w-48 text-ellipsis" title="{{$externalP->first_name}} {{$externalP->last_name}}">
                                                    {{$externalP->first_name}} {{$externalP->last_name}}
                                                </span>
                                                <span class="text-xs text-gray-500 overflow-hidden">Company Name: {{$externalP->company_name}}</span>
                                            </div>

                                            <!-- Drag icon for parent -->
                                            <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">

                                        </div>
                                    </a>
                                    @endif                                    
                                @endforeach
                            @endif
                        </li>
                        <li>
                            @if (!empty($successPeople))
                                @foreach ($successPeople as $internalP)
                                    @if($internalP->type === 'Internal')
                                    <a href="#">
                                        <div wire:key="randomKey" class="w-56 HRInfo-red rounded-lg shadow-xl bg-white relative marginClass">

                                            <div class="flex gap-10 items-center borderTop-red">
                                                <!-- Display parent information -->
                                                <div class="flex gap-2 items-center ml-3">
                                                    <img class="block cursor-pointer" src="{{ asset('images/redCrossIcon.svg') }}" alt="Delete Person">
                                                    <h2 class="text-sm overflow-hidden whitespace-nowrap w-32 text-ellipsis"
                                                        title="{{$internalP->latest_role}}">
                                                        {{$internalP->latest_role}}
                                                    </h2>
                                                </div>
                                                <!-- Dropdown menu for parent actions -->
                                                <div class="dropdown-container">
                                                    <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown"
                                                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                        type="button">
                                                        <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                                    </button>
                                                    <!-- Dropdown menu options for parent -->
                                                    <div id="dropdown2"
                                                        class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                                        <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                                            <div class="cursor-pointer li">
                                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                    <span class="font-semibold text-sm">View Profile</span>
                                                                </div>
                                                            </div>
                                                            <div class="cursor-pointer li" @click="confirmRemovePerson({{ $internalP->id }}, '{{ htmlentities($internalP->first_name) }} {{ htmlentities($internalP->last_name) }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                    <span class="font-semibold textRed text-sm">Delete</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Parent node border -->
                                            <div class="border"></div>

                                            <!-- Display parent name and ID -->
                                            <div class="p-2 ml-3 flex flex-col text-left">
                                                <span class="text-sm overflow-hidden whitespace-nowrap w-48 text-ellipsis" title="{{$internalP->first_name}} {{$internalP->last_name}}">
                                                    {{$internalP->first_name}} {{$internalP->last_name}}
                                                </span>
                                                <span class="text-xs text-gray-500 overflow-hidden">Company Name: {{$internalP->company_name}}</span>
                                            </div>

                                            <!-- Drag icon for parent -->
                                            <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">

                                        </div>
                                    </a>
                                    @endif
                                @endforeach
                            @endif
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>

<script>

    function confirmRemovePerson(personId, personName, iconUrl) {
        Swal.fire({
            // title: `Remove ${personName}?`,
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Remove ${personName}</h2>
                    <p class="px-5 font-normal">Are you sure you want to remove <b>${personName}</b>
                    from your plan. They will stay in your
                    pipeline but any scores that have been
                    manually entered will be deleted.</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Remove",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                Livewire.dispatch('removeSuccessPerson', {
                    id: personId
                });
            }
        });
    }

    function confirmApprovePerson(personId, personName, iconUrl) {
        Swal.fire({
            // title: "Approve Candidate",
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Approve ${personName}</h2>
                    <p class="px-5 font-normal">Are you sure want to approve <b>${personName}</b>?</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Approve",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                Livewire.dispatch('approveSuccessPerson', {
                    id: personId
                });
            }
        });
    }
</script>