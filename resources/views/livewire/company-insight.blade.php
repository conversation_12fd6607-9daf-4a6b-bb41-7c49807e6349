<div x-data="{ show: {} }" class="customHeight grayBackground overflow-y-scroll">
    <div x-data="{drawer: false}" class="flex flexRow flex-col lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
        <h1 class="whitespace-nowrap text-3xl font-medium">Company Insights</h1>
        <div class="rounded-lg">
            <div @click="drawer = true" class="flex cursor-pointer px-2 py-2 border border-[#EAECF0] justify-between items-center bg-white rounded-lg hover:text-cyan-500 hover:bg-cyan-50">
                <img class="h-5 w-5 hover:fill-cyan-500" src="{{asset('images/search-icon.svg')}}" alt="">
                <h2 class="text-sm font-semibold whitespace-nowrap">Advanced Search</h2>
            </div>
        </div>
        <div x-show="drawer" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50 right-0" style="display:none">
            <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
            <div x-show="drawer" x-transition:enter="transition transform ease-out duration-300" x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition transform ease-in duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full" class="drawer z-50 bg-white absolute right-0 shadow-lg overflow-hidden">
                <div class="flex justify-between mt-2 h-5">
                    <h2 class="font-semibold">
                        Advanced Search
                    </h2>
                    <img @click="drawer = false" class="h-5 w-5 cursor-pointer" src="{{asset('images/cross.svg')}}" alt="">
                </div>
                <div class="border-t mt-3 border-gray-200"></div>
                <div class="py-3 advanced_search_drawer relative overflow-y-scroll">
                    <div class="flex gap-y-2 flex-col">
                        <div class="flex flex-col gap-y-2">
                            <h2 class="block text-xs font-medium labelcolor">Name</h2>
                            <div class="text-xs" wire:ignore x-data="{
                                                    multiple: true,
                                                    value: @entangle('selectedCompanies'),
                                                    options: {{ json_encode($companies) }},
                                                    init() {
                                                            this.$nextTick(() => {
                                                                let choices = new Choices(this.$refs.select)
                                                
                                                                let refreshChoices = () => {
                                                                    let selection = this.multiple ? this.value : [this.value]
                                                
                                                                    choices.clearStore()
                                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                                        value,
                                                                        label,
                                                                        selected: selection.includes(value),
                                                                    })))
                                                                }
                                                
                                                                refreshChoices()
                                                
                                                                this.$refs.select.addEventListener('change', () => {
                                                                    this.value = choices.getValue(true)
                                                                })
                                                
                                                                this.$watch('value', () => refreshChoices())
                                                                this.$watch('options', () => refreshChoices())
                                                            })
                                                        }
                                                    }" class="bg-white text-xs max-w-sm w-full">
                                <select x-ref="select"></select>
                            </div>
                        </div>
                        <div class="flex flex-col gap-y-2">
                            <h2 class="block text-xs font-medium labelcolor">Industry</h2>
                            <div class="text-xs" wire:ignore x-data="{
                                                            multiple: true,
                                                            value: @entangle('selectedIndustries'),
                                                            options: {{ json_encode($industry) }},
                                                            init() {
                                                                    this.$nextTick(() => {
                                                                        let choices = new Choices(this.$refs.select)
                                                        
                                                                        let refreshChoices = () => {
                                                                            let selection = this.multiple ? this.value : [this.value]
                                                        
                                                                            choices.clearStore()
                                                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                                                value,
                                                                                label,
                                                                                selected: selection.includes(value),
                                                                            })))
                                                                        }
                                                        
                                                                        refreshChoices()
                                                        
                                                                        this.$refs.select.addEventListener('change', () => {
                                                                            this.value = choices.getValue(true)
                                                                        })
                                                        
                                                                        this.$watch('value', () => refreshChoices())
                                                                        this.$watch('options', () => refreshChoices())
                                                                    })
                                                                }
                                                            }" class="bg-white text-xs max-w-sm w-full">
                                <select x-ref="select"></select>
                            </div>
                        </div>
                        <div class="flex flex-col gap-y-2">
                            <h2 class="block text-xs font-medium labelcolor">Sector</h2>
                            <div class="text-xs" wire:ignore x-data="{
                                                            multiple: true,
                                                            value: @entangle('selectedSector'),
                                                            options: {{ json_encode($sector) }},
                                                            init() {
                                                                    this.$nextTick(() => {
                                                                        let choices = new Choices(this.$refs.select)
                                                        
                                                                        let refreshChoices = () => {
                                                                            let selection = this.multiple ? this.value : [this.value]
                                                        
                                                                            choices.clearStore()
                                                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                                                value,
                                                                                label,
                                                                                selected: selection.includes(value),
                                                                            })))
                                                                        }
                                                        
                                                                        refreshChoices()
                                                        
                                                                        this.$refs.select.addEventListener('change', () => {
                                                                            this.value = choices.getValue(true)
                                                                        })
                                                        
                                                                        this.$watch('value', () => refreshChoices())
                                                                        this.$watch('options', () => refreshChoices())
                                                                    })
                                                                }
                                                            }" class="bg-white text-xs max-w-sm w-full">
                                <select x-ref="select"></select>
                            </div>
                        </div>
                        <div class="flex flex-col gap-y-2">
                            <h2 class="block text-xs font-medium labelcolor">Country</h2>
                            <div class="text-xs" wire:ignore x-data="{
                                                            multiple: true,
                                                            value: @entangle('selectedCountries'),
                                                            options: {{ json_encode($countries) }},
                                                            init() {
                                                                    this.$nextTick(() => {
                                                                        let choices = new Choices(this.$refs.select)
                                                        
                                                                        let refreshChoices = () => {
                                                                            let selection = this.multiple ? this.value : [this.value]
                                                        
                                                                            choices.clearStore()
                                                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                                                value,
                                                                                label,
                                                                                selected: selection.includes(value),
                                                                            })))
                                                                        }
                                                        
                                                                        refreshChoices()
                                                        
                                                                        this.$refs.select.addEventListener('change', () => {
                                                                            this.value = choices.getValue(true)
                                                                        })
                                                        
                                                                        this.$watch('value', () => refreshChoices())
                                                                        this.$watch('options', () => refreshChoices())
                                                                    })
                                                                }
                                                            }" class="bg-white text-xs max-w-sm w-full">
                                <select x-ref="select"></select>
                            </div>
                        </div>
                    </div>
                    <div class="absolute as-footer w-full border-t pt-2 flex items-center justify-between bg-white">
                        <button class="border text-sm text-black rounded-xl py-2 advanced-search-clear font-medium" wire:click="clearFilters" @click='drawer = false'>Reset</button>
                        <button class="border text-sm text-white rounded-xl py-2 advanced-search-submit font-medium bg-cyan-500 hover:text-white" wire:click="runfilters" @click='drawer = false'>Show Results</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
   
    <div class="my-5 px-4 flex flex-col gap-y-5">
        <div class="flex flex-col gap-y-5">
            <div class="flex flex-col gap-y-2">
                <p class="mt-2 text-sm font-light text-gray-700">Choose a company of interest to view more details or select the filters to view more relevant companies.</p>
                <h1 class="whitespace-nowrap text-2xl font-medium">Companies</h1>
            </div>
            <div class="grid grid-cols-3 gap-5">
                <div class="flex flex-col justify-start gap-y-2 border px-5 py-5 bg-white rounded-xl shadow-md">
                    <h1 class="chart-heading text-sm font-semibold">People In System</h1>
                    <h2 class="font-bold text-xl">{{ $peopleInsystem }}</h2>
                </div>
                <div class="flex flex-col justify-start gap-y-2 border px-5 py-5 bg-white rounded-2xl shadow-md">
                    <h1 class="chart-heading text-sm font-semibold">Count of Exco</h1>
                    <h2 class="font-bold text-xl">{{ $excoSum }}</h2>
                </div>
                <div class="flex flex-col justify-start gap-y-2 border px-5 py-5 bg-white rounded-2xl shadow-md">
                    <h1 class="chart-heading text-sm font-semibold">Number of Companies</h1>
                    <h2 class="font-bold text-xl">{{ $companyCount }}</h2>
                </div>
            </div>
        </div>
        <div class="max-h-500p w-full flex flex-col bg-white justify-between shadow-sm overflow-y-scroll relative rounded-lg border">
            <table class="min-w-full divide-y divide-gray-300 rounded-lg border">
                <thead class="sticky top-0 z-30 rounded-lg">
                    <tr class="table-header">
                        <th scope="col" class="py-3.5 pl-4 whitespace-wrap w-56 pr-3 text-left text-sm font-semibold table-column-name sm:pl-6 lg:pl-8">Name</th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold table-column-name">Sector</th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold table-column-name">Industry</th>
                        <th scope="col" class="px-3 py-3.5 text-center text-sm font-semibold table-column-name">People In System</th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold table-column-name"></th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    @foreach ($company as $comp)
                    <tr>
                        <td class="whitespace-wrap w-56  py-2 pl-4 pr-3 text-xs font-medium text-gray-500 sm:pl-6 lg:pl-8">{{ $comp->name }}</td>
                        <td class="whitespace-wrap px-3 py-2 text-xs text-left text-gray-500">{{ $comp->sector }}</td>
                        <td class="whitespace-wrap px-3 py-2 text-xs text-left text-gray-500">{{ $comp->industry }}</td>
                        <td class="whitespace-wrap px-3 py-2 text-xs text-center text-gray-500">{{ $comp->Peopleinsystem }}</td>
                        <td class="whitespace-wrap px-3 py-2 text-xs text-gray-500 flex justify-center items-center">
                            <div class="flex h-8 justify-center items-center">
                                <button class="hover:scale-105" wire:click.prevent="addCompany( {{$comp->id}} )">
                                    <img class="h-4 w-auto" src="{{ asset('images/ArrowSquareOut.svg') }}">
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @if($filteredCompany !== null)
        <div class="bg-white flex flex-col gap-y-5 border-2 border-gray-200 shadow-md w-full rounded-2xl h-80 p-4 overflow-y-hidden">
            <h2 id="industry-header" class="whitespace-nowrap text-black text-2xl font-medium">{{ $chosenCompany[0]->name }}</h2>
            <div class="h-max overflow-y-scroll gap-x-2 grid grid-cols-2">
                <div class="rounded-xl border p-2">
                    <p class="text-sm font-light text-gray-700">{{ $chosenCompany[0]->description }}</p>
                </div>
                <div class="grid grid-cols-2 gap-x-2 gap-y-2">
                    
                    {{--
                        @if($chosenCompany[0]->isPopularDestination)
                            <div class="flex rounded-xl border flex-col gap-y-2 p-2">
                                <dl class="chart-heading text-sm font-semibold">Popular Destination</dl>
                            </div>
                        @endif

                        @if($chosenCompany[0]->isPopularSource)
                            <div class="flex rounded-xl border flex-col gap-y-2 p-2">
                                <dl class="chart-heading text-sm font-semibold">Popular Source</dl>
                            </div>
                        @endif
                    --}}

                    <div class="flex rounded-xl border flex-col gap-y-2 p-2">
                        <dl class="chart-heading text-sm font-semibold">Average Tenure</dl>
                        <dd class="text-xs font-light">{{ $faverageTenure[0] }}</dd>
                    </div>
                    <div class="flex rounded-xl border flex-col gap-y-2 p-2">
                        <dl class="chart-heading text-sm font-semibold">Annual Revenue</dl>
                        <dd class="text-xs font-light">${{ $chosenCompany[0]->Annual_Revenue }}</dd>
                    </div>
                    <div class="flex rounded-xl border flex-col gap-y-2 p-2">
                        <dl class="chart-heading text-sm font-semibold">Annual Expense</dl>
                        <dd class="text-xs font-light">${{ $chosenCompany[0]->Annual_Net_Expenses }}</dd>
                    </div>
                    <div class="flex rounded-xl border flex-col gap-y-2 p-2">
                        <dl class="chart-heading text-sm font-semibold">Earnings Before Interest and Tax</dl>
                        <dd class="text-xs font-light">{{ $chosenCompany[0]->Earnings_Before_Interest_Taxes ? '$'. $chosenCompany[0]->Earnings_Before_Interest_Taxes: "N/A" }}</dd>
                    </div>
                    <div class="flex flex-col rounded-2xl border gap-y-2 p-2 col-span-2">
                        <dl class="chart-heading text-sm font-semibold">Address</dl>
                        <dd class="text-xs font-light">{{ $chosenCompany[0]->corporate_hq_address }}</dd>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-white h-max p-4 border-2 rounded-2xl flex shadow-md flex-col gap-y-2" x-data="{ copen: false }" x-init="copen = false">
            <div class="flex flex-col gap-y-2">
                <div class="flex w-full justify-between px-2">
                    <h2 class="whitespace-nowrap text-black text-xl font-medium">Executive Committee</h2>
                    <button x-on:click="copen = !copen" class="border p-2 rounded-lg text-sm text-black font-semibold hover:text-cyan-500">View All</button>
                </div>
                <div class="flex items-center gap-x-5">
                    <span class="mt-1 text-sm font-light text-gray-700">There are {{ count($fexcoCount) > 0 ? $fexcoCount[0] : 0 }} Senior Leaders of {{ $chosenCompany[0]->name }}</span>
                </div>
            </div>
            <div class="py-4 grid grid-cols-4 gap-y-3 gap-x-2 justify-center" x-show="copen == false">
                @if($chosenCompany[0]->people)
                    @foreach(collect($chosenCompany[0]->people)->take(4) as $person)
                        <div class="flex flex-col justify-between p-2 rounded-2xl border">
                            <h3 class="chart-heading text-sm font-semibold">{{ $person['latest_role'] }}</h3>
                            <h3 class="text-lg text-gray-700">{{ $person['forename'] }} {{ $person['surname'] }}</h3>
                            <div class="flex justify-between items-center gap-x-1 w-full">
                                <div class="flex flex-col gap-y-2 w-1/2 text-left">
                                    <h3 class="chart-heading text-sm">Date Started</h3>
                                    <h4 class="chart-heading text-sm p-1">{{ $person['start_date'] }}</h4>
                                </div>
                                <div class="flex flex-col gap-y-2 w-1/2 text-left">
                                    <h3 class="chart-heading text-sm">Function</h3>
                                    <h4 class="chart-heading text-sm text-center function-card overflow-hidden text-gray-500 whitespace-nowrap p-1 rounded-md">{{ $person['function'] }}</h4>
                                </div>
                            </div>
                            {{--
                                @if($person['status'] == 'Mover')
                                    <div class="flex justify-between items-center gap-x-1 w-full">
                                        <div class="flex flex-col gap-y-2 w-1/2 text-left">
                                            <h4 class="text-xs text-center overflow-hidden text-gray-500 whitespace-nowrap py-1 px-2 rounded-md text-green-700">Recently Joined</h4>
                                        </div>
                                    </div>
                                @endif
                            --}}
                        </div>
                    @endforeach
                @endif
            </div>
            <div class="py-4 grid grid-cols-4 gap-y-3 gap-x-2 justify-center" x-show="copen" style="display:none">
                @if($chosenCompany[0]->people)
                @foreach($chosenCompany[0]->people as $person)
                <div class="flex flex-col justify-between p-2 rounded-2xl border">
                    <h3 class="chart-heading text-sm font-semibold">{{ $person['latest_role'] }}</h3>
                    <h3 class="text-lg text-gray-700">{{ $person['forename'] }} {{ $person['surname'] }}</h3>
                    <div class="flex justify-between items-center gap-x-1 w-full">
                        <div class="flex flex-col gap-y-2 w-1/2 text-left">
                            <h3 class="chart-heading text-sm">Date Started</h3>
                            <h4 class="chart-heading text-sm p-1">{{ $person['start_date'] }}</h4>
                        </div>
                        <div class="flex flex-col gap-y-2 w-1/2 text-left">
                            <h3 class="chart-heading text-sm">Function</h3>
                            <h4 class="chart-heading text-sm text-center function-card overflow-hidden text-gray-500 whitespace-nowrap p-1 rounded-md">{{ $person['function'] }}</h4>
                        </div>
                    </div>
                </div>
                @endforeach
                @endif
            </div>
        </div>
        <div class="w-full flex flex-col gap-y-5">
            <div class="flex flex-col gap-y-4 justify-start">
                <h2 class="whitespace-nowrap text-black text-xl font-medium">Organisation Breakdown</h2>
                <p class="mt-1 text-sm font-light text-gray-700">Shows the spread of top individuals across {{ $chosenCompany[0]->name }}</p>
            </div>
            <div class="bg-white flex flex-col gap-y-5 border-2 w-full border-gray-200 shadow-md rounded-2xl overflow-y-scroll">
                <div class="grid grid-cols-2 gap-5 w-full">
                    <div class="flex flex-col gap-y-2 px-5 py-5">
                        <h1 class="text-sm text-left chart-heading">Exco</h1>
                        <div wire:loading.remove class="flex doughnut-chart-container gap-x-5 justify-between items-center">
                            <canvas id="excoChart"></canvas>
                            <ul id="custom-exco-legend" class="custom-h-legend whitespace-nowrap"></ul>
                        </div>
                    </div>
                    <div class="flex flex-col gap-y-2 px-5 py-5">
                        <h2 class="text-sm text-left chart-heading">Top 5 Functions</h2>
                        <div class="flex division-vbar-chart-container">
                            <canvas id="functionsChart"></canvas>
                            <ul id="custom-hbar-legend" class="custom-vbar-legend custom-h-legend whitespace-nowrap"></ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <h2 class="whitespace-nowrap text-black text-xl font-medium">Demographical Breakdown</h2>
            <p class="mt-1 text-sm font-light text-gray-700">Shows the spread of individuals identified by location and gender across {{ $chosenCompany[0]->name }}</p>
        </div>
        <div class="bg-white flex flex-col gap-y-5 border-2 border-gray-200 shadow-md rounded-2xl p-4 overflow-y-scroll">
            <div class="grid grid-cols-2 gap-5">
                <div class="flex flex-col gap-y-2 px-5 py-5">
                    <h2 class="text-sm text-left chart-heading">Top 5 Locations</h2>
                    <div class="flex flex-col hbar-chart-container gap-y-2 justify-between items-center">
                        <canvas id="locationChart"></canvas>
                        <!-- <ul id="custom-hbar2-legend" class="custom-hbar-legend custom-legend whitespace-nowrap"></ul> -->
                    </div>
                </div>
                <div class="flex flex-col gap-y-2 px-5 py-5">
                    <h1 class="text-sm text-left chart-heading">Potential Sex Diversity</h1>
                    <div wire:loading.remove class="flex flex-col vbar-chart-container gap-y-5 justify-between items-center">
                        <canvas id="genderSplitChart"></canvas>
                        <ul id="custom-gender-legend" class="flex justify-around w-full items-center custom-h-legend whitespace-nowrap"></ul>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
    @include('livewire.loading')
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var excoChart
        var genderChart
        var functionBarChart
        var locationBarChart
        Livewire.on('createCompanyData', (data) => {
            console.log(data, "========data")
            setTimeout(() => {
                const excoElement = document.getElementById('excoChart');
                const genderElement = document.getElementById('genderSplitChart');
                const functionElement = document.getElementById('functionsChart');
                const locationElement = document.getElementById('locationChart');

                if (!excoElement) {
                    console.error('Element with id "exco" not found');
                    return;
                }
                const excoCTX = excoElement.getContext('2d');

                // Fetch data from Livewire component
                const divisionData = data[0]
                const divisionLabels = data[1]
                console.log(divisionData, divisionLabels); // Debugging: Ensure the data is here

                if (!divisionData.length || !divisionLabels.length) {
                    console.error('Division data or labels are empty');
                    return;
                }

                const _divisionData = {
                    labels: divisionLabels,
                    datasets: [{
                        label: 'Exco',
                        data: divisionData,
                        backgroundColor: [
                            'rgb(255, 99, 132)',
                            'rgb(54, 162, 235)',
                            'rgb(255, 205, 86)'
                        ],
                        hoverOffset: 4
                    }]
                };

                const divisionConfig = {
                    type: 'doughnut',
                    data: _divisionData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '80%',
                        plugins: {
                            dataLabels: {
                                show: false
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(tooltipItem) {
                                        const dataset = tooltipItem.dataset;
                                        const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                        const currentValue = dataset.data[tooltipItem.dataIndex];
                                        const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                        return `${currentValue} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    },
                    plugins: [{
                        afterUpdate: function(chart) {
                            const ul = document.getElementById('custom-exco-legend');
                            if (ul) {
                                ul.innerHTML = ''; // Safe to manipulate now
                                // Rest of your code here
                                const data = chart.data;
                                const dataset = data.datasets[0];
                                const total = dataset.data.reduce((sum, value) => sum + value, 0);

                                data.labels.forEach(function(label, i) {
                                    const value = dataset.data[i];
                                    const percentage = Math.round((value / total) * 100);
                                    const color = dataset.backgroundColor[i];

                                    const li = document.createElement('li');
                                    li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                                    ul.appendChild(li);
                                });
                            } else {
                                console.error('Element with id "custom-exco-legend" not found');
                            }
                        }
                    }]
                };
                if (excoChart) {
                    excoChart.destroy()
                }
                // Create the chart
                excoChart = new Chart(excoCTX, divisionConfig);

                if (!genderElement) {
                    console.error('Element with id "gender" not found');
                    return;
                }
                const genderCTX = genderElement.getContext('2d');

                // Fetch data from Livewire component
                const genderData = data[2]
                const genderLabels = data[3]
                const verticalData = {
                    labels: genderLabels,
                    datasets: [{
                        label: 'Potential Sex',
                        data: genderData,
                        backgroundColor: [
                            '#3B82F6',
                            '#FFA347',
                            '#8B5CF6',
                        ]
                    }]
                };
                console.log(genderData, genderLabels); // Debugging: Ensure the data is here

                if (!divisionData.length || !divisionLabels.length) {
                    console.error('Gender data or labels are empty');
                    return;
                }
                const verticalConfig = {
                    type: 'bar',
                    data: verticalData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false, // Allows control over height and width
                        borderRadius: {
                            bottomLeft: 10,
                            bottomRight: 10,
                            topRight: 10,
                            topLeft: 10
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            datalabels: {
                                anchor: 'end',
                                align: 'top',
                                formatter: function(value, context) {
                                    return value + ' (' + ((value / total) * 100).toFixed(2) + '%)';
                                }
                            }
                        },
                        scales: {
                            x: {
                                display: false // Hide x-axis
                            },
                            y: {
                                display: false // Hide y-axis
                            }
                        },
                    },
                    plugins: [{
                        afterUpdate: function(chart) {
                            const ul = document.getElementById('custom-gender-legend');
                            if (ul) {
                                ul.innerHTML = '';
                                const data = chart.data;
                                const dataset = data.datasets[0];
                                const total = dataset.data.reduce((sum, value) => sum + value, 0);

                                data.labels.forEach(function(label, i) {
                                    const value = dataset.data[i];
                                    const percentage = Math.round((value / total) * 100);
                                    const color = dataset.backgroundColor[i];

                                    const li = document.createElement('li');
                                    li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                                    ul.appendChild(li);
                                });
                            } else {
                                console.error('Element with id "custom-gender-legend" not found');
                            }
                        }
                    }]
                };
                if (genderChart) {
                    genderChart.destroy()
                }
                // Create the chart
                genderChart = new Chart(genderCTX, verticalConfig);

                if (!functionElement) {
                    console.error('Element with id "functionChart" not found');
                    return;
                }
                const functionCTX = functionElement.getContext('2d');
                const functionData = data[4]
                const functionLabels = data[5]
                const horizontalData = {
                    labels: functionLabels,
                    datasets: [{
                        label: 'Function',
                        data: functionData,
                        backgroundColor: [
                            '#3B82F6',
                            '#FFA347',
                            '#8B5CF6',
                            '#4CB140',
                            'rgb(75, 192, 192)'
                        ]
                    }]
                };

                const horizontalConfig = {
                    type: 'bar',
                    data: horizontalData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        borderRadius: {
                            bottomLeft: 10,
                            bottomRight: 10,
                            topRight: 10,
                            topLeft: 10
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            datalabels: {
                                anchor: 'end',
                                align: 'end', // Align labels to the end (right side of bars)
                                formatter: function(value, context) {
                                    const total = context.dataset.data.reduce((sum, val) => sum + val, 0);
                                    return value + ' (' + ((value / total) * 100).toFixed(2) + '%)';
                                },
                                color: '#000', // Label text color
                                offset: 2 // Adjust the offset from the bars
                            }
                        },
                        scales: {
                            x: {
                                display: false,
                                beginAtZero: true
                            },
                            y: {
                                border: {
                                    display: false,
                                },
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    display: true
                                },
                                axis: {
                                    display: false
                                }
                            }
                        },
                        plugins: [{
                            afterUpdate: function(chart) {
                                const ul = document.getElementById('custom-hbar-legend');
                                if (ul) {

                                    ul.innerHTML = '';
                                    const data = chart.data;
                                    const dataset = data.datasets[0];
                                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                                    data.labels.forEach(function(label, i) {
                                        const value = dataset.data[i];
                                        const percentage = ((value / total) * 100).toFixed(2);
                                        console.log(value)
                                        const li = document.createElement('li');
                                        li.classList.add('child')
                                        li.id = 'percentage'
                                        li.style.marginBottom = '0px'
                                        li.innerHTML = `${percentage}%`;
                                        ul.appendChild(li);
                                    });
                                    const children = ul.children;
                                    const numChildren = children.length;
                                    if (numChildren > 1) {
                                        const maxPadding = 7;
                                        const minPadding = 2;
                                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                                    } else {
                                        ul.style.justifyContent = 'center'
                                    }
                                    if (numChildren == 2) {
                                        const maxPadding = 10;
                                        const minPadding = 2;
                                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                                    }
                                } else {
                                    console.error('Element with id "custom-hbar-legend" not found');
                                }
                            }
                        }]
                    }
                };
                if (functionBarChart) {
                    functionBarChart.destroy()
                }
                // Create the chart
                functionBarChart = new Chart(functionCTX, horizontalConfig);

                if (!locationElement) {
                    console.error('Element with id "locationChart" not found');
                    return;
                }
                const locationCTX = locationElement.getContext('2d');
                const locationData = data[6]
                const locationLabels = data[7]
                const _locationData = {
                    labels: locationLabels,
                    datasets: [{
                        label: 'Location',
                        data: locationData,
                        backgroundColor: [
                            '#3B82F6',
                            '#FFA347',
                            '#8B5CF6',
                            '#4CB140',
                            'rgb(75, 192, 192)'
                        ]
                    }]
                };

                const locationConfig = {
                    type: 'bar',
                    data: _locationData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        borderRadius: {
                            bottomLeft: 10,
                            bottomRight: 10,
                            topRight: 10,
                            topLeft: 10
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            datalabels: {
                                anchor: 'end',
                                align: 'top',
                                formatter: function(value) {
                                    return value + ' (' + ((value / totalMovements) * 100).toFixed(2) + '%)';
                                }
                            }
                        },
                        scales: {
                            x: {
                                display: false,
                                beginAtZero: true
                            },
                            y: {
                                border: {
                                    display: false,
                                },
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    display: true
                                },
                                axis: {
                                    display: false
                                }
                            }
                        },
                        plugins: [{
                            afterUpdate: function(chart) {
                                const ul = document.getElementById('custom-hbar2-legend');
                                if (ul) {

                                    ul.innerHTML = '';
                                    const data = chart.data;
                                    const dataset = data.datasets[0];
                                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                                    data.labels.forEach(function(label, i) {
                                        const value = dataset.data[i];
                                        const percentage = ((value / total) * 100).toFixed(2);
                                        console.log(value)
                                        const li = document.createElement('li');
                                        li.innerHTML = `<span></span> <div>${value} <span id='percentage'>(${percentage}%)</span></div>`;
                                        ul.appendChild(li);
                                    });
                                } else {
                                    console.error('Element with id "custom-hbar-legend" not found');
                                }
                            }
                        }]
                    }
                };
                if (locationBarChart) {
                    locationBarChart.destroy()
                }
                // Create the chart
                locationBarChart = new Chart(locationCTX, locationConfig);
                console.log("here");


                const element = document.getElementById('industry-header');
                if (element) {
                    element.scrollIntoView({
                        behavior: 'smooth'
                    });
                }

            }, 1000);


        });
    });
</script>
