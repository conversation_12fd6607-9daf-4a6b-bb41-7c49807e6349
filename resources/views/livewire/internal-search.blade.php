<div x-data="{ show: {} }" class="customHeight grayBackground overflow-y-scroll">
    <div class="flex flexRow flex-col lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
        <div class="flex gap-4 items-center">
            <a href="{{ route('plan.show', ['plan' => $plan]) }}" class="flex gap-2 mainBlue items-center">
                <img class="" src="{{ asset('images/ArrowLeftBlue.svg') }}">
                <span>Back</span>
            </a>
            <h1 class="whitespace-nowrap text-3xl font-medium">Internal Candidate Search</h1>
        </div>
    </div>
    <div class="my-5 px-4 flex flex-col gap-y-10">
        <!-- Visual Layer for Search for internal candidates -->
        <div class="grid grid-cols-2 gap-5">
            <div class="flex flex-col gap-y-2 px-5 py-5 bg-white border rounded-2xl shadow-md">
                <h1 class="text-sm text-left chart-heading">Readiness</h1>
                <div class="flex doughnut-chart-container gap-x-5 justify-between items-center">
                    <canvas id="readinessChart"></canvas>
                    <ul id="custom-readiness-legend" class="custom-h-legend whitespace-nowrap"></ul>
                </div>
            </div>
            <div class="flex flex-col gap-y-2 px-5 py-5 bg-white border rounded-2xl shadow-md">
                <h1 class="text-sm text-left chart-heading">Potential Sex Diversity</h1>
                <div class="flex doughnut-chart-container gap-x-5 justify-between items-center">
                    <canvas id="genderSplitChart"></canvas>
                    <ul id="custom-gender-legend" class="custom-h-legend whitespace-nowrap"></ul>
                </div>
            </div>
        </div>
        <div class="grid grid-cols-2 gap-5">
            <div class="flex flex-col gap-y-3 px-5 py-5 bg-white border rounded-2xl shadow-md">
                <h1 class="text-sm text-left chart-heading">Top 5 Functions Identified</h1>
                <div class="flex division-vbar-chart-container">
                    <canvas id="functionBar"></canvas>
                    <ul id="custom-function-legend" class="custom-vbar-legend custom-h-legend whitespace-nowrap"></ul>
                </div>
            </div>
            <div class="flex flex-col gap-y-2 px-5 py-5 bg-white rounded-2xl border shadow-md">
                <h1 class="text-sm text-left chart-heading">Top 5 Locations Identified</h1>
                <div class="flex division-vbar-chart-container">
                    <canvas id="locationBar"></canvas>
                    <ul id="custom-location-legend" class="custom-vbar-legend custom-h-legend whitespace-nowrap"></ul>
                </div>
            </div>
        </div>
        <!-- Section for searching and filtering -->
        <div x-data="{
                    drawer: false,
                    addToPlanPopup : @entangle('addToPlanPopup'),
                    addToPlansArray: @entangle('addToPlansArray'),
                    checkSelection() {
                        if (Object.values(this.addToPlansArray).every(value => !value)) {
                            toastr.info('Please select plan!');
                            return false;
                        }
                        return true;
                    },
                    }" class="flex flex-col gap-y-2 bg-white px-2 py-2 rounded-lg border">
            <div class="flex gap-x-2 py-3 rounded-xl"  x-data="{ dallopen: false, selected: @entangle('selectedPerson') }">
                <button @click="drawer = true" class="py-1.5 px-1.5 text-black  border rounded-lg flex justify-center items-center gap-1 font-medium">
                    <img class="search-icon w-auto mr-2" src="{{ asset('/images/search-icon.svg') }}" alt="Search Icon">
                    <span class="text-sm font-semibold whitespace-nowrap">Advanced Search</span>
                </button>
                <div class="flex flex-1 gap-x-2 px-2 py-2 border border-[#EAECF0] justify-start items-center bg-white rounded-lg">
                    <input wire:model="searchByKeyword" class="bg-transparent flex-grow focus:outline-none" type="text" placeholder="Search">
                    <img wire:click="searchPeople" class="search-icon w-auto mr-2" src="{{ asset('/images/search-icon.svg') }}" alt="Search Icon">
                </div>
               <!-- <button wire:click="searchPeople" class="py-1.5 px-3 text-white bg-cyan-500 border rounded-lg flex justify-center items-center gap-1 font-medium">
                    <img class="search-icon w-auto mr-2" src="{{ asset('/images/MagnifyingGlassWhite.svg') }}" alt="Search Icon">
                    <span class="text-sm font-semibold whitespace-nowrap">Search</span>

                </button>-->
                

                <button @click.prevent="if (selected && Object.keys(selected).length > 0) { $wire.addSelectedToPlan(); }"  class="py-1.5 px-3 border rounded-lg flex justify-center items-center gap-1 font-medium">
                            <img class="add-icon w-auto mr-2" src="{{asset('images/pluswithcircle.svg')}}" alt="Add to Plan Icon">
                            <span class="text-md font-semibold">Add to Plan</span>
                        </button>
                        <button @click.prevent="if (selected && Object.keys(selected).length > 0) { $wire.addSelectedToPotentialCandidate(); }"  class="py-1.5 px-3  border rounded-lg flex justify-center items-center gap-1 font-medium">
                            <img class="add-icon w-auto mr-2" src="{{asset('images/pluswithcircle.svg')}}" alt="Add to Potential Candidate Icon">
                            <span class="text-md font-semibold">Add to Potential Candidate</span>
                        </button>
                       
             




                <div x-data="{open: false}" class="relative z-40 sortBy-width">
                    <div @click="open = !open" class="flex px-2 py-3 border cursor-pointer border-[#EAECF0] justify-between items-center bg-white rounded-lg">
                        <h2 class="text-sm font-semibold">Sort by</h2>
                        <img class="h-5 w-5" src="{{asset('images/caret-down.svg')}}" alt="">
                    </div>
                    <div x-show="open" @click.away="open = false" class="select-items absolute w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                        <div class="flex items-center px-4 py-2 hover:bg-gray-100 ">
                            <input @click="open = false" type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="tenure" id="allStatus" wire:click="Ordermytable('tenure')">
                            <label class="closedColor text-semibold p-1 rounded-lg px-2 text-xs block cursor-pointer" for="allStatus">Tenure</label>
                        </div>
                        <div class="flex items-center px-4 py-2 hover:bg-gray-100 ">
                            <input @click="open = false" type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="created_at" id="activeStatus" wire:click="Ordermytable('created_at')">
                            <label class="activeColor p-1 rounded-lg px-2 text-xs block cursor-pointer" for="activeStatus">Date Added</label>
                        </div>
                    </div>
                </div>
            </div>
            <div x-data="{ open: {} }" class="h-96 w-full flex flex-col justify-between shadow-sm overflow-y-scroll relative rounded-lg border">
                <table class="min-w-max divide-y divide-gray-300 rounded-lg border">
                    <thead class="sticky top-0 z-30 rounded-lg">
                        <tr class="table-header">
                        <th scope="col" class="flex items-center px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">  <div class="flex flex-col gap-y-1 text-start mr-2">
                                        <input type="checkbox" x-on:click="$dispatch('select-all', $event.target.checked); $wire.set('selectedPerson', $event.target.checked ? @json($internalpeople->pluck('id')) : [])" x-bind:checked="$wire.selectedPerson.length > 0" class="form-checkbox h-4 w-4">
                                        </div> Name</th>
                            <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Country</th>
                            <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Company</th>
                            <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Function</th>
                            @if($plandetails->ethnicity === 1)
                            <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Potential Diversity</th>
                            @endif
                            <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Potential Sex</th>
                            <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Tenure</th>
                            <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Readiness</th>
                            <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Regisration Status</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">
                        @if(count($internalpeople) > 0)
                        @foreach ($internalpeople as $Person)
                        <tr>
                        <td class="whitespace-nowrap flex text-center text-gray-900 px-2 max-w-min">
                                  <div class="flex flex-col gap-y-1 text-start mr-2 mt-2">
                                        <input type="checkbox" wire:model="selectedPerson" value="{{ $Person->id }}" class="form-checkbox h-4 w-4">
                                    </div>
                                    <div class="flex flex-col gap-y-1 text-start">
                                        <h3 class="text-sm mt-1 font-semibold whitespace-nowrap">{{ $Person->forename }} {{ $Person->surname }} </h3>
                                        <h3 class="text-xs table-column-name font-semibold text-gray-800 whitespace-normal">{{ $Person->latest_role }}</h3>
                                    </div>
                                </td>
                            <td class="whitespace-wrap text-center text-xs text-gray-500">{{ $Person->country }}</td>
                            <td class="whitespace-wrap text-center text-xs text-gray-500">{{ $Person->company_name }}</td>
                            <td class="whitespace-wrap text-center text-xs text-gray-500">{{ $Person->function }}</td>
                            @if($plandetails->ethnicity === 1)
                            <td class="text-center text-xs text-gray-500">{{ $Person->diverse }}</td>
                            @endif
                            <td class="text-center text-xs text-gray-500">{{ $Person->gender }}</td>
                            <td class="text-center text-xs text-gray-500">{{ $Person->tenure }}</td>
                            <td class=" px-3 py-3 text-center text-xs w-32">
                                @if($Person->readiness == 'Ready')
                                <span class="py-2 px-4 rounded-lg font-medium redlinessReaddy">
                                    Ready
                                </span>
                                @else
                                <span class="py-2 px-4 rounded-lg font-medium RedBG text-red-500">
                                    Not Ready
                                </span>
                                @endif
                            </td>
                            <td class="text-center text-xs text-gray-500">{{ $Person->other_tags }}</td>
                            <td x-data="{ open: false, vopen: false, dopen: false }" class="px-2 py-2 relative">
                                <img @click="open = !open" class="h-5 w-5 cursor-pointer" src="{{asset('images/kebab-icon.svg')}}" alt="">
                                <div class="relative">
                                    <div x-show="open" @click.away="open = false" class="absolute flex flex-col justify-start right-0 bg-white border border-gray-200 rounded-md shadow-lg z-20 table-dropdown">
                                        <div @click="vopen = true, open = false" wire:click="viewIndividual({{ $Person->id }})" class="px-2 py-2 text-gray-800 hover:bg-gray-100 text-sm whitespace-nowrap flex items-center gap-x-2 justify-start cursor-pointer">
                                            <img class="h-4 w-4" src="{{asset('images/eye.svg')}}" alt="">
                                            <h2 class="text-xs">View</h2>
                                        </div>
                                        <div @click="open = false" wire:click.prevent="showAddToPlanPopup({{ $Person->id }})" class="flex items-center gap-x-2 px-2 py-2 text-gray-800 hover:bg-gray-100 text-sm whitespace-nowrap justify-start cursor-pointer">
                                            <img class="h-4 w-4" src="{{asset('images/pluswithcircle.svg')}}" alt="">
                                            <h2 class="text-xs">Add to Plan</h2>
                                        </div>
                                        <div @click="open = false" wire:click.prevent="addToPotentialCandidate({{ $Person->id }})" class="flex items-center gap-x-2 px-2 py-2 text-gray-800 hover:bg-gray-100 text-sm whitespace-nowrap justify-start cursor-pointer">
                                            <img class="h-4 w-4" src="{{asset('images/pluswithcircle.svg')}}" alt="">
                                            <h2 class="text-xs">Add to Potential Candidate</h2>
                                        </div>
                                    </div>
                                </div>
                                @if($selectedIndividualID === $Person->id)
                                <!-- Modal container -->
                                <div x-show="vopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50" style="display:none">
                                    <!-- Modal background with a higher z-index -->
                                    <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                                    <!-- Modal content with a lower z-index -->
                                    <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50" x-show="vopen" x-transition>
                                        <!-- Modal content -->
                                        <div class="flex flex-col h-full">
                                            <div class="flex justify-between border-b px-2 py-2">
                                                <h3 class="text-base font-bold">{{ $Person->company_name }}</h3>
                                                <img @click="vopen = false" class="h-5 w-5 cursor-pointer" src="{{asset('images/cross.svg')}}" alt="">
                                            </div>
                                            <div class="grid grid-cols-2 gap-x-2 pt-3 flex-1">
                                                <div class="flex flex-1 flex-col justify-between">
                                                    <div class="flex px-2 py-2 justify-between">
                                                        <div class="flex flex-col gap-y-2">
                                                            <h3 class="text-base font-bold">{{ $Person->forename }} {{ $Person->surname }}</h3>
                                                            <h3 class="text-base text-gray-700">{{ $Person->latest_role }}</h3>
                                                        </div>
                                                        <div class="flex gap-x-2 items-center justify-center">
                                                            <div class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                                                <img class="h-5 w-5" src="{{asset('images/Plus.svg')}}" alt="">
                                                                <h2 @click="addToPlanPopup = true" class="text-sm font-semibold">Add to Plan</h2>
                                                            </div>
                                                            @if($Person->linkedinURL != 'NA')
                                                            <a wire:ignore target="_blank" id="linkedin" class="flex items-center justify-center hover:scale-105" href="{{ $Person->linkedinURL }}">
                                                                <img class="h-8 w-8" src="{{ asset('images/linkedinlogo.png') }}">
                                                            </a>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div class="grid grid-cols-2 gap-2 flex-grow {{$plandetails->ethnicity == 1 ? 'grid-row-4' : 'grid-rows-3'}}">
                                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                            <h3 class="text-sm font-bold chart-heading">Potential Sex</h3>
                                                            <h3 class="text-md chart-heading">{{ $Person->gender}}</h3>
                                                        </div>
                                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                            <h3 class="text-sm font-bold chart-heading">Function</h3>
                                                            <h3 class="text-md chart-heading">{{ $Person->function}}</h3>
                                                        </div>
                                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                            <h3 class="text-sm font-bold chart-heading">Division</h3>
                                                            <h3 class="text-md chart-heading">{{ $Person->division}}</h3>
                                                        </div>
                                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                            <h3 class="text-sm font-bold chart-heading">Tenure in Role</h3>
                                                            <h3 class="text-md chart-heading">{{ $Person->tenure ? $Person->tenure : "Not Applicabale"}}</h3>
                                                        </div>
                                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                            <h3 class="text-sm font-bold chart-heading">Readiness</h3>
                                                            @if($Person->readiness == 'Ready')
                                                            <h3 class="py-1 px-2 text-xs w-max rounded-lg font-medium redlinessReaddy">{{ $Person->readiness}}</h3>
                                                            @else
                                                            <h3 class="py-1 px-2 text-xs w-max rounded-lg font-medium RedBG text-red-500">{{ $Person->readiness}}</h3>
                                                            @endif
                                                        </div>
                                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                            <h3 class="text-sm font-bold chart-heading">Registrations</h3>
                                                            <h3 class="text-md chart-heading">{{ $Person->other_tags ? $Person->other_tags : 'Not Applicable'}}</h3>
                                                        </div>
                                                        @if($plandetails->ethnicity === 1)
                                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                            <h3 class="text-sm font-bold chart-heading">Potential Diversity</h3>
                                                            <h3 class="text-md chart-heading">{{ $Person->diverse ? $Person->diverse : 'Not Applicabale'}}</h3>
                                                        </div>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div x-data="{tab: 'summary', nopen: false, enopen: false}" class="flex flex-col pt-3 gap-y-2">
                                                    <div class="flex border-b-2">
                                                        <button @click="tab = 'summary'" class="w-full text-sm py-3 px-2 font-medium z-20" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'summary', 'chart-heading font-semibold': tab != 'summary' }">
                                                            Summary
                                                        </button>
                                                        <button @click="tab = 'career_history'" class="w-full text-sm py-3 px-2 font-medium z-20" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'career_history', 'chart-heading font-semibold': tab != 'career_history' }">
                                                            Career History
                                                        </button>
                                                        <button @click="tab = 'skills'" class="w-full text-sm py-3 px-2 font-medium z-20" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'skills', 'chart-heading font-semibold': tab != 'skills' }">
                                                            Skills
                                                        </button>
                                                        @if(!empty($internalUserPlans))

                                                            <button @click="tab = 'plans'" class="w-full text-sm py-3 px-2 font-medium"
                                                                :class="{
                                                                    'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                                        'plans',
                                                                    'chart-heading font-semibold': tab != 'plans'
                                                                }">
                                                                Plans
                                                            </button>
                                                        @endif
                                                    </div>
                                                    <div x-show="tab == 'summary'" class="{{$plandetails->ethnicity == 1 ? 'h-96' : 'h-80'}} overflow-hidden overflow-y-scroll flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                        <h4 class="text-sm text-gray-700 font-medium chart-heading">Summary</h4>
                                                        <p class="text-sm">{{ $Person->summary ? $Person->summary : 'No summary available' }}</p>
                                                    </div>
                                                    <div x-show="tab == 'career_history'" class="{{$plandetails->ethnicity == 1 ? 'h-96' : 'h-80'}} overflow-hidden overflow-y-scroll flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                        <h4 class="text-sm text-gray-700 font-medium chart-heading">Career History</h4>
                                                        @if($groupedPeoplesCareer && $groupedPeoplesCareer->isNotEmpty())
                                                        <div class="flex flex-col items-start w-full">
                                                            @foreach ($groupedPeoplesCareer as $careerHistory)
                                                            <div class="flex h-max items-start justify-center mb-1">
                                                                <div class="flex flex-col mt-1 items-center justify-center h-full w-2">
                                                                    <div class="rounded-full  blueBalls bg-mainBlue"></div>
                                                                    <div class="flex-grow border-l border-mainBlue"></div>
                                                                </div>
                                                                <div class="flex flex-col  items-start justify-start pl-4">
                                                                    <h4 class="text-sm font-semibold text-gray-900">{{$careerHistory->role}}</h4>
                                                                    <div class="flex gap-x-2">
                                                                        <span class="text-sm  font-base text-gray-700">{{ $careerHistory->company_name }}</span>
                                                                    </div>
                                                                    <div class="flex gap-x-2 mb-4">
                                                                        <span class="text-xs font-base GrayText">{{ \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y') }} - {{ !empty($careerHistory->end_date) ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : "Present" }}</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            @endforeach
                                                        </div>
                                                        @else
                                                        <p class="text-sm text-gray-500 mt-5">No career history available</p>
                                                        @endif
                                                    </div>
                                                    <div x-show="tab == 'skills'" class="{{$plandetails->ethnicity == 1 ? 'h-96' : 'h-80'}} overflow-hidden overflow-y-scroll flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                        <h4 class="text-sm text-gray-700 font-medium chart-heading">Skills</h4>
                                                        @if(!empty($internalCoSkills))
                                                        <h4 class="text-sm text-gray-700 font-medium chart-heading">Common Skills</h4>
                                                        <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                                            @foreach ($internalCoSkills as $co_skill)
                                                            <div class="flex items-center text-xs p-2 rounded-xl skill-container chart-heading">
                                                                <span>{{ $co_skill->skill_name }}</span>
                                                            </div>
                                                            @endforeach
                                                        </div>
                                                        @else
                                                        <h4 class="text-xs text-gray-700 font-medium">No Common Skills Assigned</h4>
                                                        @endif
                                                        @if(!empty($internalSpSkills))
                                                        <h4 class="text-sm text-gray-700 font-medium chart-heading">Special Skills</h4>
                                                        <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                                            @foreach ($internalSpSkills as $sp_skill)
                                                            <div class="flex items-center text-xs p-2 rounded-xl skill-container chart-heading">
                                                                <span>{{ $sp_skill->skill_name }}</span>
                                                            </div>
                                                            @endforeach
                                                        </div>
                                                        @else
                                                        <h4 class="text-xs text-gray-700 font-medium">No Special Skills Assigned</h4>
                                                        @endif
                                                        @if(!empty($internalSpSkills))
                                                        <h4 class="text-sm text-gray-700 font-medium chart-heading">Professional Skills</h4>
                                                        <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                                            @foreach ($internalPSkills as $p_skill)
                                                            <div class="flex items-center text-xs p-2 rounded-xl skill-container chart-heading">
                                                                <span>{{ $p_skill->skill_name }}</span>
                                                            </div>
                                                            @endforeach
                                                        </div>
                                                        @else
                                                        <h4 class="text-xs text-gray-700 font-medium">No Professional Skills Assigned</h4>
                                                        @endif
                                                    </div>

                                                    @if(!empty($internalUserPlans))
                                                        <div x-show="tab == 'plans'"
                                                            class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Plans</h4>
                                                            @if ($internalUserPlans && $internalUserPlans->isNotEmpty())
                                                                <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                                                    @foreach ($internalUserPlans as $plan)
                                                                        <div
                                                                            class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                                                                            <span>{{ $plan->name }}</span>
                                                                        </div>
                                                                    @endforeach
                                                                </div>
                                                            @else
                                                                <p class="text-sm text-gray-500 mt-5">No plans available</p>
                                                            @endif
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </td>

                        </tr>
                        @endforeach
                        @else
                        <div class="absolute bottom-36 w-full">
                            <h1 class="text-center chart-heading">No People Found</h1>
                        </div>
                        @endif
                    </tbody>
                </table>
                <div class="py-4 px-2 sticky bottom-0 bg-white">
                    {{ $internalpeople->links() }}
                </div>
            </div>
            <div x-show="addToPlanPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
                <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
                <div x-data="{openPlanPopup: @entangle('openPlanPopup') }" class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
                    <div class="flex justify-between">
                        <h2 class="text-black-900 text-xl font-semibold">Add to plan</h2>
                        <button type="button" @click="addToPlanPopup = false" class="text-gray-500 hover:text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- select people box -->
                    <div class="flex justify-center items-center mt-2">
                        <div class="bg-white border selectPeople p-2 rounded-lg">
                            <!-- User list with checkboxes -->
                            <ul class="mt-4 adddeduser space-y-6 py-4 overflow-y-auto" style="max-height: 300px;">
                                @if(!empty($plansList) && $plansList->isNotEmpty())
                                    @foreach ($plansList as $plan)
                                    <div class="w-full border-t my-1 border-gray-200"></div>
                                        <li class="flex justify-between">
                                            <div class="flex items-center gap-2 pl-4">
                                                <div class="space-y-1">
                                                    <span class="text-sm font-semibold block">{{ $plan->name}}</span>
                                                </div>
                                            </div>
                                            {{-- Checkbox for selecting direct reports --}}
                                            <input type="checkbox"
                                                wire:model="addToPlansArray.{{ $plan->id }}"
                                                id="addDirectReportPeople-{{ $plan->id }}"
                                                class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out"
                                                >
                                        </li>
                                        <div class="w-full border-t my-1 border-gray-200"></div>

                                    @endforeach
                                    @else 
                                    <div class="w-full border-t my-1 border-gray-200"></div>
                                        <li class="flex justify-between">No plans found!</li>
                                        <div class="w-full border-t my-1 border-gray-200"></div>
                                    @endif
                                    
                            </ul>
                            <div class="flex justify-end mt-4">
                                <!-- Submit the form when the button is clicked -->
                                <button type="button" class="p-2 rounded-lg"
                                @click="openPlanPopup ? $wire.addSelectedPersonToPlans() : (checkSelection() && $wire.addpeopleToPlans())">Add</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-show="drawer" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50 right-0" style="display:none">
                <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                <div x-show="drawer" x-transition:enter="transition transform ease-out duration-300" x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition transform ease-in duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full" class="drawer z-50 bg-white absolute right-0 shadow-lg overflow-hidden">
                    <div class="flex justify-between mt-2 h-5">
                        <h2 class="font-semibold">
                            Advanced Search
                        </h2>
                        <img @click="drawer = false" class="h-5 w-5 cursor-pointer" src="{{asset('images/cross.svg')}}" alt="">
                    </div>
                    <div class="border-t mt-3 border-gray-200"></div>
                    <div class="py-3 advanced_search_drawer relative overflow-y-scroll">
                        <div class="flex gap-y-2 flex-col">
                            <div class="">
                                <label for="name" class="block text-xs font-medium labelcolor">First Name</label>
                                <input wire:model="forename" type="text" id="name" placeholder="Enter first name" name="name" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                            </div>
                            <div class="">
                                <label for="name" class="block text-xs font-medium labelcolor">Last Name</label>
                                <input wire:model="surname" type="text" id="name" placeholder="Enter last name" name="name" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                            </div>
                            <div class="">
                                <fieldset>
                                    <legend class="text-xs font-medium labelcolor">Potential Sex</legend>
                                    <ul class="donate-now mt-1">
                                        <li>
                                            <input class="cursor-pointer" type="radio" id="Male" name="gender" value="Male" wire:model="gender" />
                                            <label for="Male" class="text-center font-semibold labelcolor">Male</label>
                                        </li>
                                        <li>
                                            <input class="cursor-pointer" type="radio" id="Female" name="gender" value="Female" wire:model="gender" />
                                            <label for="Female" class="text-center font-semibold labelcolor">Female</label>
                                        </li>
                                    </ul>
                                </fieldset>
                            </div>
                            <div class="flex flex-col gap-y-2">
                                <h2 class="block text-xs font-medium labelcolor">Location</h2>
                                <div class="text-xs" wire:ignore x-data="{
                                                    multiple: true,
                                                    value: @entangle('location'),
                                                    options: {{ json_encode($locations) }},
                                                    init() {
                                                            this.$nextTick(() => {
                                                                let choices = new Choices(this.$refs.select)
                                                
                                                                let refreshChoices = () => {
                                                                    let selection = this.multiple ? this.value : [this.value]
                                                
                                                                    choices.clearStore()
                                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                                        value,
                                                                        label,
                                                                        selected: selection.includes(value),
                                                                    })))
                                                                }
                                                
                                                                refreshChoices()
                                                
                                                                this.$refs.select.addEventListener('change', () => {
                                                                    this.value = choices.getValue(true)
                                                                })
                                                
                                                                this.$watch('value', () => refreshChoices())
                                                                this.$watch('options', () => refreshChoices())
                                                            })
                                                        }
                                                    }" class="bg-white text-xs max-w-sm w-full">
                                    <select x-ref="select"></select>
                                </div>
                            </div>
                            <div class="">
                                <label for="_role" class="block text-md font-medium labelcolor">Role</label>
                                <input wire:model="_role" type="text" id="_role" placeholder="Enter role" name="_role" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                            </div>


                            <div class="flex flex-col gap-y-2">
                                <h2 class="block text-xs font-medium labelcolor">Function</h2>
                                <div class="text-xs" wire:ignore x-data="{
                                                    multiple: true,
                                                    value: @entangle('function'),
                                                    options: {{ json_encode($functions) }},
                                                    init() {
                                                            this.$nextTick(() => {
                                                                let choices = new Choices(this.$refs.select)
                                                
                                                                let refreshChoices = () => {
                                                                    let selection = this.multiple ? this.value : [this.value]
                                                
                                                                    choices.clearStore()
                                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                                        value,
                                                                        label,
                                                                        selected: selection.includes(value),
                                                                    })))
                                                                }
                                                
                                                                refreshChoices()
                                                
                                                                this.$refs.select.addEventListener('change', () => {
                                                                    this.value = choices.getValue(true)
                                                                })
                                                
                                                                this.$watch('value', () => refreshChoices())
                                                                this.$watch('options', () => refreshChoices())
                                                            })
                                                        }
                                                    }" class="bg-white text-xs max-w-sm w-full">
                                    <select x-ref="select"></select>
                                </div>
                            </div>
                            <div class="">
                                <label for="name" class="block text-xs font-medium labelcolor">Regulatory Bodies</label>
                                <input wire:model="regBodies" type="text" id="name" placeholder="Enter Regulatory Bodies" name="name" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                            </div>
                            <div class="mt-2">
                                <label for="name" class="block text-xs font-medium labelcolor">Minimum Tenure</label>
                                <div class="Tenure mt-1 justify-between">
                                    {{--
                                    <button
                                        class="block w-8 border-2 px-1 rounded-md @if (!$min_exp) disabled @endif"
                                        wire:click="changeMinimumTenure('decrease')"
                                        @if (!$min_exp) disabled @endif>
                                        <img class="search-icon w-8 h-8 bg-white"
                                            src="{{ asset('images/Minus.svg') }}"
                                    alt="Search Icon">
                                    </button>
                                    --}}
                                    <input type="number" wire:model="min_exp" min="0" inputmode="numeric" class="w-[75%] outline-none block text-center bg-white p-2 text-md font-normal border border-gray-300 h-10 rounded-md w-full" placeholder="0">
                                    {{--
                                    <button class="block w-8 px-1 border-2  rounded-md"
                                        wire:click="changeMinimumTenure('increase')">
                                        <img class="search-icon w-8 h-8  bg-white"
                                            src="{{ asset('images/Plus.svg') }}"
                                    alt="Search Icon">
                                    </button>
                                    --}}
                                </div>
                            </div>
                        </div>
                        <div class="sticky bottom-0 w-full border-t mt-6 pt-2 pb-2 flex items-center justify-between bg-white">
                            <button class="border text-sm text-black rounded-xl py-2 advanced-search-clear font-medium" wire:click="clearFilters" @click='drawer = false'>Reset</button>
                            <button class="border text-sm text-white rounded-xl py-2 advanced-search-submit font-medium bg-cyan-500 hover:text-white" wire:click="runfilters" @click='drawer = false'>Show Results</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('livewire.loading')
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var functionBarChart
        var locationBarChart
        var divisonBarChart
        var genderChart
        const genderCTX = document.getElementById('genderSplitChart').getContext('2d');
        const _genderLabels = @json($genderLabels);
        const _genderData = @json($genderData);
        const genderData = {
            labels: _genderLabels,
            datasets: [{
                label: 'My Organization',
                data: _genderData,
                backgroundColor: [
                    'rgb(255, 99, 132)',
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)'
                ],
                hoverOffset: 4
            }]
        };

        const genderConfig = {
            type: 'doughnut',
            data: genderData,
            options: {
                animation: false,
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                cutout: '80%', // Adjusts the inner radius, making the doughnut thicker or thinner
                plugins: {
                    dataLabals: {
                        show: false
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(tooltipItem) {
                                const dataset = tooltipItem.dataset;
                                const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                const currentValue = dataset.data[tooltipItem.dataIndex];
                                const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                return `${currentValue} (${percentage}%)`;
                            }
                        }
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-gender-legend');
                    ul.innerHTML = '';
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                        ul.appendChild(li);
                    });
                }
            }]
        };
        genderChart = new Chart(genderCTX, genderConfig);

        const readinessCTX = document.getElementById('readinessChart').getContext('2d');
        const _readinessLabels = @json($readyLabels);
        const _readinessData = @json($readyData);
        const readinessData = {
            labels: _readinessLabels,
            datasets: [{
                label: 'My Organization',
                data: _readinessData,
                backgroundColor: [
                    'rgb(255, 99, 132)',
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)'
                ],
                hoverOffset: 4
            }]
        };

        const readinessConfig = {
            type: 'doughnut',
            data: readinessData,
            options: {
                animation: false,
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                cutout: '80%', // Adjusts the inner radius, making the doughnut thicker or thinner
                plugins: {
                    dataLabals: {
                        show: false
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(tooltipItem) {
                                const dataset = tooltipItem.dataset;
                                const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                const currentValue = dataset.data[tooltipItem.dataIndex];
                                const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                return `${currentValue} (${percentage}%)`;
                            }
                        }
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-readiness-legend');
                    ul.innerHTML = '';
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                        ul.appendChild(li);
                    });
                }
            }]
        };
        new Chart(readinessCTX, readinessConfig);

        const functionData = {
            labels: @json($functionLabels),
            datasets: [{
                label: 'People',
                data: @json($functionData),
                backgroundColor: [
                    '#3B82F6',
                    '#FFA347',
                    '#8B5CF6',
                ]
            }]
        };

        const functionConfig = {
            type: 'bar',
            data: functionData,
            options: {
                animation: false,
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                indexAxis: 'y',
                borderRadius: {
                    bottomLeft: 10,
                    bottomRight: 10,
                    topRight: 10,
                    topLeft: 10
                },
                scales: {
                    x: {
                        display: false, // Hide x-axis
                        beginAtZero: true // Start y-axis at zero
                    },
                    y: {
                        border: {
                            display: false,
                        },
                        grid: {
                            display: false, // Hide grid lines
                        },
                        ticks: {
                            display: true // Show y-axis labels
                        },
                        axis: {
                            display: false // Hide y-axis line
                        }
                    }
                },
                plugins: {
                    datalabels: {
                        display: true,
                        anchor: 'center',
                        align: 'center',
                        formatter: function(value, context) {
                            return value + ' (' + (value / context.dataset._meta[0].total * 100).toFixed(2) + '%)';
                        }
                    },
                    legend: {
                        display: false
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-function-legend');
                    ul.innerHTML = '';
                    // ul.style.justifyContent = 'space-between'
                    // ul.style.alignItems = 'center'
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.classList.add('child')
                        li.id = 'percentage'
                        li.style.marginBottom = '0px'
                        li.innerHTML = `${value}`;
                        ul.appendChild(li);
                    });
                    const children = ul.children;
                    const numChildren = children.length;
                    if (numChildren > 1) {
                        const maxPadding = 7;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    } else {
                        ul.style.justifyContent = 'center'
                    }
                    if (numChildren == 2) {
                        const maxPadding = 10;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    }
                    // Calculate the margin based on the number of children
                }
            }]
        };

        const divisionData = {
            labels: @json($divisionLabels),
            datasets: [{
                label: 'People',
                data: @json($divisionData),
                backgroundColor: [
                    '#3B82F6',
                    '#FFA347',
                    '#8B5CF6',
                ]
            }]
        };

        const divisionConfig = {
            type: 'bar',
            data: divisionData,
            options: {
                animation: false,
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                indexAxis: 'y',
                borderRadius: {
                    bottomLeft: 10,
                    bottomRight: 10,
                    topRight: 10,
                    topLeft: 10
                },
                scales: {
                    x: {
                        display: false, // Hide x-axis
                        beginAtZero: true // Start y-axis at zero
                    },
                    y: {
                        border: {
                            display: false,
                        },
                        grid: {
                            display: false, // Hide grid lines
                        },
                        ticks: {
                            display: true // Show y-axis labels
                        },
                        axis: {
                            display: false // Hide y-axis line
                        }
                    }
                },
                plugins: {
                    datalabels: {
                        display: true,
                        anchor: 'center',
                        align: 'center',
                        formatter: function(value, context) {
                            return value + ' (' + (value / context.dataset._meta[0].total * 100).toFixed(2) + '%)';
                        }
                    },
                    legend: {
                        display: false
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-division-legend');
                    ul.innerHTML = '';
                    // ul.style.justifyContent = 'space-between'
                    // ul.style.alignItems = 'center'
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.classList.add('child')
                        li.id = 'percentage'
                        li.style.marginBottom = '0px'
                        li.innerHTML = `${value}`;
                        ul.appendChild(li);
                    });
                    const children = ul.children;
                    const numChildren = children.length;
                    if (numChildren > 1) {
                        const maxPadding = 7;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    } else {
                        ul.style.justifyContent = 'center'
                    }
                    if (numChildren == 2) {
                        const maxPadding = 10;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    }
                    // Calculate the margin based on the number of children
                }
            }]
        };

        const locationData = {
            labels: @json($locationLabels),
            datasets: [{
                label: 'People',
                data: @json($locationData),
                backgroundColor: [
                    '#3B82F6',
                    '#FFA347',
                    '#8B5CF6',
                ]
            }]
        };

        const locationConfig = {
            type: 'bar',
            data: locationData,
            options: {
                animation: false,
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                indexAxis: 'y',
                borderRadius: {
                    bottomLeft: 10,
                    bottomRight: 10,
                    topRight: 10,
                    topLeft: 10
                },
                scales: {
                    x: {
                        display: false, // Hide x-axis
                        beginAtZero: true // Start y-axis at zero
                    },
                    y: {
                        border: {
                            display: false,
                        },
                        grid: {
                            display: false, // Hide grid lines
                        },
                        ticks: {
                            display: true // Show y-axis labels
                        },
                        axis: {
                            display: false // Hide y-axis line
                        }
                    }
                },
                plugins: {
                    datalabels: {
                        display: true,
                        anchor: 'center',
                        align: 'center',
                        formatter: function(value, context) {
                            return value + ' (' + (value / context.dataset._meta[0].total * 100).toFixed(2) + '%)';
                        }
                    },
                    legend: {
                        display: false
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-location-legend');
                    ul.innerHTML = '';
                    // ul.style.justifyContent = 'space-between'
                    // ul.style.alignItems = 'center'
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.classList.add('child')
                        li.id = 'percentage'
                        li.style.marginBottom = '0px'
                        li.innerHTML = `${value}`;
                        ul.appendChild(li);
                    });
                    const children = ul.children;
                    const numChildren = children.length;
                    if (numChildren > 1) {
                        const maxPadding = 7;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    } else {
                        ul.style.justifyContent = 'center'
                    }
                    if (numChildren == 2) {
                        const maxPadding = 10;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    }
                }
            }]
        };

        // Create the chart
        divisonBarChart = new Chart(
            document.getElementById('divisionBar'),
            divisionConfig
        );


        functionBarChart = new Chart(
            document.getElementById('functionBar'),
            functionConfig
        );

        locationBarChart = new Chart(
            document.getElementById('locationBar'),
            locationConfig
        );

        // Listen for Livewire events to update chart data
        Livewire.on('updateChart', () => {
            console.log("helloooo")
            if (functionBarChart) {
                functionBarChart.destroy()
            }
            functionBarChart = new Chart(
                document.getElementById('functionBar'),
                functionConfig
            );
            if (divisonBarChart) {
                divisonBarChart.destroy()
            }
            divisonBarChart = new Chart(
                document.getElementById('divisionBar'),
                divisionConfig
            );
            if (locationBarChart) {
                locationBarChart.destroy()
            }
            locationBarChart = new Chart(
                document.getElementById('locationBar'),
                locationConfig
            );
        });
    })
</script>