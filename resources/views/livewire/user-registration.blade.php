<div>
    @include('livewire.loading')
    <div class="card-body">
        <form wire:submit.prevent="sendVerification" autocomplete="off">
            <h1 class="text-3xl text-sky-500 font-bold leading-7">Sign-up</h1>
            <p class="mt-5 text-base font-light leading-6">Welcome to The Succession Plan, please enter your details. Once provided, we'll send you a verification email just to confirm it is you.</p>
            @csrf
            <div class="mt-5">
                <label for="name" class="block text-sm font-medium leading-6 text-gray-900">Name</label>
                <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input type="text" wire:model="name" id="name" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" placeholder="First Name" autocomplete="off">
                </div>
                @error('name')
                <span class="text-red-500 text-xs">{{ $message }}</span>
                @enderror
            </div>
            <div class="mt-5">
                <label for="password" class="block text-sm font-medium leading-6 text-gray-900">Password</label>
                <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input type="password" wire:model="password" id="password" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" autocomplete="new-password" placeholder="Enter password">
                </div>
                @error('password')
                <span class="text-red-500 text-xs">{{ $message }}</span>
                @enderror
            </div>
            <div class="mt-5">
                <label for="confirmedpassword" class="block text-sm font-medium leading-6 text-gray-900">Confirm Password</label>
                <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input type="password" wire:model="confirmedpassword" id="confirmedpassword" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" autocomplete="new-password" placeholder="Enter confirm password">
                </div>
                @error('confirmedpassword')
                <span class="text-red-500 text-xs">{{ $message }}</span>
                @enderror
            </div>
            <div class="flex items-center justify-center mt-5">
                <button type="submit" class="rounded-full bg-sky-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-sky-600">Register</button>
            </div>
        </form>
    </div>
</div>
