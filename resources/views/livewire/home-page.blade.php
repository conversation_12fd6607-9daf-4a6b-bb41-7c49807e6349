    <div class="customHeight grayBackground overflow-y-scroll">
        <div class="flex items-center px-4 py-4 justify-between border-b w-full bg-white">
            <h1 class="whitespace-nowrap text-3xl font-medium">Home</h1>
        </div>
        <div class="my-5 px-4">
            <div class="flex flex-col gap-10">
                <div class="grid grid-cols-2 gap-5">
                    <div class="grid grid-cols-2 gap-5">
                        <a href="{{ route('plan.index') }}" class="flex flex-col border cursor-pointer gap-3 bg-white rounded-xl py-3 justify-center items-center shadow-md">
                            <img class="h-20 w-auto" src="{{ asset('images/ca_1.svg') }}">
                            <div class="flex w-full justify-between items-center px-5">
                                <h1 class="text-sm font-semibold">View or Start Succession Plans</h1>
                                <img class="h-7 w-auto" src="{{ asset('images/arrow-right.svg') }}">
                            </div>
                        </a>
                        <a href="{{ route('company.index') }}" class="flex flex-col border cursor-pointer gap-3 bg-white rounded-xl py-3 justify-center items-center shadow-md">
                            <img class="h-20 w-auto" src="{{ asset('images/ca_2.svg') }}">
                            <div class="flex w-full justify-between items-center px-5">
                                <h1 class="text-sm font-semibold">Discover Company Insights</h1>
                                <img class="h-7 w-auto" src="{{ asset('images/arrow-right.svg') }}">
                            </div>
                        </a>
                        <a href="{{ route('internalpeople.index') }}" class="flex flex-col border cursor-pointer gap-3 bg-white rounded-xl py-3 justify-center items-center shadow-md">
                            <img class="h-20 w-auto" src="{{ asset('images/ca_3.svg') }}">
                            <div class="flex w-full justify-between items-center px-5">
                                <h1 class="text-sm font-semibold">View your Organisation</h1>
                                <img class="h-7 w-auto" src="{{ asset('images/arrow-right.svg') }}">
                            </div>
                        </a>
                        <a href="{{ route('job.index') }}" class="flex flex-col border gap-3 bg-white rounded-xl py-3 justify-center items-center shadow-md">
                            <img class="h-20 w-auto" src="{{ asset('images/job-search.svg') }}">
                            <div class="flex w-full justify-between items-center px-5">
                                <h1 class="text-sm font-semibold">View or Start a Talent Pool</h1>
                                <img class="h-7 w-auto" src="{{ asset('images/arrow-right.svg') }}">
                            </div>
                        </a>
                    </div>
                    <div class="h-96 bg-white border border-gray-200 rounded-xl px-4 py-4 shadow-md overflow-hidden">
                        <div class="flex w-full notification-header justify-between px-2 items-center">
                            <h2 class="text-xl font-semibold">Notifications</h2>
                            <div class="rounded-xl px-2 py-2 flex justify-center items-center border-2 cursor-pointer text-cyan-500 hover:text-white hover:bg-cyan-500">
                                <h2 class="text-base font-semibold" wire:click="deleteAllNotification">Mark all as read</h2>
                            </div>
                        </div>
                        <div class="relative py-2">
                            <div class="absolute inset-0 flex items-center" aria-hidden="true">
                                <div class="w-full border-t border-gray-200"></div>
                            </div>
                        </div>

                        <div class="notifications-container overflow-y-scroll">
                            <ul role="list" class="h-full">
                                @if(count($notifications) > 0)
                                @foreach ($notifications as $notification)
                                @if(in_array($notification->type, ['Plan_Created', 'Plan_Shared', 'Job_Shared']))
                                <li class="px-2 py-2 bg-white w-full"  type="{{$notification->type}}" id="notification-{{ $notification->id }}">
                                    <div class="flex w-full h-full justify-between items-center notification-container rounded-xl">
                                        {{-- 
                                            <div class="notification-icon flex justify-center items-center">
                                                <img class="h-8 w-8" src="{{ asset('images/notification_icon.svg') }}">
                                            </div>
                                        --}}
                                        <div class="flex flex-col gap-y-4 justify-start notification px-2 py-2">
                                            <h1 class="text-lg font-medium">
                                                {{ $notification->type === 'Plan_Created' 
                                        ? 'New Plan Added' 
                                        : ($notification->type === 'Plan_Shared' 
                                            ? 'Plan Shared' 
                                            : 'Job Shared') }}
                                            </h1>
                                            <p class="text-sm text-gray-900 w-full font-semibold whitespace-nowrap overflow-hidden text-ellipsis">
                                                {{ $notification->entity_name }}:
                                                <span class="font-normal whitespace-nowrap">{{ $notification->description }}</span>
                                            </p>
                                        </div>
                                        <div class="flex flex-col notification-icon items-center justify-center pt-2">
                                            <button @click="deleteNotificationAndRemove({{ $notification->id }})" class="text-gray-500 hover:text-red-500">
                                                <img src="{{ asset('images/bell_red.svg') }}" alt="" class="bell-icon">
                                            </button>
                                        </div>
                                    </div>
                                </li>
                                @elseif($notification->type == 'External_Added_to_plan')
                                <li class="px-2 py-2 bg-white w-full" type="{{$notification->type}}" id="notification-{{ $notification->id }}">
                                    <div class="w-full h-full">
                                        <div class="flex min-w-0 justify-between bg-white">
                                            <div class="flex w-full h-full justify-between items-center hover:bg-[#F2F4F7] rounded-xl">
                                                <div class="w-[10%] flex justify-center items-center">
                                                    <img class="h-8 w-8" src="{{ asset('images/notification_icon.svg') }}">
                                                </div>
                                                <div class="flex flex-col gap-4 justify-start w-[80%] p-2">
                                                    <h2 class="font-semibold w-5/6">{{ $notification->entity_name }} added to Plan</h2>
                                                    <time datetime="{{ $notification->created_at->format('Y-m-d') }}">{{ $notification->created_at->format('Y-m-d') }}</time>
                                                </div>
                                                <div class="flex flex-col w-[10%] items-center justify-center pt-2">
                                                    <button @click="deleteNotificationAndRemove({{ $notification->id }})" class="text-gray-500 hover:text-red-500">
                                                        <img src="{{ asset('images/bell_red.svg') }}" alt="">
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                @endif
                                @endforeach
                                @else
                                <li class="justify-center items-center flex h-full w-full">
                                    <h1 class="chart-heading">No new notification</h1>
                                </li>
                                @endif
                            </ul>
                        </div>

                    </div>
                </div>
                <div class="flex flex-col text-left gap-y-5">
                    <h1 class="text-2xl">Quick Insights</h1>
                    <div class="grid grid-cols-2 gap-x-5 gap-y-5">
                        <div class="flex flex-col gap-y-2 px-5 py-5 border bg-white rounded-2xl shadow-md">
                            <h1 class="chart-heading text-sm font-semibold">Top Hirer</h1>
                            @if (count($topHirer) > 0)
                            <div class="flex justify-start items-center gap-2">
                                <h1 class="font-semibold">{{$topHirer[0]->hirings}}</h1>
                                <p class="chart-heading">{{$topHirer[0]->company_name}}</p>
                            </div>
                            @else
                            <p class="chart-heading">Start of Quarter</p>
                            @endif
                        </div>
                        <div class="flex flex-col gap-y-2 px-5 py-5 border bg-white rounded-2xl shadow-md">
                            <h1 class="chart-heading text-sm font-semibold">External Population Size</h1>
                            <div class="flex justify-start items-center gap-2">
                                <h1 class="font-semibold">{{$externalPeopleCount}}</h1>
                                <p class="chart-heading">External People</p>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-5" wire:ignore>
                        <div class="flex flex-col pie-chart-container gap-y-3 border bg-white rounded-xl justify-center items-start shadow-md">
                            <h1 class="text-sm text-left chart-heading">My Organization</h1>
                            <div class="chart-container flex doughnut-chart-container gap-x-5 justify-between items-center">
                                <canvas id="myOrgDoughnut"></canvas>
                                <ul id="custom-myOrg-legend" class="custom-h-legend whitespace-nowrap"></ul>
                            </div>
                        </div>
                        <div class="flex flex-col pie-chart-container gap-y-3 bg-white rounded-xl border justify-center items-start shadow-md">
                            <h1 class="text-sm text-left chart-heading">Plan Status</h1>
                            <div class="chart-container flex doughnut-chart-container gap-x-5 justify-between items-center">
                                <canvas id="planDoughnut"></canvas>
                                <ul id="custom-plan-legend" class="custom-h-legend whitespace-nowrap"></ul>
                            </div>
                        </div>
                        <div class="flex flex-col pie-chart-container gap-y-3 border bg-white rounded-xl justify-center items-start shadow-md">
                            <h1 class="text-sm text-left chart-heading">Talent Pool Status</h1>
                            <div class="chart-container flex doughnut-chart-container gap-x-5 justify-between items-center">
                                <canvas id="jobDoughnut"></canvas>
                                <ul id="custom-job-legend" class="custom-h-legend whitespace-nowrap"></ul>
                            </div>
                        </div>
                        <div class="flex flex-col pie-chart-container gap-y-3 border bg-white rounded-xl justify-center items-start shadow-md">
                            <h1 class="text-sm text-left chart-heading">People Status</h1>
                            <div class="chart-container flex doughnut-chart-container gap-x-5 justify-between items-center">
                                <canvas id="peopleDoughnut"></canvas>
                                <ul id="custom-people-legend" class="custom-h-legend whitespace-nowrap"></ul>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-5" wire:ignore>
                        <div class="flex flex-col gap-y-2 px-5 py-5 bg-white rounded-2xl border shadow-md">
                            <h1 class="chart-heading">Potential Sex Diversity</h1>
                            <div class="flex flex-col locationBar-chart-container gap-y-5 justify-between items-center">
                                <canvas id="myChart"></canvas>
                                <ul id="custom-gd-legend" class="flex justify-around w-full items-center custom-h-legend whitespace-nowrap"></ul>
                            </div>
                        </div>
                        <div class="flex flex-col gap-y-2 px-5 py-5 bg-white border rounded-2xl shadow-md">
                            <h1 class="chart-heading">Movement - Top 10 Companies</h1>
                            @if (count($topMovementCompanies) > 0)
                            <div class="flex division-vbar-chart-container">
                                <canvas id="myChart2"></canvas>
                                <ul id="custom-hbar-legend" class="custom-vbar-legend custom-h-legend whitespace-nowrap flex flex-col py-4"></ul>
                            </div>
                            @else
                            <p class="self-center mt-auto mb-auto mr-auto ml-auto left-0 right-0 chart-heading">No Movements Found</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const myOrgCTX = document.getElementById('myOrgDoughnut').getContext('2d');
            const myOrgLabels = @json($candidatesLabel);
            const _myOrgData = @json($candidatesData);
            const myOrgData = {
                labels: myOrgLabels,
                datasets: [{
                    label: 'My Organization',
                    data: _myOrgData,
                    backgroundColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 205, 86)'
                    ],
                    hoverOffset: 4
                }]
            };

            const myOrgConfig = {
                type: 'doughnut',
                data: myOrgData,
                options: {
                    animation: false,
                    responsive: true,
                    maintainAspectRatio: false, // Allows control over height and width
                    cutout: '80%', // Adjusts the inner radius, making the doughnut thicker or thinner
                    plugins: {
                        dataLabals: {
                            show: false
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(tooltipItem) {
                                    const dataset = tooltipItem.dataset;
                                    const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                    const currentValue = dataset.data[tooltipItem.dataIndex];
                                    const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                    return `${currentValue} (${percentage}%)`;
                                }
                            }
                        }
                    }
                },
                plugins: [{
                    afterUpdate: function(chart) {
                        const ul = document.getElementById('custom-myOrg-legend');
                        ul.innerHTML = '';
                        const data = chart.data;
                        const dataset = data.datasets[0];
                        const total = dataset.data.reduce((sum, value) => sum + value, 0);

                        data.labels.forEach(function(label, i) {
                            const value = dataset.data[i];
                            const percentage = Math.round((value / total) * 100);
                            const color = dataset.backgroundColor[i];

                            const li = document.createElement('li');
                            li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                            ul.appendChild(li);
                        });
                    }
                }]
            };

            new Chart(myOrgCTX, myOrgConfig);
            const planCTX = document.getElementById('planDoughnut').getContext('2d');
            const plansLabels = @json($statusLabels);
            const _planData = @json($statusData);
            const planData = {
                labels: plansLabels,
                datasets: [{
                    label: 'Plan',
                    data: _planData,
                    backgroundColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 205, 86)'
                    ],
                    hoverOffset: 4
                }]
            };

            const planConfig = {
                type: 'doughnut',
                data: planData,
                options: {
                    animation: false,
                    responsive: true,
                    maintainAspectRatio: false, // Allows control over height and width
                    cutout: '80%', // Adjusts the inner radius, making the doughnut thicker or thinner
                    plugins: {
                        dataLabals: {
                            show: false
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(tooltipItem) {
                                    const dataset = tooltipItem.dataset;
                                    const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                    const currentValue = dataset.data[tooltipItem.dataIndex];
                                    const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                    return `${currentValue} (${percentage}%)`;
                                }
                            }
                        }
                    }
                },
                plugins: [{
                    afterUpdate: function(chart) {
                        const ul = document.getElementById('custom-plan-legend');
                        ul.innerHTML = '';
                        const data = chart.data;
                        const dataset = data.datasets[0];
                        const total = dataset.data.reduce((sum, value) => sum + value, 0);

                        data.labels.forEach(function(label, i) {
                            const value = dataset.data[i];
                            const percentage = Math.round((value / total) * 100);
                            const color = dataset.backgroundColor[i];

                            const li = document.createElement('li');
                            li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                            ul.appendChild(li);
                        });
                    }
                }]
            };

            new Chart(planCTX, planConfig);

            const jobCTX = document.getElementById('jobDoughnut').getContext('2d');
            const jobLabels = @json($jstatusLabels);
            const _jobData = @json($jstatusData);
            const jobData = {
                labels: jobLabels,
                datasets: [{
                    label: 'Job',
                    data: _jobData,
                    backgroundColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 205, 86)'
                    ],
                    hoverOffset: 4
                }]
            };

            const jobConfig = {
                type: 'doughnut',
                data: jobData,
                options: {
                    animation: false,
                    responsive: true,
                    maintainAspectRatio: false, // Allows control over height and width
                    cutout: '80%', // Adjusts the inner radius, making the doughnut thicker or thinner
                    plugins: {
                        dataLabals: {
                            show: false
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(tooltipItem) {
                                    const dataset = tooltipItem.dataset;
                                    const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                    const currentValue = dataset.data[tooltipItem.dataIndex];
                                    const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                    return `${currentValue} (${percentage}%)`;
                                }
                            }
                        }
                    }
                },
                plugins: [{
                    afterUpdate: function(chart) {
                        const ul = document.getElementById('custom-job-legend');
                        ul.innerHTML = '';
                        const data = chart.data;
                        const dataset = data.datasets[0];
                        const total = dataset.data.reduce((sum, value) => sum + value, 0);

                        data.labels.forEach(function(label, i) {
                            const value = dataset.data[i];
                            const percentage = Math.round((value / total) * 100);
                            const color = dataset.backgroundColor[i];

                            const li = document.createElement('li');
                            li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                            ul.appendChild(li);
                        });
                    }
                }]
            };

            new Chart(jobCTX, jobConfig);

            const peopleCTX = document.getElementById('peopleDoughnut').getContext('2d');
            const peopleLabels = @json($pstatusLabels);
            const _peopleData = @json($pstatusData);
            const peopleData = {
                labels: peopleLabels,
                datasets: [{
                    label: 'People',
                    data: _peopleData,
                    backgroundColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 205, 86)'
                    ],
                    hoverOffset: 4
                }]
            };

            const peopleConfig = {
                type: 'doughnut',
                data: peopleData,
                options: {
                    animation: false,
                    responsive: true,
                    maintainAspectRatio: false, // Allows control over height and width
                    cutout: '80%', // Adjusts the inner radius, making the doughnut thicker or thinner
                    plugins: {
                        dataLabals: {
                            show: false
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(tooltipItem) {
                                    const dataset = tooltipItem.dataset;
                                    const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                    const currentValue = dataset.data[tooltipItem.dataIndex];
                                    const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                    return `${currentValue} (${percentage}%)`;
                                }
                            }
                        }
                    }
                },
                plugins: [{
                    afterUpdate: function(chart) {
                        const ul = document.getElementById('custom-people-legend');
                        ul.innerHTML = '';
                        const data = chart.data;
                        const dataset = data.datasets[0];
                        const total = dataset.data.reduce((sum, value) => sum + value, 0);

                        data.labels.forEach(function(label, i) {
                            const value = dataset.data[i];
                            const percentage = Math.round((value / total) * 100);
                            const color = dataset.backgroundColor[i];

                            const li = document.createElement('li');
                            li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                            ul.appendChild(li);
                        });
                    }
                }]
            };

            new Chart(peopleCTX, peopleConfig);
            const genderData = @json($genderCount);
            const genderLabels = genderData.map((data) => {
                return data.gender.toString()
            })
            const genderCount = genderData.map((data) => {
                return data.gender_count
            })
            const verticalData = {
                labels: genderLabels,
                datasets: [{
                    label: 'Potential Sex',
                    data: genderCount,
                    backgroundColor: [
                        '#3B82F6',
                        '#FFA347',
                        '#8B5CF6',
                    ]
                }]
            };

            const topMovementCompanyNames = @json($topMovementCompanies)

            const companyNames = topMovementCompanyNames.map((data) => {
                return data.name
            })

            const companyMovements = topMovementCompanyNames.map((data) => {
                return data.movements
            })


            const horizontalData = {
                labels: companyNames,
                datasets: [{
                    label: 'Movements',
                    data: companyMovements,
                    backgroundColor: ['#FFA347']
                }]
            };

            console.log(horizontalData)


            const horizontalConfig = {
                type: 'bar',
                data: horizontalData,
                options: {
                    animation: false,
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    borderRadius: {
                        bottomLeft: 10,
                        bottomRight: 10,
                        topRight: 10,
                        topLeft: 10
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        datalabels: {
                            anchor: 'end',
                            align: 'top',
                            formatter: function(value) {
                                return value + ' (' + ((value / totalMovements) * 100).toFixed(2) + '%)';
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: false,
                            beginAtZero: true
                        },
                        y: {
                            border: {
                                display: false,
                            },
                            grid: {
                                display: false
                            },
                            ticks: {
                                display: true
                            },
                            axis: {
                                display: false
                            }
                        }
                    },
                    plugins: [{
                        afterUpdate: function(chart) {
                            const ul = document.getElementById('custom-hbar-legend');
                            ul.innerHTML = '';
                            const data = chart.data;
                            const dataset = data.datasets[0];
                            const total = dataset.data.reduce((sum, value) => sum + value, 0);

                            data.labels.forEach(function(label, i) {
                                const value = dataset.data[i];
                                const percentage = ((value / total) * 100).toFixed(2);
                                console.log(value)
                                const li = document.createElement('li');
                                li.innerHTML = `<span></span> <div>${value} <span id='percentage'>(${percentage}%)</span></div>`;
                                ul.appendChild(li);
                            });
                        }
                    }]
                }
            };

            // Configuration for the chart
            const verticalConfig = {
                type: 'bar',
                data: verticalData,
                options: {
                    animation: false,
                    responsive: true,
                    maintainAspectRatio: false, // Allows control over height and width
                    borderRadius: {
                        bottomLeft: 10,
                        bottomRight: 10,
                        topRight: 10,
                        topLeft: 10
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        datalabels: {
                            anchor: 'end',
                            align: 'top',
                            formatter: function(value, context) {
                                return value + ' (' + ((value / total) * 100).toFixed(2) + '%)';
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: false // Hide x-axis
                        },
                        y: {
                            display: false // Hide y-axis
                        }
                    },
                },
                plugins: [{
                    afterUpdate: function(chart) {
                        const ul = document.getElementById('custom-gd-legend');
                        ul.innerHTML = '';
                        const data = chart.data;
                        const dataset = data.datasets[0];
                        const total = dataset.data.reduce((sum, value) => sum + value, 0);

                        data.labels.forEach(function(label, i) {
                            const value = dataset.data[i];
                            const percentage = Math.round((value / total) * 100);
                            const color = dataset.backgroundColor[i];

                            const li = document.createElement('li');
                            li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                            ul.appendChild(li);
                        });
                    }
                }]
            };

            // Create the chart
            var myChart = new Chart(
                document.getElementById('myChart'),
                verticalConfig
            );


            var myChart2 = new Chart(
                document.getElementById('myChart2'),
                horizontalConfig
            );
        });

        setInterval(() => {
            document.querySelectorAll('.chart-container').forEach(chart => chart.style.opacity = '1');

        }, 1000);

        function deleteNotificationAndRemove(notificationId) {
        }
        function deleteNotificationAndRemove(notificationId) {
            document.getElementById(`notification-${notificationId}`)?.remove();
            @this.deleteNotification(notificationId);
    }
    </script>