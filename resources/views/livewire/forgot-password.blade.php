<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    @include('livewire.flashMessage')

    <div class="w-full flex">
        <div class="fixed inset-0 flex items-center justify-center  z-40">
            <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
            @if ($step == 1)
                <div
                    class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
                    <div class="flex flex-col justify-center items-center">
                        <img class="w-20 h-20" src="{{ asset('images/LogoSmall.png') }}">
                        <h2 class="mt-4 text-center text-2xl font-bold text-gray-900">Forgot Password? </h2>
                        <p class="text-sm mt-4 grayText">Enter the email address you used to create account. </br>
                            We will send a password reset email.</p>
                    </div>
                    <form wire:submit.prevent="submitEmail" class="mt-4 px-4 space-y-4">
                        <div>
                            <label for="email" class="text-xs font-medium labelcolor">Email</label>
                            <input wire:model="email" id="email" name="email" type="email"
                                class="mt-1 placeholder:text-gray-400 labelcolor bg-white  @error('email') is-invalid @enderror  placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                                placeholder="Email your email">
                            @error('email')
                                <span class="redColor text-sm">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mt-2">
                            <button type="submit"
                                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white mainblueBG  ">
                                Send reset link
                            </button>
                        </div>
                        <div wire:click="backToLogin()" class="my-5 flex justify-center">
                            <button type="button" class="text-sm text-center mainblue">Back to Log In</button>
                        </div>
                    </form>
                </div>
            @elseif($step == 2)
                <div
                    class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[24%]">
                    <div class="flex flex-col justify-center items-center">
                        <img class="w-20 h-20" src="{{ asset('images/LogoSmall.png') }}">
                        <h2 class="mt-4 text-center text-2xl font-bold text-gray-900">Forgot Password? </h2>
                        <p class="text-sm mt-4 grayText px-6 text-center">We have sent an email to
                            {{ $email }}. <br>
                            Please check your inbox and follow instructions to reset your password. </br>
                            We will send a password reset email.</p>
                        <form wire:submit.prevent="resendEmail" class="mt-4 px-4 space-y-4">
                            <div class="flex justify-center mt-3">
                                <span class="text-xs font-normal text-center">Did not receive an email?<a
                                        href="javascript:void(0);" wire:click="resendEmail"><span class=" mainblue">
                                            Send Again</span></a></span>
                            </div>
                        </form>
                    </div>
                    <div wire:click="backToLogin" class="my-5 flex justify-center">
                        <button type="button" class="text-sm text-center mainblue">Back to Log In</button>
                    </div>
                </div>
            @endif
        </div>
    </div>
    @include('livewire.loading')
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {


        Livewire.on('addEventListenerOnOtp', () => {
            setTimeout(function() {

                const inputs = document.querySelectorAll('.inputArea');

                inputs.forEach((input, index) => {
                    input.addEventListener('input', () => {
                        if (input.value.length === 1) {
                            const nextInput = inputs[index + 1];
                            if (nextInput) {
                                nextInput.focus();
                            }
                        }
                    });

                    input.addEventListener('keydown', (e) => {
                        if (e.key === 'Backspace' && input.value.length === 0) {
                            const previousInput = inputs[index - 1];
                            if (previousInput) {
                                previousInput.focus();
                            }
                        }
                    });
                });
            }, 500);
        });

    });
</script>
