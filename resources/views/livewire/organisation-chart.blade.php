<div>
    @if($topPeople !== null)
    <div class="mb-5 mt-5 flex-1">
    <div>
        <div class="relative ml-10 mr-10 py-3">
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
                    <div class="w-full border-t border-sky-300"></div>
                    </div>
            <div class="mt-1 absolute inset-0 flex items-center" aria-hidden="true">
                <div class="w-full border-t border-sky-300"></div>
            </div>
        </div>
    </div>
    
    <div class="mt-5 flex flex-1 justify-center items-center">
        <div>
            <h4 class="text-xs text-center font-semibold">{{ $topIndividual->forename}} {{ $topIndividual->surname}}</h4>
            <h4 class="text-xs text-center">{{ $topIndividual->division}} {{ $topIndividual->function}}</h4>
        </div>
    </div>
    <div x-data="{ sopen: {} }" class="mt-5 flex flex-1 items-center justify-center">
                <div class="flex gap-x-10">
                    @foreach($topPeople as $topPerson)
                    <div x-transition.duration.500ms x-init="sopen[{{ $topPerson->id }}] = true" class="flex-1 items-center justify-center rounded-full border-black">
                        <div class="flex items-center justify-center">
                            <div class="p-2 h-32 w-64 border border-gray-200 rounded-lg bg-white">
                                <div class="flex flex-1 gap-x-2">
                                    <div>
                                        <h4 class="whitespace-nowrap text-left text-sm text-blue-500 font-semibold">{{ $topPerson->forename}} {{ $topPerson->surname}}</h4>
                                        <p class="text-left text-sm text-gray-700">{{ $topPerson->role}}</p>
                                        <p class="mt-1 text-left text-xs">{{ $topPerson->division}} - {{ $topPerson->function}}</p>
                                    </div>
                                    <div class="flex flex-1 justify-end">
                                        <div class="">
                                            <div>
                                                <h4 class="text-xs text-center">Readiness</h4>
                                                @if ($topPerson->readiness ?? null)
                                                <p class="text-sm text-center text-gray-600 font-semibold">Yes</p>
                                                @else
                                                <p class="text-sm text-center text-gray-600 font-semibold">No</p>
                                                @endif
                                            </div>
                                            <div class="mt-1">
                                                <h4 class="text-xs text-center">In-Plans</h4>
                                                @if ($topPerson->isSuccessPerson === 1)
                                                <p class="text-sm text-center text-gray-600 font-semibold">Yes</p>
                                                @else
                                                <p class="text-sm text-center text-gray-600 font-semibold">No</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex flex-1 justify-center mt-2 gap-x-10">

                                    <!-- The button to view the details of an individual -->
                                    <div class="" x-data="{ iopen: false }">
                                    <!-- Trigger button inside the modal -->
                                        <div class="">
                                            <button wire:click="showIndividual({{ $topPerson->id }})" class="w-full relative flex items-center justify-center border border-transparent hover:scale-110">
                                                <img class="h-4 w-auto" src="{{ asset('images/view.png') }}">
                                            </button>
                                        </div>
                                        <div>
                                            <livewire:internal-show :$topPerson :key="$topPerson->id" /> 
                                        </div>
                                    </div>

                                    
                                    @if(!empty($topPerson->subordinate_count))
                                    <div class="flex items-center px-1">
                                        <button x-on:click="sopen[{{ $topPerson->id }}] = true">
                                            <div class="flex gap-x-2 items-center">
                                                <label class="text-xs">{{$topPerson->subordinate_count}}</label>
                                                <img class="h-4 w-auto" src="{{ asset('images/plus.png') }}">
                                            </div>
                                        </button>
                                    </div>
                                    @endif

                                    <!-- The delete button -->
                                    <div class="" x-data="{ dopen: false }">
                                        <!-- Trigger button inside the modal -->
                                        <div class="">
                                            <button @click="dopen = true" class="w-full relative flex items-center justify-center gap-x-2 rounded-br-lg border border-transparent hover:scale-110">
                                                <img class="h-4 w-auto " src="{{ asset('images/trash.png') }}">
                                            </button>
                                        </div>
                                        <!-- Modal container -->
                                        <div x-show="dopen" 
                                            class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50"
                                            style="display:none">
                                            <!-- Modal background with a higher z-index -->
                                            <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                                            <!-- Modal content with a lower z-index -->
                                            <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50 w-96">
                                            <!-- Modal content -->
                                                <p class="text-sm text-gray-700 font-semibold"> Are you sure you want to remove this individual? Removing Internal Employees may impact other plans.</p>
                                                <div class="mt-2 flex items-center justify-center gap-x-6">
                                                    <div class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-110">
                                                        <button x-on:click="dopen = false" class="transition ease-in-out delay-100 border border-sky-300 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-sky-300 hover:text-white duration-100">Cancel</button>
                                                    </div>
                                                    <div  class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-110">
                                                        <button x-on:click="dopen = false" wire:click="removeInternalCandidates({{ $topPerson->id }})" class="transition ease-in-out delay-100 border border-sky-300 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-sky-300 hover:text-white duration-100">Yes I'm Sure</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if(!empty($topPerson->subordinate_count))
                        <div x-transition.duration.500ms x-show="sopen[{{ $topPerson->id }}]">
                        <div class="mt-5 flex flex-1">
                            <div class="py-2 flex flex-1 justify-end rounded-lg border-t border-black">
                                <button x-on:click="sopen[{{ $topPerson->id }}] = !sopen[{{ $topPerson->id }}]">
                                    <span class="text-xs text-gray-800 font-light">Hide</span>
                                <button>     
                            </div>
                        </div>
                        <div class="mt-2 gap-x-10 flex flex-1 items-center justify-center">
                            @foreach ($topPerson->subordinates as $subordinate)
                            <div class="p-2 h-32 w-64 border border-gray-200 rounded-lg bg-white">
                                <div class="flex flex-1 gap-x-2">
                                    <div>
                                        <h4 class="whitespace-nowrap text-sm font-semibold">{{ $subordinate->forename}} {{ $subordinate->surname}}</h4>
                                        <p class="whitespace-nowrap text-xs text-gray-700">{{ $subordinate->role}}</p>
                                        <p class="mt-1 whitespace-nowrap text-sm font-semibold">{{ $subordinate->division}} - {{ $subordinate->function}}</p> 
                                    </div>
                                    <div class="flex flex-1 justify-end">
                                        <div class="">
                                            <div>
                                                <h4 class="text-xs text-center">Readiness</h4>
                                                @if ($subordinate->readiness ?? null)
                                                <p class="text-sm text-center text-gray-600 font-semibold">Yes</p>
                                                @else
                                                <p class="text-sm text-center text-gray-600 font-semibold">No</p>
                                                @endif
                                            </div>
                                            <div class="mt-1">
                                                <h4 class="text-xs text-center">In-Plans</h4>
                                                @if ($subordinate->isSuccessPerson === 1)
                                                <p class="text-sm text-center text-gray-600 font-semibold">Yes</p>
                                                @else
                                                <p class="text-sm text-center text-gray-600 font-semibold">No</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex flex-1 mt-2 gap-x-10 justify-center">
                                    <!-- The button to view the details of an individual -->
                                    <div class="" x-data="{ iopen: false }">
                                    <!-- Trigger button inside the modal -->
                                        <div class="">
                                            <button wire:click.ignore="showIndividual({{ $subordinate->id }})" class="w-full relative flex items-center justify-center border border-transparent hover:scale-110">
                                                <img class="h-4 w-auto" src="{{ asset('images/view.png') }}">
                                            </button>
                                        </div>
                                        <div>
                                            <livewire:internal-show :$subordinate :key="$subordinate->id" /> 
                                        </div>
                                    </div>


                                    <div>
                                    @if(!empty($subordinate->subordinate_count))
                                    <div class="flex items-center px-1">
                                        <button wire:click="showOrganisation({{ $subordinate->id }})"> 
                                            <div class="flex gap-x-2 items-center">
                                                <label class="text-xs">{{ $subordinate->subordinate_count }}</label>
                                                <img class="h-4 w-auto" src="{{ asset('images/plus.png') }}">
                                            </div>
                                        </button>
                                    </div>
                                    @endif
                                    </div>
                                    <!-- The delete button -->
                                    <div class="" x-data="{ dopen: false }">
                                        <!-- Trigger button inside the modal -->
                                        <div class="">
                                            <button @click="dopen = true" class="w-full relative flex items-center justify-center gap-x-2 rounded-br-lg border border-transparent hover:scale-110">
                                                <img class="h-4 w-auto " src="{{ asset('images/trash.png') }}">
                                            </button>
                                        </div>
                                        <!-- Modal container -->
                                        <div x-show="dopen" 
                                            class="fixed inset-0 flex items-center justify-center z-50"
                                            style="display:none">
                                            <!-- Modal background with a higher z-index -->
                                            <div class="modal-background fixed inset-0 bg-slate-50 opacity-50 z-40"></div>
                                            <!-- Modal content with a lower z-index -->
                                            <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50 w-96">
                                            <!-- Modal content -->
                                                <p class="text-sm text-gray-700 font-semibold"> Are you sure you want to remove this individual? Removing Internal Employees may impact other plans.</p>
                                                <div class="mt-2 flex items-center justify-center gap-x-6">
                                                    <div class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-110">
                                                        <button x-on:click="dopen =false" class="transition ease-in-out delay-100 border border-sky-300 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-sky-300 hover:text-white duration-100">Cancel</button>
                                                    </div>
                                                    <div class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-110">
                                                        <button x-on:click="dopen =false" wire:click="removeInternalCandidates({{ $subordinate->id }})" class="transition ease-in-out delay-100 border border-sky-300 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-sky-300 hover:text-white duration-100">Yes I'm Sure</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        </div>             
                        @endif
                        
                    </div>
                    @endforeach
                </div>
            </div>
            <div>
            </div>
        </div>
    </div>
    @else
    <div>{{$id}}</div>
    @endif
</div>
