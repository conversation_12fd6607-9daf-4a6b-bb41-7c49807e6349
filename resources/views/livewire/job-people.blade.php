<div class="">
    <div class="flex items-center px-10 py-4 bg-sky-900">
        <h1 class="whitespace-nowrap px-4 text-white text-3xl font-semibold">People Dashboard</h1>
        <div class="w-full flex justify-end items-center gap-x-5">
            <div class="border border-white rounded-full p-px hover:shadow-lg hover:scale-105">
                <div class="flex items-center border border-white rounded-full text-white p-2">
                    <a href="{{ route('job.index') }}"  class="px-4 text-sm text-white font-medium">Go Back</a>
                </div>
            </div> 
        </div>
    </div>
    <div class="flex flex-wrap px-14 shadow-2xl h-max py-4 justify-center gap-x-10 bg-white">
        <div class="flex-col flex-1 justify-center items-center">
            <div class="flex-1 items-center justify-center text-center">
                <dl class="text-sm font-medium leading-6 text-gray-500">Number of People</dl>
                <dd class="w-full flex-none text-4xl font-medium leading-10 tracking-tight text-gray-900">{{ $TotalPeople }}</dd>
            </div>
            <div class="flex-1 mt-5 items-center justify-center text-center">
                <dl class="text-sm font-medium leading-6 text-gray-500">Number of Companies</dl>
                <dd class="w-full flex-none text-4xl font-medium leading-10 tracking-tight text-gray-900">{{ $TotalCompanies }}</dd>
            </div>
        </div>
        <div class="">
            <h2 class="text-black text-xl font-medium text-center">Gender Split</h2>
            <div class="h-80" wire:ignore x-data="{
                                        gvalues: {{ json_encode($genderData) }},
                                        glabels:{{ json_encode($genderLabels) }},
                                        colors:['red','blue','green'],
                                        init() {
                                                    let chart = new ApexCharts(this.$refs.genderchart, {
                                                                                                chart: { type: 'donut',
                                                                                                        width:'100%',
                                                                                                        height:'100%' },
                                                                                                labels: this.glabels,
                                                                                                dataLabels: { enabled: true, textAnchor: 'start', style: {fontSize:'9px'}     
                                                                                                },
                                                                                                colors: ['#a78bfa', '#fb7185', '#9C27B0'],
                                                                                                series: this.gvalues,
                                                                                                fill: { colors: ['#a78bfa', '#fb7185', '#9C27B0']},
                                                                                                stroke: { width:1 },
                                                                                                legend: {   position: 'bottom',
                                                                                                            markers: {fillColors: ['#a78bfa', '#fb7185', '#9C27B0']} },
                                                                                                tooltip: { fillSeriesColor:true}
                                                                                    })
                                                        chart.render()
                                                }
                                        }">
                <div x-ref="genderchart"></div>
            </div>
        </div>
        <div>
            <h2 class="text-black text-xl font-medium text-center">Exco</h2>
            <div class="h-80" wire:ignore x-data="{
                                        gvalues: {{ json_encode($divisionData) }},
                                        glabels:{{ json_encode($divisionLabels) }},
                                        colors:['red','blue','green'],
                                        init() {
                                                    let chart = new ApexCharts(this.$refs.genderchart, {
                                                                                                chart: { type: 'donut',
                                                                                                        width:'100%',
                                                                                                        height:'100%' },
                                                                                                labels: this.glabels,
                                                                                                dataLabels: { enabled: true, textAnchor: 'start', style: {fontSize:'9px'}     
                                                                                                },
                                                                                                colors: ['#48CAE4', '#0f766e', '#9C27B0'],
                                                                                                series: this.gvalues,
                                                                                                fill: { colors: ['#48CAE4', '#0f766e', '#9C27B0']},
                                                                                                stroke: { width:1 },
                                                                                                legend: {   position: 'bottom',
                                                                                                            markers: {fillColors: ['#48CAE4', '#0f766e', '#9C27B0']} },
                                                                                                tooltip: { fillSeriesColor:true}
                                                                                    })
                                                        chart.render()
                                                }
                                        }">
                <div x-ref="genderchart"></div>
            </div>
        </div>
        <div>
            <h2 class="text-black text-xl font-medium text-center">Locations</h2>
            <div class="h-80 w-96" wire:ignore x-data="{
                                        cvalues: {{ json_encode($locationData) }},
                                        clabels:{{ json_encode($locationLabels) }},
                                        init() {
                                                let chart = new ApexCharts(this.$refs.companychart, {
                                                                                                chart: { type: 'bar',
                                                                                                         stacked:true },
                                                                                                plotOptions: { bar: { horizontal: true } },
                                                                                                tooltip: {enabled:false},
                                                                                                grid:{show:false},
                                                                                                xaxis: { categories: this.clabels },
                                                                                                
                                                                                                series: [{
                                                                                                            name: 'companies',
                                                                                                            data: this.cvalues,
                                                                                                        }],
                                                                            })
                                                chart.render()
                                    }
                }">
            <div x-ref="companychart"></div>
            </div>
        </div>
        <div>
            <h2 class="text-black text-xl font-medium text-center">Companies</h2>
            <div class="h-80 w-96" wire:ignore x-data="{
                                        dvalues: {{ json_encode($companyData) }},
                                        dlabels:{{ json_encode($companyLabels) }},
                                        init() {
                                                let chart = new ApexCharts(this.$refs.companychart, {
                                                                                                chart: { type: 'bar',
                                                                                                         stacked:true },
                                                                                                plotOptions: { bar: { horizontal: true } },
                                                                                                tooltip: {enabled:false},
                                                                                                grid:{show:false},
                                                                                                xaxis: { categories: this.dlabels },
                                                                                                
                                                                                                series: [{
                                                                                                            name: 'Peoples',
                                                                                                            data: this.dvalues,
                                                                                                        }],
                                                                                                
                                                                                                fill: { colors: ['#f97316','#fdba74','#60a5fa','#34d399']},
                                                                                                
                                                                                                legend: {   position: 'bottom',
                                                                                                            markers: {fillColors: ['#fdba74','#60a5fa','#34d399']} },
                                                                                                tooltip: { fillSeriesColor:true}
                                                                            })
                                                chart.render()
                                    }
                }">
            <div x-ref="companychart"></div>
            </div>
        </div>
    </div>

    <div class=" bg-white relative ml-10 mr-10">
        <div class="absolute inset-0 flex items-center" aria-hidden="true">
            <div class="w-full border-t border-gray-200"></div>
        </div>
    </div>
    <div x-data="{ fopen: false }" x-init="fopen = false">
        <div class="p-3 py-6 bg-white flex flex-1 shadow-md justify-center items-center gap-x-10 text-center">
            <div class="">
                <div class="w-full flex">
                    <div class="flex text-right">
                    <div class="border border-black w-40 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                            <button x-on:click="fopen = !fopen" class="transition w-full ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">
                                Advanced Search
                            </button>
                        </div>  
                    </div>
                </div>
            </div>
            <div class="w-2/6">
                <input name="username" id="username" type="text" class="p-4 w-full block rounded-full border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" placeholder="Search"></input>
            </div>
            <div class="flex" x-data="{ Aopen: false }">
                <!-- Trigger button inside the modal -->
                <div class="flex items-center justify-center">
                    <div class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                        <button x-on:click="Aopen = true" class="transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100 whitespace-nowrap">Add Person</button>
                    </div>
                </div>   
                <!-- Modal container -->
                <div x-show="Aopen" 
                    class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50"
                    style="display:none">
                    <!-- Modal background with a higher z-index -->
                    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                    <!-- Modal content with a lower z-index -->
                    <div class="modal-content border border-gray-500 bg-white shadow-lg rounded-lg p-4 z-50">
                        <!-- Advanced Search modal content -->
                        <form wire:submit="AddPerson">
                            @csrf
                            <div class="relative">
                                <div class="absolute inset-0 flex items-center" aria-hidden="true">
                                    <div class="w-full border-t border-gray-300"></div>
                                </div>
                                <div class="relative flex justify-center">
                                    <span class="bg-white px-3 text-base font-semibold text-gray-900 text-center">Primary Details</span>
                                </div>
                            </div>
                            <div class= "mt-1">
                                <div class="mt-2 flex items-center">
                                    <label for="jobName" class="block text-sm text-gray-900">Forename </label>
                                    <input type="text" wire:model="addForename" class="appearance-none border-b border-gray-200 ml-2 flex-1 bg-transparent pl-1 text-sm text-cyan-500 placeholder:text-gray-400 text-sm" placeholder="">
                                </div>
                                <div class="mt-2 flex items-center">
                                    <label for="jobName" class="block text-sm leading-6 text-gray-900">Surname</label>
                                    <input type="text" wire:model="addSurname" class="appearance-none border-b border-gray-200 ml-2 flex-1 bg-transparent pl-1 text-cyan-500 placeholder:text-gray-400 text-base font-medium" placeholder="">
                                </div>
                                <div class="mt-2 flex items-center">
                                    <label for="jobName" class="block text-sm leading-6 text-gray-900">Middle Name</label>
                                    <input type="text" wire:model="addMiddlename" class="appearance-none border-b border-gray-200 ml-2 flex-1 bg-transparent pl-1 text-cyan-500 placeholder:text-gray-400 text-base font-medium" placeholder="">
                                    <span class="text-gray-700 text-xs">( Optional )</span>
                                </div>
                                <div class="mt-2 flex items-center">
                                    <label for="jobName" class="block text-sm leading-6 text-gray-900">Other Name</label>
                                    <input type="text" wire:model="addOtherName" class="appearance-none border-b border-gray-200 ml-2 flex-1 bg-transparent pl-1 text-cyan-500 placeholder:text-gray-400" placeholder="">
                                    <span class="text-gray-700 text-xs">( Optional )</span>
                                </div>
                                <div class="mt-2 flex items-center">
                                    <label for="jobName" class="block text-sm leading-6 text-gray-900">Role</label>
                                    <input type="text" wire:model="addRole" class="appearance-none border-b border-gray-200 ml-1 flex-1 bg-transparent pl-1 text-cyan-500 placeholder:text-gray-400 text-sm hover:placeholder-cyan-500" placeholder="Chief Risk Officer, Head of Finance,">
                                    <span class="text-gray-700 text-xs">( Optional )</span>
                                </div>
                                <div class="mt-2">
                                    <label for="jobName" class="block text-sm text-gray-900 text-left">Company </label>
                                    <p class="text-red-500 text-xs text-left">Please note that if an individual added who are not part of your company will be visible to other users. For internal candidates please type "Internal"to ensure their details are not shared.</p>
                                    <input type="text" wire:model.blur="addCompany" class="flex w-3/6 appearance-none border-b border-gray-200 ml-1 bg-transparent text-cyan-500 text-left placeholder:text-gray-400 text-sm hover:placeholder-cyan-500" placeholder="Enter the company name">
                                    @if($Suggestion)
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-800">Did you mean? </span>
                                        <ul class="ml-2 flex gap-x-2 text-gray-500 text-sm items-center">
                                        @foreach($Suggestion as $suggest)
                                            <button class="mt-1 text-gray-500 text-sm" wire:click="$set('addCompany', '{{ $suggest }}')">{{ $suggest }}</button>
                                        @endforeach
                                        </ul>
                                    </div>
                                    @endif
                                </div>
                                <div class="mt-2">
                                    <div class="flex items-center">
                                        <label for="jobName" class="block text-sm leading-6 text-gray-900 text-left">When did they start? </label>
                                        <span class="text-gray-700 text-xs">( Optional )</span>
                                    </div>
                                    <input type="date" wire:model="addStartDate" class="flex appearance-none ml-1 bg-transparent text-cyan-500 text-left text-sm placeholder:text-gray-400 text-sm hover:placeholder-cyan-500" placeholder="Enter the company name">
                                </div>
                            </div>
                            <div class="mt-7 relative">
                                <div class="absolute inset-0 flex items-center" aria-hidden="true">
                                    <div class="w-full border-t border-gray-300"></div>
                                </div>
                                <div class="relative flex justify-center">
                                    <span class="bg-white px-3 text-base font-semibold text-gray-900 text-center">Additional Details</span>
                                </div>
                            </div>
                            <div class= "mt-2">
                                <div class="text-left flex items-center">
                                    <label for="jobName" class="block text-sm leading-6 text-gray-900">They are based in </label>
                                    <select class="ml-2" wire:model="addLocation" id="country" name="country">
                                        <option value="">Select a country</option>
                                        @foreach($countries as $code => $name)
                                            <option value="{{ $name }}">{{ $name }}</option>
                                        @endforeach
                                    </select>
                                    <span class="ml-2 text-gray-700 text-xs">( Optional )</span>
                                </div>
                                <div class="mt-2 flex items-center">
                                    <label for="jobName" class="block text-sm leading-6 text-gray-900">They can best be described as </label>
                                    <input type="text" wire:model="addGender" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-cyan-500 placeholder:text-gray-400 text-sm hover:placeholder-cyan-500" placeholder="Female, Male, All">
                                </div>
                            </div>
                            <div class="mt-2 flex items-center justify-end gap-x-6">
                                <div class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                    <button x-on:click="Aopen =false" type="button" class="transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                </div>
                                <div class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                    <button x-on:click="Aopen =false" type="submit" class="transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Add</button>
                                </div>
                            </div>
                        </form>
                    </div>    
                </div>
            </div>
            <div class="w-2/6 flex border-l border-gray-300 px-4 items-center gap-x-10">
                <h2 class="text-sm font-semibold text-black">Sort By</h2>
                <div class="flex flex-wrap gap-x-2 gap-y-2">
                    <div class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                        <button wire:click="Ordermytable('tenure')" class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">
                            Tenure
                        </button>
                    </div>
                    <div class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                        <button  wire:click="Ordermytable('created_at')" class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">
                            Date Added
                        </button>
                    </div>
                    <div class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                        <button  wire:click="Ordermytable('start_date')" class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">
                            Date Joined
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- The filters in case the want to them search -->
        <div class="py-4 px-20 bg-white border-b border-gray-200 justify-center items-center" x-show="fopen" style="display:none">
            <div class="relative py-3">
                <div class="absolute inset-0 flex items-center" aria-hidden="true">
                    <div class="w-full border-t border-gray-200"></div>
                </div>
            </div> 
            <div class="grid grid-cols-4 gap-4 sm:grid-cols-5 lg:grid-cols-6">
                <div>
                    <label class="text-sm font-medium">First Name</label>
                    <input wire:model="forename" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
                <div>
                    <label class="text-sm font-medium">Last Name</label>
                    <input wire:model="surname" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
                <div>
                    <label class="text-sm font-medium">Gender</label>
                    <input wire:model="gender" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
                <div class="">
                    <label class="text-sm font-medium">Location</label>
                    <input wire:model="slocation" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
                <div class="">
                    <label class="text-sm font-medium">Role</label>
                    <input wire:model="role" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
                <div>
                    <label class="text-sm font-medium">Company</label>
                    <input wire:model="company" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
                <div>
                    <label class="text-sm font-medium">Function</label>
                    <input wire:model="function" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
                <div class="">
                    <label class="text-sm font-medium">Division</label>
                    <input wire:model="division" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
                <div class="">
                    <label class="text-sm font-medium">Regulatory Bodies</label>
                    <input wire:model="regBodies" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
                <div class="">
                    <label class="text-sm font-medium">Minimum Tenure</label>
                    <input wire:model="tenure" class="px-2 rounded-full text-sm border border-gray-400 w-full">
                </div>
            </div>
            <div class="mt-5 flex flex-1 items-center justify-center gap-x-10">
                <div class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                    <button wire:click="clearFilters" class="transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Clear Filters</button>
                </div>
                <div class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                    <button type="submit" wire:click="runfilters" class="transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Search</button>
                </div>
            </div>
        </div>
        
    </div>
    
    <!-- The table of individuals -->
    <div x-data="{ open: {} }" class="h-192 py-4 px-8 mt-8 flex flex-col mx-14 bg-white rounded-lg shadow-lg overflow-y-scroll">
        <div class="">
            <div class="py-4">
                {{ $People->links() }}
            </div>
        </div>
        @foreach ($People as $Person)
        <div x-init="open[{{ $Person->id }}] = false" class="border-b border-t border-gray-300 px-2 py-4 items-center">
            <div class="flex">
                <div class="w-2/6">
                    <h3 class="text-base text-cyan-500 font-semibold">{{ $Person->company_name }} </h3>
                    <h3 class="text-sm mt-1 font-semibold">{{ $Person->forename }} {{ $Person->surname }} </h3>
                    <h3 class="text-sm mt-1 font-semibold text-gray-800">{{ $Person->latest_role }}</h3>
                </div>
                <div class="flex items-center w-1/6">
                    <div class="flex-1">
                        <dl class="text-sm text-gray-700 text-center">Country</dl>
                        <dd class="text-base font-semibold text-center">{{ $Person->country }}</dd>
                    </div>
                    <div class="flex-1">
                        <dl class="text-sm text-gray-700 text-center">Function</dl>
                        <dd class="text-base font-semibold text-center">{{ $Person->function }}</dd>
                    </div>
                </div>
                <div class="flex items-center w-2/6 justify-top">
                    <div class="flex-1">
                    <dl class="text-sm text-gray-700 text-center">Gender</dl>
                    <dd class="text-base font-semibold text-center">{{ $Person->gender }}</dd>
                    </div>
                    @if($jobdetails->ethnicity === 1)
                    <div class="flex-1">
                    <dl class="text-sm text-gray-700 text-center">Potential Diversity</dl>
                    <dd class="text-base font-semibold text-center">{{ $Person->diverse }}</dd>
                    </div>
                    @endif
                    <div class="flex-1">
                    <dl class="text-sm text-gray-700 text-center">Tenure</dl>
                    <dd class="text-base font-semibold text-center">{{ $Person->tenure }}</dd>
                    </div>
                    <div class="flex-1">
                    <dl class="text-sm text-gray-700 text-center">Readiness</dl>
                    <dd class="text-base font-semibold text-center">{{ $Person->readiness }}</dd>
                    </div>
                    <div class="flex-1">
                    <dl class="text-sm text-gray-700 text-center">Registration Status</dl>
                    <dd class="text-xs font-medium text-center">{{ $Person->other_tags }}</dd>
                    </div>
                </div>

                <div class="ml-10 flex" x-data="{ vopen: true }">
                    <!-- Trigger button inside the modal -->
                    <div class="w-full flex items-center justify-center">
                        <button id="view" @click="vopen=true" wire:click.prevent="viewIndividual({{ $Person->id }})" class="text-sm text-center text-black font-medium p-2">
                            <img class="h-6 w-auto" src="{{ asset('images/view.png') }}">
                        </button>
                    </div>
                    @if($selectedIndividualID === $Person->id) 
                    <!-- Modal container -->
                    <div x-show="vopen" 
                         class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50"
                         style="display:none">
                        <!-- Modal background with a higher z-index -->
                        <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                        <!-- Modal content with a lower z-index -->
                        <div class="modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50">
                            <!-- Modal content -->
                            <div class="flex">
                                <div class="w-96">
                                    <h3 class="text-base font-bold">{{ $Person->company_name }}</h3>
                                    <h3 class="text-base font-bold">{{ $Person->forename}} {{ $Person->surname}}</h3>
                                    <h3 class="text-base text-gray-700 font-semibold">{{ $Person->latest_role}}</h3>
                                    <h4 class="text-sm text-cyan-500 font-medium">{{ $Person->location}}</h4>
                                </div>
                                <div class="flex flex-1 justify-end items-center">
                                    <div class="">
                                        <div class="flex items-center justify-center">
                                            <button @click = "vopen = false" class="flex items-center justify-center hover:scale-105">
                                                <img class="h-4 w-auto" src="{{ asset('images/left-arrow.png') }}">
                                            </button>
                                        </div>
                                        <div class="flex items-center justify-center mt-5">
                                            <button id="addtoShort" wire:click="addTojob({{ $Person->id }})" class="flex items-center justify-center hover:scale-105">
                                                <img class="h-4 w-auto" src="{{ asset('images/approved.png') }}">
                                            </button>
                                        </div>
                                        <div id="linkedin" class="flex items-center justify-center mt-5">
                                            <a class="flex items-center justify-center hover:scale-105" href="{{ $Person->linkedinURL }}">
                                                <img class="h-4 w-auto" src="{{ asset('images/linkedinlogo.png') }}">
                                            </a>
                                        </div>
                                    </div> 
                                </div>
                            </div>
                            <div class="p-4 mt-4">
                                <div class="w-full border-t border-grey-300"></div>
                            </div>
                            <div class="flex flex-1 gap-x-8 justify-center">
                                <div>
                                    <h3 class="text-sm text-center font-bold">Gender</h3>
                                    <h3 class="text-xs text-gray-700 text-center">{{ $Person->gender}}</h3>
                                </div>
                                <div>
                                    <h3 class="text-sm text-center font-bold">Function</h3>
                                    <h3 class="text-xs text-gray-700 text-center">{{ $Person->function}}</h3>
                                </div>
                                <div>
                                    <h3 class="text-sm text-center font-bold">Seniority</h3>
                                    <h3 class="text-xs text-gray-700 text-center">{{ $Person->seniority}}</h3>
                                </div>
                                <div>
                                    <h3 class="text-sm text-center font-bold">Tenure in Role</h3>
                                    <h3 class="text-xs text-gray-700 text-center">{{ $Person->tenure}}</h3>
                                </div>
                                <div>
                                    <h3 class="text-sm text-center font-bold">Readiness</h3>
                                    <h3 class="text-xs text-gray-700 text-center ">{{ $Person->readiness}}</h3>
                                </div>
                                <div>
                                    <h3 class="text-sm text-center font-bold">Registrations</h3>
                                    <h3 class="text-xs text-gray-700 text-center">{{ $Person->other_tags}}</h3>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="w-full border-t border-grey-300"></div>
                            </div>
                            <div class="mt-4 flex gap-x-10">
                                <div class="h-96 overflow-y-scroll">
                                    <h3 class="text-sm font-bold">Career History</h3>
                                    <div class="">
                                        @foreach ($groupedPeoplesCareer as $companyName => $companyData)
                                        <div class="flex mt-2 gap-x-5 border-t border-gray-100 items-center">
                                            <div class="mt-2 w-48 flex items-center">
                                                <h4 class="text-xs font-medium text-gray-900">{{$companyName}}</h4>
                                            </div>
                                            <div>
                                                @foreach ($companyData['roles'] as $index => $role)
                                                <div class="flex mt-2 gap-x-2">
                                                    <span class="text-xs text-gray-700">{{ $companyData['start_dates'][$index] }} to {{ $companyData['end_dates'][$index] }}</span>
                                                    <h5 class="text-xs font-medium text-gray-900">{{$role}}</h5>
                                                </div>
                                                @endforeach
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                <div class="w-80 h-96 overflow-y-scroll">
                                    <h3 class="text-sm font-bold">Skills</h3>
                                    <div class="flex mt-2 flex-wrap gap-x-4 gap-y-2">
                                    @foreach($peopleskills as $skill)
                                    <div class="flex items-center text-xs text-white p-2 rounded-full {{ $skill->skill_type == 'AI Generated' ? 'bg-purple-500' : 'bg-green-500' }}">
                                        <span>{{ $skill -> skill_name }}</span>
                                    </div>
                                    @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2 flex items-center justify-end gap-x-6">
                            </div>
                        </div>
                    </div>
                    @endif 
                </div>
                <div class="ml-10 flex">
                    <!--Trigger Button to Send to jobs -->
                    <button id="addtoShort" wire:click="addTojob({{ $Person->id }})" class="text-sm text-center text-black font-medium p-2">
                        <img class="h-6 w-auto" src="{{ asset('images/approved.png') }}">
                    </button>
                </div>
            </div>  
        </div>
        @endforeach
     </div>

     @include('livewire.loading')
</div>

