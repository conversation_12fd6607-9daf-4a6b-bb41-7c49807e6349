<div wire:poll.2s="checkJobStatus">
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            Livewire.on('toast', (type, message) => {
                switch (type) {
                    case 'success':
                        toastr.success(message);
                        break;
                    case 'info':
                        toastr.info(message);
                        break;
                    case 'warning':
                        toastr.warning(message);
                        break;
                    case 'error':
                        toastr.error(message);
                        break;
                    default:
                        console.error(`Unknown toast type: ${type}`);
                        break;
                }
            });
        });
    </script>
</div>
