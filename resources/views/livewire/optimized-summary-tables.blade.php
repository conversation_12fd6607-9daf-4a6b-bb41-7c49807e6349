<div x-data="{
    editPlanPopup: @entangle('editPlanPopup'),
    showCandidateModal: @entangle('showCandidateModal'),
    modalLoading: @entangle('modalLoading'),
    isChartsLoaded: @entangle('isChartsLoaded'),
    isCandidatesLoaded: @entangle('isCandidatesLoaded'),
    isInitialized: @entangle('isInitialized'),
    
    // Performance optimizations
    intersectionObserver: null,
    candidateData: null,
    visibleCharts: new Set(),
    
    init() {
        this.setupIntersectionObserver();
        this.setupVirtualization();
    },
    
    setupIntersectionObserver() {
        // Intersection Observer for lazy loading charts
        this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    const chartType = entry.target.dataset.chart;
                    if (chartType && !this.visibleCharts.has(chartType)) {
                        this.visibleCharts.add(chartType);
                        this.loadChartIfNeeded();
                    }
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });
        
        // Observe chart containers
        this.$nextTick(() => {
            document.querySelectorAll('[data-chart]').forEach(el => {
                this.intersectionObserver.observe(el);
            });
        });
    },
    
    setupVirtualization() {
        // Virtual scrolling for candidate tables
        this.$nextTick(() => {
            const tableContainer = document.getElementById('candidates-table-container');
            if (tableContainer) {
                this.setupVirtualScrolling(tableContainer);
            }
        });
    },
    
    loadChartIfNeeded() {
        if (!this.isChartsLoaded && this.visibleCharts.size > 0) {
            @this.call('loadChartData');
        }
    },
    
    showProfile(candidateId) {
        this.modalLoading = true;
        @this.call('showCandidateProfile', candidateId);
    },
    
    closeModal() {
        this.editPlanPopup = false;
        this.showCandidateModal = false;
        @this.call('closeCandidateModal');
    }
}" 
@load-chart-data.window="loadChartIfNeeded()"
@candidate-profile-loaded.window="candidateData = $event.detail; modalLoading = false;">

    <!-- Loading Modal for candidate profiles -->
    <x-loading-modal 
        :show="modalLoading" 
        title="Loading Candidate Profile" 
        description="Please wait while we load the candidate details."
        type="skeleton" />

    <div class="planDesignerHeight">
        <!-- Header Section -->
        <div class="flex flexRow flex-col lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
            <div class="flex gap-4 items-center">
                <a href="{{ route('plan.index') }}" class="flex gap-2 mainBlue items-center">
                    <img class="" src="{{ asset('images/ArrowLeftBlue.svg') }}">
                    <span>Back</span>
                </a>
                <h1 class="whitespace-nowrap px-4 text-black text-2xl">Plan Designer</h1>
            </div>

            <div class="flex gap-x-5 gap-y-2 flex-wrap">
                <a href="{{ route('plan.people', $plan->id) }}" class="flex gap-2 items-center bg-white border-2 border-gray-300 rounded-md px-4 py-2 text-black hover:bg-gray-50 transition-colors">
                    <img src="{{ asset('images/peoplesearchIcon.svg') }}">
                    <span>People Search</span>
                </a>

                <a href="{{ route('plan.internalsuccess', $plan->id) }}" class="flex gap-2 items-center bg-white border-2 border-gray-300 rounded-md px-4 py-2 text-black hover:bg-gray-50 transition-colors">
                    <img src="{{ asset('images/search-icon.svg') }}">
                    <span>Internal Search</span>
                </a>

                <button class="flex gap-2 items-center bg-white border-2 border-gray-300 rounded-md px-4 py-2 text-black hover:bg-gray-50 transition-colors">
                    <img src="{{ asset('images/editicon.svg') }}">
                    <span>Edit</span>
                </button>

                <a href="{{ route('plan.final', $plan->id) }}" class="flex gap-2 items-center bg-white border-2 border-gray-300 rounded-md px-4 py-2 text-black hover:bg-gray-50 transition-colors">
                    <img src="{{ asset('images/reportIcon.svg') }}">
                    <span>View Report</span>
                </a>
            </div>
        </div>

        <!-- Plan Details Section -->
        <div class="bg-white px-10 py-6">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <div class="flex items-center gap-4 mb-4">
                        <h1 class="text-3xl font-bold text-gray-900">{{ $plandetails[0]->name ?? 'Loading...' }}</h1>
                        <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">
                            {{ $plandetails[0]->status ?? 'Draft' }}
                        </span>
                    </div>
                    
                    <div class="text-gray-700 leading-relaxed mb-6">
                        {{ $plandetails[0]->description ?? 'Loading plan description...' }}
                    </div>

                    <!-- Shared With Section -->
                    <div class="flex items-center gap-2 mb-6">
                        <img src="{{ asset('images/shareNetwork.svg') }}" class="w-5 h-5">
                        <span class="text-sm text-gray-600">Shared With</span>
                        @if($shared_users->isNotEmpty())
                            <div class="flex gap-2">
                                @foreach($shared_users as $user)
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">{{ $user }}</span>
                                @endforeach
                            </div>
                        @else
                            <button class="px-3 py-1 border border-dashed border-gray-300 rounded text-sm text-gray-500 hover:bg-gray-50">
                                Add users to collaborate with your planning
                            </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Requirements Section with Virtualized Tags -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold mb-4">Requirements</h2>
                <div class="requirements-tags-container" id="requirements-container" 
                     x-data="{
                         tagSystem: null,
                         init() {
                             this.setupTagSystem();
                         },
                         setupTagSystem() {
                             const tags = @json($requirements->map(fn($req) => [
                                 'id' => $req->id,
                                 'text' => $req->name,
                                 'active' => false,
                                 'count' => 0,
                                 'category' => 'requirement'
                             ]));
                             
                             this.tagSystem = new VirtualTagSystem(this.$el, {
                                 maxVisibleTags: 8,
                                 showMoreText: 'Show More Requirements',
                                 showLessText: 'Show Less'
                             });
                             
                             this.tagSystem.setTags(tags);
                             
                             // Listen for tag interactions
                             this.$el.addEventListener('tagClick', (e) => {
                                 const { tag, active } = e.detail;
                                 @this.call('updateRequirementFilter', tag.id, active);
                             });
                         }
                     }">
                    @if($requirements && $requirements->count() === 0)
                        <x-skeleton-loader type="default" :rows="1" class="w-full h-8" />
                    @endif
                </div>
            </div>
        </div>

        <!-- Charts Section with Lazy Loading -->
        <div class="bg-white px-10 py-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Company Split Chart -->
            <div class="bg-white rounded-lg shadow" data-chart="company">
                <h3 class="text-lg font-semibold p-4 border-b">Company Split</h3>
                <div class="p-4">
                    <template x-if="!isChartsLoaded">
                        <x-skeleton-loader type="chart" />
                    </template>
                    <template x-if="isChartsLoaded">
                        <div id="company-chart" class="h-64">
                            <!-- Chart will be rendered here -->
                        </div>
                    </template>
                </div>
            </div>

            <!-- Gender Distribution Chart -->
            <div class="bg-white rounded-lg shadow" data-chart="gender">
                <h3 class="text-lg font-semibold p-4 border-b">Gender Distribution</h3>
                <div class="p-4">
                    <template x-if="!isChartsLoaded">
                        <x-skeleton-loader type="chart" />
                    </template>
                    <template x-if="isChartsLoaded">
                        <div id="gender-chart" class="h-64">
                            <!-- Chart will be rendered here -->
                        </div>
                    </template>
                </div>
            </div>

            <!-- Function Distribution Chart -->
            <div class="bg-white rounded-lg shadow" data-chart="function">
                <h3 class="text-lg font-semibold p-4 border-b">Functions</h3>
                <div class="p-4">
                    <template x-if="!isChartsLoaded">
                        <x-skeleton-loader type="chart" />
                    </template>
                    <template x-if="isChartsLoaded">
                        <div id="function-chart" class="h-64">
                            <!-- Chart will be rendered here -->
                        </div>
                    </template>
                </div>
            </div>

            <!-- Candidate Type Chart -->
            <div class="bg-white rounded-lg shadow" data-chart="type">
                <h3 class="text-lg font-semibold p-4 border-b">Candidates Type</h3>
                <div class="p-4">
                    <template x-if="!isChartsLoaded">
                        <x-skeleton-loader type="chart" />
                    </template>
                    <template x-if="isChartsLoaded">
                        <div id="type-chart" class="h-64">
                            <!-- Chart will be rendered here -->
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- Candidates Section with Virtual Scrolling -->
        <div class="bg-white px-10 py-6">
            <!-- Succession Candidates -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Succession Candidates</h3>
                    <a href="{{ route('plan.orgchart', $plan->id) }}" class="text-blue-600 hover:text-blue-800">
                        View as Org Chart
                    </a>
                </div>

                <div class="overflow-hidden rounded-lg shadow">
                    <template x-if="!isInitialized">
                        <x-skeleton-loader type="table" :rows="5" />
                    </template>
                    <template x-if="isInitialized">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($successpeople as $person)
                                    <tr class="hover:bg-gray-50 cursor-pointer" 
                                        @click="$dispatch('candidate-profile-modal', { candidateId: {{ $person->id }} })">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $person->first_name }} {{ $person->last_name }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $person->latest_role }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $person->company_name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                {{ $person->status === 'Approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                {{ $person->status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button class="text-blue-600 hover:text-blue-900">View</button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                            No succession candidates found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </template>
                </div>
            </div>

            <!-- Potential Candidates -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Potential Candidates</h3>
                    <a href="{{ route('plan.success_people', $plan->id) }}" class="text-blue-600 hover:text-blue-800">
                        View All
                    </a>
                </div>

                <div class="overflow-hidden rounded-lg shadow" id="candidates-table-container">
                    <template x-if="!isInitialized">
                        <x-skeleton-loader type="table" :rows="10" />
                    </template>
                    <template x-if="isInitialized">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($pipelinepeople as $person)
                                    <tr class="hover:bg-gray-50 cursor-pointer" 
                                        @click="$dispatch('candidate-profile-modal', { candidateId: {{ $person->id }} })">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $person->first_name }} {{ $person->last_name }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $person->latest_role }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $person->company_name }}
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                                            No potential candidates found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </template>
                </div>
            </div>
        </div>

        <!-- Enhanced Modal with ModalManager Integration -->
        <div x-data="{
            openCandidateModal(candidateId) {
                this.modalLoading = true;
                
                const modalContent = `
                    <div class='p-6'>
                        <div class='modal-header flex items-center justify-between pb-4 border-b'>
                            <h3 class='text-lg font-medium text-gray-900' id='modal-title-candidate'>Candidate Profile</h3>
                        </div>
                        <div class='modal-body pt-4'>
                            <div class='skeleton-loader'>
                                <div class='animate-pulse'>
                                    <div class='h-4 bg-gray-200 rounded w-3/4 mb-2'></div>
                                    <div class='h-4 bg-gray-200 rounded w-1/2 mb-2'></div>
                                    <div class='h-20 bg-gray-200 rounded mb-4'></div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                window.modalManager.openModal('candidate-profile', modalContent, {
                    className: 'max-w-4xl w-full max-h-[90vh] overflow-y-auto',
                    removeOnClose: true,
                    animationDuration: 300
                });
                
                // Load candidate data
                @this.call('showCandidateProfile', candidateId)
                    .then((response) => {
                        this.updateModalWithCandidateData(response);
                        this.modalLoading = false;
                    })
                    .catch((error) => {
                        console.error('Failed to load candidate:', error);
                        window.modalManager.closeModal('candidate-profile');
                        this.modalLoading = false;
                    });
            },
            
            updateModalWithCandidateData(candidateData) {
                const modalContent = this.buildCandidateModalContent(candidateData);
                window.modalManager.updateModalContent('candidate-profile', modalContent);
            },
            
            buildCandidateModalContent(data) {
                return `
                    <div class='p-6'>
                        <div class='modal-header flex items-center justify-between pb-4 border-b'>
                            <h3 class='text-lg font-medium text-gray-900' id='modal-title-candidate'>
                                ${data.candidate.first_name} ${data.candidate.last_name}
                            </h3>
                        </div>
                        <div class='modal-body pt-4 space-y-6'>
                            <div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
                                <div>
                                    <h4 class='font-semibold text-gray-900 mb-2'>Personal Information</h4>
                                    <div class='space-y-2 text-sm text-gray-600'>
                                        <p><span class='font-medium'>Role:</span> ${data.candidate.latest_role || 'N/A'}</p>
                                        <p><span class='font-medium'>Company:</span> ${data.candidate.company_name || 'N/A'}</p>
                                        <p><span class='font-medium'>Location:</span> ${data.candidate.location || 'N/A'}</p>
                                    </div>
                                </div>
                                <div>
                                    <h4 class='font-semibold text-gray-900 mb-2'>Skills</h4>
                                    <div class='flex flex-wrap gap-2'>
                                        ${data.skills ? data.skills.map(skill => 
                                            `<span class='px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs'>${skill}</span>`
                                        ).join('') : '<span class="text-gray-500 text-sm">No skills available</span>'}
                                    </div>
                                </div>
                            </div>
                            
                            ${data.career_history && data.career_history.length > 0 ? `
                                <div>
                                    <h4 class='font-semibold text-gray-900 mb-2'>Career History</h4>
                                    <div class='space-y-3'>
                                        ${data.career_history.slice(0, 3).map(job => `
                                            <div class='border-l-2 border-blue-200 pl-4'>
                                                <p class='font-medium text-gray-900'>${job.role || 'Role not specified'}</p>
                                                <p class='text-sm text-gray-600'>${job.company || 'Company not specified'}</p>
                                                <p class='text-xs text-gray-500'>${job.duration || 'Duration not specified'}</p>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class='modal-footer pt-4 border-t mt-6 flex justify-end space-x-3'>
                            <button onclick='window.modalManager.closeModal(\"candidate-profile\")' 
                                    class='px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50'>
                                Close
                            </button>
                            <button class='px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700'>
                                Add to Plan
                            </button>
                        </div>
                    </div>
                `;
            }
        }"
        @candidate-profile-modal.window="openCandidateModal($event.detail.candidateId)">
        </div>
    </div>

    <!-- JavaScript for chart rendering and virtualization -->
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('chartRenderer', () => ({
                renderCharts() {
                    if (this.isChartsLoaded) {
                        // Render charts using Chart.js or ApexCharts
                        this.renderCompanyChart();
                        this.renderGenderChart();
                        this.renderFunctionChart();
                        this.renderTypeChart();
                    }
                },
                
                renderCompanyChart() {
                    // Chart rendering logic here
                },
                
                renderGenderChart() {
                    // Chart rendering logic here
                },
                
                renderFunctionChart() {
                    // Chart rendering logic here
                },
                
                renderTypeChart() {
                    // Chart rendering logic here
                }
            }));
        });
    </script>
</div>