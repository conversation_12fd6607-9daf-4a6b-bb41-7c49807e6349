<div x-data="{
    editPlanPopup: @entangle('editPlanPopup'),
    closeModal() {
        this.editPlanPopup = false;
        this.$dispatch('modalClosed');
    },
    selectedIndustriesArr: @entangle('selectedIndustries'),
    selectedSectorsArr: @entangle('selectedSectors'),
    step: @entangle('step'),
    name: @entangle('name'),  // Entangle with Livewire property
    descriptions: @entangle('descriptions'),  // Entangle with Livewire property
    errors: {},
    maxWords: 300,

    constraints: {
        name: {
            presence: { allowEmpty: false, message: '^Name is required.' }
        },
    },
    // Helper function to count words
    wordCount(text) {
        return text.trim().split(/\s+/).length;
    },

    
    // Computed property to show remaining words if within the limit
    get remainingWords() {
        const currentWordCount = this.wordCount(this.descriptions);
        return currentWordCount <= this.maxWords ? this.maxWords - currentWordCount : null;
    },

    // Computed property for word limit error message if limit is exceeded
    get wordLimitError() {
        const currentWordCount = this.wordCount(this.descriptions);
        return this.wordCount(this.descriptions) > this.maxWords 
            ? `Description should not exceed ${this.maxWords} words. (Current count: ${currentWordCount})` 
            : null;
    },

    validateStep1() {
        let validationErrors = validate({ name: this.name }, this.constraints);
        const currentWordCount = this.wordCount(this.descriptions);

       if (!this.descriptions || this.descriptions.trim() === '') {
            validationErrors = validationErrors || {};
            validationErrors.descriptions = ['Description is required.'];
        }

        if (this.wordLimitError) {
            validationErrors = validationErrors || {};
            validationErrors.descriptions = [this.wordLimitError];
        }

        // If validation errors exist, extract the first error message for each field
        this.errors = Object.fromEntries(
            Object.entries(validationErrors || {}).map(([field, messages]) => [field, messages[0]])
        );

        // Proceed to the next step if there are no errors
        if (!Object.keys(this.errors).length) {
            this.step = 2;
        }
    }
}">

    <div class="planDesignerHeight">

        <div class="flex flexRow flex-col lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
            <div class="flex gap-4 items-center">
                <a href="{{ route('plan.index') }}" class="flex gap-2 mainBlue items-center">
                    <img class="" src="{{ asset('images/ArrowLeftBlue.svg') }}">
                    <span>Back</span>
                </a>
                <h1 class="whitespace-nowrap px-4 text-black text-2xl">Plan Designer</h1>
            </div>

            <div class="flex gap-x-5 gap-y-2 flex-wrap">

                @if ($user->id === $plandetails[0]->user_id)
                <!-- Modal to update plan details -->
                <div>

                    <!-- Modal container -->
                    <div x-show="editPlanPopup" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50" style="display:none">

                        <!-- Modal background with a higher z-index -->
                        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

                        <template x-if="step === 4">
                            <div class="modal-content step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">

                                <span class="py-3 px-3 rounded-full GreenBackground mainGreen text-center">
                                    <img class="w-6 h-6" src="{{ asset('images/right_tick_with_circle_green.svg') }}">
                                </span>

                                <h1 class="font-medium px-4 text-xl text-center my-5">Plan
                                    {{ $plandetails[0]->name }} was successfully updated!
                                </h1>

                                <!-- border above buttons -->
                                <div class="w-full border-t my-5 border-gray-200"></div>

                                <!-- buttons wrapper -->
                                <div class="flex gap-2 w-full px-4">
                                    <button @click="closeModal" type="button" class="bg-white w-full text-black border p-2 rounded-md">Close</button>
                                    <a href="{{ route('plan.success_people.index', ['plan' => $plan->id]) }}" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                        <span class="block"> Review</span>
                                    </a>

                                </div>
                            </div>
                        </template>

                        <template x-if="step !== 4">
                            <!-- Modal content with a lower z-index -->
                            <div class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">

                                <!-- cross icon -->
                                <img @click="closeModal" class="absolute right top-2 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Search Icon">

                                <h2 class="font-semibold px-4">Edit {{ $plandetails[0]->name }}</h2>

                                <div class="flex justify-between mt-3 px-4">
                                    <button @click="step = 1">
                                        <span
                                            class="mainBlue p-1 rounded-full px-2 text-xs"
                                            :class="{ 'BlueBackground mainBlue': step === 1, 'GreenBackground mainGreen': step > 1 }">1</span>
                                        <span class="text-xs font-medium">Plan Details</span>
                                    </button>
                                    <button @click="step = 2">
                                        <span
                                            class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs"
                                            :class="{ 'BlueBackground mainBlue': step === 2, 'GreenBackground mainGreen': step > 2 }">2</span>
                                        <span class="text-xs font-medium">Current Experience</span>
                                    </button>
                                    <button @click="step = 3">
                                        <span
                                            class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs"
                                            :class="{ 'BlueBackground mainBlue': step === 3, 'GreenBackground mainGreen': step > 3 }">3</span>
                                        <span class="text-xs font-medium">Additional Details</span>
                                    </button>
                                </div>
                                <div class="w-full border-t mt-3 border-gray-200"></div>

                                <template x-if="step === 1">
                                    <!-- 1st Step -->
                                    <div class="h-full">
                                        <div class="h-5/6 flex items-center">
                                            <div class="w-full">

                                                <div class="relative py-2 bg-white">
                                                    <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                                                </div>

                                                <div class="modalscroll px-4">

                                                    <div class="mb-3">
                                                        <label for="name" class="block text-sm font-medium labelcolor">Name <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                                        <input wire:model="name" type="text" id="name" placeholder="Enter plan name" name="name" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" required />
                                                        <span x-show="errors.name" class="text-red-500 text-xs" x-text="errors.name"></span>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="description" class="block text-sm font-medium labelcolor">Description <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                                        <textarea x-on:input="remainingWords" wire:model="descriptions" id="description" name="description" placeholder="Enter plan description" rows="3" class="mt-1 placeholder:text-gray-400 outline-none block w-full p-2 border placeholder:labelcolor border-gray-300 rounded-md"></textarea>
                                                        <span x-show="wordLimitError" class="text-red-500 text-xs" x-text="wordLimitError"></span>
                                                        <span x-show="errors.descriptions && !wordLimitError" class="text-red-500 text-xs" x-text="errors.descriptions"></span>
                                                        <span x-show="errors.descriptions || wordLimitError"><br></span> <!-- Conditional line break -->

                                                        <!-- Remaining word count display only if within the limit -->
                                                        <template x-if="!wordLimitError">
                                                            <span class="text-gray-500 text-xs">Remaining words: <span x-text="remainingWords"></span></span>
                                                        </template>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="tagged-role" class="mb-1 block text-sm font-medium labelcolor" data-id="{{ base64_encode(json_encode($troles)) }}">Tagged
                                                            Role</label>
                                                           <div wire:ignore x-data="{
															value: @entangle('selectedTaggedRole'),
															options: [],
															debounceTimeout: null, // Store debounce timer reference
															async fetchRoles(query = '') {
																let response = await fetch(`/api/roles?search=${encodeURIComponent(query)}`);
																this.options = await response.json();
															},
															init() {
																this.$nextTick(() => {
																	let choices = new Choices(this.$refs.selectTaggedRole, {
																		allowHTML: true,
																		placeholder: true,
																		placeholderValue: 'Select tagged role',
																	});
																	const refreshChoices = () => {
																		choices.clearStore();
																		choices.setChoices(
																			this.options.map(({ value, label }) => ({
																				value,
																				label,
																				selected: this.value === value,
																			}))
																		);
																	};

																	// Debounced search logic
																	const debouncedSearch = async (event) => {
																		const query = event.detail.value || '';
																		
																		// If query is empty, don't fetch
																		if (query.trim() === '') {
																			return;
																		}

																		clearTimeout(this.debounceTimeout); // Clear previous timer
																		this.debounceTimeout = setTimeout(async () => {
																			await this.fetchRoles(query); // Fetch roles after debounce
																			refreshChoices(); // Refresh choices
																		}, 300); // Wait 300ms after typing stops
																	};

																	// Add event listener only once
																	if (!this.debounceTimeout) {
																		this.$refs.selectTaggedRole.addEventListener('search', debouncedSearch);
																	}

																	this.$refs.selectTaggedRole.addEventListener('change', () => {
																		this.value = choices.getValue(true);
																	});

																	this.$watch('value', refreshChoices);
																	this.$watch('options', refreshChoices);

																	// Initial fetch to populate dropdown
																	this.fetchRoles();
																});
															},
														}">
															<select x-ref="selectTaggedRole"></select>
														</div>


                                                        @error('selectedTaggedRole')
                                                        <span class="text-red-500 text-sm">{{ $message }}</span>
                                                        @enderror
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="tagged-role" class="mb-1 block text-sm font-medium labelcolor">Add
                                                            Colleague to Plan</label>
                                                        <div wire:ignore x-data="{
                                                                multiple: true,
                                                                value: @entangle('selectedColleagues'),
                                                                options: {{ json_encode($colleagues) }},
                                                                init() {
                                                                    this.$nextTick(() => {
                                                                        let choices = new Choices(this.$refs.selectColleagues, {
                                                                            allowHTML: true,
                                                                            removeItems: true,
                                                                            removeItemButton: true,
                                                                            placeholder: true,
                                                                            placeholderValue: 'Select colleague(s)',
                                                                            classNames: {
                                                                                containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'], 
                                                                                inputCloned: ['mb-0', 'p-0'],


                                                                            },
                                                                            noChoicesText: 'No colleague to choose from'
                                                                        })
                                                                        let refreshChoices = () => {
                                                                            let selection = this.multiple ? this.value : [this.value]
                                                            
                                                                            choices.clearStore()
                                                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                                                value,
                                                                                label,
                                                                                selected: selection.includes(value),
                                                                            })))
                                                                        }
                                                                        refreshChoices()
                                                            
                                                                        this.$refs.selectColleagues.addEventListener('change', () => {
                                                                            this.value = choices.getValue(true)
                                                                        })
                                                            
                                                                        this.$watch('value', () => refreshChoices())
                                                                        this.$watch('options', () => refreshChoices())
                                                                    })
                                                                }
                                                            }" class="text-black w-full">
                                                            <select x-ref="selectColleagues" :multiple="multiple"></select>
                                                        </div>
                                                    </div>


                                                </div>

                                                <!-- border above buttons -->
                                                <div class="w-full border-t mt-4 border-gray-200"></div>

                                                <!-- buttons wrapper -->
                                                <div class="flex gap-2 w-full px-4 mt-4 ">
                                                    <button @click="closeModal" type="button" class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                                                        <span class="block font-medium">Cancel</span>
                                                    </button>
                                                    <button @click="validateStep1()" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                                        <span class="block"> Continue</span>
                                                        <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                                                    </button>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </template>

                                <template x-if="step === 2">
                                    <!-- 2nd Step -->
                                    <div>

                                        <div class="modalscroll px-4">
                                            <p class="text-sm font-normal labelcolor mt-3 mb-3">Enter any
                                                requirements
                                                you have for your Succession Plan below.</p>

                                            <div class="flex items-center justify-between mt-2" x-data="{ newTargetRoles: '' }">
                                                <div>
                                                    <label for="planName" class="text-sm font-medium labelcolor">Target Role</label>
                                                    <!-- <input type="text" wire:model="roles" class="mt-1 placeholder:text-gray-400 placeholder:font-normal placeholder:text-sm  block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter target role"> -->

                                                    <!-- Input field for entering tags -->
                                                    {{-- <input type="text" wire:model.defer="roleTag"
                                                                    wire:keydown.enter="addRoleTag" wire:blur="addRoleTag"
                                                                    class=" bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                                    placeholder="Enter target role"> --}}
                                                    <input type="text" class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" placeholder="Enter target role" x-model="newTargetRoles" @keydown.enter.prevent="if(newTargetRoles !== '') { @this.newSkillData.targetRoles.push(newTargetRoles); newTargetRoles = ''; }" @blur="if(newTargetRoles !== '') { @this.newSkillData.targetRoles.push(newTargetRoles); newTargetRoles = ''; }">


                                                </div>

                                                <div>
                                                    <label for="minimum_Experience" class="text-sm font-medium labelcolor">Minimum
                                                        Tenure</label>
                                                    <input type="number" wire:model.defer="minimum_Experience" class="outline-none block text-center bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md w-32" placeholder="0" name="minimum_Experience" value="">
                                                </div>

                                                {{--
                                                                <div>
                                                                    <label for="planName"
                                                                        class="text-sm font-medium labelcolor">Minimum Tenure</label>
                                                                    <div class="Tenure mt-1">
                                                                        <button
                                                                            class="block w-8 border-2 px-1 rounded-md @if (!$min_exp) disabled @endif"
                                                                            wire:click="changeMinimumTenure('decrease')"
                                                                            @if (!$min_exp) disabled @endif>
                                                                            <img class="search-icon w-8 h-8 bg-white"
                                                                                src="{{ asset('images/Minus.svg') }}"
                                                alt="Search Icon">
                                                </button>
                                                <input type="number" wire:model="min_exp" min="0" inputmode="numeric" class="outline-none block text-center bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md w-32" placeholder="0">
                                                <button class="block w-8 px-1 border-2  rounded-md" wire:click="changeMinimumTenure('increase')">
                                                    <img class="search-icon w-8 h-8  bg-white" src="{{ asset('images/Plus.svg') }}" alt="Search Icon">
                                                </button>
                                            </div>
                                        </div>
                                        --}}
                                    </div>
                                    <!-- Display tags as individual elements -->
                                    {{-- <div class="mt-2 tagsWidth">
                                                            @foreach ($enteredRoles as $index => $role)
                                                                <span
                                                                    class="selectedBg bg-gray-2 <li class="cursor-pointer" x-init="{ eopen: false }">
                                                            <a class="p-2" x-on:click="eopen = true" wire:click.prevent="onSelectJob({{ $job->id }})">
                                    <span class="font-semibold text-sm">Update</span>
                                    </a>
                                    </li>00 mt-2 text-gray-700 px-2 py-1 rounded-full mr-2">
                                    {{ $role }}
                                    <button wire:click="removeRoleTag({{ $index }})" class="ml-1">&times;</button>
                                    </span>
                                    @endforeach
                            </div> --}}
                            <div class="mt-2 flex flex-wrap">
                                <template x-for="(skill, index) in @this.newSkillData.targetRoles" :key="index">
                                    <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                        <span x-text="skill"></span>
                                        <button type="button" class="ml-1" @click="@this.newSkillData.targetRoles.splice(index, 1)">&times;</button>
                                    </span>
                                </template>
                            </div>
                            <div class="mt-2" x-data="{ newStepUpCandidate: '' }">
                                <label for="planName" class="text-sm font-medium labelcolor">Step-up
                                    Candidates</label>
                                <!-- placeholder="Head of Risk, CFO, Not Applicable" -->
                                <!-- <input type="text" wire:model="stepup" class="mt-1 placeholder:text-gray-400 placeholder:font-normal bg-white p-2 py-3 text-sm font-normal border border-gray-300 outline-none rounded-md text-gray-900 w-full" placeholder="Enter candidates' names"> -->

                                <!-- Input field for entering tags -->
                                <input type="text" class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" placeholder="Enter Step-up candidates" x-model="newStepUpCandidate" @keydown.enter.prevent="if(newStepUpCandidate !== '') { @this.newSkillData.stepUpCandidate.push(newStepUpCandidate); newStepUpCandidate = ''; }" @blur="if(newStepUpCandidate !== '') { @this.newSkillData.stepUpCandidate.push(newStepUpCandidate); newStepUpCandidate = ''; }">

                                <div class="mt-2 flex flex-wrap">
                                    <template x-for="(skill, index) in @this.newSkillData.stepUpCandidate" :key="index">
                                        <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                            <span x-text="skill"></span>
                                            <button type="button" class="ml-1" @click="@this.newSkillData.stepUpCandidate.splice(index, 1)">&times;</button>
                                        </span>
                                    </template>
                                </div>
                            </div>

                            <div class="mt-2" x-data="{ newKeyword: '' }">
                                <label for="planName" class="text-sm font-medium labelcolor">Keyword
                                </label>
                                <input type="text" class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                    placeholder="Enter Step-up candidates" x-model="newKeyword"
                                    @keydown.enter.prevent="if(newKeyword !== '') { @this.newSkillData.keyword.push(newKeyword); newKeyword = ''; }"
                                    @blur="if(newKeyword !== '') { @this.newSkillData.keyword.push(newKeyword); newKeyword = ''; }">

                                <div class="mt-2 flex flex-wrap">
                                    <template x-for="(skill, index) in @this.newSkillData.keyword" :key="index">
                                        <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                            <span x-text="skill"></span>
                                            <button type="button" class="ml-1" @click="@this.newSkillData.keyword.splice(index, 1)">&times;</button>
                                        </span>
                                    </template>
                                </div>
                            </div>

                            <!-- <div class="mt-2">
                                <label for="planName"
                                    class="text-xs font-medium labelcolor">Sectors</label>
                                <div wire:ignore x-data="{
                                    multiple: true,
                                    value: @entangle('selectedSectors'),
                                    options: {{ json_encode($sectors) }},
                                    init() {
                                        this.$nextTick(() => {
                                            let choices = new Choices(this.$refs.selectedSectors, {
                                                allowHTML: true,
                                                removeItems: true,
                                                removeItemButton: true,
                                                placeholder: true,
                                                placeholderValue: 'Select sector(s)',
                                                classNames: {
                                                    containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                    inputCloned: ['mb-0', 'p-0', 'font-normal']
                                                },
                                                noChoicesText: 'No sectors to choose from'
                                            })
                                            let refreshChoices = () => {
                                                let selection = this.multiple ? this.value : [this.value]
                                
                                                choices.clearStore()
                                                choices.setChoices(this.options.map(({ value, label }) => ({
                                                    value,
                                                    label,
                                                    selected: selection.includes(value),
                                                })))
                                            }
                                            refreshChoices()
                                
                                            this.$refs.selectedSectors.addEventListener('change', () => {
                                                this.selectedSectorsArr = choices.getValue(true)
                                                this.$dispatch('onSelectSectorAndIndustry');
                                                this.value = choices.getValue(true)
                                            })
                                
                                            this.$watch('value', () => refreshChoices())
                                            this.$watch('options', () => refreshChoices())
                                        })
                                    }
                                }" class="text-black w-full">
                                    <select x-ref="selectedSectors" :multiple="multiple"></select>
                                </div>
                            </div> -->

                            <div class="mt-2">
                                <label for="planName"
                                    class="text-xs font-medium labelcolor">Industries</label>
                                <!-- textarea for entering Industries name -->
                                <div wire:ignore x-data="{
                                    multiple: true,
                                    value: @entangle('selectedIndustries'),
                                    options: {{ json_encode($industries) }},
                                    init() {
                                        this.$nextTick(() => {
                                            let choices = new Choices(this.$refs.selectedIndustries, {
                                                allowHTML: true,
                                                removeItems: true,
                                                removeItemButton: true,
                                                placeholder: true,
                                                placeholderValue: 'Select industry(s)',
                                                classNames: {
                                                    containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                    inputCloned: ['mb-0', 'p-0', 'font-normal']
                                                },
                                                noChoicesText: 'No industries to choose from'
                                            })
                                            let refreshChoices = () => {
                                                let selection = this.multiple ? this.value : [this.value]
                                
                                                choices.clearStore()
                                                choices.setChoices(this.options.map(({ value, label }) => ({
                                                    value,
                                                    label,
                                                    selected: selection.includes(value),
                                                })))
                                            }
                                            refreshChoices()
                                
                                            this.$refs.selectedIndustries.addEventListener('change', () => {
                                                this.selectedIndustriesArr = choices.getValue(true)
                                                this.$dispatch('onSelectSectorAndIndustry');
                                                this.value = choices.getValue(true)
                                            })
                                
                                            this.$watch('value', () => refreshChoices())
                                            this.$watch('options', () => refreshChoices())
                                        })
                                    }
                                }" class="text-black w-full">
                                    <select x-ref="selectedIndustries" :multiple="multiple"></select>
                                </div>
                            </div>

                            <div class="mt-2 mb-15">
                                <label for="planName" class="text-sm font-medium labelcolor">Companies</label>
                                <!-- textarea for entering companies name -->
                                <div wire:ignore x-data="{
                                    multiple: true,
                                    value: @entangle('selectedCompanies'),
                                    options: {{ json_encode($companies) }},
                                    allCompanies: {{ json_encode($companies) }},
                                    init() {
                                        this.options = this.filterCompaniesBasedOnSectorAndIndustries();
                                        this.$nextTick(() => {
                                            let choices = new Choices(this.$refs.selectCompanies, {
                                                allowHTML: true,
                                                removeItems: true,
                                                removeItemButton: true,
                                                placeholder: true,
                                                placeholderValue: 'Select company(s)',
                                                classNames: {
                                                    containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                    inputCloned: ['mb-0', 'p-0', 'font-normal'],


                                                },
                                                noChoicesText: 'No companies to choose from'
                                            })
                                            let refreshChoices = () => {
                                                let selection = this.multiple ? this.value : [this.value]
                                
                                                choices.clearStore()
                                                choices.setChoices(this.options.map(({ value, label }) => ({
                                                    value,
                                                    label,
                                                    selected: selection.includes(value),
                                                })))
                                            }
                                            refreshChoices()
                                
                                            this.$refs.selectCompanies.addEventListener('change', () => {
                                                this.value = choices.getValue(true)
                                            })
                                
                                            this.$watch('value', () => refreshChoices())
                                            this.$watch('options', () => refreshChoices())

                                            // Listen for the custom onSelectSectorAndIndustry event
                                            document.addEventListener('onSelectSectorAndIndustry', event => {
                                                this.options = this.filterCompaniesBasedOnSectorAndIndustries();
                                                refreshChoices();
                                            });
                                        })
                                    },
                                    filterCompaniesBasedOnSectorAndIndustries() {
                                        
                                        const selectedIndustries = this.selectedIndustriesArr;
                                        const selectedSectors = this.selectedSectorsArr;

                                        if((selectedIndustries && selectedIndustries.length > 0) && (selectedSectors && selectedSectors.length > 0)){
                                            return this.allCompanies.filter(company => 
                                                        selectedIndustries.includes(company.industry) && selectedSectors.includes(company.sector)
                                                    );
                                        }
                                        else if(selectedIndustries && selectedIndustries.length > 0)
                                            return this.allCompanies.filter(company => 
                                                        selectedIndustries.includes(company.industry)
                                                    );
                                        else if(selectedSectors && selectedSectors.length > 0)
                                            return this.allCompanies.filter(company => 
                                                        selectedSectors.includes(company.sector)
                                                    );
                                        else {
                                            return this.allCompanies;
                                        }                                                            
                                    }
                                }" class="text-black w-full">
                                    <select x-ref="selectCompanies" :multiple="multiple"></select>
                                </div>
                            </div>
                    </div>

                    <!-- border above buttons -->
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons for second step -->
                    <div class="flex gap-2 w-full mt-4 px-4">
                        <button @click="step = 1" type="button" class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                            <span class="block font-medium">Back</span>
                        </button>
                        <button @click="step = 3" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <span class="block"> Continue</span>
                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                        </button>
                    </div>
                </div>
                </template>

                <template x-if="step === 3">
                    <!-- 3rd Step -->
                    <div>

                        <div class="p-4 modalscroll3 pb-15">
                            <p class="text-sm font-normal labelcolor mb-3">Enter any requirements
                                you
                                have for your Succession Plan below.</p>
                            <div>
                                <div class="mt-2">
                                    <fieldset>
                                        <legend class="text-sm font-medium labelcolor mb-1">Gender
                                        </legend>

                                        <!-- radio button in form of buttons -->
                                        <ul class="donate-now flex gap-2">
                                            <li>
                                                <input type="radio" wire:model="gender" id="male" name="gender" value="Male" class="cursor-pointer" />
                                                <label for="male" class="text-center font-semibold labelcolor">Male</label>
                                            </li>
                                            <li>
                                                <input type="radio" wire:model="gender" id="female" name="gender" value="Female" class="cursor-pointer" />
                                                <label for="female" class="text-center font-semibold labelcolor">Female</label>
                                            </li>
                                            <li>
                                                <input type="radio" wire:model="gender" id="not_required" name="gender" value="Not Applicable" class="cursor-pointer" />
                                                <label for="not_required" class="text-center font-semibold labelcolor">Not
                                                    Required</label>
                                            </li>
                                        </ul>
                                    </fieldset>
                                </div>

                                <div class="mt-2">
                                    <label for="planName" class="text-sm font-medium labelcolor mb-1">Country</label>
                                    <div wire:ignore x-data="{
                                                                        multiple: true,
                                                                        value: @entangle('selectedCountries'),
                                                                        options: {{ json_encode($countries) }},
                                                                        init() {
                                                                            this.$nextTick(() => {
                                                                                let choices = new Choices(this.$refs.selectCountries, {
                                                                                    removeItems: true,
                                                                                    removeItemButton: true,
                                                                                    placeholder: true,
                                                                                    placeholderValue: 'Select country(s)',
                                                                                    classNames: {
                                                                                        containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                                                        inputCloned: ['mb-0', 'p-0'],
                                                                                        list: 'z-99'
                                                                                    },
                                                                                    noChoicesText: 'No countries to choose from'
                                                                                })
                                                                                let refreshChoices = () => {
                                                                                    let selection = this.multiple ? this.value : [this.value]
                                                                    
                                                                                    choices.clearStore()
                                                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                                                        value,
                                                                                        label,
                                                                                        selected: selection.includes(value),
                                                                                    })))
                                                                                }
                                                                                refreshChoices()
                                                                    
                                                                                this.$refs.selectCountries.addEventListener('change', () => {
                                                                                    this.value = choices.getValue(true)
                                                                                })
                                                                    
                                                                                this.$watch('value', () => refreshChoices())
                                                                                this.$watch('options', () => refreshChoices())
                                                                            })
                                                                        }
                                                                    }" class="text-black w-full">
                                        <select x-ref="selectCountries" :multiple="multiple"></select>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <fieldset>
                                        <div class="flex justify-between">
                                            <legend class="text-sm mb-2 font-normal text-gray-800">
                                                Is
                                                Ethnicity important?</legend>
                                            <div class="toggle-container">
                                                <label class="relative cursor-pointer">
                                                    <input type="checkbox" class="toggle-input" wire:model="ethnicity" @if ($ethnicity) checked @endif />
                                                    <span class="toggle-slider"></span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="flex-wrap">
                                            <p class="text-sm font-normal labelcolor">Please note
                                                profiling through ethnicity has a low accuracy only
                                                select if this is absolutely neccessary</p>
                                        </div>
                                        <!-- no need of this -->
                                        <!-- <div class="mt-1 flex space-x-3">
                                                                                    <div class="flex items-center gap-x-3">
                                                                                        <input type="radio" wire:model="ethnicity" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black" value="1">
                                                                                        <label for="push-yes" class="block text-sm leading-6 text-gray-900">Yes</label>
                                                                                    </div>
                                                                                    <div class="flex items-center gap-x-3">
                                                                                        <input type="radio" wire:model="ethnicity" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black" value="0">
                                                                                        <label for="push-no" class="block text-sm text-gray-900">No</label>
                                                                                    </div>
                                                                                </div> -->
                                    </fieldset>
                                </div>
                                {{-- <div class="mt-2">
                                                                            <label
                                                                                class="text-sm font-medium labelcolor">Qualifications</label>
                                                                            <!-- placeholder="CFA-Level 3, Another Qualification" -->
                                                                            <!-- <input type="text" wire:model="education" class=" bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"  placeholder="Enter qualification(s)"> -->

                                                                            <!-- Input field for entering tags -->
                                                                            <input type="text" wire:model.defer="qualificationTag"
                                                                                wire:keydown.enter="addQualificationTag"
                                                                                wire:blur="addQualificationTag"
                                                                                class=" bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                                                placeholder="Enter qualification(s)">

                                                                            <!-- Display tags as individual elements -->
                                                                            <div class="mt-2">
                                                                                @foreach ($qualifications as $index => $qualification)
                                                                                    <span
                                                                                        class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2">
                                                                                        {{ $qualification }}
                                <button wire:click="removeQualificationTag({{ $index }})" class="ml-1">&times;</button>
                                </span>
                                @endforeach
                            </div>

                        </div>
                        <div class="mt-2">
                            <label class="text-sm font-medium labelcolor">Skills</label>

                            <!-- Input field for entering tags -->
                            <input type="text" wire:model="newSkillTag" wire:keydown.enter="addSkillTag" wire:blur="addSkillTag" class=" bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" placeholder="Enter skill(s)">

                            <!-- Display tags as individual elements -->
                            <div class="mt-2">
                                @foreach ($newSkills as $index => $skill)
                                <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2">
                                    {{ $skill }}
                                    <button wire:click="removeSkillTag({{ $index }})" class="ml-1">&times;</button>
                                </span>
                                @endforeach
                            </div>
                        </div>
                        --}}



                        <div class="" x-data="{
                                                                    newQualifications: '',
                                                                    newSkills: ''
                                                                }">
                            <div>
                                <label for="qualifications" class="text-xs font-medium labelcolor">Qualifications</label>
                                <input type="text" class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" placeholder="Enter qualifications" x-model="newQualifications" @keydown.enter.prevent="if(newQualifications !== '') { @this.newSkillData.qualifications.push(newQualifications); newQualifications = ''; }" @blur="if(newQualifications !== '') { @this.newSkillData.qualifications.push(newQualifications); newQualifications = ''; }">

                                <div class="mt-2 flex flex-wrap">
                                    <template x-for="(skill, index) in @this.newSkillData.qualifications" :key="index">
                                        <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                            <span x-text="skill"></span>
                                            <button type="button" class="ml-1" @click="@this.newSkillData.qualifications.splice(index, 1)">&times;</button>
                                        </span>
                                    </template>
                                </div>
                            </div>


                            <div class="mt-4">
                                <label for="skills" class="text-xs font-medium labelcolor">Skills</label>
                                <input type="text" class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" placeholder="Enter skills" x-model="newSkills" @keydown.enter.prevent="if(newSkills !== '') { @this.newSkillData.skills.push(newSkills); newSkills = ''; }" @blur="if(newSkills !== '') { @this.newSkillData.skills.push(newSkills); newSkills = ''; }">

                                <div class="mt-2 flex flex-wrap">
                                    <template x-for="(skill, index) in @this.newSkillData.skills" :key="index">
                                        <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr- mb-1 ">
                                            <span x-text="skill"></span>
                                            <button type="button" class="ml-1" @click="@this.newSkillData.skills.splice(index, 1)">&times;</button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                        </div>

                        <div class="mt-2">
                            <fieldset>
                                <legend class="text-sm font-medium labelcolor mb-1">Status
                                </legend>

                                <!-- radio button in form of buttons -->
                                <ul class="donate-now flex gap-2">
                                    <li>
                                        <input type="radio" wire:model="status" id="draft" name="status" value="Draft" class="cursor-pointer" />
                                        <label for="draft" class="text-center font-semibold labelcolor">Draft</label>
                                    </li>
                                    <li>
                                        <input type="radio" wire:model="status" id="active" name="status" value="Active" class="cursor-pointer" />
                                        <label for="active" class="text-center font-semibold labelcolor">Active</label>
                                    </li>
                                    <li>
                                        <input type="radio" wire:model="status" id="closed" name="status" value="Closed" class="cursor-pointer" />
                                        <label for="closed" class="text-center font-semibold labelcolor">Closed</label>
                                    </li>
                                </ul>
                            </fieldset>
                        </div>
                    </div>
            </div>

            <!-- border above buttons -->
            <div class="w-full border-t  border-gray-200"></div>

            <!-- 3rd steps button -->
            <div class="flex gap-2 w-full mt-4 px-4">
                <button @click="step = 2" type="button" class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                    <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                    <span class="block font-medium">Back</span>
                </button>
                <button @click="closeModal" type="button" class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                    <span class="block font-medium">Cancel</span>
                </button>
                <button type="button" wire:click="updatePlan" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                    <img class="h-5 w-5" src="{{ asset('images/edit_pencil_white.svg') }}">
                    <span class="block">Update</span>
                </button>
            </div>

        </div>
        </template>
    </div>
    </template>

</div>
</div>
@endif

<div class="hover:shadow-lg hover:scale-105 rounded-lg">
    <div class="flex items-center justify-center w-full border text-center rounded-lg px-2">
        <a href="{{ route('plan.people.index', ['plan' => $plan->id]) }}" class="w-full flex gap-2 justify-center items-center text-black py-2 text-center text-sm font-medium">
            <img class="" src="{{ asset('images/peoplesearchIcon.svg') }}">
            <span>People Search</span>
        </a>
    </div>
</div>

@if($recruitmentProjectId)															
<div class="hover:shadow-lg hover:scale-105 rounded-lg">
    <div class="flex items-center justify-center w-full border text-center rounded-lg px-2">
        <a href="{{ route('recruitment.schedule', ['id' => $recruitmentProjectId]) }}" class="w-full flex gap-2 justify-center items-center text-black py-2 text-center text-sm font-medium">
            <img class="recruitmenticon" src="{{ asset('images/recruitment.svg') }}">
            <span>View Recruitment</span>
        </a>
    </div>
</div>
@endif

@if($internalPeoplesCount > 0)
<div class="hover:shadow-lg hover:scale-105 rounded-lg">
    <div class="flex items-center justify-center w-full border text-center rounded-lg px-2">
        <a href="{{ route('plan.internalsuccess.index', ['plan' => $plan->id]) }}" class="w-full flex gap-2 justify-center items-center text-black py-2 text-center text-sm font-medium">
            <img class="" src="{{ asset('images/ChartPieSlice.svg') }}">
            <span>Internal Search</span>
        </a>
    </div>
</div>
@endif

@if ($user->id === $plandetails[0]->user_id)
<div x-init="{ editPlanPopup: @entangle('editPlanPopup') }" class="hover:shadow-lg hover:scale-105 rounded-lg cursor-pointer">
    <div class="flex items-center justify-center w-full border text-center rounded-lg px-2">
        <a class="w-full flex gap-2 justify-center items-center text-black py-2 text-center text-sm font-medium" x-on:click="$wire.onSelectPlan({{ $plan->id }})">
            <img class="" src="{{ asset('images/editicon.svg') }}">
            <span>Edit</span>
        </a>
    </div>
</div>
@endif

@if ($plan->successcount > 0)
<div class="hover:shadow-lg hover:scale-105 rounded-lg">
    <div class="flex items-center justify-center w-full border text-center rounded-lg px-2">
        <a href="{{ route('plan.final.index', ['plan' => $plan->id]) }}" target="_blank" class="w-full flex gap-2 justify-center items-center text-black py-2 text-center text-sm font-medium">
            <img class="" src="{{ asset('images/reportIcon.svg') }}">
            <span>View Report</span>
        </a>
    </div>
</div>
@endif

</div>
</div>

<div class="grid grid-cols-1 col2 gap-4 gap-x-4 px-10 mt-4 mb-20">
    {{-- Chief Risk Officer Plan card --}}
    <div class="text-sm bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <div class="flex items-center">
            <h1 class="px-3 whitespace-nowrap font-semibold text-black text-base">{{ $plandetails[0]->name }}
            </h1>
            <span class="inline-flex flex-shrink-0 items-center rounded-lg px-3 py-2 text-sm font-medium 
                        @if ($plandetails[0]->status == 'Active') bg-green-50 text-green-700 ring-green-600/20
                        @elseif($plandetails[0]->status == 'Closed')
                            bg-red-50 text-red-700 ring-red-600/20
                        @elseif($plandetails[0]->status == 'Draft')
                            bg-blue-50 text-cyan-700 ring-cyan-300
                        @elseif($plandetails[0]->status == 'DraftProposals Pending')
                            bg-blue-50 text-cyan-700 ring-cyan-300 @endif">{{ $plandetails[0]->status }}
            </span>
            @if($plandetails[0]->mover && $plandetails[0]->mover == 'Mover')
            <img src="{{ asset('images/bell_red.svg') }}" class="ml-0" alt="">
            @endif
        </div>
        <div class="mt-3">
            <p class="px-3 text-sm font-normal GrayText">{{ $plandetails[0]->description }}</p>
        </div>
        <div x-init="{ editPlanPopup: @entangle('editPlanPopup') }" class="flex items-center gap-3 mt-3">
            <p class="px-3 text-sm font-normal flex gap-2">
                <img class="" src="{{ asset('images/sharedIcon.svg') }}">
                <span>Shared With</span>
            </p>

            @if ($shared_users === null)
            @if ($user->id === $plandetails[0]->user_id)
            <button x-on:click="$wire.onSelectPlan({{ $plandetails[0]->id }})" class="px-3 text-black">Add users to collaborate with
                your planning</button>
            @else
            <div class="flex gap-x-2 px-3">
                <span>No one</span>
            </div>
            @endif
            @else
            <div class="flex gap-x-2 px-3">
                @foreach ($shared_users as $suser)
                <span>{{ $suser }}</span>
                @endforeach
            </div>
            @endif
        </div>
        <div>
            <div class="px-3 mt-5">
                <h2 class="text-base font-semibold text-black">Requirements</h2>
                <!-- List of all Requirements -->
             
                @if ($requirements->isNotEmpty())
                <div class="mt-2 flex flex-wrap">
                    @foreach ($requirements as $requirement)
                    @php
                        $bgClass = match($requirement->type) {
                            'Role' => 'text-white bg-green-500',
                            'Minimum_Tenure' => 'text-white bg-purple-500',
                            'professional_skill' => 'text-white bg-blue-500',
                            'location' => 'text-white bg-red-500',
                            'Gender' => 'text-white bg-custom-orange',
                            'step_up' => 'text-white bg-slate-900',
                            'Company' => 'text-white bg-cyan-500',
                            default => 'GrayText grayBackground selectedBg',
                        };
                    @endphp

                    <div class="rounded-full flex items-center text-sm p-2 font-medium whitespace-nowrap mb-2 mr-2 {{ $bgClass }}">
                        <span>{{ $requirement->name }}</span>
                        <div class="ml-2 p-1">
                            <button wire:click="deleteRequirement({{ $requirement->id }})" class="">X</button>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="mt-2 flex flex-wrap">
                    <div class="flex items-center text-sm p-2 font-medium mb-2 mr-2 GrayText grayBackground">
                        <span>No Requirement(s) Found</span>
                    </div>
                </div>

                @endif
            </div>
        </div>
    </div>

    {{-- company split second card --}}
    <div class="rounded-lg bg-white shadow-md border border-gray-200 p-4">
        <h2 class="px-3 whitespace-nowrap font-semibold text-black text-base">Company Split</h2>

        @if (!$companyData)
        <p class="text-center my-28 text-gray-500">No Record(s) Found</p>
        @else
        <div class="" wire:ignore x-data="{
                        cvalues: {{ json_encode($companyData) }},
                        clabels: {{ json_encode($companyLabels) }},
                        init() {
                            let chart = new ApexCharts(this.$refs.chart, {
                                chart: {
                                    type: 'bar',
                                    stacked: true
                                },
                                plotOptions: {
                                    bar: {
                                        horizontal: true,
                                        'borderRadius': 10,
                                        'borderRadiusApplication': 'around',
                                        'borderRadiusWhenStacked': 'last',
                                        'dataLabels': {
                                            'position': 'top',
                                            'total': {
                                                'enabled': false,
                                                'offsetX': 0,
                                                'offsetY': 0,
                                                'style': {
                                                    'color': '#373d3f',
                                                    'fontSize': '12px',
                                                    'fontWeight': 600
                                                }
                                            }
                                        }
                                    }
                                },
                                tooltip: { enabled: true },
                                grid: { show: false },
                                'dataLabels': {
                                    'position': 'top',
                                    'offsetX': 15,
                                    'style': {
                                        'fontWeight': '500',
                                        'fontSize': '14px',
                                        'colors': [
                                            '373d3f'
                                        ]
                                    }
                                },
                                xaxis: {
                                    categories: this.clabels,
                                    'labels': {
                                        'show': true,
                                        'trim': true,
                                        'style': {}
                                    },
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                yaxis: {
                                    'labels': {
                                        'show': true,
                                        'trim': true,
                                        'style': {}
                                    },
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                fill: {
                                    colors: ['#FFA347']
                                },
                                series: [{
                                    name: 'Peoples',
                                    data: this.cvalues,
                                }],
                            })
                            chart.render()
                        }
                    }">
            <div x-ref="chart"></div>
        </div>
        @endif
    </div>

    @if ($successpeople->isEmpty())
    <div class="flex flex-1 min-h-500 justify-center items-center rounded-lg shadow-md p-4 border border-gray-200">
        <p class="text-sm font-light text-gray-600 text-center">Add people from pipelines, your
            organisation or from the database to your plan to unlock the quick insight functionality</p>
    </div>
    @else
    {{-- candidate type --}}
    <div class="flex flex-col pie-chart-container gap-y-3 border bg-white rounded-xl justify-center items-start shadow-md">
        <h1 class="text-xl md:text-base font-medium text-black  whitespace-nowrap"> Candidates Type</h1>
        <div class="chart-container candidateTypeDoughnut flex doughnut-chart-container gap-x-5 justify-between items-center">
            <canvas id="candidateTypeDoughnut"></canvas>
            <ul id="custom-myOrg-legend" class="custom-h-legend whitespace-nowrap"></ul>
        </div>
    </div>

    {{-- exco --}}
    <div class="flex flex-col pie-chart-container gap-y-3 bg-white rounded-xl border justify-center items-start shadow-md">
        <h1 class="text-xl md:text-base font-medium text-black  whitespace-nowrap">Exco</h1>
        <div class="chart-container flex doughnut-chart-container gap-x-5 justify-between items-center">
            <canvas id="excoDoughnut"></canvas>
            <ul id="custom-exco-legend" class="custom-h-legend whitespace-nowrap"></ul>
        </div>
    </div>


    {{-- gender --}}
    <div class="w-full bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <h2 class="text-xl md:text-base font-medium text-black  whitespace-nowrap">Gender(s)</h2>
        <!-- <div class="w-full h-full" wire:ignore x-data="{
                        gvalues: {{ json_encode($genderData) }},
                        glabels: {{ json_encode($genderLabels) }},
                        colors: ['red', 'blue', 'green'],
                        init() {
                            let chart = new ApexCharts(this.$refs.genderchart, {
                                chart: {
                                    type: 'donut',
                                    width: '100%',
                                    height: '100%'
                                },
                                labels: this.glabels,
                                dataLabels: {
                                    enabled: true,
                                    textAnchor: 'start',
                                    style: { fontSize: '9px' }
                                },
                                colors: ['#0d9488', '#22d3ee', '#818cf8'],
                                series: this.gvalues,
                                fill: { colors: ['#0d9488', '#22d3ee', '#818cf8', '#0284c7'] },
                                stroke: { width: 1 },
                                legend: {
                                    position: 'bottom',
                                    markers: { fillColors: ['#0d9488', '#22d3ee', '#818cf8', '#0284c7'] }
                                },
                                tooltip: { fillSeriesColor: true },
                                responsive: [{
                                        breakpoint: 1024,
                                        options: { chart: { width: '100%', position: 'center' } }
                                    },
                                    {
                                        breakpoint: 768,
                                        options: { chart: { width: '100%', height: '100%', position: 'center' } }
                                    }
                                ]
                            })
                            chart.render()
                        }
                    }">
                        <div x-ref="genderchart"></div>
                    </div> -->

        <div class="w-full h-full" wire:ignore x-data="{
                        cvalues: {{ json_encode($genderData) }},
                        clabels: {{ json_encode($genderLabels) }},
                        init() {
                            let chart = new ApexCharts(this.$refs.genderschart, {
                                chart: {
                                    type: 'bar',
                                    stacked: true
                                },
                                plotOptions: {
                                    bar: {
                                        'distributed': false,
                                        'borderRadius': 10,
                                        'borderRadiusApplication': 'around',
                                        'borderRadiusWhenStacked': 'last',
                                        'hideZeroBarsWhenGrouped': false,
                                        'isDumbbell': false,
                                        'isFunnel': false,
                                        'isFunnel3d': true,
                                        'dataLabels': {
                                            'position': 'top',
                                            'total': {
                                                'enabled': false,
                                                'offsetX': 0,
                                                'offsetY': 0,
                                                'style': {
                                                    'color': '#373d3f',
                                                    'fontSize': '12px',
                                                    'fontWeight': 600
                                                }
                                            }
                                        }
                                    }
                                },
                                tooltip: { enabled: true },
                                grid: { show: false },
                                xaxis: {
                                    categories: this.clabels,
                                    'labels': {
                                        'show': false,
                                        'trim': true,
                                        'style': {}
                                    },
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                yaxis: {
                                    show: false
                                },
                                fill: {
                                    colors: ['#3B82F6']
                                },
                                'dataLabels': {
                                    'position': 'top',
                                    'formatter': function(val, opts) {
                                        let label = opts.w.config.xaxis.categories[opts.dataPointIndex];
                                        // Customize the data label text here
                                        return label + ' (' + val + ')';
                                    },
                                    'offsetY': -25,
                                    'style': {
                                        'fontWeight': '600',
                                        'fontSize': '14px',
                                        'colors': [
                                            '373d3f'
                                        ]
                                    }
                                },
                                series: [{
                                    name: 'Gender',
                                    data: this.cvalues,
                                }],
                            })
                            chart.render()
                        }
                    }">
            <div x-ref="genderschart"></div>
        </div>
    </div>

    {{-- Function(s) --}}
    <div class="w-full bg-white rounded-lg shadow-md border border-gray-200 p-4">
        <h2 class="text-xl md:text-base font-medium text-black  whitespace-nowrap">Functions</h2>

        <div class="w-full h-full" wire:ignore x-data="{
                        cvalues: {{ json_encode($functionData) }},
                        clabels: {{ json_encode($functionLabels) }},
                        init() {
                            let chart = new ApexCharts(this.$refs.functionchart, {
                                chart: {
                                    type: 'bar',
                                    stacked: true
                                },
                                plotOptions: {
                                    bar: {
                                        horizontal: true,
                                        'borderRadius': 10,
                                        'borderRadiusApplication': 'around',
                                        'borderRadiusWhenStacked': 'last',
                                        'dataLabels': {
                                            'position': 'top',
                                            'total': {
                                                'enabled': false,
                                                'offsetX': 0,
                                                'offsetY': 0,
                                                'style': {
                                                    'color': '#373d3f',
                                                    'fontSize': '12px',
                                                    'fontWeight': 500
                                                }
                                            }
                                        }
                                    }
                                },
                                tooltip: { enabled: true },
                                grid: { show: false },
                                xaxis: {
                                    categories: this.clabels,
                                    'labels': {
                                        'show': false,
                                        'trim': true,
                                        'style': {}
                                    },
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                yaxis: {
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                fill: {
                                    colors: ['#3B82F6', '#FFA347', '#8B5CF6']
                                },
                                'dataLabels': {
                                    'position': 'top',
                                    'offsetX': 15,
                                    'style': {
                                        'fontWeight': '500',
                                        'fontSize': '14px',
                                        'colors': [
                                            '373d3f'
                                        ]
                                    }
                                },
                                series: [{
                                    name: 'Functions',
                                    data: this.cvalues,
                                }],
                            })
                            chart.render()
                        }
                    }">
            <div x-ref="functionchart"></div>
        </div>
    </div>
    @endif

    {{-- succession candidate --}}
    <div class="text-sm text-black font-medium bg-white w-full rounded-lg shadow-md border border-gray-200 p-4" x-data="{
                    dopen: false,
                    ropen: false,
                    vopen: false,
                    uopen: false,
                    tab: 'summary',
                    nopen: false,
                    enopen: false,
                    esummary: false,
                    closeViewDetailModal() {
                        this.vopen = false;
                        this.esummary = false;
                        this.$dispatch('viewDetailModalClosed');
                    },
					closeRecruitModal() {
                        this.ropen = false;
                        this.esummary = false;
                        this.$dispatch('recruitModalClosed');
                    }
                }">

        @php
        $isMoverExist = false;
        if($successpeople->isNotEmpty()){
        foreach($successpeople as $successPerson){
        if($successPerson->mover == 'Mover'){
        $isMoverExist = true;
        break;
        }
        }
        }
        @endphp

        <div class="flex items-center">
            <h3 class="text-black text-base">Succession Candidates</h3>

            @if($isMoverExist)
                <img src="{{ asset('images/bell_red.svg') }}" class="bell-icon" alt="">
            @endif

            @if (!$successpeople->isEmpty())
            <a class="w-[170px] h-[32px] gap-[4px] rounded-md border border-gray-300 px-[4px] py-[6px] pr-[8px] flex items-center ml-auto px-1" href="{{ route('plan-org-chart.view', $plan->id) }}">
                <img src="{{ asset('images/TreeStructure.png') }}" class="relative top-[1px] mx-[5px]" alt="">
                <span class="text-sm font-semibold">View as Org Chart</span>
            </a>
            @endif
        </div>


        @if ($successpeople->isEmpty())
        <div class="h-80 items-center justify-center mt-20">
            <!-- <div class="flex flex-1 items-center justify-center">
                            <img class="h-20 w-auto" src="{{ asset('images/SAM.png') }}">
                        </div> -->
            <!-- <h4 class="text-sm text-center text-black font-semibold mb-2"> No Succession Candidates </h4> -->
            <p class="text-center text-gray-500">No Succession Candidates</p>
            <p class="text-sm font-light text-gray-600 text-center">Add candidates from your
                Pipeline, Internal pool or the people dashboard to see your top candidates.</p>
        </div>
        @else
        <div class = "mt-6 h-96 overflow-y-auto">
        <table class="table-auto SuccessionTablew-full min-w-full divide-y divide-gray-300  border rounded-xl overflow-x-auto">
            <thead>
                <tr class="GrayText border rounded-lg grayBackground">
                    <th scope="col" class="py-3.5 px-3 text-left text-sm font-semibold">Name</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold">Role</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold">Company</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold">Status</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold"></th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">

                @foreach ($successpeople as $successPerson)
                <tr class="@if ($successPerson->mover == 'Mover') RedBG moverRow @endif">
                    <td class=" md:px-3 md:py-2 text-sm text-gray-500 md:pl-4 sm:pl-4">
                        {{ $successPerson->first_name }} {{ $successPerson->last_name }}
                    </td>

                    <td class=" md:px-3 md:py-2 text-sm text-gray-500 md:pl-4 sm:pl-4">
                        {{ $successPerson->latest_role }}
                    </td>

                    @if ($successPerson->type == 'Internal')
                    <td class=" md:px-3 md:py-2 text-sm text-gray-500 md:pl-4 sm:pl-4">
                        Internal
                    </td>
                    @else
                    <td class="md:px-3 md:py-2 text-sm text-gray-500 md:pl-4 sm:pl-4">
                        {{ $successPerson->company_name }}
                    </td>
                    @endif
                    <td class="md:px-3 md:py-2 text-sm text-gray-500 md:pl-4 sm:pl-4">
                        @if(strtolower($successPerson->status) == "approved")
                        <span class="success-badge">
                            {{$successPerson->status}}
                        </span>

                        @else
                        <span class="info-badge">
                            {{$successPerson->status}}
                        </span>
                        @endif
                    </td>

                    <td class="flex items-center h-full py-2 text-sm text-gray-500">

                        <div class="flex">
                            <div class="flex gap-x-2">

                                <!-- The view button -->
                                <div class="dropdown-container mt-2 relative">

                                    <!-- Trigger button inside the modal -->

                                    <!-- <button id="view" @click="dopen = true"
                                                        wire:click="saveSelectedPeople({{ $successPerson->id }})"
                                                        class="w-full relative flex items-center justify-center gap-x-2 rounded-br-lg border border-transparent hover:scale-105">
                                                        <img class="h-4 w-auto" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                                    </button> -->


                                    <button tabindex="1" id="dropdownDefaultButton" data-dropdown-toggle="dropdown" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" type="button">
                                        <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                    </button>

                                    <!-- Dropdown menu -->
                                    <div id="dropdown" class="dropdown-menu z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                        <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">

                                            <li class="cursor-pointer" @click="vopen=true" wire:click.prevent="viewSelectedPeople({{ $successPerson->id }}, {{ $successPerson->people_id }}, '{{ $successPerson->type }}')">
                                                <button class="p-0">
                                                    <div class="flex gap-4 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                        <span class="font-semibold text-sm">View</span>
                                                    </div>
                                                </button>
                                            </li>   
										
								
										
											<li class="cursor-pointer" 
											@if(!$recruitmentProjectId)
												@click="ropen=true; $wire.selectedpersonId='{{ $successPerson->people_id }}'"
											@endif
											wire:click.prevent="{{ $recruitmentProjectId ? 'savePlanPeopleId(' . $successPerson->people_id . ', ' . $recruitmentProjectId . ')' : 'viewSelectedPeople(' . $successPerson->id . ', ' . $successPerson->people_id . ', \'' . $successPerson->type . '\')' }}">
											<button class="p-0">
												<div class="flex gap-4 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
													<span class="font-semibold text-sm">Recruit</span>
												</div>
											</button>
										</li>
							
                                            <!-- Linked IN button -->
                                            @if ($successPerson->type !== 'Internal' && $successPerson->linkedinURL && $successPerson->linkedinURL !== 'NA')
                                            <li class="cursor-pointer">
                                                <div class="flex items-center mt-2">
                                                    <a id="linkedin" class="p-0" href="{{ $successPerson->linkedinURL }}" target="_blank">
                                                        <div class="flex gap-4 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            <span class="font-semibold text-sm">
                                                                View on LinkedIn
                                                            </span>
                                                        </div>
                                                    </a>
                                                </div>
                                            </li>
                                            @endif

                                            <!-- Approve Functioality -->
                                            @if ($user->id === $plandetails[0]->user_id && $successPerson->status === 'Proposed')
                                            <li class="cursor-pointer" @click="confirmApprovePerson({{ $successPerson->id }}, '{{ $successPerson->first_name }} {{ $successPerson->last_name }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                <!-- wire:click="approveSuccessPerson({{ $successPerson->id }})" -->
                                                <button class="p-0">
                                                    <div class="flex gap-4 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                        <span class="font-semibold text-sm">Approve</span>
                                                    </div>
                                                </button>
                                            </li>
                                            @endif 

                                            <li class="cursor-pointer"
                                                @click="confirmRemovePerson({{ $successPerson->id }}, '{{ htmlentities($successPerson->first_name) }} {{ htmlentities($successPerson->last_name) }}', '{{ asset('images/redTrashIcon.svg') }}')"> 
                                                <a class="p-0">
                                                    <div class="flex gap-5 py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                        <span class="font-semibold text-sm text-danger">Delete</span>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
        </div>

        @if ($filteredPeople->isNotEmpty() && $savedPeople == $filteredPeople[0]->id)
        <div x-show="vopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" style="display:none">
            <!-- Modal background with a higher z-index -->
            <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
            <!-- Modal content with a lower z-index -->
            <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50" x-show="vopen" x-transition>
                <!-- Modal content -->
                <div class="flex flex-col h-full">
                    <div class="flex justify-between border-b px-2 py-2">
                        <h3 class="text-base font-bold flex gap-2">
                            @if($filteredPeople[0]->mover == 'Mover' && $filteredPeople[0]->current_company)
                            {{ $filteredPeople[0]->company_name }}
                            <img src="{{ asset('images/right-arrow-black.svg') }}" alt="">
                            <span class="text-red-500">{{ $filteredPeople[0]->current_company }}</span>
                            @else
                            {{ $filteredPeople[0]->company_name }}
                            @endif
                        </h3>
                        <div class="flex">
                            @if($filteredPeople[0]->mover == 'Mover')
                            <img wire:click="refreshMover({{ $filteredPeople[0] }})" title="Refresh" class="h-4 w-4 mr-2 cursor-pointer" src="{{ asset('images/sync-alt-solid.svg') }}" alt="">
                            @endif
                            <img @click="closeViewDetailModal" class="h-5 w-5 cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-x-2 pt-3 flex-1">
                        <div class="flex flex-1 flex-col justify-between">
                            <div class="flex px-2 py-2 gap-2 justify-between">

                                <div class="flex flex-col gap-y-2">
                                    <h3 class="text-base font-bold">
                                        {{ $filteredPeople[0]->first_name }}
                                        {{ $filteredPeople[0]->last_name }}
                                    </h3>
                                    <h3 class="text-base text-gray-700 d-ruby gap-2">
                                        @if($filteredPeople[0]->mover == 'Mover' && $filteredPeople[0]->current_role)

                                        <span>{{ $filteredPeople[0]->latest_role }} </span>
                                        &nbsp;<img src="{{ asset('images/right-arrow-black.svg') }}" alt=""> &nbsp;
                                        <span class="text-red-500">{{ $filteredPeople[0]->current_role }}</span>
                                        @else
                                        <span>{{ $filteredPeople[0]->latest_role }} </span>
                                        @endif
                                    </h3>
                                </div>

                                <div class="flex gap-x-2 items-center justify-center">
                                    @if(strtolower($filteredPeople[0]->status) != "approved")
                                    <div wire:click.prevent="approveSuccessPeople({{$filteredPeople[0]->id}})" class="flex cursor-pointer  px-2 h-8 py-5 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18 32.4L9.6 24l-2.8 2.8L18 38 42 14l-2.8-2.8L18 32.4z" fill="#4CAF50" />
                                        </svg>
                                    </div>
                                    @endif
                                    <div class="flex cursor-pointer  scoreBtn px-2 h-8 py-5 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                        <img class="h-4 w-4 mr-2" src="{{ asset('images/editicon.svg') }}" alt="">
                                        <h2 @click="uopen = true" class=" font-semibold">Update Scores</h2>
                                    </div>
                                    @if ($filteredPeople[0]->linkedinURL && $filteredPeople[0]->linkedinURL != 'NA')
                                    <a id="linkedin" class="flex items-center justify-center hover:scale-105" href="{{ $filteredPeople[0]->linkedinURL }}" target="_blank">
                                        <img src="{{ asset('images/LinkedIn.svg') }}">
                                    </a>
                                    @endif
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-2 grid-rows-4 flex-grow">
                                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                    <h3 class="text-sm font-bold chart-heading">Potential Sex</h3>
                                    <h3 class="text-sm text-gray-700">{{ $filteredPeople[0]->gender }}
                                    </h3>
                                </div>
                                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                    <h3 class="text-sm font-bold chart-heading">Function</h3>
                                    <h3 class="text-sm text-gray-700">
                                        {{ $filteredPeople[0]->function ? $filteredPeople[0]->function : 'Not Applicable' }}
                                    </h3>
                                </div>
                                <!-- <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                    <h3 class="text-sm font-bold chart-heading">Division</h3>
                                                    <h3 class="text-sm text-gray-700">{{ $filteredPeople[0]->division ? $filteredPeople[0]->division : 'Not Applicable' }}</h3>
                                                </div>
                                                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                    <h3 class="text-sm font-bold chart-heading">Seniority</h3>
                                                    <h3 class="text-sm text-gray-700">{{ $filteredPeople[0]->seniority ? $filteredPeople[0]->seniority : 'Not Applicable' }}</h3>
                                                </div> -->
                                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                    <h3 class="text-sm font-bold chart-heading">Tenure in Role</h3>
                                    <h3 class="text-sm text-gray-700">{{ $filteredPeople[0]->tenure }}
                                    </h3>
                                </div>
                                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                    <h3 class="text-sm font-bold chart-heading">Readiness</h3>
                                    @if ($filteredPeople[0]->readiness)
                                    @if (strtolower($filteredPeople[0]->readiness) == 'ready')
                                    <span class="px-2 py-1 text-sm rounded-lg font-medium redlinessReaddy">
                                        Ready
                                    </span>
                                    @elseif(strtolower($filteredPeople[0]->readiness) == 'not ready')
                                    <span class="px-2 py-1 text-sm rounded-lg font-medium RedBG text-red-500">
                                        Not Ready
                                    </span>
                                    @endif
                                    @else
                                    <span class="px-2 py-1 text-sm rounded-lg font-medium">
                                        N/A
                                    </span>
                                    @endif
                                </div>
                                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                    <h3 class="text-sm font-bold chart-heading">Registrations</h3>
                                    <h3 class="text-sm text-gray-700">
                                        {{ $filteredPeople[0]->other_tags ? $filteredPeople[0]->other_tags : 'Not Applicable' }}
                                    </h3>
                                </div>
                                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                    <h3 class="text-sm font-bold chart-heading">Score</h3>
                                    <h3 class="text-sm text-gray-700">
                                        {{ $filteredPeople[0]->total_score }}
                                    </h3>
                                </div>
                            </div>
                        </div>
                        <div x-data="{ tab: 'summary' }" class="flex flex-col pt-3 gap-y-2">
                            <div class="flex border-b-2">
                                <button @click="tab = 'summary'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                            'summary',
                                                        'chart-heading font-semibold': tab != 'summary'
                                                    }">
                                    Summary
                                </button>
                                <button @click="tab = 'career_history'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                            'career_history',
                                                        'chart-heading font-semibold': tab !=
                                                            'career_history'
                                                    }">
                                    Career History
                                </button>
                                <button @click="tab = 'skills'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                            'skills',
                                                        'chart-heading font-semibold': tab != 'skills'
                                                    }">
                                    Skills
                                </button>
                                <button @click="tab = 'notes'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                            'notes',
                                                        'chart-heading font-semibold': tab != 'notes'
                                                    }">
                                    Notes
                                </button>
                            </div>
                            <div x-show="tab == 'summary'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                <div class="flex justify-between">
                                    <h4 class="text-lg text-gray-700 font-semibold chart-heading mr-2">
                                        Summary</h4>
                                    <button x-show="!esummary" @click="esummary = true" type="button" class="flex justify-center items-center border py-2 px-4 rounded-md text-sm bg-mainBlue text-white">
                                        @if ($filteredPeople[0]->summary)
                                        <img class="h-4 w-4" src="{{ asset('images/edit_pencil_white.svg') }}">
                                        <span class="block text-white pl-1">&nbsp;Edit</span>
                                        @else
                                        <img class="h-4 w-4" src="{{ asset('images/plus-white-without-circle.svg') }}">
                                        <span class="block text-white pl-1">&nbsp;Add</span>
                                        @endif
                                    </button>

                                    <button x-show="esummary" @click="esummary = false" wire:click.prevent="updateSummary({{ $filteredPeople[0]->id }})" type="button" class="flex justify-center items-center border py-2 px-4 rounded-md text-sm bg-mainBlue text-white">
                                        <span class="block text-white pl-1">Save</span>
                                    </button>
                                </div>

                                @if ($filteredPeople[0]->summary)
                                <p x-show="!esummary" class="text-sm">
                                    {{ $filteredPeople[0]->summary }}
                                </p>
                                @else
                                <p x-show="!esummary" class="text-gray-500 py-5">No Summary Found
                                </p>
                                @endif

                                <textarea x-show="esummary" wire:model="successPersonSummary" rows="4" class="bg-gray-50 p-2 text-sm font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500" placeholder="Enter your summary here"></textarea>
                            </div>

                            <div x-show="tab == 'career_history'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                <h4 class="text-lg text-gray-700 font-semibold chart-heading">Career
                                    History</h4>
                                @if ($peoplescareer && $peoplescareer->isNotEmpty())
                                <div class="flex flex-col items-start w-full">
                                    @foreach ($peoplescareer as $careerHistory)
                                    <div class="flex h-max items-start justify-center mb-1">
                                        <div class="flex flex-col mt-1 items-center justify-center h-full w-2">
                                            <div class="rounded-full  blueBalls bg-mainBlue">
                                            </div>
                                            <div class="flex-grow border-l border-mainBlue">
                                            </div>
                                        </div>
                                        <div class="flex flex-col  items-start justify-start pl-4">
                                            <h4 class="text-sm font-semibold text-gray-900">
                                                {{ $careerHistory->role }}
                                            </h4>
                                            <div class="flex gap-x-2">
                                                <span class="text-sm  font-base text-gray-700">{{ $careerHistory->company_name }}</span>
                                            </div>
                                            <div class="flex gap-x-2 mb-4">
                                                <span class="text-xs font-base GrayText">{{ \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y') }}
                                                    -
                                                    {{ !empty($careerHistory->end_date) ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : 'Present' }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                                @else
                                <p class="text-sm">Not Applicable</p>
                                @endif
                            </div>

                            <div x-show="tab == 'skills'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                <h4 class="text-lg text-gray-700 font-semibold chart-heading">Skills
                                </h4>
                              
                                @if ($filteredPeople[0]->skills)
                                @php
                                $peopletypearr=[];
                                if ($successpeople->isNotEmpty()){
                                    foreach ($successpeople as $successPerson){
                                        $peopletypearr[$successPerson->people_id]=$successPerson->type;
                                    }
                                }
                                $skillsArr = explode("\n", $filteredPeople[0]->skills);
                                @endphp
                               
                                <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                    @if(!empty($peopletypearr) && $peopletypearr[$filteredPeople[0]->people_id]=='Internal')
                                        @foreach ($skillsArr as $skill)
                                        <div class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                                            <span>{{ $skill }}</span>
                                        </div>
                                        @endforeach
                                    @else
                                     @if(!empty($filteredPeople))
                                        @foreach ($filteredPeople[0]->skills_list as $skill)
                                            <div class="flex items-center text-sm p-2 rounded-xl text-white {{ $skill['skill_type'] == 'AI Generated' ? 'bg-purple-500' : 'bg-green-500' }}">
                                                <span>{{ $skill['skill_name'] }}</span>
                                            </div>
                                        @endforeach
                                        @endif
                                    @endif
                                </div>
                                @else
                                <p class="text-sm">Not Applicable</p>
                                @endif
                            </div>

                            <div x-show="tab == 'notes'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                <div class="flex justify-between">
                                    <h4 class="text-lg text-gray-700 font-semibold chart-heading pt-1">
                                        Notes</h4>
                                    <button x-show="!nopen" @click="nopen = true" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                        <img class="h-4 w-4" src="{{ asset('images/Plus.svg') }}">
                                        <span class="block text-black pl-1"> Add Notes</span>
                                    </button>
                                    <div x-show="nopen" class="flex gap-2">
                                        <button @click="nopen = false" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                            <span class="block text-black pl-1"> Cancel</span>
                                        </button>
                                        <button x-show="nopen" wire:click.prevent="addNotes({{ $filteredPeople[0]->id }})" @click="nopen = false" type="button" class="flex justify-center items-center border p-2 rounded-md text-sm bg-mainBlue text-white">
                                            <span class="block pl-1"> Save</span>
                                        </button>
                                    </div>

                                    <!-- <button x-show="!enopen" @click="enopen = true" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                                            <img class="h-4 w-4" src="{{ asset('images/editicon.svg') }}">
                                                            <span class="block text-black pl-1"> Edit Notes</span>
                                                        </button>
                                                        <div x-show="enopen" class="flex gap-2">
                                                            <button @click="enopen = false" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                                                <span class="block text-black pl-1"> Cancel</span>
                                                            </button>
                                                            <button x-show="enopen" wire:click.prevent="addNotes({{ $filteredPeople[0]->id }})" @click="enopen = false" type="button" class="flex justify-center items-center border p-2 rounded-md text-sm bg-mainBlue text-white">
                                                                <span class="block pl-1"> Save</span>
                                                            </button>
                                                        </div> -->
                                </div>

                                <div class="mt-2" x-show="nopen">
                                    <textarea type="text" wire:model="filteredPnotes" rows="4" class="bg-gray-50 p-2 text-sm font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500" placeholder="Enter your note here">
                                                    </textarea>
                                </div>

                                @if ($successPersonNotes && $successPersonNotes->isNotEmpty())
                                @foreach ($successPersonNotes as $note)
                                <div class="mt-3">
                                    <div class="flex justify-between">
                                        <h3 class="text-xl md:text-base font-medium text-black">
                                            {{ $note->user_name }}
                                        </h3>

                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-500 font-normal">{{ \Carbon\Carbon::parse($note->created_at)->format('m/d/y') }}</span>
                                            <!-- <img class="h-6 w-6 ml-1 cursor-pointer"
                                                                        @click="confirmDeleteNote({{ $note->id }}, '{{ asset('images/redTrashIcon.svg') }}')"
                                                                        src="{{ asset('images/redTrashIcon.svg') }}" alt=""
                                                                    /> -->
                                        </div>

                                    </div>
                                    <p class="text-sm text-gray-600 font-normal">
                                        {{ $note->Notes }}
                                    </p>
                                </div>
                                @endforeach
                                @else
                                <div class="mt-2" x-show="!enopen && !nopen">
                                    <p class="text-sm">No notes available</p>
                                </div>
                                @endif

                            </div>
                        </div>
                    </div>
                    <!-- <h4 class="text-sm text-cyan-500 font-medium">{{ $filteredPeople[0]->location }}</h4> -->
                </div>


                <!-- Update Score Modal container -->
                <div x-show="uopen" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
                    <!-- Modal background with a higher z-index -->
                    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                    <!-- Modal content with a lower z-index -->
                    <div class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
                        <!-- Modal content -->

                        <img @click="uopen = false" class="absolute right top-2 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Search Icon">

                        <h2 class="text-black-900 text-xl text-black font-bold px-4">Update the Plan
                            scores for {{ $filteredPeople[0]->first_name }}</h2>

                        <div class="w-full border-t mt-3 border-gray-200"></div>


                        <div class="h-full">
                            <div class="h-5/6 flex items-center">
                                <div class="w-full">

                                    <div class="modalscroll px-4">

                                        <div class="mt-4">
                                            <div class="flex justify-between mb-1">
                                                <h3 class="text-base font-medium text-black pt-1">Role
                                                    Match Score</h3>
                                                <input type="number" min="0.0" max="1.0" step="0.1" id="updaterolScore" wire:model="updaterolScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->role_match }}" style="-moz-appearance: textfield;" oninput="clampValue(this)" wire:ignore>
                                            </div>

                                            <p class="text-sm text-gray-500">
                                                Score indicating the
                                                relevance of a person's
                                                role against the
                                                requirement of the plan.
                                                (0-low to 1-High)
                                            </p>
                                        </div>

                                        <div class="mt-4">
                                            <div class="flex justify-between mb-1">
                                                <h3 class="text-base font-medium text-black pt-1">
                                                    Tenure Match Score</h3>
                                                <input type="number" min="0.0" max="1.0" step="0.1" style="-moz-appearance: textfield;" wire:ignore wire:model="updatetenureScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->tenure_match }}" oninput="clampValue(this)">
                                            </div>

                                            <p class="text-sm text-gray-500">
                                                Score indicating the
                                                relevance of a person's
                                                tenure against the
                                                requirement of the plan.
                                                (0-low to 1-High)
                                            </p>
                                        </div>

                                        <div class="mt-4">
                                            <div class="flex justify-between mb-1">
                                                <h3 class="text-base font-medium text-black pt-1">
                                                    Location Match Score </h3>
                                                <input type="number" min="0.0" .0 max="1" step="0.1" style="-moz-appearance: textfield;" wire:ignore wire:model="updatelocScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->location_match }}" oninput="clampValue(this)">
                                            </div>

                                            <p class="text-sm text-gray-500">
                                                Score indicating the
                                                relevance of a person's
                                                location against the
                                                requirement of the plan.
                                                (0-low to 1-High)</p>
                                        </div>

                                        <div class="mt-4">
                                            <div class="flex justify-between mb-1">
                                                <h3 class="text-base font-medium text-black pt-1">
                                                    Gender Match Score</h3>
                                                <input type="number" min="0.0" max="1.0" step="0.1" style="-moz-appearance: textfield;" wire:ignore wire:model="updategenScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->gender_match }}" oninput="clampValue(this)">
                                            </div>
                                            <p class="text-sm text-gray-500">
                                                Score indicating the
                                                relevance of a person's
                                                likely gender against
                                                the requirement of the
                                                plan. (0-low to 1-High)
                                            </p>
                                        </div>
                                        <div class="mt-4">
                                            <div class="flex justify-between mb-1">
                                                <h3 class="text-base font-medium text-black pt-1">Skill
                                                    Match Score</h3>
                                                <input type="number" min="0.0" max="1.0" step="0.1" style="-moz-appearance: textfield;" wire:ignore wire:model="updateskillScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->skills_match }}" oninput="clampValue(this)">
                                            </div>


                                            <p class="text-sm text-gray-500">
                                                Score indicating the
                                                relevance of a person's
                                                skills against the
                                                requirement of the plan.
                                                (0-low to 1-High)
                                            </p>
                                        </div>
                                    </div>

                                    <div class="w-full border-t mt-4 border-gray-200"></div>

                                    <!-- buttons wrapper -->
                                    <div class="flex gap-2 w-full px-4 mt-4 ">
                                        <button @click="uopen = false" type="button" class="text-base bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                        <button wire:click.prevent="updateScore({{ $filteredPeople[0]->id }})" type="submit" class="text-base text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                            <span class="block"> Update</span>
                                        </button>
                                    </div>

                                </div>


                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
   
		
		<div x-show="ropen" @project-saved.window="ropen = false"  class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" style="display:none">
			<!-- Modal background with a higher z-index -->
			<div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
			<!-- Modal content with a lower z-index -->
			<div class="ropen-modal-content modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50" x-show="ropen" x-transition>
					<div class="flex flex-col h-full">
					<div class="flex justify-between border-b px-2 py-2">
						<h3 class="text-base font-bold flex gap-2">Add to Recruitment</h3>
						<div class="flex">
							<img @click="closeRecruitModal" class="h-5 w-5 cursor-pointer" src="http://127.0.0.1:8000/images/cross.svg" alt="">
						</div>
					</div>
					<div class="w-full">
							  <div class="mb-3 mt-3 px-2 py-2">
								<div class="px-4">
									<!--<div class="recruitment-title">
										<div class="mb-3 flex">
											<label class="self-center w-64 text-sm text-gray-600">Recruitment Name</label>
											<input type="text" placeholder="Enter the Title" wire:model="recruitmentTitle" class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md">
										</div>
									</div>-->

									<div class="text-sm mb-3">Select how many interviews are in this recruitment process.</div>

									 <div class="mb-3 flex">
										<label for="interviewCount" class="self-center w-40 text-sm text-gray-600">Number of Stages</label>
										<div class="flex items-center gap-3 mt-1">
											<button wire:click="decrement" class="border border-blue-500 rounded-lg w-8 h-8">-</button>
											<input type="number" id="interviewCount" wire:model.lazy="interviewCount" class="w-16 h-8 rounded-md p-2 text-center border border-blue-500" min="1">
											<button wire:click="increment" class="border border-blue-500 rounded-lg w-8 h-8">+</button>
										</div>
									</div>

									<div class="divider-border border-t mt-4 border-gray-200"></div>

								    <div id="interviewFields">
									<div class="text-sm mb-2 mt-3">Provide a name for each interview in this process.</div>
										@foreach(range(1, $interviewCount) as $index)
											<div class="mb-3 flex">
												<label for="name{{ $index }}" class="self-center w-64 text-sm text-gray-600">Interview {{ $index }}</label>
												<input type="text" id="name{{ $index }}" placeholder="Enter the name" wire:model.defer="interviewfields.{{ $index - 1 }}" class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md">
											</div>
										@endforeach
									</div>
								</div>
							</div>

						
					</div>
				</div>
				 <div class="w-full border-t mt-4 border-gray-200"></div>
					<div class="flex gap-4 w-full mt-4 ">
						<button @click="closeRecruitModal" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
						<button wire:click="SaveProject" type="button" class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">
							<span class="block"> Save</span>
							<!--<img class="h-5 w-5" src="http://127.0.0.1:8000/images/right-arrow.svg">-->
						</button>
					</div>
			   </div>
		   
		</div>
      @endif

        @endif
    </div>

	
    {{-- Potential Candidates --}}
    <div class="text-sm bg-white text-black font-medium w-full rounded-lg shadow-md border border-gray-200 p-4">
        <div class="text-black">
            <div class="flex flex-1 justify-center items-center">
                <div class="flex flex-1">
                    <h3 class="text-black text-base">Potential Candidates</h3>
                    @if($isPipelineMoverExist)
                    <img src="{{ asset('images/bell_red.svg') }}" class="bell-icon" alt="">
                    @endif
                </div>
                @if ($pipelinepeople->isNotEmpty())
                <div class="flex justify-end">
                    <a href="{{ route('plan.success_people.index', ['plan' => $plan->id]) }}" class="text-black text-sm font-semibold border p-2 rounded-lg">View All</a>
                </div>
                @endif
            </div>
        </div>
        @if ($pipelinepeople->isEmpty())
        <div class="h-80 items-center justify-center mt-20">
            <!-- <h4 class="text-sm text-center text-black font-semibold mb-2"> No Potential Candidates </h4> -->
            <p class="text-center text-gray-500">No Potential Candidates</p>
            <p class="text-sm font-light text-gray-600 text-center">Add candidates from your
                Pipeline, Internal pool or the people dashboard to see your potential candidates.</p>
        </div>
        @else
        <table class="min-w-full divide-y border rounded-xl divide-gray-300 mt-2">
            <thead>
                <tr class="GrayText border rounded-lg">
                    <th scope="col" class="py-3.5 grayBackground px-3 text-left text-sm font-semibold ">Name</th>
                    <th scope="col" class="px-3 py-3.5 grayBackground text-left text-sm font-semibold ">Role</th>
                    <th scope="col" class="px-3 py-3.5 grayBackground text-left text-sm font-semibold ">Company</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
                @foreach ($pipelinepeople as $pipelinePerson)
                <tr>
                    <td class="py-2 pl-4 text-sm font-medium text-gray-900 sm:pl-4 md:pl-4">
                        <h4>
                            {{ $pipelinePerson->first_name }} {{ $pipelinePerson->last_name }}
                        </h4>
                    </td>
                    <td class=" px-3 py-2 text-sm text-gray-500">
                        {{ $pipelinePerson->latest_role }}
                    </td>
                    <td class="py-2 text-sm text-gray-500 md:pl-4 sm:pl-4">
                        {{ $pipelinePerson->company_name }}
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @endif
    </div>
</div>
</div>
@include('livewire.loading')

</div>

<script>

    function confirmRemovePerson(personId, personName, iconUrl) {
        Swal.fire({
            // title: `Remove ${personName}?`,
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Remove ${personName}</h2>
                    <p class="px-5 font-normal">Are you sure you want to remove <b>${personName}</b>
                    from your plan. They will stay in your
                    pipeline but any scores that have been
                    manually entered will be deleted.</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Remove",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                Livewire.dispatch('removeSuccessPerson', {
                    id: personId
                });
            }
        });
    }

    function confirmApprovePerson(personId, personName, iconUrl) {
        Swal.fire({
            // title: "Approve Candidate",
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Approve ${personName}</h2>
                    <p class="px-5 font-normal">Are you sure want to approve <b>${personName}</b>?</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Approve",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                Livewire.dispatch('approveSuccessPerson', {
                    id: personId
                });
            }
        });
    }

    function confirmDeleteNote(noteId, iconUrl) {
        Swal.fire({
            // title: `Remove ${personName}?`,
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Delete Note</h2>
                    <p class="px-5 font-normal">Are you sure you want to delete this note?</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Delete",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                Livewire.dispatch('deleteUserNote', {
                    id: noteId
                });
            }
        });
    }
</script>


<script>
    function clampValue(input) {
        let value = input.value;

        // Allow partial entries with decimals like "0." or "0.1"
        if (value == "" || value == "." || value == "0." || /^0\.\d*$/.test(value) || /^1\.?0*$/.test(value)) {
            return;
        }

        value = parseFloat(value);

        if (isNaN(value) || value < 0 || value > 1) {
            value = 0;
            input.value = value;
        }

        if (value != "0.") {
            input.value = value.toFixed(1);
        }
    }
</script>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        const createChart = (ctxId, labels, data, colors, legendId) => {
            const ctx = document.getElementById(ctxId).getContext('2d');

            const chartData = {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    hoverOffset: 4
                }]
            };

            const chartConfig = {
                type: 'doughnut',
                data: chartData,
                options: {
                    animation: false,
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '80%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(tooltipItem) {
                                    const dataset = tooltipItem.dataset;
                                    const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                    const currentValue = dataset.data[tooltipItem.dataIndex];
                                    const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                    return `${currentValue} (${percentage}%)`;
                                }
                            }
                        }
                    }
                },
                plugins: [{
                    afterUpdate: function(chart) {
                        const ul = document.getElementById(legendId);
                        ul.innerHTML = '';
                        const data = chart.data;
                        const dataset = data.datasets[0];
                        const total = dataset.data.reduce((sum, value) => sum + value, 0);

                        data.labels.forEach((label, i) => {
                            const value = dataset.data[i];
                            const percentage = Math.round((value / total) * 100);
                            const color = dataset.backgroundColor[i];

                            const li = document.createElement('li');
                            li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                            ul.appendChild(li);
                        });
                    }
                }]
            };

            new Chart(ctx, chartConfig);
        };

        createChart('candidateTypeDoughnut', @json($typeLabels), @json($typeData), [
            'rgb(139, 92, 246)', 'rgb(249, 194, 26)', 'rgb(255, 205, 86)'
        ], 'custom-myOrg-legend');

        createChart('excoDoughnut', @json($statusLabels), @json($statusData), [
            'rgb(255, 163, 71)', 'rgb(3, 4, 94)', 'rgb(156, 39, 176)'
        ], 'custom-exco-legend');

        setInterval(() => {
            document.querySelectorAll('.chart-container').forEach(chart => chart.style.opacity = '1');

        }, 1000);
    });
</script>