<div class="chatbot-visible">
    <div class="flex items-center px-4 py-4 justify-between border-b w-full bg-white">
        <h1 class="whitespace-nowrap text-3xl font-medium">Create Plans</h1>
    </div>
    <div class="p-4 w-full">
        <div class="chat-box flex flex-col justify-center text-center">
            <div class ="ai-chat-img flex-shrink-0 mx-auto">
                <div class="flex-none w-20 h-20 text-blue-600 bg-white rounded-full flex items-center justify-center">  
                    <img src="{{ asset('images/ai-2.png') }}" class="w-16 h-14" >
                </div>
            </div>
            <div class ="ai-chat-content mx-auto">  
                <h2 class="alex-title font-semibold text-blue-500">Hi, {{auth()->user()->name}}!</h2>
                <p class="alex-content text-gray-500">Describe the plan you want to make in as much detail as possible and I'll find the people for you!</p>

            </div>
        </div>
        <div id="chat-window" class="py-2 hidden overflow-y-auto p-5 my-4">
            
        </div>
        <div class="">
        <div class="flex items-center border border-gray-200 rounded-xl bg-white p-1">
            <div class="justify-start w-full">
                <textarea class="flex-1 p-2 border rounded-lg focus:outline-none mr-8 border-white w-full" id="message-input" wire:model="userInput" placeholder="Describe the plan you want to make in as much details as possible"></textarea>
            </div>
            <div class="justify-end w-20 items-center h-full flex">
            <button id="send-button" class="" type="submit">
                <img src="{{ asset('images/Vector.png') }}" class="send-p-v">
            </button>
            </div>

        </div>
        </div>

        <div id="plan-container" style="display: none;">
            <h2>Plan Created:</h2>
            <table border="1" id="plan-table">
                <tr>
                    <th>Field</th>
                    <th>Value</th>
                </tr>
                <!-- Plan data will be appended here -->
            </table>
        </div>
        <div class="text-center text-xs text-gray-500 mt-4 pb-4">
            <p>SAM can make mistakes please make sure you check important information</p>
        </div>
    </div>
</div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(document).ready(function() {

        $('textarea').on('input', function() {
            this.style.height = 'auto'; // Reset height to calculate actual height
            const newHeight = Math.min(this.scrollHeight, 100); // Limit height to 100px
            this.style.height = newHeight + 'px';
        });
        
         // Add margin-top: 200px on page load
        //$('.chat-button').css('margin-top', '256px');
        const inputField = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        inputField.addEventListener('keydown', (event) => {
            if (event.key === "Enter") {
                event.preventDefault(); 
                sendButton.click();
            }
        }); 

        $('#send-button').click(function() {
            const req = new Date();
            const reqhours = req.getHours(); // Get hours (0–23)
            const reqminutes = req.getMinutes().toString().padStart(2, '0'); // Get minutes (0–59)

            // Get the day of the week
            const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
            const dayName = days[req.getDay()];

            // Get the month name
            const months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
            const monthName = months[req.getMonth()];

            // Get the date
            const date = req.getDate();

            var message = $('#message-input').val();
            if (message.trim() === '') {
                alert('Please enter a message.');
                return;
            }
            $('.chat-button').css('margin-top', '0'); // Reset margin
            $('#chat-window').removeClass('hidden');
            $('.chat-box').html('<p class="text-gray-500 text-xs">'+ dayName + ', ' + date + ' ' + monthName + '</p>');

            // Append user message
            $('#chat-window').append(`
                <div class="flex items-start space-x-3 mb-4">
                    <div class="flex-none w-10 h-10 bg-white text-blue-600 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-user p-3 rounded-3xl GrayText"></i>
                    </div>
                    <div class="">
                        <p class="font-bold text-gray-700">{{auth()->user()->name}} (you): <span class="text-gray-100 text-xs">${reqhours}:${reqminutes}</span></p>
                        <p class="text-gray-500">${message}</p>
                    </div>
                </div>
            `);
            $('#message-input').val('');

            $('#chat-window').scrollTop($('#chat-window')[0].scrollHeight);

            $('#chat-window').append(`
                <div id="loading-indicator" class="text-gray-500 italic my-4">
                    <p>AI is thinking...</p>
                </div>
            `);

            $('#chat-window').scrollTop($('#chat-window')[0].scrollHeight);

            $.ajax({
                url: '{{ route("my-chatbot.send-message") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    message: message
                },
                success: function(response) {
                console.log("AI response", response);
                    $('#loading-indicator').remove();
                    const res = new Date();
                    const reshours = res.getHours(); // Get hours (0–23)
                    const resminutes = res.getMinutes(); // Get minutes (0–59)

                    if (response.aiResponse) {
                        var aiContent = '';
                        if(Array.isArray(response.aiResponse)){
                            // Log the raw response for debugging
                            console.log("AI response array length:", response.aiResponse.length);
                            
                            // Create a Set to track seen text content
                            var seenTextContent = new Set();
                            
                            // Filter out duplicates before processing
                            var uniqueResponses = [];
                            var seenTexts = new Set();
                            
                            // First pass: collect unique responses
                            response.aiResponse.forEach(function(content) {
                                if (content.type === 'text' && !seenTexts.has(content.text)) {
                                    seenTexts.add(content.text);
                                    uniqueResponses.push(content);
                                }
                            });
                            
                            console.log("Original responses:", response.aiResponse.length, "Unique responses:", uniqueResponses.length);
                            
                            // Second pass: process only unique responses
                            uniqueResponses.forEach(function(content) {
                                if (content.type === 'text') {
                                    var formattedText = content.text.replace(/\n/g, '<br>');
                                    aiContent += `<p class="text-gray-500">${formattedText}</p>`;
                                }
                                
                                // Check if citations exist and format them as a numbered list
                            if (content.citations && content.citations.length > 0) {
                                var citationsList = '<ul class="list-decimal pl-5 text-gray-400">';
                                content.citations.forEach(function(citation, index) {
                                    citationsList += `<li><a href="${citation}" target="_blank" class="text-blue-500 underline">${citation}</a></li>`;
                                });
                                citationsList += '</ul>';
                                
                                // Append the formatted citations to AI response
                                aiContent += `<p class="text-gray-800 font-semibold mt-2">Citations:</p> ${citationsList}`;
                            }

                        });

                        var assistant_img_path = "{{ asset('images/ai-2.png') }}";
                        }
                        else{
                            var formattedText = response.aiResponse.replace(/\n/g, '<br>');
                            aiContent = `<p class="text-gray-500">${formattedText}</p>`;
                        }
                        var aiChatMessage = `
                            <div class="flex items-start space-x-3 mb-4">
                                <div class="flex-none w-10 h-10 bg-white p-2 text-blue-600 rounded-full flex items-center justify-center">
                                    <img src="${assistant_img_path}" class="w-10">
                                </div>
                                <div class="rounded-lg w-full">
                                    <p class="font-bold text-gray-700">SAM: <span class="text-gray-100 text-xs">${reshours}:${resminutes}</span></p>
                                    ${aiContent}
                                </div>
                            </div>
                        `;

                        // Append the formatted message to the chat window
                        $('#chat-window').append(aiChatMessage);


                        $('#chat-window').scrollTop($('#chat-window')[0].scrollHeight);
                    }

                    // If response contains a choice, update the currentRoute
                    //if (response.choice) {
                    //    let routes = {
                    //        "makePlan": '{{ route("ai-chat.create-plan") }}'
                    //    };

                    //    if (routes[response.choice]) {
                    //        currentRoute = routes[response.choice]; // Update route for next requests
                    //    }
                    //}

                },
                error: function(xhr) {
                    $('#loading-indicator').hide();

                    alert('Error: ' + xhr.responseJSON.error);
                }
            });
        });
});
</script>
