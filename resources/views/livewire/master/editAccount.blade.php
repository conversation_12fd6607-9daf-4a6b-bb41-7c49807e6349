<div x-data="{
        open: false,
        uopen: @entangle('uopen'),
        openModal: @entangle('openModal'),
        addAdminAccountPopup:@entangle('addAdminAccountPopup'),
        
        closeModal() {
            this.open = false;
            this.$dispatch('createJobModalClosed');
        },
        closeUpdateModal() {
            this.uopen = false;
            this.$dispatch('updateJobModalClosed');
        },
        updateAccount() {
            @this.call('updateAccount', openModal);
            this.openModal = false;
        }
    }">
    <!-- Modal for updating account -->
    <div x-show="openModal" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50" style="display:none">
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

        <!-- Modal content -->
        <div class="modal-content bg-white shadow-md rounded-2xl p-4 z-50 w-96 relative">
            <h3 class="text-base font-semibold">Update Account</h3>
            <!-- Account Type Select -->
            <div class="mt-4">
                <label for="account_type" class="block text-xs font-medium labelcolor">Account Type</label>
                <select name="account_type" wire:model="updateAccounttype" id="account_type"
                    class="w-full cursor-pointer bg-white p-2 mt-1 border rounded-md text-gray-400 shadow-sm outline-none border-gray-300">
                    <option value="">Select Type</option>
                    <option value="gold" {{ $updateAccounttype === 'gold' ? 'selected' : '' }}
                        class="text-gray-400">Gold</option>
                    <option value="silver" {{ $updateAccounttype === 'silver' ? 'selected' : '' }}>Silver
                    </option>
                    <option value="platinum" {{ $updateAccounttype === 'platinum' ? 'selected' : '' }}>Platinum
                    </option>
                </select>
            </div>
            <!-- User Limit -->
            <div class="mt-2">
                <label for="users_limit" class="block text-xs font-medium labelcolor">User Limit</label>
                <div class="flex rounded-md mt-1 shadow-sm sm:max-w-md border border-gray-300">
                    <input type="number" wire:model="users_limit" name="users_limit" id="users_limit"
                        class="block flex-1 border-0 bg-transparent py-1.5 pl-1 outline-none text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                        placeholder="User Limit" min="1">
                </div>
                @error('users_limit')
                <span class="text-red-500 text-md text-center">{{ $message }}</span>
                @enderror
            </div>
            <div class="mt-2">
                <label for="team" class="block text-xs font-medium labelcolor">Relationship
                    Manager</label>
                <div class="text-xs mt-1 cursor-pointer" wire:ignore x-data="{
                    multiple: true, // Set to true for multiple selections
                    value: @entangle('selectedManager'),
                    options: {{ json_encode($managers) }},
                    init() {
                        this.$nextTick(() => {
                            let choices = new Choices(this.$refs.select, {
                                removeItems: true,
                                removeItemButton: true,
                                duplicateItemsAllowed: false,
                                shouldSort: false, // Ensure the order remains as per the options array
                                placeholder: true,
                                placeholderValue: 'Select relationship managers',
                            });
                
                            let refreshChoices = () => {
                                let selection = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
                
                                choices.clearStore();
                                choices.setChoices(this.options.map(option => ({
                                    value: option.value,
                                    label: option.label,
                                    selected: selection.includes(option.value),
                                })), 'value', 'label', true);
                            };
                
                            refreshChoices();
                
                            this.$refs.select.addEventListener('change', () => {
                                this.value = choices.getValue(true);
                            });
                
                            this.$watch('value', () => refreshChoices());
                            this.$watch('options', () => refreshChoices());
                        });
                    }
                }"
                    class="bg-white text-xs max-w-sm w-full">
                    <select x-ref="select" :multiple="multiple"></select>
                </div>
                @error('selectedManager')
                <span class="text-red-500 text-md text-center">{{ $message }}</span>
                @enderror
            </div>

            <div class="mt-2">
                <label for="team" class="block text-xs font-medium labelcolor">Sector
                    Interest</label>
                <div class="text-xs mt-1 cursor-pointer" wire:ignore x-data="{
                    multiple: true, // Set to true for multiple selections
                    value: @entangle('sector_interest'),
                    options: {{ json_encode($sectors) }},
                    init() {
                        this.$nextTick(() => {
                            let choices = new Choices(this.$refs.select, {
                                removeItems: true,
                                duplicateItemsAllowed: false,
                                removeItemButton: true,
                                shouldSort: false, // Ensure the order remains as per the options array
                                placeholder: true,
                                placeholderValue: 'Select sector interest',
                            });
                
                            let refreshChoices = () => {
                                let selection = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
                
                                choices.clearStore();
                                choices.setChoices(this.options.map(option => ({
                                    value: option.value,
                                    label: option.label,
                                    selected: selection.includes(option.value),
                                })), 'value', 'label', true);
                            };
                
                            refreshChoices();
                
                            this.$refs.select.addEventListener('change', () => {
                                this.value = choices.getValue(true);
                            });
                
                            this.$watch('value', () => refreshChoices());
                            this.$watch('options', () => refreshChoices());
                        });
                    }
                }"
                    class="bg-white text-xs max-w-sm w-full">
                    <select x-ref="select" :multiple="multiple"></select>
                </div>
                @error('selectedManager')
                <span class="text-red-500 text-md text-center">{{ $message }}</span>
                @enderror
            </div>
            <div class="mt-2">
                <label for="team" class="block text-xs font-medium labelcolor">Industry
                    Interest</label>
                <div class="text-xs mt-1 cursor-pointer" wire:ignore x-data="{
                    multiple: true, // Set to true for multiple selections
                    value: @entangle('industry_interest'),
                    options: {{ json_encode($industry) }},
                    init() {
                        this.$nextTick(() => {
                            let choices = new Choices(this.$refs.select, {
                                removeItems: true,
                                removeItemButton: true,
                                duplicateItemsAllowed: false,
                                shouldSort: false, // Ensure the order remains as per the options array
                                placeholder: true,
                                placeholderValue: 'Select industry interest',
                            });
                
                            let refreshChoices = () => {
                                let selection = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
                
                                choices.clearStore();
                                choices.setChoices(this.options.map(option => ({
                                    value: option.value,
                                    label: option.label,
                                    selected: selection.includes(option.value),
                                })), 'value', 'label', true);
                            };
                
                            refreshChoices();
                
                            this.$refs.select.addEventListener('change', () => {
                                this.value = choices.getValue(true);
                            });
                
                            this.$watch('value', () => refreshChoices());
                            this.$watch('options', () => refreshChoices());
                        });
                    }
                }"
                    class="bg-white text-xs max-w-sm w-full">
                    <select x-ref="select" :multiple="multiple"></select>
                </div>
                @error('selectedManager')
                <span class="text-red-500 text-md text-center">{{ $message }}</span>
                @enderror
            </div>

            <div class="mt-2">

                <label for="team" class="block text-xs font-medium labelcolor">Company Of Interest</label>
                <div class="text-xs mt-1 cursor-pointer" wire:ignore x-data="{
                    multiple: true, // Set to true for multiple selections
                    value: @entangle('company_interest'),
                    options: {{ json_encode($companiesOfInterest) }},
                    init() {
                        this.$nextTick(() => {
                            let choices = new Choices(this.$refs.select, {
                                removeItems: true,
                                duplicateItemsAllowed: false,
                                removeItemButton: true,
                                shouldSort: false, // Ensure the order remains as per the options array
                                placeholder: true,
                                placeholderValue: 'Select company of interest',
                            });
                
                            let refreshChoices = () => {
                                let selection = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
                
                                choices.clearStore();
                                choices.setChoices(this.options.map(option => ({
                                    value: option.value,
                                    label: option.label,
                                    selected: selection.includes(option.value),
                                })), 'value', 'label', true);
                            };
                
                            refreshChoices();
                
                            this.$refs.select.addEventListener('change', () => {
                                this.value = choices.getValue(true);
                            });
                
                            this.$watch('value', () => refreshChoices());
                            this.$watch('options', () => refreshChoices());
                        });
                    }
                }"
                    class="bg-white text-xs max-w-sm w-full">
                    <select x-ref="select" :multiple="multiple"></select>
                </div>
                @error('company_interest')
                <span class="text-red-500 text-md text-center">{{ $message }}</span>
                @enderror
            </div>
            <div class="mt-2">
                <button x-on:click="addAdminAccountPopup = true"
                    class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">Add Admin Account</button>
            </div>

            <!-- Modal footer -->
            <div class="mt-3 flex items-center justify-center gap-x-6">
                <button x-on:click="openModal = null"
                    class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                <button wire:click="updateAccount"
                    class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">Update</button>
            </div>
        </div>
    </div>

    <!-- Email Confirmation Popup -->
    <div x-show="addAdminAccountPopup" class="fixed inset-0 flex items-center justify-center z-50 backdrop-blur-sm" style="display:none">
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

        <!-- Popup content -->

        <div class="modal-content bg-white shadow-md rounded-2xl p-4 z-50 w-80 relative">
            <h2 class="text-black-900 text-xl font-semibold">Add Admin Account</h2>
            <div class="w-full border-t my-5 border-gray-200"></div>
            <div class="overflow-y-auto max-h-96">
                @foreach ($adminUsers as $admin)
                <div class="grid grid-cols-2 gap-4 items-center">
                    <div class="careerHistoryEndDate">
                        <label for="editEndDate" class="text-base font-medium labelcolor">{{ $admin->email }}</label>
                    </div>
                    <div class="flex justify-end items-center">
                        <button @click="confirmAdminDelete({{ $admin->id }}, `Delete {{ $admin->name }}`, `Are you sure want to delete the admin user <b>{{ $admin->name }}</b>?`, '{{ asset('images/redTrashIcon.svg') }}', 'deleteAdminUser')" type="button" class="text-red-500">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
                @endforeach


                <div class="mb-3">
                    <input wire:model="addNewAccount" type="text" id="name" placeholder="Enter email"
                        name="name"
                        class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                        required />
                    @error('addNewAccount')
                    <span class="text-red-500 text-md text-center">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="w-full border-t my-5 border-gray-200"></div>

            <div class="mt-3 flex items-center justify-center gap-x-6">
                <button x-on:click="addAdminAccountPopup = false" class="bg-white text-black border p-2 rounded-md w-full">Cancel</button>
                <button wire:click="addNewAdmin()"
                    class="bg-mainBlue text-white p-2 rounded-md w-full">Add New</button>
            </div>
        </div>
    </div>
  
</div>


