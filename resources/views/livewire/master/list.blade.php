<div class="master-platform-component ">
    <div class="mt-5 px-6">
        <div class="relative py-2">
            <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                <div class="w-full border-gray-200"></div>
            </div>
        </div>
        <div class="h-full MasterTableOuter rounded-2xl">
            <div class=" masterTable bg-white border border-gray-200 rounded-2xl">
                <table class="w-full tableEdges">
                    <thead>
                        <tr class="grayBackground">
                            <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                Account
                            </th>
                            <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                Type
                            </th>
                            <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                Active Users
                            </th>
                            <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                Company
                            </th>
                            <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                Relationship Manager
                            </th>
                            <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                Action
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white overflow-y-auto">
                        @if ($Saccounts->isNotEmpty())
                            @foreach ($Saccounts as $Sac)
                                <tr>
                                    <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                        {{ $Sac->Account_name }}
                                    </td>
                                    <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                        {{ $Sac->account_type }}
                                    </td>
                                    <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                        {{ $Sac->active_users }}
                                    </td>
                                    <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                        {{ $Sac->company->name }}
                                    </td>
                                    <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                        @if ($Sac->relationManager())
                                            {{$Sac->relationManager()}}
                                        @else
                                            Not Provided
                                        @endif
                                    </td>
                                    <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                        <div class="dropdown-container relative w-5">
                                            <button tabindex="1" id="dropdownDefaultButton" data-dropdown-toggle="dropdown" class=" text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" type="button">
                                                <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                            </button>
                                            <div id="dropdown" class="dropdown-menu hidden z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                                    <li class="cursor-pointer" x-init="{ uopen: false }">
                                                        <a wire:click.prevent="onSelectAccount({{ $Sac->id }})"  class="p-2">
                                                            <span class="font-semibold text-sm">Update</span>
                                                        </a>
                                                    </li>
                                                    
                                                    <div class="flex" x-data="{ dopen: false }" @click="confirmDelete({{ $Sac->id }}, '{{ asset('images/redTrashIcon.svg') }}')">
                                                        <a class="p-2 w-full">
                                                            <li class="cursor-pointer">
                                                                <span class="font-semibold text-red-700 text-sm">Delete</span>
                                                            </li>
                                                        </a>
                                                    </div>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="6" class="whitespace-wrap px-3 text-sm font-normal text-center text-gray-500 pt-20">
                                    No account(s) Found
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
                <div class="flex absolute bottom-0 w-full p-2 gap-4 paginationContainer">
                    <div class="w-full">
                        {{ $Saccounts->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function confirmDelete(accountId, iconUrl) {
            Swal.fire({
                html: `
                        <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Delete Account</h2>
                        <p class="px-5 font-normal">Are you sure you want to delete this account? Any data that is saved for the account will be permanently erased.</p>
                       `,
                showDenyButton: true,
                showCancelButton: false,
                confirmButtonText: "Delete",
                denyButtonText: `Cancel`,
                reverseButtons: true,
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md',
                    denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md'
                },
                showCloseButton: true
            }).then((result) => {
                if (result.isConfirmed) {
                    Livewire.dispatch('deleteAccount', {
                        id: accountId
                    });
                }
            });
        }
     
    </script>

</div>

<script>
       function confirmAdminDelete(id, title, message, iconUrl, dispatchName) {
        Swal.fire({
            html: `
                        <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Delete Admin User</h2>
                        <p class="px-5 font-normal">${message}</p>
                       `,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Delete",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md',
                denyButton: 'bg-white btnsWidth btnsmargin text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md'
            },
            showCloseButton: true
        }).then((result) => {
            if (result.isConfirmed) {
                Livewire.dispatch(dispatchName, { id: id });
            }
        });
    }
</script>