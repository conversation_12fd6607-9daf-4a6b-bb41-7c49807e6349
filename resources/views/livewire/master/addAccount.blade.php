<div x-data="{
        step: @entangle('step'),
        account_name: @entangle('account_name'), 
        accountType: @entangle('accountType'), 
        companyName: @entangle('companyName'), 
        managerRelation: @entangle('managerRelation'),
        selectedSectorsArr: [],
        selectedIndustriesArr: [],
        errors: {},
        maxWords: 300,
        constraints: {
            account_name: {
                presence: { allowEmpty: false, message: '^Account name is required.' }
            },
            accountType: {
                presence: { allowEmpty: false, message: '^Account type is required.' }
            },
            companyName: {
                presence: { allowEmpty: false, message: '^Company name is required.' }
            },
            managerRelation: {
                presence: { allowEmpty: false, message: '^Relationship manager is required.' }
            }
        },

        
        validateStep1() {
            let validationErrors = validate({ account_name: this.account_name, accountType: this.accountType, companyName: this.companyName, managerRelation: this.managerRelation }, this.constraints);

            this.errors = Object.fromEntries(
                Object.entries(validationErrors || {}).map(([field, messages]) => [field, messages[0]])
            );

            if (!Object.keys(this.errors).length) {
                this.step = 2;
            }
        }
    }">

    <div x-show="nopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50" style="display:none">
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
        <div class="modal-content relative bg-white shadow-md rounded-2xl p-4 z-50 w-96">
            <img x-on:click="nopen = false" class="absolute right top-2 w-auto cursor-pointer"
                src="{{ asset('images/cross.svg') }}" alt="Search Icon">
            <h3 class="text-base font-semibold">Add Account</h3>
            <div class="w-full border-t my-5 border-gray-200"></div>

            <template x-if="step === 1">
                <div wire:key="addAccountStepOne">
                    <div class="mt-2">
                        <label for="team" class="block text-xs font-medium labelcolor">Account
                            Name <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                        <div class="flex rounded-md mt-1 shadow-sm sm:max-w-md border border-gray-300 ">
                            <input type="text" wire:model="account_name" name="account_name" id="account_type"
                                class="block flex-1 border-0 bg-transparent py-1.5 pl-1 outline-none text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                                placeholder="Account Name">
                        </div>
                        <span x-show="errors.account_name" class="text-red-500 text-xs" x-text="errors.account_name"></span>

                    </div>
                    <div class="mt-2">
                        <label for="team" class="block text-xs font-medium labelcolor">Account
                            Type <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                        <select name="" wire:model="accountType" id="account_type"
                            class="w-full cursor-pointer bg-white p-2 mt-1 border rounded-md text-gray-400 shadow-sm outline-none border-gray-300 ">
                            <option value="">Select Type</option>
                            <option value="gold" class="text-gray-400">Gold</option>
                            <option value="silver">Silver</option>
                            <option value="platinum">Platinum</option>
                        </select>
                        <span x-show="errors.accountType" class="text-red-500 text-xs" x-text="errors.accountType"></span>

                    </div>

                    <div class="mt-2" x-data="companyDropdown">
                        <label for="team" class="block text-xs font-medium labelcolor">
                            Company Name <span class="text-red-500" style="font-size: 1rem;">*</span>
                        </label>
                        <p class="GrayText text-xs text-left">
                            Please make sure there are no suggested names from the system
                        </p>

                        <!-- Hidden input to capture selected company names for form submission -->
                        <div class="text-xs mt-1" wire:ignore class="bg-white text-xs max-w-sm w-full relative">
                            <!-- Dropdown -->
                            <select x-ref="select" style="display: none;">
                                <option value="" disabled selected>Select company(s)</option>
                            </select>
                        </div>

                        <span x-show="errors.companyName" class="text-red-500 text-xs" x-text="errors.companyName"></span>
                    </div>





                    <div class="mt-2">
                        <label for="team" class="block text-xs font-medium labelcolor">Relationship Manager <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                        <div class="text-xs mt-1" wire:ignore x-data="{
                            multiple: true,
                            value: @entangle('managerRelation'),
                            options: {{ json_encode($managers) }},
                            init() {
                                this.$nextTick(() => {
                                    let choices = new Choices(this.$refs.select, {
                                        removeItems: true,
                                        removeItemButton: true, // Enables the cross button on selected items
                                        duplicateItemsAllowed: false,
                                        shouldSort: false,
                                        placeholder: true,
                                        placeholderValue: 'Select relationship managers',
                                    });
                        
                                    let refreshChoices = () => {
                                        let selection = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
                        
                                        choices.clearStore();
                                        choices.setChoices(this.options.map(option => ({
                                            value: option.value,
                                            label: option.label,
                                            selected: selection.includes(option.value),
                                        })), 'value', 'label', true);
                                    };
                        
                                    refreshChoices();
                        
                                    this.$refs.select.addEventListener('change', () => {
                                        this.value = choices.getValue(true);
                                    });
                        
                                    this.$watch('value', () => refreshChoices());
                                    this.$watch('options', () => refreshChoices());
                                });
                            }
                        }"
                            class="bg-white text-xs max-w-sm w-full">
                            <select x-ref="select" :multiple="multiple"
                                placeholder="Select relationship manager"></select>
                        </div>
                        <span x-show="errors.managerRelation" class="text-red-500 text-xs" x-text="errors.managerRelation"></span>

                    </div>

                    <div class="mt-2">
                        <label for="userLimit" class="block text-xs font-medium labelcolor">User Limit</label>
                        <div class="flex rounded-md mt-1 shadow-sm sm:max-w-md border border-gray-300">
                            <input type="number" wire:model="userLimit" name="userLimit" id="userLimit"
                                class="block flex-1 border-0 bg-transparent py-1.5 pl-1 outline-none text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                                placeholder="User Limit" min="1">
                        </div>
                        @error('userLimit')
                        <span class="text-red-500 text-md text-center">
                            {{ $message }}
                        </span>
                        @enderror
                    </div>
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button x-on:click="nopen =false"
                            class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                        <button @click="validateStep1()" type="button"
                            class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <span class="block"> Next</span>
                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                        </button>
                    </div>
                </div>
            </template>
            <template x-if="step === 2">
                <div wire:key="addAccountStepTwo">
                    <div class="mt-2">
                        <label for="team" class="block text-xs font-medium labelcolor">Sector
                            Interest</label>
                        <div class="text-xs mt-1 cursor-pointer" wire:ignore x-data="{
                            multiple: true, // Set to true for multiple selections
                            value: @entangle('sectorInterest'),
                            options: {{ json_encode($sectors) }},
                            init() {
                                this.$nextTick(() => {
                                    let choices = new Choices(this.$refs.select, {
                                        removeItems: true,
                                        removeItemButton: true,
                                        duplicateItemsAllowed: false,
                                        shouldSort: false, // Ensure the order remains as per the options array
                                        placeholder: true,
                                        placeholderValue: 'Select sector interest',
                                    });
                        
                                    let refreshChoices = () => {
                                        let selection = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
                        
                                        choices.clearStore();
                                        choices.setChoices(this.options.map(option => ({
                                            value: option.value,
                                            label: option.label,
                                            selected: selection.includes(option.value),
                                        })), 'value', 'label', true);
                                    };
                        
                                    refreshChoices();
                        
                                    this.$refs.select.addEventListener('change', () => {
                                        this.value = choices.getValue(true);
                                    });
                        
                                    this.$watch('value', () => refreshChoices());
                                    this.$watch('options', () => refreshChoices());
                                });
                            }
                        }"
                            class="bg-white text-xs max-w-sm w-full">
                            <select x-ref="select" :multiple="multiple"></select>
                        </div>
                        @error('sectorInterest')
                        <span class="text-red-500 text-md text-center">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mt-2">
                        <label for="team" class="block text-xs font-medium labelcolor">Industry
                            Interest</label>
                        <div class="text-xs mt-1 cursor-pointer" wire:ignore x-data="{
                            multiple: true, // Set to true for multiple selections
                            value: @entangle('industryInterest'),
                            options: {{ json_encode($industry) }},
                            init() {
                                this.$nextTick(() => {
                                    let choices = new Choices(this.$refs.select, {
                                        removeItems: true,
                                        removeItemButton: true,
                                        duplicateItemsAllowed: false,
                                        shouldSort: false, // Ensure the order remains as per the options array
                                        placeholder: true,
                                        placeholderValue: 'Select industry interest',
                                    });
                        
                                    let refreshChoices = () => {
                                        let selection = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
                        
                                        choices.clearStore();
                                        choices.setChoices(this.options.map(option => ({
                                            value: option.value,
                                            label: option.label,
                                            selected: selection.includes(option.value),
                                        })), 'value', 'label', true);
                                    };
                        
                                    refreshChoices();
                        
                                    this.$refs.select.addEventListener('change', () => {
                                        this.value = choices.getValue(true);
                                    });
                        
                                    this.$watch('value', () => refreshChoices());
                                    this.$watch('options', () => refreshChoices());
                                });
                            }
                        }"
                            class="bg-white text-xs max-w-sm w-full">
                            <select x-ref="select" :multiple="multiple"></select>
                        </div>
                        @error('industryInterest')
                        <span class="text-red-500 text-md text-center">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mt-2">
                        <label for="team" class="block text-xs font-medium labelcolor">Company Of
                            Interest</label>
                        <div class="text-xs mt-1 cursor-pointer" wire:ignore x-data="{
                            multiple: true, // Set to true for multiple selections
                            value: @entangle('companyInterest'),
                            options: {{ json_encode($companiesOfInterest) }},
                            init() {
                                this.$nextTick(() => {
                                    let choices = new Choices(this.$refs.select, {
                                        removeItems: true,
                                        duplicateItemsAllowed: false,
                                        removeItemButton: true,
                                        shouldSort: false, // Ensure the order remains as per the options array
                                        placeholder: true,
                                        placeholderValue: 'Select company of interest',
                                    });
                        
                                    let refreshChoices = () => {
                                        let selection = this.value ? (Array.isArray(this.value) ? this.value : [this.value]) : [];
                        
                                        choices.clearStore();
                                        choices.setChoices(this.options.map(option => ({
                                            value: option.value,
                                            label: option.label,
                                            selected: selection.includes(option.value),
                                        })), 'value', 'label', true);
                                    };
                        
                                    refreshChoices();
                        
                                    this.$refs.select.addEventListener('change', () => {
                                        this.value = choices.getValue(true);
                                    });
                        
                                    this.$watch('value', () => refreshChoices());
                                    this.$watch('options', () => refreshChoices());
                                });
                            }
                        }"
                            class="bg-white text-xs max-w-sm w-full">
                            <select x-ref="select" :multiple="multiple"></select>
                        </div>
                        @error('companyInterest')
                        <span class="text-red-500 text-md text-center">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mt-2">
                        <label for="email" class="block text-xs font-medium labelcolor">Email<span class="text-red-500" style="font-size: 1rem;">*</span></label>
                        <div class="flex rounded-md shadow-sm  sm:max-w-md mt-1  border border-gray-300">
                            <input type="email" wire:model="email" id="email"
                                class="block flex-1 border-0 bg-transparent py-1.5 pl-1 outline-none text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                                placeholder="Your Email" required>
                        </div>
                        @error('email')
                        <span class="text-red-500 text-md text-center">
                            {{ $message }}
                        </span>
                        @enderror
                    </div>
                    <div class="flex gap-2 w-full mt-4 ">



                        <button @click="step = 1" type="button"
                            class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                            <span class="block font-medium">Back</span>
                        </button>

                        <button wire:click="addAccount"
                            class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">Send</button>

                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('companyDropdown', function() { // Use `function` keyword for Alpine context
            return {
                allOptions: @json($companies),
                filteredOptions: [],
                paginatedOptions: [],
                itemsPerPage: 100,
                currentPage: 1,
                choices: null,
                selectedCompany: @entangle('companyName'),

                init() {
                    // Filter out any options with empty company names
                    this.filteredOptions = this.allOptions.filter(company => company.label.trim() !== '');
                    this.paginateOptions();

                    // Initialize Choices.js
                    this.choices = new Choices(this.$refs.select, {
                        allowHTML: true,
                        searchEnabled: true,
                        shouldSort: false,
                        placeholder: true,
                        placeholderValue: 'Select company(s)',
                    });

                    this.setChoices();

                    // Scroll listener for loading more options
                    this.setScrollListener();

                    // Update hidden input value on selection change
                    this.$refs.select.addEventListener('change', () => {
                        this.selectedCompany = this.choices.getValue(true);
                    });

                    // Search input listener for filtering options
                    this.setSearchListener();
                },

                paginateOptions() {
                    const start = (this.currentPage - 1) * this.itemsPerPage;
                    const end = start + this.itemsPerPage;
                    this.paginatedOptions = this.filteredOptions.slice(0, end);
                },

                loadMoreOptions() {
                    if (this.paginatedOptions.length < this.filteredOptions.length) {
                        this.currentPage++;
                        this.paginateOptions();

                        // Save current scroll position
                        const dropdown = document.querySelector('.choices__list--dropdown .choices__list');
                        const currentScrollPosition = dropdown.scrollTop;

                        // Append new choices
                        this.setChoices(true);

                        // Restore scroll position
                        dropdown.scrollTop = currentScrollPosition;
                    }
                },

                setChoices(append = false) {
                    const selectedValues = this.choices.getValue(true) || [];

                    // Append new choices only if requested
                    this.choices.setChoices(
                        this.paginatedOptions.map(({
                            value,
                            label
                        }) => ({
                            value,
                            label,
                            selected: Array.isArray(selectedValues) && selectedValues.includes(value)
                        })),
                        'value',
                        'label',
                        append
                    );
                },

                handleSearchInput(event) {
                    const query = event.target.value.toLowerCase();
                    this.filteredOptions = this.allOptions
                        .filter(company => company.label.trim() !== '') // Skip empty names
                        .filter(({
                            label
                        }) => label.toLowerCase().includes(query));
                    this.currentPage = 1;
                    this.paginateOptions();
                    this.setChoices();
                },

                setScrollListener() {
                    const dropdown = document.querySelector('.choices__list--dropdown .choices__list');
                    dropdown.addEventListener('scroll', () => {
                        if (dropdown.scrollTop + dropdown.clientHeight >= dropdown.scrollHeight) {
                            this.loadMoreOptions();
                        }
                    });
                },

                setSearchListener() {
                    const searchInput = document.querySelector('.choices__input--cloned');
                    searchInput.addEventListener('input', this.handleSearchInput.bind(this));
                },
            };
        });
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var userLimitInput = document.getElementById('userLimit');

        userLimitInput.addEventListener('input', function() {
            var value = parseFloat(userLimitInput.value);

            if (value < 1) {
                userLimitInput.value = 1;
            }
        });
    });
</script>