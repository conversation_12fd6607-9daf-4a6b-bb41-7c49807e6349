<div class="customHeight overflow-y-scroll">

    @include('livewire.flashMessage')

    <div class="flex justify-between items-center px-5 py-4 border-b shadow-md bg-white">
        <div class="flex gap-2">
            <h1 class="whitespace-nowrap px-4 text-black text-3xl font-semibold">Accounts</h1>
        </div>
        <div class="flex gap-2">

            <div class="flex" x-data="{
                nopen: @entangle('nopen'), step: 1,
                uopen: @entangle('uopen'),
                openModal: null,
                step: 1
            }">
                <!-- Trigger button inside the modal -->
                <div class="flex items-center justify-end">
                    <div>
                        <button x-on:click="nopen = true; step = 1"
                            class="bg-cyan-500 flex items-center w-32 space-x-1 border border-gray-300 rounded-lg py-2 hover:bg-cyan-600 font-medium  text-white text-md justify-center">
                            <img class="h-4 font-bold w-auto img px-1"
                                src="{{ asset('/images/plus-white-without-circle.svg') }}">
                            <h1 class="text-md font-semibold">Add Account</h1>
                        </button>
                    </div>
                </div>

                @include('livewire.master.addAccount')
                @if($accountIdToUpdate)
                    @include('livewire.master.editAccount')
                @endif

            </div>
        </div>
    </div>
    @include('livewire.master.list')

    @include('livewire.loading')
</div>
