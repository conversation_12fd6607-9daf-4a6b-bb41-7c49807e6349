@push('style')
    <link rel="stylesheet" href="{{ asset('css/organisationChart.css') }}">
@endpush
<ul>
    <li>
        <a href="#">
            <div wire:key="randomKey" class="w-56 HRInfo rounded-lg shadow-xl bg-white relative marginClass">
                <div class="flex gap-10 items-center borderTop">
                    <!-- Display parent information -->
                    <div class="flex gap-2 items-center ml-3">
                        <img class="block cursor-pointer" src="{{ asset('images/redCrossIcon.svg') }}" alt="Delete Person">
                        <h2 class="text-sm overflow-hidden whitespace-nowrap w-32 text-ellipsis"
                            title="{{ $person['latest_role'] }}<">
                            {{ $person['latest_role'] }}
                        </h2>
                    </div>
                    <!-- Dropdown menu for parent actions -->
                    <div class="dropdown-container">
                        <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                            type="button">
                            <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                        </button>
                        <!-- Dropdown menu options for parent -->
                        <div id="dropdown2"
                            class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                            <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                <div class="cursor-pointer li">
                                    <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                        <span class="font-semibold text-sm">View Profile</span>
                                    </div>
                                </div>
                                <div class="cursor-pointer li">
                                    <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                        <span class="font-semibold textRed text-sm">Delete</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Parent node border -->
                <div class="border"></div>
                <!-- Display parent name and ID -->
                <div class="p-2 ml-3 flex flex-col text-left">
                    <span class="text-sm overflow-hidden whitespace-nowrap w-48 text-ellipsis" title="Leslie Alexander">
                    {{ $person['first_name'] }} {{ $person['last_name'] }}
                    </span>
                    <span class="text-xs text-gray-500">Company Name: {{ $person['company_name'] }}</span>
                </div>
                <!-- Drag icon for parent -->
                <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">
            </div>
        </a>
        
    </li>
</ul>