@push('style')
    <link rel="stylesheet" href="{{ asset('css/planOrgChart.css') }}">
@endpush
<div>
    @include('livewire.flashMessage')
    
    @if (empty($plan->tagged_individual))
        <script>
            alert("Tagged individual is missing, please add to the plan to view the org chart.");
            window.location.href = "{{ route('plan.show', ['plan' => $plan->id]) }}";
        </script>
    @endif

    <div x-data="{
                    dopen: false,
                    ropen: false,
                    vopen: false,
                    uopen: false,
                    rvopen: false,
                    tab: 'summary',
                    nopen: false,
                    enopen: false,
                    esummary: false,
                    closeViewDetailModal() {
                        this.vopen = false;
                        this.esummary = false;
                        this.rvopen = false;
                        this.$dispatch('viewDetailModalClosed');
                    },
					closeRecruitModal() {
                        this.ropen = false;
                        this.esummary = false;
                        this.$dispatch('recruitModalClosed');
                    }
                }">
        <div class="flex flexRow flex-col lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
            <div class="flex gap-4 items-center">
                <a href="{{ route('plan.show', ['plan' => $plan->id]) }}" class="flex gap-2 mainBlue items-center">
                    <img class="" src="{{ asset('images/ArrowLeftBlue.svg') }}">
                    <span>Back to Plan</span>
                </a>
                <h1 class="whitespace-nowrap font-medium">{{$plan->name}} Org Chart</h1>
            </div>
            <div class="ml-auto">
                <button id="download"  class="flex items-center border border-gray-300 rounded-lg px-2 py-2 gap-2 hover:bg-gray-100 transition ml-auto">
                    <img src="{{ asset('images/DownloadSimple.png') }}" alt="Download" class="w-5 h-5" />
                    <span class="text-gray-700 font-medium">Download</span>
                </button>
               
            </div>
        </div>
        <div 
        class="my-org relative customHeight bg-gray-50"
        x-data="{ zoomLevel: 100 }" >            
             
            <!-- Zoom Controls (unaffected by scaling) -->
            <div class="flex halfwidth absolute zoom-div z-10">
                <img
                    class="search-icon p-1 rounded-lg cursor-pointer w-8 h-8 border-2 bg-white"
                    src="/images/Plus.svg"
                    alt="Zoom In"
                    @click="if(zoomLevel < 100) zoomLevel += 10"
                >
                <div
                    class="text-sm text-gray-700 flex w-14 h-8 justify-center items-center p-1 rounded-lg border-2 bg-white"
                    x-text="zoomLevel + '%'"
                ></div>
                <img
                    class="search-icon p-1 rounded-lg cursor-pointer border-2 w-8 h-8 bg-white"
                    src="/images/Minus.svg"
                    alt="Zoom Out"
                    @click="if(zoomLevel > 30) zoomLevel -= 10"
                >
            </div>
            <div class="plan-org-chart relative customHeight">
                <div 
                    
                    class="tree customHeight flex justify-center items-center"
                    :style="`transform: scale(${zoomLevel / 100}); transform-origin: top center;`"
                    id="orgChartWrapper"
                >
                    <ul>
                        <li>
                            <a href="#">
                                <div wire:key="user-{{$plan->tagged_individual}}" class="w-56 HRInfo rounded-lg bg-white relative marginClass">
                                    <div class="flex gap-10 items-center borderTop pb-4">
                                        <!-- Display parent information -->
                                        <div class="flex gap-2 item-center mx-3 text-left">
                                            <h2 class="font-semibold text-sm whitespace-nowrap w-32"
                                                title="{{$plan->latest_role}}">
                                                {!! wordwrap(\Illuminate\Support\Str::limit($plan->latest_role, 40, '.'), 25, '<br>') !!}
                                            </h2>
                                        </div>
                                        <!-- Dropdown menu for parent actions -->
                                        <div class="dropdown-container">
                                            <!-- <button tabindex="1" id="dropdownDefaultButton1" data-dropdown-toggle="dropdown1"
                                                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                type="button">
                                                <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                            </button> -->
                                            <!-- Dropdown menu options for parent -->
                                            <div id="dropdown1" class="dropdown-menu z-10 absolute bg-white divide-y divide-gray-100 rounded-lg w-44 dark:bg-gray-700">
                                                <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                                    <div class="cursor-pointer li" @click="rvopen = true" wire:click.prevent="viewSelectedPeople({{ null }}, {{ $plan->tagged_individual }}, 'internal')">
                                                        <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            <span class="font-semibold text-sm">View</span>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="cursor-pointer li" @click="confirmRemovePerson({{$plan->tagged_individual}}, '{{ htmlentities($plan->forename) }} {{ htmlentities($plan->surname) }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                        <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            <span class="font-semibold textRed text-sm">Delete</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Parent node border -->
                                    <div class="border"></div>

                                    <!-- Display parent name and ID -->
                                    <div class="p-2 flex flex-col text-left mb-3">
                                        <span class="text-sm whitespace-nowrap w-48" title="{{$plan->forename}} {{$plan->surname}}">
                                            {{$plan->forename}} {{$plan->surname}}
                                        </span>
                                        <span class="text-xs text-gray-500">Company: {!! wordwrap($plan->company_name, 30, '<br>') !!}</span>
                                    </div>

                                    <!-- Drag icon for parent -->
                                    <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">
                                </div>
                            </a>
                            <!-- Succession peoples of the plan -->
                            @if (!empty($successPeople))
                                <ul>
                                    @php
                                        $external = $successPeople->filter(fn($p) => $p['type'] === 'External')->values();
                                        $internal = $successPeople->filter(fn($p) => $p['type'] === 'Internal')->values();
                                    @endphp
                                    @if(!$external->isEmpty())
                                    <li>
                                        <a href="#">
                                            <div wire:key="user-{{ isset($external[0]) ? $external[0]->id : '' }}" class="w-56 HRInfo rounded-lg bg-white relative marginClass">
                                                <div class="flex gap-10 items-center borderTop pb-4">
                                                    <!-- Display parent information -->
                                                    <div class="flex gap-2 items-center mx-3 text-left">
                                                        <h2 class="font-bold text-sm whitespace-nowrap w-32"
                                                            title="{{$external[0]->latest_role}}">
                                                            {!! wordwrap(\Illuminate\Support\Str::limit($external[0]->latest_role, 40, '.'), 25, '<br>') !!}
                                                        </h2>
                                                    </div>
                                                    <!-- Dropdown menu for parent actions -->
                                                    <div class="dropdown-container">
                                                        <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown2"
                                                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                            type="button">
                                                            <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                                        </button>
                                                        <!-- Dropdown menu options for parent -->
                                                        <div id="dropdown2" class="dropdown-menu z-10 absolute bg-white divide-y divide-gray-100 rounded-lg w-44 dark:bg-gray-700">
                                                            <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                                                <div class="cursor-pointer li" @click="vopen = true" wire:click.prevent="viewSelectedPeople({{ $external[0]->id }}, {{ $external[0]->people_id }}, '{{ $external[0]->type }}')">
                                                                    <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                        <span class="font-semibold text-sm">View</span>
                                                                    </div>
                                                                </div>

                                                                @if($external[0]->type != 'Internal')
                                                                    <div class="cursor-pointer li" 
                                                                        @if(!$recruitmentProjectId)
                                                                            @click="ropen=true; $wire.selectedpersonId='{{ $external[0]->people_id }}'"
                                                                        @endif
                                                                        wire:click.prevent="{{ $recruitmentProjectId ? 'savePlanPeopleId(' . $external[0]->people_id . ', ' . $recruitmentProjectId . ')' : 'viewSelectedPeople(' . $external[0]->id . ', ' . $external[0]->people_id . ', \'' . $external[0]->type . '\')' }}">
                                                                        <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                            <span class="font-semibold text-sm">Recruit</span>
                                                                        </div>
                                                                    </div>
                                                                @endif

                                                                @if ($external[0]->type !== 'Internal' && $external[0]->linkedinURL && $external[0]->linkedinURL !== 'NA')
                                                                    <div class="cursor-pointer li">
                                                                        <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" onclick="window.open('{{ $external[0]->linkedinURL }}', '_blank')">                                                                    
                                                                                <span class="font-semibold text-sm">View LinkedIn</span>                                                                    
                                                                        </div>
                                                                    </div>
                                                                @endif

                                                                <!-- Approve Functioality -->
                                                                @if ($user->id === $plan->user_id && $external[0]->status === 'Proposed')
                                                                    <div class="cursor-pointer li" @click="confirmApprovePerson({{ $external[0]->id }}, '{{ $external[0]->first_name }} {{ $external[0]->last_name }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                                        <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                            <span class="font-semibold text-sm">Approve</span>
                                                                        </div>
                                                                    </div>
                                                                @endif

                                                                <div class="cursor-pointer li"  @click="confirmRemovePerson({{ $external[0]->id }}, '{{ htmlentities($external[0]->first_name) }} {{ htmlentities($external[0]->last_name) }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                                    <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                        <span class="font-semibold textRed text-sm">Delete</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Parent node border -->
                                                <div class="border"></div>

                                                <!-- Display parent name and ID -->
                                                <div class="p-2 flex flex-col text-left mb-3">
                                                    <span class="text-sm whitespace-nowrap w-48" title="{{$external[0]->first_name}} {{$external[0]->last_name}}">
                                                        {{$external[0]->first_name}} {{$external[0]->last_name}}
                                                    </span>
                                                    <span class="text-xs text-gray-500">Company: {!! wordwrap($external[0]->company_name, 30, '<br>') !!}</span>
                                                </div>

                                                <!-- Drag icon for parent -->
                                                <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">

                                            </div>
                                        </a>
                                        
                                        @php
                                            $remaining = $external->slice(1);
                                    
                                            if (!$remaining->isEmpty()) {
                                                echo renderSuccessPeopleTree($remaining, $recruitmentProjectId, $plan->user_id);
                                            }                            
                                        @endphp
                                    </li>
                                    @endif
                                    @if(!$internal->isEmpty()) 
                                    <li>                            
                                        <a href="#">
                                            <div wire:key="user-{{$internal[0]->id}}" class="w-56 HRInfo-red rounded-lg bg-white relative marginClass">

                                                <div class="flex gap-10 items-center borderTop-red pb-4">
                                                    <!-- Display parent information -->
                                                    <div class="flex gap-2 items-center mx-3 text-left">
                                                        <h2 class="font-semibold text-sm whitespace-nowrap w-32"
                                                            title="{{$internal[0]->latest_role}}">
                                                            {!! wordwrap(\Illuminate\Support\Str::limit($internal[0]->latest_role, 40, '.'), 25, '<br>') !!}
                                                        </h2>
                                                    </div>
                                                    <!-- Dropdown menu for parent actions -->
                                                    <div class="dropdown-container">
                                                        <button tabindex="1" id="dropdownDefaultButton3" data-dropdown-toggle="dropdown3"
                                                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                            type="button">
                                                            <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                                        </button>
                                                        <!-- Dropdown menu options for parent -->
                                                        <div id="dropdown3" class="dropdown-menu z-10 absolute bg-white divide-y divide-gray-100 rounded-lg w-44 dark:bg-gray-700">
                                                            <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                                                <div class="cursor-pointer li" @click="vopen = true" wire:click.prevent="viewSelectedPeople({{ $internal[0]->id }}, {{ $internal[0]->people_id }}, '{{ $internal[0]->type }}')">
                                                                    <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                        <span class="font-semibold text-sm">View</span>
                                                                    </div>
                                                                </div>

                                                                @if ($internal[0]->type !== 'Internal' && $internal[0]->linkedinURL && $internal[0]->linkedinURL !== 'NA')
                                                                    <div class="cursor-pointer li">
                                                                        <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" onclick="window.open('{{ $internal[0]->linkedinURL }}', '_blank')">                                                                    
                                                                                <span class="font-semibold text-sm">View LinkedIn</span>                                                                    
                                                                        </div>
                                                                    </div>
                                                                @endif

                                                                <!-- Approve Functionality -->
                                                                @if ($user->id === $plan->user_id && $internal[0]->status === 'Proposed')
                                                                    <div class="cursor-pointer li" @click="confirmApprovePerson({{ $internal[0]->id }}, '{{ $internal[0]->first_name }} {{ $internal[0]->last_name }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                                        <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                            <span class="font-semibold text-sm">Approve</span>
                                                                        </div>
                                                                    </div>
                                                                @endif

                                                                <div class="cursor-pointer li" @click="confirmRemovePerson({{ $internal[0]->id }}, '{{ htmlentities($internal[0]->first_name) }} {{ htmlentities($internal[0]->last_name) }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                                    <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                        <span class="font-semibold textRed text-sm">Delete</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Parent node border -->
                                                <div class="border"></div>

                                                <!-- Display parent name and ID -->
                                                <div class="p-2 flex flex-col text-left mb-3">
                                                    <span class="text-sm whitespace-nowrap w-48" title="{{$internal[0]->first_name}} {{$internal[0]->last_name}}">
                                                        {{$internal[0]->first_name}} {{$internal[0]->last_name}}
                                                    </span>
                                                    <span class="text-xs text-gray-500">Company: {!! wordwrap($internal[0]->company_name, 20, '<br>') !!}</span>
                                                </div>

                                                <!-- Drag icon for parent -->
                                                <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">

                                            </div>
                                        </a>
                                        @php
                                        $internalRemaining = $internal->slice(1);
                                        if (!$internalRemaining->isEmpty()) {
                                            echo renderSuccessPeopleTree($internalRemaining, $recruitmentProjectId, $plan->user_id);
                                        }                            
                                        @endphp
                                    </li>
                                    @endif
                                </ul>
                            @endif
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- vopen Popup Modal -->
    @if (!empty($filteredPeople) && $filteredPeople->isNotEmpty())
        @if($savedPeople == $filteredPeople[0]->id)
            <div x-show="vopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" style="display:none">
                <!-- Modal background with a higher z-index -->
                <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                <!-- Modal content with a lower z-index -->
                <div class="pipeline-modal-content bg-white rounded-lg border border-gray-300 p-4 z-50" x-show="vopen" x-transition>
                    <!-- Modal content -->
                    <div class="flex flex-col h-full">
                        <div class="flex justify-between border-b px-2 py-2">
                            <h3 class="text-base font-bold flex gap-2">
                                @if($filteredPeople[0]->mover == 'Mover' && $filteredPeople[0]->current_company)
                                {{ $filteredPeople[0]->company_name }}
                                <img src="{{ asset('images/right-arrow-black.svg') }}" alt="">
                                <span class="text-red-500">{{ $filteredPeople[0]->current_company }}</span>
                                @else
                                {{ $filteredPeople[0]->company_name }}
                                @endif
                            </h3>
                            <div class="flex">
                                @if($filteredPeople[0]->mover == 'Mover')
                                <img wire:click="refreshMover({{ $filteredPeople[0] }})" title="Refresh" class="h-4 w-4 mr-2 cursor-pointer" src="{{ asset('images/sync-alt-solid.svg') }}" alt="">
                                @endif
                                <img @click="closeViewDetailModal" class="h-5 w-5 cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="">
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-x-2 pt-3 flex-1">
                            <div class="flex flex-1 flex-col justify-between">
                                <div class="flex px-2 py-2 gap-2 justify-between">

                                    <div class="flex flex-col gap-y-2">
                                        <h3 class="text-base font-bold">
                                            {{ $filteredPeople[0]->first_name }}
                                            {{ $filteredPeople[0]->last_name }}
                                        </h3>
                                        <h3 class="text-base text-gray-700 d-ruby gap-2">
                                            @if($filteredPeople[0]->mover == 'Mover' && $filteredPeople[0]->current_role)

                                            <span>{{ $filteredPeople[0]->latest_role }} </span>
                                            &nbsp;<img src="{{ asset('images/right-arrow-black.svg') }}" alt=""> &nbsp;
                                            <span class="text-red-500">{{ $filteredPeople[0]->current_role }}</span>
                                            @else
                                            <span>{{ $filteredPeople[0]->latest_role }} </span>
                                            @endif
                                        </h3>
                                    </div>

                                    <div class="flex gap-x-2 items-center justify-center">
                                        @if(strtolower($filteredPeople[0]->status) != "approved")
                                        <div wire:click.prevent="approveSuccessPeople({{$filteredPeople[0]->id}})" class="flex cursor-pointer  px-2 h-8 py-5 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M18 32.4L9.6 24l-2.8 2.8L18 38 42 14l-2.8-2.8L18 32.4z" fill="#4CAF50" />
                                            </svg>
                                        </div>
                                        @endif
                                        <div class="flex cursor-pointer  scoreBtn px-2 h-8 py-5 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                            <img class="h-4 w-4 mr-2" src="{{ asset('images/editicon.svg') }}" alt="">
                                            <h2 @click="uopen = true" class=" font-semibold">Update Scores</h2>
                                        </div>
                                        @if ($filteredPeople[0]->linkedinURL && $filteredPeople[0]->linkedinURL != 'NA')
                                        <a id="linkedin" class="flex items-center justify-center hover:scale-105" href="{{ $filteredPeople[0]->linkedinURL }}" target="_blank">
                                            <img src="{{ asset('images/LinkedIn.svg') }}">
                                        </a>
                                        @endif
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-2 grid-rows-4 flex-grow">
                                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                        <h3 class="text-sm font-bold chart-heading">Potential Sex</h3>
                                        <h3 class="text-sm text-gray-700">{{ $filteredPeople[0]->gender }}
                                        </h3>
                                    </div>
                                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                        <h3 class="text-sm font-bold chart-heading">Function</h3>
                                        <h3 class="text-sm text-gray-700">
                                            {{ $filteredPeople[0]->function ? $filteredPeople[0]->function : 'Not Applicable' }}
                                        </h3>
                                    </div>
                                    <!-- <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                        <h3 class="text-sm font-bold chart-heading">Division</h3>
                                                        <h3 class="text-sm text-gray-700">{{ $filteredPeople[0]->division ? $filteredPeople[0]->division : 'Not Applicable' }}</h3>
                                                    </div>
                                                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                        <h3 class="text-sm font-bold chart-heading">Seniority</h3>
                                                        <h3 class="text-sm text-gray-700">{{ $filteredPeople[0]->seniority ? $filteredPeople[0]->seniority : 'Not Applicable' }}</h3>
                                                    </div> -->
                                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                        <h3 class="text-sm font-bold chart-heading">Tenure in Role</h3>
                                        <h3 class="text-sm text-gray-700">{{ $filteredPeople[0]->tenure }}
                                        </h3>
                                    </div>
                                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                        <h3 class="text-sm font-bold chart-heading">Readiness</h3>
                                        @if ($filteredPeople[0]->readiness)
                                        @if (strtolower($filteredPeople[0]->readiness) == 'ready')
                                        <span class="px-2 py-1 text-sm rounded-lg font-medium redlinessReaddy">
                                            Ready
                                        </span>
                                        @elseif(strtolower($filteredPeople[0]->readiness) == 'not ready')
                                        <span class="px-2 py-1 text-sm rounded-lg font-medium RedBG text-red-500">
                                            Not Ready
                                        </span>
                                        @endif
                                        @else
                                        <span class="px-2 py-1 text-sm rounded-lg font-medium">
                                            N/A
                                        </span>
                                        @endif
                                    </div>
                                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                        <h3 class="text-sm font-bold chart-heading">Registrations</h3>
                                        <h3 class="text-sm text-gray-700">
                                            {{ $filteredPeople[0]->other_tags ? $filteredPeople[0]->other_tags : 'Not Applicable' }}
                                        </h3>
                                    </div>
                                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                        <h3 class="text-sm font-bold chart-heading">Score</h3>
                                        <h3 class="text-sm text-gray-700">
                                            {{ $filteredPeople[0]->total_score }}
                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <div x-data="{ tab: 'summary' }" class="flex flex-col pt-3 gap-y-2">
                                <div class="flex border-b-2">
                                    <button @click="tab = 'summary'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                                'summary',
                                                            'chart-heading font-semibold': tab != 'summary'
                                                        }">
                                        Summary
                                    </button>
                                    <button @click="tab = 'career_history'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                                'career_history',
                                                            'chart-heading font-semibold': tab !=
                                                                'career_history'
                                                        }">
                                        Career History
                                    </button>
                                    <button @click="tab = 'skills'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                                'skills',
                                                            'chart-heading font-semibold': tab != 'skills'
                                                        }">
                                        Skills
                                    </button>
                                    <button @click="tab = 'notes'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                                'notes',
                                                            'chart-heading font-semibold': tab != 'notes'
                                                        }">
                                        Notes
                                    </button>
                                </div>
                                <div x-show="tab == 'summary'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                    <div class="flex justify-between">
                                        <h4 class="text-lg text-gray-700 font-semibold chart-heading mr-2">
                                            Summary</h4>
                                        <button x-show="!esummary" @click="esummary = true" type="button" class="flex justify-center items-center border py-2 px-4 rounded-md text-sm bg-mainBlue text-white">
                                            @if ($filteredPeople[0]->summary)
                                            <img class="h-4 w-4" src="{{ asset('images/edit_pencil_white.svg') }}">
                                            <span class="block text-white pl-1">&nbsp;Edit</span>
                                            @else
                                            <img class="h-4 w-4" src="{{ asset('images/plus-white-without-circle.svg') }}">
                                            <span class="block text-white pl-1">&nbsp;Add</span>
                                            @endif
                                        </button>

                                        <button x-show="esummary" @click="esummary = false" wire:click.prevent="updateSummary({{ $filteredPeople[0]->id }})" type="button" class="flex justify-center items-center border py-2 px-4 rounded-md text-sm bg-mainBlue text-white">
                                            <span class="block text-white pl-1">Save</span>
                                        </button>
                                    </div>

                                    @if ($filteredPeople[0]->summary)
                                    <p x-show="!esummary" class="text-sm">
                                        {{ $filteredPeople[0]->summary }}
                                    </p>
                                    @else
                                    <p x-show="!esummary" class="text-gray-500 py-5">No Summary Found
                                    </p>
                                    @endif

                                    <textarea x-show="esummary" wire:model="successPersonSummary" rows="14" class="bg-gray-50 p-2 text-sm font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500" placeholder="Enter your summary here"></textarea>
                                </div>

                                <div x-show="tab == 'career_history'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                    <h4 class="text-lg text-gray-700 font-semibold chart-heading">Career
                                        History</h4>
                                    @if ($peoplescareer && $peoplescareer->isNotEmpty())
                                    <div class="flex flex-col items-start w-full">
                                        @foreach ($peoplescareer as $careerHistory)
                                        <div class="flex h-max items-start justify-center mb-1">
                                            <div class="flex flex-col mt-1 items-center justify-center h-full w-2">
                                                <div class="rounded-full  blueBalls bg-mainBlue">
                                                </div>
                                                <div class="flex-grow border-l border-mainBlue">
                                                </div>
                                            </div>
                                            <div class="flex flex-col  items-start justify-start pl-4">
                                                <h4 class="text-sm font-semibold text-gray-900">
                                                    {{ $careerHistory->role }}
                                                </h4>
                                                <div class="flex gap-x-2">
                                                    <span class="text-sm  font-base text-gray-700">{{ $careerHistory->company_name }}</span>
                                                </div>
                                                <div class="flex gap-x-2 mb-4">
                                                    <span class="text-xs font-base GrayText">{{ \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y') }}
                                                        -
                                                        {{ !empty($careerHistory->end_date) ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : 'Present' }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                    @else
                                    <p class="text-sm">Not Applicable</p>
                                    @endif
                                </div>

                                <div x-show="tab == 'skills'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                    <h4 class="text-lg text-gray-700 font-semibold chart-heading">Skills
                                    </h4>
                                
                                    @if ($filteredPeople[0]->skills)
                                    @php
                                    $peopletypearr=[];
                                    if ($successPeople->isNotEmpty()){
                                        foreach ($successPeople as $successPerson){
                                            $peopletypearr[$successPerson->people_id]=$successPerson->type;
                                        }
                                    }
                                    $skillsArr = explode("\n", $filteredPeople[0]->skills);
                                    @endphp
                                
                                    <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                        @if(!empty($peopletypearr) && $peopletypearr[$filteredPeople[0]->people_id]=='Internal')
                                            @foreach ($skillsArr as $skill)
                                            <div class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                                                <span>{{ $skill }}</span>
                                            </div>
                                            @endforeach
                                        @else
                                        @if(!empty($filteredPeople))
                                            @foreach ($filteredPeople[0]->skills_list as $skill)
                                                <div class="flex items-center text-sm p-2 rounded-xl text-white {{ $skill['skill_type'] == 'AI Generated' ? 'bg-purple-500' : 'bg-green-500' }}">
                                                    <span>{{ $skill['skill_name'] }}</span>
                                                </div>
                                            @endforeach
                                            @endif
                                        @endif
                                    </div>
                                    @else
                                    <p class="text-sm">Not Applicable</p>
                                    @endif
                                </div>

                                <div x-show="tab == 'notes'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                    <div class="flex justify-between">
                                        <h4 class="text-lg text-gray-700 font-semibold chart-heading pt-1">
                                            Notes</h4>
                                        <button x-show="!nopen" @click="nopen = true" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                            <img class="h-4 w-4" src="{{ asset('images/Plus.svg') }}">
                                            <span class="block text-black pl-1"> Add Notes</span>
                                        </button>
                                        <div x-show="nopen" class="flex gap-2">
                                            <button @click="nopen = false" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                                <span class="block text-black pl-1"> Cancel</span>
                                            </button>
                                            <button x-show="nopen" wire:click.prevent="addNotes({{ $filteredPeople[0]->id }})" @click="nopen = false" type="button" class="flex justify-center items-center border p-2 rounded-md text-sm bg-mainBlue text-white">
                                                <span class="block pl-1"> Save</span>
                                            </button>
                                        </div>

                                        <!-- <button x-show="!enopen" @click="enopen = true" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                                                <img class="h-4 w-4" src="{{ asset('images/editicon.svg') }}">
                                                                <span class="block text-black pl-1"> Edit Notes</span>
                                                            </button>
                                                            <div x-show="enopen" class="flex gap-2">
                                                                <button @click="enopen = false" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                                                    <span class="block text-black pl-1"> Cancel</span>
                                                                </button>
                                                                <button x-show="enopen" wire:click.prevent="addNotes({{ $filteredPeople[0]->id }})" @click="enopen = false" type="button" class="flex justify-center items-center border p-2 rounded-md text-sm bg-mainBlue text-white">
                                                                    <span class="block pl-1"> Save</span>
                                                                </button>
                                                            </div> -->
                                    </div>

                                    <div class="mt-2" x-show="nopen">
                                        <textarea type="text" wire:model="filteredPnotes" rows="4" class="bg-gray-50 p-2 text-sm font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500" placeholder="Enter your note here">
                                                        </textarea>
                                    </div>

                                    @if ($successPersonNotes && $successPersonNotes->isNotEmpty())
                                    @foreach ($successPersonNotes as $note)
                                    <div class="mt-3">
                                        <div class="flex justify-between">
                                            <h3 class="text-xl md:text-base font-medium text-black">
                                                {{ $note->user_name }}
                                            </h3>

                                            <div class="flex justify-between">
                                                <span class="text-sm text-gray-500 font-normal">{{ \Carbon\Carbon::parse($note->created_at)->format('m/d/y') }}</span>
                                                <!-- <img class="h-6 w-6 ml-1 cursor-pointer"
                                                                            @click="confirmDeleteNote({{ $note->id }}, '{{ asset('images/redTrashIcon.svg') }}')"
                                                                            src="{{ asset('images/redTrashIcon.svg') }}" alt=""
                                                                        /> -->
                                            </div>

                                        </div>
                                        <p class="text-sm text-gray-600 font-normal">
                                            {{ $note->Notes }}
                                        </p>
                                    </div>
                                    @endforeach
                                    @else
                                    <div class="mt-2" x-show="!enopen && !nopen">
                                        <p class="text-sm">No notes available</p>
                                    </div>
                                    @endif

                                </div>
                            </div>
                        </div>
                        <!-- <h4 class="text-sm text-cyan-500 font-medium">{{ $filteredPeople[0]->location }}</h4> -->
                    </div>
                </div>
            </div>

            <!-- Update Score Modal container -->
            <div x-show="uopen" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50">
                <!-- Modal background with a higher z-index -->
                <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                <!-- Modal content with a lower z-index -->
                <div class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
                    <!-- Modal content -->

                    <img @click="uopen = false" class="absolute right top-2 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Search Icon">

                    <h2 class="text-black-900 text-xl text-black font-bold px-4">Update the Plan
                        scores for {{ $filteredPeople[0]->first_name }}</h2>

                    <div class="w-full border-t mt-3 border-gray-200"></div>


                    <div class="h-full">
                        <div class="h-5/6 flex items-center">
                            <div class="w-full">

                                <div class="modalscroll px-4">

                                    <div class="mt-4">
                                        <div class="flex justify-between mb-1">
                                            <h3 class="text-base font-medium text-black pt-1">Role
                                                Match Score</h3>
                                            <input type="number" min="0.0" max="1.0" step="0.1" id="updaterolScore" wire:model="updaterolScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->role_match }}" style="-moz-appearance: textfield;" oninput="clampValue(this)" wire:ignore>
                                        </div>

                                        <p class="text-sm text-gray-500">
                                            Score indicating the
                                            relevance of a person's
                                            role against the
                                            requirement of the plan.
                                            (0-low to 1-High)
                                        </p>
                                    </div>

                                    <div class="mt-4">
                                        <div class="flex justify-between mb-1">
                                            <h3 class="text-base font-medium text-black pt-1">
                                                Tenure Match Score</h3>
                                            <input type="number" min="0.0" max="1.0" step="0.1" style="-moz-appearance: textfield;" wire:ignore wire:model="updatetenureScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->tenure_match }}" oninput="clampValue(this)">
                                        </div>

                                        <p class="text-sm text-gray-500">
                                            Score indicating the
                                            relevance of a person's
                                            tenure against the
                                            requirement of the plan.
                                            (0-low to 1-High)
                                        </p>
                                    </div>

                                    <div class="mt-4">
                                        <div class="flex justify-between mb-1">
                                            <h3 class="text-base font-medium text-black pt-1">
                                                Location Match Score </h3>
                                            <input type="number" min="0.0" .0 max="1" step="0.1" style="-moz-appearance: textfield;" wire:ignore wire:model="updatelocScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->location_match }}" oninput="clampValue(this)">
                                        </div>

                                        <p class="text-sm text-gray-500">
                                            Score indicating the
                                            relevance of a person's
                                            location against the
                                            requirement of the plan.
                                            (0-low to 1-High)</p>
                                    </div>

                                    <div class="mt-4">
                                        <div class="flex justify-between mb-1">
                                            <h3 class="text-base font-medium text-black pt-1">
                                                Gender Match Score</h3>
                                            <input type="number" min="0.0" max="1.0" step="0.1" style="-moz-appearance: textfield;" wire:ignore wire:model="updategenScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->gender_match }}" oninput="clampValue(this)">
                                        </div>
                                        <p class="text-sm text-gray-500">
                                            Score indicating the
                                            relevance of a person's
                                            likely gender against
                                            the requirement of the
                                            plan. (0-low to 1-High)
                                        </p>
                                    </div>
                                    <div class="mt-4">
                                        <div class="flex justify-between mb-1">
                                            <h3 class="text-base font-medium text-black pt-1">Skill
                                                Match Score</h3>
                                            <input type="number" min="0.0" max="1.0" step="0.1" style="-moz-appearance: textfield;" wire:ignore wire:model="updateskillScore" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center scoreField" placeholder="{{ $filteredPeople[0]->skills_match }}" oninput="clampValue(this)">
                                        </div>


                                        <p class="text-sm text-gray-500">
                                            Score indicating the
                                            relevance of a person's
                                            skills against the
                                            requirement of the plan.
                                            (0-low to 1-High)
                                        </p>
                                    </div>
                                </div>

                                <div class="w-full border-t mt-4 border-gray-200"></div>

                                <!-- buttons wrapper -->
                                <div class="flex gap-2 w-full px-4 mt-4 ">
                                    <button @click="uopen = false" type="button" class="text-base bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                    <button wire:click.prevent="updateScore({{ $filteredPeople[0]->id }})" type="submit" class="text-base text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                        <span class="block"> Update</span>
                                    </button>
                                </div>

                            </div>


                        </div>
                    </div>
                </div>
            </div>

            <div x-show="ropen" @project-saved.window="ropen = false"  class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" style="display:none">
                <!-- Modal background with a higher z-index -->
                <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                <!-- Modal content with a lower z-index -->
                <div class="ropen-modal-content modal-content bg-white rounded-lg border border-gray-300 p-4 z-50" x-show="ropen" x-transition>
                    <div class="flex flex-col h-full">
                    <div class="flex justify-between border-b px-2 py-2">
                        <h3 class="text-base font-bold flex gap-2">Add to Recruitment</h3>
                        <div class="flex">
                            <img @click="closeRecruitModal" class="h-5 w-5 cursor-pointer" src="http://127.0.0.1:8000/images/cross.svg" alt="">
                        </div>
                    </div>
                    <div class="w-full">
                                <div class="mb-3 mt-3 px-2 py-2">
                                <div class="px-4">
                                    <!--<div class="recruitment-title">
                                        <div class="mb-3 flex">
                                            <label class="self-center w-64 text-sm text-gray-600">Recruitment Name</label>
                                            <input type="text" placeholder="Enter the Title" wire:model="recruitmentTitle" class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md">
                                        </div>
                                    </div>-->

                                    <div class="text-sm mb-3">Select how many interviews are in this recruitment process.</div>

                                        <div class="mb-3 flex">
                                        <label for="interviewCount" class="self-center w-40 text-sm text-gray-600">Number of Stages</label>
                                        <div class="flex items-center gap-3 mt-1">
                                            <button wire:click="decrement" class="border border-blue-500 rounded-lg w-8 h-8">-</button>
                                            <input type="number" id="interviewCount" wire:model.lazy="interviewCount" class="w-16 h-8 rounded-md p-2 text-center border border-blue-500" min="1">
                                            <button wire:click="increment" class="border border-blue-500 rounded-lg w-8 h-8">+</button>
                                        </div>
                                    </div>

                                    <div class="divider-border border-t mt-4 border-gray-200"></div>

                                    <div id="interviewFields">
                                    <div class="text-sm mb-2 mt-3">Provide a name for each interview in this process.</div>
                                        @foreach(range(1, $interviewCount) as $index)
                                            <div class="mb-3 flex">
                                                <label for="name{{ $index }}" class="self-center w-64 text-sm text-gray-600">Interview {{ $index }}</label>
                                                <input type="text" id="name{{ $index }}" placeholder="Enter the name" wire:model.defer="interviewfields.{{ $index - 1 }}" class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md">
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                        
                    </div>
                </div>
                    <div class="w-full border-t mt-4 border-gray-200"></div>
                    <div class="flex gap-4 w-full mt-4 ">
                        <button @click="closeRecruitModal" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                        <button wire:click="SaveProject" type="button" class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">
                            <span class="block"> Save</span>
                            <!--<img class="h-5 w-5" src="http://127.0.0.1:8000/images/right-arrow.svg">-->
                        </button>
                    </div>
                </div>
            
            </div>
        @endif
    @endif   

    <!-- View Popup Modal -->
<div x-show="rvopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" style="display:none">
    <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

    <div class="pipeline-modal-content bg-white rounded-lg border border-gray-300 p-4 z-50" x-show="rvopen" x-transition>
        <div class="flex flex-col h-full">
            <div class="flex justify-between border-b px-2 py-2">
                <h3 class="text-base font-bold flex gap-2">
                    
                company_name
                </h3>
                <div class="flex">                    
                    <img @click="closeViewDetailModal" class="h-5 w-5 cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="">
                </div>
            </div>
            <div class="grid grid-cols-2 gap-x-2 pt-3 flex-1">
                <div class="flex flex-1 flex-col justify-between">
                    <div class="flex px-2 py-2 gap-2 justify-between">

                        <div class="flex flex-col gap-y-2">
                            <h3 class="text-base font-bold">
                            first_name, last_name
                            </h3>
                            <h3 class="text-base text-gray-700 d-ruby gap-2">
                               
                                latest_role
                            </h3>
                        </div>

                        <div class="flex gap-x-2 items-center justify-center">
                            
                            <a id="linkedin" class="flex items-center justify-center hover:scale-105" href="#" target="_blank">
                                <img src="{{ asset('images/LinkedIn.svg') }}">
                            </a>
                            
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 grid-rows-4 flex-grow">
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Potential Sex</h3>
                            <h3 class="text-sm text-gray-700">gender
                            </h3>
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Function</h3>
                            <h3 class="text-sm text-gray-700">
                            function
                            </h3>
                        </div>
                        
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Tenure in Role</h3>
                            <h3 class="text-sm text-gray-700">Tenure
                            </h3>
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Readiness</h3>
                            
                            <span class="px-2 py-1 text-sm rounded-lg font-medium">
                            Readiness
                            </span>
                           
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Registrations</h3>
                            <h3 class="text-sm text-gray-700">
                            Registrations
                            </h3>
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            
                        </div>
                    </div>
                </div>
                <div x-data="{ tab: 'summary' }" class="flex flex-col pt-3 gap-y-2">
                            <div class="flex border-b-2">
                                <button @click="tab = 'summary'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                            'summary',
                                                        'chart-heading font-semibold': tab != 'summary'
                                                    }">
                                    Summary
                                </button>
                                <button @click="tab = 'career_history'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                            'career_history',
                                                        'chart-heading font-semibold': tab !=
                                                            'career_history'
                                                    }">
                                    Career History
                                </button>
                                <button @click="tab = 'skills'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                            'skills',
                                                        'chart-heading font-semibold': tab != 'skills'
                                                    }">
                                    Skills
                                </button>
                                <button @click="tab = 'notes'" class="w-full text-sm py-3 px-2 font-medium" :class="{
                                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                            'notes',
                                                        'chart-heading font-semibold': tab != 'notes'
                                                    }">
                                    Notes
                                </button>
                            </div>
                            <div x-show="tab == 'summary'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                <div class="flex justify-between">
                                    <h4 class="text-lg text-gray-700 font-semibold chart-heading mr-2">
                                        Summary</h4>
                                    <button x-show="!esummary" @click="esummary = true" type="button" class="flex justify-center items-center border py-2 px-4 rounded-md text-sm bg-mainBlue text-white">
                                        
                                    </button>

                                    <button x-show="esummary" @click="esummary = false"  type="button" class="flex justify-center items-center border py-2 px-4 rounded-md text-sm bg-mainBlue text-white">
                                        <span class="block text-white pl-1">Save</span>
                                    </button>
                                </div>

                                
                                <p x-show="!esummary" class="text-gray-500 py-5">No Summary Found
                                </p>


                                <textarea x-show="esummary" wire:model="successPersonSummary" rows="14" class="bg-gray-50 p-2 text-sm font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500" placeholder="Enter your summary here"></textarea>
                            </div>

                            <div x-show="tab == 'career_history'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                <h4 class="text-lg text-gray-700 font-semibold chart-heading">Career
                                    History</h4>
                               
                                <p class="text-sm">Not Applicable</p>
                            
                            </div>

                            <div x-show="tab == 'skills'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                <h4 class="text-lg text-gray-700 font-semibold chart-heading">Skills
                                </h4>
                              
                                
                               
                                <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                    
                                <p class="text-sm">Not Applicable</p>
                            </div>

                            <div x-show="tab == 'notes'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                <div class="flex justify-between">
                                    <h4 class="text-lg text-gray-700 font-semibold chart-heading pt-1">
                                        Notes</h4>
                                    

                                    
                                </div>

                               
                                <div class="mt-2" x-show="!enopen && !nopen">
                                    <p class="text-sm">No notes available</p>
                                </div>
                               

                            </div>
                        </div>

            </div>
        
        </div>
    </div>
</div>
</div>

    </div>
    @include('livewire.loading')
</div>

<script>

    function confirmRemovePerson(personId, personName, iconUrl) {
        Swal.fire({
            // title: `Remove ${personName}?`,
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Remove ${personName}</h2>
                    <p class="px-5 font-normal">Are you sure you want to remove <b>${personName}</b>
                    from your plan. They will stay in your
                    pipeline but any scores that have been
                    manually entered will be deleted.</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Remove",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                Livewire.dispatch('removeSuccessPerson', {
                    id: personId
                });
            }
        });
    }

    function confirmApprovePerson(personId, personName, iconUrl) {
        Swal.fire({
            // title: "Approve Candidate",
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Approve ${personName}</h2>
                    <p class="px-5 font-normal">Are you sure want to approve <b>${personName}</b>?</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Approve",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                Livewire.dispatch('approveSuccessPerson', {
                    id: personId
                });
            }
        });
    }
    
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>

async function downloadOrgChartPDF(plan_name) {
    const chart = document.getElementById('orgChartWrapper');

    const originalHeight = chart.style.height;
    const originalOverflow = chart.style.overflow;

    chart.style.height = "auto";
    chart.style.overflow = "visible";

    html2canvas(chart, { scale: 2 }).then(canvas => {
        chart.style.height = originalHeight;
        chart.style.overflow = originalOverflow;

        const imgData = canvas.toDataURL("image/png");

        // Initialize PDF
        const pdf = new jspdf.jsPDF({
            orientation: "landscape",
            unit: "px",
            format: [canvas.width, canvas.height + 100] // add extra height for header
        });

        // Add logo
        const logo = new Image();
        logo.src = '/images/LogoSmall.png'; // adjust the path

        logo.onload = function () {
            const logoWidth = 100;
            const logoHeight = 100;
            const centerX = (canvas.width - logoWidth) / 2;

            //pdf.addImage(logo, 'PNG', centerX, 20, logoWidth, logoHeight); //logo in center
            pdf.addImage(logo, 'PNG', 40, 20, logoWidth, logoHeight); //logo on left

            // Add title (centered)
            const title = plan_name;
            pdf.setFontSize(50);
            const textWidth = pdf.getTextWidth(title);
            const pageCenter = (canvas.width - textWidth) / 2;
            pdf.text(title, pageCenter, 130); // slightly below the logo

            // Add chart image
            pdf.addImage(imgData, "PNG", 0, 160, canvas.width, canvas.height);
            pdf.save("org-chart.pdf");
        };
    });
}
</script>
<script>
    function clampValue(input) {
        let value = input.value;

        // Allow partial entries with decimals like "0." or "0.1"
        if (value == "" || value == "." || value == "0." || /^0\.\d*$/.test(value) || /^1\.?0*$/.test(value)) {
            return;
        }

        value = parseFloat(value);

        if (isNaN(value) || value < 0 || value > 1) {
            value = 0;
            input.value = value;
        }

        if (value != "0.") {
            input.value = value.toFixed(1);
        }
    }

    // document.getElementById('download').addEventListener('click', () => {
    //     const loaderElement = document.getElementById("loader");
    //     if (loaderElement) {
    //         loaderElement.classList.add('enable-loading');
    //     }

    //     // Get all .tree ul elements
    //     const allTreeUls = document.querySelectorAll('.tree ul');

    //     console.log(allTreeUls);

    //     // Skip the first one
    //     const treeUlsToModify = Array.from(allTreeUls).slice(2);

    //     console.log(allTreeUls);

    //     // Remove padding temporarily
    //     treeUlsToModify.forEach(el => {
    //         el.style.paddingTop = '0px';
    //     });

    //     document.querySelectorAll('.dropdown-container').forEach(container => {
    //         container.classList.add('hidden');
    //     });

    //     // setTimeout(async () => {
    //     //     const elements = document.getElementsByClassName('plan-org-chart');
    //     //     const elementsArray = Array.from(elements);

    //     //     const processElement = async (element, index) => {
    //     //         const canvas = await html2canvas(element, {
    //     //             scale: 1.5,
    //     //             useCORS: true,
    //     //             scrollX: 0,
    //     //             scrollY: 0,
    //     //             width: element.scrollWidth,
    //     //             height: element.scrollHeight
    //     //         });

    //     //         const imgData = canvas.toDataURL('image/jpeg', 0.7);
    //     //         const logo = new Image();
    //     //         logo.src = '/images/LogoSmall.png';

    //     //         logo.onload = function () {
    //     //             const imgWidth = 297; // A4 landscape width in mm
    //     //             const canvasHeight = (canvas.height * imgWidth) / canvas.width;

    //     //             const { jsPDF } = window.jspdf;
    //     //             const pdf = new jsPDF({
    //     //                 orientation: 'landscape',
    //     //                 unit: 'mm',
    //     //                 format: [imgWidth, canvasHeight + 50],
    //     //                 compress: true
    //     //             });

    //     //             // Add logo top-left
    //     //             pdf.addImage(logo, 'PNG', 10, 10, 20, 20);

    //     //             // Add plan title top-center
    //     //             const planTitle = '{{ $plan->name }}' ?? 'Plan Title'; // dynamically set plan name
    //     //             pdf.setFontSize(18);
    //     //             const textWidth = pdf.getTextWidth(planTitle);
    //     //             const centerX = (imgWidth - textWidth) / 2;
    //     //             pdf.text(planTitle, centerX, 25);

    //     //             // Add org chart below title
    //     //             pdf.addImage(imgData, 'JPEG', 0, 45, imgWidth, canvasHeight);

    //     //             // Save PDF
    //     //             pdf.save(`plan-org-chart-${index + 1}.pdf`);

    //     //             // Revert padding after saving
    //     //             treeUlsToModify.forEach(el => {
    //     //                 el.style.paddingTop = '20px';
    //     //             });

    //     //             document.querySelectorAll('.dropdown-container').forEach(container => {
    //     //                 container.classList.remove('hidden');
    //     //             });
                    
    //     //             if (loaderElement) {
    //     //                 loaderElement.classList.remove('enable-loading');
    //     //             }
    //     //         };
    //     //     };

    //     //     for (let i = 0; i < elementsArray.length; i++) {
    //     //         await processElement(elementsArray[i], i);
    //     //     }

    //     // }, 100);

    //     setTimeout(async () => {
    //         const elements = document.getElementsByClassName('plan-org-chart');
    //         const elementsArray = Array.from(elements);

    //         const logo = new Image();
    //         logo.src = '/images/LogoSmall.png';

    //         const imgWidth = 297; // A4 landscape width in mm
    //         const { jsPDF } = window.jspdf;
    //         let pdf;
    //         console.log(elementsArray);
    //         logo.onload = async function () {
    //             for (let i = 0; i < elementsArray.length; i++) {
    //                 const element = elementsArray[i];
    //                 console.log(element);

    //                 const tempElement = element.querySelectorAll('.tree ul');

    //                 console.log(tempElement);

    //                 const elementModify = Array.from(tempElement).slice(2);
                    
    //                 console.log(elementModify);
    //                 const canvas = await html2canvas(element, {
    //                     scale: 1.5,
    //                     useCORS: true,
    //                     scrollX: 0,
    //                     scrollY: 0,
    //                     width: element.scrollWidth,
    //                     height: element.scrollHeight
    //                 });

    //                 const imgData = canvas.toDataURL('image/jpeg', 0.7);
    //                 const canvasHeight = (canvas.height * imgWidth) / canvas.width;

    //                 const imgHeight = canvasHeight + 50;

    //                 if (i === 0) {
    //                     pdf = new jsPDF({
    //                         orientation: 'landscape',
    //                         unit: 'mm',
    //                         format: [imgWidth, imgHeight],
    //                         compress: true
    //                     });
    //                 } else {
    //                     pdf.addPage([imgWidth, imgHeight]);
    //                     pdf.setPage(i + 1); // optional: make sure you're on the right page
    //                 }

    //                 // Add logo top-left
    //                 pdf.addImage(logo, 'PNG', 10, 10, 20, 20);

    //                 // Add plan title top-center
    //                 const planTitle = '{{ $plan->name }}' ?? 'Plan Title';
    //                 pdf.setFontSize(18);
    //                 const textWidth = pdf.getTextWidth(planTitle);
    //                 const centerX = (imgWidth - textWidth) / 2;
    //                 pdf.text(planTitle, centerX, 25);

    //                 // Add org chart image
    //                 pdf.addImage(imgData, 'JPEG', 0, 45, imgWidth, canvasHeight);
    //             }

    //             // Save final combined PDF
    //             pdf.save('plan-org-chart.pdf');

    //             // Revert padding after saving
    //             treeUlsToModify.forEach(el => {
    //                 el.style.paddingTop = '20px';
    //             });

    //             document.querySelectorAll('.dropdown-container').forEach(container => {
    //                 container.classList.remove('hidden');
    //             });

    //             if (loaderElement) {
    //                 loaderElement.classList.remove('enable-loading');
    //             }
    //         };
    //     }, 100);

        
    // });


document.getElementById('download').addEventListener('click', () => {
    const loaderElement = document.getElementById("loader");
    if (loaderElement) {
        loaderElement.classList.add('enable-loading');
    }

    const elements = document.getElementsByClassName('plan-org-chart');
    const elementsArray = Array.from(elements);

    const logo = new Image();
    logo.src = '/images/LogoSmall.png';

    logo.onload = async function () {
        const { jsPDF } = window.jspdf; // ✅ FIX: Required when using UMD version of jsPDF
        const imgWidth = 297; // A4 landscape width in mm
        let pdf;

        for (let i = 0; i < elementsArray.length; i++) {
            const originalElement = elementsArray[i];

            // ✅ Clone the element
            const clonedElement = originalElement.cloneNode(true);

            // Remove padding-top from ul (skip first 2)
            const allTreeUls = clonedElement.querySelectorAll('.tree ul');
            const ulsToModify = Array.from(allTreeUls).slice(2);
            ulsToModify.forEach(ul => {
                ul.style.paddingTop = '0px';
            });

            // Hide dropdowns in the cloned element
            clonedElement.querySelectorAll('.dropdown-container').forEach(container => {
                container.classList.add('hidden');
            });

            // Temporarily place the clone off-screen to render
            clonedElement.style.position = 'absolute';
            clonedElement.style.left = '-9999px';
            clonedElement.style.top = '0';
            document.body.appendChild(clonedElement);

            // Generate canvas from cloned element
            const canvas = await html2canvas(clonedElement, {
                scale: 1.5,
                useCORS: true,
                scrollX: 0,
                scrollY: 0,
                width: clonedElement.scrollWidth,
                height: clonedElement.scrollHeight
            });

            // Remove the off-screen clone
            document.body.removeChild(clonedElement);

            // Convert to image
            const imgData = canvas.toDataURL('image/jpeg', 0.7);
            const canvasHeight = (canvas.height * imgWidth) / canvas.width;
            const imgHeight = canvasHeight + 50;

            // Initialize or add page to PDF
            if (i === 0) {
                pdf = new jsPDF({
                    orientation: 'landscape',
                    unit: 'mm',
                    format: [imgWidth, imgHeight],
                    compress: true
                });
            } else {
                pdf.addPage([imgWidth, imgHeight]);
                pdf.setPage(i + 1);
            }

            // Add logo
            pdf.addImage(logo, 'PNG', 10, 10, 20, 20);

            // Plan title (replace dynamically in Laravel)
            const planTitle = '{{ $plan->name }}' ?? 'Plan Title';
            pdf.setFontSize(18);
            const textWidth = pdf.getTextWidth(planTitle);
            const centerX = (imgWidth - textWidth) / 2;
            pdf.text(planTitle, centerX, 25);

            // Add org chart image
            pdf.addImage(imgData, 'JPEG', 0, 45, imgWidth, canvasHeight);
        }

        // Save PDF
        if (pdf) {
            pdf.save('plan-org-chart.pdf');
        }

        // Restore dropdowns
        document.querySelectorAll('.dropdown-container').forEach(container => {
            container.classList.remove('hidden');
        });

        // Stop loader
        if (loaderElement) {
            loaderElement.classList.remove('enable-loading');
        }
    };
});
</script>
