<div>
    @include('livewire.flashMessage')
    <div x-data="{ show: {} }" class="customHeight grayBackground overflow-y-scroll">
        <div class="flex flexRow flex-col lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
            <div class="flex gap-4 items-center">
                <a href="{{ route('job.index') }}" class="flex gap-2 mainBlue items-center">
                    <img class="" src="{{ asset('images/ArrowLeftBlue.svg') }}">
                    <span>Back</span>
                </a>
                <h1 class="whitespace-nowrap text-3xl font-medium">Shortlist for {{ $jobdesc[0]->name }}</h1>
            </div>
            @if (auth()->user()->role != 'Viewer')
            <div class="hover:shadow-lg hover:scale-105 rounded-lg">
                <div class="flex items-center justify-center w-full border text-center rounded-lg px-2">
                    <a href="{{ route('job.people.index', ['job' => $job]) }}" class="w-full flex gap-2 justify-center items-center text-black py-2 text-center text-sm font-medium">
                        <img class="" src="{{ asset('images/peoplesearchIcon.svg') }}">
                        <span>People Search</span>
                    </a>
                </div>
            </div>
            @endif
        </div>

        <div class="my-5 px-4 flex flex-col gap-y-10">
            <div class="grid grid-cols-2 gap-5">
                <div class="flex flex-col gap-y-3 bg-white border rounded-2xl px-5 py-5 justify-between items-start shadow-md">
                    <h1 class="text-sm text-left chart-heading">Potential Sex Diversity</h1>
                    <div class="flex doughnut-chart-container gap-x-5 justify-between items-center">
                        <canvas id="genderSplitChart"></canvas>
                        <ul id="custom-gender-legend" class="custom-h-legend whitespace-nowrap"></ul>
                    </div>
                </div>
                <div class="flex flex-col gap-y-3 px-5 py-5 bg-white border rounded-2xl justify-between items-start shadow-md">
                    <h1 class="text-sm text-left chart-heading">Top 5 Companies Identified</h1>
                    <div class="flex division-vbar-chart-container">
                        <canvas id="topCompaniesBar"></canvas>
                        <ul id="custom-topcompanies-legend" class="custom-vbar-legend custom-h-legend whitespace-nowrap"></ul>
                    </div>
                </div>
                <div class="flex flex-col gap-y-3 bg-white border rounded-2xl px-5 py-5 justify-between items-start shadow-md">
                    <h1 class="text-sm text-left chart-heading">Top 5 Locations Identified</h1>
                    <div class="flex division-vbar-chart-container">
                        <canvas id="locationBar"></canvas>
                        <ul id="custom-division-legend" class="custom-vbar-legend custom-h-legend whitespace-nowrap"></ul>
                    </div>
                </div>
                <div class="flex flex-col gap-y-3 bg-white border rounded-xl px-5 py-5 justify-between items-start shadow-md overflow-hidden overflow-y-scroll">
                    <h1 class="text-sm text-left chart-heading">Function Breakdown</h1>
                    <div class="flex doughnut-chart-container gap-x-5 justify-between items-center">
                        <canvas id="functionDoughnut"></canvas>
                        <div class="h-32 w-max">
                            <ul id="custom-function-legend" class="custom-h-legend whitespace-nowrap"></ul>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Section for searching and filtering -->
            <div x-data="{
                    drawer: false,
                    vopen: @entangle('vopen'),
                    addToPlanPopup : @entangle('addToPlanPopup'),
                    addToTalentPoolPopup : @entangle('addToTalentPoolPopup'),
                    addToPlansArray: @entangle('addToPlansArray'),
                    addToTalentPoolArray: @entangle('addToTalentPoolArray'),
                    checkSelection() {
                        if (Object.values(this.addToPlansArray).every(value => !value)) {
                            toastr.info('Please select plan!');
                            return false;
                        }
                        return true;
                    },
                    checkSelectionOfTalentPool() {
                        if (Object.values(this.addToTalentPoolArray).every(value => !value)) {
                            toastr.info('Please select Talent Pool!');
                            return false;
                        }
                        return true;
                    },
                    }" class="flex flex-col gap-y-2 bg-white px-2 py-2 rounded-lg border">
                <div class="flex gap-x-2 py-3 rounded-xl"  x-data="{ dallopen: false, selected: @entangle('selectedPerson') }">
                    <button @click="drawer = true" class="py-1.5 px-1.5 text-black  border rounded-lg flex justify-center items-center gap-1 font-medium">
                        <img class="search-icon w-auto mr-2" src="{{ asset('/images/search-icon.svg') }}" alt="Search Icon">
                        <span class="text-sm font-semibold whitespace-nowrap">Advanced Search</span>
                    </button>
                    <div class="flex flex-1 gap-x-2 px-2 py-2 border border-[#EAECF0] justify-start items-center bg-white rounded-lg">
                        <input wire:model="searchByKeyword" class="bg-transparent flex-grow focus:outline-none" type="text" placeholder="Search">
                        <img wire:click="searchPeople"  class="search-icon w-auto mr-2" src="{{ asset('/images/search-icon.svg') }}" alt="Search Icon">    
                    </div>
                    <!--<button wire:click="searchPeople" class="py-1.5 px-3 text-white bg-cyan-500 border rounded-lg flex justify-center items-center gap-1 font-medium">
                        <img class="search-icon w-auto mr-2" src="{{ asset('/images/MagnifyingGlassWhite.svg') }}" alt="Search Icon">
                        <span class="text-sm font-semibold whitespace-nowrap">Search</span>
                    </button>-->


                    <button @click.prevent="if (selected && Object.keys(selected).length > 0) { $wire.addSelectedToPlan(); }"  class="py-1.5 px-3 border rounded-lg flex justify-center items-center gap-1 font-medium">
                            <img class="add-icon w-auto mr-2" src="{{asset('images/pluswithcircle.svg')}}" alt="Add to Plan Icon">
                            <span class="text-md font-semibold">Add to Plan</span>
                        </button>
                        <button @click.prevent="if (selected && Object.keys(selected).length > 0) { $wire.addSelectedToTalentPool(); }"  class="py-1.5 px-3  border rounded-lg flex justify-center items-center gap-1 font-medium">
                            <img class="add-icon w-auto mr-2" src="{{asset('images/pluswithcircle.svg')}}" alt="Add to Talent Pool Icon">
                            <span class="text-md font-semibold">Add to Talent Pool</span>
                        </button> 

                        <button @click=" if (selected && Object.keys(selected).length > 0) { dallopen = true; } " class="py-1.5 px-3 border rounded-lg flex justify-center items-center gap-1 font-medium">
                            <img class="delete-icon w-auto" src=" {{asset('images/deleteIcon.svg')}}" alt="Delete Icon">
                        </button>

                        <div x-show="dallopen" @click.away="dallopen = false" class="fixed inset-0 flex items-center justify-center z-50" style="display:none">
                                <!-- Modal background with a higher z-index -->
                                <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                                <!-- Modal content with a lower z-index -->
                                <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50 w-96" x-show="dallopen" x-transition>
                                    <!-- Modal content -->
                                    <div class="flex justify-end">
                                        <img @click="dallopen = false" class="h-5 w-5 cursor-pointer" src="{{asset('images/cross.svg')}}" alt="">
                                    </div>
                                    <div class="flex gap-y-3 flex-col text-center justify-center items-center">
                                        <img class="h-10 w-10" src="{{ asset('images/deleteIcon_red.svg') }}">
                                        <p class="text-sm chart-heading font-semibold">Are you sure you want to remove Selected person from your pipeline they will be permantely erased from your plan.</p>
                                    </div>
                                    <div class="w-full mt-6 border-t delete-modal-action-container flex items-center justify-center gap-x-6">
                                        <button x-on:click="dallopen = false" class="border text-sm text-black rounded-xl py-2 px-20 font-medium">Cancel</button>
                                        <button x-on:click="dallopen = false" wire:click="deleteSelected" class="border text-sm text-danger rounded-xl py-2 px-20 font-medium delete-button hover:text-white">Delete</button>
                                    </div>
                                </div>
                            </div>




                    <div x-data="{open: false}" class="relative z-40 sortBy-width">
                        <div @click="open = !open" class="flex px-2 py-3 border cursor-pointer border-[#EAECF0] justify-between items-center bg-white rounded-lg">
                            <h2 class="text-sm font-semibold">Sort by</h2>
                            <img class="h-5 w-5" src="{{asset('images/caret-down.svg')}}" alt="">
                        </div>
                        <div x-show="open" @click.away="open = false" class="select-items absolute w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                            <div class="flex items-center px-4 py-2 hover:bg-gray-100 ">
                                <input @click="open = false" type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="tenure" id="allStatus" wire:click="Ordermytable('tenure')">
                                <label class="closedColor text-semibold p-1 rounded-lg px-2 text-xs block cursor-pointer" for="allStatus">Tenure</label>
                            </div>
                            <div class="flex items-center px-4 py-2 hover:bg-gray-100 ">
                                <input @click="open = false" type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="created_at" id="activeStatus" wire:click="Ordermytable('created_at')">
                                <label class="activeColor p-1 rounded-lg px-2 text-xs block cursor-pointer" for="activeStatus">Date Added</label>
                            </div>
                            <div class="flex items-center px-4 py-2 hover:bg-gray-100 ">
                                <input @click="open = false" type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="skills_match" id="draftStatus" wire:click="Ordermytable('skills_match')">
                                <label class="DraftColor  p-1 rounded-lg px-2 text-xs cursor-pointer" for="draftStatus">Skills Match</label>
                            </div>
                        </div>
                    </div>
                    @php
                    $filterArr = [
                    "jobId" => $this->job,
                    "sortBy" => $this->sortBy,
                    "sortDirection" => $this->sortDirection,
                    "search" => $this->search,
                    "searchByKeyword" => $this->searchByKeyword,
                    "forename" => $this->forename,
                    "surname" => $this->surname,
                    "gender" => $this->gender,
                    "role" => $this->role,
                    "company" => $this->company,
                    "function" => $this->function,
                    "division" => $this->division,
                    "slocation" => $this->slocation,
                    "regBodies" => $this->regBodies,
                    "moverFilter" => $this->moverFilter,
                    "min_exp" => $this->min_exp
                    ];
                    @endphp
                    <button wire:click="downloadShortListedPeople({{ json_encode($filterArr) }})" class="py-1.5 px-1.5 text-black  border rounded-lg flex justify-center items-center gap-1 font-medium">
                        <img class="h-5 font-bold w-auto img px-1" src="{{ asset('images/BlackDownload.png') }}">
                        <span class="text-md font-semibold">Download</span>
                    </button>
                </div>

                <div x-data="{ open: {} }" class="h-[70vh] w-full flex flex-col justify-between shadow-sm overflow-y-scroll relative rounded-lg border">
                    <table class="min-w-full divide-y divide-gray-300 rounded-lg border">
                        <thead class="sticky top-0 z-30 rounded-lg">
                            <tr class="table-header">
                            <th scope="col" class="flex items-center px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">  <div class="flex flex-col gap-y-1 text-start mr-2">
                                        <input type="checkbox" x-on:click="$dispatch('select-all', $event.target.checked); $wire.set('selectedPerson', $event.target.checked ? @json($jobPeople->pluck('id')) : [])" x-bind:checked="$wire.selectedPerson.length > 0" class="form-checkbox h-4 w-4">
                                        </div> Name</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Country</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Company</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Function</th>
                                @if($jobdetails->ethnicity === 1)
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Potential Diversity</th>
                                @endif
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Potential Sex</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Tenure</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name" style="min-width:120px;">Readiness</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Status</th>
                                <th scope="col" class="px-2 py-2 text-center text-sm font-semibold whitespace-nowrap table-column-name">Regisration Status</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white">
                            @if(count($jobPeople) > 0)
                            @foreach ($jobPeople as $pipelinePerson)
                            <tr class="@if ($pipelinePerson->people_mover == 'Mover') RedBG moverRow @endif">
                                <td class="flex whitespace-nowrap text-center text-gray-900 px-2">
                                <div class="flex flex-col gap-y-1 text-start mr-2 mt-2">
                                                <input type="checkbox" wire:model="selectedPerson" value="{{ $pipelinePerson->id }}" class="form-checkbox h-4 w-4">
                                        </div>
                                    <div class="flex flex-col gap-y-1 text-start">
                                        <!-- <h3 class="font-semibold">{{ $pipelinePerson->company_name }} </h3> -->
                                        <h3 class="text-sm mt-1 font-semibold table-h3">{{ $pipelinePerson->first_name }} {{ $pipelinePerson->last_name }} </h3>
                                        <h3 class="text-xs table-column-name font-semibold text-gray-800 table-h3">{{ $pipelinePerson->latest_role }}</h3>
                                        <!-- <h3 class="text-sm mt-1 text-gray-700">{{ $pipelinePerson->location }}</h3> -->
                                    </div>
                                </td>
                                <td class="whitespace-wrap text-center text-xs text-gray-500">{{ $pipelinePerson->country }}</td>
                                <td class="whitespace-wrap text-center text-xs text-gray-500">{{ $pipelinePerson->company_name }}</td>
                                <td class="whitespace-wrap text-center text-xs text-gray-500">{{ $pipelinePerson->function }}</td>
                                @if($jobdetails->ethnicity === 1)
                                <td class="text-center text-xs text-gray-500">{{ $pipelinePerson->diverse }}</td>
                                @endif
                                <td class="text-center text-xs text-gray-500">{{ $pipelinePerson->gender }}</td>
                                <td class="text-center text-xs text-gray-500">{{ $pipelinePerson->tenure }}</td>
                                <td class="text-center text-xs px-3 py-3">
                                    @if($pipelinePerson->readiness == 'Ready')
                                    <span class="py-2 px-4 rounded-lg font-medium redlinessReaddy">
                                        Ready
                                    </span>
                                    @else
                                    <span class="py-2 px-4 rounded-lg font-medium RedBG text-red-500 @if ($pipelinePerson->people_mover == 'Mover') white-bg-2px @endif">
                                        Not Ready
                                    </span>
                                    @endif
                                </td>
                                <td class="text-center text-xs text-gray-500">
                                    @if(strtolower($pipelinePerson->status) == "approved")
                                    <span class="success-badge">
                                        {{$pipelinePerson->status}}
                                    </span>

                                    @else
                                    <span class="info-badge">
                                        {{$pipelinePerson->status}}
                                    </span>
                                    @endif
                                </td>

                                <td class="text-center text-xs text-gray-500">{{ $pipelinePerson->other_tags }}</td>
                                <td x-data="{ open: false, dopen: false }" class="px-2 py-2 relative">
                                    <img @click="open = !open" class="h-5 w-5 cursor-pointer" src="{{asset('images/kebab-icon.svg')}}" alt="">
                                    <div class="relative">
                                        <div x-show="open" @click.away="open = false" class="absolute flex flex-col justify-start right-0 bg-white border border-gray-200 rounded-md shadow-lg z-20 table-dropdown">
                                            <div @click="vopen = true, open = false" wire:click.prevent="viewIndividual({{ $pipelinePerson->people_id }})" class="px-2 py-2 text-gray-800 hover:bg-gray-100 text-sm whitespace-nowrap flex items-center gap-x-2 justify-start cursor-pointer">
                                                <img class="h-4 w-4" src="{{asset('images/eye.svg')}}" alt="">
                                                <h2 class="text-xs">View</h2>
                                            </div>
                                            @if (auth()->user()->role != 'Viewer')
                                                <div @click="open = false" wire:click="showAddToPlanPopup({{ $pipelinePerson->id }})" class="flex items-center gap-x-2 px-2 py-2 text-gray-800 hover:bg-gray-100 text-sm whitespace-nowrap justify-start cursor-pointer">
                                                    <img class="h-4 w-4" src="{{asset('images/pluswithcircle.svg')}}" alt="">
                                                    <h2 class="text-xs">Add to Plan</h2>
                                                </div>
                                                <div @click="open = false" wire:click="showAddToTalentPoolPopup({{ $pipelinePerson->id }})" class="flex items-center gap-x-2 px-2 py-2 text-gray-800 hover:bg-gray-100 text-sm whitespace-nowrap justify-start cursor-pointer">
                                                    <img class="h-4 w-4" src="{{asset('images/pluswithcircle.svg')}}" alt="">
                                                    <h2 class="text-xs">Add to Talent Pool</h2>
                                                </div>
                                                <div @click="dopen = true, open = false" class="flex items-center gap-x-2 px-2 py-2 text-gray-800 hover:bg-gray-100 text-sm whitespace-nowrap justify-start cursor-pointer">
                                                    <img class="h-4 w-4" src="{{ asset('images/deleteIcon.svg') }}">
                                                    <h2 class="text-xs">Delete</h2>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    <div x-show="dopen" class="fixed inset-0 flex items-center justify-center z-50" style="display:none">
                                        <!-- Modal background with a higher z-index -->
                                        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                                        <!-- Modal content with a lower z-index -->
                                        <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50 w-96" x-show="dopen" x-transition>
                                            <!-- Modal content -->
                                            <div class="flex justify-end">
                                                <img @click="dopen = false" class="h-5 w-5 cursor-pointer" src="{{asset('images/cross.svg')}}" alt="">
                                            </div>
                                            <div class="flex gap-y-3 flex-col text-center justify-center items-center">
                                                <img class="h-10 w-10" src="{{ asset('images/deleteIcon_red.svg') }}">
                                                <p class="text-sm chart-heading font-semibold">Are you sure you want to remove this person from your pipeline they will be permantely erased from your plan.</p>
                                            </div>
                                            <div class="w-full mt-6 border-t delete-modal-action-container flex items-center justify-center gap-x-6">
                                                <button x-on:click="dopen = false" class="border text-sm text-black rounded-xl py-2 px-20 font-medium">Cancel</button>
                                                <button x-on:click="dopen = false" wire:click="removepipPerson({{ $pipelinePerson->id }})" class="border text-sm text-danger rounded-xl py-2 px-20 font-medium delete-button hover:text-white">Delete</button>
                                            </div>
                                        </div>
                                    </div>
                                    @if($selectedIndividualID === $pipelinePerson->people_id)
                                    <!-- Modal container -->
                                    <div x-show="vopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50" style="display:none">
                                        <!-- Modal background with a higher z-index -->
                                        <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                                        <!-- Modal content with a lower z-index -->
                                        <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50" x-show="vopen" x-transition>
                                            <!-- Modal content -->
                                            <div class="flex flex-col h-full">
                                                <div class="flex justify-between border-b px-2 py-2">
                                                    <h3 class="text-base font-bold text-black flex gap-2">
                                                        @if($pipelinePerson->people_mover == 'Mover' && $pipelinePerson->current_company)
                                                        {{ $pipelinePerson->company_name }}
                                                        <img src="{{ asset('images/right-arrow-black.svg') }}" alt="">
                                                        <span class="text-red-500">{{ $pipelinePerson->current_company }}</span>
                                                        @else
                                                        {{ $pipelinePerson->company_name }}
                                                        @endif
                                                    </h3>

                                                    <div class="flex">
                                                        @if($pipelinePerson->people_mover == 'Mover')
                                                        <img wire:click="refreshMover({{ $pipelinePerson }})" title="Refresh" class="h-4 w-4 mr-2 cursor-pointer" src="{{ asset('images/sync-alt-solid.svg') }}" alt="">
                                                        @endif
                                                        <img @click="vopen = false" class="h-5 w-5 cursor-pointer" src="{{asset('images/cross.svg')}}" alt="">
                                                    </div>
                                                </div>
                                                <div class="grid grid-cols-2 gap-x-2 pt-3 flex-1">
                                                    <div class="flex flex-1 flex-col justify-between">
                                                        <div class="flex px-2 py-2 gap-2 justify-between">
                                                            <div class="flex flex-col gap-y-2">
                                                                <h3 class="text-base font-bold text-black">{{ $pipelinePerson->first_name}} {{ $pipelinePerson->last_name}}</h3>
                                                                <h3 class="text-base text-gray-700 d-ruby">
                                                                    @if($pipelinePerson->people_mover == 'Mover' && $pipelinePerson->current_role)
                                                                    <span>{{ $pipelinePerson->latest_role }} </span>
                                                                    &nbsp;<img src="{{ asset('images/right-arrow-black.svg') }}" alt=""> &nbsp;
                                                                    <span class="text-red-500">{{ $pipelinePerson->current_role }}</span>
                                                                    @else
                                                                    <span>{{ $pipelinePerson->latest_role }}</span>
                                                                    @endif
                                                                </h3>
                                                            </div>
                                                            <div class="flex gap-x-2 items-center justify-center">
                                                                @if(strtolower($pipelinePerson->status) != "approved" && auth()->user()->id == $jobDetail->user_id)
                                                                <div wire:click.prevent="approveJobPeople({{$pipelinePerson->id}})" class="flex cursor-pointer  px-2 h-8 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                                                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M18 32.4L9.6 24l-2.8 2.8L18 38 42 14l-2.8-2.8L18 32.4z" fill="#4CAF50" />
                                                                    </svg>
                                                                </div>
                                                                @endif
                                                                @if (auth()->user()->role != 'Viewer')
                                                                    <div class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                                                        <img class="h-4 w-4 mr-2" src="{{ asset('images/Plus.svg') }}" alt="">
                                                                        <h2 @click="addToTalentPoolPopup = true;" class="text-sm font-semibold">Add to Talent Pool</h2>
                                                                    </div>
                                                                    <div class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                                                        <img class="h-4 w-4 mr-2" src="{{ asset('images/Plus.svg') }}" alt="">
                                                                        <h2 @click="addToPlanPopup = true;" class="text-sm font-semibold">Add to Plan</h2>
                                                                    </div>
                                                                @endif
                                                                @if($pipelinePerson->linkedinURL != 'NA')
                                                                <a wire:ignore target="_blank" id="linkedin" class="flex items-center justify-center hover:scale-105" href="{{ $pipelinePerson->linkedinURL }}">
                                                                    <img src="{{ asset('images/LinkedIn.svg') }}">
                                                                </a>
                                                                @endif
                                                            </div>
                                                        </div>
                                                        <div class="grid grid-cols-2 gap-2 flex-grow {{$jobdetails->ethnicity == 1 ? 'grid-row-4' : 'grid-rows-3'}}">
                                                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                                <h3 class="text-sm font-bold chart-heading">Potential Sex</h3>
                                                                <h3 class="text-md chart-heading">{{ $pipelinePerson->gender}}</h3>
                                                            </div>
                                                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                                <h3 class="text-sm font-bold chart-heading">Function</h3>
                                                                <h3 class="text-md chart-heading">{{ $pipelinePerson->function}}</h3>
                                                            </div>
                                                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                                <h3 class="text-sm font-bold chart-heading">Tenure in Role</h3>
                                                                <h3 class="text-md chart-heading">{{ $pipelinePerson->tenure}}</h3>
                                                            </div>
                                                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                                <h3 class="text-sm font-bold chart-heading">Readiness</h3>
                                                                @if($pipelinePerson->readiness == 'Ready')
                                                                <h3 class="py-1 px-2 text-xs w-max rounded-lg font-medium redlinessReaddy">Ready</h3>
                                                                @else
                                                                <h3 class="py-1 px-2 text-xs w-max rounded-lg font-medium RedBG text-red-500">Not Ready</h3>
                                                                @endif
                                                            </div>
                                                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                                <h3 class="text-sm font-bold chart-heading">Registrations</h3>
                                                                <h3 class="text-md chart-heading">{{ $pipelinePerson->other_tags}}</h3>
                                                            </div>
                                                            @if($jobdetails->ethnicity === 1)
                                                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                                                <h3 class="text-sm font-bold chart-heading">Potential Diversity</h3>
                                                                <h3 class="text-md chart-heading">{{ $pipelinePerson->diverse}}</h3>
                                                            </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div x-data="{tab: 'summary', nopen: false, enopen: false}" class="flex flex-col pt-3 gap-y-2">
                                                        <div class="flex border-b-2">
                                                            <button @click="tab = 'summary'" class="w-full text-sm py-3 px-2 font-medium z-20" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'summary', 'chart-heading font-semibold': tab != 'summary' }">
                                                                Summary
                                                            </button>
                                                            <button @click="tab = 'career_history'" class="w-full text-sm py-3 px-2 font-medium z-20" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'career_history', 'chart-heading font-semibold': tab != 'career_history' }">
                                                                Career History
                                                            </button>
                                                            <button @click="tab = 'skills'" class="w-full text-sm py-3 px-2 font-medium z-20" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'skills', 'chart-heading font-semibold': tab != 'skills' }">
                                                                Skills
                                                            </button>
                                                            <button @click="tab = 'notes'" class="w-full text-sm py-3 px-2 font-medium z-20" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'notes', 'chart-heading font-semibold': tab != 'notes' }">
                                                                Notes
                                                            </button>

                                                            <button @click="tab = 'jobs'" class="w-full text-sm py-3 px-2 font-medium"
                                                                :class="{
                                                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                                                'jobs',
                                                                            'chart-heading font-semibold': tab != 'jobs'
                                                                        }">
                                                                Talent Pool
                                                            </button>
                                                            <button @click="tab = 'plans'" class="w-full text-sm py-3 px-2 font-medium"
                                                                :class="{
                                                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                                                'plans',
                                                                            'chart-heading font-semibold': tab != 'plans'
                                                                        }">
                                                                Plans
                                                            </button>
                                                        </div>
                                                        <div x-show="tab == 'summary'" class="{{$jobdetails->ethnicity == 1 ? 'h-96' : 'h-80'}} overflow-hidden overflow-y-scroll flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                            <h4 class="text-sm text-gray-700 font-medium chart-heading">Summary</h4>
                                                            <p class="text-sm text-gray-500 mt-5">{{ $pipelinePerson->summary ? $pipelinePerson->summary : 'No summary available' }}</p>
                                                        </div>
                                                        <div x-show="tab == 'career_history'" class="{{$jobdetails->ethnicity == 1 ? 'h-96' : 'h-80'}} overflow-hidden overflow-y-scroll flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                            <h4 class="text-sm text-gray-700 font-medium chart-heading">Career History</h4>
                                                            <div class="flex flex-col items-start w-full">
                                                                @if($groupedPeoplesCareer && $groupedPeoplesCareer->isNotEmpty())
                                                                <div class="flex flex-col items-start w-full">
                                                                    @foreach ($groupedPeoplesCareer as $careerHistory)
                                                                    <div class="flex h-max items-start justify-center mb-1">
                                                                        <div class="flex flex-col mt-1 items-center justify-center h-full w-2">
                                                                            <div class="rounded-full  blueBalls bg-mainBlue"></div>
                                                                            <div class="flex-grow border-l border-mainBlue"></div>
                                                                        </div>
                                                                        <div class="flex flex-col  items-start justify-start pl-4">
                                                                            <h4 class="text-sm font-semibold text-gray-900">{{$careerHistory->role}}</h4>
                                                                            <div class="flex gap-x-2">
                                                                                <span class="text-sm  font-base text-gray-700">{{ $careerHistory->company_name }}</span>
                                                                            </div>
                                                                            <div class="flex gap-x-2 mb-4">
                                                                                <span class="text-xs font-base GrayText">{{ \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y') }} - {{ !empty($careerHistory->end_date) ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : "Present" }}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    @endforeach
                                                                </div>
                                                                @else
                                                                <p class="text-sm text-gray-500 mt-5">No career history available</p>
                                                                @endif
                                                            </div>
                                                        </div>
                                                        <div x-show="tab == 'skills'" class="{{$jobdetails->ethnicity == 1 ? 'h-96' : 'h-80'}} overflow-hidden overflow-y-scroll flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                            <h4 class="text-sm text-gray-700 font-medium chart-heading">Skills</h4>
                                                            @if($peopleskills->isNotEmpty())
                            <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                @foreach($peopleskills as $skill)
                                <div class="flex items-center text-xs p-2 rounded-xl text-white {{ $skill->skill_type == 'AI Generated' ? 'bg-purple-500' : 'bg-green-500' }}">
                                    <span>{{ $skill -> skill_name }}</span>
                                </div>
                                @endforeach
                            </div>
                                                            @else
                                                            <p class="text-sm text-gray-500 mt-5">No skills available</p>
                                                            @endif
                                                        </div>
                                                        <div x-show="tab == 'jobs'"
                                                            class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Talent Pools</h4>
                                                            @if ($shortListUsers && $shortListUsers->isNotEmpty())
                                                            <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                                                @foreach ($shortListUsers as $job)
                                                                <div
                                                                    class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                                                                    <span>{{ $job->name }}</span>
                                                                </div>
                                                                @endforeach
                                                            </div>
                                                            @else
                                                            <p class="text-sm text-gray-500 mt-5">No talent pool available</p>
                                                            @endif
                                                        </div>
                                                        <div x-show="tab == 'plans'"
                                                            class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Plans</h4>
                                                            @if ($userPlansData && $userPlansData->isNotEmpty())
                                                            <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                                                @foreach ($userPlansData as $plan)
                                                                <div
                                                                    class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                                                                    <span>{{ $plan->name }}</span>
                                                                </div>
                                                                @endforeach
                                                            </div>
                                                            @else
                                                            <p class="text-sm text-gray-500 mt-5">No plans available</p>
                                                            @endif
                                                        </div>
                                                        <div x-show="tab == 'notes'" class="{{$jobdetails->ethnicity == 1 ? 'h-96' : 'h-80'}} overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                                            <div class="flex justify-between">
                                                                <h4 class="text-sm text-gray-700 font-medium chart-heading"">Notes</h4>
                                                                        <button x-show=" !nopen" @click="nopen = true" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                                                    <img class="h-4 w-4" src="{{ asset('images/Plus.svg') }}">
                                                                    <span class="block text-black pl-1"> Add Notes</span>
                                                                    </button>
                                                                    <div x-show="nopen" class="flex gap-2">
                                                                        <button @click="nopen = false" type="button" class="flex justify-center  items-center  border p-2 rounded-md  text-sm">
                                                                            <span class="block text-black pl-1"> Cancel</span>
                                                                        </button>
                                                                        <button x-show="nopen" wire:click.prevent="addNotes({{ $pipelinePerson->people_id }})" @click="nopen = false" type="button" class="flex justify-center items-center border p-2 rounded-md text-sm bg-mainBlue text-white">
                                                                            <span class="block pl-1"> Save</span>
                                                                        </button>
                                                                    </div>
                                                            </div>

                                                            <div class="mt-2" x-show="nopen">
                                                                <textarea type="text" wire:model="filteredPnotes" rows="4" class="bg-gray-50 p-2 text-sm font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500" placeholder="Enter your note here">
                                                                </textarea>
                                                            </div>

                                                            @if ($successPersonNotes && count($successPersonNotes) > 0)
                                                            @foreach($successPersonNotes as $note)
                                                            <div class="mt-3">
                                                                <div class="flex justify-between">
                                                                    <h3 class="text-xl md:text-base font-medium text-black">{{ $note->user_name }}</h3>

                                                                    <div class="flex justify-between">
                                                                        <span class="text-sm text-gray-500 font-normal">{{ \Carbon\Carbon::parse($note->created_at)->format('m/d/y') }}</span>
                                                                        <!-- <img class="h-6 w-6 ml-1 cursor-pointer" 
                                                                                    @click="confirmDeleteNote({{ $note->id }}, '{{asset('images/redTrashIcon.svg')}}')"
                                                                                    src="{{asset('images/redTrashIcon.svg')}}" alt=""
                                                                                /> -->
                                                                    </div>

                                                                </div>
                                                                <p class="text-sm text-gray-600 font-normal">
                                                                    {{ $note->Notes }}
                                                                </p>
                                                            </div>
                                                            @endforeach
                                                            @else
                                                            <div class="mt-2" x-show="!enopen && !nopen">
                                                                <p class="text-sm text-gray-500 mt-5">No notes available</p>
                                                            </div>
                                                            @endif

                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- <h4 class="text-sm text-cyan-500 font-medium">{{ $pipelinePerson->location}}</h4> -->
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                </td>

                            </tr>
                            @endforeach
                            @else
                            <tr>
                                <td colspan="11" class="whitespace-nowrap text-center text-gray-900 px-2 max-w-min text-center">
                                    <h1 class="text-center chart-heading mt-10 mb-10">No People Found</h1>
                                </td>
                            </tr>
                            @endif
                        </tbody>
                    </table>
                    <div class="py-4 px-2 sticky bottom-0 bg-white">
                        {{ $jobPeople->links('vendor.pagination.tailwind') }}
                    </div>
                </div>
                @if (auth()->user()->role != 'Viewer')
                <div x-show="addToPlanPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
                    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
                    <div  x-data="{openPlanPopup: @entangle('openPlanPopup') }" class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50"> 
                        <div class="flex justify-between">
                            <h2 class="text-black-900 text-xl font-semibold">Add to plan</h2>
                            <button type="button" @click="addToPlanPopup = false" class="text-gray-500 hover:text-gray-800">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <!-- select people box -->
                        <div class="flex justify-center items-center mt-2">
                            <div class="bg-white border selectPeople p-2 rounded-lg">
                                <!-- User list with checkboxes -->
                                <ul class="mt-4 adddeduser space-y-6 py-4 overflow-y-auto" style="max-height: 300px;">
                                    @if(!empty($plansList) && $plansList->isNotEmpty())
                                    @foreach ($plansList as $plan)
                                    <div class="w-full border-t my-1 border-gray-200"></div>
                                    <li class="flex justify-between">
                                        <div class="flex items-center gap-2 pl-4">
                                            <div class="space-y-1">
                                                <span class="text-sm font-semibold block">{{ $plan->name}}</span>
                                            </div>
                                        </div>
                                        {{-- Checkbox for selecting direct reports --}}
                                        <input type="checkbox"
                                            wire:model="addToPlansArray.{{ $plan->id }}"
                                            id="addDirectReportPeople-{{ $plan->id }}"
                                            class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out">
                                    </li>
                                    <div class="w-full border-t my-1 border-gray-200"></div>

                                    @endforeach
                                    @else
                                    <div class="w-full border-t my-1 border-gray-200"></div>
                                    <li class="flex justify-between">No plans found!</li>
                                    <div class="w-full border-t my-1 border-gray-200"></div>
                                    @endif

                                </ul>
                                <div class="flex justify-end mt-4">
                                    <!-- Submit the form when the button is clicked -->
                                    <button type="button" class="p-2 rounded-lg"
                                    @click="openPlanPopup ? $wire.addSelectedPersonToPlans() : (checkSelection() && $wire.addpeopleToPlans())">Add</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                @if (auth()->user()->role != 'Viewer')
                <div x-show="addToTalentPoolPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
                    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
                    <div  x-data="{openTalentPopup: @entangle('openTalentPopup') }" class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
                        <div class="flex justify-between">
                            <h2 class="text-black-900 text-xl font-semibold">Add to Talent Pools</h2>
                            <button type="button" @click="addToTalentPoolPopup = false" class="text-gray-500 hover:text-gray-800">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <!-- select people box -->
                        <div class="flex justify-center items-center mt-2">
                            <div class="bg-white border selectPeople p-2 rounded-lg">
                                <!-- User list with checkboxes -->
                                <ul class="mt-4 adddeduser space-y-6 py-4 overflow-y-auto" style="max-height: 300px;">
                                    @if(!empty($talentPoolsList) && $talentPoolsList->isNotEmpty())
                                    @foreach ($talentPoolsList as $talentPool)
                                    <div class="w-full border-t my-1 border-gray-200"></div>
                                    <li class="flex justify-between">
                                        <div class="flex items-center gap-2 pl-4">
                                            <div class="space-y-1">
                                                <span class="text-sm font-semibold block">{{ $talentPool->name}}</span>
                                            </div>
                                        </div>
                                        {{-- Checkbox for selecting direct reports --}}
                                        <input type="checkbox"
                                            wire:model="addToTalentPoolArray.{{ $talentPool->id }}"
                                            id="addToTalentPoolArray-{{ $talentPool->id }}"
                                            class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out">
                                    </li>
                                    <div class="w-full border-t my-1 border-gray-200"></div>

                                    @endforeach
                                    @else
                                    <div class="w-full border-t my-1 border-gray-200"></div>
                                    <li class="flex justify-between">No talent pool found!</li>
                                    <div class="w-full border-t my-1 border-gray-200"></div>
                                    @endif

                                </ul>
                                <div class="flex justify-end mt-4">
                                    <!-- Submit the form when the button is clicked -->
                                    <button type="button" class="p-2 rounded-lg"
                                    @click="openTalentPopup ? $wire.addSelectedPersonToTalentPool() : (checkSelectionOfTalentPool() && $wire.addpeopleToTalentPools())">Add</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <div x-show="drawer" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50 right-0" style="display:none">
                    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                    <div x-show="drawer" x-transition:enter="transition transform ease-out duration-300" x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition transform ease-in duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full" class="drawer z-50 bg-white absolute right-0 shadow-lg overflow-hidden">
                        <div class="flex justify-between mt-2 h-5">
                            <h2 class="font-semibold">
                                Advanced Search
                            </h2>
                            <img @click="drawer = false" class="h-5 w-5 cursor-pointer" src="{{asset('images/cross.svg')}}" alt="">
                        </div>
                        <div class="border-t mt-3 border-gray-200"></div>
                        <div class="py-3 advanced_search_drawer relative overflow-y-scroll">
                            <div class="flex gap-y-2 flex-col">
                                <div class="">
                                    <label for="name" class="block text-xs font-medium labelcolor">First Name</label>
                                    <input wire:model="forename" type="text" id="name" placeholder="Enter first name" name="name" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                                </div>
                                <div class="">
                                    <label for="name" class="block text-xs font-medium labelcolor">Last Name</label>
                                    <input wire:model="surname" type="text" id="name" placeholder="Enter last name" name="name" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                                </div>
                                <div class="">
                                    <fieldset>
                                        <legend class="text-xs font-medium labelcolor">Potential Sex</legend>
                                        <ul class="donate-now mt-1">
                                            <li>
                                                <input class="cursor-pointer" type="radio" id="Male" name="gender" value="Male" wire:model="gender" />
                                                <label for="Male" class="text-center font-semibold labelcolor">Male</label>
                                            </li>
                                            <li>
                                                <input class="cursor-pointer" type="radio" id="Female" name="gender" value="Female" wire:model="gender" />
                                                <label for="Female" class="text-center font-semibold labelcolor">Female</label>
                                            </li>
                                        </ul>
                                    </fieldset>
                                </div>
                                <div class="flex flex-col gap-y-2">
                                    <h2 class="block text-xs font-medium labelcolor">Location</h2>
                                    <div class="text-xs" wire:ignore x-data="{
                                                        multiple: true,
                                                        value: @entangle('slocation'),
                                                        options: {{ json_encode($locations) }},
                                                        init() {
                                                                this.$nextTick(() => {
                                                                    let choices = new Choices(this.$refs.select)
                                                    
                                                                    let refreshChoices = () => {
                                                                        let selection = this.multiple ? this.value : [this.value]
                                                    
                                                                        choices.clearStore()
                                                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                                                            value,
                                                                            label,
                                                                            selected: selection.includes(value),
                                                                        })))
                                                                    }
                                                    
                                                                    refreshChoices()
                                                    
                                                                    this.$refs.select.addEventListener('change', () => {
                                                                        this.value = choices.getValue(true)
                                                                    })
                                                    
                                                                    this.$watch('value', () => refreshChoices())
                                                                    this.$watch('options', () => refreshChoices())
                                                                })
                                                            }
                                                        }" class="bg-white text-xs max-w-sm w-full">
                                        <select x-ref="select"></select>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-y-2">
                                    <h2 class="block text-xs font-medium labelcolor">Role</h2>
                                    <div class="text-xs" wire:ignore x-data="{
                                                        multiple: true,
                                                        value: @entangle('_role'),
                                                        options: {{ json_encode($personRoles) }},
                                                        init() {
                                                                this.$nextTick(() => {
                                                                    let choices = new Choices(this.$refs.select)
                                                    
                                                                    let refreshChoices = () => {
                                                                        let selection = this.multiple ? this.value : [this.value]
                                                    
                                                                        choices.clearStore()
                                                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                                                            value,
                                                                            label,
                                                                            selected: selection.includes(value),
                                                                        })))
                                                                    }
                                                    
                                                                    refreshChoices()
                                                    
                                                                    this.$refs.select.addEventListener('change', () => {
                                                                        this.value = choices.getValue(true)
                                                                    })
                                                    
                                                                    this.$watch('value', () => refreshChoices())
                                                                    this.$watch('options', () => refreshChoices())
                                                                })
                                                            }
                                                        }" class="bg-white text-xs max-w-sm w-full">
                                        <select x-ref="select"></select>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-y-2">
                                    <h2 class="block text-xs font-medium labelcolor">Previous Role</h2>
                                    <div class="text-xs" wire:ignore x-data="{
                                                        multiple: true,
                                                        value: @entangle('previousRole'),
                                                        options: {{ json_encode($personRoles) }},
                                                        init() {
                                                                this.$nextTick(() => {
                                                                    let choices = new Choices(this.$refs.select)
                                                    
                                                                    let refreshChoices = () => {
                                                                        let selection = this.multiple ? this.value : [this.value]
                                                    
                                                                        choices.clearStore()
                                                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                                                            value,
                                                                            label,
                                                                            selected: selection.includes(value),
                                                                        })))
                                                                    }
                                                    
                                                                    refreshChoices()
                                                    
                                                                    this.$refs.select.addEventListener('change', () => {
                                                                        this.value = choices.getValue(true)
                                                                    })
                                                    
                                                                    this.$watch('value', () => refreshChoices())
                                                                    this.$watch('options', () => refreshChoices())
                                                                })
                                                            }
                                                        }" class="bg-white text-xs max-w-sm w-full">
                                        <select x-ref="select"></select>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-y-2">
                                    <h2 class="block text-xs font-medium labelcolor">Company</h2>
                                    <div class="text-xs" wire:ignore x-data="{
                                                        multiple: false,
                                                        value: @entangle('company'),
                                                        options: {{ json_encode($companies) }},
                                                        init() {
                                                                this.$nextTick(() => {
                                                                    let choices = new Choices(this.$refs.select)
                                                    
                                                                    let refreshChoices = () => {
                                                                        let selection = this.multiple ? this.value : [this.value]
                                                    
                                                                        choices.clearStore()
                                                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                                                            value,
                                                                            label,
                                                                            selected: selection.includes(value),
                                                                        })))
                                                                    }
                                                    
                                                                    refreshChoices()
                                                    
                                                                    this.$refs.select.addEventListener('change', () => {
                                                                        this.value = choices.getValue(true)
                                                                    })
                                                    
                                                                    this.$watch('value', () => refreshChoices())
                                                                    this.$watch('options', () => refreshChoices())
                                                                })
                                                            }
                                                        }" class="bg-white text-xs max-w-sm w-full">
                                        <select x-ref="select"></select>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-y-2">
                                    <h2 class="block text-xs font-medium labelcolor">Function</h2>
                                    <div class="text-xs" wire:ignore x-data="{
                                                        multiple: false,
                                                        value: @entangle('function'),
                                                        options: {{ json_encode($functions) }},
                                                        init() {
                                                                this.$nextTick(() => {
                                                                    let choices = new Choices(this.$refs.select)
                                                    
                                                                    let refreshChoices = () => {
                                                                        let selection = this.multiple ? this.value : [this.value]
                                                    
                                                                        choices.clearStore()
                                                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                                                            value,
                                                                            label,
                                                                            selected: selection.includes(value),
                                                                        })))
                                                                    }
                                                    
                                                                    refreshChoices()
                                                    
                                                                    this.$refs.select.addEventListener('change', () => {
                                                                        this.value = choices.getValue(true)
                                                                    })
                                                    
                                                                    this.$watch('value', () => refreshChoices())
                                                                    this.$watch('options', () => refreshChoices())
                                                                })
                                                            }
                                                        }" class="bg-white text-xs max-w-sm w-full">
                                        <select x-ref="select"></select>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-y-2">
                                    <h2 class="block text-xs font-medium labelcolor">Division</h2>
                                    <div class="text-xs" wire:ignore x-data="{
                                                        multiple: false,
                                                        value: @entangle('division'),
                                                        options: {{ json_encode($divisions) }},
                                                        init() {
                                                                this.$nextTick(() => {
                                                                    let choices = new Choices(this.$refs.select)
                                                    
                                                                    let refreshChoices = () => {
                                                                        let selection = this.multiple ? this.value : [this.value]
                                                    
                                                                        choices.clearStore()
                                                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                                                            value,
                                                                            label,
                                                                            selected: selection.includes(value),
                                                                        })))
                                                                    }
                                                    
                                                                    refreshChoices()
                                                    
                                                                    this.$refs.select.addEventListener('change', () => {
                                                                        this.value = choices.getValue(true)
                                                                    })
                                                    
                                                                    this.$watch('value', () => refreshChoices())
                                                                    this.$watch('options', () => refreshChoices())
                                                                })
                                                            }
                                                        }" class="bg-white text-xs max-w-sm w-full">
                                        <select x-ref="select"></select>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-y-2">
                                    <h2 class="block text-xs font-medium labelcolor">Mover/Non Mover</h2>
                                    <select wire:model="moverFilter" class="flex px-2 py-3 border cursor-pointer border-[#EAECF0] justify-between items-center bg-white rounded-lg text-gray-500">
                                        <option value="" selected>All</option>
                                        <option value="Mover">Mover</option>
                                        <option value="Non Mover">Non Mover</option>
                                    </select>
                                </div>
                                <div class="">
                                    <label for="name" class="block text-xs font-medium labelcolor">Regulatory Bodies</label>
                                    <input wire:model="regBodies" type="text" id="name" placeholder="Enter Regulatory Bodies" name="name" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                                </div>
                                <div class="mt-2">
                                    <label for="name" class="block text-xs font-medium labelcolor">Minimum Tenure</label>
                                    <div class="Tenure mt-1 justify-between">
                                        {{--
                                        <button
                                            class="block w-8 border-2 px-1 rounded-md @if (!$min_exp) disabled @endif"
                                            wire:click="changeMinimumTenure('decrease')"
                                            @if (!$min_exp) disabled @endif>
                                            <img class="search-icon w-8 h-8 bg-white"
                                                src="{{ asset('images/Minus.svg') }}"
                                        alt="Search Icon">
                                        </button>
                                        --}}
                                        <input type="number" wire:model="min_exp" min="0" inputmode="numeric" class="w-[75%] outline-none block text-center bg-white p-2 text-md font-normal border border-gray-300 h-10 rounded-md w-full" placeholder="0">
                                        {{--
                                        <button class="block w-8 px-1 border-2  rounded-md"
                                            wire:click="changeMinimumTenure('increase')">
                                            <img class="search-icon w-8 h-8  bg-white"
                                                src="{{ asset('images/Plus.svg') }}"
                                        alt="Search Icon">
                                        </button>
                                        --}}
                                    </div>
                                </div>
                            </div>
                            <div class="sticky bottom-0 w-full border-t mt-6 pt-2 pb-2 flex items-center justify-between bg-white">
                                <button class="border text-sm text-black rounded-xl py-2 advanced-search-clear font-medium" wire:click="clearFilters" @click='drawer = false'>Reset</button>
                                <button class="border text-sm text-white rounded-xl py-2 advanced-search-submit font-medium bg-cyan-500 hover:text-white" wire:click="runfilters" @click='drawer = false'>Show Results</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @include('livewire.loading')

    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var genderChart
        var functionBarChart
        var locationBarChart
        var topCompaniesBarChart
        const genderCTX = document.getElementById('genderSplitChart').getContext('2d');
        const _genderLabels = @json($genderLabels);
        const _genderData = @json($genderData);
        const genderData = {
            labels: _genderLabels,
            datasets: [{
                label: 'My Organization',
                data: _genderData,
                hoverOffset: 4
            }]
        };

        const genderConfig = {
            type: 'doughnut',
            data: genderData,
            options: {
                animation: false,
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                cutout: '80%', // Adjusts the inner radius, making the doughnut thicker or thinner
                plugins: {
                    dataLabals: {
                        show: false
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(tooltipItem) {
                                const dataset = tooltipItem.dataset;
                                const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                const currentValue = dataset.data[tooltipItem.dataIndex];
                                const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                return `${currentValue} (${percentage}%)`;
                            }
                        }
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-gender-legend');
                    ul.innerHTML = '';
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                        ul.appendChild(li);
                    });
                }
            }]
        };
        genderChart = new Chart(genderCTX, genderConfig);

        const functionCTX = document.getElementById('functionDoughnut').getContext('2d');
        const _functionLabels = @json($functionLabels);
        const _functionData = @json($functionData);
        const functionData = {
            labels: _functionLabels,
            datasets: [{
                label: 'Functions',
                data: _functionData,
                hoverOffset: 4
            }]
        };

        const functionConfig = {
            type: 'doughnut',
            data: functionData,
            options: {
                animation: false,
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                cutout: '80%', // Adjusts the inner radius, making the doughnut thicker or thinner
                plugins: {
                    dataLabals: {
                        show: false
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(tooltipItem) {
                                const dataset = tooltipItem.dataset;
                                const total = dataset.data.reduce((sum, value) => sum + value, 0);
                                const currentValue = dataset.data[tooltipItem.dataIndex];
                                const percentage = Math.floor(((currentValue / total) * 100) + 0.5);
                                return `${currentValue} (${percentage}%)`;
                            }
                        }
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-function-legend');
                    ul.innerHTML = '';
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.innerHTML = `<span style="background-color:${color}"></span> <div>${label}: ${value} <span id='percentage'>(${percentage}%)<span></div>`;
                        ul.appendChild(li);
                    });
                }
            }]
        };
        functionBarChart = new Chart(functionCTX, functionConfig);

        const verticalData = {
            labels: @json($divisionLabels),
            datasets: [{
                label: 'People',
                data: @json($divisionData),
                backgroundColor: [
                    '#3B82F6',
                    '#FFA347',
                    '#8B5CF6',
                    '#4CB140',
                    'rgb(75, 192, 192)'
                ]
            }]
        };
        const verticalConfig = {
            type: 'bar',
            data: verticalData,
            options: {
                animation: false,
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                indexAxis: 'y',
                borderRadius: {
                    bottomLeft: 10,
                    bottomRight: 10,
                    topRight: 10,
                    topLeft: 10
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    datalabels: {
                        anchor: 'end',
                        align: 'end', // Align labels to the end (right side of bars)
                        formatter: function(value, context) {
                            const total = context.dataset.data.reduce((sum, val) => sum + val, 0);
                            return value + ' (' + ((value / total) * 100).toFixed(2) + '%)';
                        },
                        color: '#000', // Label text color
                        offset: 2 // Adjust the offset from the bars
                    }
                },
                scales: {
                    x: {
                        display: false, // Hide x-axis
                        beginAtZero: true // Start y-axis at zero
                    },
                    y: {
                        border: {
                            display: false,
                        },
                        grid: {
                            display: false, // Hide grid lines
                        },
                        ticks: {
                            display: true // Show y-axis labels
                        },
                        axis: {
                            display: false // Hide y-axis line
                        }
                    }
                },
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-division-legend');
                    ul.innerHTML = '';
                    // ul.style.justifyContent = 'space-between'
                    // ul.style.alignItems = 'center'
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.classList.add('child')
                        li.id = 'percentage'
                        li.style.marginBottom = '0px'
                        li.innerHTML = `${value}`;
                        ul.appendChild(li);
                    });
                    const children = ul.children;
                    const numChildren = children.length;
                    if (numChildren > 1) {
                        const maxPadding = 7;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    } else {
                        ul.style.justifyContent = 'center'
                    }
                    if (numChildren == 2) {
                        const maxPadding = 10;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    }
                    // Calculate the margin based on the number of children
                }
            }]
        };

        const horizontalData = {
            labels: @json($companyLabels),
            datasets: [{
                label: 'People',
                data: @json($companyData),
                backgroundColor: [
                    '#3B82F6',
                    '#FFA347',
                    '#8B5CF6',
                    '#4CB140',
                    'rgb(75, 192, 192)'
                ]
            }]
        };

        const horizontalConfig = {
            type: 'bar',
            data: horizontalData,
            options: {
                animation: false,
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                indexAxis: 'y',
                borderRadius: {
                    bottomLeft: 10,
                    bottomRight: 10,
                    topRight: 10,
                    topLeft: 10
                },
                scales: {
                    x: {
                        display: false, // Hide x-axis
                        beginAtZero: true // Start y-axis at zero
                    },
                    y: {
                        border: {
                            display: false,
                        },
                        grid: {
                            display: false, // Hide grid lines
                        },
                        ticks: {
                            display: true // Show y-axis labels
                        },
                        axis: {
                            display: false // Hide y-axis line
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    datalabels: {
                        anchor: 'end',
                        align: 'end', // Align labels to the end (right side of bars)
                        formatter: function(value, context) {
                            const total = context.dataset.data.reduce((sum, val) => sum + val, 0);
                            return value + ' (' + ((value / total) * 100).toFixed(2) + '%)';
                        },
                        color: '#000', // Label text color
                        offset: 2 // Adjust the offset from the bars
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-topcompanies-legend');
                    ul.innerHTML = '';
                    // ul.style.justifyContent = 'space-between'
                    // ul.style.alignItems = 'center'
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.classList.add('child')
                        li.id = 'percentage'
                        li.style.marginBottom = '0px'
                        li.innerHTML = `${value}`;
                        ul.appendChild(li);
                    });
                    const children = ul.children;
                    const numChildren = children.length;
                    if (numChildren > 1) {
                        const maxPadding = 7;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    } else {
                        ul.style.justifyContent = 'center'
                    }
                    if (numChildren == 2) {
                        const maxPadding = 10;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    }
                }
            }]
        };

        // Create the chart
        locationBarChart = new Chart(
            document.getElementById('locationBar'),
            verticalConfig
        );

        topCompaniesBarChart = new Chart(
            document.getElementById('topCompaniesBar'),
            horizontalConfig
        );
        Livewire.on('updateChart', () => {
            console.log("helloooo")

            if (functionBarChart) {
                functionBarChart.destroy()
            }
            functionBarChart = new Chart(
                document.getElementById('functionDoughnut'),
                functionConfig
            );
            if (locationBarChart) {
                locationBarChart.destroy()
            }
            locationBarChart = new Chart(
                document.getElementById('locationBar'),
                verticalConfig
            );
            if (genderChart) {
                genderChart.destroy()
            }
            genderChart = new Chart(
                document.getElementById('genderSplitChart'),
                genderConfig
            );
            if (topCompaniesBarChart) {
                topCompaniesBarChart.destroy()
            }
            topCompaniesBarChart = new Chart(
                document.getElementById('topCompaniesBar'),
                horizontalConfig
            );
        });
    })
</script>