<div>
    @if($individualPerson  !== null)
    <div x-data="{ pnopen: false}" class="">
        <!-- Modal container -->
        <div
            class="fixed inset-0 flex items-center justify-center z-50"
            >
            <!-- Modal background with a higher z-index -->
            <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
            <!-- Modal content with a lower z-index -->
            <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50">
                <!-- Modal content -->
                <div x-transition.duration.500ms class="flex">
                    <div>
                        <h3 class="text-base font-bold">{{ $individualPerson->forename}} {{ $individualPerson->surname}}</h3>
                        <h3 class="text-base text-gray-700 font-semibold">{{ $individualPerson->role}}</h3>
                        <h4 class="text-sm text-sky-500 font-medium">{{ $individualPerson->division}} - {{ $individualPerson->function}}</h4>
                    </div>
                    <div class="flex flex-1 justify-end">
                        <div>
                            <div class="flex items-center justify-center">
                                <button wire:click = "resetInternalPerson" class="flex items-center justify-center hover:scale-105">
                                    <img class="h-4 w-auto" src="{{ asset('images/left-arrow.png') }}">
                                </button>
                            </div>
                            
                            <div x-data="{ uopen: false}">
                                <div>
                                    <button @click="uopen = true" class="w-full relative flex items-center justify-center gap-x-2 rounded-br-lg border border-transparent py-2 px-3 text-sm font-semibold text-gray-900 hover:scale-105">
                                        <img class="h-4 w-auto" src="{{ asset('images/pencil.png') }}">
                                    </button>
                                </div>
                                <div x-show="uopen"
                                            class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50"
                                            style="display:none">
                                    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                                    <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50">
                                        <h2 class="text-black-900 text-xl text-black font-bold">Update Employee</h2>
                                        <p class="mt-4 text-sm font-light text-gray-700">Update an individual's role details and skills here.</p>
                                            <div>
                                                <div class="mt-4 flex gap-x-5">
                                                    <div class="w-3/6">
                                                        <label class="text-sm font-medium text-black">Role</label>
                                                        <input type="text" class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" wire:model="updateRole" placeholder="{{ $individualPerson->role }}">
                                                    </div>
                                                    <div class="w-3/6">
                                                        <label class="text-sm font-medium text-black">Function</label>
                                                        <input type="text" class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" wire:model="updateFunction" placeholder="{{ $individualPerson->function }}">
                                                    </div>
                                                </div>
                                                <div class="mt-4 flex gap-x-5">
                                                    <div class="w-3/6">
                                                        <label class="text-sm font-medium text-black">Division</label>
                                                        <input type="text"class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" wire:model="updateDivision" placeholder="{{ $individualPerson->division }}">
                                                    </div>
                                                    <div class="w-3/6">
                                                        <label class="text-sm font-medium text-black">Readiness</label>
                                                        <input type="text"class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" wire:model="updateReadiness" placeholder="{{ $individualPerson->readiness }}">
                                                    </div>
                                                </div>
                                                <div class="mt-4">
                                                    <label class="text-sm font-medium text-black">ReportTo</label>
                                                    <input type="text"class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" wire:model="updateReportTo">
                                                </div>
                                                <div class="mt-2">
                                                    <h3 class="mt-5 text-black font-semibold text-lg">Skills</h3> 
                                                    <div>
                                                        <div class="mt-2">
                                                            <div class="flex gap-x-5 items-center">
                                                                <h3 class="text-sm font-medium">Common</h3>
                                                                <label class="text-xs text-gray-700">Add Skill</label>
                                                                <button wire:click.prevent="addCommonSkill">
                                                                    <img class="h-4 w-auto" src="{{ asset('images/plus.png') }}">
                                                                </button>
                                                            </div>
                                                            <div>
                                                                @foreach($commonskills as $index => $CSkill)
                                                                <div class="flex gap-x-2 mt-1">
                                                                    <input wire:model="commonskills.{{ $index }}.name" class="w-48 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500" placeholder="{{ $CSkill['name'] }}">
                                                                    <input wire:model="commonskills.{{ $index }}.rating" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center" placeholder="{{ $CSkill['rating'] }}">
                                                                    <button class="hover:scale-105" wire:click.prevent="removeSkill({{ $CSkill['name'] }})">
                                                                        <img class="h-4 w-auto" src="{{ asset('images/minus.png') }}">
                                                                    </button>
                                                                </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                        <div class="mt-3">
                                                            <div class="flex gap-x-5 items-center">
                                                                <h3 class="text-sm font-medium">Specialised</h3>
                                                                <label class="text-xs text-gray-700">Add Skill</label>
                                                                <button class="hover:scale-105" wire:click="addspecialisedSkill">
                                                                    <img class="h-4 w-auto" src="{{ asset('images/plus.png') }}">
                                                                </button>
                                                            </div>
                                                            <div>
                                                                @foreach($specialisedskills as $index => $Spskill)
                                                                <div class="flex gap-x-2 mt-1">
                                                                    <input wire:model="specialisedskills.{{ $index }}.name" class="w-48 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500" placeholder="{{ $Spskill['name'] }}">
                                                                    <input wire:model="specialisedskills.{{ $index }}.rating" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center" placeholder="{{ $Spskill['rating'] }}">
                                                                    <button wire:click.prevent="#">
                                                                        <img class="h-4 w-auto" src="{{ asset('images/minus.png') }}">
                                                                    </button>
                                                                </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                        <div class="mt-3">
                                                            <div class="flex gap-x-5 items-center">
                                                                <h3 class="text-sm font-medium">Certifications</h3>
                                                                <label class="text-xs text-gray-700">Add Skill</label>
                                                                <button wire:click.prevent="addcertification">
                                                                    <img class="h-4 w-auto" src="{{ asset('images/plus.png') }}">
                                                                </button>
                                                            </div>
                                                            <div>
                                                                @foreach($certifications as $index => $Cerskill)
                                                                <div class="flex gap-x-2 mt-1">
                                                                    <input wire:model="certifications.{{ $index }}.name" class="w-48 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500" placeholder="{{ $Cerskill['name'] }}">
                                                                    <input wire:model="certifications.{{ $index }}.rating" class="w-10 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 hover:text-cyan-500 placeholder:text-center items-center" placeholder="{{ $Cerskill['rating'] }}">
                                                                    <button wire:click.prevent="#">
                                                                        <img class="h-4 w-auto" src="{{ asset('images/minus.png') }}">
                                                                    </button>
                                                                </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-3 flex items-center justify-center gap-x-6">
                                                    <div class="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                        <button x-on:click="uopen =false"  type="button" class="w-full transition ease-in-out delay-100 border border-sky-300 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                                    </div>
                                                    <div class="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                        <button x-on:click="uopen =false" type="submit" wire:click="updateInternalCandidate( {{ $individualPerson->id }})" class="w-full transition ease-in-out delay-100 border border-sky-300 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Update</button>
                                                    </div>
                                                </div>
                                            </div>
                                    </div>
                                </div>
                            </div>
                            <div x-init="pnopen = false" class="flex items-center justify-center">
                                <button x-on:click="pnopen = !pnopen" class="flex items-center justify-center hover:scale-105">
                                    <img class="h-4 w-auto" src="{{ asset('images/notebook.png') }}">
                                </button>
                            </div>
                        </div>

                    </div>
                    </div>
                    <div class="p-2">
                        <div class="w-full border-t border-grey-300"></div>
                    </div>
                    <div class="flex gap-x-5">
                        <div class="w-80 h-80">
                            <h4 class="text-sm text-gray-700 font-medium">Skills</h4>
                            <div class="h-full flex items-center justify-center">
                                <div class="h-80 w-80" wire:ignore x-data="{
                                                                svalues: {{ json_encode($individualSkillsdata) }},
                                                                slabels:{{ json_encode($individualSkillslabels) }},
                                                                init() {
                                                                let chart = new ApexCharts(this.$refs.skillschart, {
                                                                                                                    chart: { type: 'bar',
                                                                                                                            stacked:true },
                                                                                                                            plotOptions: { bar: { horizontal: true } },
                                                                                                                            tooltip: {enabled:false},
                                                                                                                            grid:{show:false},
                                                                                                                            xaxis: { categories: this.slabels }, 
                                                                                                                            series: [{
                                                                                                                            name: 'Skills',
                                                                                                                            data: this.svalues,
                                                                                                                            }],
                                                                                                                    })
                                                                            chart.render()
                                                                            }
                                                                }">
                                    <div x-ref="skillschart"></div>
                                </div>
                            </div>                                  
                        </div>
                        <div class="w-80 h-80">
                            <h4 class="text-sm text-gray-700 font-medium">Plans</h4>
                            @if (empty($Curplan))
                            <div class="mt-1 flex-1 items-center justify-center h-full">
                                <div>
                                    <p class="text-xs text-center text-gray-600"> Not included in any plans</p>
                                    <div class="flex items-center justify-center">
                                        <button>
                                            <span class="text-xs text-center font-semibold">Add to plan</span>
                                        <button>
                                    </div>
                                </div>
                            </div>
                            @else

                                @if ($Curplan->isEmpty())
                                <div class="mt-1 h-full">
                                    <div x-data="{ popen: false }">
                                        <div x-show="!popen"
                                            class="">
                                            <p class="text-xs text-center text-gray-600"> Not included in any plans</p>
                                            <div class="flex items-center justify-center">
                                                <button @click="popen = true">
                                                    <span class="text-xs text-center font-semibold">Add to plan</span>
                                                <button>
                                            </div>
                                        </div>
                                        <div x-show="popen"
                                             class=""
                                             style="display:none">

                                            <p class="text-xs text-gray-700">Plans(s) where candidate is not included in:</p>
                                            <div class="text-xs"
                                                wire:ignore x-data="{
                                                    multiple: true,
                                                    value: @entangle('selectedPlans'),
                                                    options: {{ json_encode($plans) }},
                                                    init() {
                                                            this.$nextTick(() => {
                                                                let choices = new Choices(this.$refs.select)
                                                
                                                                let refreshChoices = () => {
                                                                    let selection = this.multiple ? this.value : [this.value]
                                                
                                                                    choices.clearStore()
                                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                                        value,
                                                                        label,
                                                                        selected: selection.includes(value),
                                                                    })))
                                                                }
                                                
                                                                refreshChoices()
                                                
                                                                this.$refs.select.addEventListener('change', () => {
                                                                    this.value = choices.getValue(true)
                                                                })
                                                
                                                                this.$watch('value', () => refreshChoices())
                                                                this.$watch('options', () => refreshChoices())
                                                            })
                                                        }
                                                    }"
                                                    class="bg-white text-xs max-w-sm w-full">
                                                <select x-ref="select" :multiple="multiple"></select>
                                            </div>
                                            <div>
                                                <button wire:click="addtoPlan" @click="popen = false">
                                                    <span class="text-xs hover:font-semibold">Add to Plan(s)</span>
                                                <button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @else
                                <div>
                                    <p class="text-xs text-gray-700">{{$individualPerson->forename}} is included in the following plans:</p>
                                    <ul>
                                        @foreach($Curplan as $Cp)
                                        <li>
                                            <a class="text-xs hover:font-semibold" href="{{ route('plan.show', ['plan' => $Cp->id]) }}">{{$Cp->name}}</a>
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>
                                @endif
                            @endif
                        </div>
                        <div class="w-80 h-80"
                              x-show="pnopen"
                              style="display:none"
                              >
                            <h4 class="text-sm text-gray-700 font-medium">Notes</h4>
                            @if(empty($individualNotes[0]))
                            <div class="mt-1 h-full">
                                    <div x-data="{ nopen: false }">
                                        <div x-show="!nopen"
                                            class="">
                                            <p class="text-xs text-center text-gray-600"> No Notes</p>
                                            <div class="flex items-center justify-center">
                                                <button x-on:click="nopen = true">
                                                    <span class="text-xs text-center font-semibold">Add Notes</span>
                                                <button>
                                            </div>
                                        </div>
                                        <div x-show="nopen"
                                             class="w-full"
                                             style="display:none">

                                            <form wire:submit.prevent="addNotes({{ $individualPerson->id }})"> 
                                                @csrf
                                                    <div class="mt-1 flex flex-1">
                                                        <textarea rows="4" class="bg-gray-50 p-2 text-xs font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500" type="textbox" wire:model='newNotes'></textarea>
                                                    </div>
                                                <div class="mt-2 flex flex-1 gap-x-4">
                                                    <div>
                                                        <button type="button" x-on:click="nopen = false">
                                                            <span class="text-xs hover:font-semibold"> Cancel</span>
                                                        </button>
                                                    </div>
                                                    <div>
                                                        <button type="submit" @click="nopen = false">
                                                            <span class="text-xs hover:font-semibold">Add Note</span>
                                                        <button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                @else
                                <div>
                                    <div x-data="{ nopen: false }">
                                        @foreach($individualNotes as $note)
                                        <div class="mt-1">
                                            <h3 class="text-xs text-gray-700 font-semibold">{{$note->created_at}}</h3>
                                            <p class="text-xs font-light">{{$note->Notes}}</p>
                                        </div>
                                        @endforeach
                                        <div x-show="!nopen"
                                            class="">
                                            <div class="flex items-center justify-center">
                                                <button x-on:click="nopen = true">
                                                    <span class="text-xs text-center font-semibold">Add Notes</span>
                                                <button>
                                            </div>
                                        </div>
                                        <div x-show="nopen"
                                             class="w-full"
                                             style="display:none">

                                            <form wire:submit.prevent="addNotes({{ $individualPerson->id }})"> 
                                                @csrf
                                                    <div class="mt-1 flex flex-1">
                                                        <textarea rows="4" class="bg-gray-50 p-2 text-xs font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500" type="textbox" wire:model='newNotes'></textarea>
                                                    </div>
                                                <div class="mt-2 flex flex-1 gap-x-4">
                                                    <div>
                                                        <button type="button" x-on:click="nopen = false">
                                                            <span class="text-xs hover:font-semibold"> Cancel</span>
                                                        </button>
                                                    </div>
                                                    <div>
                                                        <button type="submit" @click="nopen = false">
                                                            <span class="text-xs hover:font-semibold">Add Note</span>
                                                        <button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif 
</div>
