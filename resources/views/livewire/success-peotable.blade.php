<div class="h-full overflow-hidden">
<!-- customHeight overflow-y-scroll -->
<!-- <?php
    var_dump(extension_loaded('curl'));
?> -->
    <div class="bg-white min-h-screen" x-data="{ external: true }">
        <div class="relative bg-white mainouter">
            <div class=" bg-white shadow-xl flex justify-between items-center px-4 p-3">
                
                <div class="flex gap-4 items-center">
                    {{--
                        <a href="{{ route('plan.show', ['plan' => $plan->id]) }}" class="flex gap-2 mainBlue items-center">
                            <img class="" src="{{ asset('images/ArrowLeftBlue.svg') }}">
                            <span>Back</span>
                        </a>
                    --}}
                    <h2 class="whitespace-nowrap text-black text-2xl font-semibold">Report</h2>
                </div>
                
                <div class="flex">
                    <div class="container">
                        <!--
                        <div class="switches-container">
                            <input x-on:click="external = false, internal = true" type="radio" id="switchInternal" name="switchPlan" value="Internal" />
                            <input x-on:click="internal = false, external = true" type="radio" id="switchExternal" name="switchPlan" value="External" checked="checked" />
                            <label for="switchInternal">Internal</label>
                            <label for="switchExternal">External</label>
                            <div class="switch-wrapper">
                                <div class="switch">
                                <div>Internal</div>
                                <div>External</div>
                                </div>
                            </div>
                        </div>
                        -->
                    </div>
                   @if($account->account_type!='Demo')         
                    <button
                        id="download"
                        class="bg-cyan-500 flex items-center w-32 space-x-1 border border-gray-300 rounded-lg py-2 hover:bg-cyan-600 font-medium  text-white text-md justify-center px-4"
                    >
                        <h1 class="text-md font-semibold">Download</h1>
                    </button>
                    @endif
                </div>
            </div>
            <div class="px-14 outerContainer overflow-y-scroll customHeight" id="webpageContent">

                {{-- ist section --}}
                <section class="istSection bg-white border relative mb-10 pdf-sections">
                    <img class="h-24 mt-20 ml-20 mb-10" src="{{ asset('images/LogoSmall.png') }}" alt="logo">
                    <div class="flex flex-col justify-center items-center absolute istdiv">
                        <div class="flex flex-col justify-center">
                            <h1 class="font-medium" style="font-size: 55px;">
                                <span>Succession Plan for </span>
                                <span class="mainBlue">{{$plan->name}}</span>
                            </h1>
                            <h3 class="font-normal text-3xl GrayText">SUMMARY REPORT</h3>
                            <h4 class="font-normal text-2xl GrayText text-uppercase">{{ date('F') }} {{ date('Y') }}</h4>
                        </div>
                    </div>
                </section>

                {{-- second section --}}
                @if($plan->description && trim($plan->description) != '')
                    <section class="w-full rounded-md shadow-xl js-slide-on-scroll mb-10 pdf-sections">
                        <!-- <img src="{{ asset('images/overview.png') }}" alt="Your Image" class="filter grayscale invert h-[65%]"> -->
                        <div class="overview relative">
                            <img class="h-24" src="{{ asset('images/LogoSmall.png') }}" alt="logo">
                        </div>
                        <div
                            class="flex px-20 flex-col  bg-mainBlue py-20 overviewPara justify-center items-center w-full p-14">
                            <h2 class="text-4xl font-semibold text-white">Overview</h2>
                            <p class="mt-1 text-lg text-white font-normal">{{ $plan->description }}</p>
                        </div>
                    </section>
                @endif

                {{-- THIRD SECTION --}}
                <section class="LongListed flex flex-col justify-center border h-max px-20 mb-10 pdf-sections">
                    <div class="flex justify-between">
                        <div>
                            <h1 class="font-semibold LongListedH1">
                                <span class="mainBlue">Long</span>
                                <span>Listed Candidates Breakdown</span>
                            </h1>
                            <h2 class="text-xl font-normal mt-5 ml-10 px-2 syan-bl-3">Out of 
                                <span class="text-cyan-500 text-2xl font-bold">{{ $pipelinePeople[0]->pipelinecount }}</span> candidates,
                                <span class="text-cyan-500 text-2xl font-bold">{{ $successPeople[0]->successcount }}</span> are shortlisted
                            </h2>
                        </div>
                        <img class="h-24 mt-20" src="{{ asset('images/LogoSmall.png') }}" alt="logo">
                    </div>
                    <div class="grid  px-10  LongListedIstDiv">
                        <div class="flex flex-col items-center border shadow-lg bg-white rounded-lg">
                            <h2 class="text-black font-medium text-xl text-center my-5">Potential Sex Diversity</h2>

                            @if($LLgenderData)
                                <div class="w-full h-full px-5" wire:ignore x-data="{
                                        cvalues: {{ json_encode($LLgenderData) }},
                                        clabels: {{ json_encode($LLgenderLabels) }},
                                        init() {
                                            let chart = new ApexCharts(this.$refs.genderchart, {
                                                chart: {
                                                    type: 'bar',
                                                    stacked: true
                                                },
                                                plotOptions: { 
                                                    bar: {
                                                        'distributed': false,
                                                        'borderRadius': 10,
                                                        'borderRadiusApplication': 'around',
                                                        'borderRadiusWhenStacked': 'last',
                                                        'hideZeroBarsWhenGrouped': false,
                                                        'isDumbbell': false,
                                                        'isFunnel': false,
                                                        'isFunnel3d': true,
                                                        'dataLabels': {
                                                            'position': 'top',
                                                            'total': {
                                                                'enabled': false,
                                                                'offsetX': 0,
                                                                'offsetY': 0,
                                                                'style': {
                                                                    'color': '#373d3f',
                                                                    'fontSize': '12px',
                                                                    'fontWeight': 600
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                tooltip: { enabled: true },
                                                grid: { show: false },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': false,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    show: false
                                                },
                                                fill: {
                                                    colors: ['#3B82F6']
                                                },
                                                'dataLabels': {
                                                    'position': 'top',
                                                    'formatter': function(val, opts) {
                                                        let label = opts.w.config.xaxis.categories[opts.dataPointIndex];
                                                        // Customize the data label text here
                                                        return label + ' (' + val + ')';
                                                    },
                                                    'offsetY': -25,
                                                    'style': {
                                                        'fontWeight': '600',
                                                        'fontSize': '14px',
                                                        'colors': [
                                                            '373d3f'
                                                        ]
                                                    }
                                                },
                                                series: [{
                                                    name: 'Gender',
                                                    data: this.cvalues,
                                                }],
                                            })
                                            chart.render()
                                        }
                                    }"
                                >
                                    <div x-ref="genderchart"></div>
                                </div>
                            @else
                                <p class="py-10 text-gray-500 mb-10">No Record(s) Found</p>
                            @endif
                        </div>

                        <div class="flex flex-col items-center border shadow-lg bg-white rounded-lg">
                            <h2 class="text-black font-medium text-xl text-center my-5">Location</h2>

                            @if($LLlocationData)
                                <div class="w-full h-full px-3" wire:ignore x-data="{
                                        cvalues: {{ json_encode($LLlocationData) }},
                                        clabels: {{ json_encode($LLlocationLabels) }},
                                        init() {
                                            let chart = new ApexCharts(this.$refs.locationchart, {
                                                chart: {
                                                    type: 'bar',
                                                    stacked: true
                                                },
                                                plotOptions: { 
                                                    bar: { 
                                                        horizontal: true,
                                                        'borderRadius': 10,
                                                        'borderRadiusApplication': 'around',
                                                        'borderRadiusWhenStacked': 'last',
                                                        'dataLabels': {
                                                            'position': 'top',
                                                            'total': {
                                                                'enabled': false,
                                                                'offsetX': 0,
                                                                'offsetY': 0,
                                                                'style': {
                                                                    'color': '#373d3f',
                                                                    'fontSize': '12px',
                                                                    'fontWeight': 500
                                                                }
                                                            }
                                                        }
                                                    } 
                                                },
                                                tooltip: { enabled: true },
                                                grid: { show: false },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': false,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                fill: {
                                                    colors: ['#3B82F6', '#FFA347', '#8B5CF6']
                                                },
                                                'dataLabels': {
                                                    'position': 'top',
                                                    'offsetX': 20,
                                                    'style': {
                                                        'fontWeight': '500',
                                                        'fontSize': '14px',
                                                        'colors': [
                                                            '373d3f'
                                                        ]
                                                    }
                                                },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': true,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    'labels': {
                                                        'show': true,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                series: [{
                                                    name: 'Locations',
                                                    data: this.cvalues,
                                                }],
                                            })
                                            chart.render()
                                        }
                                    }"
                                >
                                    <div x-ref="locationchart"></div>
                                </div>
                            @else
                                <p class="py-10 text-gray-500 mb-10">No Record(s) Found</p>
                            @endif
                        </div>
                    </div>
                </section>

                {{-- fourth section --}}
                <section class="ShortListed flex flex-col h-max border pdf-sections">
                    <div class="flex justify-between items-center ShortListedHeading">
                        <h1>
                            <span class="mainBlue">Short</span>
                            <span> Listed Candidates Breakdown</span>
                        </h1>
                        <img class="h-24" src="{{ asset('images/LogoSmall.png') }}" alt="logo">
                    </div>
                    
                    <div class="grid ShortListedIstDiv">
                        <div class="flex flex-col items-center justify-center border shadow-lg bg-white rounded-lg">
                            <h2 class="text-black font-medium text-xl text-center my-5">Potential Sex Diversity</h2>
                            @if($genderData)
                                <div class="w-full h-full px-5" wire:ignore x-data="{
                                        cvalues: {{ json_encode($genderData) }},
                                        clabels: {{ json_encode($genderLabels) }},
                                        init() {
                                            let chart = new ApexCharts(this.$refs.gender2chart, {
                                                chart: {
                                                    type: 'bar',
                                                    stacked: true
                                                },
                                                plotOptions: { 
                                                    bar: {
                                                        'distributed': false,
                                                        'borderRadius': 10,
                                                        'borderRadiusApplication': 'around',
                                                        'borderRadiusWhenStacked': 'last',
                                                        'hideZeroBarsWhenGrouped': false,
                                                        'isDumbbell': false,
                                                        'isFunnel': false,
                                                        'isFunnel3d': true,
                                                        'dataLabels': {
                                                            'position': 'top',
                                                            'total': {
                                                                'enabled': false,
                                                                'offsetX': 0,
                                                                'offsetY': 0,
                                                                'style': {
                                                                    'color': '#373d3f',
                                                                    'fontSize': '12px',
                                                                    'fontWeight': 600
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                tooltip: { enabled: true },
                                                grid: { show: false },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': false,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    show: false
                                                },
                                                fill: {
                                                    colors: ['#3B82F6']
                                                },
                                                'dataLabels': {
                                                    'position': 'top',
                                                    'formatter': function(val, opts) {
                                                        let label = opts.w.config.xaxis.categories[opts.dataPointIndex];
                                                        // Customize the data label text here
                                                        return label + ' (' + val + ')';
                                                    },
                                                    'offsetY': -25,
                                                    'style': {
                                                        'fontWeight': '600',
                                                        'fontSize': '14px',
                                                        'colors': [
                                                            '373d3f'
                                                        ]
                                                    }
                                                },
                                                series: [{
                                                    name: 'Gender',
                                                    data: this.cvalues,
                                                }],
                                            })
                                            chart.render()
                                        }
                                    }"
                                >
                                    <div x-ref="gender2chart"></div>
                                </div>
                            @else
                                <p class="py-10 text-gray-500 mb-10">No Record(s) Found</p>
                            @endif
                        </div>
                        
                        <div class="flex flex-col items-center border shadow-lg bg-white rounded-lg">
                            <h2 class="text-black font-medium text-xl text-center my-5">Candidates Type</h2>
                            
                            @if($typeData)
                                <div class="w-full h-full px-5 mb-5" wire:ignore 
                                    x-data="{
                                        tvalues: {{ json_encode($typeData) }},
                                        tlabels: {{ json_encode($typeLabels) }},
                                        init() {
                                            let chart = new ApexCharts(this.$refs.typechart, {
                                                chart: {
                                                    type: 'donut',
                                                    width: '100%',
                                                    height: '100%'
                                                },
                                                labels: this.tlabels,
                                                dataLabels: {
                                                    enabled: true,
                                                    textAnchor: 'start',
                                                    style: { fontSize: '9px' }
                                                },
                                                colors: ['#8B5CF6', '#F9C21A', '#9C27B0', '#0284c7'],
                                                series: this.tvalues,
                                                fill: { colors: ['#8B5CF6', '#F9C21A', '#9C27B0', '#0284c7'] },
                                                stroke: { width: 1 },
                                                legend: {
                                                    show: true,
                                                    position: 'right',
                                                    markers: { fillColors: ['#8B5CF6', '#F9C21A', '#9C27B0', '#0284c7'] },
                                                    
                                                },
                                                tooltip: { fillSeriesColor: true },
                                                plotOptions: {
                                                    pie: {
                                                        donut: {
                                                            size: '80%'
                                                        }
                                                    }
                                                },
                                                responsive: [{
                                                        breakpoint: 1024,
                                                        options: { chart: { width: '100%', position: 'center' } }
                                                    },
                                                    {
                                                        breakpoint: 768,
                                                        options: { chart: { width: '100%', height: '100%', position: 'center' } }
                                                    }
                                                ]
                                            })
                                            chart.render()
                                        }
                                    }">
                                    <div x-ref="typechart"></div>
                                </div>
                            @else
                                <p class="py-10 text-gray-500 mb-10">No Record(s) Found</p>
                            @endif
                        </div>
                    </div>
                </section>

                <section class="ShortListed flex flex-col h-max border mb-10 pdf-sections">
                    <div class="grid ShortListedIstDiv mt-10">
                        <div class="flex flex-col items-center border shadow-lg bg-white rounded-lg">
                            <h2 class="text-black font-medium text-xl text-center my-5">Companies They Are From</h2>
                            @if($LLFromCompanyData)
                                <div class="w-full h-full px-3" wire:ignore x-data="{
                                        cvalues: {{ json_encode($LLFromCompanyData) }},
                                        clabels: {{ json_encode($LLFromCompanyLabels) }},
                                        init() {
                                            let chart = new ApexCharts(this.$refs.companiesfromchart, {
                                                chart: {
                                                    type: 'bar',
                                                    stacked: true
                                                },
                                                plotOptions: { 
                                                    bar: { 
                                                        horizontal: true,
                                                        'borderRadius': 10,
                                                        'borderRadiusApplication': 'around',
                                                        'borderRadiusWhenStacked': 'last',
                                                        'dataLabels': {
                                                            'position': 'top',
                                                            'total': {
                                                                'enabled': false,
                                                                'offsetX': 0,
                                                                'offsetY': 0,
                                                                'style': {
                                                                    'color': '#373d3f',
                                                                    'fontSize': '12px',
                                                                    'fontWeight': 500
                                                                }
                                                            }
                                                        }
                                                    } 
                                                },
                                                tooltip: { enabled: true },
                                                grid: { show: false },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': false,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                fill: {
                                                    colors: ['#12B76A', '#FFA347', '#8B5CF6']
                                                },
                                                'dataLabels': {
                                                    'position': 'top',
                                                    'offsetX': 20,
                                                    'style': {
                                                        'fontWeight': '500',
                                                        'fontSize': '14px',
                                                        'colors': [
                                                            '373d3f'
                                                        ]
                                                    }
                                                },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': true,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    'labels': {
                                                        'show': true,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                series: [{
                                                    name: 'Peoples',
                                                    data: this.cvalues,
                                                }],
                                            })
                                            chart.render()
                                        }
                                    }"
                                >
                                    <div x-ref="companiesfromchart"></div>
                                </div>
                            @else
                                <p class="py-10 text-gray-500 mb-10">No Record(s) Found</p>
                            @endif
                        </div>
                    
                        <div class="flex flex-col items-center border shadow-lg bg-white rounded-lg">
                            <h2 class="text-black font-medium text-xl text-center my-5">Locations</h2>

                            @if($locationData)
                                <div class="w-full h-full px-3" wire:ignore x-data="{
                                        cvalues: {{ json_encode($locationData) }},
                                        clabels: {{ json_encode($locationLabels) }},
                                        init() {
                                            let chart = new ApexCharts(this.$refs.location2chart, {
                                                chart: {
                                                    type: 'bar',
                                                    stacked: true
                                                },
                                                plotOptions: { 
                                                    bar: { 
                                                        horizontal: true,
                                                        'borderRadius': 10,
                                                        'borderRadiusApplication': 'around',
                                                        'borderRadiusWhenStacked': 'last',
                                                        'dataLabels': {
                                                            'position': 'top',
                                                            'total': {
                                                                'enabled': false,
                                                                'offsetX': 0,
                                                                'offsetY': 0,
                                                                'style': {
                                                                    'color': '#373d3f',
                                                                    'fontSize': '12px',
                                                                    'fontWeight': 500
                                                                }
                                                            }
                                                        }
                                                    } 
                                                },
                                                tooltip: { enabled: true },
                                                grid: { show: false },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': false,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                fill: {
                                                    colors: ['#3B82F6', '#FFA347', '#8B5CF6']
                                                },
                                                'dataLabels': {
                                                    'position': 'top',
                                                    'offsetX': 20,
                                                    'style': {
                                                        'fontWeight': '500',
                                                        'fontSize': '14px',
                                                        'colors': [
                                                            '373d3f'
                                                        ]
                                                    }
                                                },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': true,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    'labels': {
                                                        'show': true,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                series: [{
                                                    name: 'Locations',
                                                    data: this.cvalues,
                                                }],
                                            })
                                            chart.render()
                                        }
                                    }"
                                >
                                    <div x-ref="location2chart"></div>
                                </div>
                            @else
                                <p class="py-10 text-gray-500 mb-10">No Record(s) Found</p>
                            @endif
                        </div>
                    </div>
                </section>
            

                {{-- fifth section --}}
                <div class="flex flex-col text-center border tableSection mb-10 pdf-sections">
                    <div class="flex justify-between items-center tableHeading">
                        <h1>
                            <span class="mainBlue">List</span>
                            <span>Of Candidates</span>
                        </h1>
                        <img class="h-24" src="{{ asset('images/LogoSmall.png') }}" alt="logo">
                    </div>
                    <div class="flex flex-col gap-y-10 max-w-full overflow-x-auto tableOuter js-show-on-scroll">
                        <div class="bg-white z-50 border">
                            <table class="min-w-full divide-y divide-gray-300 px-10">
                                <thead>
                                    <tr class="istTR">
                                        <th scope="col" rowspan="2" class="px-3 py-3.5  text-left text-sm">Name</th>
                                        <th scope="col" rowspan="2" class="px-3 py-3.5  text-left text-sm">Type</th>
                                        <th scope="col" rowspan="2" class="px-3 py-3.5  text-left text-sm">Role</th>
                                        <th scope="col" rowspan="2" class="px-3 py-3.5  text-left text-sm">Location</th>
                                        <th scope="col" rowspan="2" class="px-3 py-3.5  text-left text-sm">Gender</th>
                                        {{-- <th scope="col" colspan="6"
                                            class="px-3 py-3.5  text-left text-sm">
                                            Plan
                                            Match Score</th>
                                        <th class=""></th> --}}
                                        <th scope="col" class="py-3.5 px-3 text-left text-xs ">Skills</th>
                                        <th scope="col" class="py-3.5 px-3 text-left text-xs ">Roles</th>
                                        <th scope="col" class="py-3.5 px-3 text-left text-xs ">Location</th>
                                        <th scope="col" class="py-3.5 px-3 text-left text-xs ">Gender</th>
                                        <th scope="col" class="py-3.5 px-3 text-left text-xs ">Tenure</th>
                                        <th scope="col" class="px-3 py-3.5  text-left text-sm">Total</th>
                                        <th class=""></th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 bg-white ">
                                    @if($finalPeople->isNotEmpty())
                                        @foreach ($finalPeople as $index => $finalPerson)
                                            <tr class="BodyTR">
                                                <td class="whitespace-nowrap py-2 pl-4 text-left text-xs sm:pl-4 md:pl-4">
                                                    {{ $finalPerson->first_name }} {{ $finalPerson->last_name }}
                                                </td>
                                                <td class="whitespace-wrap text-left px-3 py-2 text-xs ">
                                                    {{ $finalPerson->type }}
                                                </td>
                                                <td class="whitespace-wrap text-left px-3 py-2 text-xs ">
                                                    {{ $finalPerson->latest_role }}
                                                </td>
                                                <td class="py-2 text-left text-xs  md:pl-4 sm:pl-4">
                                                    {{ $finalPerson->country }}
                                                </td>
                                                <td class="py-2 text-left text-xs  md:pl-4 sm:pl-4">
                                                    {{ $finalPerson->gender }}
                                                </td>
                                                <td class="py-2 text-left text-xs  md:pl-4 sm:pl-4">
                                                    {{ $finalPerson->match_data[0] }}
                                                </td>
                                                <td class="py-2 text-left text-xs  md:pl-4 sm:pl-4">
                                                    {{ $finalPerson->match_data[1] }}
                                                </td>
                                                <td class="py-2 text-left text-xs  md:pl-4 sm:pl-4">
                                                    {{ $finalPerson->match_data[2] }}
                                                </td>
                                                <td class="py-2 text-left text-xs  md:pl-4 sm:pl-4">
                                                    {{ $finalPerson->match_data[3] }}
                                                </td>
                                                <td class="py-2 text-left text-xs  md:pl-4 sm:pl-4">
                                                    {{ $finalPerson->match_data[4] }}
                                                </td>
                                                <td class="py-2 text-left text-xs  md:pl-4 sm:pl-4">
                                                    {{ $finalPerson->match_data[0] + $finalPerson->match_data[1] + $finalPerson->match_data[2] + $finalPerson->match_data[3] + $finalPerson->match_data[4] }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr class="text-center">
                                            <td colspan="10" class="py-10 text-gray-500">No Record(s) Found</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                {{-- sixth section --}}
                @if($finalPeople->isNotEmpty())
                    @foreach ($finalPeople as $index => $finalPerson)
                        <div class="border CandidateDetails mb-10 pdf-sections">
                            <div class="flex justify-between items-center candidateHeading px-12">
                                <h1 class="text-center">
                                    <span class="mainBlue">Candidate</span>
                                    <span>Details</span>
                                </h1>
                                <img class="h-24" src="{{ asset('images/LogoSmall.png') }}" alt="logo">
                            </div>
                            <div class="grid cards">
                                <div class="border shadow-lg  cardContainer pl-4  bg-white rounded-lg">
                                    <h2 class="istH2">Gender</h2>
                                    <span class="detail">{{ $finalPerson->gender ? $finalPerson->gender : 'N/A' }}</span>
                                </div>
                                <div class="border shadow-lg  cardContainer pl-4  bg-white rounded-lg">
                                    <h2 class="istH2">Registration Status</h2>
                                    <span class="detail">{{ $finalPerson->other_tags ? $finalPerson->other_tags : 'N/A' }}</span>
                                </div>
                                <div class="border shadow-lg cardContainer3 pl-4  bg-white rounded-lg">
                                    <h2 class="istH2">Role</h2>
                                    <span class="detail">{{ $finalPerson->latest_role ? $finalPerson->latest_role : 'N/A' }}</span>
                                </div>
                            </div>
                            <div class="pl-4 Summary shadow-lg mt-10 bg-white rounded-lg">
                                <h2 class="istH2">Candidate Summary</h2>
                                <p class="ParaH2">{{ $finalPerson->summary ? $finalPerson->summary : 'N/A' }}</p>
                            </div>
                        </div>

                        {{-- seventh section --}}
                        <section class="px-14 border CandidateDetails mb-10 pdf-sections">
                            <div class="flex justify-between items-center candidateHeading">
                                <h1 class="text-center">
                                    <span class="mainBlue">Candidate</span>
                                    <span>Details</span>
                                </h1>
                                <img class="h-24" src="{{ asset('images/LogoSmall.png') }}" alt="logo">
                            </div>
                            
                            <div class="career-history-box">
                                <h2>Career History</h2>
                                @if ($finalPerson->career_histories && isset($finalPerson->career_histories))
                                    <div class="timeline">
                                        @foreach ($finalPerson->career_histories as $careerHistory)
                                            <div class="timeline-item">
                                                <div class="timeline-dot"></div>
                                                <div class="timeline-content">
                                                    <h3 class="font-medium">
                                                        <label>{{$careerHistory->role}}</label>
                                                    </h3>
                                                    <p>{{$careerHistory->company_name}}</p>
                                                        <span class="text-xs font-base GrayText">{{ \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y') }} - {{ !empty($careerHistory->end_date) ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : "Present" }}</span>
                                                    </div>
                                            </div>
                                        @endforeach
                                        
                                    </div>
                                @else
                                    <div class="justify-center items-center py-5">
                                        <h1 style="color: rgb(156,163,175)">No recent history</h1>
                                    </div>
                                @endif
                            </div>
                            
                            <div>
                                <h2 class="text-2xl font-medium mt-10 text-gray-500 px-5">Plan Match Score</h2>
                                <div class="w-full h-full px-3" wire:ignore x-data="{
                                        cvalues: {{ json_encode($finalPerson->match_data) }},
                                        clabels: {{ json_encode($finalPerson->match_labels) }},
                                        init() {
                                            let chart = new ApexCharts(this.$refs.matchscore_{{$finalPerson->id}}, {
                                                chart: {
                                                    type: 'bar',
                                                    stacked: true,
                                                    height: 400
                                                },
                                                plotOptions: { 
                                                    bar: { 
                                                        horizontal: true,
                                                        'borderRadius': 10,
                                                        'borderRadiusApplication': 'around',
                                                        'borderRadiusWhenStacked': 'last',
                                                        'dataLabels': {
                                                            'position': 'top',
                                                            'total': {
                                                                'enabled': false,
                                                                'offsetX': 0,
                                                                'offsetY': 0,
                                                                'style': {
                                                                    'color': '#373d3f',
                                                                    'fontSize': '12px',
                                                                    'fontWeight': 500
                                                                }
                                                            }
                                                        }
                                                    } 
                                                },
                                                tooltip: { enabled: true },
                                                grid: { show: false },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': false,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                fill: {
                                                    colors: ['#3B82F6', '#FFA347', '#8B5CF6']
                                                },
                                                'dataLabels': {
                                                    'position': 'top',
                                                    'offsetX': 25,
                                                    'style': {
                                                        'fontWeight': '500',
                                                        'fontSize': '14px',
                                                        'colors': [
                                                            '373d3f'
                                                        ]
                                                    }
                                                },
                                                xaxis: {
                                                    categories: this.clabels,
                                                    'labels': {
                                                        'show': true,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                yaxis: {
                                                    'labels': {
                                                        'show': true,
                                                        'trim': true,
                                                        'style': {}
                                                    },
                                                    axisBorder: {
                                                        show: false
                                                    }
                                                },
                                                series: [{
                                                    name: 'Plan Match Score',
                                                    data: this.cvalues,
                                                }],
                                            })
                                            chart.render()
                                        }
                                    }"
                                >
                                    <div x-ref="matchscore_{{$finalPerson->id}}"></div>
                                </div>
                            </div>
                        </section>
                    @endforeach
                @endif
            </div>
        </div>
    </div>

    @include('livewire.loading')

</div>

<!-- Backup -->
<!-- <script>

    const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

    document.getElementById('download').addEventListener('click', () => {
        
        var loaderElement = document.getElementById("loader");
        if (loaderElement) {
            loaderElement.classList.add('enable-loading');
        }

        setTimeout(async () => {
            const elements = document.getElementsByClassName('pdf-sections');

            let previousElementHeight = 0;

            const { jsPDF } = window.jspdf;
            // const pdf = new jsPDF('l', 'mm', 'a4');
            let pdf = new jsPDF({
                orientation: 'landscape',
                unit: 'mm',
                compress: true
            });

            const elementsArray = Array.isArray(elements) ? elements : Array.from(elements);

            const processElements = async () => {
                for (const [index, element] of elementsArray.entries()) {

                    const canvas = await html2canvas(element, {
                        scale: 1.5, // 1.5
                        useCORS: true,
                        scrollX: 0,
                        scrollY: 0,
                        width: element.scrollWidth,
                        height: element.scrollHeight
                    });

                    const imgData = canvas.toDataURL('image/png', 0.7); // 0.7
                    const pageWidth = 297; // A4 width in mm (landscape)
                    const pageHeight = 210; // A4 height in mm (landscape)
                    const imgWidth = 297; // A4 width in mm
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;

                    if (index === 0) {
                        pdf = new jsPDF({
                            orientation: 'landscape',
                            unit: 'mm',
                            format: [imgWidth, imgHeight],
                            compress: true
                        });
                    } else {
                        pdf.addPage([imgWidth, imgHeight]);
                    }

                    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
                }

                pdf.save('plan-report.pdf');
                if (loaderElement) {
                    loaderElement.classList.remove('enable-loading');
                }
            };

            processElements();

        }, 100);
        
    });
</script> -->
<!-- Backup End -->

<script>

    // const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

    document.getElementById('download').addEventListener('click', () => {
        
        var loaderElement = document.getElementById("loader");
        if (loaderElement) {
            loaderElement.classList.add('enable-loading');
        }

        setTimeout(async () => {
            const elements = document.getElementsByClassName('pdf-sections');

            let previousElementHeight = 0;

            const { jsPDF } = window.jspdf;
            // const pdf = new jsPDF('l', 'mm', 'a4');
            let pdf = new jsPDF({
                orientation: 'landscape',
                unit: 'mm',
                compress: true
            });

            const elementsArray = Array.isArray(elements) ? elements : Array.from(elements);

            const processElements = async () => {
                const processElement = async (element, index) => {
                    const canvas = await html2canvas(element, {
                        scale: 1.5, // 1.5
                        useCORS: true,
                        scrollX: 0,
                        scrollY: 0,
                        width: element.scrollWidth,
                        height: element.scrollHeight
                    });

                    const imgData = canvas.toDataURL('image/jpeg', 0.7); // Adjust quality
                    const pageWidth = 297;
                    const pageHeight = 210;
                    const imgWidth = pageWidth;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;

                    if (index === 0) {
                        pdf = new jsPDF({
                            orientation: 'landscape',
                            unit: 'mm',
                            format: [imgWidth, imgHeight],
                            compress: true
                        });
                    } else {
                        pdf.addPage([imgWidth, imgHeight]);
                    }

                    pdf.addImage(imgData, 'JPEG', 0, 0, imgWidth, imgHeight);
                };

                for (let i = 0; i < elementsArray.length; i++) {
                    await processElement(elementsArray[i], i);
                }

                pdf.save('plan-report.pdf');
                if (loaderElement) {
                    loaderElement.classList.remove('enable-loading');
                }
            };

            processElements();

        }, 100);
        
    });
</script>