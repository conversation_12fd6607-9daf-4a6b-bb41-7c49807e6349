 <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
     @include('livewire.flashMessage')
     @if (session('csrf'))
        <div class="alert alert-success">
            {{ session('csrf') }}
        </div>
    @endif
     
     <div class="w-full flex">

         <div class="fixed inset-0 flex items-center justify-center  z-40">
             <!-- Modal background with a higher z-index -->
             <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

             <!-- Modal content with a lower z-index -->

             <div class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">

                 <div class="flex flex-col justify-center items-center">

                     <img class="w-20 h-20" src="{{ asset('images/LogoSmall.png') }}">
                     <h2 class="mt-4 text-center text-2xl font-bold text-gray-900">
                         Log In </h2>
                     <p class="text-sm mt-4 grayText">Welcome back to Successionplan! </br>
                         Please enter your details to log in.</p>
                 </div>

                 @if (Session::has('test_code'))
                 <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4" role="alert">
                     <p class="font-bold">Test Code (for debugging):</p>
                     <p>{{ Session::get('test_code') }}</p>
                 </div>
                 @endif

                 @if ($step == 1)
                 <form wire:submit.prevent="submit" class="mt-4 px-4 space-y-4">

                     <div>
                         <label for="email-address" class="text-xs font-medium labelcolor">Email address</label>
                         <input wire:model="email" id="email-address" name="email" type="email" class="mt-1 placeholder:text-gray-400 labelcolor bg-white  @error('email') is-invalid @enderror  placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Email address">
                         @error('email')
                         <span class="redColor text-sm">{{ $message }}</span>
                         @enderror
                     </div>
                     <div class="mt-4">
                         <label for="password" class=" text-xs font-medium labelcolor">Password</label>
                         <input wire:model="password" id="password" name="password" type="password" class="mt-1 placeholder:text-gray-400 labelcolor bg-white  @error('password') is-invalid @enderror placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border  border-gray-300 rounded-md" placeholder="Password">
                         @error('password')
                         <span class="redColor text-sm">{{ $message }}</span>
                         @enderror
                     </div>
                     <div class="flex justify-end mt-2 ">
                         <a href="{{route('forgot-password')}}" class="mainblue">Forget Password</a>
                     </div>

                     <div class="mt-2">
                         <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white mainblueBG  ">
                             Log in
                         </button>
                     </div>
                 </form>
                 @elseif ($step == 2)
                 <form wire:submit.prevent="submit" class="mt-4 space-y-4">
                     <div class="flex space-x-2 justify-center">
                         <input wire:model="code1" type="text" maxlength="1" required pattern="[0-9]" class="inputArea text-center border @error('code1') is-invalid @enderror border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                         <input wire:model="code2" type="text" maxlength="1" required pattern="[0-9]" class="inputArea text-center border @error('code2') is-invalid @enderror border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                         <input wire:model="code3" type="text" maxlength="1" required pattern="[0-9]" class="inputArea text-center border @error('code3') is-invalid @enderror border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                         <input wire:model="code4" type="text" maxlength="1" required pattern="[0-9]" class="inputArea text-center border @error('code4') is-invalid @enderror border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                     </div>
                     @error('code')
                     <span class="text-red-500 flex justify-center mt-2 text-sm text-center">{{ $message }}</span>
                     @enderror
                     <div class="flex justify-center  mt-3">
                         <span class="text-xs font-normal text-center">Did not receive a code?
                             <a href="javascript:void(0);" wire:click="resend()"><span class=" mainblue"> Resend</span></a></span>

                     </div>

                     <div class="mt-4 px-4">
                         <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white  mainblueBG">
                             Confirm
                         </button>
                     </div>
                     <div wire:click="backToLogin()" class="my-5 flex justify-center">
                         <button type="button" class="text-sm text-center mainblue">Back to Log In</button>
                     </div>
                 </form>
                 @endif
             </div>
         </div>
     </div>
     @include('livewire.loading')
 </div>

 <script>
     document.addEventListener('DOMContentLoaded', function() {


         Livewire.on('addEventListenerOnOtp', () => {
             setTimeout(function() {

                 const inputs = document.querySelectorAll('.inputArea');

                 inputs.forEach((input, index) => {
                     input.addEventListener('input', () => {
                         if (input.value.length === 1) {
                             const nextInput = inputs[index + 1];
                             if (nextInput) {
                                 nextInput.focus();
                             }
                         }
                     });

                     input.addEventListener('keydown', (e) => {
                         if (e.key === 'Backspace' && input.value.length === 0) {
                             const previousInput = inputs[index - 1];
                             if (previousInput) {
                                 previousInput.focus();
                             }
                         }
                     });
                 });
             }, 500);
         });

     });
 </script>