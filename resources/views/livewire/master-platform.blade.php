<div class="">
    <div class="flex items-center text-white bg-sky-900 px-10 py-4">
        <h1 class="whitespace-nowrap px-4 text-white text-3xl font-semibold">Master</h1>
    </div>

    <div class=" bg-white border border-gray-300 rounded-3xl p-4 masterContainer">
        <div class="flex items-center">
            <div>
                <h3 class="text-black font-bold text-xl">Account</h3>
            </div>
            <div class="flex flex-1 justify-end">
                <div class="flex" x-data="{ nopen: @entangle('nopen') }">
                    <!-- Trigger button inside the modal -->
                    <div class="flex items-center justify-end">
                        <div>
                            <button x-on:click="nopen = true" wire:click=""
                                class="bg-cyan-500 flex items-center w-32 space-x-1 border border-gray-300 rounded-lg py-2 hover:bg-cyan-600 font-medium  text-white text-md justify-center">Add
                                Account</button>
                        </div>
                    </div>
                    <!-- Modal container -->
                    <div x-show="nopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50"
                        style="display:none">
                        <!-- Modal background with a higher z-index -->
                        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                        <!-- Modal content with a lower z-index -->
                        <div class="modal-content bg-white shadow-md rounded-2xl p-4 z-50 w-96">
                            <!-- Modal content -->
                            <div class="">
                                <div class="">
                                    <h3 class="text-base font-semibold">Add Account</h3>
                                    <div class="mt-2">
                                        <label for="team" class="block text-xs font-medium labelcolor">Account
                                            Name</label>
                                        <div class="flex rounded-md mt-1 shadow-sm sm:max-w-md border border-gray-300 ">
                                            <input type="text" wire:model="account_name" name="account_name"
                                                id="account_type"
                                                class="block flex-1 border-0 bg-transparent py-1.5 pl-1 outline-none text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                                                placeholder="The Account Type">
                                        </div>
                                        @error('account_name')
                                            <span class="text-red-500 text-md text-center">
                                                {{ $message }}
                                            </span>
                                        @enderror
                                    </div>
                                    <div class="mt-2">
                                        <label for="team" class="block text-xs font-medium labelcolor">Account
                                            Type</label>
                                        {{-- <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                                            <input type="text" wire:model="accountType" id="account_type" class="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" placeholder="The Account Type">
                                            </div> --}}
                                        <select name="" wire:model="accountType" id="account_type"
                                            class="w-full cursor-pointer bg-white p-2 mt-1 border rounded-md text-gray-400 shadow-sm outline-none border-gray-300 ">
                                            <option value="">Select Type</option>
                                            <option value="gold" class="text-gray-400">Gold</option>
                                            <option value="silver">Silver</option>
                                            <option value="platinum">Platinum</option>
                                        </select>
                                        @error('accountType')
                                            <span class="text-red-500 text-md text-center">
                                                {{ $message }}
                                            </span>
                                        @enderror
                                    </div>
                                    <div class="mt-2">
                                        <label for="team" class="block text-xs font-medium labelcolor">Company
                                            Name</label>
                                        <p class="GrayText text-xs text-left">Please make sure there are no suggested
                                            names from the system</p>
                                        <div class="text-xs mt-1" wire:ignore x-data="{
                                            multiple: false,
                                        
                                            value: @entangle('companyName'),
                                            options: {{ json_encode($companies) }},
                                        
                                            init() {
                                                this.$nextTick(() => {
                                                    let choices = new Choices(this.$refs.select, {
                                                        allowHTML: true,
                                                        addItems: true,
                                                    })
                                        
                                                    let refreshChoices = () => {
                                                        let selection = this.multiple ? this.value : [this.value]
                                        
                                                        choices.clearStore()
                                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                                            value,
                                                            label,
                                                            selected: selection.includes(value),
                                                        })))
                                                    }
                                        
                                                    refreshChoices()
                                        
                                                    this.$refs.select.addEventListener('change', () => {
                                                        this.value = choices.getValue(true)
                                                    })
                                        
                                                    this.$watch('value', () => refreshChoices())
                                                    this.$watch('options', () => refreshChoices())
                                                })
                                            }
                                        }"
                                            class="bg-white text-xs max-w-sm w-full">
                                            <select x-ref="select" :multiple="multiple"></select>
                                        </div>
                                        @error('companyName')
                                            <span class="text-red-500 text-md text-center">
                                                {{ $message }}
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="mt-2">
                                        <label for="team" class="block text-xs font-medium labelcolor">Relationship
                                            Manager</label>
                                        <div class="text-xs mt-1" wire:ignore x-data="{
                                            multiple: false,
                                            value: @entangle('managerRelation'),
                                            options: {{ json_encode($managers) }},
                                            init() {
                                                this.$nextTick(() => {
                                                    let choices = new Choices(this.$refs.select, {
                                                        allowHTML: true,
                                                        addItems: true,
                                                        shouldSort: false, // Ensure the order remains as per the options array
                                                    });
                                        
                                                    let refreshChoices = () => {
                                                        let selection = this.multiple ? this.value : [this.value];
                                        
                                                        choices.clearStore();
                                                        choices.setChoices(this.options.map(option => ({
                                                            value: option.value,
                                                            label: option.label,
                                                            selected: selection.includes(option.value),
                                                        })), 'value', 'label', true);
                                                    };
                                        
                                                    refreshChoices();
                                        
                                                    this.$refs.select.addEventListener('change', () => {
                                                        this.value = choices.getValue(true);
                                                    });
                                        
                                                    this.$watch('value', () => refreshChoices());
                                                    this.$watch('options', () => refreshChoices());
                                                });
                                            }
                                        }"
                                            class="bg-white text-xs max-w-sm w-full">
                                            <select x-ref="select" :multiple="multiple"></select>
                                        </div>
                                        @error('managerRelation')
                                            <span class="text-red-500 text-md text-center">{{ $message }}</span>
                                        @enderror
                                    </div>


                                </div>
                                <div class="mt-2">
                                    <label for="name" class="block text-xs font-medium labelcolor">Name</label>
                                    <div class="flex rounded-md shadow-sm  sm:max-w-md mt-1  border border-gray-300">
                                        <input type="text" wire:model="name" id="name"
                                            class="block flex-1 border-0 bg-transparent py-1.5 pl-1 outline-none text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                                            placeholder="Full Name" required>
                                    </div>
                                    @error('name')
                                        <span class="text-red-500 text-md text-center">
                                            {{ $message }}
                                        </span>
                                    @enderror
                                </div>
                                <div class="mt-2">
                                    <label for="email" class="block text-xs font-medium labelcolor">Email</label>
                                    <div class="flex rounded-md shadow-sm  sm:max-w-md mt-1  border border-gray-300">
                                        <input type="email" wire:model="email" id="email"
                                            class="block flex-1 border-0 bg-transparent py-1.5 pl-1 outline-none text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                                            placeholder="Your Email" required>
                                    </div>
                                    @error('email')
                                        <span class="text-red-500 text-md text-center">
                                            {{ $message }}
                                        </span>
                                    @enderror
                                </div>
                                <div class="flex gap-2 w-full mt-4 ">

                                    <button x-on:click="nopen =false"
                                        class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>

                                    <button wire:click="addAccount"
                                        class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">Send</button>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div class="relative py-2">
                <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                    <div class="w-full border-gray-200"></div>
                </div>
            </div>
            <div class=" h-full MasterTableOuter">
                @if ($Saccounts->isEmpty())
                    <div class="h-80 flex flex-1 items-center justify-center">
                        <div class="">
                            <div class="flex flex-1 items-center justify-center">
                                <img class="h-20 w-auto" src="{{ asset('images/SAM.png') }}">
                            </div>
                            <h4 class="text-xs text-center text-black font-semibold">No Accounts</h4>
                            <p class="text-xs font-light text-gray-600 text-center">Set up accounts to get started</p>
                        </div>
                    </div>
                @else
                    <div class="table_wrapper masterTable border bg-white border-gray-200 rounded-2xl">
                        <table class="w-full tableEdges">
                            <thead>
                                <tr class="grayBackground">
                                    <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                        Account</th>
                                    <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                        Type
                                    </th>
                                    <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                        Active
                                        Users</th>
                                    <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                        Company</th>
                                    <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                        Relationship Manager Name</th>
                                    <th scope="col" class="py-3.5 px-3 text-left text-sm border-b font-semibold">
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white overflow-y-auto">
                                @foreach ($Saccounts as $Sac)
                                    <tr>
                                        <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                            {{ $Sac->Account_name }}</td>
                                        <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                            {{ $Sac->account_type }}</td>
                                        <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                            {{ $Sac->active_users }}</td>
                                        <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                            {{ $Sac->company->name }}
                                        </td>
                                        <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                            {{ ($Sac->relationManager) ? ($Sac->relationManager->name): 'Not Provided' }}</td>
                                        <td class="whitespace-wrap px-3 py-3 text-sm font-normal">
                                            {{-- <div class="flex">
                                                    <div class="flex" x-data="{ uopen: false }">
                                                        <!-- Trigger button inside the modal -->
                                                        <div class="flex items-center justify-end">
                                                            <div
                                                                class="border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-110">
                                                                <button x-on:click="uopen = true" wire:click=""
                                                                    class="transition ease-in-out delay-100 border border-sky-300 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-sky-300 hover:text-white duration-100">Update</button>
                                                            </div>
                                                        </div>
                                                        <!-- Modal container -->
                                                        <div x-show="uopen"
                                                            class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50"
                                                            style="display:none">
                                                            <!-- Modal background with a higher z-index -->
                                                            <div
                                                                class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40">
                                                            </div>
                                                            <!-- Modal content with a lower z-index -->
                                                            <div
                                                                class="modal-content bg-white shadow-md rounded-2xl p-4 z-50 w-96">
                                                                <!-- Modal content -->
                                                                <div class="flex flex-1 justify-center items-center">
                                                                    <div class="">
                                                                        <h3 class="text-base font-semibold">Update
                                                                            Account</h3>
                                                                        <div class="mt-4">
                                                                            <label
                                                                                class="w-1/6 h-full text-sm font-medium text-black">Account
                                                                                Type</label>
                                                                            <input type="text"
                                                                                wire:model="updateAccounttype"
                                                                                class="mt-2 bg-gray-50 p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                                                placeholder="Enter new type here">
                                                                        </div>
                                                                        <div class="mt-4">
                                                                            <label for="name"
                                                                                class="block text-sm font-medium leading-6 text-gray-900">Add
                                                                                Relationship Manager</label>
                                                                            <div class="text-xs" wire:ignore
                                                                                x-data="{
                                                                                    multiple: false,
                                                                                    value: @entangle('selectedManager'),
                                                                                    options: {{ json_encode($managers) }},
                                                                                    init() {
                                                                                        this.$nextTick(() => {
                                                                                            let choices = new Choices(this.$refs.select)
                                                                                
                                                                                            let refreshChoices = () => {
                                                                                                let selection = this.multiple ? this.value : [this.value]
                                                                                
                                                                                                choices.clearStore()
                                                                                                choices.setChoices(this.options.map(({ value, label }) => ({
                                                                                                    value,
                                                                                                    label,
                                                                                                    selected: selection.includes(value),
                                                                                                })))
                                                                                            }
                                                                                
                                                                                            refreshChoices()
                                                                                
                                                                                            this.$refs.select.addEventListener('change', () => {
                                                                                                this.value = choices.getValue(true)
                                                                                            })
                                                                                
                                                                                            this.$watch('value', () => refreshChoices())
                                                                                            this.$watch('options', () => refreshChoices())
                                                                                        })
                                                                                    }
                                                                                }"
                                                                                class="bg-white text-xs max-w-sm w-full">
                                                                                <select x-ref="select"
                                                                                    :multiple="multiple"></select>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div
                                                                    class="mt-3 flex items-center justify-center gap-x-6">
                                                                    <div
                                                                        class=" w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                                        <button x-on:click="uopen =false"
                                                                            class="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                                                    </div>
                                                                    <div
                                                                        class="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                                        <button x-on:click="uopen =false"
                                                                            wire:click="updateAccount({{ $Sac->id }})"
                                                                            class="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Update</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div> --}}
                                            <div class="dropdown-container relative">
                                                <button tabindex="1" id="dropdownDefaultButton"
                                                    data-dropdown-toggle="dropdown"
                                                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                    type="button">
                                                    <img class="h-5 w-5"
                                                        src="{{ asset('images/DotsThreeVertival.svg') }}">
                                                </button>

                                                <div id="dropdown"
                                                    class="dropdown-menu hidden z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 ">
                                                    <ul class="py-2 text-sm text-gray-700 dark:text-gray-200"
                                                        aria-labelledby="dropdownDefaultButton">

                                                        <li class="font-semibold text-sm p-2 cursor-pointer">Update</li>
                                                        <li class="font-semibold text-sm p-2 cursor-pointer">Delete</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
            @include('livewire.loading')

        </div>
    </div>
    @include('livewire.loading')
</div>
