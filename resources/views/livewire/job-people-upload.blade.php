<div class="customHeight overflow-y-scroll">
    {{-- Header --}}
    <div class="flex justify-between items-center px-8 py-4 border-b shadow-md bg-white">
        <div class="flex gap-2 items-center">
            <a href="{{ route('job.show', ['job' => $job->id]) }}" class="flex gap-2 text-cyan-500 items-center">
                <img src="{{ asset('/images/ArrowLeftBlue.svg') }}" alt="Back">
                <span>Back</span>
            </a>
            <h1 class="text-3xl font-semibold text-gray-900">Upload</h1>
        </div>
    </div>

    <div class="flex flex-wrap gap-6 lg:gap-12 p-6" x-data="uploadComponent()">
        <!-- Upload File Section -->
        <div class="flex-1 divide-gray-200 border border-gray-200 rounded-2xl bg-white shadow-md p-6">
            <div>
                <label for="External-candidate" class="block text-lg font-semibold text-gray-900 mb-4">
                    Upload File
                </label>
                <div class="h-52 flex justify-center items-center rounded-lg border border-dashed border-gray-300 p-6">
                    <div class="text-center">
                        <div class="flex items-center justify-center">
                            <img class="h-10 w-10" src="{{ asset('images/upload.png') }}" alt="Upload">
                        </div>
                        <div class="mt-4 text-md text-gray-600">
                            <label for="file-upload" class="relative cursor-pointer font-semibold text-cyan-500 hover:text-sky-500">
                                <span>Upload a file</span>
                                <input id="file-upload" wire:model="csvFile" type="file" name="file-upload" class="sr-only" accept=".csv, .xlsx, .xls">
                            </label>
                            <p class="text-sm">(CSV or Excel)</p>
                            @if ($csvFile && $csvFile->getClientOriginalName())
                            <p class="text-sm mt-2">
                                <span class="font-semibold">Selected File:</span>
                                {{ $csvFile->getClientOriginalName() }}
                            </p>
                            @endif
                        </div>
                        @error('csvFile') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>
                <br>

                <div class="h-48 flex justify-center items-center rounded-lg border border-dashed border-gray-300 p-6">
                    <div class="text-center">
                        <div class="flex items-center justify-center">
                            <img class="h-10 w-10" src="{{ asset('images/upload.png') }}" alt="Upload">
                        </div>
                        <div class="mt-4 text-md text-gray-600">
                           
                        <div class="text-center">
                                <div class="mt-4 flex text-sm leading-6 text-gray-600">
                                    <label for="file-upload"
                                        class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 hover:text-cyan-600">
                                        <button type = "button" wire:click="downloadCSV">Download Template File</button>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    x-init="init()"
                    >
                    <button
                        x-on:click="startProcessing"
                        wire:click="uploadFile"
                        type="button"
                        class="mt-4 w-full py-2 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600"
                        x-text="isProcessing ? 'Processing the file, Please do not close the browser...' : 'Upload'"
                        x-bind:disabled="isProcessing"></button>
                </div>

            </div>
        </div>

        <!-- Search People Section -->
        <div class="flex-1 divide-gray-200 border border-gray-200 rounded-2xl bg-white shadow-md p-6">
            <div>
                <h2 class="text-lg font-semibold text-gray-900 mb-4">
                    Search People
                </h2>
                <!-- Company Name Field -->
                <div class="mb-4" wire:ignore>
                    <label for="company" class="block text-sm font-semibold text-gray-900 mb-1">
                        Company Name
                    </label>
                    <input type="text"
                        class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                        placeholder="Enter company name" x-model="newCompany"
                        @keydown.enter.prevent="if(newCompany !== '') { @this.searchData.companies.push(newCompany); newCompany = ''; }"
                        @blur="if(newCompany !== '') { @this.searchData.companies.push(newCompany); newCompany = ''; }">

                        <div class="mt-2 flex flex-wrap">
                            <template x-for="(skill, index) in @this.searchData.companies"
                                :key="index">
                                <span
                                    class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                    <span x-text="skill"></span>
                                    <button type="button" class="ml-1"
                                        @click="@this.searchData.companies.splice(index, 1)">&times;</button>
                                </span>
                            </template>
                        </div>
                    @error('company')
                    <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>

                <div class="mb-4">
                    <label for="role" class="block text-sm font-semibold text-gray-900 mb-1">
                        Forename
                    </label>
                    <input type="text"
                        class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                        placeholder="Enter forename" x-model="newForename"
                        @keydown.enter.prevent="if(newForename !== '') { @this.searchData.forenames.push(newForename); newForename = ''; }"
                        @blur="if(newForename !== '') { @this.searchData.forenames.push(newForename); newForename = ''; }">

                        <div class="mt-2 flex flex-wrap">
                            <template x-for="(forename, index) in @this.searchData.forenames"
                                :key="index">
                                <span
                                    class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                    <span x-text="forename"></span>
                                    <button type="button" class="ml-1"
                                        @click="@this.searchData.forenames.splice(index, 1)">&times;</button>
                                </span>
                            </template>
                        </div>
                </div>

                 <!-- Surname Field -->
                 <div class="mb-4">
                    <label for="role" class="block text-sm font-semibold text-gray-900 mb-1">
                        Surname
                    </label>
                    <input type="text"
                        class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                        placeholder="Enter surname" x-model="newSurname"
                        @keydown.enter.prevent="if(newSurname !== '') { @this.searchData.surnames.push(newSurname); newSurname = ''; }"
                        @blur="if(newSurname !== '') { @this.searchData.surnames.push(newSurname); newSurname = ''; }">

                        <div class="mt-2 flex flex-wrap">
                            <template x-for="(forename, index) in @this.searchData.surnames"
                                :key="index">
                                <span
                                    class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                    <span x-text="forename"></span>
                                    <button type="button" class="ml-1"
                                        @click="@this.searchData.surnames.splice(index, 1)">&times;</button>
                                </span>
                            </template>
                        </div>
                </div>  

                <!-- Role Field -->
                <div class="mb-4">
                    <label for="role" class="block text-sm font-semibold text-gray-900 mb-1">
                        Role
                    </label>
                    <input type="text"
                        class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                        placeholder="Enter role" x-model="newRole"
                        @keydown.enter.prevent="if(newRole !== '') { @this.searchData.roles.push(newRole); newRole = ''; }"
                        @blur="if(newRole !== '') { @this.searchData.roles.push(newRole); newRole = ''; }">

                        <div class="mt-2 flex flex-wrap">
                            <template x-for="(skill, index) in @this.searchData.roles"
                                :key="index">
                                <span
                                    class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                    <span x-text="skill"></span>
                                    <button type="button" class="ml-1"
                                        @click="@this.searchData.roles.splice(index, 1)">&times;</button>
                                </span>
                            </template>
                        </div>
                    @error('role')
                    <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Country Field -->
                <div class="mb-4">
                    <label for="country" class="block text-sm font-semibold text-gray-900 mb-1">
                        Country
                    </label>
                    <div wire:ignore x-data="{
                    value: @entangle('country'),
                    options: {{ json_encode($countries) }},
                    choicesInstance: null, // Variable to hold Choices instance
                    init() {
                        this.$nextTick(() => {
                            this.choicesInstance = new Choices(this.$refs.selectLocation, {
                                allowHTML: true,
                                placeholder: true,
                                placeholderValue: 'Select country',
                                searchEnabled: true,
                                classNames: {
                                    containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'], // Split into an array
                                }
                            });

                            let refreshChoices = () => {
                                this.choicesInstance.clearStore();
                                this.choicesInstance.setChoices(this.options.map(({ value, label }) => ({
                                    value,
                                    label,
                                    selected: this.value === value,
                                })));
                            };

                            refreshChoices();

                            this.$refs.selectLocation.addEventListener('change', () => {
                                this.value = this.choicesInstance.getValue(true);
                            });

                            this.$watch('value', () => refreshChoices());
                            this.$watch('options', () => refreshChoices());

                            // Save Choices instance to window global variable
                            window.myChoicesInstance = this.choicesInstance;
                        });
                    }
                }" class="text-black w-full">
                        <select x-ref="selectLocation" class="my-select-class" ></select>
                    </div>
                </div>

                <!-- Search Button -->
                <button
                    @click="search()"
                    :class="isProcessing ? 'bg-gray-500 cursor-not-allowed' : 'bg-cyan-500 hover:bg-cyan-600'"
                    :disabled="isProcessing"
                    type="button"
                    class="w-full py-2 text-white rounded-lg">
                    Search
                </button>
            </div>
        </div>
    </div>

    @if($showPeopleList)
    <div class="p-6" x-data="checkboxHandler()">
        <div class="bg-white divide-gray-200 border border-gray-200 rounded-2xl bg-white shadow-md px-6 mb-4">

            <div class="flex justify-between mt-4 mb-4">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">People List</h2>
                @if(!empty($peoplesList))
                <button @click="addPeoples()"
                    type="button"
                    class="py-2 px-3 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600">
                    Add People
                </button>
                @endif
            </div>
            <div class="overflow-auto max-h-96 border border-gray-200 mb-5" @scroll="handleScroll">

                <table class="min-w-full border-collapse border border-gray-200 text-left text-sm">
                    <thead class="bg-gray-50">
                        <tr>
                            <!-- Checkbox Header -->
                            <th class="px-4 py-3 border border-gray-200 font-medium text-gray-900">
                                <input
                                    type="checkbox"
                                    class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out"
                                    x-model="selectAll" id="selectAll">
                            </th>
                            <th class="px-6 py-3 border border-gray-200 font-medium text-gray-900">Name</th>
                            <th class="px-6 py-3 border border-gray-200 font-medium text-gray-900">Company</th>
                            <th class="px-6 py-3 border border-gray-200 font-medium text-gray-900">Role</th>
                            <th class="px-6 py-3 border border-gray-200 font-medium text-gray-900">LinkedIn URL</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Rows -->
                        @if(!empty($peoplesList))
                        @foreach (($peoplesList ?? []) as $index => $people)
                        <tr>
                            <td class="px-4 py-3 border border-gray-200">
                                <input
                                    type="checkbox"
                                    class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out"
                                    x-model="selected"
                                    x-bind:value="'{{ $people['linkedin_profile_url'] }}'"
                                    @change="toggleSelection($event)">
                            </td>
                            <td class="px-6 py-3 border border-gray-200">{{ $people['profile']['full_name'] ?? null }}</td>
                            <td class="px-6 py-3 border border-gray-200">{{ $people['profile']['experiences'][0]['company'] ?? null }}</td>
                            <td class="px-6 py-3 border border-gray-200">{{ $people['profile']['experiences'][0]['title'] ?? null }}</td>
                            <td class="px-6 py-3 border border-gray-200">
                                <a href="{{ $people['linkedin_profile_url'] }}" style="color: #06b6d4;" target="_blank">{{ $people['linkedin_profile_url'] }}</a>
                            </td>
                        </tr>
                        @endforeach
                        @else
                        <tr>
                            <td colspan="4" class="px-6 py-3 border border-gray-200 text-center">No result found!</td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>

        </div>
    </div>

    @endif



    @include('livewire.loading')



</div>

@push('script')

<script>
    function checkboxHandler() {
        return {
            selectAll: false,
            selected: [],
            totalItems: 0,
            isLoading: false,
            nextPageUrl: @entangle('nextPageUrl'),

            init() {
                Livewire.on('updateSelectedProp', () => {
                    this.selected = [];
                    this.selectAll = false;
                });
                // Set the total number of checkboxes
                this.totalItems = document.querySelectorAll('tbody input[type="checkbox"]').length;

                // Watch for changes in `selectAll` and update `selected` without triggering feedback
                this.$watch('selectAll', (value) => {
                    if (value) {
                        this.selected = Array.from(
                            document.querySelectorAll('tbody input[type="checkbox"]')
                        ).map((checkbox) => checkbox.value);
                    } else if (this.selected.length !== 0) {
                        this.selected = []; // Avoid setting `selected` again if already empty
                    }
                    console.log(this.selected);
                });
            },
            handleScroll(event) {
                const element = event.target;

                // Check if scrolling is near the bottom, a request is not already loading, and nextPageUrl is not empty
                if (
                    element.scrollTop + element.clientHeight + 2 >= element.scrollHeight &&
                    !this.isLoading &&
                    this.nextPageUrl // Ensure nextPageUrl is not null or empty
                ) {
                    this.isLoading = true; // Set the loading flag

                    // Dispatch the Livewire event to load more data
                    Livewire.dispatch('loadMorePeople');

                    // Reset the flag after a delay
                    setTimeout(() => {
                        this.isLoading = false;
                    }, 500);
                }
            },
            toggleSelection(event) {
                const value = event.target.value;

                if (event.target.checked) {
                    if (!this.selected.includes(value)) {
                        this.selected.push(value);
                    }
                } else {
                    this.selected = this.selected.filter((item) => item !== value);
                    if (this.selected.length == 0) {
                        this.selectAll = false;
                    }

                }


                // Update `selectAll` only if needed
                //this.selectAll = this.selected.length === this.totalItems;
            },
            addPeoples() {
                // if (this.selected.length <= 0) {
                //     alert("Please select peoples");
                //     return;
                // }
                Livewire.dispatch('addPeoples', {
                    selectedData: this.selected
                });
            }
        };
    }

    document.getElementById('file-upload').addEventListener('change', function() {
        // Show the loading spinner
        document.getElementById('loading-spinner').classList.remove('hidden');

    });
</script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('uploadComponent', () => ({
            isProcessing: false,
            newCompany: '',
            newRole: '',
            newForename: '',
            newSurname: '',

            init() {
                Livewire.on('uploadComplete', () => {
                    console.log('Processing complete.');
                    document.getElementById('loading-spinner').classList.add('hidden');
                    this.isProcessing = false;
                });
            },
            startProcessing() {
                this.isProcessing = true;
            },
            search() {
                  // Validate that at least one company or role is entered
                    const hasCompany = Array.isArray(@this.searchData.companies) && @this.searchData.companies.length > 0;
                    const hasRole = Array.isArray(@this.searchData.roles) && @this.searchData.roles.length > 0;

                    if (!hasCompany && !hasRole) {
                        toastr.info('Please add at least one company or one role.');
                        return;
                    }
                Livewire.dispatch('searchPeople');
            }
        }));
    });
</script>
@endpush