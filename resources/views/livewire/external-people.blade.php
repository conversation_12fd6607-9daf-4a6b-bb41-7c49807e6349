<div class="customHeight overflow-y-scroll">

    {{-- header --}}
    <div class="flex justify-between items-center px-10 py-4 border-b shadow-md bg-white">
        <div class="flex gap-2">
            <a href="{{ route('plan.show', ['plan' => $plan]) }}" class="flex gap-2 mainBlue items-center">
                <img class="" src="{{ asset('/images/ArrowLeftBlue.svg') }}">
                <span>Back</span>
            </a>
            <h1 class="whitespace-nowrap px-4 text-black text-3xl font-semibold">People Dashboard</h1>
        </div>
        <div class="flex gap-2">

            <!-- Bulk upload Button -->
            {{-- <div class="" x-data="{ bopen: false, closeUploadCandidatesModal() { this.bopen = false; this.$dispatch('uploadCandidatesModalClosed');} }"> --}}
            <div class="" x-data="{
                bopen: @entangle('bopen'),
            
                duplicateUploadPopup: @entangle('duplicateUploadPopup')
                }">
                <!-- Trigger button inside the modal -->
                <div class="w-32 border rounded-lg p-px shadow-md hover:shadow-lg hover:scale-105">
                <a href="{{ route('plan.upload.index', ['plan' => $plan]) }}"><button 
                        class="w-full flex justify-center items-center gap-2 font-semibold transition ease-in-out delay-100 text-md text-black rounded-lg py-2 hover:bg-cyan-500 hover:text-white duration-100">
                        
                        <span>Search External</span>
                    </button></a>
                </div>
                @include('livewire.jobPeople.duplicateUpload')

                <!-- Modal container -->
                <div x-show="bopen" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50"
                    style="display:none">

                    <!-- Modal background with a higher z-index -->
                    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

                    @if ($this->hasCandidatesUploaded)
                    <div
                        class="modal-content step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">

                        <span class="py-3 px-3 rounded-full GreenBackground mainGreen text-center">
                            <img class="w-6 h-6" src="{{ asset('images/right_tick_with_circle_green.svg') }}">
                        </span>

                        <h1 class="font-medium px-4 text-xl text-center my-5">Candidates was successfully uploaded!
                        </h1>

                        <!-- border above buttons -->
                        <div class="w-full border-t my-5 border-gray-200"></div>

                        <!-- buttons wrapper -->
                        <div class="flex gap-2 w-full px-4">
                            <button @click="bopen = false" type="button"
                                class="bg-white w-full text-black border p-2 rounded-md">Close</button>
                        </div>
                    </div>
                    @else
                    <!-- Modal content with a lower z-index -->
                    <div
                        class="modal-content2 step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50">

                        <img @click="bopen = false" class="absolute right top-2 w-auto cursor-pointer"
                            src="{{ asset('images/cross.svg') }}" alt="Search Icon">
                        <h2 class="font-semibold px-4">Bulk upload candidates</h2>
                        <div class="w-full border-t mt-3 border-gray-200"></div>

                        <div class="modalscroll mt-1 grid grid-cols-2 grid gap-1 px-4">
                            <div class="col-span-2 mt-2">
                                @error('csvFile')
                                <div class="text-red-500 text-md text-center">
                                    {{ $message }}
                                </div>
                                @enderror
                            </div>
                            <div class="col-span-2 mb-5 mt-2">
                                <label for="External-candidate" class="block text-md text-center font-semibold leading-6 text-gray-900">Candidates</label>
                                <div class="mt-2 h-48 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                    <div class="text-center">
                                        <div class="flex items-center justify-center">
                                            <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                        </div>
                                        <div class="mt-4 text-md leading-6 text-gray-600">
                                            <label for="file-upload" class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 hover:text-sky-500">
                                                <span>Upload a file</span>
                                                <input id="file-upload" wire:model="csvFile" name="file-upload" type="file" class="sr-only" accept=".csv, .xlsx, .xls">
                                            </label>
                                            <!-- <p class="pl-1">or drag and drop</p> -->
                                        </div>
                                        <p class="text-sm leading-5 text-gray-600">(CSV or Excel)</p>
                                        @if ($csvFile && $csvFile->getClientOriginalName())
                                        <p class="text-sm leading-5 text-gray-600 mt-2">
                                            <span class="font-semibold">Selected File:</span>
                                            {{ $csvFile->getClientOriginalName() }}
                                        </p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-span-2 mb-5">
                                <label for="cover-photo"
                                    class="block text-md font-medium text-center leading-6 text-gray-900">Download
                                    Templates</label>
                                <div
                                    class="mt-2 h-48 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                                    <div class="text-center">
                                        <div class="flex items-center justify-center">
                                            <img class="h-10 w-10" src="{{ asset('images/download.png') }}">
                                        </div>
                                        <div class="mt-4 flex text-md leading-6 text-gray-600">
                                            <label for="file-upload"
                                                class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 hover:text-indigo-500">
                                                <button wire:click="downloadCSV" class="">
                                                    <span>Download Template Files</span>
                                                </button>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- border above buttons -->
                        <div class="w-full border-t  border-gray-200"></div>

                        <!-- 3rd steps button -->
                        <div class="flex gap-2 w-full mt-4 px-4">
                            <button @click="closeUploadCandidatesModal" type="button"
                                class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                                <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                                <span class="block font-medium" @click="bopen = false">Cancel</span>
                            </button>
                            <button type="button" wire:click="uploadCSV"
                                class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                                <span class="block">Upload</span>
                            </button>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <div class="flex" x-data="{
                Aopen: @entangle('Aopen'),
                closeModal() {
                    this.Aopen = false;
                    this.$dispatch('addPersonModalClosed');
                },
                peopleAreadyExistsPopup: @entangle('peopleAreadyExistsPopup')
            }">
                <!-- Trigger button inside the modal -->
                <div class="flex items-center justify-center">
                    <div>
                        <button x-on:click="Aopen = true"
                            class="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium text-white text-md justify-center">
                            <img class="h-4 font-bold w-auto img px-1"
                                src="{{ asset('/images/plus-white-without-circle.svg') }}">
                            <h1 class="text-md font-semibold">Add Person</h1>
                        </button>
                    </div>
                </div>
                <!-- Modal container -->
                <div x-show="Aopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50"
                    style="display:none">
                    <!-- Modal background with a higher z-index -->
                    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

                    @if ($step == 3)
                    <div
                        class="modal-content step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">

                        <span class="py-3 px-3 rounded-full GreenBackground mainGreen text-center">
                            <img class="w-6 h-6" src="{{ asset('images/right_tick_with_circle_green.svg') }}">
                        </span>

                        <h1 class="font-medium px-4 text-xl text-center my-5">New person was successfully added!
                        </h1>

                        <!-- border above buttons -->
                        <div class="w-full border-t my-5 border-gray-200"></div>

                        <!-- buttons wrapper -->
                        <div class="flex gap-2 w-full px-4">
                            <button x-on:click="Aopen = false" type="button"
                                class="bg-white w-full text-black border p-2 rounded-md">Close</button>
                            {{--
                                    @if (!empty($job))
                                        <a href="{{ route('job.long.index', ['job' => $job->id]) }}" type="button"
                            class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">
                            <span class="block"> Review</span>
                            </a>
                            @else
                            <button @click="closeModal" type="button"
                                class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                <span class="block"> Review</span>
                            </button>
                            @endif
                            --}}
                        </div>
                    </div>
                    @else
                    <!-- Modal content with a lower z-index -->
                    <div class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]"
                        x-transition>
                        <!-- Advanced Search modal content -->

                        <img x-on:click="Aopen = false" class="absolute right top-2 w-auto cursor-pointer"
                            src="{{ asset('images/cross.svg') }}" alt="Search Icon">

                        <h2 class="font-semibold px-4">Add Person</h2>
                        <div class="flex justify-between mt-3 px-4">
                            <button>
                                <span
                                    class="mainBlue p-1 rounded-full px-2 text-xs 
                                                    @if ($step == 1) BlueBackground mainBlue
                                                    @else
                                                        GreenBackground mainGreen @endif">1</span>
                                <span class="text-xs font-medium">Personal Details</span>
                            </button>
                            <button>
                                <span
                                    class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                                            @if ($step == 2) BlueBackground mainBlue
                                            @elseif($step > 2)
                                                GreenBackground mainGreen @endif">2</span>
                                <span class="text-xs font-medium">Additional Information</span>
                            </button>
                        </div>
                        <div class="w-full border-t mt-3 border-gray-200"></div>

                        @if ($step === 1)
                        <!-- 1st Step -->
                        <div class="h-full">
                            <div class="h-5/6 flex items-center">
                                <div class="w-full">

                                    <div class="relative py-2 bg-white">
                                        <div class="absolute inset-0 flex items-center px-1"
                                            aria-hidden="true"></div>
                                    </div>

                                    <div class="modalscroll px-4">

                                        <div class="mb-3">
                                            <label for="addForename"
                                                class="block text-xs font-medium labelcolor">Forename
                                            </label>
                                            <input type="text" wire:model="addForename"
                                                class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                placeholder="Enter forename">

                                            @error('addForename')
                                            <span class="text-red-500 text-xs">{{ $message }}</span>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="addSurname"
                                                class="block text-xs font-medium labelcolor">Surname</label>
                                            <input type="text" wire:model="addSurname"
                                                class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                placeholder="Enter surname">

                                            @error('addSurname')
                                            <span class="text-red-500 text-xs">{{ $message }}</span>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="addMiddlename"
                                                class="block text-xs font-medium labelcolor">Middle Name
                                                (Optional)</label>
                                            <input type="text" wire:model="addMiddlename"
                                                class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                placeholder="Enter middle name">
                                        </div>

                                        <div class="mb-3">
                                            <label for="addOtherName"
                                                class="block text-xs font-medium labelcolor">Other Name
                                                (Optional)</label>
                                            <input type="text" wire:model="addOtherName"
                                                class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                placeholder="Enter other name">
                                        </div>

                                        <div class="mb-3">
                                            <label for="addRole"
                                                class="block text-xs font-medium labelcolor">Role</label>
                                            <input type="text" wire:model="addRole"
                                                class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                placeholder="Enter role">

                                            @error('addRole')
                                            <span class="text-red-500 text-xs">{{ $message }}</span>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="addStartDate"
                                                class="block text-xs font-medium labelcolor">When did they
                                                start? (Optional)</label>
                                            <input type="date" wire:model="addStartDate"
                                                class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                placeholder="Enter the company name">
                                        </div>

                                        <div class="mb-3">
                                            <label for="planName"
                                                class="block text-xs font-medium labelcolor">Company</label>
                                            <div x-data="{
                                                        recommendations: {{ json_encode($companies) }},
                                                        searchQuery: @entangle('selectedCompanies').defer,
                                                        filteredRecommendations: [],
                                                        selectRecommendation(value) {
                                                            this.searchQuery = value;
                                                            const inputField = document.getElementById('selectedCompanies');
                                                            inputField.value = value;
                                                            inputField.focus();
                                                            inputField.dispatchEvent(new Event('input'));
                                                            this.filteredRecommendations = []; // Clear recommendations after selection
                                                        },
                                                        search() {
                                                            const query = String(this.searchQuery ?? '').toLowerCase();
                                                            if (query === '') {
                                                                this.filteredRecommendations = [];
                                                            } else {
                                                                this.filteredRecommendations = this.recommendations.filter(recommendation => {
                                                                    return recommendation.value.toLowerCase().includes(query);
                                                                }).slice(0, 10);
                                                            }
                                                        },
                                                        initialize() {
                                                            this.searchQuery = @js($selectedCompanies);
                                                            this.search();
                                                        }
                                                        }" x-init="initialize">
                                                <input type="text" id="selectedCompanies"
                                                    wire:model="selectedCompanies" x-model="searchQuery"
                                                    @input="search"
                                                    class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md">
                                                <div class="flex flex-col gap-y-2 mt-2"
                                                    x-show="filteredRecommendations.length > 0">
                                                    <template x-for="recommendation in filteredRecommendations"
                                                        :key="recommendation.value">
                                                        <div class="flex items-center gap-x-2">
                                                            <button class="text-sm font-semibold text-cyan-500"
                                                                @click.prevent="selectRecommendation(recommendation.value); $dispatch('close-suggestions')">
                                                                Use Recommendation
                                                            </button>
                                                            <p class="text-sm text-gray-900 font-light"
                                                                x-text="recommendation.value"></p>
                                                        </div>
                                                    </template>
                                                </div>
                                            </div>

                                            @error('selectedCompanies')
                                            <span class="text-red-500 text-xs">{{ $message }}</span>
                                            @enderror
                                        </div>

                                    </div>

                                    <!-- border above buttons -->
                                    <div class="w-full border-t mt-4 border-gray-200"></div>

                                    <!-- buttons wrapper -->
                                    <div class="flex gap-2 w-full px-4 mt-4 ">
                                        <button x-on:click="Aopen = false" type="button"
                                            class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                        <button wire:click.prevent="validateStepOne" type="button"
                                            class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                            <span class="block"> Continue</span>
                                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                                        </button>
                                    </div>

                                </div>
                            </div>
                        </div>
                        @elseif($step === 2)
                        <!-- 2nd Step -->
                        <div class="h-full">
                            <div class="h-5/6 flex items-center">
                                <div class="w-full">

                                    <div class="relative py-2 bg-white">
                                        <div class="absolute inset-0 flex items-center px-1"
                                            aria-hidden="true"></div>
                                    </div>

                                    <div class="modalscroll px-4">
                                        <div class="mb-3">
                                            <label for="addLocation"
                                                class="block text-xs font-medium labelcolor">They are based in
                                                (Optional)
                                            </label>
                                            <select
                                                class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                wire:model="addLocation" id="country" name="country">
                                                <option value=""
                                                    class="mt-1 block w-full p-2 outline-none border border-gray-300 rounded-md">
                                                    Select a country</option>
                                                @foreach ($countries as $code => $name)
                                                <option value="{{ $name }}">{{ $name }}
                                                </option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div class="mb-3">

                                            <!-- <input type="text" wire:model="addGender"
                                                        class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                        placeholder="Female, Male, All"> -->

                                            <fieldset>
                                                <label for="addGender"
                                                    class="block text-xs font-medium labelcolor">They could be
                                                    described as </label>

                                                <!-- radio button in form of buttons -->
                                                <ul class="donate-now flex gap-2 mt-1">
                                                    <li>
                                                        <input type="radio" wire:model="addGender"
                                                            id="male" name="addGender" value="Male"
                                                            class="cursor-pointer" />
                                                        <label for="male"
                                                            class="text-center font-semibold labelcolor">Male</label>
                                                    </li>
                                                    <li>
                                                        <input type="radio" wire:model="addGender"
                                                            id="female" name="addGender" value="Female"
                                                            class="cursor-pointer" />
                                                        <label for="female"
                                                            class="text-center font-semibold labelcolor">Female</label>
                                                    </li>
                                                    <li>
                                                        <input type="radio" wire:model="addGender"
                                                            id="not_required" name="addGender"
                                                            value="Gender Not Applicable"
                                                            class="cursor-pointer" />
                                                        <label for="not_required"
                                                            class="text-center font-semibold labelcolor">Not
                                                            Applicable</label>
                                                    </li>
                                                </ul>
                                            </fieldset>
                                        </div>

                                        <div class="mb-3">
                                            <label for="addLinkedinURL"
                                                class="block text-xs font-medium labelcolor">LinkedIn Url (Optional)</label>
                                            <input type="text" wire:model="addLinkedinURL"
                                                class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                placeholder="Enter LinkedIn url">

                                            @error('addLinkedinURL')
                                            <span class="text-red-500 text-xs">{{ $message }}</span>
                                            @enderror
                                        </div>

                                    </div>

                                    <!-- border above buttons -->
                                    <div class="w-full border-t mt-4 border-gray-200"></div>

                                    <!-- buttons wrapper -->
                                    <div class="flex gap-2 w-full px-4 mt-4 ">
                                        <button wire:click="previousStep" type="button"
                                            class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                                            <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                                            <span class="block font-medium">Back</span>
                                        </button>

                                        <button type="button" wire:click="AddPerson"
                                            class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                            <img class="h-5 w-5"
                                                src="{{ asset('images/plus-white-without-circle.svg') }}">
                                            <span class="block">Add Person</span>
                                        </button>
                                    </div>

                                </div>
                            </div>
                        </div>
                        @endif


                        {{-- <div class="modalscroll">
                                


                                <h3 class=" mt-2 font-semibold text-base px-2">Additional Information</h3>
                                <div class="relative py-2 bg-white">
                                    <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                                        <div class="w-full border-t-2 border-gray-200"></div>
                                    </div>
                                </div>
                                <div class="">
                                    <div class="text-left">
                                        <label for="Surname" class="text-sm font-medium labelcolor">They are based in (
                                            Optional )
                                        </label>
                                        <select
                                            class="block w-full p-2  text-gray-400 outline-none border bg-white border-gray-300 rounded-md"
                                            wire:model="addLocation" id="country" name="country">
                                            <option value="" class="text-gray-400 text-sm">Select a country</option>
                                            @foreach ($countries as $code => $name)
                                                <option value="{{ $name }}">{{ $name }}</option>
                        @endforeach
                        </select>
                    </div>
                    <div class="mt-2">
                        <label for="planName" class="text-sm font-medium labelcolor">They could be
                            described as </label>
                        <input type="text" wire:model="addGender"
                            class="mt-1 placeholder:text-gray-400 placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                            placeholder="Female, Male, All">
                    </div>
                </div>
            </div>
            --}}

            {{--
                                <div class="flex gap-2 w-full mt-4">
                                    <div class="w-full">
                                        <button x-on:click="Aopen =false" type="button"
                                            class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                    </div>
                                    <div class="w-full">
                                        <button wire:click="AddPerson" x-on:click="Aopen =false"
                                            class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">Add</button>
                                    </div>
                                </div>
                            --}}
        </div>
        @endif
    </div>

    <!-- Duplicatec confirmation Modal -->
    <div x-show="peopleAreadyExistsPopup" x-cloak
        class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50">
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
        <div class="bg-white relative  border-2 p-4 rounded-lg flex flex-col z-50">
            <img @click="peopleAreadyExistsPopup = false"
                class="absolute right-2 crossTop w-auto cursor-pointer"
                src="{{ asset('images/cross.svg') }}" alt="Close Icon">
            <h2 class="font-semibold px-4">Confirmation</h2>
            <div class="w-full border-t mt-3 border-gray-200"></div>
            <div class="h-full" wire:key="AddPersonStep2">
                <div class="h-5/6 flex items-center">
                    <div class="w-full">

                        <div class="relative py-2 bg-white">
                            <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                        </div>

                        <div class="modalscroll">
                            <div class="mb-2">
                                <label for="existingPeople" class="block text-xs font-medium labelcolor">A
                                    person with detail Forename, surname and company are already exists
                                </label>
                                <select
                                    class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                    wire:model="existingPeopleField" id="existingPeopleField"
                                    name="existingPeopleField">
                                    <option value=""> Select people</option>
                                    @foreach ($isPeopleAlreadyExists as $code => $existingPeople)
                                    <option value="{{ $existingPeople->id }}">
                                        {{ $existingPeople->forename . ' ' . $existingPeople->surname }}({{ $existingPeople->latest_role ?? 'N/A' }})
                                    </option>
                                    @endforeach
                                </select>
                                @error('existingPeopleField')
                                <span class="text-red-500 text-xs">{{ $message }}</span>
                                @enderror
                            </div>

                        </div>

                        <!-- border above buttons -->
                        <div class="w-full border-t mt-4 border-gray-200"></div>

                        <!-- buttons wrapper -->


                    </div>
                </div>
            </div>

            <div class="flex gap-2 w-full px-4 mt-4">
                <button @click="peopleAreadyExistsPopup = false;" type="button"
                    class="bg-white font-semibold w-full text-black border p-2 rounded-md">Cancel</button>
                <button wire:click="useExistingPeople" type="button"
                    class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                    <span class="block">Use Existing</span>
                </button>
            </div>
        </div>
    </div>


</div>
</div>
</div>

<div class="lightgray ContentContainer" x-data="{
        drawer: false,
        vopen: @entangle('vopen'),
        addToPlanPopup : @entangle('addToPlanPopup'),
        addToTalentPoolPopup : @entangle('addToTalentPoolPopup'),
        addToPlansArray: @entangle('addToPlansArray'),
        addToTalentPoolArray: @entangle('addToTalentPoolArray'),
        checkSelection() {
            if (Object.values(this.addToPlansArray).every(value => !value)) {
                toastr.info('Please select plan!');
                return false;
            }
            return true;
        },
        checkSelectionOfTalentPool() {
            if (Object.values(this.addToTalentPoolArray).every(value => !value)) {
                toastr.info('Please select Talent Pool!');
                return false;
            }
            return true;
        },
        ropen: false,
        tab: 'summary',
        closeDrawer() {
            this.drawer = false;
            this.$dispatch('clearFilters');
        },
        closeReportModal() {
            this.ropen = false;
            this.$dispatch('clearSelectedIndividuals');
        }
    }">
    <div class=" grid grid-cols-2 px-10 shadow-2xl gap-4 w-full h-full py-4">

        <div class="flex-1 bg-white shadow-lg rounded-xl p-4">
            <dl class="text-lg font-medium leading-6 text-gray-500">Number of People</dl>
            <dd class="w-full flex-none text-xl font-medium leading-10 tracking-tight text-gray-900">
                {{ $TotalPeople }}
            </dd>
        </div>
        <div class="flex-1 bg-white shadow-lg rounded-xl p-4">
            <dl class="text-lg font-medium leading-6 text-gray-500">Number of Companies</dl>
            <dd class="w-full flex-none text-xl font-medium leading-10 tracking-tight text-gray-900">
                {{ $TotalCompanies }}
            </dd>
        </div>

        {{-- exco --}}
        <div class="bg-white shadow-lg rounded-xl p-4">
            <dl class="text-lg font-medium leading-6 text-gray-500">Exco</dl>
            @if ($divisionData)
            <div class="h-80" wire:ignore x-data="{
                                        tvalues: {{ json_encode($divisionData) }},
                                        tlabels:{{ json_encode($divisionLabels) }},
                                        colors:['red','blue','green'],
                                        init() {
                                                    let chart = new ApexCharts(this.$refs.genderchart, {
                                                                                                chart: { type: 'donut',
                                                                                                        width:'100%',
                                                                                                        height:'100%' },
                                                                                                labels: this.tlabels,
                                                                                                dataLabels: { enabled: true, textAnchor: 'start', style: {fontSize:'9px'}     
                                                                                                },
                                                                                                plotOptions:{
                                                                                                    pie: {
                                                                                                            donut:{
                                                                                                                size: '80%'
                                                                                                            }
                                                                                                        }
                                                                                                },
                                                                                                colors: ['#48CAE4', '#0f766e', '#9C27B0'],
                                                                                                series: this.tvalues,
                                                                                                fill: { colors: ['#48CAE4', '#0f766e', '#9C27B0']},
                                                                                                stroke: {width:0.5 },
                                                                                                legend: {   position: 'right',
                                                                                                            markers: {fillColors: ['#48CAE4', '#0f766e', '#9C27B0']} },
                                                                                                tooltip: { fillSeriesColor:true}
                                                                                    })
                                                        chart.render()
                                                }
                                        }">
                <div x-ref="genderchart"></div>
            </div>
            @else
            <p class="py-10 text-gray-500 mb-10 mt-10 text-center">No Record(s) Found</p>
            @endif
        </div>

        {{-- companies --}}
        <div wire:ignore class="bg-white shadow-lg rounded-xl p-4">
            <dl class="text-lg font-medium leading-6 text-gray-500">Top 5 Companies Identified</dl>
            @if ($companyData)
            <!-- <div class="h-80 w-full" wire:ignore x-data="{
                        cvalues: {{ json_encode($companyData) }},
                        clabels: {{ json_encode($companyLabels) }},
                        init() {
                            let chart = new ApexCharts(this.$refs.companychart, {
                                chart: {
                                    type: 'bar',
                                    stacked: true,
                                    height: 320
                                },
                                plotOptions: {
                                    bar: {
                                        horizontal: true,
                                        'borderRadius': 10,
                                        'borderRadiusApplication': 'around',
                                        'borderRadiusWhenStacked': 'last',
                                        'dataLabels': {
                                            'position': 'top',
                                            'total': {
                                                'enabled': false,
                                                'offsetX': 0,
                                                'offsetY': 0,
                                                'style': {
                                                    'color': '#373d3f',
                                                    'fontSize': '12px',
                                                    'fontWeight': 600
                                                }
                                            }
                                        }
                                    }
                                },
                                tooltip: { enabled: true },
                                grid: { show: false },
                                'dataLabels': {
                                    'position': 'top',
                                    'offsetX': 25,
                                    'style': {
                                        'fontWeight': '500',
                                        'fontSize': '14px',
                                        'colors': [
                                            '373d3f'
                                        ]
                                    }
                                },
                                xaxis: {
                                    categories: this.clabels,
                                    'labels': {
                                        'show': true,
                                        'trim': true,
                                        'style': {}
                                    },
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                yaxis: {
                                    'labels': {
                                        'show': true,
                                        'trim': true,
                                        'style': {}
                                    },
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                fill: {
                                    colors: ['#CF67E1']
                                },
                                series: [{
                                    name: 'Peoples',
                                    data: this.cvalues,
                                }],
                            })
                            chart.render()
                        }
                    }">
                        <div x-ref="companychart"></div>
                    </div> -->

            <div class="flex division-vbar-chart-container">
                <canvas id="companyBar"></canvas>
                <ul id="custom-company-legend" class="custom-vbar-legend custom-h-legend whitespace-nowrap"></ul>
            </div>
            @else
            <p class="py-10 text-gray-500 mb-10 mt-10 text-center">No Record(s) Found</p>
            @endif
        </div>

        {{-- gender split --}}
        <div wire:ignore class="bg-white shadow-lg rounded-xl p-4">
            <dl class="text-lg font-medium leading-6 text-gray-500">Potential Sex Diversity</dl>
            @if ($genderData)
            <div class="h-56 w-full px-5" wire:ignore x-data="{
                        cvalues: {{ json_encode($genderData) }},
                        clabels: {{ json_encode($genderLabels) }},
                        init() {
                            let chart = new ApexCharts(this.$refs.genderchart, {
                                chart: {
                                    type: 'bar',
                                    stacked: true,
                                    height: 220
                                },
                                plotOptions: {
                                    bar: {
                                        'distributed': false,
                                        'borderRadius': 10,
                                        'borderRadiusApplication': 'around',
                                        'borderRadiusWhenStacked': 'last',
                                        'hideZeroBarsWhenGrouped': false,
                                        'isDumbbell': false,
                                        'isFunnel': false,
                                        'isFunnel3d': true,
                                        'dataLabels': {
                                            'position': 'top',
                                            'total': {
                                                'enabled': false,
                                                'offsetX': 0,
                                                'offsetY': 0,
                                                'style': {
                                                    'color': '#373d3f',
                                                    'fontSize': '12px',
                                                    'fontWeight': 600
                                                }
                                            }
                                        }
                                    }
                                },
                                tooltip: { enabled: true },
                                grid: { show: false },
                                xaxis: {
                                    categories: this.clabels,
                                    'labels': {
                                        'show': false,
                                        'trim': true,
                                        'style': {}
                                    },
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                yaxis: {
                                    show: false
                                },
                                fill: {
                                    colors: ['#3B82F6']
                                },
                                'dataLabels': {
                                    'position': 'top',
                                    'formatter': function(val, opts) {
                                        let label = opts.w.config.xaxis.categories[opts.dataPointIndex];
                                        // Customize the data label text here
                                        return label ? `${label} (${val})` : `${val}`;
                                    },
                                    'offsetY': -25,
                                    'style': {
                                        'fontWeight': '600',
                                        'fontSize': '14px',
                                        'colors': [
                                            '373d3f'
                                        ]
                                    }
                                },
                                series: [{
                                    name: 'Gender',
                                    data: this.cvalues,
                                }],
                            })
                            chart.render()
                        }
                    }">
                <div x-ref="genderchart"></div>
            </div>
            @else
            <p class="py-10 text-gray-500 mb-10 mt-10 text-center">No Record(s) Found</p>
            @endif
        </div>

        {{-- locations --}}
        <div wire:ignore class="bg-white shadow-lg rounded-xl p-4">
            <dl class="text-lg font-medium leading-6 text-gray-500">Top 5 Locations Identified</dl>
            @if ($locationData)
            <!-- <div class="h-80 w-full" wire:ignore x-data="{
                        cvalues: {{ json_encode($locationData) }},
                        clabels: {{ json_encode($locationLabels) }},
                        init() {
                            let chart = new ApexCharts(this.$refs.locationchart, {
                                chart: {
                                    type: 'bar',
                                    stacked: true,
                                    height: 320
                                },
                                plotOptions: {
                                    bar: {
                                        horizontal: true,
                                        'borderRadius': 10,
                                        'borderRadiusApplication': 'around',
                                        'borderRadiusWhenStacked': 'last',
                                        'dataLabels': {
                                            'position': 'top',
                                            'total': {
                                                'enabled': false,
                                                'offsetX': 0,
                                                'offsetY': 0,
                                                'style': {
                                                    'color': '#373d3f',
                                                    'fontSize': '12px',
                                                    'fontWeight': 600
                                                }
                                            }
                                        }
                                    }
                                },
                                tooltip: { enabled: true },
                                grid: { show: false },
                                'dataLabels': {
                                    'position': 'top',
                                    'offsetX': 25,
                                    'style': {
                                        'fontWeight': '500',
                                        'fontSize': '14px',
                                        'colors': [
                                            '373d3f'
                                        ]
                                    }
                                },
                                xaxis: {
                                    categories: this.clabels,
                                    'labels': {
                                        'show': true,
                                        'trim': true,
                                        'style': {}
                                    },
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                yaxis: {
                                    'labels': {
                                        'show': true,
                                        'trim': true,
                                        'style': {}
                                    },
                                    axisBorder: {
                                        show: false
                                    }
                                },
                                fill: {
                                    colors: ['#FFA347']
                                },
                                series: [{
                                    name: 'Peoples',
                                    data: this.cvalues,
                                }],
                            })
                            chart.render()
                        }
                    }">
                        <div x-ref="locationchart"></div>
                    </div> -->
            <div class="flex division-vbar-chart-container">
                <canvas id="locationBar"></canvas>
                <ul id="custom-location-legend" class="custom-vbar-legend custom-h-legend whitespace-nowrap"></ul>
            </div>
            @else
            <p class="py-10 text-gray-500 mb-10 mt-10 text-center">No Record(s) Found</p>
            @endif
        </div>
    </div>

    <div class="px-10 lightgray pb-10">
        <div class="bg-white border-2 rounded-xl w-full h-max px-4">

            {{-- header above table --}}
            <div class="pt-4 w-full flex justify-between items-start gap-4" >

                <button @click="drawer = true"
                class="py-2 px-1.5 text-black  border rounded-lg flex justify-center items-center gap-1 font-medium">
                    <img class="search-icon w-auto mr-2" src="{{ asset('/images/search-icon.svg') }}"
                        alt="Search Icon">
                    <span class="text-md"> Advanced Search</span>
                </button>

                <div class=" w-40p items-center col-span-2">

                    <div class="flex flex-1 gap-x-2 px-2 py-2 border border-[#EAECF0] justify-start items-center bg-white rounded-lg">
                        <input type="text" wire:model="searchByKeyword"
                            class="px-2  w-full block  text-gray-900 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6"
                            placeholder="Search">
                        <img wire:click="searchRecordByKeyword" class="search-icon w-auto mr-2" src="{{ asset('/images/search-icon.svg') }}" alt="Search Icon">    
                    </div>
                    <label class="text-sm text-gray-500">To search by multiple criteria, Please enter the keywords
                        comma(,) separated</label>
                </div>

                <div class="flex relative w-20p z-20 w-full col-span-1 items-center">
                    <label for="sortBy" class="block text-md font-medium labelcolor w-24">Sort By</label>
                    <select wire:model.live="sortBy" id="sortBy"
                        class="flex justify-between select-selected px-4 py-2 w-full border border-gray-300 rounded-lg z-auto shadow-sm cursor-pointer bg-white text-gray-500">
                        <option value="tenure">Tenure</option>
                        <option value="created_at">Date Added</option>
                        <option value="start_date">Date Joined</option>
                    </select>
                    <!-- <div class=" flex justify-between select-selected px-4 py-2 w-full border border-gray-300 rounded-lg z-auto shadow-sm cursor-pointer bg-white text-gray-500"
                                id="selectStatus">
                                <span> Sort by</span>
                                <img src="{{ asset('/images/arrowDown.svg') }}" alt="arrowDown">
                            </div>
                            <div class="select-items absolute w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10 d-none">
                                <div class="flex items-center px-4 py-2 hover:bg-gray-100 ">
                                    <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="active" id="activeStatus"  wire:click="Ordermytable('tenure')">
                                    <label class="activeColor p-1 rounded-lg px-2 text-sm block cursor-pointer" for="activeStatus"> Tenure</label>
                                </div>
                                <div class="flex items-center px-4 py-2 hover:bg-gray-100">
                                    <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="draft" id="draftStatus" wire:click="Ordermytable('created_at')">
                                    <label class="DraftColor  p-1 rounded-lg px-2 text-sm cursor-pointer" for="draftStatus">Date Added</label>
                                </div>
                                <div class="flex items-center px-4 py-2 hover:bg-gray-100">
                                    <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="closed" id="closedStatus" wire:click="Ordermytable('start_date')" >
                                    <label class="closedColor p-1 rounded-lg px-2 text-sm cursor-pointer" for="closedStatus">Date Joined</label>
                                </div>
                            </div> -->
                </div>
                <div class="flex relative w-20p z-20 w-full col-span-1">
                    <select wire:model.live="sortDirection" id="sortBy"
                        class="flex justify-between select-selected px-4 py-2 w-full border border-gray-300 rounded-lg z-auto shadow-sm cursor-pointer bg-white text-gray-500">
                        <option value="asc">Ascending</option>
                        <option value="desc">Descending</option>
                    </select>
                </div>
            </div>

            <div class="pb-4 w-full flex justify-end items-start gap-4"  x-data="{ dallopen: false, selected: @entangle('selectedPerson') }">
                    <button @click.prevent="if (selected && Object.keys(selected).length > 0) { $wire.addSelectedToPlan(); }"  class="py-2 px-4 border rounded-lg flex justify-center items-center gap-1 font-medium">
                    <img class="add-icon w-auto mr-2" src="{{ asset('images/pluswithcircle.svg') }}" alt="Add to Plan Icon">
                    <span class="text-md font-semibold">Add to Plan</span>
                    </button>
                    <button @click.prevent="if (selected && Object.keys(selected).length > 0) { $wire.addSelectedToTalentPool(); }"  class="py-2 px-4 border rounded-lg flex justify-center items-center gap-1 font-medium">
                    <img class="add-icon w-auto mr-2" src="{{ asset('images/pluswithcircle.svg') }}" alt="Add to Talent Pool Icon">
                    <span class="text-md font-semibold">Add to Talent Pool</span>
                    </button>
                    <button @click.prevent="if (selected && Object.keys(selected).length > 0) { $wire.addSelectedToPotential(); }"  class="py-2 px-4 border rounded-lg flex justify-center items-center gap-1 font-medium">
                    <img class="add-icon w-auto mr-2" src="{{ asset('images/pluswithcircle.svg') }}" alt="Add to Potential Candidate">
                    <span class="text-md font-semibold">Add to Potential Candidate</span>
                    </button>
                </div>                   





            <div class="TableOuter relative rounded-2xl h-full bg-white border mb-5">
                <div class="table_wrapper peopledashboardTable rounded-2xl people-dashboard">
                    <table class="tableEdges w-full overflow-x-auto">
                        <thead class="rounded-2xl">
                            <tr class="grayBackground GrayText">
                            <th scope="col" class="flex items-center px-2 py-2 text-center py-3.5 px-3 text-left text-lg border-b font-medium">  <div class="flex flex-col gap-y-1 text-start mr-2">
                                        <input type="checkbox" x-on:click="$dispatch('select-all', $event.target.checked); $wire.set('selectedPerson', $event.target.checked ? @json($People->pluck('id')) : [])" x-bind:checked="$wire.selectedPerson.length > 0" class="form-checkbox h-4 w-4">
                                        </div> Name</th>
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">
                                    Company</th>
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">
                                    Country</th>
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">
                                    Function</th>
                                {{-- <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Division</th> --}}
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">
                                    Potential Sex</th>
                                @if ($plandetails->ethnicity === 1)
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">
                                    Potential Diversity</th>
                                @endif
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">
                                    Tenure</th>
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">
                                    Readiness</th>
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">
                                    Registration Status</th>
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">
                                </th>
                            </tr>
                        </thead>
                        <tbody class="myclass">
                            @if ($People->isNotEmpty())
                            @foreach ($People as $Person)
                            <tr>
                                <td class=" px-3 flex py-3 text-md font-normal">
                                <div class="flex flex-col gap-y-1 text-start mr-2 mt-2">
                                        <input type="checkbox" wire:model="selectedPerson" value="{{ $Person->id }}" class="form-checkbox h-4 w-4">
                                </div>
                                    <div class="flex flex-col">
                                        <span>{{ $Person->forename }} {{ $Person->surname }}</span>
                                        <span
                                            class="truncate GrayText w-48 text-sm">{{ $Person->latest_role }}</span>
                                    </div>
                                </td>
                                <td class=" px-3 py-3 text-md font-normal">{{ $Person->company_name }}
                                </td>
                                <td class=" px-3 py-3 text-md font-normal">{{ $Person->country }}</td>
                                <td class=" px-3 py-3 text-md font-normal">{{ $Person->function }}</td>
                                {{-- <td class=" px-3 py-3 text-md font-normal">{{ $Person->division }}</td> --}}
                                <td class=" px-3 py-3 text-md font-normal">{{ $Person->gender }}</td>
                                @if ($plandetails->ethnicity === 1)
                                <td class=" px-3 py-3 text-md font-normal">{{ $Person->diverse }}</td>
                                @endif
                                <td class=" px-3 py-3 text-md font-normal">{{ $Person->tenure }}</td>
                                <td class=" px-3 py-3 text-sm">
                                    @if ($Person->readiness)
                                    @if (strtolower($Person->readiness) == 'ready')
                                    <span class="py-2 px-4 rounded-lg font-medium redlinessReaddy">
                                        Ready
                                    </span>
                                    @elseif(strtolower($Person->readiness) == 'not ready')
                                    <span
                                        class="py-2 px-4 rounded-lg font-medium RedBG text-red-500">
                                        Not Ready
                                    </span>
                                    @endif
                                    @endif
                                </td>
                                <td class=" px-3 py-3 text-md font-normal">{{ $Person->other_tags }}</td>
                                <td>
                                    <div class="dropdown-container relative">
                                        <button tabindex="1" id="dropdownDefaultButton"
                                            data-dropdown-toggle="dropdown"
                                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-md py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                            type="button">
                                            <img class="h-5 w-5"
                                                src="{{ asset('images/DotsThreeVertival.svg') }}">
                                            <span class="ml-1" aria-hidden="true"></span>
                                        </button>

                                        <!-- Dropdown menu -->
                                        <div id="dropdown"
                                            class="dropdown-menu hidden z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                            <ul class="py-2 text-md text-gray-700 dark:text-gray-200"
                                                aria-labelledby="dropdownDefaultButton">
                                                <li class="cursor-pointer">
                                                    <a class="p-2"
                                                        wire:click.prevent="viewIndividual({{ $Person->id }})">
                                                        <span class="font-semibold text-md">
                                                            View
                                                        </span>
                                                    </a>
                                                </li>
                                                <li class="cursor-pointer">
                                                    <a class="p-2" wire:click="showAddToPlanPopup({{ $Person->id }})">
                                                        <span class="font-semibold text-md">Add to
                                                            Plan</span>
                                                    </a>
                                                </li>
                                                <li class="cursor-pointer">
                                                    <a class="p-2" wire:click="showAddToTalentPoolPopup({{ $Person->id }})">
                                                        <span class="font-semibold text-md">Add to Talent Pool</span>
                                                    </a>
                                                </li>
                                                <li class="cursor-pointer">
                                                    <a class="p-2"
                                                        wire:click="addToPotentialCandidate({{ $Person->id }})">
                                                        <span class="font-semibold text-md">Add to
                                                            Potential Candidate</span>
                                                    </a>
                                                </li>
                                                <li class="cursor-pointer">
                                                    <a class="p-2" @click="ropen=true"
                                                        wire:click.prevent="viewIndividual({{ $Person->id }})">
                                                        <span class="font-semibold text-md">Report</span>
                                                    </a>
                                                </li>
                                            </ul>

                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                            @else
                            <tr class="text-center">
                                <td colspan="10" class="py-10 text-gray-500">No Record(s) Found</td>
                            </tr>
                            @endif
                        </tbody>
                    </table>
                </div>

                <div class="flex absolute bottom-0 w-full px-2 gap-4 paginationContainer border-t py-4">
                    <select wire:model.live="perPage" id="perPage"
                        class="form-select bg-white border rounded-lg outline-none GrayText py-2">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="30">30</option>
                        <option value="40">40</option>
                        <option value="50">50</option>
                    </select>
                </div>
                <div class="pl">

                    {{ $People->links() }}
                </div>

            </div>
        </div>
    </div>

    <!-- Advance Search Started -->
    <div x-show="drawer" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50 right-0"
        style="display:none">

        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

        <div x-show="drawer" x-transition:enter="transition transform ease-out duration-300"
            x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0"
            x-transition:leave="transition transform ease-in duration-300"
            x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full"
            class="z-50 bg-white absolute right-0 shadow-lg overflow-hidden p-0">
            <div class="flex justify-between mt-2 h-5 px-4 py-2 mb-5">
                <h2 class="font-semibold">
                    Advanced Search
                </h2>
                <img @click="drawer = false" class="h-5 w-5 cursor-pointer"
                    src="{{ asset('images/cross.svg') }}" alt="">
            </div>
            <div class="border-t mt-3 border-gray-200"></div>
            <div class=" advanced_search_drawer relative overflow-y-scroll">
                <div class="flex gap-y-2 flex-col p-4">
                    <div class="">
                        <label for="name" class="block text-md font-medium labelcolor">First Name</label>
                        <input wire:model="forename" type="text" id="name"
                            placeholder="Enter first name" name="name"
                            class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                    </div>
                    <div class="">
                        <label for="name" class="block text-md font-medium labelcolor">Last Name</label>
                        <input wire:model="surname" type="text" id="name" placeholder="Enter last name"
                            name="name"
                            class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                    </div>
                    <div class="">
                        <fieldset>
                            <legend class="text-md font-medium labelcolor">Gender</legend>
                            <ul class="donate-now mt-1">
                                <li>
                                    <input type="radio" id="Male" name="gender" value="Male"
                                        class="cursor-pointer" wire:model="gender" />
                                    <label for="Male"
                                        class="text-center font-semibold labelcolor">Male</label>
                                </li>
                                <li>
                                    <input type="radio" id="Female" name="gender" value="Female"
                                        class="cursor-pointer" wire:model="gender" />
                                    <label for="Female"
                                        class="text-center font-semibold labelcolor">Female</label>
                                </li>
                            </ul>
                        </fieldset>
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <h2 class="block text-md font-medium labelcolor">Location</h2>
                        <div class="text-md" wire:ignore x-data="{
                                multiple: true,
                                value: @entangle('location'),
                                options: {{ json_encode($locations) }},
                                init() {
                                    this.$nextTick(() => {
                                        let choices = new Choices(this.$refs.select)
                            
                                        let refreshChoices = () => {
                                            let selection = this.multiple ? this.value : [this.value]
                            
                                            choices.clearStore()
                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                value,
                                                label,
                                                selected: selection.includes(value),
                                            })))
                                        }
                            
                                        refreshChoices()
                            
                                        this.$refs.select.addEventListener('change', () => {
                                            this.value = choices.getValue(true)
                                        })
                            
                                        this.$watch('value', () => refreshChoices())
                                        this.$watch('options', () => refreshChoices())
                                    })
                                }
                            }"
                            class="bg-white text-md max-w-sm w-full">
                            <select x-ref="select"></select>
                        </div>
                    </div>
                    <div class="">
                        <label for="role" class="block text-md font-medium labelcolor">Role</label>
                        <input wire:model="role" type="text" id="role" placeholder="Enter role"
                            name="role"
                            class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                    </div>
                    <div class="">
                        <label for="previousRole" class="block text-md font-medium labelcolor">Previous
                            Role</label>
                        <input wire:model="previousRole" type="text" id="previousRole"
                            placeholder="Enter previous role" name="selectedRole"
                            class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <h2 class="block text-md font-medium labelcolor">Company</h2>
                        <div class="text-md" wire:ignore x-data="{
                                multiple: false,
                                value: @entangle('company'),
                                options: {{ json_encode($companiesListToSearch) }},
                                init() {
                                    this.$nextTick(() => {
                                        let choices = new Choices(this.$refs.select)
                            
                                        let refreshChoices = () => {
                                            let selection = this.multiple ? this.value : [this.value]
                            
                                            choices.clearStore()
                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                value,
                                                label,
                                                selected: selection.includes(value),
                                            })))
                                        }
                            
                                        refreshChoices()
                            
                                        this.$refs.select.addEventListener('change', () => {
                                            this.value = choices.getValue(true)
                                        })
                            
                                        this.$watch('value', () => refreshChoices())
                                        this.$watch('options', () => refreshChoices())
                                    })
                                }
                            }"
                            class="bg-white text-md max-w-sm w-full">
                            <select x-ref="select"></select>
                        </div>
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <h2 class="block text-md font-medium labelcolor">Function</h2>
                        <div class="text-md" wire:ignore x-data="{
                                multiple: false,
                                value: @entangle('function'),
                                options: {{ json_encode($functions) }},
                                init() {
                                    this.$nextTick(() => {
                                        let choices = new Choices(this.$refs.select)
                            
                                        let refreshChoices = () => {
                                            let selection = this.multiple ? this.value : [this.value]
                            
                                            choices.clearStore()
                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                value,
                                                label,
                                                selected: selection.includes(value),
                                            })))
                                        }
                            
                                        refreshChoices()
                            
                                        this.$refs.select.addEventListener('change', () => {
                                            this.value = choices.getValue(true)
                                        })
                            
                                        this.$watch('value', () => refreshChoices())
                                        this.$watch('options', () => refreshChoices())
                                    })
                                }
                            }"
                            class="bg-white text-md max-w-sm w-full">
                            <select x-ref="select"></select>
                        </div>
                    </div>
                    <div class="flex flex-col gap-y-2">
                        <h2 class="block text-md font-medium labelcolor">Division</h2>
                        <div class="text-md" wire:ignore x-data="{
                                multiple: false,
                                value: @entangle('division'),
                                options: {{ json_encode($divisions) }},
                                init() {
                                    this.$nextTick(() => {
                                        let choices = new Choices(this.$refs.select)
                            
                                        let refreshChoices = () => {
                                            let selection = this.multiple ? this.value : [this.value]
                            
                                            choices.clearStore()
                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                value,
                                                label,
                                                selected: selection.includes(value),
                                            })))
                                        }
                            
                                        refreshChoices()
                            
                                        this.$refs.select.addEventListener('change', () => {
                                            this.value = choices.getValue(true)
                                        })
                            
                                        this.$watch('value', () => refreshChoices())
                                        this.$watch('options', () => refreshChoices())
                                    })
                                }
                            }"
                            class="bg-white text-md max-w-sm w-full">
                            <select x-ref="select"></select>
                        </div>
                    </div>
                    <div class="">
                        <label for="name" class="block text-md font-medium labelcolor">Regulatory
                            Bodies</label>
                        <input wire:model="regBodies" type="text" id="name"
                            placeholder="Enter regulatory bodies" name="name"
                            class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                    </div>
                    <div class="mt-2">
                        <label for="name" class="block text-md font-medium labelcolor">Minimum Tenure</label>
                        <div class="Tenure mt-1 justify-between">
                            {{--
                                    <button
                                        class="block w-8 border-2 px-1 rounded-md @if (!$min_exp) disabled @endif"
                                        wire:click="changeMinimumTenure('decrease')"
                                        @if (!$min_exp) disabled @endif>
                                        <img class="search-icon w-8 h-8 bg-white"
                                            src="{{ asset('images/Minus.svg') }}"
                            alt="Search Icon">
                            </button>
                            --}}
                            <input type="number" wire:model="min_exp" min="0" inputmode="numeric"
                                class="w-[75%] outline-none block text-center bg-white p-2 text-md font-normal border border-gray-300 h-10 rounded-md w-full"
                                placeholder="0">
                            {{--
                                    <button class="block w-8 px-1 border-2  rounded-md"
                                        wire:click="changeMinimumTenure('increase')">
                                        <img class="search-icon w-8 h-8  bg-white"
                                            src="{{ asset('images/Plus.svg') }}"
                            alt="Search Icon">
                            </button>
                            --}}
                        </div>
                    </div>
                </div>
                <div class="sticky bottom-0 w-full border-t mt-2 pt-2 pb-2 bg-white">
                    <div class="p-4 flex items-center justify-between">
                        <button class="border text-md text-black rounded-xl py-2 advanced-search-clear font-medium"
                            wire:click="clearFilters">Reset</button>
                        <button
                            class="border text-sm px-2 text-white rounded-xl py-2 font-medium bg-cyan-500 hover:text-white"
                            wire:click="runfilters" @click="drawer = false">Show Results</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Advance search ended -->

    <!-- View detail started -->
    <div x-show="addToPlanPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div x-data="{openPlanPopup: @entangle('openPlanPopup') }" class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
            <div class="flex justify-between">
                <h2 class="text-black-900 text-xl font-semibold">Add to plan</h2>
                <button type="button" @click="addToPlanPopup = false" class="text-gray-500 hover:text-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- select people box -->
            <div class="flex justify-center items-center mt-2">
                <div class="bg-white border selectPeople p-2 rounded-lg">
                    <!-- User list with checkboxes -->
                    <ul class="mt-4 adddeduser space-y-6 py-4 overflow-y-auto" style="max-height: 300px;">
                        @if(!empty($plansList) && $plansList->isNotEmpty())
                        @foreach ($plansList as $plan)
                        <div class="w-full border-t my-1 border-gray-200"></div>
                        <li class="flex justify-between">
                            <div class="flex items-center gap-2 pl-4">
                                <div class="space-y-1">
                                    <span class="text-sm font-semibold block">{{ $plan->name}}</span>
                                </div>
                            </div>
                            {{-- Checkbox for selecting direct reports --}}
                            <input type="checkbox"
                                wire:model="addToPlansArray.{{ $plan->id }}"
                                id="addDirectReportPeople-{{ $plan->id }}"
                                class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out">
                        </li>
                        <div class="w-full border-t my-1 border-gray-200"></div>

                        @endforeach
                        @else
                        <div class="w-full border-t my-1 border-gray-200"></div>
                        <li class="flex justify-between">No plans found!</li>
                        <div class="w-full border-t my-1 border-gray-200"></div>
                        @endif

                    </ul>
                    <div class="flex justify-end mt-4">
                        <!-- Submit the form when the button is clicked -->
                        <button type="button" class="p-2 rounded-lg"
                        @click="openPlanPopup ? $wire.addSelectedPersonToPlans() : (checkSelection() && $wire.addpeopleToPlans())">Add</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div x-show="addToTalentPoolPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div x-data="{openTalentPopup: @entangle('openTalentPopup') }"  class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
            <div class="flex justify-between">
                <h2 class="text-black-900 text-xl font-semibold">Add to Talent Pools</h2>
                <button type="button" @click="addToTalentPoolPopup = false" class="text-gray-500 hover:text-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- select people box -->
            <div class="flex justify-center items-center mt-2">
                <div class="bg-white border selectPeople p-2 rounded-lg">
                    <!-- User list with checkboxes -->
                    <ul class="mt-4 adddeduser space-y-6 py-4 overflow-y-auto" style="max-height: 300px;">
                        @if(!empty($talentPoolsList) && $talentPoolsList->isNotEmpty())
                        @foreach ($talentPoolsList as $talentPool)
                        <div class="w-full border-t my-1 border-gray-200"></div>
                        <li class="flex justify-between">
                            <div class="flex items-center gap-2 pl-4">
                                <div class="space-y-1">
                                    <span class="text-sm font-semibold block">{{ $talentPool->name}}</span>
                                </div>
                            </div>
                            {{-- Checkbox for selecting direct reports --}}
                            <input type="checkbox"
                                wire:model="addToTalentPoolArray.{{ $talentPool->id }}"
                                id="addToTalentPoolArray-{{ $talentPool->id }}"
                                class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out">
                        </li>
                        <div class="w-full border-t my-1 border-gray-200"></div>

                        @endforeach
                        @else
                        <div class="w-full border-t my-1 border-gray-200"></div>
                        <li class="flex justify-between">No talent pool found!</li>
                        <div class="w-full border-t my-1 border-gray-200"></div>
                        @endif

                    </ul>
                    <div class="flex justify-end mt-4">
                        <!-- Submit the form when the button is clicked -->
                        <button type="button" class="p-2 rounded-lg"
                        @click="openTalentPopup ? $wire.addSelectedPersonToTalentPool() : (checkSelectionOfTalentPool() && $wire.addpeopleToTalentPools())">Add</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @if ($selectedIndividualID && $selectedIndividual)

    <!-- Modal container -->
    <div x-show="vopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40"
        style="display:none">
        <!-- Modal background with a higher z-index -->
        <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
        <!-- Modal content with a lower z-index -->
        <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50">
            <!-- Modal content -->
            <div class="flex flex-col h-full">
                <div class="flex justify-between border-b px-2 py-2">
                    <h3 class="text-base text-lg font-bold">{{ $selectedIndividual->company_name }}</h3>
                    <img @click="vopen = false;" class="h-5 w-5 cursor-pointer"
                        src="{{ asset('images/cross.svg') }}" alt="">
                </div>
                <div class="grid grid-cols-2 gap-x-2 pt-3 flex-1">
                    <div class="flex flex-1 flex-col justify-between">
                        <div class="flex px-2 py-2 justify-between">
                            <div class="w-96 flex flex-col gap-y-1">
                                <h3 class="text-base text-lg font-bold">{{ $selectedIndividual->forename }}
                                    {{ $selectedIndividual->surname }}
                                </h3>
                                <h3 class="text-base text-sm text-gray-700">
                                    {{ $selectedIndividual->latest_role }}
                                </h3>
                            </div>
                            <div class="flex gap-x-2 items-center justify-center">
                                <div
                                    class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                    <img class="h-4 w-4 mr-2" src="{{ asset('images/Plus.svg') }}"
                                        alt="">
                                    <h2
                                        @click="addToTalentPoolPopup = true;" class="text-sm font-semibold">Add to Talent Pool</h2>
                                </div>
                                <div
                                    class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                    <img class="h-4 w-4 mr-2" src="{{ asset('images/Plus.svg') }}"
                                        alt="">
                                    <h2
                                        @click="addToPlanPopup = true;" class="text-sm font-semibold">Add to Plan</h2>
                                </div>
                                @if ($selectedIndividual->linkedinURL && $selectedIndividual->linkedinURL != 'NA')
                                <a id="linkedin"
                                    class="flex items-center justify-center hover:scale-105"
                                    href="{{ $selectedIndividual->linkedinURL }}" target="_blank">
                                    <img class="h-6 w-6" src="{{ asset('images/linkedinlogo.png') }}">
                                </a>
                                @endif
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-2 grid-rows-4 flex-grow">
                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Potential Sex</h3>
                                <h3 class="text-md text-gray-700">{{ $selectedIndividual->gender }}</h3>
                            </div>
                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Function</h3>
                                <h3 class="text-md text-gray-700">
                                    {{ $selectedIndividual->function ? $selectedIndividual->function : 'Not Applicable' }}
                                </h3>
                            </div>
                            {{--
                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                            <h3 class="text-sm font-bold chart-heading">Division</h3>
                                            <h3 class="text-md text-gray-700">{{ $selectedIndividual->division ? $selectedIndividual->division : 'Not Applicable' }}</h3>
                        </div>
                        --}}
                        {{--
                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                            <h3 class="text-sm font-bold chart-heading">Seniority</h3>
                                            <h3 class="text-md text-gray-700">{{ $selectedIndividual->seniority ? $selectedIndividual->seniority : 'Not Applicable' }}</h3>
                    </div>
                    --}}
                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                        <h3 class="text-sm font-bold chart-heading">Tenure in Role</h3>
                        <h3 class="text-md text-gray-700">{{ $selectedIndividual->tenure }}</h3>
                    </div>
                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                        <h3 class="text-sm font-bold chart-heading">Readiness</h3>
                        <h3 class="text-md text-gray-700">
                            @if ($selectedIndividual->readiness)
                            @if (strtolower($selectedIndividual->readiness) == 'ready')
                            <span
                                class="px-2 py-1 text-sm rounded-lg font-medium redlinessReaddy">
                                Ready
                            </span>
                            @elseif(strtolower($selectedIndividual->readiness) == 'not ready')
                            <span
                                class="px-2 py-1 text-sm rounded-lg font-medium RedBG text-red-500">
                                Not Ready
                            </span>
                            @endif
                            @else
                            <span class="px-2 py-1 text-sm rounded-lg font-medium">
                                N/A
                            </span>
                            @endif
                        </h3>
                    </div>
                    <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                        <h3 class="text-sm font-bold chart-heading">Registration Status</h3>
                        <h3 class="text-md text-gray-700">
                            {{ $selectedIndividual->other_tags ? $selectedIndividual->other_tags : 'Not Applicable' }}
                        </h3>
                    </div>
                    {{--
                                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                            <h3 class="text-sm font-bold chart-heading">Score</h3>
                                            <h3 class="text-md text-gray-700">{{ $selectedIndividual->total_score}}</h3>
                </div>
                --}}
            </div>
        </div>
        <div x-data="{ tab: 'summary' }" class="flex flex-col pt-3 gap-y-2">
            <div class="flex border-b-2">
                <button @click="tab = 'summary'" class="w-full text-sm py-3 px-2 font-medium"
                    :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                'summary',
                                            'chart-heading font-semibold': tab != 'summary'
                                        }">
                    Summary
                </button>
                <button @click="tab = 'career_history'"
                    class="w-full text-sm py-3 px-2 font-medium"
                    :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                'career_history',
                                            'chart-heading font-semibold': tab !=
                                                'career_history'
                                        }">
                    Career History
                </button>
                <button @click="tab = 'skills'" class="w-full text-sm py-3 px-2 font-medium"
                    :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                'skills',
                                            'chart-heading font-semibold': tab != 'skills'
                                        }">
                    Skills
                </button>
                <button @click="tab = 'jobs'" class="w-full text-sm py-3 px-2 font-medium"
                    :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                'jobs',
                                            'chart-heading font-semibold': tab != 'jobs'
                                        }">
                    Talent Pool
                </button>

                <button @click="tab = 'plans'" class="w-full text-sm py-3 px-2 font-medium"
                    :class="{
                                            'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                                'plans',
                                            'chart-heading font-semibold': tab != 'plans'
                                        }">
                    Plans
                </button>
                {{--
                                        <button @click="tab = 'notes'" class="w-full text-sm py-3 px-2 font-medium" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'notes', 'chart-heading font-semibold': tab != 'notes' }">
                                            Notes
                                        </button>
                                    --}}
            </div>
            <div x-show="tab == 'summary'"
                class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                <h4 class="text-lg text-gray-700 font-semibold chart-heading mr-2">Summary</h4>
                <p class="text-sm">
                    {{ $selectedIndividual->summary ? $selectedIndividual->summary : 'No summary available' }}
                </p>
            </div>

            <div x-show="tab == 'career_history'"
                class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                <h4 class="text-lg text-gray-700 font-semibold chart-heading">Career History</h4>
                @if ($selectedIndividualCareerHistory && $selectedIndividualCareerHistory->isNotEmpty())
                <div class="flex flex-col items-start w-full">
                    @foreach ($selectedIndividualCareerHistory as $careerHistory)
                    <div class="flex h-max items-start justify-center mb-1">
                        <div
                            class="flex flex-col mt-1 items-center justify-center h-full w-2">
                            <div class="rounded-full  blueBalls bg-mainBlue"></div>
                            <div class="flex-grow border-l border-mainBlue"></div>
                        </div>
                        <div class="flex flex-col  items-start justify-start pl-4">
                            <h4 class="text-sm font-semibold text-gray-900">
                                {{ $careerHistory->role }}
                            </h4>
                            <div class="flex gap-x-2">
                                <span
                                    class="text-sm  font-base text-gray-700">{{ $careerHistory->company_name }}</span>
                            </div>
                            <div class="flex gap-x-2 mb-4">
                                <span
                                    class="text-xs font-base GrayText">{{ \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y') }}
                                    -
                                    {{ !empty($careerHistory->end_date) ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : 'Present' }}</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <p class="text-sm text-gray-500 mt-5">No career history available</p>
                @endif
            </div>

            <div x-show="tab == 'skills'"
                class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                <h4 class="text-lg text-gray-700 font-semibold chart-heading">Skills</h4>
                @if ($selectedIndividualSkills && $selectedIndividualSkills->isNotEmpty())
                <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                    @foreach ($selectedIndividualSkills as $skill)
                    <div
                        class="flex items-center text-sm p-2 rounded-xl text-white {{ $skill->skill_type == 'AI Generated' ? 'bg-purple-500' : 'bg-green-500' }}">
                        <span>{{ $skill->skill_name }}</span>
                    </div>
                    @endforeach
                </div>
                @else
                <p class="text-sm text-gray-500 mt-5">No skills available</p>
                @endif
            </div>
            <div x-show="tab == 'jobs'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                <h4 class="text-lg text-gray-700 font-semibold chart-heading">Talent Pools</h4>
                @if($userJobs && $userJobs->isNotEmpty())
                <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                    @foreach($userJobs as $job)
                    <div class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                        <span>{{ $job->name }}</span>
                    </div>
                    @endforeach
                </div>
                @else
                <p class="text-sm text-gray-500 mt-5">No talent pool available</p>
                @endif
            </div>
            <div x-show="tab == 'plans'"
                class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                <h4 class="text-lg text-gray-700 font-semibold chart-heading">Plans</h4>
                @if ($userPlans && $userPlans->isNotEmpty())
                <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                    @foreach ($userPlans as $plan)
                    <div
                        class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                        <span>{{ $plan->name }}</span>
                    </div>
                    @endforeach
                </div>
                @else
                <p class="text-sm text-gray-500 mt-5">No plans available</p>
                @endif
            </div>

            {{--
                                    <div x-show="tab == 'notes'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                        <h4 class="text-lg text-gray-700 font-semibold chart-heading">Notes</h4>
                                        @if ($selectedIndividual->notes)
                                            <p class="text-sm text-gray-600 font-normal">{{ $selectedIndividual->notes }}</p>
            @else
            <p class="text-sm text-gray-500 mt-5">No notes available</p>
            @endif
        </div>
        --}}
    </div>
</div>
<!-- <h4 class="text-sm text-cyan-500 font-medium">{{ $selectedIndividual->location }}</h4> -->
</div>
</div>
</div>

<div x-show="ropen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40"
    style="display:none">
    <!-- Modal background with a higher z-index -->
    <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
    <!-- Modal content with a lower z-index -->
    <div class="modal-content bg-white shadow-md rounded-lg border border-gray-300 z-50">
        <!-- Modal content -->
        <div class="flex justify-between border-b px-4 py-2">
            <h3 class="text-base text-lg font-bold">Report</h3>
            <img @click="closeReportModal" class="h-5 w-5 cursor-pointer"
                src="{{ asset('images/cross.svg') }}" alt="">
        </div>

        <div class="px-4 my-5">

            <div class="">
                <fieldset>
                    <legend class="text-black text-md font-light mb-2 font-normal text-gray-500">Tell us a bit
                        more about the problem with this profile</legend>
                    <div class="mt-1">
                        <div class="flex items-center gap-x-3">
                            <input type="radio" wire:model="problem" id="profile-fake"
                                class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black cursor-pointer"
                                value="1">
                            <label for="profile-fake"
                                class="block text-sm leading-6 text-gray-900 cursor-pointer">
                                This profile is fake
                            </label>
                        </div>
                        <div class="mt-1 flex items-center gap-x-3">
                            <input type="radio" wire:model="problem" id="no-longer-work"
                                class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black cursor-pointer"
                                value="2">
                            <label for="no-longer-work" class="block text-sm text-gray-900 cursor-pointer">
                                They no longer work at this company
                            </label>
                        </div>
                        <div class="mt-2 flex items-center gap-x-3">
                            <input type="radio" wire:model="problem" id="changed-role"
                                class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black cursor-pointer"
                                value="3">
                            <label for="changed-role" class="block text-sm text-gray-900 cursor-pointer">
                                They have changed role
                            </label>
                        </div>
                        <div class="mt-2 flex items-center gap-x-3">
                            <input type="radio" wire:model="problem" id="incorrect-info"
                                class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black cursor-pointer"
                                value="4">
                            <label for="incorrect-info" class="block text-sm text-gray-900 cursor-pointer">
                                The personal details gathered about this individual are
                                incorrect
                            </label>
                        </div>
                    </div>
                </fieldset>
            </div>

            <div class="mt-5">
                <label for="reportDescription" class="text-black text-md font-light mb-2 font-normal text-gray-500">Description</label>
                <textarea wire:model="reportDescription" type="text" id="reportDescription" placeholder="Enter description"
                    name="reportDescription" rows="4"
                    class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md">
                            </textarea>
            </div>
        </div>

        <!-- border above buttons -->
        <div class="w-full border-t  border-gray-200"></div>

        <div class="flex gap-4 my-5 w-full px-4">
            <button @click="closeReportModal" type="button"
                class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                <span class="block font-medium">Cancel</span>
            </button>
            <button type="button" wire:click="reportPerson({{ $selectedIndividual->id }})"
                @click="closeReportModal"
                class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                <span class="block">Report</span>
            </button>
        </div>
    </div>
</div>
@endif
<!-- View detail ended -->

</div>
@include('livewire.loading')

</div>
<script>
    document.addEventListener("DOMContentLoaded", function() {

        // Location chart
        var locationBarChart;
        var companyBarChart;
        const locationData = {
            labels: @json($locationLabels),
            datasets: [{
                label: 'People',
                data: @json($locationData),
                backgroundColor: [
                    '#3B82F6',
                    '#FFA347',
                    '#8B5CF6',
                ]
            }]
        };

        const companyData = {
            labels: @json($companyLabels),
            datasets: [{
                label: 'People',
                data: @json($companyData),
                backgroundColor: [
                    '#3B82F6',
                    '#FFA347',
                    '#8B5CF6',
                ]
            }]
        };

        const locationConfig = {
            type: 'bar',
            data: locationData,
            options: {
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                indexAxis: 'y',
                borderRadius: {
                    bottomLeft: 10,
                    bottomRight: 10,
                    topRight: 10,
                    topLeft: 10
                },
                scales: {
                    x: {
                        display: false, // Hide x-axis
                        beginAtZero: true // Start y-axis at zero
                    },
                    y: {
                        border: {
                            display: false,
                        },
                        grid: {
                            display: false, // Hide grid lines
                        },
                        ticks: {
                            display: true // Show y-axis labels
                        },
                        axis: {
                            display: false // Hide y-axis line
                        }
                    }
                },
                plugins: {
                    datalabels: {
                        display: true,
                        anchor: 'center',
                        align: 'center',
                        formatter: function(value, context) {
                            return value + ' (' + (value / context.dataset._meta[0].total * 100).toFixed(2) + '%)';
                        }
                    },
                    legend: {
                        display: false
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-location-legend');
                    ul.innerHTML = '';
                    // ul.style.justifyContent = 'space-between'
                    // ul.style.alignItems = 'center'
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.classList.add('child')
                        li.id = 'percentage'
                        li.style.marginBottom = '0px'
                        li.innerHTML = `${value}`;
                        ul.appendChild(li);
                    });
                    const children = ul.children;
                    const numChildren = children.length;
                    if (numChildren > 1) {
                        const maxPadding = 7;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    } else {
                        ul.style.justifyContent = 'center'
                    }
                    if (numChildren == 2) {
                        const maxPadding = 10;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    }
                }
            }]
        };

        const companyConfig = {
            type: 'bar',
            data: companyData,
            options: {
                responsive: true,
                maintainAspectRatio: false, // Allows control over height and width
                indexAxis: 'y',
                borderRadius: {
                    bottomLeft: 10,
                    bottomRight: 10,
                    topRight: 10,
                    topLeft: 10
                },
                scales: {
                    x: {
                        display: false, // Hide x-axis
                        beginAtZero: true // Start y-axis at zero
                    },
                    y: {
                        border: {
                            display: false,
                        },
                        grid: {
                            display: false, // Hide grid lines
                        },
                        ticks: {
                            display: true // Show y-axis labels
                        },
                        axis: {
                            display: false // Hide y-axis line
                        }
                    }
                },
                plugins: {
                    datalabels: {
                        display: true,
                        anchor: 'center',
                        align: 'center',
                        formatter: function(value, context) {
                            return value + ' (' + (value / context.dataset._meta[0].total * 100).toFixed(2) + '%)';
                        }
                    },
                    legend: {
                        display: false
                    }
                }
            },
            plugins: [{
                afterUpdate: function(chart) {
                    const ul = document.getElementById('custom-company-legend');
                    ul.innerHTML = '';
                    // ul.style.justifyContent = 'space-between'
                    // ul.style.alignItems = 'center'
                    const data = chart.data;
                    const dataset = data.datasets[0];
                    const total = dataset.data.reduce((sum, value) => sum + value, 0);

                    data.labels.forEach(function(label, i) {
                        const value = dataset.data[i];
                        const percentage = Math.round((value / total) * 100);
                        const color = dataset.backgroundColor[i];

                        const li = document.createElement('li');
                        li.classList.add('child')
                        li.id = 'percentage'
                        li.style.marginBottom = '0px'
                        li.innerHTML = `${value}`;
                        ul.appendChild(li);
                    });
                    const children = ul.children;
                    const numChildren = children.length;
                    if (numChildren > 1) {
                        const maxPadding = 7;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    } else {
                        ul.style.justifyContent = 'center'
                    }
                    if (numChildren == 2) {
                        const maxPadding = 10;
                        const minPadding = 2;
                        const dynamicPadding = Math.max(minPadding, maxPadding - (numChildren - 1)); // Decrease by 2px per child
                        console.log('dynamicPadding ', maxPadding - (numChildren - 1))

                        ul.style.setProperty('--dynamic-padding', `${dynamicPadding}%`);
                    }
                }
            }]
        };

        // Function to create and return a chart
        function createChart(ctx, config) {
            return new Chart(ctx, config);
        }

        // Function to update charts
        function updateCharts() {
            if (locationBarChart) {
                locationBarChart.destroy();
            }
            locationBarChart = createChart(document.getElementById('locationBar').getContext('2d'), locationConfig);

            if (companyBarChart) {
                companyBarChart.destroy();
            }
            companyBarChart = createChart(document.getElementById('companyBar').getContext('2d'), companyConfig);
        }

        // locationBarChart = new Chart(
        //     document.getElementById('locationBar'),
        //     locationConfig
        // );

        // companyBarChart = new Chart(
        //     document.getElementById('companyBar'),
        //     companyConfig
        // );

        // Initial chart creation
        var locationBarChart = createChart(document.getElementById('locationBar').getContext('2d'), locationConfig);
        var companyBarChart = createChart(document.getElementById('companyBar').getContext('2d'), companyConfig);


        // Listen for Livewire events to update chart data
        Livewire.on('updateChart', () => {
            updateCharts();
            // if (locationBarChart) {
            //     locationBarChart.destroy()
            // }
            // locationBarChart = new Chart(
            //     document.getElementById('locationBar'),
            //     locationConfig
            // );

            // if (companyBarChart) {
            //     companyBarChart.destroy()
            // }
            // companyBarChart = new Chart(
            //     document.getElementById('companyBar'),
            //     companyConfig
            // );
        });

        var selectStatus = document.getElementById('selectStatus');
        if (selectStatus) {
            var selectItems = selectStatus.nextElementSibling;

            selectStatus.addEventListener('click', function() {
                if (selectItems.classList.contains('d-none')) {
                    selectItems.classList.remove('d-none');
                } else {
                    selectItems.classList.add('d-none');
                }
            });
        }
    });
</script>
<script>
    document.getElementById('file-upload').addEventListener('change', function() {
        // Show the loading spinner
        document.getElementById('loading-spinner').classList.remove('hidden');

    });
</script>