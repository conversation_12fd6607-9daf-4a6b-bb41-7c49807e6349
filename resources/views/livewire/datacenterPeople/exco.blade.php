<div class="bg-white shadow-lg rounded-xl p-4">
    <dl class="text-lg font-medium leading-6 text-gray-500">Exco</dl>
    @if($divisionData)
        <div class="h-80 w-full px-5 mb-5" wire:ignore
            x-data="{
                tvalues: {{ json_encode($divisionData) }},
                tlabels: {{ json_encode($divisionLabels) }},
                init() {
                    let chart = new ApexCharts(this.$refs.excochart, {
                        chart: {
                            type: 'donut',
                            width: '100%',
                            height: '100%'
                        },
                        labels: this.tlabels,
                        dataLabels: {
                            enabled: true,
                            textAnchor: 'start',
                            style: { fontSize: '9px' }
                        },
                        colors: ['#8B5CF6', '#F9C21A', '#9C27B0', '#0284c7'],
                        series: this.tvalues,
                        fill: { colors: ['#8B5CF6', '#F9C21A', '#9C27B0', '#0284c7'] },
                        stroke: { width: 1 },
                        legend: {
                            show: true,
                            position: 'right',
                            markers: { fillColors: ['#8B5CF6', '#F9C21A', '#9C27B0', '#0284c7'] },
                            
                        },
                        tooltip: { fillSeriesColor: true },
                        plotOptions: {
                            pie: {
                                donut: {
                                    size: '80%' // Adjust this value to control the width of the donut chart's circle
                                }
                            }
                        },
                        responsive: [{
                                breakpoint: 1024,
                                options: { chart: { width: '100%', position: 'center' } }
                            },
                            {
                                breakpoint: 768,
                                options: { chart: { width: '100%', height: '100%', position: 'center' } }
                            }
                        ]
                    })
                    chart.render()
                }
            }">
            <div x-ref="excochart"></div>
        </div>
    @else
        <p class="py-10 text-gray-500 mb-10 mt-10 text-center">No Record(s) Found</p>
    @endif
</div>