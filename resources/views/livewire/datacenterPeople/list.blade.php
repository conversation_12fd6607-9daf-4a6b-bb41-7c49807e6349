<div class="px-10 lightgray pb-10">
    <div class="bg-white border-2 rounded-xl w-full h-max px-4 ">

        {{-- header above table --}}
        <div class="py-4 w-full flex justify-between items-start gap-4">

            <button
                @click="drawer = true"
                class="py-1.5 px-1.5 w-full w-20p text-black ring-1 ring-inset ring-gray-300  border border-gray-300 rounded-lg flex justify-center items-center gap-1 font-medium">
                <img class="search-icon w-auto mr-2" src="{{ asset('/images/search-icon.svg') }}"
                    alt="Search Icon">
                <span class="text-md"> Advanced Search</span>
            </button>

            <div class="w-40p items-center col-span-2">
              
                <div class="flex item-center gap-1">
                    <input type="text" wire:model="searchByKeyword"
                        class="p-4 outline-none w-full block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6"
                        placeholder="Search">
                        <button wire:click="searchRecordByKeyword" class="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium text-white text-md justify-center px-5">
                            <img class="h-4 font-bold w-auto img mr-2"
                                src="{{ asset('/images/MagnifyingGlassWhite.svg') }}">
                            <span>Search</span>
                        </button>
                </div>
                <label class="text-sm text-gray-500">To search by multiple criteria, Please enter the keywords comma(,) separated</label>
            </div>

            <div class="flex relative w-20p z-20 w-full col-span-1 items-center">
                <label for="sortBy" class="block text-md font-medium labelcolor w-24">Sort By</label>
                <select wire:model.live="sortBy" id="sortBy"
                    class="flex justify-between select-selected px-4 py-2 w-full border border-gray-300 rounded-lg z-auto shadow-sm cursor-pointer bg-white text-gray-500">
                    <option value="tenure">Tenure</option>
                    <option value="created_at">Date Added</option>
                    <option value="start_date">Date Joined</option>
                </select>
               
            </div>
            <div class="flex relative w-20p z-20 w-full col-span-1">
                <select wire:model.live="sortDirection" id="sortBy"
                    class="flex justify-between select-selected px-4 py-2 w-full border border-gray-300 rounded-lg z-auto shadow-sm cursor-pointer bg-white text-gray-500">
                    <option value="asc">Ascending</option>
                    <option value="desc">Descending</option>
                </select>
            </div>
        </div>
        
        <div class="TableOuter relative rounded-2xl h-full bg-white border mb-5">
            <div class="table_wrapper rounded-2xl people-dashboard">
                <table class="tableEdges JobPeopleTable w-full overflow-x-auto">
                    <thead class="rounded-2xl">
                        <tr class="grayBackground GrayText">
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">People ID</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Name</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Company</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Country</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Function</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Potential Sex</th>
                                                      <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Tenure</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Readiness</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Registration Status</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @if($peoples->isNotEmpty())
                            @foreach ($peoples as $people)
                                <tr>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $people->id }}</td>
                                    <td class=" px-3 py-3 text-md font-normal">
                                        <div class="flex flex-col">
                                            <span>{{ $people->forename }} {{ $people->surname }}</span>
                                            <span class="truncate GrayText w-48 text-sm">{{ $people->latest_role }}</span>
                                        </div>
                                    </td>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $people->company_name }}</td>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $people->country }}</td>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $people->function }}</td>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $people->gender }}</td>
                                  
                                    
                                    <td class=" px-3 py-3 text-md font-normal">{{ $people->tenure }}</td>
                                    <td class=" px-3 py-3 text-sm">
                                        @if($people->readiness)
                                            @if(strtolower($people->readiness) == 'ready')
                                                <span class="py-2 px-4 rounded-lg font-medium redlinessReaddy">
                                                    Ready
                                                </span>
                                            @elseif(strtolower($people->readiness) == 'not ready')
                                                <span class="py-2 px-4 rounded-lg font-medium RedBG text-red-500">
                                                    Not Ready
                                                </span>
                                            @endif
                                        @endif
                                    </td>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $people->other_tags }}</td>
                                    <td>
                                        <div class="dropdown-container relative">
                                            <button tabindex="1" id="dropdownDefaultButton"
                                                data-dropdown-toggle="dropdown"
                                                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-md py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                type="button">
                                                <img class="h-5 w-5"
                                                    src="{{ asset('images/DotsThreeVertival.svg') }}">
                                            </button>

                                            <!-- Dropdown menu -->
                                            <div id="dropdown"
                                                class="dropdown-menu hidden z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                                <ul class="py-2 text-md text-gray-700 dark:text-gray-200"
                                                    aria-labelledby="dropdownDefaultButton">
                                                    <li class="cursor-pointer">
                                                        <a class="p-2" @click="vopen=true" wire:click.prevent="viewIndividual({{ $people->id }})">
                                                            <span class="font-semibold text-md">
                                                                View
                                                            </span>
                                                        </a>
                                                    </li>
                                                    <li class="cursor-pointer">
                                                        <a class="p-2" wire:click.prevent="editIndividual({{ $people->id }})">
                                                            <span class="font-semibold text-md">
                                                                Edit
                                                            </span>
                                                        </a>
                                                    </li>
                                                    <li class="cursor-pointer">
                                                        <a class="p-2" @click="ropen=true" wire:click.prevent="viewIndividual({{ $people->id }})">
                                                            <span class="font-semibold text-md">Report</span>
                                                        </a>
                                                    </li>
                                                    <li class="cursor-pointer">
                                                        <a @click="confirmDelete({{ $people->id }}, '{{ $people->forename }}', '{{ $people->surname }}', '{{ asset('images/redTrashIcon.svg') }}')"
                                                        class="p-2">
                                                            <span class="font-semibold text-md">Delete</span>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr class="text-center">
                                <td colspan="10" class="py-10 text-gray-500">No Record(s) Found</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <div class="flex absolute bottom-0 w-full px-2 gap-4 paginationContainer border-t py-4">
                <select wire:model.live="perPage" id="perPage"
                    class="form-select bg-white border rounded-lg outline-none GrayText py-2">
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="30">30</option>
                    <option value="40">40</option>
                    <option value="50">50</option>
                </select>
            </div>
            <div class="pl">
                {{ $peoples->links() }}
            </div>
        </div>
    </div>
</div>