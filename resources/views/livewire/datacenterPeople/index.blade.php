<div class="customHeight grayBackground overflow-y-scroll">
    @include('livewire.flashMessage')

    <div class="flex lg:items-center justify-between bg-white border-t border-b font-semibold px-10 py-4">
        <div class="flex gap-4 items-center">
            <a href="{{ route('datacenter.index') }}" class="flex gap-2 mainBlue items-center">
                <img class="" src="{{ asset('/images/ArrowLeftBlue.svg') }}">
                <span>Back</span>
            </a>
            <h1 class="whitespace-nowrap text-3xl font-medium">Peoples</h1>
        </div>
    </div>

    <div class="lightgray ContentContainer" x-data="{
    drawer: false, 
    vopen: false, 
    ropen: false, 
    tab: 'summary', 
    editPeoplePopup: @entangle('editPeoplePopup'),
    editPeopleCareerHistoryPopup: @entangle('editPeopleCareerHistoryPopup'),
    editPeopleSkillsPopup: @entangle('editPeopleSkillsPopup'),
    closeDrawer() { this.drawer = false; this.$dispatch('clearFilters'); }, 
    closeViewModal() { this.vopen = false; this.$dispatch('clearSelectedIndividuals'); }, 
    closeReportModal() { this.ropen = false; this.$dispatch('clearSelectedIndividuals'); }
    }">
        <div class=" grid grid-cols-2 px-10 shadow-2xl gap-4 w-full h-full py-4">
            <div class="flex-1 bg-white shadow-lg rounded-xl p-4">
                <dl class="text-lg font-medium leading-6 text-gray-500">Number of People</dl>
                <dd class="w-full flex-none text-xl font-medium leading-10 tracking-tight text-gray-900">
                    {{ $totalPeople }}
                </dd>
            </div>
            <div class="flex-1 bg-white shadow-lg rounded-xl p-4">
                <dl class="text-lg font-medium leading-6 text-gray-500">Number of Companies</dl>
                <dd class="w-full flex-none text-xl font-medium leading-10 tracking-tight text-gray-900">
                    {{ $totalCompanies }}
                </dd>
            </div>

            {{-- exco --}}
            @include('livewire.datacenterPeople.exco')

            {{-- companies --}}
            @include('livewire.datacenterPeople.companies')

            {{-- gender split --}}
            @include('livewire.datacenterPeople.genderSplit')

            {{-- locations --}}
            @include('livewire.datacenterPeople.locations')

        </div>

        @include('livewire.datacenterPeople.list')

        <!-- Advance Search Started -->

        @include('livewire.datacenterPeople.advanceSearch')

        @if ($selectedIndividualID)

        @include('livewire.datacenterPeople.view')
        @include('livewire.datacenterPeople.report')

        @endif

        @if($editIndividualID)
        @include('livewire.datacenterPeople.edit')
        @include('livewire.datacenterPeople.careerHistory')
        @include('livewire.datacenterPeople.skills')
        @endif


    </div>

    @include('livewire.loading')

  

</div>

<script>
        function confirmDelete(peopleId, forename, surname, iconUrl) {
            Swal.fire({
                // title: "Do you want to delete this plan?",
                html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Delete ${forename + ' ' + surname}</h2>
                    <p class="px-5 font-normal">Are you sure want to delete the people <b>${forename + ' ' + surname}</b>?</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
                showDenyButton: true,
                showCancelButton: false,
                confirmButtonText: "Delete",
                denyButtonText: `Cancel`,
                reverseButtons: true,
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                    denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
                },
                showCloseButton: true
            }).then((result) => {
                /* Read more about isConfirmed, isDenied below */
                if (result.isConfirmed) {
                    Livewire.dispatch('deletePeople', {
                        peopleId: peopleId
                    });
                }
            });
        }
    </script>