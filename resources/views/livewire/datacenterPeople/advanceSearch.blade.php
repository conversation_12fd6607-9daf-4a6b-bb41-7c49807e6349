<div x-show="drawer" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50 right-0" style="display:none">
            
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
    
    <div x-show="drawer" x-transition:enter="transition transform ease-out duration-300" x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition transform ease-in duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full" class="z-50 bg-white absolute right-0 shadow-lg overflow-hidden p-0">
        <div class="flex justify-between mt-2 h-5 px-4 py-2 mb-5">
            <h2 class="font-semibold">
                Advanced Search
            </h2>
            <img @click="drawer = false" class="h-5 w-5 cursor-pointer" src="{{asset('images/cross.svg')}}" alt="">
        </div>
        <div class="border-t mt-3 border-gray-200"></div>
        <div class=" advanced_search_drawer relative overflow-y-scroll">
            <div class="flex gap-y-2 flex-col p-4">
                <div class="">
                    <label for="name" class="block text-md font-medium labelcolor">First Name</label>
                    <input wire:model="forename" type="text" id="name" placeholder="Enter first name" name="name" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                </div>
                <div class="">
                    <label for="name" class="block text-md font-medium labelcolor">Last Name</label>
                    <input wire:model="surname" type="text" id="name" placeholder="Enter last name" name="name" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                </div>
                <div class="">
                        <fieldset>
                            <legend class="text-md font-medium labelcolor">Gender</legend>
                            <ul class="donate-now mt-1">
                                <li>
                                    <input type="radio" id="Male" name="gender" value="Male" class="cursor-pointer" wire:model="gender" />
                                    <label for="Male" class="text-center font-semibold labelcolor">Male</label>
                                </li>
                                <li>
                                    <input type="radio" id="Female" name="gender" value="Female" class="cursor-pointer" wire:model="gender" />
                                    <label for="Female" class="text-center font-semibold labelcolor">Female</label>
                                </li>
                            </ul>
                        </fieldset>
                    </div>
                <div class="flex flex-col gap-y-2">
                    <h2 class="block text-md font-medium labelcolor">Location</h2>
                    <div class="text-md" wire:ignore x-data="{
                        multiple: true,
                        value: @entangle('location'),
                        options: {{ json_encode($locations) }},
                        init() {
                                this.$nextTick(() => {
                                    let choices = new Choices(this.$refs.select)
                    
                                    let refreshChoices = () => {
                                        let selection = this.multiple ? this.value : [this.value]
                    
                                        choices.clearStore()
                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                            value,
                                            label,
                                            selected: selection.includes(value),
                                        })))
                                    }
                    
                                    refreshChoices()
                    
                                    this.$refs.select.addEventListener('change', () => {
                                        this.value = choices.getValue(true)
                                    })
                    
                                    this.$watch('value', () => refreshChoices())
                                    this.$watch('options', () => refreshChoices())
                                })
                            }
                        }" class="bg-white text-md max-w-sm w-full"
                    >
                        <select x-ref="select"></select>
                    </div>
                </div>
                <div class="">
                    <label for="selectedRole" class="block text-md font-medium labelcolor">Role</label>
                    <input wire:model="selectedRole" type="text" id="selectedRole" placeholder="Enter role" name="selectedRole" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                </div>
                <div class="">
                    <label for="previousRole" class="block text-md font-medium labelcolor">Previous Role</label>
                    <input wire:model="previousRole" type="text" id="previousRole" placeholder="Enter previous role" name="selectedRole" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                </div>
                <div class="flex flex-col gap-y-2">
                    <h2 class="block text-md font-medium labelcolor">Company</h2>
                    <div class="text-md" wire:ignore x-data="{
                                    multiple: false,
                                    value: @entangle('selectedCompany'),
                                    options: {{ json_encode($companiesListToSearch) }},
                                    init() {
                                            this.$nextTick(() => {
                                                let choices = new Choices(this.$refs.select)
                                
                                                let refreshChoices = () => {
                                                    let selection = this.multiple ? this.value : [this.value]
                                
                                                    choices.clearStore()
                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                        value,
                                                        label,
                                                        selected: selection.includes(value),
                                                    })))
                                                }
                                
                                                refreshChoices()
                                
                                                this.$refs.select.addEventListener('change', () => {
                                                    this.value = choices.getValue(true)
                                                })
                                
                                                this.$watch('value', () => refreshChoices())
                                                this.$watch('options', () => refreshChoices())
                                            })
                                        }
                                    }" class="bg-white text-md max-w-sm w-full">
                        <select x-ref="select"></select>
                    </div>
                </div>
                <div class="flex flex-col gap-y-2">
                    <h2 class="block text-md font-medium labelcolor">Function</h2>
                    <div class="text-md" wire:ignore x-data="{
                                    multiple: false,
                                    value: @entangle('selectedFunctionsValue'),
                                    options: {{ json_encode($functionsValue) }},
                                    init() {
                                            this.$nextTick(() => {
                                                let choices = new Choices(this.$refs.select)
                                
                                                let refreshChoices = () => {
                                                    let selection = this.multiple ? this.value : [this.value]
                                
                                                    choices.clearStore()
                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                        value,
                                                        label,
                                                        selected: selection.includes(value),
                                                    })))
                                                }
                                
                                                refreshChoices()
                                
                                                this.$refs.select.addEventListener('change', () => {
                                                    this.value = choices.getValue(true)
                                                })
                                
                                                this.$watch('value', () => refreshChoices())
                                                this.$watch('options', () => refreshChoices())
                                            })
                                        }
                                    }" class="bg-white text-md max-w-sm w-full">
                        <select x-ref="select"></select>
                    </div>
                </div>
                <div class="">
                    <label for="name" class="block text-md font-medium labelcolor">Regulatory Bodies</label>
                    <input wire:model="regBodies" type="text" id="name" placeholder="Enter regulatory bodies" name="name" class="mt-1 placeholder:text-gray-500  block w-full p-2 outline-none border border-gray-300 rounded-md" />
                </div>
                <div class="mt-2">
                    <label for="name" class="block text-md font-medium labelcolor">Minimum Tenure</label>
                    <div class="Tenure mt-1 justify-between">
                        {{--
                            <button
                                class="block w-8 border-2 px-1 rounded-md @if (!$min_exp) disabled @endif"
                                wire:click="changeMinimumTenure('decrease')"
                                @if (!$min_exp) disabled @endif>
                                <img class="search-icon w-8 h-8 bg-white"
                                    src="{{ asset('images/Minus.svg') }}"
                                    alt="Search Icon">
                            </button>
                        --}}
                        <input type="number" wire:model="min_exp" min="0"
                            inputmode="numeric"
                            class="w-[75%] outline-none block text-center bg-white p-2 text-md font-normal border border-gray-300 h-10 rounded-md w-full"
                            placeholder="0">
                        {{--
                            <button class="block w-8 px-1 border-2  rounded-md"
                                wire:click="changeMinimumTenure('increase')">
                                <img class="search-icon w-8 h-8  bg-white"
                                    src="{{ asset('images/Plus.svg') }}"
                                    alt="Search Icon">
                            </button>
                        --}}
                    </div>
                </div>
            </div>
            <div class="sticky bottom-0 w-full border-t mt-2 pt-2 pb-2 bg-white">
                <div class="p-4 flex items-center justify-between">
                    <button class="border text-md text-black rounded-xl py-2 advanced-search-clear font-medium" wire:click="clearFilters">Reset</button>
                    <button class="border text-sm px-2 text-white rounded-xl py-2 font-medium bg-cyan-500 hover:text-white" wire:click="runfilters" @click="drawer = false">Show Results</button>
                </div>
            </div>
        </div>
    </div>
</div>