<div x-show="ropen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" style="display:none">
    <!-- Modal background with a higher z-index -->
    <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
    <!-- Modal content with a lower z-index -->
    <div class="modal-content bg-white shadow-md rounded-lg border border-gray-300 z-50">
        <!-- Modal content -->
        <div class="flex justify-between border-b px-4 py-2">
            <h3 class="text-base text-lg font-bold">Report</h3>
            <img @click="closeReportModal" class="h-5 w-5 cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="">
        </div>

        <div class="px-4 my-5">
            <fieldset>
                <legend class="text-black text-md font-light mb-2 font-normal text-gray-500">Tell us a bit
                    more about the problem with this profile</legend>
                <div class="mt-1">
                    <div class="flex items-center gap-x-3">
                        <input type="radio" wire:model="problem" id="profile-fake" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black cursor-pointer" value="1">
                        <label for="profile-fake" class="block text-sm leading-6 text-gray-900 cursor-pointer">
                            This profile is fake
                        </label>
                    </div>
                    <div class="mt-1 flex items-center gap-x-3">
                        <input type="radio" wire:model="problem" id="no-longer-work" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black cursor-pointer" value="2">
                        <label for="no-longer-work" class="block text-sm text-gray-900 cursor-pointer">
                            They no longer work at this company
                        </label>
                    </div>
                    <div class="mt-2 flex items-center gap-x-3">
                        <input type="radio" wire:model="problem" id="changed-role" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black cursor-pointer" value="3">
                        <label for="changed-role" class="block text-sm text-gray-900 cursor-pointer">
                            They have changed role
                        </label>
                    </div>
                    <div class="mt-2 flex items-center gap-x-3">
                        <input type="radio" wire:model="problem" id="incorrect-info" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black cursor-pointer" value="4">
                        <label for="incorrect-info" class="block text-sm text-gray-900 cursor-pointer">
                            The personal details gathered about this individual are
                            incorrect
                        </label>
                    </div>
                </div>
            </fieldset>
        </div>

        <!-- border above buttons -->
        <div class="w-full border-t  border-gray-200"></div>

        <div class="flex gap-4 my-5 w-full px-4">
            <button @click="closeReportModal" type="button" class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                <span class="block font-medium">Cancel</span>
            </button>
            <button type="button" wire:click="reportPerson({{ $selectedIndividual->id }})" @click="closeReportModal" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                <span class="block">Report</span>
            </button>
        </div>
    </div>
</div>