<div x-show="editPeopleSkillsPopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

    <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50 relative"> <!-- Added relative positioning -->
        <!-- Cross icon -->
        <img x-on:click="editPeopleSkillsPopup = false" class="absolute top-2 right-2 w-5 h-5 cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Close Icon">

        <h2 class="font-semibold text-lg px-4">Edit ({{$editForename}} {{$editSurname}}) Skills</h2>
        <div class="w-full border-t mt-3 border-gray-200"></div>

        <!-- Scrollable content area with increased height -->
        <div class="overflow-y-auto max-h-96">
            @foreach($editPeopleSkills as $index => $history)
            <div class="grid grid-cols-1 gap-3 mt-3 items-center">
                <div class="flex flex-wrap gap-3">
                    <div class="careerHistoryEndDate">
                        <label for="editEndDate" class="text-base font-medium labelcolor">Skill</label>
                        <input wire:model.defer="editPeopleSkills.{{$index}}.skill_name" type="text" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md" placeholder="Skill">
                    </div>
                    <div class="flex justify-start items-center" style="position: relative; top: 7px;">
                        <button wire:click="removeSkillsRow({{ $index }})" type="button" class="text-red">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
                <!-- Delete Button -->
            </div>
            @endforeach

            <!-- Add Button -->
            <div class="flex justify-start items-center mt-4 mb-2">
                <button wire:click="addSkillsRow" type="button" class="text-green-600 py-2 px-3 rounded-lg bg-cyan-500 font-medium text-white hover:text-green-800">
                    <i class="fas fa-plus-circle"></i> Add New Entry
                </button>
            </div>
        </div>

        <div class="mt-3 flex justify-start">
            <button wire:click="updateSkills" type="button" class="mr-6 bg-blue-500 text-white font-semibold py-2 px-3 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                Update
            </button>
        </div>
    </div>
</div>
