<div x-show="editPeoplePopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

    <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50 w-full max-w-3xl relative">
        <!-- cross icon -->
        <img x-on:click="editPeoplePopup = false" class="absolute top-2 right-2 w-6 h-6 cursor-pointer"
            src="{{ asset('images/cross.svg') }}" alt="Close Icon">

        <h2 class="font-semibold px-4">Edit {{$editForename}} {{$editSurname}}</h2>
        <div class="w-full border-t mt-3 border-gray-200"></div>

        <!-- First Row -->
        <div class="grid grid-cols-4 gap-4 mt-4">
            <div>
                <label for="editForename" class="text-base font-medium labelcolor">First Name</label>
                <input type="text" wire:model="editForename" id="editForename" placeholder="Enter First Name" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editSurname" class="text-base font-medium labelcolor">Last Name</label>
                <input type="text" wire:model="editSurname" id="editSurname" placeholder="Enter Last Name" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editGender" class="text-base font-medium labelcolor">Gender</label>
                <select id="editGender" wire:model="editGender" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                    <option value="" disabled selected>Select Gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Not Applicable">Not Applicable</option>
                </select>
            </div>
            <div>
                <label for="editDiverse" class="text-base font-medium labelcolor">Diverse</label>
                <input type="text" wire:model="editDiverse" id="editDiverse" placeholder="Enter Diversity Status" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editCountry" class="text-base font-medium labelcolor">Country</label>
                <input type="text" wire:model="editCountry" id="editCountry" placeholder="Enter Country" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editCity" class="text-base font-medium labelcolor">City</label>
                <input type="text" wire:model="editCity" id="editCity" placeholder="Enter City" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
        </div>

        <!-- Second Row -->
        <div class="grid grid-cols-4 gap-4 mt-4">
            <div>
                <label for="editLinkedIn" class="text-base font-medium labelcolor">LinkedIn URL</label>
                <input type="text" wire:model="editLinkedIn" id="editLinkedIn" placeholder="Enter LinkedIn URL" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editLatestRole" class="text-base font-medium labelcolor">Latest Role</label>
                <input type="text" wire:model="editLatestRole" id="editLatestRole" placeholder="Enter Latest Role" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editExco" class="text-base font-medium labelcolor">Exco</label>
                <input type="text" wire:model="editExco" id="editExco" placeholder="Enter Exco Status" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editCompany" class="text-base font-medium labelcolor">Company</label>
                <div wire:ignore x-data="{
                    multiple: false,
                    value: @entangle('editCompany'),
                    options: {{ json_encode($companies) }},
                    init() {
                        this.$nextTick(() => {
                            let choices = new Choices(this.$refs.selectCompanies, {
                                allowHTML: true,
                                removeItems: true,
                                removeItemButton: true,
                                placeholder: true,
                                placeholderValue: 'Select company',
                                classNames: {
                                    containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'], // Split into an array
                                    inputCloned: ['mb-0', 'p-0'],
                                },
                                noChoicesText: 'No companies to choose from'
                            });

                            let refreshChoices = () => {
                                let selection = this.value ? [this.value] : [];

                                choices.clearStore();
                                choices.setChoices(this.options.map(({ value, label }) => ({
                                    value,
                                    label,
                                    selected: selection.includes(value),
                                })));
                            };
                            refreshChoices();

                            this.$refs.selectCompanies.addEventListener('change', () => {
                                this.value = choices.getValue(true);
                            });

                            this.$watch('value', () => refreshChoices());
                            this.$watch('options', () => refreshChoices());
                        });
                    }
                }" class="text-black w-full">
                    <select x-ref="selectCompanies"></select>
                </div>

            </div>
        </div>

        <div class="grid grid-cols-4 gap-4 mt-4">
            <div>
                <label for="editStartDate" class="text-base font-medium labelcolor">Start Date</label>
                <input type="date" wire:model="editStartDate" id="editStartDate" placeholder="Enter Start Date" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editTenure" class="text-base font-medium labelcolor">Tenure</label>
                <input type="text" wire:model="editTenure" id="editTenure" placeholder="Enter Tenure" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editFunction" class="text-base font-medium labelcolor">Function</label>
                <input type="text" wire:model="editFunction" id="editFunction" placeholder="Enter Function" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editDivision" class="text-base font-medium labelcolor">Division</label>
                <input type="text" wire:model="editDivision" id="editDivision" placeholder="Enter Division" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
        </div>
        <div class="grid grid-cols-4 gap-4 mt-4">
            <div>
                <label for="editOtherTags" class="text-base font-medium labelcolor">Other Tags</label>
                <input type="text" wire:model="editOtherTags" id="editOtherTags" placeholder="Enter Other Tags" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editStatus" class="text-base font-medium labelcolor">Status</label>
                <input type="text" wire:model="editStatus" id="editStatus" placeholder="Enter Status" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
            <div>
                <label for="editReadiness" class="text-base font-medium labelcolor">Readiness</label>
                <input type="text" wire:model="editReadiness" id="editReadiness" placeholder="Enter Readiness" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
            </div>
        </div>

        <div class="mt-4">
            <div>
                <label for="editSummary" class="text-base font-medium labelcolor">Summary</label>
                <textarea id="editSummary" wire:model="editSummary" placeholder="Enter Summary" rows="4" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 rounded-md">
                </textarea>
            </div>
        </div>

        <div class="mt-4 flex justify-start">
            <button wire:click="updateIndividual" type="button" class="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                Update
            </button>
            <button wire:click="editPeopleCareerHistoryPopup = true" type="button" class="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                Update Career History
            </button>
            <button wire:click="editPeopleSkillsPopup = true" type="button" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                Update Skills
            </button>



        </div>




    </div>
</div>