<div>
    <div class="flex items-center px-10 py-4 bg-sky-900">
        <h1 class="whitespace-nowrap px-4 text-white text-3xl font-semibold">SAM - Simply Ask Me</h1>
    </div>
    <div class="my-5 px-24">
        <div class="w-full flex border border-gray-200 rounded-3xl bg-white shadow-md p-4">
            <div class="border-r border-gray-200 w-96 px-5">
                <div class="flex w-full items-center justify-center">
                <a class="bg-cyan-500 py-2 px-3 rounded-full text-sm text-white hover:bg-cyan-600" href="{{ route('builder.index') }}">New Topic</a>
                </div>
                <h2 class="mt-5 text-sky-800 text-center text-xl text-black font-bold">My History</h2>
                <div class="mt-5">
                    @foreach($topics as $topic)
                    <div class="mt-5">
                        <button wire:click="showTopic('{{ $topic }}')" class="text-black text-left font-light text-sm hover:text-cyan-500">{{ $topic }}</button>
                    </div>
                    @endforeach
                </div>
            </div>
            <div class="w-full">
                <div>
                </div>
                <div class="ml-5">
                    <div class="h-192 bg-gray-100 p-4 mb-3 overflow-y-scroll">
                        
                        @if($selectedTopic->isEmpty())
                        <div class="flex items-start gap-2.5">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center">
                                <img class="w-10 h-10" src="images/technical-support.png" alt="Sam image">
                            </div>
                            <div class="flex flex-col gap-1 w-full">
                                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                    <span class="text-sm font-semibold text-gray-900 dark:text-gray-800">SAM</span>
                                </div>
                                <div class="flex flex-col leading-1.5 p-4 border-gray-300 rounded-e-xl rounded-es-xl bg-white shadow-md">
                                    <p class="text-sm font-normal text-gray-900">Hi {{$user[0]->name}}, welcome to my page ask me a question below to discover quick insights i'll try to help you as much as I can!</p>
                                </div>
                            </div>
                        </div>
                        @else
                            @foreach($selectedTopic as $chat)
                            <div class="mb-5 flex items-start gap-2.5">
                                <div class="w-10 h-10 rounded-full flex items-center bg-white justify-center">
                                    <img class="w-10 h-10" src="images/profile.png" alt="user image">
                                </div> 
                                <div class="flex flex-col gap-1 w-full">
                                    <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                        <span class="text-sm font-semibold text-gray-900">{{ $user[0]->name }}</span>
                                        <span class="text-sm font-normal text-gray-500 dark:text-gray-400">{{ $chat->created_at->format('H:i') }}</span>
                                    </div>
                                    <div class="flex flex-col leading-1.5 p-4 border-gray-200 bg-gray-100 rounded-e-xl rounded-es-xl bg-sky-100 shadow-md">
                                        <p class="text-sm font-normal text-gray-900">{{ $chat->question }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-5 flex items-start gap-2.5">
                                <div class="flex flex-col gap-1 w-full">
                                    <div class="flex w-full text-right items-center space-x-2 rtl:space-x-reverse justify-end">
                                        <span class="text-sm font-semibold text-gray-900">SAM</span>
                                    </div>
                                    <div class="flex flex-col leading-1.5 p-4 border-gray-300 rounded-b-xl rounded-tl-xl bg-white shadow-md">
                                        <p class="text-sm font-normal text-gray-900">{{ $chat->response }}</p>
                                    </div>
                                </div>
                                <div class="w-10 h-10 rounded-full flex items-center justify-center">
                                    <img class="w-10 h-10" src="images/technical-support.png" alt="Sam image">
                                </div>
                            </div>
                            @endforeach
                        @endif
                    </div>
                    <div class="flex p-2 border border-gray-300 rounded-xl w-full bg-gray-50">
                        <textarea type="text" wire:model="question" rows="1" class="w-full bg-transparent text-sm font-normal text-gray-900 form-input block hover:text-cyan-500 focus:outline-none" placeholder="Ask me something..."></textarea>
                        <button type="submit" wire:click="sendMessage" class="text-gray-300 hover:text-cyan-500 font-bold py-1 px-2 rounded">
                            <svg class="h-6 w-6 justify-items-center bg-none" fill="" viewBox="0 0 24 24" aria-hidden="true">>@svg('gmdi-send-r')</svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('livewire.loading') 
</div>
