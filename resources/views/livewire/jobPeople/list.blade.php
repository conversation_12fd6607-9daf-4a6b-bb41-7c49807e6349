<div class="px-10 lightgray pb-10">
<div class="bg-white border-2 rounded-xl w-full h-max px-4">

        {{-- header above table --}}
        <div class="pt-4 w-full flex justify-between items-start gap-4">

            <button
                @click="drawer = true"
                class="py-2 px-1.5 text-black  border rounded-lg flex justify-center items-center gap-1 font-medium">
                <img class="search-icon w-auto mr-2" src="{{ asset('/images/search-icon.svg') }}"
                    alt="Search Icon">
                <span class="text-md"> Advanced Search</span>
            </button>

            <div class="w-40p items-center col-span-2">
                <!-- <img class="search-icon people-dash h-4 w-auto" src="{{ asset('/images/MagnifyingGlass.svg') }}"
                    alt="Search Icon">
                <input type="text" wire:model.live.debounce.600ms="searchByKeyword"
                    class="p-4 outline-none w-full  block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6"
                    placeholder="Search"> -->

                    <div class="flex flex-1 gap-x-2 px-2 py-2 border border-[#EAECF0] justify-start items-center bg-white rounded-lg">
                    <input type="text" wire:model="searchByKeyword"
                        class="px-2  w-full block  text-gray-900 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6"
                        placeholder="Search">
                        <img wire:click="searchRecordByKeyword"  class="search-icon w-auto mr-2" src="{{ asset('/images/search-icon.svg') }}" alt="Search Icon">    
                        
                </div>
                <label class="text-sm text-gray-500">To search by multiple criteria, Please enter the keywords comma(,) separated</label>
            </div>

            <div class="flex relative w-20p z-20 w-full col-span-1 items-center">
                <label for="sortBy" class="block text-md font-medium labelcolor w-24">Sort By</label>
                <select wire:model.live="sortBy" id="sortBy"
                    class="flex justify-between select-selected px-4 py-2 w-full border border-gray-300 rounded-lg z-auto shadow-sm cursor-pointer bg-white text-gray-500">
                    <option value="tenure">Tenure</option>
                    <option value="created_at">Date Added</option>
                    <option value="start_date">Date Joined</option>
                </select>
                    <!-- <div class=" flex justify-between select-selected px-4 py-2 w-full border border-gray-300 rounded-lg z-auto shadow-sm cursor-pointer bg-white text-gray-500"
                        id="selectStatus">
                        <span> Sort by</span>
                        <img src="{{ asset('/images/arrowDown.svg') }}" alt="arrowDown">
                    </div>
                    <div class="select-items absolute w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10 d-none">
                        <div class="flex items-center px-4 py-2 hover:bg-gray-100 ">
                            <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="active" id="activeStatus"  wire:click="Ordermytable('tenure')">
                            <label class="activeColor p-1 rounded-lg px-2 text-sm block cursor-pointer" for="activeStatus"> Tenure</label>
                        </div>
                        <div class="flex items-center px-4 py-2 hover:bg-gray-100">
                            <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="draft" id="draftStatus" wire:click="Ordermytable('created_at')">
                            <label class="DraftColor  p-1 rounded-lg px-2 text-sm cursor-pointer" for="draftStatus">Date Added</label>
                        </div>
                        <div class="flex items-center px-4 py-2 hover:bg-gray-100">
                            <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="closed" id="closedStatus" wire:click="Ordermytable('start_date')" >
                            <label class="closedColor p-1 rounded-lg px-2 text-sm cursor-pointer" for="closedStatus">Date Joined</label>
                        </div>
                    </div> -->
            </div>
            <div class="flex relative w-20p z-20 w-full col-span-1">
                <select wire:model.live="sortDirection" id="sortBy"
                    class="flex justify-between select-selected px-4 py-2 w-full border border-gray-300 rounded-lg z-auto shadow-sm cursor-pointer bg-white text-gray-500">
                    <option value="asc">Ascending</option>
                    <option value="desc">Descending</option>
                </select>
            </div>
        </div>
        <div class="pb-4 w-full flex justify-end items-start gap-4"  x-data="{ dallopen: false, selected: @entangle('selectedPerson') }">
                    <button @click.prevent="if (selected && Object.keys(selected).length > 0) { $wire.addSelectedToPlan(); }"  class="py-2 px-4 border rounded-lg flex justify-center items-center gap-1 font-medium">
                    <img class="add-icon w-auto mr-2" src="{{ asset('images/pluswithcircle.svg') }}" alt="Add to Plan Icon">
                    <span class="text-md font-semibold">Add to Plan</span>
                    </button>
                    <button @click.prevent="if (selected && Object.keys(selected).length > 0) { $wire.addSelectedToTalentPool(); }"  class="py-2 px-4 border rounded-lg flex justify-center items-center gap-1 font-medium">
                    <img class="add-icon w-auto mr-2" src="{{ asset('images/pluswithcircle.svg') }}" alt="Add to Talent Pool Icon">
                    <span class="text-md font-semibold">Add to Talent Pool</span>
                    </button>

                   
                </div>                   





            
        <div class="TableOuter relative rounded-2xl h-full bg-white border mb-5">
            <div class="table_wrapper rounded-2xl people-dashboard">
                <table class="tableEdges JobPeopleTable w-full overflow-x-auto">
                    <thead class="rounded-2xl">
                        <tr class="grayBackground GrayText">
                        <th scope="col" class="flex items-center px-2 py-2 text-center py-3.5 px-3 text-left text-lg border-b font-medium">  <div class="flex flex-col gap-y-1 text-start mr-2">
                                        <input type="checkbox" x-on:click="$dispatch('select-all', $event.target.checked); $wire.set('selectedPerson', $event.target.checked ? @json($People->pluck('id')) : [])" x-bind:checked="$wire.selectedPerson.length > 0" class="form-checkbox h-4 w-4">
                                        </div> Name</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Company</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Country</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Function</th>
                            {{-- <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Division</th> --}}
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Potential Sex</th>
                            @if(($jobdetails ?? false) && $jobdetails->ethnicity === 1)
                                <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Potential Diversity</th>
                            @endif
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Tenure</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Readiness</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium">Registration Status</th>
                            <th scope="col" class="py-3.5 px-3 text-left text-lg border-b font-medium"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @if($People->isNotEmpty())
                            @foreach ($People as $Person)
                                <tr>
                                    <td class=" flex px-3 py-3 text-md font-normal">
                                    <div class="flex flex-col gap-y-1 text-start mr-2 mt-2">
                                        <input type="checkbox" wire:model="selectedPerson" value="{{ $Person->id }}" class="form-checkbox h-4 w-4">
                                </div>
                                        <div class="flex flex-col">
                                            <span>{{ $Person->forename }} {{ $Person->surname }}</span>
                                            <span class="truncate GrayText w-48 text-sm">{{ $Person->latest_role }}</span>
                                        </div>
                                    </td>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $Person->company_name }}</td>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $Person->country }}</td>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $Person->function }}</td>
                                    {{-- <td class=" px-3 py-3 text-md font-normal">{{ $Person->division }}</td> --}}
                                    <td class=" px-3 py-3 text-md font-normal">{{ $Person->gender }}</td>
                                    @if(($jobdetails ?? false) && $jobdetails->ethnicity === 1)
                                        <td class=" px-3 py-3 text-md font-normal">{{ $Person->diverse }}</td>
                                    @endif
                                    <td class=" px-3 py-3 text-md font-normal">{{ $Person->tenure }}</td>
                                    <td class=" px-3 py-3 text-sm">
                                        @if($Person->readiness)
                                            @if(strtolower($Person->readiness) == 'ready')
                                                <span class="py-2 px-4 rounded-lg font-medium redlinessReaddy">
                                                    Ready
                                                </span>
                                            @elseif(strtolower($Person->readiness) == 'not ready')
                                                <span class="py-2 px-4 rounded-lg font-medium RedBG text-red-500">
                                                    Not Ready
                                                </span>
                                            @endif
                                        @endif
                                    </td>
                                    <td class=" px-3 py-3 text-md font-normal">{{ $Person->other_tags }}</td>
                                    <td>
                                        <div class="dropdown-container relative">
                                            <button tabindex="1" id="dropdownDefaultButton"
                                                data-dropdown-toggle="dropdown"
                                                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-md py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                type="button">
                                                <img class="h-5 w-5"
                                                    src="{{ asset('images/DotsThreeVertival.svg') }}">
                                            </button>

                                            <!-- Dropdown menu -->
                                            <div id="dropdown"
                                                class="dropdown-menu hidden z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                                <ul class="py-2 text-md text-gray-700 dark:text-gray-200"
                                                    aria-labelledby="dropdownDefaultButton">
                                                    <li class="cursor-pointer">
                                                        <a class="p-2" wire:click.prevent="viewIndividual({{ $Person->id }})">
                                                            <span class="font-semibold text-md">
                                                                View
                                                            </span>
                                                        </a>
                                                    </li>
                                                    <li class="cursor-pointer">
                                                        <a class="p-2" wire:click="showAddToTalentPoolPopup({{ $Person->id }})">
                                                            <span class="font-semibold text-md">Add to Talent Pool</span>
                                                        </a>
                                                    </li>
                                                    <li class="cursor-pointer">
                                                        <a class="p-2" wire:click="showAddToPlanPopup({{ $Person->id }})">
                                                            <span class="font-semibold text-md">Add to
                                                                Plan</span>
                                                        </a>
                                                    </li>
                                                    <li class="cursor-pointer">
                                                        <a class="p-2" @click="ropen=true" wire:click.prevent="viewIndividual({{ $Person->id }})">
                                                            <span class="font-semibold text-md">Report</span>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr class="text-center">
                                <td colspan="10" class="py-10 text-gray-500">No Record(s) Found</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <div class="flex absolute bottom-0 w-full px-2 gap-4 paginationContainer border-t py-4">
                <select wire:model.live="perPage" id="perPage"
                    class="form-select bg-white border rounded-lg outline-none GrayText py-2">
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="30">30</option>
                    <option value="40">40</option>
                    <option value="50">50</option>
                </select>
            </div>
            <div class="pl">
                {{ $People->links() }}
            </div>
        </div>
    </div>
</div>