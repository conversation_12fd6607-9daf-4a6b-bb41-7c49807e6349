<div x-show="vopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" style="display:none">
    <!-- Modal background with a higher z-index -->
    <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
    <!-- Modal content with a lower z-index -->
    <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50">
        <!-- Modal content -->
        <div class="flex flex-col h-full">
            <div class="flex justify-between border-b px-2 py-2">
                <h3 class="text-base text-lg font-bold">{{ $selectedIndividual->company_name }}</h3>
                <img @click="vopen = false" class="h-5 w-5 cursor-pointer" src="{{asset('images/cross.svg')}}" alt="">
            </div>
            <div class="grid grid-cols-2 gap-x-2 pt-3 flex-1">
                <div class="flex flex-1 flex-col justify-between">
                    <div class="flex px-2 py-2 justify-between">
                        <div class="flex flex-col gap-y-1">
                            <h3 class="text-base text-lg font-bold">{{ $selectedIndividual->forename}} {{ $selectedIndividual->surname}}</h3>
                            <h3 class="text-base text-sm text-gray-700">{{ $selectedIndividual->latest_role }}</h3>
                        </div>
                        <div class="flex gap-x-2 items-center justify-center">
                            <div class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                <img class="h-4 w-4 mr-2" src="{{ asset('images/Plus.svg') }}"
                                    alt="">
                                <h2 @click="addToPlanPopup = true;" class="text-sm font-semibold">Add to Plan</h2>
                            </div>
                            <div class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                <h2 @click="addToTalentPoolPopup = true" class="text-sm font-semibold">Add to Talent Pool</h2>
                            </div>
                            @if($selectedIndividual->linkedinURL && $selectedIndividual->linkedinURL != 'NA')
                            <a id="linkedin" class="flex items-center justify-center hover:scale-105" href="{{ $selectedIndividual->linkedinURL }}" target="_blank">
                                <img class="h-6 w-6" src="{{ asset('images/linkedinlogo.png') }}">
                            </a>
                            @endif
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 grid-rows-4 flex-grow">
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Gender</h3>
                            <h3 class="text-md text-gray-700">{{ $selectedIndividual->gender}}</h3>
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Function</h3>
                            <h3 class="text-md text-gray-700">{{ $selectedIndividual->function ? $selectedIndividual->function : 'Not Applicable' }}</h3>
                        </div>
                        {{--
                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Division</h3>
                                <h3 class="text-md text-gray-700">{{ $selectedIndividual->division ? $selectedIndividual->division : 'Not Applicable' }}</h3>
                    </div>
                    --}}
                    {{--
                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Seniority</h3>
                                <h3 class="text-md text-gray-700">{{ $selectedIndividual->seniority ? $selectedIndividual->seniority : 'Not Applicable' }}</h3>
                </div>
                --}}
                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                    <h3 class="text-sm font-bold chart-heading">Tenure in Role</h3>
                    <h3 class="text-md text-gray-700">{{ $selectedIndividual->tenure }}</h3>
                </div>
                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                    <h3 class="text-sm font-bold chart-heading">Readiness</h3>
                    <h3 class="text-md text-gray-700">
                        @if($selectedIndividual->readiness)
                        @if(strtolower($selectedIndividual->readiness) == 'ready')
                        <span class="px-2 py-1 text-sm rounded-lg font-medium redlinessReaddy">
                            Ready
                        </span>
                        @elseif(strtolower($selectedIndividual->readiness) == 'not ready')
                        <span class="px-2 py-1 text-sm rounded-lg font-medium RedBG text-red-500">
                            Not Ready
                        </span>
                        @endif
                        @else
                        <span class="px-2 py-1 text-sm rounded-lg font-medium">
                            N/A
                        </span>
                        @endif
                    </h3>
                </div>
                <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                    <h3 class="text-sm font-bold chart-heading">Registration Status</h3>
                    <h3 class="text-md text-gray-700">{{ $selectedIndividual->other_tags ? $selectedIndividual->other_tags : 'Not Applicable' }}</h3>
                </div>
                {{--
                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Score</h3>
                                <h3 class="text-md text-gray-700">{{ $selectedIndividual->total_score}}</h3>
            </div>
            --}}
        </div>
    </div>
    <div x-data="{tab: 'summary'}" class="flex flex-col pt-3 gap-y-2">
        <div class="flex border-b-2">
            <button @click="tab = 'summary'" class="w-full text-sm py-3 px-2 font-medium" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'summary', 'chart-heading font-semibold': tab != 'summary' }">
                Summary
            </button>
            <button @click="tab = 'career_history'" class="w-full text-sm py-3 px-2 font-medium" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'career_history', 'chart-heading font-semibold': tab != 'career_history' }">
                Career History
            </button>
            <button @click="tab = 'skills'" class="w-full text-sm py-3 px-2 font-medium" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'skills', 'chart-heading font-semibold': tab != 'skills' }">
                Skills
            </button>
            <button @click="tab = 'jobs'" class="w-full text-sm py-3 px-2 font-medium"
                :class="{
                                'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                    'jobs',
                                'chart-heading font-semibold': tab != 'jobs'
                            }">
                Talent Pool
            </button>
            <button @click="tab = 'plans'" class="w-full text-sm py-3 px-2 font-medium"
                :class="{
                                'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                    'plans',
                                'chart-heading font-semibold': tab != 'plans'
                            }">
                Plans
            </button>
            {{--
                            <button @click="tab = 'notes'" class="w-full text-sm py-3 px-2 font-medium" :class="{ 'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab == 'notes', 'chart-heading font-semibold': tab != 'notes' }">
                                Notes
                            </button>
                        --}}
        </div>
        <div x-show="tab == 'summary'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
            <h4 class="text-lg text-gray-700 font-semibold chart-heading mr-2">Summary</h4>
            <p class="text-sm">{{ $selectedIndividual->summary ? $selectedIndividual->summary : 'No summary available' }}</p>
        </div>

        <div x-show="tab == 'career_history'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Career History</h4>
            @if($selectedIndividualCareerHistory && $selectedIndividualCareerHistory->isNotEmpty())
            <div class="flex flex-col items-start w-full">
                @foreach ($selectedIndividualCareerHistory as $careerHistory)
                <div class="flex h-max items-start justify-center mb-1">
                    <div class="flex flex-col mt-1 items-center justify-center h-full w-2">
                        <div class="rounded-full  blueBalls bg-mainBlue"></div>
                        <div class="flex-grow border-l border-mainBlue"></div>
                    </div>
                    <div class="flex flex-col  items-start justify-start pl-4">
                        <h4 class="text-sm font-semibold text-gray-900">{{$careerHistory->role}}</h4>
                        <div class="flex gap-x-2">
                            <span class="text-sm  font-base text-gray-700">{{ $careerHistory->company_name }}</span>
                        </div>
                        <div class="flex gap-x-2 mb-4">
                            <span class="text-xs font-base GrayText">{{ \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y') }} - {{ !empty($careerHistory->end_date) ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : "Present" }}</span>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @else
            <p class="text-sm text-gray-500 mt-5">No career history available</p>
            @endif
        </div>

        <div x-show="tab == 'skills'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Skills</h4>
            @if($selectedIndividualSkills && $selectedIndividualSkills->isNotEmpty())
            <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                @foreach($selectedIndividualSkills as $skill)
                <div class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                    <span>{{ $skill->skill_name }}</span>
                </div>
                @endforeach
            </div>
            @else
            <p class="text-sm text-gray-500 mt-5">No skills available</p>
            @endif
        </div>

        <div x-show="tab == 'jobs'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Talent Pools</h4>
            @if($userJobs && $userJobs->isNotEmpty())
            <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                @foreach($userJobs as $job)
                <div class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                    <span>{{ $job->name }}</span>
                </div>
                @endforeach
            </div>
            @else
            <p class="text-sm text-gray-500 mt-5">No talent pool available</p>
            @endif
        </div>

        <div x-show="tab == 'plans'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Plans</h4>
            @if($userPlans && $userPlans->isNotEmpty())
            <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                @foreach($userPlans as $plan)
                <div class="flex items-center text-sm p-2 rounded-xl skill-container chart-heading">
                    <span>{{ $plan->name }}</span>
                </div>
                @endforeach
            </div>
            @else
            <p class="text-sm text-gray-500 mt-5">No plans available</p>
            @endif
        </div>


        {{--
                        <div x-show="tab == 'notes'" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Notes</h4>
                            @if($selectedIndividual->notes)
                                <p class="text-sm text-gray-600 font-normal">{{ $selectedIndividual->notes }}</p>
        @else
        <p class="text-sm text-gray-500 mt-5">No notes available</p>
        @endif
    </div>
    --}}
</div>
</div>
<!-- <h4 class="text-sm text-cyan-500 font-medium">{{ $selectedIndividual->location}}</h4> -->
</div>
</div>
</div>

