<div class="bg-white shadow-lg rounded-xl p-4">
    <dl class="text-lg font-medium leading-6 text-gray-500">Companies</dl>
    @if($companyData)
        <div class="h-80 w-full" 
            wire:ignore x-data="{
                cvalues: {{ json_encode($companyData) }},
                clabels: {{ json_encode($companyLabels) }},
                init() {
                    let chart = new ApexCharts(this.$refs.companychart, {
                        chart: {
                            type: 'bar',
                            stacked: true,
                            height: 320
                        },
                        plotOptions: { 
                            bar: { 
                                horizontal: true,
                                'borderRadius': 10,
                                'borderRadiusApplication': 'around',
                                'borderRadiusWhenStacked': 'last',
                                'dataLabels': {
                                    'position': 'top',
                                    'total': {
                                        'enabled': false,
                                        'offsetX': 0,
                                        'offsetY': 0,
                                        'style': {
                                            'color': '#373d3f',
                                            'fontSize': '12px',
                                            'fontWeight': 600
                                        }
                                    }
                                }
                            } 
                        },
                        tooltip: { enabled: true },
                        grid: { show: false },
                        'dataLabels': {
                            'position': 'top',
                            'offsetX': 25,
                            'style': {
                                'fontWeight': '500',
                                'fontSize': '14px',
                                'colors': [
                                    '373d3f'
                                ]
                            }
                        },
                        xaxis: {
                            categories: this.clabels,
                            'labels': {
                                'show': true,
                                'trim': true,
                                'style': {}
                            },
                            axisBorder: {
                                show: false
                            }
                        },
                        yaxis: {
                            'labels': {
                                'show': true,
                                'trim': true,
                                'style': {}
                            },
                            axisBorder: {
                                show: false
                            }
                        },
                        fill: {
                            colors: ['#CF67E1']
                        },
                        series: [{
                            name: 'Peoples',
                            data: this.cvalues,
                        }],
                    })
                    chart.render()
                }
            }"
        >
            <div x-ref="companychart"></div>
        </div>
    @else
        <p class="py-10 text-gray-500 mb-10 mt-10 text-center">No Record(s) Found</p>
    @endif
</div>

