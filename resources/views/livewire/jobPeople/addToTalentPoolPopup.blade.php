<div x-show="addToTalentPoolPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <div x-data="{openTalentPopup: @entangle('openTalentPopup') }" class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
        <div class="flex justify-between">
            <h2 class="text-black-900 text-xl font-semibold">Add to Talent Pools</h2>
            <button type="button" @click="addToTalentPoolPopup = false" class="text-gray-500 hover:text-gray-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- select people box -->
        <div class="flex justify-center items-center mt-2">
            <div class="bg-white border selectPeople p-2 rounded-lg">
                <!-- User list with checkboxes -->
                <ul class="mt-4 adddeduser space-y-6 py-4 overflow-y-auto" style="max-height: 300px;">
                    @if(!empty($talentPoolsList) && $talentPoolsList->isNotEmpty())
                        @foreach ($talentPoolsList as $talentPool)
                        <div class="w-full border-t my-1 border-gray-200"></div>
                        <li class="flex justify-between">
                            <div class="flex items-center gap-2 pl-4">
                                <div class="space-y-1">
                                    <span class="text-sm font-semibold block">{{ $talentPool->name}}</span>
                                </div>
                            </div>
                            {{-- Checkbox for selecting direct reports --}}
                            <input type="checkbox"
                                wire:model="addToTalentPoolArray.{{ $talentPool->id }}"
                                id="addToTalentPoolArray-{{ $talentPool->id }}"
                                class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out">
                        </li>
                        <div class="w-full border-t my-1 border-gray-200"></div>

                        @endforeach
                    @else 
                    <div class="w-full border-t my-1 border-gray-200"></div>
                        <li class="flex justify-between">No talent pool found!</li>
                        <div class="w-full border-t my-1 border-gray-200"></div>
                    @endif

                </ul>
                <div class="flex justify-end mt-4">
                    <!-- Submit the form when the button is clicked -->
                    <button type="button" class="p-2 rounded-lg"
                    @click="openTalentPopup ? $wire.addSelectedPersonToTalentPool() : (checkSelection() && $wire.addpeopleToTalentPools())">Add</button>
                </div> 
            </div>
        </div>
    </div>
</div>