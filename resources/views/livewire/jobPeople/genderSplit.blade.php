<div class="bg-white shadow-lg rounded-xl p-4">
    <dl class="text-lg font-medium leading-6 text-gray-500">Potential Sex Diversity</dl>
    @if($genderData)
        <div class="h-80 w-full px-5" wire:ignore x-data="{
                cvalues: {{ json_encode($genderData) }},
                clabels: {{ json_encode($genderLabels) }},
                init() {
                    let chart = new ApexCharts(this.$refs.genderchart, {
                        chart: {
                            type: 'bar',
                            stacked: true,
                            height: 320
                        },
                        plotOptions: { 
                            bar: {
                                'distributed': false,
                                'borderRadius': 10,
                                'borderRadiusApplication': 'around',
                                'borderRadiusWhenStacked': 'last',
                                'hideZeroBarsWhenGrouped': false,
                                'isDumbbell': false,
                                'isFunnel': false,
                                'isFunnel3d': true,
                                'dataLabels': {
                                    'position': 'top',
                                    'total': {
                                        'enabled': false,
                                        'offsetX': 0,
                                        'offsetY': 0,
                                        'style': {
                                            'color': '#373d3f',
                                            'fontSize': '12px',
                                            'fontWeight': 600
                                        }
                                    }
                                }
                            }
                        },
                        tooltip: { enabled: true },
                        grid: { show: false },
                        xaxis: {
                            categories: this.clabels,
                            'labels': {
                                'show': false,
                                'trim': true,
                                'style': {}
                            },
                            axisBorder: {
                                show: false
                            }
                        },
                        yaxis: {
                            show: false
                        },
                        fill: {
                            colors: ['#3B82F6']
                        },
                        'dataLabels': {
                            'position': 'top',
                            'formatter': function(val, opts) {
                                let label = opts.w.config.xaxis.categories[opts.dataPointIndex];
                                // Customize the data label text here
                                return label + ' (' + val + ')';
                            },
                            'offsetY': -25,
                            'style': {
                                'fontWeight': '600',
                                'fontSize': '14px',
                                'colors': [
                                    '373d3f'
                                ]
                            }
                        },
                        series: [{
                            name: 'Gender',
                            data: this.cvalues,
                        }],
                    })
                    chart.render()
                }
            }"
        >
            <div x-ref="genderchart"></div>
        </div>
    @else
        <p class="py-10 text-gray-500 mb-10 mt-10 text-center">No Record(s) Found</p>
    @endif
</div>