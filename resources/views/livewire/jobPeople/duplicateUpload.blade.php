<div x-show="duplicateUploadPopup" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50" x-cloak>

    <!-- Modal background with a higher z-index -->
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

    <!-- Modal content with a lower z-index -->
    <div class="modal-content2 step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50">

        <img @click="duplicateUploadPopup = false" class="absolute right top-2 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Cross Icon">
        <h2 class="font-semibold px-4">{{count((array)$duplicateRecords)}} DUPLICATE RECORDS FOUND PLEASE SELECT WHICH YOU WANT TO UPLOAD</h2>
        <div class="w-full border-t mt-3 border-gray-200"></div>

        <div class="modalscroll mt-1 gap-1 px-4">
    @foreach ($duplicateRecords as $duplicateRecord)
        <div class="section">
            <h2 class="font-semibold text-lg chooseOption">Choose an option</h2>

            <div class="square-radio">
                <input type="radio" class="form-radio" name="selectedRecord_{{ $duplicateRecord->forename }}" id="uploaded_{{ $duplicateRecord->forename }}_{{ $loop->index }}" wire:model="selectedRecords.{{ $duplicateRecord->forename }}" value="{{ $duplicateRecord->forename . '-' . $duplicateRecord->surname . '-' . $duplicateRecord->latest_role . '-' . $duplicateRecord->companyName }}">
                <label for="uploaded_{{ $duplicateRecord->forename }}_{{ $loop->index }}">
                  {{ $duplicateRecord->forename . ' ' . $duplicateRecord->surname }} ({{ $duplicateRecord->latest_role }}) {{ $duplicateRecord->companyName }} - From uploaded template
                </label>
            </div>
            @foreach ($duplicateRecord->duplicate as $data)
                <div class="square-radio">
                    <input type="radio" class="form-radio" name="selectedRecord_{{ $duplicateRecord->forename }}" id="existing_{{ $data->id }}_{{ $loop->index }}" wire:model="selectedRecords.{{ $duplicateRecord->forename }}" value="{{ $data->id }}" required>
                    <label for="existing_{{ $data->id }}_{{ $loop->index }}">
                      {{ $data->forename . ' ' . $data->surname }} ({{ $data->latest_role }}) {{ $data->company_name }} - From the system
                    </label>
                </div>
            @endforeach
        </div>
        <div class="w-full border-t mt-3 border-gray-200"></div>
    @endforeach
</div>



        <!-- 3rd steps button -->
        <div class="flex gap-2 w-full mt-4 px-4">
            <button type="button" wire:click="saveSelectedRecords" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                <img class="h-5 w-5" src="{{ asset('images/CloudArrowUp.svg') }}">
                <span class="block">Upload</span>
            </button>
        </div>
    </div>
</div>