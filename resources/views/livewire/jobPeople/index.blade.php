<div class="customHeight overflow-y-scroll">

@include('livewire.flashMessage')

    @include('livewire.jobPeople.contentTitle')

    <div class="lightgray ContentContainer" x-data="{
                                                    drawer: false, 
                                                    vopen: @entangle('vopen'), 
                                                    addToTalentPoolPopup : @entangle('addToTalentPoolPopup'),
                                                    addToPlanPopup : @entangle('addToPlanPopup'),  
                                                    ropen: false, 
                                                    tab: 'summary', 
                                                    closeDrawer() { 
                                                        this.drawer = false; 
                                                        this.$dispatch('clearFilters'); 
                                                    }, 
                                                    closeReportModal() { 
                                                        this.ropen = false; 
                                                        this.$dispatch('clearSelectedIndividuals'); 
                                                    },
                                                    addToTalentPoolArray: @entangle('addToTalentPoolArray'),
                                                    addToPlansArray: @entangle('addToPlansArray'),
                                                    checkSelection() {
                                                        if (Object.values(this.addToTalentPoolArray).every(value => !value)) {
                                                            toastr.info('Please select talent pool!');
                                                            return false;
                                                        }
                                                        return true;
                                                    },
                                                    checkAddToPlanSelection() {
                                                        if (Object.values(this.addToPlansArray).every(value => !value)) {
                                                            toastr.info('Please select talent pool!');
                                                            return false;
                                                        }
                                                        return true;
                                                    },
                                                    }">
        <div class=" grid grid-cols-2 px-10 shadow-2xl gap-4 w-full h-full py-4">
            <div class="flex-1 bg-white shadow-lg rounded-xl p-4">
                <dl class="text-lg font-medium leading-6 text-gray-500">Number of People</dl>
                <dd class="w-full flex-none text-xl font-medium leading-10 tracking-tight text-gray-900">
                    {{ $TotalPeople }}
                </dd>
            </div>
            <div class="flex-1 bg-white shadow-lg rounded-xl p-4">
                <dl class="text-lg font-medium leading-6 text-gray-500">Number of Companies</dl>
                <dd class="w-full flex-none text-xl font-medium leading-10 tracking-tight text-gray-900">
                    {{ $TotalCompanies }}</dd>
            </div>

            {{-- exco --}}
            @include('livewire.jobPeople.exco')

            {{-- companies --}}
            @include('livewire.jobPeople.companies')

            {{-- gender split --}}
            @include('livewire.jobPeople.genderSplit')

            {{-- locations --}}
            @include('livewire.jobPeople.locations')

        </div>

        @include('livewire.jobPeople.list')

        <!-- Advance Search Started -->

        @include('livewire.jobPeople.advanceSearch')

        @if ($selectedIndividualID && $selectedIndividual)

            @include('livewire.jobPeople.view')
            @include('livewire.jobPeople.report')

        @endif
        @include('livewire.jobPeople.addToTalentPoolPopup')
        @include('livewire.jobPeople.addToPlanPopup')



    </div>

    @include('livewire.loading')

  
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        var selectStatus = document.getElementById('selectStatus');
        var selectItems = selectStatus?.nextElementSibling;

        selectStatus?.addEventListener('click', function() {
            if (selectItems.classList.contains('d-none')) {
                selectItems.classList.remove('d-none');
            } else {
                selectItems.classList.add('d-none');
            }
        });
    });
</script>
<script>
    document.getElementById('file-upload').addEventListener('change', function() {
        // Show the loading spinner
        document.getElementById('loading-spinner').classList.remove('hidden');
        
    });
</script>