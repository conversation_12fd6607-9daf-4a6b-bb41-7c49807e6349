<div x-show="uploadPopup" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50" x-cloak>

    <!-- Modal background with a higher z-index -->
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

    <!-- Modal content with a lower z-index -->
    <div class="modal-content2 step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50">

        <img @click="uploadPopup = false"  class="absolute right top-2 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Cross Icon">
        <h2 class="font-semibold px-4">Bulk upload candidates</h2>
        <div class="w-full border-t mt-3 border-gray-200"></div>

        <div class="modalscroll mt-1 grid grid-cols-2 grid gap-1 px-4">
            <div class="col-span-2 mt-2">
                @if ($errors->any())
                <div class="text-red-500 text-sm text-center">
                    @foreach ($errors->all() as $error)
                        <p>{{ $error }}</p>
                    @endforeach
                </div>
            @endif
            </div>
            <div class="col-span-2 mb-5 mt-2">
                <label for="External-candidate"
                    class="block text-md text-center font-semibold leading-6 text-gray-900">Candidates</label>
                <div
                    class="mt-2 h-48 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                    <div class="text-center">

                        <div class="flex items-center justify-center">
                            <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                        </div>
                        <div class="mt-4 text-md leading-6 text-gray-600">
                            <label for="file-upload"
                                class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 hover:text-sky-500">
                                <span>Upload a file</span>
                                <input id="file-upload" wire:model="csvFile" name="file-upload" type="file" class="sr-only"  accept=".csv, .xlsx, .xls">
                            </label>
                            <!-- <p class="pl-1">or drag and drop</p> -->
                        </div>
                        <p class="text-sm leading-5 text-gray-600">(CSV or Excel)</p>
                        @if($csvFile && $csvFile->getClientOriginalName())
                            <p class="text-sm leading-5 text-gray-600 mt-2">
                                <span class="font-semibold">Selected File:</span> {{ $csvFile->getClientOriginalName() }}
                            </p>
                        @endif
                    </div>
                </div>
            </div>
            <div class="col-span-2 mb-5">
                <label for="cover-photo"
                    class="block text-md font-medium text-center leading-6 text-gray-900">Download Templates</label>
                <div
                    class="mt-2 h-48 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                    <div class="text-center">
                        <div class="flex items-center justify-center">
                            <img class="h-10 w-10" src="{{ asset('images/download.png') }}">
                        </div>
                        <div class="mt-4 flex text-md leading-6 text-gray-600">
                            <label for="file-upload"
                                class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 hover:text-indigo-500">
                                <button wire:click="downloadCSV" class="">
                                    <span>Download Template Files</span>
                                </button>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- border above buttons -->
        <div class="w-full border-t  border-gray-200"></div>

        <!-- 3rd steps button -->
        <div class="flex gap-2 w-full mt-4 px-4">
            <button @click="uploadPopup = false" type="button"
                class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                <span class="block font-medium">Cancel</span>
            </button>
            <button type="button" wire:click="uploadCSV"
                class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                <img class="h-5 w-5"
                    src="{{ asset('images/CloudArrowUp.svg') }}">
                <span class="block">Upload</span>
            </button>
        </div>
    </div>
</div>