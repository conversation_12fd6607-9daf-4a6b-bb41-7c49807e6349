<div class="flex justify-between items-center px-10 py-4 border-b shadow-md bg-white">
        <div class="flex gap-2">
            <a href="{{ route('job.index') }}" class="flex gap-2 mainBlue items-center">
                <img class="" src="{{ asset('/images/ArrowLeftBlue.svg') }}">
                <span>Back</span>
            </a>
           
            <h1 class="whitespace-nowrap px-4 text-black text-3xl font-semibold">People Dashboard</h1>
        </div>
        <div class="flex gap-2">

            <div class="" x-data="{ uploadPopup:  @entangle('uploadPopup'), duplicateUploadPopup:  @entangle('duplicateUploadPopup')}">

                <div class="w-32 border rounded-lg p-px shadow-md hover:shadow-lg hover:scale-105">
                <a href="{{ route('job.upload.index', ['job' => $job]) }}"><button 
                        class="w-full flex justify-center items-center gap-2 font-semibold transition ease-in-out delay-100 text-md text-black rounded-lg py-2 hover:bg-cyan-500 hover:text-white duration-100">
                        <span>Search External</span>
                    </button></a>
                </div>
                @include('livewire.jobPeople.upload')
                @include('livewire.jobPeople.duplicateUpload')

            </div>

            <div class="flex" x-data="{ addPersonPopup: @entangle('addPersonPopup'), peopleAreadyExistsPopup: @entangle('peopleAreadyExistsPopup') }">
                <!-- Trigger button inside the modal -->
                <div class="flex items-center justify-center">
                    <div>
                        <button x-on:click="addPersonPopup = true"
                            class="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium text-white text-md justify-center">
                            <img class="h-4 font-bold w-auto img px-1"
                                src="{{ asset('/images/plus-white-without-circle.svg') }}">
                            <h1 class="text-md font-semibold">Add Person</h1>
                        </button>
                    </div>
                </div>
                <!-- Modal container -->
                @include('livewire.jobPeople.addPerson')
            </div>
        </div>
    </div>