<div x-show="addPersonPopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50" x-cloak>
    <!-- Modal background with a higher z-index -->
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

    @if ($step == 3)
    <div class="modal-content step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">

        <span class="py-3 px-3 rounded-full GreenBackground mainGreen text-center">
            <img class="w-6 h-6" src="{{ asset('images/right_tick_with_circle_green.svg') }}">
        </span>

        <h1 class="font-medium px-4 text-xl text-center my-5">New person was successfully added!</h1>

        <!-- border above buttons -->
        <div class="w-full border-t my-5 border-gray-200"></div>

        <!-- buttons wrapper -->
        <div class="flex gap-2 w-full px-4">
            <button @click="closeModal" type="button" class="bg-white w-full text-black border p-2 rounded-md">Close</button>
            {{--
                    @if (!empty($job))
                        <a href="{{ route('job.long.index', ['job' => $job->id]) }}" type="button"
            class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">
            <span class="block"> Review</span>
            </a>
            @else
            <button @click="closeModal" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                <span class="block"> Review</span>
            </button>
            @endif
            --}}
        </div>
    </div>
    @else
    <!-- Modal content with a lower z-index -->
    <div class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]" x-transition>
        <!-- Advanced Search modal content -->

        <img @click="addPersonPopup = false" class="absolute right top-2 w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Search Icon">

        <h2 class="font-semibold px-4">Add Person</h2>
        <div class="flex justify-between mt-3 px-4">
            <button>
                <span class="mainBlue p-1 rounded-full px-2 text-xs 
                                    @if ($step == 1) BlueBackground mainBlue
                                    @else
                                        GreenBackground mainGreen @endif">1</span>
                <span class="text-xs font-medium">Personal Details</span>
            </button>
            <button>
                <span class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                            @if ($step == 2) BlueBackground mainBlue
                            @elseif($step > 2)
                                GreenBackground mainGreen @endif">2</span>
                <span class="text-xs font-medium">Additional Information</span>
            </button>
        </div>
        <div class="w-full border-t mt-3 border-gray-200"></div>

        @if ($step === 1)
        <!-- 1st Step -->
        <div class="h-full" wire:key="AddPersonStep1">
            <div class="h-5/6 flex items-center">
                <div class="w-full">

                    <div class="relative py-2 bg-white">
                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                    </div>

                    <div class="modalscroll px-4">

                        <div class="mb-3">
                            <label for="addForename" class="block text-xs font-medium labelcolor">Forename
                            </label>
                            <input type="text" wire:model="addForename" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter forename">

                            @error('addForename')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="addSurname" class="block text-xs font-medium labelcolor">Surname</label>
                            <input type="text" wire:model="addSurname" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter surname">

                            @error('addSurname')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="addMiddlename" class="block text-xs font-medium labelcolor">Middle Name
                                (Optional)</label>
                            <input type="text" wire:model="addMiddlename" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter middle name">
                        </div>

                        <div class="mb-3">
                            <label for="addOtherName" class="block text-xs font-medium labelcolor">Other Name
                                (Optional)</label>
                            <input type="text" wire:model="addOtherName" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter other name">
                        </div>

                        <div class="mb-3">
                            <label for="addRole" class="block text-xs font-medium labelcolor">Role</label>
                            <input type="text" wire:model="addRole" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter role">

                            @error('addRole')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="addStartDate" class="block text-xs font-medium labelcolor">When did they
                                start? (Optional)</label>
                            <input type="date" wire:model="addStartDate" class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" placeholder="Enter the company name">
                        </div>


                        <div class="mb-3">
                            <label for="planName" class="block text-xs font-medium labelcolor">Company</label>
                            <div x-data="{
                                recommendations: {{ json_encode($companies) }},
                                searchQuery: @entangle('selectedCompanies').defer,
                                filteredRecommendations: [],
                                selectRecommendation(value) {
                                    this.searchQuery = value;
                                    const inputField = document.getElementById('selectedCompanies');
                                    inputField.value = value;
                                    inputField.focus();
                                    inputField.dispatchEvent(new Event('input'));
                                    this.filteredRecommendations = []; // Clear recommendations after selection
                                },
                                search() {
                                    const query = String(this.searchQuery ?? '').toLowerCase();
                                    if (query === '') {
                                        this.filteredRecommendations = [];
                                    } else {
                                        this.filteredRecommendations = this.recommendations.filter(recommendation => {
                                            return recommendation.value.toLowerCase().includes(query);
                                        }).slice(0, 10);
                                    }
                                },
                                initialize() {
                                    this.searchQuery = @js($selectedCompanies);
                                    this.search();
                                }
                            }" x-init="initialize">
                                <input type="text" id="selectedCompanies" wire:model="selectedCompanies" x-model="searchQuery" @input="search" class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md">
                                <div class="flex flex-col gap-y-2 mt-2" x-show="filteredRecommendations.length > 0">
                                    <template x-for="recommendation in filteredRecommendations" :key="recommendation.value">
                                        <div class="flex items-center gap-x-2">
                                            <button class="text-sm font-semibold text-cyan-500" @click.prevent="selectRecommendation(recommendation.value); $dispatch('close-suggestions')">
                                                Use Recommendation
                                            </button>
                                            <p class="text-sm text-gray-900 font-light" x-text="recommendation.value"></p>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            @error('selectedCompanies')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <!-- border above buttons -->
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button @click="closeModal" type="button" class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                        <button wire:click.prevent="validateStepOne" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <span class="block"> Continue</span>
                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                        </button>
                    </div>

                </div>
            </div>
        </div>
        @elseif($step === 2)
        <!-- 2nd Step -->
        <div class="h-full" wire:key="AddPersonStep2">
            <div class="h-5/6 flex items-center">
                <div class="w-full">

                    <div class="relative py-2 bg-white">
                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                    </div>

                    <div class="modalscroll px-4">
                        <div class="mb-3">
                            <label for="addLocation" class="block text-xs font-medium labelcolor">They are
                                based in (Optional)
                            </label>
                            <select class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" wire:model="addLocation" id="country" name="country">
                                <option value="" class="mt-1 block w-full p-2 outline-none border border-gray-300 rounded-md">
                                    Select a country</option>
                                @foreach ($countries as $code => $name)
                                <option value="{{ $name }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="mb-3">

                            <!-- <input type="text" wire:model="addGender"
                                        class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                        placeholder="Female, Male, All"> -->

                            <fieldset>
                                <label for="addGender" class="block text-xs font-medium labelcolor">They could
                                    be described as </label>

                                <!-- radio button in form of buttons -->
                                <ul class="donate-now flex gap-2 mt-1">
                                    <li>
                                        <input type="radio" wire:model="addGender" id="male" name="addGender" value="Male" class="cursor-pointer" />
                                        <label for="male" class="text-center font-semibold labelcolor">Male</label>
                                    </li>
                                    <li>
                                        <input type="radio" wire:model="addGender" id="female" name="addGender" value="Female" class="cursor-pointer" />
                                        <label for="female" class="text-center font-semibold labelcolor">Female</label>
                                    </li>
                                    <li>
                                        <input type="radio" wire:model="addGender" id="not_required" name="addGender" value="Gender Not Applicable" class="cursor-pointer" />
                                        <label for="not_required" class="text-center font-semibold labelcolor">Not Applicable</label>
                                    </li>
                                </ul>
                            </fieldset>
                        </div>

                        <div class="mb-3">
                            <label for="addLinkedinURL"
                                class="block text-xs font-medium labelcolor">LinkedIn Url (Optional)</label>
                            <input type="text" wire:model="addLinkedinURL"
                                class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                placeholder="Enter LinkedIn url">

                            @error('addLinkedinURL')
                                <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>

                    </div>

                    <!-- border above buttons -->
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->
                    <div class="flex gap-2 w-full px-4 mt-4 ">
                        <button wire:click="previousStep" type="button" class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                            <span class="block font-medium">Back</span>
                        </button>

                        <button type="button" wire:click="AddPerson" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/plus-white-without-circle.svg') }}">
                            <span class="block">Add Person</span>
                        </button>
                    </div>

                </div>
            </div>
        </div>
        @endif


    </div>
    @endif
</div>


<div x-show="peopleAreadyExistsPopup" x-cloak class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50">
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
    <div class="bg-white relative  border-2 p-4 rounded-lg flex flex-col z-50">
        <img @click="peopleAreadyExistsPopup = false" class="absolute right-2 crossTop w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}" alt="Close Icon">
        <h2 class="font-semibold px-4">Confirmation</h2>
        <div class="w-full border-t mt-3 border-gray-200"></div>
        <div class="h-full" wire:key="AddPersonStep2">
            <div class="h-5/6 flex items-center">
                <div class="w-full">

                    <div class="relative py-2 bg-white">
                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                    </div>

                    <div class="modalscroll">
                        <div class="mb-2">
                            <label for="existingPeople" class="block text-xs font-medium labelcolor">A person with detail Forename, surname and company are already exists
                            </label>
                            <select class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md" wire:model="existingPeopleField" id="existingPeopleField" name="existingPeopleField">
                                <option value=""> Select people</option>
                            @foreach ($isPeopleAlreadyExists as $code => $existingPeople)
                                <option value="{{ $existingPeople->id }}">{{ $existingPeople->forename." ".$existingPeople->surname }}({{$existingPeople->latest_role ?? "N/A"}})</option>
                                @endforeach
                            </select>
                            @error('existingPeopleField')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>

                    </div>

                    <!-- border above buttons -->
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons wrapper -->


                </div>
            </div>
        </div>

        <div class="flex gap-2 w-full px-4 mt-4">
            <button @click="peopleAreadyExistsPopup = false;" type="button" class="bg-white font-semibold w-full text-black border p-2 rounded-md">Cancel</button>
            <button  wire:click="useExistingPeople" type="button" class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                <span class="block">Use Existing</span>
            </button>
        </div>
    </div>
</div>