<div class="bg-gray-200">
    <div class="customHeight overflow-y-auto">
        <!-- Button that brings up close account email
        <div class="bg-white border px-10 py-4 flex items-center justify-between">
            <h1 class="text-2xl text-black font-medium">Admin</h1>
            <button class="border  p-2  redText rounded-lg">Deactivate Account</button>
        </div>
        -->
        <div class="px-8 overflow-y-auto">
            <div class="mt-5">
                <div class="flex-1">
                    <div class="w-full bg-white p-4 border border-gray-200 rounded-lg shadow-md">
                        <h2 class="text-2xl font-semibold">My Plan</h2>
                        <div class="grid grid-cols-1 gap-3 w-full mt-3">

                            {{-- deactivate button commented out --}}
                            {{-- <div class="w-full flex" x-data="{ lopen: false }">
                        <!-- Trigger button inside the modal -->
                            <div class="w-full flex items-center justify-end">
                                <div class="w-40 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                    <button x-on:click="lopen = true" class="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Deactivate Account</button>
                                </div>
                            </div>   
                            <!-- Modal container -->
                            <div x-show="lopen" 
                                 class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50"
                                 style="display:none">
                                <!-- Modal background with a higher z-index -->
                                <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                                <!-- Modal content with a lower z-index -->
                                <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50 w-96" x-show="lopen" x-transition>
                                <!-- Modal content -->
                                    <h2 class="text-black-900 text-xl text-black font-bold">Deactivate Account</h2>
                                    <div class="mt-4">
                                        <fieldset>
                                            <div class="flex-wrap">
                                                <p class="font-light text-sm text-gray-700">We're really sorry to see you go. Your feedback is really valuable to us, please let us know the reason using the options below:</p>
                                            </div>
                                            <div class="">
                                                <div class="mt-3 flex items-center gap-x-3">
                                                    <input type="radio" wire:model="ethnicity" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black" value="1">
                                                    <label for="push-yes" class="block text-xs text-gray-900">No longer need the platform</label>
                                                </div>
                                                <div class="mt-2 flex items-center gap-x-3">
                                                    <input type="radio" wire:model="ethnicity" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black" value="0">
                                                    <label for="push-no" class="block text-xs text-gray-900">Cost of account</label>
                                                </div>
                                                <div class="mt-2 flex items-center gap-x-3">
                                                    <input type="radio" wire:model="ethnicity" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black" value="0">
                                                    <label for="push-no" class="block text-xs text-gray-900">Does not meet our requirements</label>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </div>
                                    <div class="mt-5 flex flex-1 items-center justify-end gap-x-6">
                                        <div class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                        <button x-on:click="lopen =false" class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                        </div>
                                        <div class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                            <button x-on:click="lopen =false" wire:click="sendDeactivate" class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Deactivate</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> --}}

                            <div class="grid grid-cols-4 grid-rows-2 gap-4">
                                <div class="border p-2 rounded-lg">
                                    <h3 class="text-sm GrayText font-medium">Account Type</h3>
                                    <p class="text-base mt-2 font-normal">
                                        {{ $AccountDetails->account_type }}
                                    </p>
                                </div>
                                <div class="border p-2 rounded-lg">
                                    <h3 class="text-sm   GrayText  font-medium">Date Set-up</h3>
                                    <p class="text-base mt-2 font-normal">{{ $AccountDetails->created_at }}
                                    </p>
                                </div>
                                <div class="border p-2 rounded-lg">
                                    <h3 class="text-sm GrayText font-medium">Licenses Allowance</h3>
                                    <p class="text-base mt-2  font-normal">
                                        {{ $AccountDetails->users_limit }}
                                    </p>
                                </div>
                                <div class="border p-2 rounded-lg">
                                    <h3 class="text-sm GrayText font-medium">Licenses Used</h3>
                                    <p class="text-base mt-2 font-normal">
                                        {{ $AccountDetails->active_users }}
                                    </p>
                                </div>
                            </div>

                            <!-- <div class="border  p-2 rounded-lg">
                                <h3 class="text-sm font-medium GrayText">Features</h3>
                                <p class="text-base mt-2 font-normal">The following features have been included in your
                                    plan:
                                </p>
                                <div class="flex gap-3 mt-3">
                                    <img class="" src="{{ asset('images/bigGreenTick.svg') }}" alt="bigGreenTick">
                                    <p class="GrayText font-normal text-base">Individual Licenses with access to
                                        Unlimited
                                        Plans</p>
                                </div>
                                <div class="flex gap-3 mt-1">
                                    <img class="" src="{{ asset('images/bigGreenTick.svg') }}" alt="bigGreenTick">
                                    <p class="GrayText font-normal text-base">Private Data</p>
                                </div>
                            </div> -->
                        </div>
                    </div>
                    <!-- <div class="w-full">





                        <div>
                            <h3 class="text-xl mt-3 font-semibold ">Alternative Plans</h3>
                            <div class="grid grid-cols-3 gap-6 mt-3">

                                <div class="flex-1 bg-white rounded-lg silver shadow-lg p-2">
                                    <div class=" flex flex-1">
                                        <h3 class="text-2xl font-semibold text-black">Silver</h3>
                                    </div>
                                    <div class="flex gap-3 mt-3">
                                        <img class="" src="{{ asset('images/bigGreenTick.svg') }}" alt="bigGreenTick">
                                        <p class="GrayText font-normal text-base">Individual Licenses with access to
                                            Unlimited Plans</p>
                                    </div>
                                    <div class="flex gap-3 mt-1">
                                        <img class="" src="{{ asset('images/bigGreenTick.svg') }}" alt="bigGreenTick">
                                        <p class="GrayText font-normal text-base">Private Data</p>
                                    </div>

                                    <div class="mt-4 flex border justify-center items-center rounded-lg cursor-pointer font-semibold p-2">
                                        <a href="{{ route('upgrade.sendRequest', ['Option' => 'Silver']) }}">Contact
                                            Sales</a>
                                    </div>
                                </div>

                                <div class="flex-1 bg-white rounded-lg shadow-lg gold  p-2">
                                    <div class=" flex flex-1">
                                        <h3 class="text-2xl font-semibold text-black">Gold</h3>
                                    </div>
                                    <div class="flex gap-3 mt-3">
                                        <img class="" src="{{ asset('images/bigGreenTick.svg') }}" alt="bigGreenTick">
                                        <p class="GrayText font-normal text-base">Individual Licenses with access to
                                            Unlimited Plans</p>
                                    </div>
                                    <div class="flex gap-3 mt-1">
                                        <img class="" src="{{ asset('images/bigGreenTick.svg') }}" alt="bigGreenTick">
                                        <p class="GrayText font-normal text-base">Private Data</p>
                                    </div>

                                    <div class="mt-4 flex border justify-center items-center rounded-lg cursor-pointer font-semibold p-2">
                                        <a href="{{ route('upgrade.sendRequest', ['Option' => 'Gold']) }}">Contact
                                            Sales</a>
                                    </div>
                                </div>

                                <div class="flex-1 bg-white rounded-lg shadow-lg platinum p-2">
                                    <div class=" flex flex-1">
                                        <h3 class="text-2xl font-semibold text-black">Platinum</h3>
                                    </div>
                                    <div class="flex gap-3 mt-3">
                                        <img class="" src="{{ asset('images/bigGreenTick.svg') }}" alt="bigGreenTick">
                                        <p class="GrayText font-normal text-base">Individual Licenses with access to
                                            Unlimited Plans</p>
                                    </div>
                                    <div class="flex gap-3 mt-1">
                                        <img class="" src="{{ asset('images/bigGreenTick.svg') }}" alt="bigGreenTick">
                                        <p class="GrayText font-normal text-base">Private Data</p>
                                    </div>

                                    <div class="mt-4 flex border justify-center items-center rounded-lg cursor-pointer font-semibold p-2">
                                        <a href="{{ route('upgrade.sendRequest', ['Option' => 'Platinum']) }}">Contact
                                            Sales</a>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div> -->
                </div>
                <div class="flex-1 mt-5 bg-white p-4 border border-gray-200 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="w-full">
                        <h2 class="text-xl font-semibold">User(s)</h2>
                        </div>
                        <div class="flex items-left gap-x-2">
                            <div class="flex flex-1 justify-end">
                                <div class="flex" x-data="{ uopen: @entangle('uopen') }">
                                    <!-- Trigger button inside the modal -->
                                    <div class="flex items-center justify-end">
                                        <div>
                                            <button x-on:click="uopen = true" class="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium w-32 text-white text-md justify-center">
                                                <img class="h-4 font-bold w-auto img px-1" src="{{ asset('images/plus-white-without-circle.svg') }}">
                                                <h1 class="text-md font-semibold">Add User</h1>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- Modal container -->
                                    <div x-show="uopen" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
                                        <!-- Modal background with a higher z-index -->
                                        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
                                        <!-- Modal content with a lower z-index -->
                                        <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
                                            <!-- Modal content -->
                                            <div class="flex justify-between">
                                                <h2 class="text-black-900 text-xl font-semibold">Add User</h2>
                                                <button type="button" @click="uopen = false" class="text-gray-500 hover:text-gray-800">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                    </svg>
                                                </button>
                                            </div>
                                            <div class="w-full border-t mt-3 border-gray-200"></div>
                                            <div class="mt-4">
                                                <label class="text-lg text-gray-800 font-light">Send an email to
                                                    the
                                                    person you would like to invite below.</label>
                                                <div>
                                                    <div class="relative" >
                                                        <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 16">
                                                                <path d="m10.036 8.278 9.258-7.79A1.979 1.979 0 0 0 18 0H2A1.987 1.987 0 0 0 .641.541l9.395 7.737Z" />
                                                                <path d="M11.241 9.817c-.36.275-.801.425-1.255.427-.428 0-.845-.138-1.187-.395L0 2.6V14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2.5l-8.759 7.317Z" />
                                                            </svg>
                                                        </div>
                                                        <input type="text" wire:model="email" id="email-address-icon" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 placeholder:text-gray-500" placeholder="Email Address">
                                                    </div>
                                                    @error('email')
                                                    <span class="text-red-500 text-xs">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="w-full border-t mt-3 border-gray-200"></div>
                                            <div class="flex gap-2 w-full mt-4">
                                                <button x-on:click="uopen =false" type="button" class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                                                    <span class="block font-medium">Cancel</span>
                                                </button>
                                                <button wire:click="sendInvite" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                                    <span class="block"> Send</span>
                                                </button>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-1 justify-end">
                            <div class="flex" x-data="{ vopen: @entangle('vopen') }">
                                <!-- Trigger button inside the modal -->
                                <div class="flex items-center justify-end">
                                    <div>
                                        <button x-on:click="vopen = true" class="bg-cyan-500 flex items-center w-40 space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium text-white text-md justify-center">
                                            <img class="h-4 font-bold w-auto img px-1" src="{{ asset('images/plus-white-without-circle.svg') }}">
                                            <h1 class="text-md font-semibold">Add Viewer</h1>
                                        </button>
                                    </div>
                                </div>
                                <!-- Modal container -->
                                <div x-show="vopen" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
                                    <!-- Modal background with a higher z-index -->
                                    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
                                    <!-- Modal content with a lower z-index -->
                                    <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
                                        <!-- Modal content -->
                                        <div class="flex justify-between">
                                            <h2 class="text-black-900 text-xl font-semibold">Add Viewer</h2>
                                            <button type="button" @click="vopen = false" class="text-gray-500 hover:text-gray-800">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="w-full border-t mt-3 border-gray-200"></div>
                                        <div class="mt-4">
                                            <label class="text-lg text-gray-800 font-light">Send an email to
                                                the
                                                person you would like to invite below.</label>
                                            <div>
                                                <div class="relative" >
                                                    <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 16">
                                                            <path d="m10.036 8.278 9.258-7.79A1.979 1.979 0 0 0 18 0H2A1.987 1.987 0 0 0 .641.541l9.395 7.737Z" />
                                                            <path d="M11.241 9.817c-.36.275-.801.425-1.255.427-.428 0-.845-.138-1.187-.395L0 2.6V14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2.5l-8.759 7.317Z" />
                                                        </svg>
                                                    </div>
                                                    <input type="text" wire:model="email" id="email-address-icon" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 placeholder:text-gray-500" placeholder="Email Address">
                                                </div>
                                                @error('email')
                                                <span class="text-red-500 text-xs">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="w-full border-t mt-3 border-gray-200"></div>
                                        <div class="flex gap-2 w-full mt-4">
                                            <button x-on:click="vopen =false" type="button" class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                                                <span class="block font-medium">Cancel</span>
                                            </button>
                                            <button wire:click="sendInviteViewer" type="button" class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                                <span class="block"> Send</span>
                                            </button>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>

                        
                    </div>
                    
                    <div>
                        <div class="mt-3 border rounded-lg overflow-x-auto">

                            <table class=" min-w-full border-separate border-spacing-0 overflow-x-auto">
                                <thead>
                                    <tr class="grayBackground">
                                        <th scope="col" class=" border-b border-gray-300 bg-white bg-opacity-75 py-3.5 pl-4 pr-3 text-left text-sm font-semibold GrayText backdrop-blur backdrop-filter sm:pl-6 lg:pl-8">
                                            Name</th>
                                        <th scope="col" class="  border-b border-gray-300 bg-white bg-opacity-75 px-3 py-3.5 text-left text-sm font-semibold GrayText backdrop-blur backdrop-filter">
                                            Role</th>
                                        <th scope="col" class=" border-b border-gray-300 bg-white bg-opacity-75 px-3 py-3.5 text-left text-sm font-semibold GrayText backdrop-blur backdrop-filter">
                                            Date Joined</th>
                                        <th scope="col" class=" border-b border-gray-300 bg-white bg-opacity-75 px-3 py-3.5 text-left text-sm font-semibold GrayText backdrop-blur backdrop-filter">
                                            Last Active</th>
                                        <th scope="col" class=" border-b border-gray-300 bg-white bg-opacity-75 px-3 py-3.5 text-left text-sm font-semibold GrayText backdrop-blur backdrop-filter">
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($licenses as $license)
                                    <tr>
                                        <td class="whitespace-nowrap border-b border-gray-200 py-1 pl-4 pr-3 text-sm font-normal text-gray-900 sm:pl-6 lg:pl-8">
                                            {{ $license->name }}
                                        </td>
                                        <td class="whitespace-wrap border-b border-gray-200 font-normal  px-3 py-1 text-sm  ">
                                            {{ $license->role }}
                                        </td>
                                        <td class="whitespace-wrap border-b border-gray-200 font-normal px-3 py-1 text-sm  ">
                                            {{ $license->created_at ? Carbon\Carbon::parse($license->created_at)->format('d-M-Y') : "N/A" }}
                                        </td>
                                        <td class="whitespace-wrap border-b border-gray-200 font-normal px-3 py-1 text-sm  ">
                                            {{ $license->last_activity ? Carbon\Carbon::parse($license->last_activity)->diffForHumans() : "N/A" }}
                                        </td>
                                        <td class="whitespace-wrap border-b border-gray-200  px-3 py-1 text-sm  ">
                                            <div class="-ml-px flex w-full flex-1 items-center p-2">
                                                <div class="w-full flex" x-data="{ dopen: false }">
                                                    <!-- Trigger button inside the modal -->
                                                    <div class="w-full flex items-center justify-center">
                                                        <button @click="confirmDelete({{ $license->id }}, 'Delete {{ $license->name }}', 'Are you sure want to delete the user <b>{{ $license->name }}</b>?', '{{ asset('images/redTrashIcon.svg') }}', 'removeUser')" class="w-full relative flex items-center justify-center gap-x-2 rounded-br-lg border border-transparent py-2 px-3 text-sm font-semibold text-gray-900">
                                                            <img class="h-4 w-auto" src="{{ asset('images/grayDeleteIcon.svg') }}">
                                                        </button>
                                                    </div>
                                                    <!-- Modal container -->
                                                    <div x-show="dopen" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50" style="display:none">
                                                        <!-- Modal background with a higher z-index -->
                                                        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40">
                                                        </div>
                                                        <!-- Modal content with a lower z-index -->
                                                        <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50 w-96" x-show="dopen" x-transition>
                                                            <!-- Modal content -->
                                                            <p>Are you sure you want to remove this individual
                                                                please
                                                                keep in mind you may lose any relevant plans or
                                                                recruiting pipelines if these have not been shared
                                                                with
                                                                other teammates</p>
                                                            <div class="mt-5 flex flex-1 items-center justify-end gap-x-6">
                                                                <div class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                                    <button x-on:click="dopen =false" class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                                                </div>
                                                                <div class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                                    <button x-on:click="dopen =false" wire:click="removeUser({{ $license->id }})" class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Yes
                                                                        I'm Sure</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            {{$licenses->links()}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('livewire.loading')
        <script>
            function confirmDelete(id, title, message, iconUrl, dispatchName) {
                Swal.fire({
                    html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">${title}</h2>
                    <p class="px-5 font-normal">${message}</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
                    showDenyButton: true,
                    showCancelButton: false,
                    confirmButtonText: "Delete",
                    denyButtonText: `Cancel`,
                    reverseButtons: true,
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                        denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
                    },
                    showCloseButton: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        Livewire.dispatch(dispatchName, {
                            id: id
                        });
                    }
                });
            }
        </script>
    </div>
