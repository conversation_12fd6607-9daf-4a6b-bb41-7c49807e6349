<div x-data="{
    criteriaRows: @entangle('criteriaRows'),
    errors: [],
    addRowClicked: false,

    addRow() {
        // Add a new row with an empty ID to indicate it's a new entry
        this.criteriaRows.push({ id: null, criteria: '', isChecked: 'No', deleted: false });
        this.errors = [];
        this.addRowClicked = true;
        setTimeout(() => { this.addRowClicked = false; }, 50);
    },

    removeRow(index) {
        // Mark the row as deleted
        this.criteriaRows[index].deleted = true;
        this.validateCriteria(); // Validate after marking a row for deletion
    },

    validateCriteria() {
        this.errors = [];
        if (!this.addRowClicked) {
            this.criteriaRows.forEach((row, index) => {
                if (!row.criteria) {
                    this.errors.push({ index, field: 'criteria', message: 'The criteria field cannot be empty.' });
                } else if (row.criteria.length > 255) {
                    this.errors.push({ index, field: 'criteria', message: 'The criteria may not be greater than 255 characters.' });
                }
                if (row.isChecked !== 'Yes' && row.isChecked !== 'No') {
                    this.errors.push({ index, field: 'isChecked', message: 'The checkbox value must be Yes or No.' });
                }
            });
        }
    },

    saveCriteria() {
        this.validateCriteria();
        if (this.errors.length === 0) {
            @this.call('saveCriteria');
        }
    }
}" x-init="$watch('criteriaRows', value => validateCriteria(), { deep: true })">
    <!-- Modal content remains unchanged -->

    <div x-show="assessmentCriteriaPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50 ">
            <div class="flex justify-between items-center p-4">
                <h2 class="text-sm font-bold chart-heading">Add Assessment Criteria</h2>
                <button type="button" @click="assessmentCriteriaPopup = false" class="text-gray-500 hover:text-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Border above buttons -->
            <div class="w-full border-t border-gray-200"></div>

            <!-- Criteria Rows -->
            <div class="p-4 overflow-y-auto" style="height:100%;max-height:400px;">
                <template x-for="(row, index) in criteriaRows" :key="index">
                    <div x-show="!row.deleted" class="flex flex-col gap-2 mb-4">
                        <!-- Existing input fields and error handling here -->
                        <div class="flex items-center gap-2">
                            <input type="text"
                                x-model="row.criteria"
                                class="mt-1 placeholder:text-[#667085] placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                                placeholder="Enter the criteria">
                            <!-- Toggle switch and delete icon -->
                            <div class="switch-toggle">
                                <input type="checkbox" 
                                    x-model="row.isChecked"
                                    :checked="row.isChecked === 'Yes'" 
                                    @change="row.isChecked = $event.target.checked ? 'Yes' : 'No'"
                                    class="switch-toggle-checkbox" />
                                <label class="switch-toggle-label">
                                    <span>No</span>
                                    <span>Yes</span>
                                </label>
                            </div>

                            <div class="border px-3 rounded-lg critreaDelete cursor-pointer" @click="removeRow(index)">
                                <img class="w-5 h-5" src="{{ asset('images/red_trash_icon_without_circle.svg') }}" alt="Trash Icon">
                            </div>
                        </div>

                        <!-- Display error message if present -->
                        <template x-if="errors.some(error => error.index === index && error.field === 'criteria') && !addRowClicked">
                            <span class="text-red-500 text-xs" x-text="errors.find(error => error.index === index && error.field === 'criteria').message"></span>
                        </template>
                    </div>
                </template>

                <!-- Add More Button -->
                <button @click="addRow()"
                    id="assessmentCriteriaButton"
                    class="transition mt-3 flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100">
                    <img class="h-3 w-auto" src="{{ asset('images/Plus.svg') }}" alt="Plus Icon">
                    <span>Add more</span>
                </button>
            </div>

            <!-- Buttons for second step -->
            <div class="flex gap-2 w-full p-4 text-sm border-t">
                <button @click="assessmentCriteriaPopup = false" type="button"
                    class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                    <span class="block font-medium">Cancel</span>
                </button>
                <button @click="saveCriteria" type="button"
                    class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">
                    <span class="block font-semibold">Save Changes</span>
                </button>
            </div>
        </div>
    </div>
</div>
