@foreach($topPeople as $topPerson)
    <div x-transition.duration.500ms x-init="sopen[{{ $topPerson->id }}] = true"
    class="flex-1 items-center justify-center rounded-full border-black">
    <div class="flex items-center justify-center">
        <div class="h-32 w-64 rounded-3xl border border-gray-200 bg-white p-2">
            <div class="flex w-full gap-x-2">
                <div class="">
                    <h4 class="truncate text-left text-sm font-semibold text-cyan-500">{{ $topPerson->forename}}
                        {{ $topPerson->surname}}</h4>
                    <p class="truncate text-left text-sm text-gray-700">{{ $topPerson->role}}</p>
                    <p class=" white-space:nowrap mt-1 truncate text-left text-xs">{{ $topPerson->division}} -
                        {{ $topPerson->function}}</p>
                </div>
                <div class="flex flex-1 justify-end">
                    <div class="">
                        <div>
                            <h4 class="text-center text-xs">Readiness</h4>
                            @if ($topPerson->readiness ?? null)
                                <p class="text-sm text-center text-gray-600 font-semibold">Yes</p>
                            @else

                                <p class="text-sm text-center text-gray-600 font-semibold">No</p>
                            @endif

                        </div>
                        <div class="mt-1">
                            <h4 class="text-center text-xs">In-Plans</h4>
                            @if ($topPerson->isSuccessPerson === 1)
                                <p class="text-sm text-center text-gray-600 font-semibold">Yes</p>
                            @else

                                <p class="text-sm text-center text-gray-600 font-semibold">No</p>
                            @endif

                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-1 justify-center mt-2 gap-x-10">
                @if($topPerson->plan_id === null)
                    <div x-data="{ open: false }">
                        <!-- Trigger button inside the modal -->
                        <div class="">
                            <button x-on:click="open = true"
                                class="w-full relative flex items-center justify-center border border-transparent hover:scale-105">
                                <div class="flex gap-x-2 items-center">
                                    <img id="CreatePlan" class="h-4 w-auto" src="{{ asset('images/clipboard.png') }}">
                                </div>
                            </button>
                        </div>
                        <!-- Modal container -->
                        <div x-show="open" class="fixed inset-0 bg-white flex items-center justify-center z-50"
                            style="display:none" x-transition.opacity>
                            <!-- Modal background with a higher z-index -->
                            <div class="modal-background fixed inset-0 z-40"></div>
                            Modal content with a lower z-index
                            <div x-data="{ first:true, second:false, third:false, fourth:false }"
                                class="modal-content bg-white h-192 w-192 p-4 z-50">
                                <!-- Modal content -->
                                <div class="h-full" x-show="first" style="display:none">
                                    <div class="h-5/6 flex items-center">
                                        <div class="w-full">
                                            <h3 class="mt-5 text-black text-center font-medium text-2xl">Plan Details</h3>
                                            <div class="relative py-2 bg-white">
                                                <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                                                    <div class="w-full border-t-2 border-gray-200"></div>
                                                </div>
                                            </div>
                                            <div class="">
                                                <label for="planName" class="text-sm font-medium text-black">Name</label>
                                                <input type="text" wire:model="pname"
                                                    class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                    placeholder="Role Name in Function-Division in Country." required>
                                            </div>
                                            <div class="mt-4 col-span-full">
                                                <label for="about"
                                                    class="text-sm font-medium text-black">Description</label>
                                                <textarea id="description" wire:model="pdescriptions" rows="2"
                                                    class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                    required></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-5 flex items-center justify-center gap-x-6">
                                        <div
                                            class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                            <button x-on:click="first = false, second = true" type="button"
                                                class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Next
                                                Step</button>
                                        </div>
                                        <div
                                            class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                            <button x-on:click="open = false" type="button"
                                                class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                        </div>
                                    </div>
                                </div>
                                <div x-show="second" style="display:none" x-transition.opacity:enter.duration.800ms>
                                    <div class="flex items-center justify-center">
                                        <button x-on:click="first = true, second = false"
                                            class="text-slate-400 font-medium hover:text-slate-500">Previous</button>
                                    </div>
                                    <h3 class="mt-5 text-sky-800 text-center font-medium text-2xl">Requirements</h3>
                                    <h3 class="text-rose-400 text-center font-medium text-lg">Current Experience</h3>
                                    <div class="relative py-2 bg-white">
                                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                                            <div class="w-full border-t-2 border-gray-200"></div>
                                        </div>
                                    </div>
                                    <p class="text-sm font-light text-gray-700">Enter any requirements you have for your
                                        Succession Plan below.</p>
                                    <div>
                                        <div class="mt-4">
                                            <label for="planName" class="text-sm font-medium text-black">Target
                                                Role(s)</label>
                                            <input type="text" wire:model="proles"
                                                class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                placeholder="Head of Risk, CFO">
                                        </div>
                                        <div class="mt-4">
                                            <label for="planName" class="text-sm font-medium text-black">Minimum
                                                Tenure</label>
                                            <input type="number" wire:model="min_exp"
                                                class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                placeholder="10">
                                        </div>
                                        <div class="mt-4">
                                            <label for="planName" class="text-sm font-medium text-black">Step-up
                                                Candidates</label>
                                            <input type="text" wire:model="pstepup"
                                                class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                placeholder="Head of Risk, CFO, Not Applicable">
                                        </div>
                                        <div class="mt-4">
                                            <label for="planName" class="text-sm font-medium text-black">Companies</label>
                                            <input type="text" wire:model="pcompany"
                                                class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                placeholder="Company A, Company B, Not Applicable">
                                        </div>

                                    </div>
                                    <div class="mt-5 flex items-center justify-center gap-x-6">
                                        <div
                                            class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                            <button x-on:click="first = false; second = false, third = true" type="button"
                                                class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Next
                                                Step</button>
                                        </div>
                                        <div
                                            class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                            <button x-on:click="open = false" type="button"
                                                class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                        </div>
                                    </div>
                                </div>
                                <div x-show="third" style="display:none" x-transition.opacity:enter.duration.800ms>
                                    <div class="flex items-center justify-center">
                                        <button x-on:click="second = true, third = false"
                                            class="text-slate-400 font-medium hover:text-slate-500">Previous</button>
                                    </div>
                                    <h3 class="mt-5 text-sky-800 text-center font-medium text-2xl">Requirements</h3>
                                    <h3 class="text-rose-400 text-center font-medium text-lg">Additional Details</h3>
                                    <div class="relative py-2 bg-white">
                                        <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                                            <div class="w-full border-t-2 border-gray-200"></div>
                                        </div>
                                    </div>
                                    <p class="text-sm font-light text-gray-700">Enter any requirements you have for your
                                        Succession Plan below.</p>
                                    <div>
                                        <div class="mt-4">
                                            <label for="planName" class="text-sm font-medium text-black">Gender</label>
                                            <input type="text" wire:model="pgender"
                                                class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                placeholder="Female, Male, All">
                                        </div>
                                        <div class="mt-4">
                                            <label for="planName" class="text-sm font-medium text-black">Country</label>
                                            <div wire:ignore x-data="{
                                                                        multiple: true,
                                                                        value: @entangle('selectedCountries'),
                                                                        options: {{ json_encode($countries) }},
                                                                        init() {
                                                                            this.$nextTick(() => {
                                                                                let choices = new Choices(this.$refs.select)

                                                                                let refreshChoices = () => {
                                                                                    let selection = this.multiple ? this.value : [this.value]

                                                                                    choices.clearStore()
                                                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                                                        value,
                                                                                        label,
                                                                                        selected: selection.includes(value),
                                                                                    })))
                                                                                }

                                                                                refreshChoices()

                                                                                this.$refs.select.addEventListener('change', () => {
                                                                                    this.value = choices.getValue(true)
                                                                                })

                                                                                this.$watch('value', () => refreshChoices())
                                                                                this.$watch('options', () => refreshChoices())
                                                                            })
                                                                        }
                                                                    }" class="max-w-sm w-full">
                                                <select x-ref="select" :multiple="multiple"></select>
                                            </div>
                                        </div>
                                        <div class="mt-4">
                                            <fieldset>
                                                <legend class="text-sm font-medium text-black">Is Ethnicity important?
                                                </legend>
                                                <div class="flex-wrap">
                                                    <p class="text-xs leading-6 text-gray-600">Please note profiling through
                                                        ethnicity has a low accuracy only select if this is absolutely
                                                        neccessary</p>
                                                </div>
                                                <div class="mt-1 flex space-x-3">
                                                    <div class="flex items-center gap-x-3">
                                                        <input type="radio" wire:model="ethnicity"
                                                            class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black"
                                                            value="1">
                                                        <label for="push-yes"
                                                            class="block text-xs leading-6 text-gray-900">Yes</label>
                                                    </div>
                                                    <div class="flex items-center gap-x-3">
                                                        <input type="radio" wire:model="ethnicity"
                                                            class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black"
                                                            value="0">
                                                        <label for="push-no" class="block text-xs text-gray-900">No</label>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                        <div class="mt-4">
                                            <label for="planName"
                                                class="text-sm font-medium text-black">Qualifications</label>
                                            <input type="text" wire:model="education"
                                                class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                placeholder="CFA-Level 3, Another Qualification">
                                        </div>
                                        <div class="mt-4">
                                            <label for="planName" class="text-sm font-medium text-black">Skills</label>
                                            <input type="text" wire:model="skills"
                                                class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                placeholder="Risk Management, Public Speaking, Not Applicable">
                                        </div>
                                    </div>

                                    <div class="mt-5 flex items-center justify-center gap-x-6">
                                        <div
                                            class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                            <button x-on:click="open = false"
                                                wire:click="CreatePlanTravel({{ $topPerson->id }})" type="submit"
                                                class="transition ease-in-out w-full delay-100 text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Save
                                                & View</button>
                                        </div>
                                        <div
                                            class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                            <button x-on:click="open = false" wire:click="CreatePlan({{ $topPerson->id }})"
                                                type="submit"
                                                class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Save
                                                & Close</button>
                                        </div>
                                        <div
                                            class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                            <button x-on:click="open = false" type="button"
                                                class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @else

                    <div class="w-8 relative flex items-center justify-center border border-transparent hover:scale-105">
                        <a href="{{ route('plan.show', ['plan' => $topPerson->plan_id]) }}" class="">
                            <img id="showPlan" class="h-4 w-auto" src="{{ asset('images/diagram.png') }}">
                        </a>
                    </div>
                @endif

                <!-- The button to view the details of an individual -->
                <div class="" x-data="{ iopen: false }">
                    <!-- Trigger button inside the modal -->
                    <div class="">
                        <button wire:click="showIndividual({{ $topPerson->id }})"
                            class="w-full relative flex items-center justify-center border border-transparent hover:scale-105">
                            <img id="view" class="h-4 w-auto" src="{{ asset('images/view.png') }}">
                        </button>
                    </div>
                    @if($viewIndividual)
                        <div>
                            <livewire:internal-show :$topPerson :key="$topPerson->id" />
                        </div>
                    @endif

                </div>
                @if ($topPerson->subordinates->isNotEmpty())
                    <div class="flex items-center px-1">
                        <button x-on:click="sopen[{{ $topPerson->id }}] = true">
                            <div class="flex gap-x-2 items-center">
                                <label class="text-xs">{{$topPerson->subordinate_count}}</label>
                                <img id="expand" class="h-4 w-auto" src="{{ asset('images/plus.png') }}">
                            </div>
                        </button>
                    </div>
                @endif


                <!-- The delete button -->
                <div class="" x-data="{ dopen: false }">
                    <!-- Trigger button inside the modal -->
                    <div class="">
                        <button @click="dopen = true"
                            class="w-full relative flex items-center justify-center gap-x-2 rounded-br-lg border border-transparent hover:scale-105">
                            <img id="delete" class="h-4 w-auto " src="{{ asset('images/trash.png') }}">
                        </button>
                    </div>
                    <!-- Modal container -->
                    <div x-show="dopen" class="fixed backdrop-blur-sm inset-0 flex items-center justify-center z-50"
                        style="display:none">
                        <!-- Modal background with a higher z-index -->
                        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
                        <!-- Modal content with a lower z-index -->
                        <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50 w-96" x-show="dopen"
                            x-transition>
                            <!-- Modal content -->
                            <p class="text-sm text-gray-700 font-semibold"> Are you sure you want to remove this
                                individual? Removing Internal Employees may impact other plans.</p>
                            <div class="mt-2 flex items-center justify-center gap-x-6">
                                <div
                                    class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                    <button x-on:click="dopen = false"
                                        class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                </div>
                                <div
                                    class="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                    <button x-on:click="dopen = false"
                                        wire:click="removeInternalCandidates({{ $topPerson->id }})"
                                        class="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Yes
                                        I'm Sure</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if ($topPerson->subordinates->isNotEmpty())
        <div x-transition.duration.500ms x-show="sopen[{{ $topPerson->id }}]">
            <div class="mt-5 flex flex-1">
                <div class="py-2 flex flex-1 justify-end rounded-lg border-t border-black">
                    <button x-on:click="sopen[{{ $topPerson->id }}] = !sopen[{{ $topPerson->id }}]">
                        <span class="text-xs text-gray-800 font-light">Hide</span>
                        <button>
                </div>
            </div>
            <div class="mt-2 gap-x-10 flex flex-1 items-center justify-center">
                @foreach ($topPerson->subordinates as $subordinate)
                            <div class="p-2 h-32 w-64 border border-gray-200 rounded-3xl bg-white">
                        <div class="flex gap-x-2">
                            <div class="w-48 overflow-hidden text-ellipsis">
                                <h4 class="truncate text-left text-sm font-semibold text-cyan-500">{{ $subordinate->forename}}
                                    {{ $subordinate->surname}}</h4>
                                <p class="truncate text-left text-sm text-gray-700">{{ $subordinate->role}}</p>
                                <p class="white-space:nowrap mt-1 truncate text-left text-xs">{{ $subordinate->division}} -
                                    {{ $subordinate->function}}</p>
                            </div>
                            <div class="flex flex-1 justify-end">
                                <div class="">
                                    <div>
                                        <h4 class="text-xs text-center">Readiness</h4>
                                        @if ($subordinate->readiness ?? null)
                                            <p class="text-sm text-center text-gray-600 font-semibold">Yes</p>
                                        @else

                                            <p class="text-sm text-center text-gray-600 font-semibold">No</p>
                                        @endif

                                    </div>
                                    <div class="mt-1">
                                        <h4 class="text-xs text-center">In-Plans</h4>
                                        @if ($subordinate->isSuccessPerson === 1)
                                            <p class="text-sm text-center text-gray-600 font-semibold">Yes</p>
                                        @else

                                            <p class="text-sm text-center text-gray-600 font-semibold">No</p>
                                        @endif

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-1 mt-2 gap-x-10 justify-center">
                            @if($subordinate->plan_id === null)
                                <div x-data="{ open: false }">
                                    <!-- Trigger button inside the modal -->
                                    <div class="">
                                        <button x-on:click="open = true"
                                            class="w-full relative flex items-center justify-center border border-transparent hover:scale-105">
                                            <div class="flex gap-x-2 items-center">
                                                <img id="CreatePlan" class="h-4 w-auto" src="{{ asset('images/clipboard.png') }}">
                                            </div>
                                        </button>
                                    </div>
                                    <!-- Modal container -->
                                    <div x-show="open" class="fixed inset-0 bg-white flex items-center justify-center z-50"
                                        style="display:none" x-transition.opacity>
                                        <!-- Modal background with a higher z-index -->
                                        <div class="modal-background fixed inset-0 z-40"></div>
                                        <!-- Modal content with a lower z-index -->
                                        <div x-data="{ first:true, second:false, third:false, fourth:false }"
                                            class="modal-content bg-white h-192 w-192 p-4 z-50">
                                            <!-- Modal content -->
                                            <div class="h-full" x-show="first" style="display:none">
                                                <div class="h-5/6 flex items-center">
                                                    <div class="w-full">
                                                        <h3 class="mt-5 text-black text-center font-medium text-2xl">Plan Details
                                                        </h3>
                                                        <div class="relative py-2 bg-white">
                                                            <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                                                                <div class="w-full border-t-2 border-gray-200"></div>
                                                            </div>
                                                        </div>
                                                        <div class="">
                                                            <label for="planName"
                                                                class="text-sm font-medium text-black">Name</label>
                                                            <input type="text" wire:model="name"
                                                                class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                                placeholder="Role Name in Function-Division in Country." required>
                                                        </div>
                                                        <div class="mt-4 col-span-full">
                                                            <label for="about"
                                                                class="text-sm font-medium text-black">Description</label>
                                                            <textarea id="description" wire:model="descriptions" rows="2"
                                                                class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                                required></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-5 flex items-center justify-center gap-x-6">
                                                    <div
                                                        class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                        <button x-on:click="first = false, second = true" type="button"
                                                            class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Next
                                                            Step</button>
                                                    </div>
                                                    <div
                                                        class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                        <button x-on:click="open = false" type="button"
                                                            class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div x-show="second" style="display:none" x-transition.opacity:enter.duration.800ms>
                                                <div class="flex items-center justify-center">
                                                    <button x-on:click="first = true, second = false"
                                                        class="text-slate-400 font-medium hover:text-slate-500">Previous</button>
                                                </div>
                                                <h3 class="mt-5 text-sky-800 text-center font-medium text-2xl">Requirements</h3>
                                                <h3 class="text-rose-400 text-center font-medium text-lg">Current Experience</h3>
                                                <div class="relative py-2 bg-white">
                                                    <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                                                        <div class="w-full border-t-2 border-gray-200"></div>
                                                    </div>
                                                </div>
                                                <p class="text-sm font-light text-gray-700">Enter any requirements you have for your
                                                    Succession Plan below.</p>
                                                <div>
                                                    <div class="mt-4">
                                                        <label for="planName" class="text-sm font-medium text-black">Target
                                                            Role(s)</label>
                                                        <input type="text" wire:model="roles"
                                                            class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Head of Risk, CFO">
                                                    </div>
                                                    <div class="mt-4">
                                                        <label for="planName" class="text-sm font-medium text-black">Minimum
                                                            Tenure</label>
                                                        <input type="number" wire:model="min_exp"
                                                            class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="10">
                                                    </div>
                                                    <div class="mt-4">
                                                        <label for="planName" class="text-sm font-medium text-black">Step-up
                                                            Candidates</label>
                                                        <input type="text" wire:model="stepup"
                                                            class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Head of Risk, CFO, Not Applicable">
                                                    </div>
                                                    <div class="mt-4">
                                                        <label for="planName"
                                                            class="text-sm font-medium text-black">Companies</label>
                                                        <input type="text" wire:model="company"
                                                            class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Company A, Company B, Not Applicable">
                                                    </div>

                                                </div>
                                                <div class="mt-5 flex items-center justify-center gap-x-6">
                                                    <div
                                                        class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                        <button x-on:click="first = false; second = false, third = true"
                                                            type="button"
                                                            class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Next
                                                            Step</button>
                                                    </div>
                                                    <div
                                                        class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                        <button x-on:click="open = false" type="button"
                                                            class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div x-show="third" style="display:none" x-transition.opacity:enter.duration.800ms>
                                                <div class="flex items-center justify-center">
                                                    <button x-on:click="second = true, third = false"
                                                        class="text-slate-400 font-medium hover:text-slate-500">Previous</button>
                                                </div>
                                                <h3 class="mt-5 text-sky-800 text-center font-medium text-2xl">Requirements</h3>
                                                <h3 class="text-rose-400 text-center font-medium text-lg">Additional Details</h3>
                                                <div class="relative py-2 bg-white">
                                                    <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                                                        <div class="w-full border-t-2 border-gray-200"></div>
                                                    </div>
                                                </div>
                                                <p class="text-sm font-light text-gray-700">Enter any requirements you have for your
                                                    Succession Plan below.</p>
                                                <div>
                                                    <div class="mt-4">
                                                        <label for="planName" class="text-sm font-medium text-black">Gender</label>
                                                        <input type="text" wire:model="gender"
                                                            class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Female, Male, All">
                                                    </div>
                                                    <div class="mt-4">
                                                        <label for="planName" class="text-sm font-medium text-black">Country</label>
                                                        <div wire:ignore x-data="{
                                                                                multiple: true,
                                                                                value: @entangle('selectedCountries'),
                                                                                options: {{ json_encode($countries) }},
                                                                                init() {
                                                                                    this.$nextTick(() => {
                                                                                        let choices = new Choices(this.$refs.select)

                                                                                        let refreshChoices = () => {
                                                                                            let selection = this.multiple ? this.value : [this.value]

                                                                                            choices.clearStore()
                                                                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                                                                value,
                                                                                                label,
                                                                                                selected: selection.includes(value),
                                                                                            })))
                                                                                        }

                                                                                        refreshChoices()

                                                                                        this.$refs.select.addEventListener('change', () => {
                                                                                            this.value = choices.getValue(true)
                                                                                        })

                                                                                        this.$watch('value', () => refreshChoices())
                                                                                        this.$watch('options', () => refreshChoices())
                                                                                    })
                                                                                }
                                                                            }" class="max-w-sm w-full">
                                                            <select x-ref="select" :multiple="multiple"></select>
                                                        </div>
                                                    </div>
                                                    <div class="mt-4">
                                                        <fieldset>
                                                            <legend class="text-sm font-medium text-black">Is Ethnicity important?
                                                            </legend>
                                                            <div class="flex-wrap">
                                                                <p class="text-xs leading-6 text-gray-600">Please note profiling
                                                                    through ethnicity has a low accuracy only select if this is
                                                                    absolutely neccessary</p>
                                                            </div>
                                                            <div class="mt-1 flex space-x-3">
                                                                <div class="flex items-center gap-x-3">
                                                                    <input type="radio" wire:model="ethnicity"
                                                                        class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black"
                                                                        value="1">
                                                                    <label for="push-yes"
                                                                        class="block text-xs leading-6 text-gray-900">Yes</label>
                                                                </div>
                                                                <div class="flex items-center gap-x-3">
                                                                    <input type="radio" wire:model="ethnicity"
                                                                        class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black"
                                                                        value="0">
                                                                    <label for="push-no"
                                                                        class="block text-xs text-gray-900">No</label>
                                                                </div>
                                                            </div>
                                                        </fieldset>
                                                    </div>
                                                    <div class="mt-4">
                                                        <label for="planName"
                                                            class="text-sm font-medium text-black">Qualifications</label>
                                                        <input type="text" wire:model="education"
                                                            class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="CFA-Level 3, Another Qualification">
                                                    </div>
                                                    <div class="mt-4">
                                                        <label for="planName" class="text-sm font-medium text-black">Skills</label>
                                                        <input type="text" wire:model="skills"
                                                            class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Risk Management, Public Speaking, Not Applicable">
                                                    </div>
                                                </div>

                                                <div class="mt-5 flex items-center justify-center gap-x-6">
                                                    <div
                                                        class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                        <button x-on:click="open = false"
                                                            wire:click="CreatePlanTravel({{ $subordinate->id }})" type="submit"
                                                            class="transition ease-in-out w-full delay-100 text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Save
                                                            & View</button>
                                                    </div>
                                                    <div
                                                        class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                        <button x-on:click="open = false"
                                                            wire:click="CreatePlan({{ $subordinate->id }})" type="submit"
                                                            class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Save
                                                            & Close</button>
                                                    </div>
                                                    <div
                                                        class="border border-black w-32 rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                        <button x-on:click="open = false" type="button"
                                                            class="transition ease-in-out delay-100 w-full text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @else

                                <a href="{{ route('plan.show', ['plan' => $subordinate->plan_id]) }}"
                                    class="relative -mr-px inline-flex w-0 flex-1 items-center justify-center gap-x-3 rounded-bl-lg border border-transparent text-sm font-semibold text-gray-900">
                                    <img id=showPlan class="h-4 w-auto" src="{{ asset('images/diagram.png') }}">
                                </a>
                            @endif

                            <!-- The button to view the details of an individual -->
                            <div class="" x-data="{ iopen: false }">
                                <!-- Trigger button inside the modal -->
                                <div class="">
                                    <button wire:click="showIndividual({{ $subordinate->id }})"
                                        class="w-full relative flex items-center justify-center border border-transparent hover:scale-105">
                                        <img id="view" class="h-4 w-auto" src="{{ asset('images/view.png') }}">
                                    </button>
                                </div>
                                @if($viewIndividual)
                                    <div>
                                        <livewire:internal-show :$subordinate :key="$subordinate->id" />
                                    </div>
                                @endif

                            </div>
                            <div>
                                @if(!empty($subordinate->subordinate_count))
                                    <div class="flex items-center px-1">
                                        <button wire:click="showOrganisation({{ $subordinate->id }})">
                                            <div class="flex gap-x-2 items-center">
                                                <label class="text-xs">{{ $subordinate->subordinate_count }}</label>
                                                <img id="expand" class="h-4 w-auto" src="{{ asset('images/plus.png') }}">
                                            </div>
                                        </button>
                                    </div>
                                @endif

                            </div>
                            <!-- The delete button -->
                            <div class="" x-data="{ dopen: false }">
                                <!-- Trigger button inside the modal -->
                                <div class="">
                                    <button @click="dopen = true"
                                        class="w-full relative flex items-center justify-center gap-x-2 rounded-br-lg border border-transparent hover:scale-105">
                                        <img id="delete" class="h-4 w-auto " src="{{ asset('images/trash.png') }}">
                                    </button>
                                </div>
                                <!-- Modal container -->
                                <div x-show="dopen" class="fixed inset-0 flex items-center justify-center z-50"
                                    style="display:none">
                                    <!-- Modal background with a higher z-index -->
                                    <div class="modal-background fixed inset-0 bg-slate-50 opacity-50 z-40"></div>
                                    <!-- Modal content with a lower z-index -->
                                    <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50 w-96" x-show="dopen"
                                        x-transition>
                                        <!-- Modal content -->
                                        <p class="text-sm text-gray-700 font-semibold"> Are you sure you want to remove this
                                            individual? Removing Internal Employees may impact other plans.</p>
                                        <div class="mt-2 flex items-center justify-center gap-x-6">
                                            <div
                                                class="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                <button x-on:click="dopen =false"
                                                    class="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                                            </div>
                                            <div
                                                class="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                                                <button x-on:click="dopen =false"
                                                    wire:click="removeInternalCandidates({{ $subordinate->id }})"
                                                    class="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium  hover:bg-cyan-500 hover:text-white duration-100">Yes
                                                    I'm Sure</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
                </div>
        </div>
    @endif


</div>@endforeach