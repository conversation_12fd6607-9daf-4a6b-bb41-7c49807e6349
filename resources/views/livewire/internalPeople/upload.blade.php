<div class="" x-data="{ uploadPopup: @entangle('uploadPopup'), duplicateUploadPopup: @entangle('duplicateUploadPopup') }">
    <!-- Trigger button inside the modal -->
    <div class="w-32 border rounded-lg p-px shadow-md hover:shadow-lg hover:scale-105">
        <button @click="uploadPopup = true"
            class="w-full flex justify-center items-center gap-2 font-semibold transition ease-in-out delay-100 text-sm text-black rounded-lg py-2 hover:bg-cyan-500 hover:text-white duration-100">
            <img class="h-3 w-auto" src="{{ asset('images/CloudArrowUp.svg') }}" alt="Your Company">
            <span> Upload</span>
        </button>
    </div>
    <!-- Modal container -->
    <div x-show="uploadPopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50"
        style="display:none">
        <!-- Modal background with a higher z-index -->
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
        <!-- Modal content with a lower z-index -->
        <div class="modal-content2 bg-white relative shadow-md rounded-lg p-4 z-50">
            <!-- Modal content -->
            <p class="text-black font-semibold">Bulk upload candidates</p>
            <img @click="uploadPopup = false" class="absolute bulkCross top-1 w-auto cursor-pointer"
                src="{{ asset('images/cross.svg') }}" alt="cross Icon">
            <form class="mt-3" wire:submit.prevent="uploadCSV">
                @csrf
                @if ($errors->any())
                    <div class="text-red-500 text-sm text-center">
                        @foreach ($errors->all() as $error)
                            <p>{{ $error }}</p>
                        @endforeach
                    </div>
                @endif

                <div class="mt-1 grid grid-cols-2 gap-1">
                    <div class="col-span-2">
                        <label for="internal-candidate"
                            class="block text-sm text-center font-semibold leading-6 text-gray-900">Internal
                            Candidates</label>
                        <div
                            class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                            <div class="text-center">
                                {{-- @if ($csvFile) --}}
                                    
                                {{-- @else --}}
                                    <div class="flex items-center justify-center">
                                        <img class="h-10 w-10" src="{{ asset('images/upload.png') }}">
                                    </div>
                                    <div class="mt-2 flex justify-center text-sm leading-6 text-gray-600">
                                        <label for="file-upload"
                                            class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 hover:text-cyan-600">
                                            <span>Upload a file</span>
                                            <input id="file-upload" wire:model="csvFile" name="file-upload" type="file" class="sr-only" accept=".csv, .xlsx, .xls" >

                                    </div>
                                    <p class="text-xs  text-gray-600">(CSV or Excel)</p>
                                    @if ($csvFile && $csvFile->getClientOriginalName())
                                        <p class="text-sm  text-gray-600">
                                            <span class="font-semibold">Selected File:</span>
                                            {{ $csvFile->getClientOriginalName() }}
                                        </p>
                                    @endif
                                {{-- @endif --}}
                            </div>
                        </div>
                    </div>
                    <div class="col-span-2">
                        <label for="cover-photo"
                            class="block text-sm font-medium text-center leading-6 text-gray-900">Download
                            Templates</label>
                        <div
                            class="mt-2 h-40 flex justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10">
                            <div class="text-center">
                                <div class="flex items-center justify-center">
                                    <img class="h-10 w-10" src="{{ asset('images/download.png') }}">
                                </div>
                                <div class="mt-4 flex text-sm leading-6 text-gray-600">
                                    <label for="file-upload"
                                        class="relative cursor-pointer rounded-md bg-white font-semibold text-cyan-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 hover:text-cyan-600">
                                        <button type = "button" wire:click="downloadCSV">Download Template Files</button>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-2 flex items-center justify-end gap-x-6">
                    <div class="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                        <button @click="uploadPopup = false" type="button"
                            class="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Cancel</button>
                    </div>
                    <div class="w-32 border border-black rounded-full p-px shadow-md hover:shadow-lg hover:scale-105">
                        <button type="submit"
                            class="w-full transition ease-in-out delay-100 text-sm text-black rounded-full py-1 px-2 font-medium hover:bg-cyan-500 hover:text-white duration-100">Upload</button>
                    </div>
                </div>
                {{-- @include('livewire.jobPeople.duplicateUpload') --}}

            </form>
        </div>
    </div>

</div>
