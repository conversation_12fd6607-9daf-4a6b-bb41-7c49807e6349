<!-- Modal -->
<div x-data="ReplaceUserComponent()" x-show="replaceUserOrgPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">

        <div class="flex justify-between">
            <h2 class="text-black-900 text-xl font-semibold">Replace user</h2>
            <button type="button" @click="replaceUserOrgPopup = false" class="text-gray-500 hover:text-gray-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Search box -->
        <div class="flex justify-center items-center mt-2" x-data="{ query: '', isFocused: false, firstFocus: false }">
            <div class="bg-white border selectPeople p-2 rounded-lg">
                <div class="search-container flex items-center mt-2">
                    <img class="search-icon h-4 w-auto" src="{{ asset('images/MagnifyingGlass.svg') }}" alt="Search Icon">
                    <input x-on:focus="isFocused = true; firstFocus = true;" x-on:blur="isFocused = false"
                        type="text" x-model="query"
                        class="w-full bordermainBlue outline-none block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-500 sm:text-md sm:leading-6"
                        placeholder="Search">
                        <button type="button"
                        @click="search()"
                        class="bg-mainBlue text-white px-4 py-2 rounded-r-md flex items-center justify-center focus:outline-none hover:bg-blue-700"
                        >
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <div class="searchBox flex justify-center flex-col items-center" x-show="!firstFocus">
                    <img class="" src="{{ asset('images/Illustration.png') }}" alt="Search Icon">
                    <p class="text-black text-base font-semibold">Replace User</p>
                </div>

                <!-- User list with checkboxes -->
                <ul class="mt-4 adddeduser space-y-6 border py-4"x-show="(isFocused && query.length > 1) || firstFocus" @scroll="handleScroll($event)">
                    <template x-for="user in selectedPeoples" :key="user.id">
                        <li class="flex justify-between">
                            <div class="flex items-center gap-2 pl-4">
                                <div class="border col-span-1 rounded-full w-10 h-10 text-white flex justify-center items-center text-center bg-cyan-500"
                                    x-text="`${user.forename.charAt(0).toUpperCase()}${user.surname.charAt(0).toUpperCase()}`">
                                </div>
                                <div class="space-y-1">
                                <span class="text-sm font-semibold block"
                                            x-text="`${user.forename} ${user.surname}`"></span>
                                        <span class="text-xs text-gray-500 block"
                                            x-text="user.latest_role ? (user.employee_id ? `${user.latest_role} (${user.employee_id})` : user.latest_role) : ''"></span>
                                </div>
                            </div>
                            <!-- Radio for selecting users -->
                            <div class="square-radio">
                                <input type="radio" :value="user.id" name="radio-group" x-model="replaceUserInOrganisationChart" :id="'replaceUser-' + user.id">
                                <label :for="'replaceUser-' + user.id"></label>
                            </div>
                        </li>
                    </template>
                </ul>

                <div class="flex justify-end mt-4">
                    <!-- Submit the form when the button is clicked -->
                    <button type="button" class="p-2 rounded-lg" @click="replaceUser()">Replace</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
  function ReplaceUserComponent() {
    return {
        selectedPeoples: [], // User list data
        replaceUserInOrganisationChart: null,
        selectedPeoplesForOrganisation: [],
        query: '',
        loading: false, // Add loading state
        currentPage: 1,
        lastPage: 1,

        // Fetch users initially or on search
        async fetchPaginatedItems(page = 1, query = '') {
            try {
                this.loading = true;
                const response = await fetch(`/paginated-internal-peoples?page=${page}&query=${encodeURIComponent(query)}`);
                const data = await response.json();
                
                let fetchedPeoples = data.data;

                // Remove users in selectedPeoplesForOrganisation from fetchedPeoples
                fetchedPeoples = fetchedPeoples.filter(
                    user => !this.selectedPeoplesForOrganisation.some(orgUser => orgUser.id === user.id)
                );

                if (page === 1) {
                    this.selectedPeoples = fetchedPeoples; // Initialize with first page items
                } else {
                    this.selectedPeoples = [...this.selectedPeoples, ...fetchedPeoples]; // Append next page items
                }

                this.currentPage = data.current_page;
                this.lastPage = data.last_page;

                window.dispatchEvent(new CustomEvent('updateInternalPeoples', { 
                    detail: { user: data.data } 
                }));
            } catch (error) {
                console.error("Error fetching paginated items:", error);
            } finally {
                this.loading = false;
                $("#loading-spinner").addClass('hidden');
            }
        },
        
        search() {
            const lowerCaseQuery = this.query.trim().toLowerCase();
            this.showLoading();
            this.fetchPaginatedItems(1, lowerCaseQuery);
           
        },

        // Handle replace user action
        replaceUser() {
            if (this.replaceUserInOrganisationChart) {
                console.log('Replacing user with ID:', this.replaceUserInOrganisationChart);
                Livewire.dispatch("updateUserInOrganisationChart", {
                    replaceUserInOrganisationChart: this.replaceUserInOrganisationChart
                });
            } else {
                alert('Please select a user to replace.');
            }
        },
        
        handleScroll(event) {
            const ulElement = event.target;
            const isAtBottom = ulElement.scrollTop + ulElement.clientHeight + 50 >= ulElement.scrollHeight;
            
            console.log(`Scrolled to bottom: ${isAtBottom}, Current Page: ${this.currentPage}, Last Page: ${this.lastPage}, Loading: ${this.loading}`);
            
            if (isAtBottom && this.currentPage < this.lastPage && !this.loading) {
                this.showLoading();
                this.fetchPaginatedItems(this.currentPage + 1);
            }
        },

        init() {
            Livewire.on('selectedPeoplesForOrganisation', (data) => {
                console.log("here");
                this.selectedPeoplesForOrganisation = data[0];              
                this.fetchPaginatedItems();
            });
        }
    };
}

</script>
