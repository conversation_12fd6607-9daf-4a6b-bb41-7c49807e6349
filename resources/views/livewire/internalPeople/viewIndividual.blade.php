@if (!empty($individualPeople))
<div x-show="viewIndividualPopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40"
    style="display:none">
    <!-- Modal background with a higher z-index -->
    <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
    <!-- Modal content with a lower z-index -->
    <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50"
        x-show="viewIndividualPopup" x-transition>
        <!-- Modal content -->
        <div class="flex flex-col h-full">
            <div class="flex justify-between border-b px-2 py-2">
                <h3 class="text-base font-bold mb-3">{{ $individualPeople->company_name }}</h3>
                <img @click="viewIndividualPopup = false" class="h-5 w-5 cursor-pointer"
                    src="{{ asset('images/cross.svg') }}" alt="">
            </div>
            <div class="grid grid-cols-2 gap-x-2 pt-3 flex-1">
                <div class="flex flex-1 flex-col justify-between">
                    <div class="flex px-2 py-2 justify-between">
                        <div class="flex flex-col gap-y-2">
                            <h3 class="text-base font-bold">{{ $individualPeople->forename }}
                                {{ $individualPeople->surname }}
                            </h3>
                            <h3 class="text-base text-gray-700">{{ $individualPeople->latest_role }}</h3>
                        </div>
                        <div class="flex gap-x-2 items-center justify-center">
                            <div @click="addSkillsPopup = true"
                                class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                <img class="h-4 w-4 mr-2" src="{{ asset('images/editicon.svg') }}" alt="">
                                <h2 class="text-sm font-semibold">Update</h2>
                            </div>
                            @if ($individualPeople->linkedinURL && $individualPeople->linkedinURL != 'NA')
                            <a id="linkedin" class="flex items-center justify-center hover:scale-105"
                                href="{{ $individualPeople->linkedinURL }}" target="_blank">
                                <img class="h-6 w-6" src="{{ asset('images/linkedinlogo.png') }}">
                            </a>
                            @endif
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 grid-rows-4 flex-grow">
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Function</h3>
                            <h3 class="text-xs text-gray-700">
                                {{ $individualPeople->function ? $individualPeople->function : 'Not Applicable' }}
                            </h3>
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Division</h3>
                            <h3 class="text-xs text-gray-700">
                                {{ $individualPeople->division ? $individualPeople->division : 'Not Applicable' }}
                            </h3>
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Tenure in Role</h3>
                            <h3 class="text-xs text-gray-700">
                                {{ $individualPeople->tenure != 0 ? $individualPeople->tenure : 'Less then year' }}
                            </h3>
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Readiness</h3>
                            <h3 class="text-xs text-gray-700">
                                {{ $individualPeople->readiness ? $individualPeople->readiness : 'Not Ready' }}
                            </h3>
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Tenure in Company</h3>
                            <h3 class="text-xs text-gray-700">
                                {{ $individualPeople->tenure != 0 ? $individualPeople->tenure : 'Less then year' }}
                            </h3>
                        </div>
                        <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                            <h3 class="text-sm font-bold chart-heading">Registrations</h3>
                            <h3 class="text-xs text-gray-700">
                                {{ $individualPeople->other_tags ? $individualPeople->other_tags : 'Not Applicable' }}
                            </h3>
                        </div>

                    </div>

                </div>
                <div x-data="{ tab: 'summary', nopen: @entangle('nopen'), 'nineboxGridOpen': @entangle('nineboxGridOpen') }" class="flex flex-col pt-3 gap-y-2">
                    <div class="flex border-b-2">
                        <button @click="tab = 'summary'" class="w-full text-sm py-3 font-medium"
                            :class="{
                                    'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                        'summary',
                                    'chart-heading font-semibold': tab != 'summary'
                                }">
                            Summary
                        </button>
                        <button @click="tab = 'career_history'" class="w-full text-sm py-3 font-medium"
                            :class="{
                                    'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                        'career_history',
                                    'chart-heading font-semibold': tab != 'career_history'
                                }">
                            Career History
                        </button>
                        <button @click="tab = 'skills'" class="w-full text-sm py-3 font-medium"
                            :class="{
                                    'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                        'skills',
                                    'chart-heading font-semibold': tab != 'skills'
                                }">
                            Skills
                        </button>
                        <button @click="tab = 'notes'" class="w-full text-sm py-3 font-medium"
                            :class="{
                                    'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                        'notes',
                                    'chart-heading font-semibold': tab != 'notes'
                                }">
                            Notes
                        </button>
                        <button x-show="canShowAssessmentButton()" @click="tab = 'assessmentCriteria'" class="w-full text-sm py-3  font-medium"
                            :class="{
                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                            'assessmentCriteria',
                                        'chart-heading font-semibold': tab != 'assessmentCriteria'
                                    }">
                            Assessment Criteria
                        </button>
                        <button x-show="canShowAssessmentButton()" @click="tab = '9boxGrid'" class="w-full text-sm py-3  font-medium"
                            :class="{
                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                            '9boxGrid',
                                        'chart-heading font-semibold': tab != '9boxGrid'
                                    }">
                            9boxGrid
                        </button>

                    </div>

                    <div x-show="tab == 'summary'"
                        class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                        <h4 class="text-lg text-gray-700 font-semibold chart-heading">Summary</h4>
                        <p class="text-sm">
                            {{ $individualPeople->summary ? $individualPeople->summary : 'Not Applicable' }}
                        </p>
                    </div>

                    <div x-show="tab == 'career_history'"
                        class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                        <h4 class="text-lg text-gray-700 font-semibold chart-heading">Career History</h4>
                        @if ($individualPeople->careerHistory->isNotEmpty())
                        <div class="flex flex-col items-start w-full">
                            @foreach ($individualPeople->careerHistory as $careerHistory)
                            <div class="flex h-max items-start justify-center mb-1">
                                <div class="flex flex-col mt-1 items-center justify-center h-full w-2">
                                    <div class="rounded-full  blueBalls bg-mainBlue"></div>
                                    <div class="flex-grow border-l border-mainBlue"></div>
                                </div>
                                <div class="flex flex-col  items-start justify-start pl-4">
                                    <h4 class="text-sm font-semibold text-gray-900">
                                        {{ $careerHistory->role }}
                                    </h4>
                                    <div class="flex gap-x-2">
                                        <span
                                            class="text-sm  font-base text-gray-700">{{ $careerHistory->company->name ?? 'N/A' }}</span>
                                    </div>
                                    <div class="flex gap-x-2 mb-4">
                                        <span
                                            class="text-xs font-base GrayText">{{ \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y') }}
                                            -
                                            {{ !empty($careerHistory->end_date) ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : 'Present' }}</span>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        @else
                        <p class="text-sm text-gray-500 mt-5">No career history available</p>
                        @endif
                    </div>

                    <div x-show="tab == 'skills'"
                        class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                        <h4 class="text-lg text-gray-700 font-semibold chart-heading">Skills</h4>
                        @foreach ($peopleSkills as $skillType => $skillsArr)
                        <h4 class="text-sm text-gray-700 font-medium chart-heading">{{ $skillType }} Skills
                        </h4>
                        <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                            @foreach ($skillsArr as $skill)
                            <div
                                class="flex items-center text-xs p-2 rounded-xl skill-container chart-heading">
                                <span>{{ $skill->skill_name }}</span>
                                @if ($skillType != 'Professional')
                                <button type="button" wire:click="deleteSkill({{ $skill->id }})"
                                    class="ml-1">&times;</button>
                                @endif
                            </div>
                            @endforeach
                        </div>
                        @endforeach
                    </div>

                    @if ($this->isAdminUser && $this->editingOrganisationId)
                    <div x-show="tab == '9boxGrid'" class="h-96  border rounded-xl p-2">
                        <div x-show="!nineboxGridOpen">

                            <div class="flex justify-between items-center">
                                <div class="text-lg font-semibold">9 Box Grid</div>
                                <div @click="nineboxGridOpen = !nineboxGridOpen"
                                    class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                    <img class="h-4 w-4 mr-2" src="/images/editicon.svg" alt="">
                                    <h2 class="text-sm font-semibold">Update</h2>
                                </div>
                            </div>
                            <div class="p-3 relative">

                                <div class="flex gap-5 items-center">
                                    <img class="" src="/images/yAxis.svg" alt="">
                                    <!-- Grid container -->
                                    <div class="relative grid grid-cols-3 gap-3 w-full">
                                        @foreach ($editNineBoxGrid as $key => $editNineBoxGridData)
                                        <div class="{{(!is_null($selectedOption) && $selectedOption == $key) ? "bg-cyan-500" : "orangeBG"}} text-white boxSize  text-center rounded">
                                            {{ $editNineBoxGridData['label'] }}
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                <img class="w-full mt-3" src="/images/xAxis.svg" alt="">


                            </div>
                        </div>

                        <div x-show="nineboxGridOpen">

                            <div class="flex justify-between items-center">
                                <div class="text-lg font-semibold">Update 9 Box Grid</div>
                                <div class="flex gap-3">
                                    <button @click="nineboxGridOpen = false" type="button"
                                        class="flex gap-2 justify-center items-center border p-2 rounded-md text-sm font-bold">
                                        Cancel
                                    </button>
                                    <button wire:click="edit9BoxGrid()" type="button"
                                        class="text-white flex justify-center gap-5 items-center bg-mainBlue p-2 rounded-md">
                                        <span class="block font-semibold text-sm">Save</span>
                                    </button>
                                </div>
                            </div>

                            <div class="p-3 relative" x-data="{ selected: @entangle('selectedOption') }">
                                <div class="flex gap-5 items-center">
                                    <img src="/images/yAxis.svg" alt="">
                                    <!-- Grid container -->
                                    <div class="relative grid grid-cols-3 gap-3 w-full">
                                        <!-- Iterate through editNineBoxGrid -->
                                        @foreach ($editNineBoxGrid as $key => $editNineBoxGridData)
                                        <label
                                            class="relative text-black boxSize text-center rounded cursor-pointer"
                                            :class="{ 'grayLightBorder': selected !== '{{ $key }}', 'afterCheckBorder afterBG': selected === '{{ $key }}' }">
                                            <input type="radio" name="selection" value="{{ $key }}"
                                                class="absolute Blue-radio top-1 right-2"
                                                wire:model="selectedOption"
                                                @click="selected = '{{ $key }}'">
                                            {{ $editNineBoxGridData['label'] }}
                                        </label>
                                        @endforeach
                                    </div>
                                </div>
                                <img class="w-full mt-3" src="/images/xAxis.svg" alt="">
                            </div>


                        </div>
                    </div>
                    <div x-data="{
                        criterias: @entangle('editAssessmentCriterias'), // Bind criterias to Livewire
                        viewNotesPopup: false,
                        selectedNotes: '',
                        fetchNotes(criteriaId) {
                            let criteria = this.criterias.find(c => c.criteria_id === criteriaId);
                            if (criteria) {
                                this.selectedNotes = criteria.notes;
                            }
                            this.viewNotesPopup = true;
                        }
                    }">
                        <div x-show="tab == 'assessmentCriteria'"
                            class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                            <!-- Existing Content -->
                            <div x-show="!nopen">
                                <div class="flex justify-between">
                                    <h4 class="text-sm font-bold chart-heading">Assessment Criteria</h4>
                                    <button @click="nopen = !nopen" type="button"
                                        class="flex gap-2 justify-center items-center border p-2 rounded-md text-sm font-bold">
                                        <img class="h-4 w-4" src="{{ asset('images/editicon.svg') }}">
                                        Edit
                                    </button>
                                </div>

                                @if (!empty($editAssessmentCriterias))
                                <div class="creteriaList">
                                    @foreach ($editAssessmentCriterias as $assessmentCriteria)
                                    <div class="mt-3">
                                        <div class="flex justify-between padding20 border-b">
                                            <h3 class="text-lg md:text-base font-medium text-black">
                                                {{ $assessmentCriteria['label'] }}
                                            </h3>
                                            <div class="flex items-center">
                                                <!-- Yes/No Badge -->
                                                @if ($assessmentCriteria['isChecked'] == 'Yes')
                                                <span
                                                    class="text-xs py-2 px-3 rounded-lg font-normal redlinessReaddy ml-1">
                                                    Yes
                                                </span>
                                                @else
                                                <span
                                                    class="text-xs py-2 px-3 rounded-lg font-normal ml-1 RedBG text-red-500">
                                                    No
                                                </span>
                                                @endif

                                                <!-- View Notes Link -->
                                                <div @click="fetchNotes({{ $assessmentCriteria['criteria_id'] }})"
                                                    class="flex justify-between cursor-pointer ml-4">
                                                    <span class="text-sm text-cyan-500 font-bold">View
                                                        Notes</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                                @else
                                <div class="mt-2">
                                    <p class="text-sm">No Assessment Criteria Available</p>
                                </div>
                                @endif
                            </div>

                            <!-- New Content for Edit Mode -->

                            <div x-show="nopen">
                                <form wire:submit.prevent="editIndividualCriteria">

                                    <div class="flex justify-between items-center">
                                        <h4 class="text-sm font-bold chart-heading">Edit Assessment Criteria
                                        </h4>
                                        <div class="flex gap-3">
                                            <button @click="nopen = false" type="button"
                                                class="flex gap-2 justify-center items-center border p-2 rounded-md text-sm font-bold">
                                                Cancel
                                            </button>
                                            <button wire:click="editIndividualCriteria()" type="button"
                                                class="text-white flex justify-center gap-5 items-center bg-mainBlue p-2 rounded-md">
                                                <span class="block font-semibold text-sm">Save</span>
                                            </button>
                                        </div>
                                    </div>

                                    @if (!empty($editAssessmentCriterias))
                                    <div class="editAssessment">
                                        @foreach ($editAssessmentCriterias as $index => $assessmentCriteria)
                                        <div class="mt-3 innerEditAssessment">
                                            <h3 class="text-lg md:text-base font-medium text-black mb-2">
                                                {{ $assessmentCriteria['label'] }}
                                            </h3>
                                            <div class="flex justify-between padding20 gap-3 border-b">
                                                <div class="switch-toggle">
                                                    <input type="checkbox"
                                                        wire:model="editAssessmentCriterias.{{ $index }}.isChecked"
                                                        :checked="{{ $assessmentCriteria['isChecked'] === 'Yes' ? 'true' : 'false' }}"
                                                        @change="editAssessmentCriterias[{{ $index }}].isChecked = $event.target.checked ? 'true' : 'false'"
                                                        class="switch-toggle-checkbox" />
                                                    <label class="switch-toggle-label">
                                                        <span>No</span>
                                                        <span>Yes</span>
                                                    </label>
                                                </div>
                                                <textarea wire:model="editAssessmentCriterias.{{ $index }}.notes" placeholder="Enter Notes"
                                                    class="placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md resize-y"
                                                    rows="1"></textarea>
                                            </div>
                                        </div>
                                        @endforeach

                                    </div>


                                    @endif
                                </form>

                            </div>
                        </div>
                        @include('livewire.internalPeople.viewNotesPopup')

                    </div>
                    @endif






                    <div x-show="tab == 'notes'"
                        class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                        <div class="flex justify-between">
                            <h4 class="text-lg text-gray-700 font-semibold chart-heading pt-1">Notes</h4>
                            <button @click="nopen = !nopen" type="button"
                                class="flex justify-center items-center border p-2 rounded-md text-sm">
                                <img class="h-4 w-4" src="{{ asset('images/Plus.svg') }}">
                                <span class="block text-black pl-1" x-text="nopen ? 'Cancel' : 'Add Notes'"></span>
                            </button>
                        </div>

                        <div class="mt-2" x-show="nopen" x-transition>
                            <textarea wire:model="filteredPnotes" rows="4"
                                class="bg-gray-50 p-2 text-sm font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500"
                                placeholder="Enter your note here"></textarea>
                            <div class="flex gap-2 mt-2">
                                {{-- <button type="button" @click="nopen = false"
                                        class="flex justify-center items-center border p-2 rounded-md text-sm">
                                        <span class="block text-black pl-1">Cancel</span>
                                    </button> --}}
                                <button wire:click.prevent="addNotes({{ $individualPeople->id }})"
                                    @click="nopen = false" type="button"
                                    class="flex justify-center items-center border p-2 rounded-md text-sm bg-mainBlue text-white">
                                    <span class="block pl-1">Save</span>
                                </button>
                            </div>
                        </div>

                        @if ($successPersonNotes && $successPersonNotes->isNotEmpty())
                        @foreach ($successPersonNotes as $note)
                        <div class="mt-3">
                            <div class="flex justify-between">
                                <h3 class="text-xl md:text-base font-medium text-black">
                                    {{ $note->user_name }}
                                </h3>
                                <div class="flex justify-between">
                                    <span
                                        class="text-sm text-gray-500 font-normal">{{ \Carbon\Carbon::parse($note->created_at)->format('m/d/y') }}</span>
                                </div>
                            </div>
                            <p class="text-sm text-gray-600 font-normal">{{ $note->Notes }}</p>
                        </div>
                        @endforeach
                        @else
                        <div class="mt-2" x-show="!nopen">
                            <p class="text-sm">No notes available</p>
                        </div>
                        @endif
                    </div>





                </div>
            </div>
        </div>
    </div>
</div>
@endif


<!-- resources/views/livewire/add-skills.blade.php -->
<div x-show="addSkillsPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
        <form wire:submit.prevent="addSkills">
            <div class="flex justify-between">
                <h2 class="text-black-900 text-xl font-semibold">Add Skills</h2>
                <button type="button" @click="addSkillsPopup = false" class="text-gray-500 hover:text-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="w-full border-t mt-3 border-gray-200"></div>

            <div class="h-full" wire:key="123">
                <div class="h-5/6 flex items-center">
                    <div class="w-full">

                        <div class="relative py-2 bg-white">
                            <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                            </div>
                        </div>


                        <div class="" x-data="{
                            newSpecialised: '',
                            newCommon: '',
                            newCertification: ''
                        }">
                            <div>
                                <label for="Specialised" class="text-xs font-medium labelcolor">Specialised</label>
                                <input type="text"
                                    class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                    placeholder="Enter specialised skills" x-model="newSpecialised"
                                    @keydown.enter.prevent="if(newSpecialised !== '') { @this.newSkillData.specialised.push(newSpecialised); newSpecialised = ''; }"
                                    @blur="if(newSpecialised !== '') { @this.newSkillData.specialised.push(newSpecialised); newSpecialised = ''; }">

                                <div class="mt-2">
                                    <template x-for="(skill, index) in @this.newSkillData.specialised"
                                        :key="index">
                                        <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2">
                                            <span x-text="skill"></span>
                                            <button type="button" class="ml-1"
                                                @click="@this.newSkillData.specialised.splice(index, 1)">&times;</button>
                                        </span>
                                    </template>
                                </div>
                            </div>


                            <div class="mt-4">
                                <label for="Common" class="text-xs font-medium labelcolor">Common</label>
                                <input type="text"
                                    class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                    placeholder="Enter common skills" x-model="newCommon"
                                    @keydown.enter.prevent="if(newCommon !== '') { @this.newSkillData.common.push(newCommon); newCommon = ''; }"
                                    @blur="if(newCommon !== '') { @this.newSkillData.common.push(newCommon); newCommon = ''; }">

                                <div class="mt-2">
                                    <template x-for="(skill, index) in @this.newSkillData.common"
                                        :key="index">
                                        <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2">
                                            <span x-text="skill"></span>
                                            <button type="button" class="ml-1"
                                                @click="@this.newSkillData.common.splice(index, 1)">&times;</button>
                                        </span>
                                    </template>
                                </div>
                            </div>


                            <div class="mt-4">
                                <label for="Certification"
                                    class="text-xs font-medium labelcolor">Certification</label>
                                <input type="text"
                                    class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                    placeholder="Enter certifications" x-model="newCertification"
                                    @keydown.enter.prevent="if(newCertification !== '') { @this.newSkillData.certification.push(newCertification); newCertification = ''; }"
                                    @blur="if(newCertification !== '') { @this.newSkillData.certification.push(newCertification); newCertification = ''; }">

                                <div class="mt-2">
                                    <template x-for="(cert, index) in @this.newSkillData.certification"
                                        :key="index">
                                        <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2">
                                            <span x-text="cert"></span>
                                            <button type="button" class="ml-1"
                                                @click="@this.newSkillData.certification.splice(index, 1)">&times;</button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                        </div>

                        <div class="mt-2">
                            <fieldset>
                                <legend class="text-xs font-medium labelcolor">Readiness</legend>
                                <ul class="donate-now mt-1">
                                    <li>
                                        <input type="radio" id="Male" name="readiness" value="Ready"
                                            wire:model="readiness" />
                                        <label for="Male"
                                            class="text-center font-semibold labelcolor">Ready</label>
                                    </li>
                                    <li>
                                        <input type="radio" id="Female" name="readiness" value="Not Ready"
                                            wire:model="readiness" />
                                        <label for="Female" class="text-center font-semibold labelcolor">Not
                                            Ready</label>
                                    </li>
                                    <li>
                                        <input type="radio" id="Not Applicable" name="readiness"
                                            value="Not Applicable" wire:model="readiness" />
                                        <label for="Not Applicable" class="text-center font-semibold labelcolor">Not
                                            Applicable</label>
                                    </li>
                                </ul>
                                @error('readiness')
                                <span class="text-red-500 text-xs">{{ $message }}</span>
                                @enderror
                            </fieldset>
                        </div>
                        <div class="mt-4">
                            <label for="updateSummary" class="text-xs font-medium labelcolor">Summary</label>
                            <textarea wire:model="updateSummary" id="updateSummary"
                                class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                placeholder="Enter summary"></textarea>
                        </div>
                        <div class="mt-4">
                            <button id="addHistory" type="button" x-on:click=" addCareerHistroyPopup =true"
                                class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                placeholder="Enter summary">Add Career History</button>
                        </div>
                        <!-- border above buttons -->
                        <div class="w-full border-t mt-4 border-gray-200"></div>

                        <!-- buttons wrapper -->
                        <div class="flex gap-2 w-full px-4 mt-4 ">
                            <button x-on:click="addSkillsPopup = false" type="button"
                                class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                            <button type="submit"
                                class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">Add</button>

                        </div>

                    </div>
                </div>
            </div>

        </form>
    </div>
</div>


<div x-show="addCareerHistroyPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
        <form wire:submit.prevent="addCareerHistory">
            <div class="flex justify-between">
                <h2 class="text-black-900 text-xl font-semibold">Add Career History</h2>
                <button type="button" @click="addCareerHistroyPopup = false"
                    class="text-gray-500 hover:text-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="w-full border-t mt-3 border-gray-200"></div>

            <div class="h-full" wire:key="123">
                <div class="h-5/6 flex items-center">
                    <div class="w-full">

                        <div class="relative py-2 bg-white">
                            <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                            </div>
                        </div>

                        <div class="mt-4">
                            <label for="pastRole" class="text-xs font-medium labelcolor">Past Role <span
                                    class="text-red-500" style="font-size: 1rem;">*</span></label>
                            <input wire:model="pastRole" id="pastRole"
                                class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                placeholder="Enter past role">
                            @error('pastRole')
                            <span class="text-red-500 text-md text-center">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mt-4">
                            <label for="start_date" class="text-xs font-medium labelcolor">Start Date <span
                                    class="text-red-500" style="font-size: 1rem;">*</span></label>
                            <input wire:model="past_start_date" id="start_date" type="date"
                                class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500">
                            @error('past_start_date')
                            <span class="text-red-500 text-md text-center">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mt-4">
                            <label for="end_date" class="text-xs font-medium labelcolor"> End Date</label>
                            <input wire:model="past_end_date" id="end_date" type="date"
                                class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500">
                            @error('past_end_date')
                            <span class="text-red-500 text-md text-center">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="mt-4">
                            <label for="past_company" class="text-xs font-medium labelcolor">Past Company <span
                                    class="text-red-500" style="font-size: 1rem;">*</span></label>
                            <div x-data="{
                                recommendations: {{ json_encode($pastCompanies) }},
                                searchQuery: '', // For displaying the selected company's name
                                selectedCompanyId: null, // For storing the selected company's ID
                                filteredRecommendations: [],
                                selectRecommendation(id, label) {
                                    this.selectedCompanyId = id;
                                    this.searchQuery = label;
                                    // Manually trigger input event for Livewire update
                                    const hiddenInput = document.getElementById('selectedCompanyId');
                                    hiddenInput.value = id;
                                    hiddenInput.dispatchEvent(new Event('input'));
                                    this.filteredRecommendations = []; // Clear recommendations after selection
                                },
                                search() {
                                    const query = String(this.searchQuery ?? '').toLowerCase();
                                    if (query === '') {
                                        this.filteredRecommendations = [];
                                    } else {
                                        this.filteredRecommendations = this.recommendations.filter(recommendation => {
                                            return recommendation.label.toLowerCase().includes(query);
                                        }).slice(0, 10);
                                    }
                                },
                                initialize() {
                                    this.searchQuery = @js($pastCompany);
                                    this.search();
                                },
                                updateHiddenInput() {
                                    const hiddenInput = document.getElementById('selectedCompanyId');
                                    hiddenInput.value = this.searchQuery;
                                    hiddenInput.dispatchEvent(new Event('input'));
                                }
                            }" x-init="initialize">
                                <!-- Hidden input to hold the selected company ID or entered value -->
                                <input type="hidden" id="selectedCompanyId" wire:model="pastCompany"
                                    x-model="selectedCompanyId">

                                <!-- Visible input to display the company name -->
                                <input type="text" id="pastCompany" x-model="searchQuery" @input="search"
                                    @blur="updateHiddenInput"
                                    class="mt-1 placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md">

                                <div class="flex flex-col gap-y-2 mt-2" x-show="filteredRecommendations.length > 0">
                                    <template x-for="recommendation in filteredRecommendations"
                                        :key="recommendation.value">
                                        <div class="flex items-center gap-x-2">
                                            <button class="text-sm font-semibold text-cyan-500"
                                                @click.prevent="selectRecommendation(recommendation.value, recommendation.label); $dispatch('close-suggestions')">
                                                Use Recommendation
                                            </button>
                                            <p class="text-sm text-gray-900 font-light" x-text="recommendation.label">
                                            </p>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            @error('pastCompany')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="w-full border-t mt-4 border-gray-200"></div>
                        <!-- buttons wrapper -->
                        <div class="flex gap-2 w-full px-4 mt-4 ">
                            <button x-on:click="addCareerHistroyPopup = false" type="button"
                                class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                            <button type="submit"
                                class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">Add</button>
                        </div>
                    </div>
                </div>
            </div>

        </form>
    </div>
</div>