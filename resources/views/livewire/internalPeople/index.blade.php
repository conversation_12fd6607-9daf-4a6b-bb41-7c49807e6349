<div x-data="{
    isVisible: @entangle('isVisible'),
    isFirstClick: true,
    individualPopup: @entangle('individualPopup'),
    showSaveButton: false,
    showShareButton: false,
    clickToBeginPopup: false,
    addOrganisationButton: true,
    showOranisationListing: @entangle('showOranisationListing'),
    deleteOrganisationPeoplePopup: @entangle('deleteOrganisationPeoplePopup'),
    userId: null,
    addDirectReportPopup: @entangle('addDirectReportPopup'),
    replaceUserOrgPopup: @entangle('replaceUserOrgPopup'),
    addReportingManagerPopup: @entangle('addReportingManagerPopup'),
    viewReportingManagerPopup: false,
    addSkillsPopup: @entangle('addSkillsPopup'),
    addCareerHistroyPopup: @entangle('addCareerHistroyPopup'),
    shareOrgansationPopup: @entangle('shareOrgansationPopup'),
    assessmentCriteriaPopup: @entangle('assessmentCriteriaPopup'),
    viewNotesPopup: @entangle('viewNotesPopup'),
    nineboxGridPopup: @entangle('nineboxGridPopup')
}">

    @include('livewire.flashMessage')


    <!-- The container for all the content-->
    <div class="relative bg-Color">
        <!-- The second container -->
        <div class="bg-white col-span-1 shadow-sm border-b border-gray-200 p-3" x-data="{ fopen: false }"
            x-init="fopen = false">

            @include('livewire.internalPeople.contentTitle')

        </div>

        @include('livewire.internalPeople.organization')


        <div x-show="deleteOrganisationPeoplePopup" x-cloak class="fixed inset-0 flex items-center justify-center">
            <div class="fixed inset-0 bg-slate-400 opacity-50 backdrop-blur-sm z-40"></div>
            <div
                class="bg-white relative  deleteOrg border-2 p-4 rounded-lg flex flex-col justify-center items-center z-50">
                <img @click="deleteOrganisationPeoplePopup = false"
                    class="absolute right-2 crossTop w-auto cursor-pointer" src="{{ asset('images/cross.svg') }}"
                    alt="Close Icon">
                <img class="" src="{{ asset('images/redTrashIcon.svg') }}" alt="Trash Icon">
                <h3 class="font-semibold text-base mt-3">Delete People</h3>
                <p class="text-xs text-center text-gray-600 py-3">Are you sure you want to delete the People?
                    <br />
                    This action cannot be undone.
                </p>
                <div class="flex gap-2 w-full px-4 mt-4">
                    <button @click="deleteOrganisationPeoplePopup = false" type="button"
                        class="bg-white font-semibold w-full text-black border p-2 rounded-md">Cancel</button>
                    <button wire:click="deletePeopleFromOrganization(userId)" type="button"
                        class="text-red-500 font-semibold w-full bg-white border p-2 rounded-md">
                        <span class="block">Delete</span>
                    </button>
                </div>
            </div>
        </div>


        <div x-show="showOranisationListing" class="grid grid-cols-3 p-4 gap-4 {{ count($organisationArr) > 6 ? 'contentHeight2' : '' }}">
            @foreach ($organisationArr as $organisation)
            <div class="bg-white containerHeight rounded-lg">
                <div class="p-3">
                    <div class="flex justify-between">
                        <div class="text-sm">
                            <span class="font-semibold text-black">{{ $organisation['organisation_title'] }}</span>
                            <span class="font-normal">Organization Chart</span>
                        </div>

                        @if($organisation['created_by'] == auth()->user()->id)
                        <div class="dropdown-container relative">
                            <button tabindex="1" id="dropdownDefaultButton" data-dropdown-toggle="dropdown"
                                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                type="button">
                                <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                            </button>

                            <div id="dropdown"
                                class="dropdown-menu hidden z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200"
                                    aria-labelledby="dropdownDefaultButton">
                                    <li class="cursor-pointer">
                                        <a class="p-0">
                                            <div @click="confirmDelete({{ $organisation['organisation_id'] }}, `Delete {{ $organisation['organisation_title'] }}`, `Are you sure want to delete the organisation <b>{{ $organisation['organisation_title'] }}</b>?`, '{{ asset('images/redTrashIcon.svg') }}', 'deleteOrganisation')"
                                                class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                <img class="h-5 w-5" src="{{ asset('images/deleteIcon.svg') }}">
                                                <span class="font-semibold text-sm">Delete</span>
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        @endif

                    </div>
                    <div class="flex justify-between mt-3">
                        <div class="flex gap-2">
                            <img class="h-5 w-5 cursor-pointer" src="{{ asset('images/UsersThree.svg') }}">
                            <span class="font-normal text-sm block">Number of People</span>
                        </div>
                        <span class="block text-sm">{{ $organisation['organisation_people'] }}</span>
                    </div>
                    <div class="flex justify-between mt-3">
                        <div class="flex gap-2 items-center">
                            <span class="font-normal text-sm block">Female-Ratio</span>
                            <progress id="female-ratio" value="{{ $organisation['female_ratio'] }}" max="100"
                                class="progress-bar progressColor1"></progress>
                        </div>
                        <span class="block text-sm">{{ $organisation['female_ratio'] }}%</span>
                    </div>
                    <div class="flex justify-between mt-2">
                        <div class="flex gap-6 items-center">
                            <span class="font-normal text-sm block">Male-Ratio</span>
                            <progress id="male-ratio" value="{{ $organisation['male_ratio'] }}" max="100"
                                class="progress-bar progressColor2"></progress>
                        </div>
                        <span class="block text-sm">{{ $organisation['male_ratio'] }}%</span>
                    </div>
                    <button wire:click="viewOrganisation({{ $organisation['organisation_id'] }})"
                        onclick="viewOrganisationClicked()"
                        class="bg-white flex items-center space-x-1 mt-2 border border-gray-300 rounded-lg px-2 py-2 hover:bg-gray-200 font-medium text-black text-md justify-center w-full">
                        <h1 class="text-md font-semibold">View</h1>
                    </button>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    <div x-show="showOranisationListing" class="px-5 mt-2">
        {{ $organisationsObj->links() }}
    </div>
    @include('livewire.loading')

</div>

<script>
    function viewOrganisationClicked() {
        document.getElementById("addOrganisationButton").click();
        document.getElementById("clickToBegin").click();

    }

    function confirmDelete(id, title, message, iconUrl, dispatchName) {
        Swal.fire({
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">${title}</h2>
                    <p class="px-5 font-normal">${message}</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Delete",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            if (result.isConfirmed) {
                Livewire.dispatch(dispatchName, {
                    id: id
                });
            }
        });
    }

    function deleteOrganisationPeople(primaryKey, parentId, childId, title, message, iconUrl, dispatchName) {
        Swal.fire({
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">${title}</h2>
                <p class="px-5 font-normal">${message}</p>
                <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showCancelButton: true,
            confirmButtonText: "Delete",
            cancelButtonText: "Cancel",
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md',
                cancelButton: 'bg-white btnsWidth btnsmargin text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md'
            },
            footer: `
            <div class="flex justify-center gap-4">
                <button id="vacantButton" class="swal2-styled bg-white btnsWidth btnsmargin text-yellow-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md">Vacant</button>
                <button id="replaceButton" class="swal2-styled bg-white btnsWidth btnsmargin text-blue-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md">Replace</button>
            </div>
            <div class="flex justify-center gap-4">
                <button id="permanentButton" class="swal2-styled bg-white btnsWidth btnsmargin text-yellow-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md">Delete From Platform</button>
            </div>
            `,
            showCloseButton: true
        }).then((result) => {
            if (result.isConfirmed) {
                Livewire.dispatch(dispatchName, {
                    primaryKey: primaryKey,
                    parentId: parentId,
                    childId: childId,
                    action: 'delete'
                });
            }
        });

        document.getElementById('vacantButton').addEventListener('click', function() {
            Swal.close();
            Livewire.dispatch(dispatchName, {
                primaryKey: primaryKey,
                parentId: parentId,
                childId: childId,
                action: 'vacant'
            });
        });

        document.getElementById('replaceButton').addEventListener('click', function() {
            console.log(dispatchName);
            Swal.close();
            Livewire.dispatch(dispatchName, {
                primaryKey: primaryKey,
                parentId: parentId,
                childId: childId,
                action: 'replace'
            });
        });
        document.getElementById('permanentButton').addEventListener('click', function() {
            Swal.close();
            Livewire.dispatch(dispatchName, {
                primaryKey: primaryKey,
                parentId: parentId,
                childId: childId,
                action: 'permanent'
            });
        });
    }


    document.addEventListener("DOMContentLoaded", function() {
        var hasOrgansationCreationStarted = {};
        var isCreatingOrganisation = true;
        Livewire.on('handleRedirection', (data) => {
            hasOrgansationCreationStarted = data[0];
            isCreatingOrganisation = data[1];

        });
        setInterval(() => {
            let ulElements = document.querySelectorAll('.tree ul');

            // Loop through each <ul> element
            ulElements.forEach(function(ul) {
                // Check if the <ul> has any <li> children
                if (ul.children.length === 0) {
                    // If the <ul> is empty, remove it
                    ul.remove();
                }
            });
        }, 1000);

        let menuItems = document.querySelectorAll('.side-menu');

        // Loop through each menu item and add click event listener
        menuItems.forEach(function(item) {
            item.addEventListener('click', function(event) {
                event.preventDefault();
                let data = hasOrgansationCreationStarted;
                if ((typeof data === 'object' && data !== null && Object.keys(data).length > 0) && isCreatingOrganisation) {
                    Swal.fire({
                        html: `
                    <div class="px-5 w-full flex justify-center mb-3">
                    </div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">
                        Save organisation
                    </h2>
                    <p class="px-5 font-normal">Please save organisation first</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>
                `,
                        confirmButtonText: "OK",
                        buttonsStyling: false,
                        customClass: {
                            confirmButton: 'bg-white btnsWidth text-blue-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md',
                        },
                        showCloseButton: true
                    });
                    return;
                }

                window.location.href = this.href;

            });
        });
    });
    function redirectToPlan(id) {
        // Construct the URL using the route name and ID
        var url = "{{ route('plan.show', ':plan') }}";
        url = url.replace(':plan', id);

        // Redirect to the constructed URL
        window.location.href = url;
    }

    function checkIfOrganisationSaved(id, isCreatingOrganisation) {
        if(isCreatingOrganisation == '') {
            Swal.fire({
                    html: `
                <div class="px-5 w-full flex justify-center mb-3">
                </div>
                <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">
                    Save organisation
                </h2>
                <p class="px-5 font-normal">Please save organisation first</p>
                <div class="w-full border-t mt-5 border-gray-200"></div>
            `,
                    confirmButtonText: "OK",
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'bg-white btnsWidth text-blue-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md',
                    },
                    showCloseButton: true
                });
                return;

        }
        redirectToPlan(id);

    }
</script>

<script>
    document.addEventListener('livewire:load', function() {

        Livewire.hook('message.sent', (message, component) => {
            // Check if the file input was updated
            if (message.updateQueue.some(update => update.payload.name === 'file')) {
                // Fire your custom JavaScript event here
                console.log('File selected:', component.file);

                // Example: Triggering a custom event
                const fileInput = document.getElementById('fileInput');
                fileInput.dispatchEvent(new Event('file-selected'));
            }
        });
    });
</script>
<script>
    document.getElementById('file-upload').addEventListener('change', function() {
        // Show the loading spinner
        document.getElementById('loading-spinner').classList.remove('hidden');

    });
</script>