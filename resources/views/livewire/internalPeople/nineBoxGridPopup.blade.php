<div x-show="nineboxGridPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50 ">
        <div class="flex justify-between items-center p-4">
            <h2 class="text-sm font-bold">Add 9 Box Grid</h2>
            <button type="button" @click="nineboxGridPopup = false" class="text-gray-500 hover:text-gray-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Border above buttons -->
        <div class="w-full border-t border-gray-200"></div>

        <div class="p-4">
            <label class="chart-heading text-sm font-medium">Grid Labels</label>
              <!-- Display only the first error message -->
              @if($errors->any())
                <div class="text-red-500 text-sm mb-3">
                    {{ $errors->first('gridLabels.*') }}
                </div>
            @endif
            <div class="grid grid-cols-3 gap-3 mt-2">
                @foreach($gridLabels as $index => $label)
                    <div class="gridBorder px-3 py-4 rounded-lg">
                        <textarea wire:model="gridLabels.{{ $index }}" class="custom-textarea w-full outline-none text-center break-words" placeholder="Enter Label" rows="2"></textarea>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Buttons for second step -->
        <div class="flex gap-2 w-full p-4 text-sm border-t">
            <button @click="nineboxGridPopup = false" type="button"
                class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                <span class="block font-medium">Cancel</span>
            </button>
            <button wire:click="saveGridLabels" type="button"
                class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">
                <span class="block font-semibold">Save Changes</span>
            </button>
        </div>
    </div>
</div>
