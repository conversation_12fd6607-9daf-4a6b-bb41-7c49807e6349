<!-- Modal -->
<div x-show="addReportingManagerPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
        <form wire:submit.prevent="addDirectReportForm">

            <div class="flex justify-between">
                <h2 class="text-black-900 text-xl font-semibold">Add Reporting Manager</h2>
                <button type="button" @click="addReportingManagerPopup = false" class="text-gray-500 hover:text-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- select people box -->
            <div class="flex justify-center items-center mt-2" x-data="{ query: '', isFocused: false, firstFocus: false }">
                <div class="bg-white border selectPeople p-2 rounded-lg">
                    <div x-show="!(isFocused && query.length > 1)">
                    </div>
                    <div class="search-container flex items-center mt-2">
                        <img class="search-icon h-4 w-auto" src="{{ asset('images/MagnifyingGlass.svg') }}"
                            alt="Search Icon">
                        <input wire:model.live.debounce.500ms="searchAddDirectReportPeoples" type="text"
                            x-model="query" x-on:focus="isFocused = true; firstFocus = true;"
                            x-on:blur="isFocused = false"
                            class="w-full bordermainBlue outline-none block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-500 sm:text-md sm:leading-6"
                            placeholder="Search">
                    </div>
                    <div class="searchBox flex justify-center flex-col items-center" x-show="!firstFocus">
                        <img class="" src="{{ asset('images/Illustration.png') }}" alt="Search Icon">
                        <p class="text-black text-base font-semibold">Start entering
                            Reporting Managers
                        </p>
                    </div>
                    <!-- User list with radio buttons -->
                    <ul class="mt-4 adddeduser space-y-6 border py-4"
                        x-show="(isFocused && query.length > 1) || firstFocus">
                        @foreach ($reportingManagerOptionsObject as $addDirectReportPeople)
                            <li class="flex justify-between items-center">
                                <div class="flex items-center gap-2 pl-4">
                                    <div
                                        class="border rounded-full w-10 h-10 text-white flex justify-center items-center text-center bg-cyan-500">
                                        {{ strtoupper(substr($addDirectReportPeople->forename, 0, 1)) . strtoupper(substr($addDirectReportPeople->surname, 0, 1)) }}
                                    </div>
                                    <div class="space-y-1">
                                        <span
                                            class="text-sm font-semibold block">{{ $addDirectReportPeople->forename . ' ' . $addDirectReportPeople->surname }}</span>
                                        <span
                                            class="text-xs text-gray-500 block">{{ $addDirectReportPeople->latest_role ?? '' }}
                                            @if (!empty($addDirectReportPeople->employee_id))
                                                ({{ $addDirectReportPeople->employee_id }})
                                            @endif
                                        </span>
                                    </div>
                                </div>
                                <!-- Single selection using radio buttons -->
                                {{-- <input type="radio" 
                                    wire:model="addReportingManagerField" 
                                    value="{{ $addDirectReportPeople->id }}"
                                    id="addReportingManagerField-{{ $addDirectReportPeople->id }}"
                                    class="form-radio h-4 w-4 mr-4 radio transition duration-150 ease-in-out"> --}}
                                <div class="square-radio">
                                    <input type="radio" wire:model="addReportingManagerField"
                                        value="{{ $addDirectReportPeople->id }}"
                                        id="addReportingManagerField-{{ $addDirectReportPeople->id }}"
                                        name="radio-group">
                                    <label for="addReportingManagerField-{{ $addDirectReportPeople->id }}"></label>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                    <div class="flex justify-end mt-4">
                        <!-- Submit the form when the button is clicked -->
                        <button type="button" class="p-2 rounded-lg" wire:click="addReportingManager">Add</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
