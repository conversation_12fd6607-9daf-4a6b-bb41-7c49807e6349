<div x-data="{
    isFocused: false,
    firstFocus: false,
    query: '',
    shareWithUsers: {{ json_encode($shareWithUsers) }},
    selectedshareOrgansationPeoples: {},
    
    filteredUsers() {
        if (!this.query) {
            return this.shareWithUsers;
        }

        const query = this.query.toLowerCase();

        return this.shareWithUsers.filter(user => {
            const fullName = user.label.toLowerCase();
            return fullName.includes(query);
        });
    },

    shareOrganisation() {
    
        Livewire.dispatch('shareOrganisation', {
            data: this.selectedshareOrgansationPeoples
        });
    },
    init() {
        Livewire.on('shareOrganisationData', (data) => {
            this.selectedshareOrgansationPeoples = {};
            data[0].forEach(id => {
                this.selectedshareOrgansationPeoples[id] = true;
            });
        });
    }
}">
    <div x-show="shareOrgansationPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
        <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">

            <div class="flex justify-between">
                <h2 class="text-black-900 text-xl font-semibold">Share organisation</h2>
                <button type="button" @click="shareOrgansationPopup = false" class="text-gray-500 hover:text-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- select people box -->
            <div class="flex justify-center items-center mt-2">
                <div class="bg-white border selectPeople p-2 rounded-lg">
                    <div x-show="!(isFocused && query.length > 1)"></div>

                    <div class="search-container flex items-center mt-2">
                        <img class="search-icon h-4 w-auto" src="{{ asset('images/MagnifyingGlass.svg') }}" alt="Search Icon">
                        <input x-on:focus="isFocused = true; firstFocus = true;" x-on:blur="isFocused = false"
                            type="text" x-model="query"
                            class="w-full bordermainBlue outline-none block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-500 sm:text-md sm:leading-6"
                            placeholder="Search">
                    </div>

                    <div class="searchBox flex justify-center flex-col items-center" x-show="!firstFocus">
                        <img class="" src="{{ asset('images/Illustration.png') }}" alt="Search Icon">
                        <p class="text-black text-base font-semibold">Start entering
                            share peoples
                        </p>
                    </div>

                    <!-- User list with checkboxes -->
                    <ul class="mt-4 adddeduser space-y-6 border py-4" x-show="(isFocused && query.length > 1) || firstFocus">
                        <template x-for="user in filteredUsers()" :key="user.value">
                            <li class="flex justify-between">
                                <div class="flex items-center gap-2 pl-4">
                                    <div class="border rounded-full w-10 h-10 text-white flex justify-center items-center text-center bg-cyan-500">
                                        <span x-text="user.label.charAt(0).toUpperCase()"></span>
                                    </div>
                                    <div class="space-y-1">
                                        <span class="text-sm font-semibold block" x-text="`${user.label}`"></span>
                                    </div>
                                </div>
                                <input type="checkbox" x-model="selectedshareOrgansationPeoples[user.value]"
                                    :id="'shareOrgansationPeople-' + user.value"
                                    class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out">
                            </li>
                        </template>
                    </ul>

                    <div class="flex justify-end mt-4">
                        <button type="button" class="p-2 rounded-lg" @click="shareOrganisation">Share</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>