<?php
$randomNumber = rand(10, 10000);
$plan =$organisationPeople->plan;
?>
<div wire:key="{{ $randomNumber }}" class="w-56 HRInfo  rounded-lg shadow-xl bg-white relative {{ $margin ?? '' }}">

    <div class="flex gap-10 p-1 py-2 items-center borderTop">
        <!-- Display parent information -->
        <div class="flex gap-2 items-center ml-3">
            @if (!$plan)
                <img class="block cursor-pointer" src="{{ asset('images/redCrossIcon.svg') }}" alt="Delete Person">
            @else
                @if ($organistaionDetailObj)
                    @if($organistaionDetailObj->created_by == auth()->user()->id)
                        <img class="block cursor-pointer w-7 h-7" src="{{ asset('images/greenTick.svg') }}" alt="Plan Added">
                    @else
                        @if(in_array( auth()->user()->id, json_decode($plan->shared_with)))
                            <img class="block cursor-pointer w-7 h-7" src="{{ asset('images/greenTick.svg') }}" alt="Plan Added">
                        @else 
                            <img class="block cursor-pointer" src="{{ asset('images/redCrossIcon.svg') }}" alt="Delete Person">
                        @endif
                    @endif
                @else
                    <img class="block cursor-pointer w-7 h-7" src="{{ asset('images/greenTick.svg') }}" alt="Plan Added">
                @endif

            @endif

            <h2 class="font-semibold text-sm overflow-hidden whitespace-nowrap w-32 text-ellipsis"
                title="{{ $organisationPeople->latest_role ?? '' }}">
                {{ $organisationPeople->latest_role ?? '' }}</h2>
        </div>
        <!-- Dropdown menu for parent actions -->
        <div class="dropdown-container">
            <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown"
                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                type="button">
                <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
            </button>
            <!-- Dropdown menu options for parent -->
            <div id="dropdown2"
                class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                    @if ($plan)
                        <div class="cursor-pointer li">
                            <div @click="checkIfOrganisationSaved({{ $plan->id }}, '{{$editingOrganisationId}}')"
                                class="flex gap-4 px-5 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                               <span class="font-semibold text-sm">View Plan</span>
                            </div>
                        </div>
                    @endif
                    <div class="cursor-pointer li">
                        <div 
                       x-on:click="window.dispatchEvent(new CustomEvent('triggerShowPopup', { 
                                detail: { user: {{ json_encode($organisationPeople) }}, showAssessmentCriteriaButton: true } 
                            }))"
                            class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            <span class="font-semibold text-sm">View Profile</span>
                        </div>
                    </div>
                    {{-- {{dd($createdPlansId)}} --}}
                    @if (!$plan)
                        <div class="cursor-pointer li">
                            <div @click="peopleId = {{ $organisationPeople->id }}; createPlanPopup = true;"
                                class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                <span class="font-semibold text-sm">Create Plan</span>
                            </div>
                        </div>
                    @endif
                    @if (!$reachedFiveLevels)
                        <div class="cursor-pointer li">
                            <div @click="addDirectReportPopup = true; addDirectReport({{ $organisationPeople->id }})"
                                class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                <span class="font-semibold text-sm">Add Direct Reports</span>
                            </div>
                        </div>
                    @endif

                    <?php if(isset($row) && isset($rootElement) && !empty($rootElement)) { ?>
                    <div class="cursor-pointer li">
                        <div @click="addReportingManagerPopup = true; addDirectReport({{ $organisationPeople->id }}, {{ $row }})"
                            class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                            <span class="font-semibold text-sm">Add Reporting Manager</span>
                        </div>
                    </div>
                    <?php } ?>

                    <div class="cursor-pointer li">
                        <?php if (isset($margin) && !empty($margin)) { ?>
                        <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                            @click="confirmDelete({{ $organisationPeople->id }}, 'Delete {{ $organisationPeople->forename }}', 'Are you sure want to delete the people <b>{{ $organisationPeople->forename }}</b>?', '{{ asset('images/redTrashIcon.svg') }}', 'deletePeopleFromOrganization')">
                            <span class="font-semibold textRed text-sm">Delete</span>
                        </div>

                        <?php } else { ?>
                        <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                            @click="deleteOrganisationPeople({{ $row->id ?? 0 }}, {{ $parentId ?? 0 }}, {{ $organisationPeople->id }}, 'Delete {{ $organisationPeople->forename }}', 'Are you sure want to delete the people <b>{{ $organisationPeople->forename }}</b>?', '{{ asset('images/redTrashIcon.svg') }}', 'deletePeopleFromOrganizationChart')">
                            <span class="font-semibold textRed text-sm">Delete</span>
                        </div>

                        <?php } ?>

                    </div>
                    <!-- Add more parent actions if needed -->
                </div>
            </div>
        </div>
    </div>
    <!-- Parent node border -->
    <div class="border"></div>
    <!-- Display parent name and ID -->
    <div class="p-2 ml-3 flex flex-col">
        <span class="font-semibold text-sm overflow-hidden whitespace-nowrap w-48 text-ellipsis"
            title="{{ $organisationPeople->forename . ' ' . $organisationPeople->surname }}">
            {{ $organisationPeople->forename . ' ' . $organisationPeople->surname }}</span>
        <span class="text-xs text-gray-500 ">{{ $organisationPeople->employee_id }}</span>
    </div>
    <!-- Drag icon for parent -->
    <img class="absolute dragIcon left-1/2" src="{{ asset('images/dragcircle.svg') }}" alt="Drag Icon">

</div>
