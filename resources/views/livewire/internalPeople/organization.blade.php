<!-- header-->
<form wire:submit.prevent="createOrganisaton">
<div x-data="organizationChart()">
    <div x-show="isVisible" class="bg-white p-2 flex px-3  w-full border organisationTitle" x-cloak>
        <div class="flex gap-2 halfwidth ">
        <img @click="zoomIn" class="search-icon p-1 rounded-lg cursor-pointer w-10 h-10 border-2 bg-white" src="{{ asset('images/Plus.svg') }}" alt="Zoom In">
            <div class="text-sm text-gray-700 flex w-16 h-10 justify-center items-center p-1 rounded-lg border-2 bg-white" x-text="`${zoomLevel}%`"></div>
            <img @click="zoomOut" class="search-icon p-1 rounded-lg cursor-pointer border-2 w-10 h-10 bg-white" src="{{ asset('images/Minus.svg') }}" alt="Zoom Out">

        </div>
        <div class="flex justify-between items-center w-full">
            <div>
                <input wire:model="organisationName" type="text"
                    class=" outline-none block py-1.5 OrganizationInput px-3 HRInfo text-gray-900 shadow-sm text-center rounded-lg placeholder:text-gray-500 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6"
                    placeholder="Organization Name"
                    @if (!empty($organistaionDetails)) {{ $organistaionDetails->name }} @endif>
                @error('organisationName')
                <span class="text-red-500 text-xs">{{ $message }}</span>
                @enderror
            </div>
            <img class="block" src="{{ asset('images/SquareButton.svg') }}" alt="Search Icon">
        </div>
    </div>
    <!-- header -->
    <div class="w-full relative" x-bind:class="{ 'contentHeight': isVisible || clickToBeginPopup }">
        <!-- Listing of selected individuals on the right side -->
        <div class="flex">
            <div x-show="isVisible" class="bg-white w-72 p-2" x-cloak>
                <div class="flex justify-between items-center">
                    <h2 class="text-black text-center text-base font-semibold">Selected Individuals</h2>
                    <img class="search-icon p-1 rounded-lg border-2 bg-white" src="{{ asset('images/Plus.svg') }}" alt="Search Icon" @click="individualPopup = true">
                </div>
                <p class="text-sm text-gray-500 mt-2">Click to add individuals on your org chart</p>
                <ul class="mt-4 scrollable-list space-y-3">
                <template x-for="user in selectedPeoplesForOrganisation.filter(u => !addedPeoplesInOrganisation.includes(u.id))" :key="user.id">

                        <li class="flex border-2 items-center justify-evenly py-2 relative">
                            <div class="border rounded-full w-10 h-10 text-white flex justify-center items-center text-center bg-cyan-500" x-text="`${user.forename.charAt(0).toUpperCase()}${user.surname.charAt(0).toUpperCase()}`"></div>
                            <div class="space-y-1">
                                <span class="text-sm overflow-hidden whitespace-nowrap w-20 text-ellipsis font-semibold block" x-text="`${user.forename} ${user.surname}`"></span>
                                <span class="text-xs text-gray-500 block overflow-hidden whitespace-nowrap w-20 text-ellipsis font-semibold" x-text="`${user.latest_role ?? ''} ${user.employee_id ? `(${user.employee_id})` : ''}`"></span>
                            </div>
                            <div class="dropdown-container">
                                <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center">
                                    <img class="h-5 w-5" src="{{ asset('images/DotsThreeVertival.svg') }}">
                                </button>
                                <div id="dropdown2" class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44">
                                    <div class="py-2 ul text-sm text-gray-700">
                                        <div class="cursor-pointer li" x-on:click="window.dispatchEvent(new CustomEvent('triggerShowPopup', { detail: { user: user } }))">

                                            <div class="flex gap-5 py-2 px-5 hover:bg-gray-100">
                                                <span class="font-semibold text-sm">View Profile</span>
                                            </div>
                                        </div>
                                        <div class="cursor-pointer li" @click="addDirectReportPopup = true; $wire.updateDirectReportOptions(user.id, null, selectedPeoplesForOrganisation.map(user => user.id))">

                                            <div class="flex gap-5 py-2 px-5 hover:bg-gray-100">
                                                <span class="font-semibold text-sm">Add Direct Reports</span>
                                            </div>
                                        </div>
                                        <div class="cursor-pointer li" @click="removeSelection(user.id)">
                                            <div class="flex gap-5 py-2 px-5 hover:bg-gray-100">
                                                <span class="font-semibold textRed text-sm">Delete</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </template>

                </ul>
            </div>

            <!-- Organization Chart inclusion -->
            @include('livewire.internalPeople.organizationChart')

        </div>

        <!-- Welcome to My Organisation Popup -->
        <template x-if="isFirstClick">

            <div x-show="clickToBeginPopup" x-cloak class="flex justify-center items-center mt-2">

                <div
                    class="border bg-white flex justify-center flex-col items-center Organisation border-gray-200 rounded-xl">
                    <div>
                        <img class="" src="{{ asset('images/Organization.png') }}" alt="Organization">
                    </div>
                    <div class="space-y-2">
                        <h2 class="text-black font-semibold text-center">Welcome to My Organisation</h2>
                        <p class="text-sm text-gray-500">
                            This is a place for you to build organisation charts, create and manage succession plans
                            and view any updates we may have.
                        </p>
                        <div class="flex justify-center items-center">

                            <button id="clickToBegin"
                                @click="isVisible = !isVisible; isFirstClick = false; showSaveButton = true"
                                class="text-white text-xs bg-mainBlue p-3 rounded-lg font-semibold">
                                Click to begin
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- Individual Popup Modal -->
        @include('livewire.internalPeople.addPeoplesToOrganisationPopup')

    </div>
</div>
</form>

<script>
    function organizationChart() {
        return {
            isFirstClick: true,
            selectedPeoples: [],
            selectedPeoplesForOrganisation: [], 
            addedPeoplesInOrganisation: [],
            peoplesExistsInOrganisation : [],
            zoomLevel: 100, // default zoom level
            currentPage: 1,
            lastPage: 1,
            loading: false,
            query: '',
            zoomIn() {
                if (this.zoomLevel < 200) { // Maximum zoom level
                    this.zoomLevel += 10;
                }
            },
            zoomOut() {
                if (this.zoomLevel > 30) { // Minimum zoom level
                    this.zoomLevel -= 10;
                }
            },
            showLoading() {
                $("#loading-spinner").removeClass('hidden');
            },
            handleScroll(event) {
                const ulElement = event.target;
                const isAtBottom = ulElement.scrollTop + ulElement.clientHeight +50 >= ulElement.scrollHeight;
                
                console.log(`Scrolled to bottom: ${isAtBottom}, Current Page: ${this.currentPage}, Last Page: ${this.lastPage}, Loading: ${this.loading}`);
                
                if (isAtBottom && this.currentPage < this.lastPage && !this.loading) {
                    this.showLoading();
                    this.fetchPaginatedItems(this.currentPage + 1);
                }
            },

            async fetchPaginatedItems(page = 1, query = '') {
                try {
                    this.loading = true;
                    const response = await fetch(`/paginated-internal-peoples?page=${page}&query=${encodeURIComponent(query)}`);
                    const data = await response.json();
                    
                    if (page === 1) {
                        this.selectedPeoples = data.data; // Initialize with first page items
                    } else {
                        this.selectedPeoples = [...this.selectedPeoples, ...data.data]; // Append next page items
                    }

                    this.currentPage = data.current_page;
                    this.lastPage = data.last_page;
                    window.dispatchEvent(new CustomEvent('updateInternalPeoples', { 
                                detail: { user: data.data } 
                            }));
                } catch (error) {
                    console.error("Error fetching paginated items:", error);
                } finally {
                    this.loading = false;
                    $("#loading-spinner").addClass('hidden');
                }
            },

            startOrganisationChart() {
                this.isVisible = true;
                this.isFirstClick = false;
                this.showSaveButton = true;
            },
            search() {
                const lowerCaseQuery = this.query.trim().toLowerCase();
                this.showLoading();
                this.fetchPaginatedItems(1, lowerCaseQuery);
                this.filteredUsers();
                            
            },
            filteredUsers() {
                return this.selectedPeoples; 
            },
            isUserInOrganisation(userId) {
                return this.peoplesExistsInOrganisation.some(existingUser => existingUser.id === userId);
            },
            toggleSelection(user) {
                
                const existingUserIndex = this.selectedPeoplesForOrganisation.findIndex(selectedUser => selectedUser.id === user.id);

                if (existingUserIndex !== -1) {
                    this.selectedPeoplesForOrganisation.splice(existingUserIndex, 1);
                } else {
                    this.selectedPeoplesForOrganisation.push(user);
                }
                localStorage.setItem('selectedPeoplesForOrganisation', JSON.stringify(this.selectedPeoplesForOrganisation.map(user => user.id)));
            },

            isSelected(userId) {
                return this.selectedPeoplesForOrganisation.some(selectedUser => selectedUser.id === userId);
            },

            removeSelection(userId) {
                this.selectedPeoplesForOrganisation = this.selectedPeoplesForOrganisation.filter(id => id !== userId);
                localStorage.setItem('selectedPeoplesForOrganisation', JSON.stringify(this.selectedPeoplesForOrganisation.map(user => user.id)));
            },
            init() {
                this.fetchPaginatedItems();
                Livewire.on('updateSelection', (data) => {
                    this.addedPeoplesInOrganisation = data[0];
                    console.log(this.addedPeoplesInOrganisation, "this.addedPeoplesInOrganisation");
                });
                Livewire.on('selectedPeoplesForOrganisation', (data) => {
                    this.selectedPeoplesForOrganisation = data[0];
                    console.log(this.selectedPeoplesForOrganisation, "this.selectedPeoplesForOrganisation");
                    window.dispatchEvent(new CustomEvent('updateInternalPeoples', { 
                        detail: { user: this.selectedPeoplesForOrganisation } 
                    }));
                    this.peoplesExistsInOrganisation = JSON.parse(JSON.stringify(data[0]));
                    this.addedPeoplesInOrganisation = JSON.parse(JSON.stringify(this.selectedPeoplesForOrganisation.map(person => person.id)));
                });
                Livewire.on('addIndividualPeople', (item) => {
                    let newEntry = item[0];
                    this.selectedPeoples.push(newEntry); // Add new entry to the items array
                });

            },

        };
    }
</script>