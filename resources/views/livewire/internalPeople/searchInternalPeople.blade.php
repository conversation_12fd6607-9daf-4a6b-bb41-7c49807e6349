<div class="relative" x-data="searchComponent()">
<div class="flex items-center w-full border border-gray-300  rounded-md">
    <!-- Search Input -->
    <input
        x-ref="searchInput"
        type="text"
        x-model="query"
        placeholder="Search Internal People"
        class=" focus:outline-none focus:border-mainBlue focus:ring-1 focus:ring-mainBlue w-full"
    />

    <!-- Search Button -->
    <button
        @click="search()"
        class="bg-mainBlue text-white px-4 py-2 rounded-r-md flex items-center justify-center focus:outline-none hover:bg-blue-700"
    >
        <i class="fas fa-search"></i>
    </button>
</div>
    <div  @click.away="closePopup()"> <!-- Wrapper for the entire popup to detect outside clicks -->
        <ul
            :class="{
                'h-96': filteredItems.length > 5,
                'max-h-60': filteredItems.length <= 5
            }"
            class="absolute z-50 bg-white overflow-y-auto border border-gray-200 rounded-md shadow-lg mt-1"
            x-show="showSearchList"
            @scroll="handleScroll($event)">

            <template x-if="loading">
                <li class="p-2 text-gray-500">Searching...</li>
            </template>

            <template x-if="!loading && filteredItems.length === 0 && query !== ''">
                <li class="p-2 text-gray-500">No results found.</li>
            </template>

            <template x-for="item in filteredItems" :key="item.id">
                <li
                    class="p-2 border-b cursor-pointer hover:bg-gray-100"
                    @click="showPopup(item); filteredItems = []; query = ''">
                    <span x-text="`${item.forename} ${item.surname}`"></span>
                </li>
            </template>
        </ul>
    </div>
    @include('livewire.internalPeople.showInternalPeopleProfile')
</div>


<script>
    function searchComponent() {
        return {
            query: '',
            items: [],
            filteredItems: [],
            isPopupVisible: false,
            selectedItem: {},
            updateInternalpeoplePopup: @entangle('updateInternalpeoplePopup'),
            assessmentCriteria: [],
            savedAsssessmentCriteria:[],
            savedNineBoxGrid:[],
            nineBoxGrid: [],
            isAdminUser: false,
            isMasterUser: false,
            canAccessAssessmentCriteria: false,
            editingOrganisationId: null,
            criterias: [],
            nineBoxGridData: [],
            showAssessmentCriteriaButton: false,
            currentPage: 1,
            lastPage: 1,
            loading: false,
            showSearchList:false,
            allInternalPeoples: [],
            canShowAssessmentButton() {
                return ((this.isAdminUser || this.isMasterUser || this.canAccessAssessmentCriteria) && this.editingOrganisationId !== null && this.showAssessmentCriteriaButton);
            },
            showLoading() {
                $("#loading-spinner").removeClass('hidden');
            },

            search() {
                const lowerCaseQuery = this.query.trim().toLowerCase();

                if (lowerCaseQuery !== '') {
                    this.showLoading();
                    this.fetchPaginatedItems(1, lowerCaseQuery);
                    setTimeout(() => {
                        this.showSearchList = true;
                    }, 500);
                } else {
                    this.filteredItems = [];
                }
                            
            },
            handleScroll(event) {
                const ulElement = event.target;
                const isAtBottom = ulElement.scrollTop + ulElement.clientHeight +50 >= ulElement.scrollHeight;
                
                console.log(`Scrolled to bottom: ${isAtBottom}, Current Page: ${this.currentPage}, Last Page: ${this.lastPage}, Loading: ${this.loading}`);
                
                if (isAtBottom && this.currentPage < this.lastPage && !this.loading) {
                    this.showLoading();
                    this.fetchPaginatedItems(this.currentPage + 1);
                }
            },
            async fetchPaginatedItems(page = 1, query = null) {
                try {
                    this.loading = true;
                    const response = await fetch(`/paginated-internal-peoples?page=${page}&query=${query}`);
                    const data = await response.json();
                    
                    if (page === 1) {
                        this.items = data.data; // Initialize with first page items
                    } else {
                        this.items = [...this.items, ...data.data]; // Append next page items
                    }
                    this.filteredItems = [...this.items]; // Update filteredItems with the new list                   


                    this.updateAllInternalPeoples(data.data);
                    this.currentPage = data.current_page;
                    this.lastPage = data.last_page;
                    console.log(this.allInternalPeoples);
                } catch (error) {
                    console.error("Error fetching paginated items:", error);
                } finally {
                    this.loading = false;
                    $("#loading-spinner").addClass('hidden');
                }
            },
            updateAllInternalPeoples(data) {
                data.forEach(item => {
                        // Check if the item already exists in `allInternalPeoples`
                        const existingIndex = this.allInternalPeoples.findIndex(existingItem => existingItem.id === item.id);

                        if (existingIndex === -1) {
                            // If not found, append the new item to the array
                            this.allInternalPeoples.push(item);
                        } else {
                            // If found, replace the existing item
                            this.allInternalPeoples[existingIndex] = item;
                        }
                    });
            },

            showPopup(item, showAssessmentCriteriaButton = false) {
                this.showAssessmentCriteriaButton = showAssessmentCriteriaButton;
                this.selectedItem = {};
                this.processSkills(item);
                this.selectedItem = item;
                this.criterias = this.assessmentCriteria[item.id] || [];
                this.nineBoxGridData = this.nineBoxGrid[item.id] || [];
                document.getElementById('summaryButton')?.click();


                // Ensure popup visibility is updated after DOM is rendered
                this.$nextTick(() => {
                    this.isPopupVisible = true;
                });
            },
            editIndividualCriteria(criterias, internalPeopleId) {
                Livewire.dispatch("editIndividualCriteria", {
                    editAssessmentCriterias: criterias,
                    internalPeopleId: internalPeopleId
                });
            },
            closeIndividualCriteriaPopup(internalPeopleId) {
                if (this.savedAsssessmentCriteria[internalPeopleId]) {
                    // Deep copy the saved original values to prevent reference issues
                    this.criterias = JSON.parse(JSON.stringify(this.savedAsssessmentCriteria[internalPeopleId]));
                } else {
                    console.log("No saved criteria for this internalPeopleId");
                }

            },
            edit9BoxGrid (nineBoxGridData, internalPeopleId) {
                Livewire.dispatch("edit9BoxGrid", {
                    nineBoxGridData: nineBoxGridData,
                    internalPeopleId: internalPeopleId
                });

            },
            closeNineBoxGridPopup(internalPeopleId) {
                if (this.savedNineBoxGrid[internalPeopleId]) {
                    // Deep copy the saved original values to prevent reference issues
                    this.nineBoxGridData = JSON.parse(JSON.stringify(this.savedNineBoxGrid[internalPeopleId]));
                } else {
                    console.log("No saved criteria for this internalPeopleId");
                }

            },

            updatePopup(item) {
                const updatedItem = item[0]; // Assuming item[0] is the updated item

                // Find the index of the updated item in the items array
                const index = this.items.findIndex(existingItem => existingItem.id === updatedItem.id);
                if (index !== -1) {
                    this.items[index] = updatedItem; // Update the item in the items array
                }

                this.processSkills(updatedItem);
                this.selectedItem = updatedItem;

                this.$nextTick(() => {
                    this.isPopupVisible = true;
                    this.filteredItems = [];
                    this.query = '';
                });

            },

            processSkills(item) {
                const peopleSkills = {
                    'Specialised': [],
                    'Common': [],
                    'Certification': [],
                    'Professional': [],
                };

                if (Array.isArray(item.internal_skills)) {
                    item.internal_skills.forEach((skill) => {
                        if (!peopleSkills[skill.skill_type]) {
                            peopleSkills[skill.skill_type] = [];
                        }
                        peopleSkills[skill.skill_type].push(skill);
                    });
                    item.internal_skills = peopleSkills;
                }
            },

            closePopup() {
                this.showSearchList = false;
                // this.filteredItems = {};
                // this.query = '';
            },

            init() {
                this.filteredItems = [];
                // this.fetchPaginatedItems(); 
                Livewire.on('addIndividualPeople', (item) => {
                    let newEntry = item[0];
                    this.processSkills(newEntry);
                    this.items.push(newEntry); // Add new entry to the items array
                });
                Livewire.on('updateIndividualPeople', (item) => {
                    this.updatePopup(item);
                });
                window.addEventListener('triggerShowPopup', (event) => {
                    let item = event.detail.user;
                    console.log(this.allInternalPeoples);
                    let showButton = event.detail.showAssessmentCriteriaButton;
                    let index = this.allInternalPeoples.findIndex(existingItem => existingItem.id === item.id);
                    item = this.allInternalPeoples[index];
                    this.showPopup(item, showButton);
                });
                window.addEventListener('updateInternalPeoples', (event) => {
                    this.updateAllInternalPeoples(event.detail.user);                   
                });

                Livewire.on('assessmentCriteriaData', (data) => {
                    this.assessmentCriteria = data[0];
                    this.savedAsssessmentCriteria = JSON.parse(JSON.stringify(this.assessmentCriteria)); // Deep copy
                    this.nineBoxGrid = data[1];
                    this.savedNineBoxGrid = JSON.parse(JSON.stringify(this.nineBoxGrid)); // Deep copy
                    this.isAdminUser = data[2];
                    this.editingOrganisationId = data[3];
                    this.isMasterUser = data[4];
                    this.canAccessAssessmentCriteria = data[5];
                });
            },
        };
    }
</script>