<div x-show="viewNotesPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <div class="modal-content border w-full max-w-lg md:max-w-2xl max-h-[80vh] border-gray-300 border-solid bg-white shadow-lg rounded-xl z-50">
        <div class="flex justify-between items-center p-4">
            <h2 class="text-sm font-bold chart-heading">Notes</h2>
            <button type="button" @click="viewNotesPopup = false" class="text-gray-500 hover:text-gray-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Border above buttons -->
        <div class="w-full border-t border-gray-200"></div>

        <!-- Criteria Rows with Vertical Scroll -->
        <div class="p-4 max-h-[60vh] overflow-y-auto">
            <p x-text="selectedNotes" class="text-base leading-6"></p>
        </div>
    </div>
</div>
