<div x-show="createPlanPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <template x-if="step === 4">

        <div
            class="modal-content step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">

            <span class="py-3 px-3 rounded-full GreenBackground mainGreen text-center">
                <img class="w-6 h-6" src="{{ asset('images/right_tick_with_circle_green.svg') }}">
            </span>

            <h1 class="font-medium px-4 text-xl text-center my-5">New plan was successfully added!
            </h1>

            <!-- border above buttons -->
            <div class="w-full border-t my-5 border-gray-200"></div>

            <!-- buttons wrapper -->
            <div class="flex gap-2 w-full px-4">
                <button @click="resetStepTo1();createPlanPopup = false;" type="button"
                    class="bg-white w-full text-black border p-2 rounded-md">Close</button>
                @if (!empty($plan))
                    <div wire:click="reviewPlan({{ $plan->id }})"
                        class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                        <span class="block"> Review</span>
                    </div>
                @else
                    <button @click="createPlanPopup = false" type="button"
                        class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                        <span class="block"> Review</span>
                    </button>
                @endif

            </div>
        </div>
    </template>
    <template x-if="step != 4">
        <div
            class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
            {{-- <form wire:submit.prevent="addDirectReportForm"> --}}
            <div class="flex justify-between">

                <h2 class="font-semibold">Add New Plan</h2>
                <button type="button" @click="createPlanPopup = false" class="text-gray-500 hover:text-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex justify-between mt-3">
                <button>
                <span class="p-1 rounded-full px-2 text-xs"  :class="{ 'BlueBackground mainBlue': step === 1, 'GreenBackground mainGreen': step > 1 }">1</span>
                    <span class="text-xs font-medium">Plan Details</span>
                </button>
                <button>
                    <span
                        class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs" :class="{ 'BlueBackground mainBlue': step === 2, 'GreenBackground mainGreen': step > 2 }">2</span>
                    <span class="text-xs font-medium">Current Experience</span>
                </button>
                <button>
                    <span
                        class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs" :class="{ 'BlueBackground mainBlue': step === 2, 'GreenBackground mainGreen': step > 2 }">3</span>
                    <span class="text-xs font-medium">Additional Details</span>
                </button>
            </div>
            <div class="w-full border-t mt-3 border-gray-200"></div>

            <template x-if="step === 1">

                <!-- 1st Step -->
                <div class="h-full" wire:key="6787788">
                    <div class="h-5/6 flex items-center">
                        <div class="w-full">

                            <div class="relative py-2 bg-white">
                                <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                            </div>

                            <div class="modalscroll">

                                <div class="mb-3">
                                    <label for="name" class="block text-xs font-medium labelcolor">Name <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                    <input wire:model="name" type="text" id="name" placeholder="Enter plan name"
                                        name="name"
                                        class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                        required />
                                        <span x-show="errors.name" class="text-red-500 text-xs" x-text="errors.name"></span>
                                </div>

                                <div>
                                    <label for="description"
                                        class="block text-xs font-medium labelcolor">Description <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                    <textarea x-on:input="remainingWords" wire:model="descriptions" id="description" name="description" placeholder="Enter plan description"
                                        rows="3"
                                        class="mt-1 placeholder:text-gray-400 outline-none block w-full p-2 border placeholder:labelcolor border-gray-300 rounded-md"></textarea>
                                        <span x-show="wordLimitError" class="text-red-500 text-xs" x-text="wordLimitError"></span>
                                        <span x-show="errors.descriptions && !wordLimitError" class="text-red-500 text-xs" x-text="errors.descriptions"></span>
                                        <span x-show="errors.descriptions || wordLimitError"><br></span> <!-- Conditional line break -->
                                        
                                        <!-- Remaining word count display only if within the limit -->
                                        <template x-if="!wordLimitError">
                                            <span class="text-gray-500 text-xs">Remaining words: <span x-text="remainingWords"></span></span>
                                        </template>

                                    </div>

                                <div class="mb-3">
                                    <label for="tagged-role" class="mb-1 block text-xs font-medium labelcolor mt-3">Add
                                        Colleague to Plan</label>
                                    <div wire:ignore x-data="{
                                        multiple: true,
                                        value: @entangle('selectedColleagues'),
                                        options: {{ json_encode($colleagues) }},
                                        init() {
                                            this.$nextTick(() => {
                                                let choices = new Choices(this.$refs.selectColleagues, {
                                                    allowHTML: true,
                                                    removeItems: true,
                                                    removeItemButton: true,
                                                    placeholder: true,
                                                    placeholderValue: 'Select colleague(s)',
                                                    classNames: {
                                                        containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                        inputCloned: ['mb-0', 'p-0'],

                                                    },
                                                    noChoicesText: 'No tagged roles to choose from'
                                                })
                                                let refreshChoices = () => {
                                                    let selection = this.multiple ? this.value : [this.value]
                                    
                                                    choices.clearStore()
                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                        value,
                                                        label,
                                                        selected: selection.includes(value),
                                                    })))
                                                }
                                                refreshChoices()
                                    
                                                this.$refs.selectColleagues.addEventListener('change', () => {
                                                    this.value = choices.getValue(true)
                                                })
                                    
                                                this.$watch('value', () => refreshChoices())
                                                this.$watch('options', () => refreshChoices())
                                            })
                                        }
                                    }" class="text-black w-full">
                                        <select x-ref="selectColleagues" :multiple="multiple"></select>
                                    </div>
                                </div>
                            </div>

                            <!-- border above buttons -->
                            <div class="w-full border-t mt-4 border-gray-200"></div>

                            <!-- buttons wrapper -->
                            <div class="flex gap-2 w-full px-4 mt-4 ">
                                <button @click="createPlanPopup = false" type="button"
                                    class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                <button @click="validateStep1()" type="button"
                                    class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                    <span class="block"> Continue</span>
                                    <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                                </button>
                            </div>

                        </div>
                    </div>
                </div>
            </template>
            <template x-if="step === 2">
                <!-- 2nd Step -->
                <div wire:key="4565764576">

                    <div class="modalscroll">
                        <p class="text-sm font-normal labelcolor mt-3 mb-3">Enter any requirements
                            you have for your Succession Plan below.</p>

                        <div class="flex items-center justify-between mt-2">
                            <div class="" x-data="{ newTargetRoles: ''}">
                                <div>
                                    <label for="targetRoles" class="text-xs font-medium labelcolor">Target Role</label>
                                    <input type="text" class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" placeholder="Enter target role" x-model="newTargetRoles" 
                                        @keydown.enter.prevent="if(newTargetRoles !== '') { @this.mutipleSelectionData.targetRoles.push(newTargetRoles); newTargetRoles = ''; }"
                                        @blur="if(newTargetRoles !== '') { @this.mutipleSelectionData.targetRoles.push(newTargetRoles); newTargetRoles = ''; }"
                                    >
                                </div>
                            </div>
                            <div>
                                <label for="planName" class="text-xs font-medium labelcolor">Minimum
                                    Tenure</label>
                                <div class="Tenure mt-1">

                                    <input type="number" wire:model="min_exp" min="0" inputmode="numeric"
                                        class="outline-none block text-center bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md w-32"
                                        placeholder="0">
                                   
                                </div>
                            </div>
                        </div>
                        <!-- Display tags as individual elements -->
                        <div class="mt-2 flex flex-wrap">
                            <template x-for="(skill, index) in @this.mutipleSelectionData.targetRoles" :key="index">
                                <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2  mb-1">
                                    <span x-text="skill"></span>
                                    <button type="button" class="ml-1" @click="@this.mutipleSelectionData.targetRoles.splice(index, 1)">&times;</button>
                                </span>
                            </template>
                        </div>
                        <div class="mt-2">
                            <div class="" x-data="{ newStepUpCandidate: ''}">
                                <div>
                                    <label for="stepUpCandidate" class="text-xs font-medium labelcolor">Step-up candidates</label>
                                    <input type="text" class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" placeholder="Enter Step-up candidates" x-model="newStepUpCandidate" 
                                        @keydown.enter.prevent="if(newStepUpCandidate !== '') { @this.mutipleSelectionData.stepUpCandidate.push(newStepUpCandidate); newStepUpCandidate = ''; }"
                                        @blur="if(newStepUpCandidate !== '') { @this.mutipleSelectionData.stepUpCandidate.push(newStepUpCandidate); newStepUpCandidate = ''; }"
                                    >

                                    <div class="mt-2 flex flex-wrap">
                                        <template x-for="(skill, index) in @this.mutipleSelectionData.stepUpCandidate" :key="index">
                                            <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                                <span x-text="skill"></span>
                                                <button type="button" class="ml-1" @click="@this.mutipleSelectionData.stepUpCandidate.splice(index, 1)">&times;</button>
                                            </span>
                                        </template>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="" x-data="{ newKeyword: '' }">
                                <div>
                                    <label for="keyword"
                                        class="text-xs font-medium labelcolor">Keyword
                                    </label>
                                    <input type="text"
                                        class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                        placeholder="Enter keyword"
                                        x-model="newKeyword"
                                        @keydown.enter.prevent="if(newKeyword !== '') { @this.mutipleSelectionData.keyword.push(newKeyword); newKeyword = ''; }"
                                        @blur="if(newKeyword !== '') { @this.mutipleSelectionData.keyword.push(newKeyword); newKeyword = ''; }">

                                    <div class="mt-2 flex flex-wrap">
                                        <template
                                            x-for="(skill, index) in @this.mutipleSelectionData.keyword"
                                            :key="index">
                                            <span
                                                class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                                <span x-text="skill"></span>
                                                <button type="button" class="ml-1"
                                                    @click="@this.mutipleSelectionData.keyword.splice(index, 1)">&times;</button>
                                            </span>
                                        </template>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <!-- <div class="mt-2">
                            <label for="planName" class="text-xs font-medium labelcolor">Sectors</label>
                            <div wire:ignore x-data="{
                                multiple: true,
                                value: @entangle('selectedSectors'),
                                options: {{ json_encode($sectors) }},
                                init() {
                                    this.$nextTick(() => {
                                        let choices = new Choices(this.$refs.selectedSectors, {
                                            allowHTML: true,
                                            removeItems: true,
                                            removeItemButton: true,
                                            placeholder: true,
                                            placeholderValue: 'Select sector(s)',
                                            classNames: {
                                                containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                inputCloned: ['mb-0', 'p-0']
                                            },
                                            noChoicesText: 'No sectors to choose from'
                                        });

                                        let refreshChoices = () => {
                                            let selection = this.multiple ? this.value : [this.value];
                                            choices.clearStore();
                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                value,
                                                label,
                                                selected: selection.includes(value),
                                            })));
                                        };

                                        refreshChoices();

                                        this.$refs.selectedSectors.addEventListener('change', () => {
                                            this.selectedSectorsArr = choices.getValue(true)
                                            this.$dispatch('onSelectSectorAndIndustry');
                                            this.value = choices.getValue(true)
                                        })
                            
                                        this.$watch('value', () => refreshChoices())
                                        this.$watch('options', () => refreshChoices())
                                    })
                                }
                            }" class="text-black w-full">
                                <select x-ref="selectedSectors" :multiple="multiple"></select>
                            </div>
                        </div> -->

                        <div class="mt-2">
                            <label for="planName" class="text-xs font-medium labelcolor">Industries</label>
                            <div wire:ignore x-data="{
                                multiple: true,
                                value: @entangle('selectedIndustries'),
                                options: {{ json_encode($industries) }},
                                init() {
                                    this.$nextTick(() => {
                                        let choices = new Choices(this.$refs.selectedIndustries, {
                                            allowHTML: true,
                                            removeItems: true,
                                            removeItemButton: true,
                                            placeholder: true,
                                            placeholderValue: 'Select industry(s)',
                                            classNames: {
                                                containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                inputCloned: ['mb-0', 'p-0']
                                            },
                                            noChoicesText: 'No industries to choose from'
                                        });

                                        let refreshChoices = () => {
                                            let selection = this.multiple ? this.value : [this.value];
                                            choices.clearStore();
                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                value,
                                                label,
                                                selected: selection.includes(value),
                                            })));
                                        };

                                        refreshChoices();

                                        this.$refs.selectedIndustries.addEventListener('change', () => {
                                            this.selectedIndustriesArr = choices.getValue(true)
                                            this.$dispatch('onSelectSectorAndIndustry');
                                            this.value = choices.getValue(true)
                                        })
                            
                                        this.$watch('value', () => refreshChoices())
                                        this.$watch('options', () => refreshChoices())
                                    })
                                }
                            }" class="text-black w-full">
                                <select x-ref="selectedIndustries" :multiple="multiple"></select>
                            </div>
                        </div>

                        <div class="mt-2 mb-15">
                            <label for="planName" class="text-xs font-medium labelcolor">Companies</label>
                            <div wire:ignore x-data="{
                                multiple: true,
                                value: @entangle('selectedCompanies'),
                                options: {{ json_encode($companies) }},
                                allCompanies: {{ json_encode($companies) }},
                                init() {
                                    this.$nextTick(() => {
                                        let choices = new Choices(this.$refs.selectCompanies, {
                                            allowHTML: true,
                                            removeItems: true,
                                            removeItemButton: true,
                                            placeholder: true,
                                            placeholderValue: 'Select company(s)',
                                            classNames: {
                                                containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                inputCloned: ['mb-0', 'p-0'],


                                            },
                                            noChoicesText: 'No companies to choose from'
                                        });

                                        let refreshChoices = () => {
                                            let selection = this.multiple ? this.value : [this.value];

                                            choices.clearStore();
                                            choices.setChoices(this.options.map(({ value, label }) => ({
                                                value,
                                                label,
                                                selected: selection.includes(value),
                                            })));
                                        };
                                        refreshChoices();

                                        this.$refs.selectCompanies.addEventListener('change', () => {
                                            this.value = choices.getValue(true);
                                        });

                                        this.$watch('value', () => refreshChoices());
                                        this.$watch('options', () => refreshChoices());

                                        // Listen for the custom onSelectSectorAndIndustry event
                                        document.addEventListener('onSelectSectorAndIndustry', event => {
                                            this.options = this.filterCompaniesBasedOnSectorAndIndustries();
                                            refreshChoices();
                                        });
                                    });
                                },
                                filterCompaniesBasedOnSectorAndIndustries() {

                                    const selectedIndustries = this.selectedIndustriesArr;
                                    const selectedSectors = this.selectedSectorsArr;

                                    if((selectedIndustries && selectedIndustries.length > 0) && (selectedSectors && selectedSectors.length > 0)){
                                        return this.allCompanies.filter(company => 
                                                    selectedIndustries.includes(company.industry) && selectedSectors.includes(company.sector)
                                                );
                                    }
                                    else if(selectedIndustries && selectedIndustries.length > 0)
                                        return this.allCompanies.filter(company => 
                                                    selectedIndustries.includes(company.industry)
                                                );
                                    else if(selectedSectors && selectedSectors.length > 0)
                                        return this.allCompanies.filter(company => 
                                                    selectedSectors.includes(company.sector)
                                                );
                                    else {
                                        return this.allCompanies;
                                    }                                                            
                                }
                            }" class="text-black w-full">
                                <select x-ref="selectCompanies" :multiple="multiple"></select>
                            </div>
                        </div>
               
                    </div>

                    <!-- border above buttons -->
                    <div class="w-full border-t mt-4 border-gray-200"></div>

                    <!-- buttons for second step -->
                    <div class="flex gap-2 w-full mt-4 ">
                        <button @click="step = 1" type="button"
                            class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                            <span class="block font-medium">Back</span>
                        </button>
                        <button @click="step = 3" type="button"
                            class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <span class="block"> Continue</span>
                            <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                        </button>
                    </div>
                </div>
            </template>

            <template x-if="step === 3">                
    <!-- 3rd Step -->
                <div wire:key="3446464">

                    <div class="p-2 modalscroll3 pb-15">
                        <p class="text-sm font-normal labelcolor mb-3">Enter any requirements you
                            have for your Succession Plan below.</p>
                        <div>
                            <div class="mt-2">
                                <fieldset>
                                    <legend class="text-xs font-medium labelcolor mb-1">Gender
                                    </legend>

                                    <!-- radio button in form of buttons -->
                                    <ul class="donate-now flex gap-2">
                                        <li>
                                            <input type="radio" wire:model="gender" id="male" name="gender"
                                                value="2" class="cursor-pointer" />
                                            <label for="male"
                                                class="text-center font-semibold labelcolor">Male</label>
                                        </li>
                                        <li>
                                            <input type="radio" wire:model="gender" id="female" name="gender"
                                                value="1" class="cursor-pointer" />
                                            <label for="female"
                                                class="text-center font-semibold labelcolor">Female</label>
                                        </li>
                                        <li>
                                            <input type="radio" wire:model="gender" id="not_required"
                                                name="gender" value="0" class="cursor-pointer" />
                                            <label for="not_required" class="text-center font-semibold labelcolor">Not
                                                Required</label>
                                        </li>
                                    </ul>
                                </fieldset>
                            </div>

                            <div class="mt-2">
                                <label for="planName" class="text-xs font-medium labelcolor mb-1">Country</label>
                                <div wire:ignore x-data="{
                                    multiple: true,
                                    value: @entangle('selectedCountries'),
                                    options: {{ json_encode($countries) }},
                                    init() {
                                        this.$nextTick(() => {
                                            let choices = new Choices(this.$refs.selectCountries, {
                                                removeItems: true,
                                                removeItemButton: true,
                                                placeholder: true,
                                                placeholderValue: 'Select country(s)',
                                                classNames: {
                                                    containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                    inputCloned: ['mb-0', 'p-0'],
                                                    list: 'z-99'
                                                },
                                                noChoicesText: 'No countries to choose from'
                                            });
                                            let refreshChoices = () => {
                                                let selection = this.multiple ? this.value : [this.value];

                                                choices.clearStore();
                                                choices.setChoices(this.options.map(({ value, label }) => ({
                                                    value,
                                                    label,
                                                    selected: selection.includes(value),
                                                })));
                                            };
                                            refreshChoices();

                                            this.$refs.selectCountries.addEventListener('change', () => {
                                                this.value = choices.getValue(true);
                                            });

                                            this.$watch('value', () => refreshChoices());
                                            this.$watch('options', () => refreshChoices());
                                        });
                                    }
                                }" class="text-black w-full">
                                    <select x-ref="selectCountries" :multiple="multiple" class="form-control"></select>
                                </div>
                            </div>

                          
                            <div class="mt-4">
                                <fieldset>
                                    <div class="flex justify-between">
                                        <legend class="text-sm mb-2 font-normal text-gray-800">Is
                                            Ethnicity important?</legend>
                                        <div class="toggle-container">
                                            <label class="relative cursor-pointer">
                                                <input type="checkbox" class="toggle-input" wire:model="ethnicity" />
                                                <span class="toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="flex-wrap">
                                        <p class="text-xs font-normal labelcolor">Please note
                                            profiling through ethnicity has a low accuracy only
                                            select if this is absolutely neccessary</p>
                                    </div>
                                </fieldset>
                            </div>
                           
                            <div class="" x-data="{ 
                                            newQualifications: '', 
                                            newSkills: '' 
                                        }">
                                <div>
                                    <!--
                                    <label for="qualifications" class="text-xs font-medium labelcolor">Qalifications</label>
                                    <input type="text" class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" placeholder="Enter qualifications" x-model="newQualifications" 
                                        @keydown.enter.prevent="if(newQualifications !== '') { @this.mutipleSelectionData.qualifications.push(newQualifications); newQualifications = ''; }"
                                        @blur="if(newQualifications !== '') { @this.mutipleSelectionData.qualifications.push(newQualifications); newQualifications = ''; }"
                                    >
                                    -->

                                    <div class="mt-2 flex flex-wrap">
                                        <template x-for="(skill, index) in @this.mutipleSelectionData.qualifications" :key="index">
                                            <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                                <span x-text="skill"></span>
                                                <button type="button" class="ml-1" @click="@this.mutipleSelectionData.qualifications.splice(index, 1)">&times;</button>
                                            </span>
                                        </template>
                                    </div>
                                </div>


                                <div class="mt-4">
                                    <label for="skills" class="text-xs font-medium labelcolor">Skills</label>
                                    <input type="text" class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500" placeholder="Enter skills" x-model="newSkills" 
                                        @keydown.enter.prevent="if(newSkills !== '') { @this.mutipleSelectionData.skills.push(newSkills); newSkills = ''; }"
                                        @blur="if(newSkills !== '') { @this.mutipleSelectionData.skills.push(newSkills); newSkills = ''; }"
                                    >

                                    <div class="mt-2 flex flex-wrap">
                                        <template x-for="(skill, index) in @this.mutipleSelectionData.skills" :key="index">
                                            <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                                <span x-text="skill"></span>
                                                <button type="button" class="ml-1" @click="@this.mutipleSelectionData.skills.splice(index, 1)">&times;</button>
                                            </span>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- border above buttons -->
                    <div class="w-full border-t  border-gray-200"></div>

                    <!-- 3rd steps button -->
                    <div class="flex gap-2 w-full mt-4 px-4">
                        <button @click="step = 2" type="button"
                            class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                            <span class="block font-medium">Back</span>
                        </button>
                        <input type="hidden" wire:model="createdPlans">
                        <button type="button" @click="$wire.createPlan(peopleId)"
                            class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                            <img class="h-5 w-5" src="{{ asset('images/plus-white-without-circle.svg') }}">
                            <span class="block">Add Plan</span>
                        </button>
                    </div>
                </div>
            </template>
            {{-- </form> --}}
        </div>
    </template>
</div>
