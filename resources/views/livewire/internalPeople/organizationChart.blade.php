<div class="border-2 w-full relative outermostdiv flex items-center treeOuter z-10 {{ count($organisationChartData) > 0 ? 'treeHeight' : '' }} treeWidth overflow-x-auto" 
x-data="{
    peopleId: null,
    createPlanPopup: @entangle('createPlanPopup'),
    step: @entangle('step'),
    name: @entangle('name'), 
    descriptions: @entangle('descriptions'),
    selectedSectorsArr: [],
    selectedIndustriesArr: [],
    errors: {},
    maxWords: 300,
    constraints: {
        name: {
            presence: { allowEmpty: false, message: '^Name is required.' }
        },
    },
    // Helper function to count words
    wordCount(text) {
        if(!text){
        return 0;
        }
        return text.trim().split(/\s+/).length;
    },

    // Computed property to get remaining words
    get remainingWords() {
        const currentWordCount = this.wordCount(this.descriptions);
        return this.maxWords - currentWordCount;
    },
    get wordLimitError() {
        const currentWordCount = this.wordCount(this.descriptions);
        return this.wordCount(this.descriptions) > this.maxWords 
            ? `Description should not exceed ${this.maxWords} words. (Current count: ${currentWordCount})` 
            : null;
    },

    validateStep1() {
        
        let validationErrors = validate({ name: this.name }, this.constraints);
        const currentWordCount = this.wordCount(this.descriptions);

        if (!this.descriptions || this.descriptions.trim() === '') {
            validationErrors = validationErrors || {};
            validationErrors.descriptions = ['Description is required.'];
        }

         if (this.wordLimitError) {
            validationErrors = validationErrors || {};
            validationErrors.descriptions = [this.wordLimitError];
        }

        // If validation errors exist, extract the first error message for each field
        this.errors = Object.fromEntries(
            Object.entries(validationErrors || {}).map(([field, messages]) => [field, messages[0]])
        );

        // Proceed to the next step if there are no errors
        if (!Object.keys(this.errors).length) {
            this.step = 2;
        }
    } ,
    resetStepTo1() {
        this.step = 1;
    },
     addDirectReport(organisationPeopleId, row = null) {
        // Retrieve 'selectedPeoplesForOrganisation' from localStorage
        const storedData = localStorage.getItem('selectedPeoplesForOrganisation');
        const selectedPeoplesForOrganisation = storedData ? JSON.parse(storedData) : [];
        console.log(selectedPeoplesForOrganisation);

        // Call the Livewire method and pass both organisationPeopleId, row, and selected people
        $wire.updateDirectReportOptions(organisationPeopleId, row, selectedPeoplesForOrganisation);
    }
}" x-cloak>
    {{-- Hierarchical view --}}
    @if(count($organisationChartData) > 0)

    <div class="flex justify-between treeWidth  w-full treeHeight overflow-x-auto">

        @foreach ($organisationChartData as $organization)
        <div class="tree" x-bind:style="'transform: scale(' + (zoomLevel / 100) + ')'">
        <ul>
                <li>
                    <a href="#">
                        <!-- Parent node content -->
                        @include('livewire.internalPeople.organisationPeople', [
                            'organisationPeople' => $organization->internalPeople,
                            'parentId' => 0,
                            'row' => $organization,
                            'reachedFiveLevels' => false,
                            "rootElement" => true
                        ])
                    </a>
                    @if ($organization->descendants->isNotEmpty())
                    {{-- fromleft fromright --}}
                    <ul class="">  
                        @foreach ($organization->descendants as $children)
                        <li>
                            <a href="#">
                                @include('livewire.internalPeople.organisationPeople', [
                                    'organisationPeople' => $children->internalPeople,
                                    'parentId' => $children->descendants->isNotEmpty() ? 0 : $organization->internal_people_id,
                                    'row' => $children,
                                    'reachedFiveLevels' => false
                                ])
                            </a>
                            @if ($children->descendants->isNotEmpty())
                            <ul>
                                @foreach ($children->descendants as $subChild)
                                <li>
                                    <a href="#">
                                        @include('livewire.internalPeople.organisationPeople', [
                                            'organisationPeople' => $subChild->internalPeople,
                                            'parentId' =>  $subChild->descendants->isNotEmpty() ? 0 : $children->internal_people_id,
                                            'row' => $subChild,
                                            'reachedFiveLevels' => false
                                        ])
                                    </a>
                                    @if ($subChild->descendants->isNotEmpty())
                                    <ul>
                                        @foreach ($subChild->descendants as $subSubChild)
                                        <li>
                                            <a href="#">
                                                @include('livewire.internalPeople.organisationPeople', [
                                                    'organisationPeople' => $subSubChild->internalPeople,
                                                    'parentId' =>  $subSubChild->descendants->isNotEmpty() ? 0 : $subChild->internal_people_id,
                                                    'row' => $subSubChild,
                                                    'reachedFiveLevels' => false
                                                ])
                                            </a>
                                            @if ($subSubChild->descendants->isNotEmpty())
                                            <ul>
                                                @foreach ($subSubChild->descendants as $subSubSubChild)
                                                <li>
                                                    <a href="#">
                                                        @include('livewire.internalPeople.organisationPeople', [
                                                            'organisationPeople' => $subSubSubChild->internalPeople,
                                                            'parentId' => $subSubSubChild->descendants->isNotEmpty() ? 0 : $subSubChild->internal_people_id,
                                                            'row' => $subSubSubChild,
                                                            'reachedFiveLevels' => false
                                                        ])
                                                    </a>
                                                    @if ($subSubSubChild->descendants->isNotEmpty())
                                                    <ul>
                                                        @foreach ($subSubSubChild->descendants as $subSubSubSubChild)
                                                        <li>
                                                            <a href="#">
                                                                @include('livewire.internalPeople.organisationPeople', [
                                                                    'organisationPeople' => $subSubSubSubChild->internalPeople,
                                                                    'parentId' => $subSubSubChild->internal_people_id,
                                                                    'row' => $subSubSubSubChild,
                                                                    'reachedFiveLevels' => $reachedFiveLevels
                                                                ])
                                                            </a>
                                                        </li>
                                                        @endforeach
                                                    </ul>
                                                    @endif
                                                </li>
                                                @endforeach
                                            </ul>
                                            @endif
                                        </li>
                                        @endforeach
                                    </ul>
                                    @endif
                                </li>
                                @endforeach
                            </ul>
                            @endif
                        </li>
                        @endforeach
                    </ul>
                    @endif
                </li>
            </ul>
        </div>
        @endforeach
    </div>
    @endif

    {{-- Include popups --}}
    @include('livewire.internalPeople.createPlan')
</div>
@include('livewire.internalPeople.addDirectReport')
@include('livewire.internalPeople.replaceUserPopup')
@include('livewire.internalPeople.addReportingManager')
@include('livewire.internalPeople.viewReportingManager')
