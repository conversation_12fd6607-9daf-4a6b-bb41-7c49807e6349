<div class="">

    <div class="w-full flex" 
    x-data="{ 
        dopenNewRole: @entangle('dopenNewRole'), 
        open: @entangle('open'),
        step: @entangle('step'),
        forename: @entangle('forename'),
        surname: @entangle('surname'),
        gender: @entangle('gender'),
        roles: @entangle('roles'),
        start_date: @entangle('start_date'),
        newQualifications: '', 
        newSkills: '', 
        newCertifications: '', 
        errors: {},
        
        constraintsStep1: {
            forename: {
                presence: { allowEmpty: false, message: '^Forname field is required.' }
            },
            surname: {
                presence: { allowEmpty: false, message: '^Surname field is required.' }
            },
            gender: {
                presence: { allowEmpty: false, message: '^Gender field is required.' }
            }
        },

        constraintsStep2: {
            roles: {
                presence: { allowEmpty: false, message: '^Role field is required.' }
            },
            start_date: {
                presence: { allowEmpty: false, message: '^Start Date field is required.' }
            }
        },

        // Validate step 1
        validateStep1() {
            let validationErrors = validate({ 
                forename: this.forename, 
                surname: this.surname, 
                gender: this.gender 
            }, this.constraintsStep1);

            this.errors = Object.fromEntries(
                Object.entries(validationErrors || {}).map(([field, messages]) => [field, messages[0]])
            );

            if (!Object.keys(this.errors).length) {
                console.log('Step 1 is valid');
                this.step = 2;  // Move to step 2
            } else {
                console.log('Validation errors in Step 1:', this.errors);
            }
        },

        // Validate step 2
        validateStep2() {
            let validationErrors = validate({ 
                roles: this.roles, 
                start_date: this.start_date
            }, this.constraintsStep2);

            this.errors = Object.fromEntries(
                Object.entries(validationErrors || {}).map(([field, messages]) => [field, messages[0]])
            );

            if (!Object.keys(this.errors).length) {
                console.log('Step 2 is valid');
                this.step = 3;  // Move to step 3
            } else {
                console.log('Validation errors in Step 2:', this.errors);
            }
        },

        resetStepTo1() {
            this.step = 1;
            this.errors = {}; // Clear errors
        },
    }">
        <!-- Trigger button inside the modal -->
        <div class="flex text-right">
            <div class="rounded-lg p-px hover:scale-105">
                <button @click="open = true"
                    class="transition flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100">
                    <img class="h-3 w-auto" src="{{ asset('images/Plus.svg') }}" alt="Plus Icon">
                    <span> Add Individual</span>
                </button>
            </div>
        </div>

        <!-- Modal container -->
        <div x-show="open" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50"
            style="display:none">
            <!-- Modal background with a higher z-index -->
            <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

            <!-- Modal content with a lower z-index -->

            <div
                class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
                <!-- cross icon -->
                <img x-on:click="open = false" class="absolute right top-2 w-auto cursor-pointer"
                    src="{{ asset('images/cross.svg') }}" alt="Search Icon">

                <h2 class="font-semibold px-4">Add Internal Candidate</h2>
                <div class="flex justify-between mt-3 px-4">
                    <button>
                        <span
                            class="mainBlue p-1 rounded-full px-2 text-xs 
                                                @if ($step == 1) BlueBackground mainBlue
                                                @else
                                                GreenBackground mainGreen @endif">1</span>
                        <span class="text-xs font-medium">Personal Details</span>
                    </button>
                    <button>
                        <span
                            class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                                        @if ($step == 2) BlueBackground mainBlue
                                        @elseif($step > 2)
                                        GreenBackground mainGreen @endif">2</span>
                        <span class="text-xs font-medium">Professional Details</span>
                    </button>
                    <button>
                        <span
                            class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs
                                        @if ($step == 3) BlueBackground mainBlue
                                        @elseif($step > 3)
                                        GreenBackground mainGreen @endif">3</span>
                        <span class="text-xs font-medium">Skills</span>
                    </button>
                </div>
                <div class="w-full border-t mt-3 border-gray-200"></div>

                <template x-if="step === 1">
                    <!-- 1st Step -->
                    <div class="h-full" wire:key="123">
                        <div class="h-5/6 flex items-center">
                            <div class="w-full">

                                <div class="relative py-2 bg-white">
                                    <div class="absolute inset-0 flex items-center px-1" aria-hidden="true">
                                    </div>
                                </div>

                                <div class="px-4 modalscroll">
                                    <p class="text-xs font-normal labelcolor mt-3 mb-3">
                                        If the name is not available please use the employee id to ensure the
                                        individual
                                        may be identified by yourself and other team members in the system.
                                    </p>
                                    <div>
                                        <label for="Forename" class="text-xs font-medium labelcolor">Forename</label>
                                        <input type="text"
                                            class="mt-1 placeholder:text-gray-400 placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                                            placeholder="Enter forename" wire:model="forename">
                                            <span x-show="errors.forename" class="text-red-500 text-xs" x-text="errors.forename"></span>

                                    </div>
                                    <div>
                                        <label for="Surname" class="text-xs font-medium labelcolor">Surname</label>
                                        <input type="text"
                                            class="mt-1 placeholder:text-gray-400 placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                                            placeholder="Enter surname" wire:model="surname">
                                            <span x-show="errors.surname" class="text-red-500 text-xs" x-text="errors.surname"></span>
                                    </div>
                                    <div class="mt-2">
                                        <fieldset>
                                            <legend class="text-xs font-medium labelcolor">Gender</legend>
                                            <ul class="donate-now mt-1">
                                                <li>
                                                    <input type="radio" id="Male" name="gender" value="Male"
                                                        wire:model="gender" />
                                                    <label for="Male"
                                                        class="text-center font-semibold labelcolor">Male</label>
                                                </li>
                                                <li>
                                                    <input type="radio" id="Female" name="gender" value="Female"
                                                        wire:model="gender" />
                                                    <label for="Female"
                                                        class="text-center font-semibold labelcolor">Female</label>
                                                </li>
                                                <li>
                                                    <input type="radio" id="Not Applicable" name="gender"
                                                        value="Not Applicable" wire:model="gender" />
                                                    <label for="Not Applicable"
                                                        class="text-center font-semibold labelcolor">Not
                                                        Applicable</label>
                                                </li>
                                            </ul>
                                            <span x-show="errors.gender" class="text-red-500 text-xs" x-text="errors.gender"></span>
                                        </fieldset>
                                    </div>
                                    <div class="mt-2">
                                        <label for="planName" class="text-xs font-medium labelcolor">Country</label>
                                        <span class="ml-2 text-gray-700 text-xs">( Optional )</span>
                                        <div wire:ignore x-data="{
                                            multiple: false, // Set to false for single selection
                                            value: @entangle('selectedCountries'), // Ensure 'selectedCountry' is a single value
                                            options: {{ json_encode($countries) }},
                                            init() {
                                                this.$nextTick(() => {
                                                    let choices = new Choices(this.$refs.select, { removeItemButton: false, searchEnabled: true })
                                        
                                                    let refreshChoices = () => {
                                                        choices.clearStore()
                                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                                            value,
                                                            label,
                                                            selected: this.value === value,
                                                        })))
                                                    }
                                        
                                                    refreshChoices()
                                        
                                                    this.$refs.select.addEventListener('change', () => {
                                                        this.value = choices.getValue(true)
                                                    })
                                        
                                                    this.$watch('value', () => refreshChoices())
                                                    this.$watch('options', () => refreshChoices())
                                                })
                                            }
                                        }" class="w-full z-50 bg-white">
                                            <select x-ref="select"></select>
                                        </div>
                                    </div>
                                </div>

                                <!-- border above buttons -->
                                <div class="w-full border-t mt-4 border-gray-200"></div>

                                <!-- buttons wrapper -->
                                <div class="flex gap-2 w-full px-4 mt-4 ">
                                    <button x-on:click="open = false" type="button"
                                        class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                    <button @click="validateStep1()" type="button"
                                        class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                        <span class="block"> Continue</span>
                                        <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                                    </button>
                                </div>

                            </div>
                        </div>
                    </div>
                </template>

                <template x-if="step === 2">
                    <!-- 2nd Step -->
                    <div wire:key="234">

                        <div class="px-4 modalscroll">
                            <div class="mt-2">
                                <label for="roles" class="text-xs font-medium labelcolor">Role</label>
                                <input type="text"
                                    class="mt-1 placeholder:text-gray-400 labelcolor bg-white placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                                    placeholder="Enter role" wire:model="roles">
                                    <span x-show="errors.roles" class="text-red-500 text-xs" x-text="errors.roles"></span>
                            </div>

                             <div class="mt-2">
                                <label for="start_date" class="text-xs font-medium labelcolor">Date
                                    Joined</label>
                                <input type="date" wire:model="start_date"
                                    class="mt-1 block w-full p-2 border border-gray-300 rounded-md outline-none">
                                    <span x-show="errors.start_date" class="text-red-500 text-xs" x-text="errors.start_date"></span>
                            </div>
                            <div>
                                <label for="linkedInUrl" class="text-xs font-medium labelcolor">LinkedIn URL</label>
                                <input type="text"
                                    class="mt-1 placeholder:text-gray-400 labelcolor bg-white placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                                    placeholder="Enter LinkedIn URL" wire:model="linkedInUrl">
                            </div>
                            <div>
                                <label for="function" class="text-xs font-medium labelcolor">Function</label>
                                <input type="text"
                                    class="mt-1 placeholder:text-gray-400 labelcolor bg-white placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                                    placeholder="Enter function" wire:model="function">
                                @error('function')
                                    <span class="text-red-500 text-xs">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="mt-2">
                                <label for="division" class="text-xs font-medium labelcolor">Division</label>
                                <input type="text"
                                    class="mt-1 placeholder:text-gray-400 labelcolor bg-white placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                                    placeholder="Enter division" wire:model="division">
                                @error('division')
                                    <span class="text-red-500 text-xs">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="mt-2">
                                <label for="empid" class="text-xs font-medium labelcolor">Employee
                                    ID</label>
                                <input type="text"
                                    class="mt-1 placeholder:text-gray-400 placeholder:font-normal placeholder:text-sm block w-full p-2 outline-none border border-gray-300 rounded-md"
                                    placeholder="Enter employee ID" wire:model="empid">
                                @error('empid')
                                    <span class="text-red-500 text-xs">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <!-- border above buttons -->
                        <div class="w-full border-t mt-4 border-gray-200"></div>

                        <!-- buttons for second step -->
                        <div class="flex gap-2 w-full mt-4 px-4">
                            <button @click="step = 1" type="button"
                                class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                                <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                                <span class="block font-medium">Back</span>
                            </button>
                            <button  @click="validateStep2()" type="button"
                                class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                <span class="block"> Continue</span>
                                <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                            </button>
                        </div>
                    </div>
                </template>

                <template x-if="step === 3">
                    <!-- 3rd Step -->
                    <div wire:key="2343454">
                    <div class="px-4 modalscroll">
                        <!-- Specialised Section -->
                        <div>
                            <label for="qualifications" class="text-xs font-medium labelcolor">Specialised</label>
                            <input type="text"
                                class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                placeholder="Enter specialised skills" x-model="newQualifications"
                                @keydown.enter.prevent="if(newQualifications !== '') { @this.newSkillData.specialised.push(newQualifications); newQualifications = ''; }"
                                @blur="if(newQualifications !== '') { @this.newSkillData.specialised.push(newQualifications); newQualifications = ''; }"
                            >

                            <div class="mt-2 flex flex-wrap">
                                <template x-for="(skill, index) in @this.newSkillData.specialised" :key="index">
                                    <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                        <span x-text="skill"></span>
                                        <button type="button" class="ml-1" @click="@this.newSkillData.specialised.splice(index, 1)">&times;</button>
                                    </span>
                                </template>
                            </div>
                        </div>

                        <!-- Common Skills Section -->
                        <div class="mt-4">
                            <label for="skills" class="text-xs font-medium labelcolor">Skills</label>
                            <input type="text"
                                class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                placeholder="Enter common skills" x-model="newSkills"
                                @keydown.enter.prevent="if(newSkills !== '') { @this.newSkillData.common.push(newSkills); newSkills = ''; }"
                                @blur="if(newSkills !== '') { @this.newSkillData.common.push(newSkills); newSkills = ''; }"
                            >

                            <div class="mt-2 flex flex-wrapsummary">
                                <template x-for="(skill, index) in @this.newSkillData.common" :key="index">
                                    <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                        <span x-text="skill"></span>
                                        <button type="button" class="ml-1" @click="@this.newSkillData.common.splice(index, 1)">&times;</button>
                                    </span>
                                </template>
                            </div>
                        </div>

                        <!-- Certifications Section -->
                        <div class="mt-4">
                            <label for="certifications" class="text-xs font-medium labelcolor">Certification</label>
                            <input type="text"
                                class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                placeholder="Enter Certifications" x-model="newCertifications"
                                @keydown.enter.prevent="if(newCertifications !== '') { @this.newSkillData.certification.push(newCertifications); newCertifications = ''; }"
                                @blur="if(newCertifications !== '') { @this.newSkillData.certification.push(newCertifications); newCertifications = ''; }"
                            >

                            <div class="mt-2 flex flex-wrapsummary">
                                <template x-for="(cert, index) in @this.newSkillData.certification" :key="index">
                                    <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                        <span x-text="cert"></span>
                                        <button type="button" class="ml-1" @click="@this.newSkillData.certification.splice(index, 1)">&times;</button>
                                    </span>
                                </template>
                            </div>
                        </div>

                        <div class="w-full border-t mt-4 border-gray-200"></div>
                    </div>


                        <!-- border above buttons -->
                        <div class="w-full border-t  border-gray-200"></div>

                        <!-- 3rd steps button -->
                        <div class="flex gap-2 w-full mt-4 px-4">
                            <button @click="step = 2" type="button"
                                class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                                <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                                <span class="block font-medium">Back</span>
                            </button>
                            <button type="button" wire:click="addInternalCandidates"
                                class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                <img class="h-5 w-5" src="{{ asset('images/plus-white-without-circle.svg') }}">
                                <span class="block">Add Candidate</span>
                            </button>
                        </div>

                    </div>
                </template>


            </div>
        </div>



        <!-- Add new role popup -->
        <div x-show="dopenNewRole" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
            <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
            <div
                class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
                <form wire:submit.prevent="saveRole">
                    <div class="flex justify-between">
                        <h2 class="text-black-900 text-xl font-semibold">Add New Role</h2>
                        <button type="button" @click="dopenNewRole = false"
                            class="text-gray-500 hover:text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <input type="hidden">
                    <div class="mt-4">
                        <label for="newRoleTitle" class="block text-sm font-medium text-black">Role Title<span
                                class="text-red-500">*</span></label>
                        <input type="text" id="newRoleTitle" wire:model="newRoleTitle"
                            class="mt-2 bg-gray-50 p-1 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full @error('newRoleTitle') border-red-500 @enderror">
                        @error('newRoleTitle')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="flex justify-end mt-5 gap-2">
                        <button type="submit"
                            class="border border-gray-300 rounded-md py-2 px-3 text-sm font-semibold hover:bg-cyan-500 hover:text-white">Save</button>
                        <button type="button" @click="dopenNewRole = false"
                            class="border border-gray-300 rounded-md py-2 px-3 text-sm font-semibold hover:bg-cyan-500 hover:text-white">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    window.addEventListener('DOMContentLoaded', (event) => {
        Livewire.on('roleUpdated', (data) => {
            const role = data[0];
            window.dispatchEvent(new CustomEvent('role-created', {
                detail: role
            }));

        });
    });
</script>
