<div class="flex justify-between items-center gap-x-3">
    <h2 class="text-xl font-semibold text-gray-800 mr-auto"></h2> <!-- Add mr-auto -->

    <div class="flex gap-5">

    @include('livewire.internalPeople.searchInternalPeople')
        <!-- share button -->
        @if($editingOrganisationId)
            <div>
                <button @click="shareOrgansationPopup = true"
                    class="transition flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100">
                    <img class="h-3 w-auto" src="{{ asset('images/ShareNetwork.svg') }}" alt="Plus Icon">
                    <span>Share</span>
                </button>
            </div>
        @endif
        @include('livewire.internalPeople.shareOrganisationPopup')


        @if($isAdminUser || $isMasterUser)
        <div x-show="addOrganisationButton">
            <button id="assessmentCriteriaButton"
                @click="assessmentCriteriaPopup = true;"
                class="transition flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100">
                <img class="h-3 w-auto" src="{{ asset('images/Plus.svg') }}" alt="Plus Icon">
                <span>Asssessment Criteria</span>
            </button>
        </div>
        @include('livewire.internalPeople.assessmentCriteria')

         <div x-show="addOrganisationButton">
            <button id="9boxGridButton"
                @click="nineboxGridPopup = true;"
                class="transition flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100">
                <img class="h-3 w-auto" src="{{ asset('images/Plus.svg') }}" alt="Plus Icon">
                <span>9 Box Grid</span>
            </button>
        </div>
        @include('livewire.internalPeople.nineBoxGridPopup')
        @endif


        <div x-show="addOrganisationButton">
            <button id="addOrganisationButton"
                @click="clickToBeginPopup = true; addOrganisationButton = false;showOranisationListing = false"
                class="transition flex gap-2 shadow-md hover:shadow-lg items-center border ease-in-out delay-100 text-sm text-black rounded-lg py-2 px-3 font-semibold hover:bg-cyan-500 hover:text-white duration-100">
                <img class="h-3 w-auto" src="{{ asset('images/Plus.svg') }}" alt="Plus Icon">
                <span>Add Organisation</span>
            </button>
        </div>

        <!-- addIndividual people -->
        @include('livewire.internalPeople.addIndividual')

        <!-- Bulk upload Button -->
        @include('livewire.internalPeople.upload')


        <div x-show="showSaveButton" x-cloak
            class="w-32  rounded-lg p-px shadow- hover:scale-105">
            <button wire:click="createOrganisaton"
                class="w-full flex justify-center items-center gap-2 font-semibold transition ease-in-out delay-100 text-sm rounded-lg py-2 bg-cyan-500 text-white duration-100">
                <img class="h-3 w-auto" src="{{ asset('images/tick.svg') }}" alt="Your Company">
                <span>
                    @if ($editingOrganisationId)
                        Update
                    @else
                        Save
                    @endif
                </span>
            </button>
        </div>

    </div>

</div>

