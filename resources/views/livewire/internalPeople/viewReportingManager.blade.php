<!-- Modal -->
<div x-show="viewReportingManagerPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
        <div class="flex justify-between">
            <h2 class="text-black-900 text-xl font-semibold">View Reporting Manager</h2>
            <button type="button" @click="viewReportingManagerPopup = false" class="text-gray-500 hover:text-gray-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <div class="flex justify-center items-center mt-2">
            <div class="bg-white border selectPeople p-2 rounded-lg">
                <div class="items flex justify-center">
                    <ul>
                        <li>
                            <a href="#" class="">Dagon</a>
                            <a href="#" class="">Veil of Discord</a>
                            <ul>
                                <li>
                                    <a href="#">Null Talisman</a>
                                </li>
                             
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>  
        </div>
    </div>
</div>


<style>
        .items  {
        margin: 0 auto;
        padding: 0;
        text-align: center;
        color: black;
        font-family: tahoma;
        
    }

    .items ul {
        padding-top: 20px;
        position: relative;
    }


    /* Make all children "inline" */

    .items li {
        float: left;
        text-align: center;
        list-style-type: none;
        position: relative;
        padding: 20px 5px 0 5px;
    }


    /* Add horizontal connector. Note: they are 2 pseudo-elements */

    .items li::before,
    .items li::after {
        content: '';
        position: absolute;
        top: 0;
        right: 50%;
        width: 50%;
        height: 45px;
        z-index: 0;
        border-top: 1px solid red;
    }

    .items li::after {
        border-left: 1px solid green;
        left: 50%;
        right: auto;
    }


    /* Remove left and right connector from a single child */

    .items li:only-child::after,
    .items li:only-child::before {
        display: none;
    }

    .items li:only-child {
        padding-top: 0;
    }


    /* Remove "outer" connector */

    .items li:first-child::before,
    .items li:last-child::after {
        border: 0 none;
    }


    /* Add back the down connector for last node */

    .items li:last-child::before {
        border-right: 1px solid blue;
        border-radius: 0 5px 0 0;
    }


    /* Add curve line to the first child's connector */

    .items li:first-child::after {
        border-radius: 5px 0 0 0;
    }


    /* Add down connector from parent */

    .items ul ul::before {
        content: '';
        border-left: 1px solid magenta;
        z-index: 0;
        height: 20px;
        position: absolute;
        top: 0px;
        /* Changed */
        left: 50%;
        width: 0;
    }


    /* Add cosmetic for each item */

    .items li a {
        font-size: 12px;
        background-color: white;
        border: 1px solid #ccc;
        padding: 5px 10px;
        text-decoration: none;
        display: inline-block;
        border-radius: 4px;
    }

    .items li a:hover {
        background-color: #EEE;
    }


    /* Experimental for multiple parents */


    /* Add margin for the parents */

    .items li a+a {
        position: relative;
        margin-bottom: 12px;
    }


    /* Implement also for the first parent */

    .items li a:first-child {
        position: relative;
        margin-bottom: 12px;
    }


    /* 
- Add "down" connector (vertical line) from each parent 
- Currently it will also select the single parent
*/

    .items li>a:not(:only-child)::after {
        content: '';
        position: absolute;
        border-right: 1px solid pink;
        top: 29px;
        height: 11px;
        width: 0;
        left: 50%;
        z-index: 0;
    }


    /* Starting to fvcked up from here */


    /* Making the horizontal connector line after each multiple parent */

    .items li>a:not(:only-of-type):not(:last-of-type)::before {
        content: '';
        position: absolute;
        top: 40px;
        left: auto;
        width: 100%;
        /* Changed */
        border-top: 1px solid indigo;
    }

    .items li>a:last-of-type:not(:first-child)::before {
        content: '';
        position: absolute;
        top: 40px;
        right: auto;
        width: 40%;
        /* Changed */
        border-top: 1px solid indigo;
    }


    /* ADDED STYLES */
    .items li>a:first-of-type:first-child::before {
        left: 50%;
    }

    .items li>a:last-of-type:last-child::before {
        left: 50%;
    }
</style>