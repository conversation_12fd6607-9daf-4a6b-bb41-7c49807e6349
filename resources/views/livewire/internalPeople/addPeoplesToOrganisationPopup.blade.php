<div x-show="individualPopup" class="fixed inset-0 flex items-center justify-center z-50" x-cloak>
    <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-50"></div>
    <div class="modal-content border addRoleWidth border-gray-300 border-solid bg-white shadow-lg rounded-lg p-4 z-50">
        <div class="flex justify-between">
            <h2 class="text-black-900 text-xl font-semibold">Select people</h2>
            <button type="button" @click="individualPopup = false" class="text-gray-500 hover:text-gray-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Select people box with search -->
        <div class="flex justify-center items-center mt-2" x-data="{ query: '', isFocused: false, firstFocus: false }">
            <div class="bg-white border selectPeople p-2 rounded-lg">
                <p class="text-sm text-gray-500 mt-2">Select all the people you would like in your organisation
                    chart</p>
                <div class="search-container flex items-center mt-2">
                    <img class="search-icon h-4 w-auto" src="{{ asset('images/MagnifyingGlass.svg') }}"
                        alt="Search Icon">
                    <input x-on:focus="isFocused = true; firstFocus = true;" x-on:blur="isFocused = false"
                        type="text" x-model="query"
                        class="w-full bordermainBlue outline-none block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-500 sm:text-md sm:leading-6"
                        placeholder="Search">
                        <button type="button"
                        @click="search()"
                        class="bg-mainBlue text-white px-4 py-2 rounded-r-md flex items-center justify-center focus:outline-none hover:bg-blue-700"
                        >
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="searchBox flex justify-center flex-col items-center" x-show="!firstFocus">
                    <img class="" src="{{ asset('images/Illustration.png') }}" alt="Search Icon">
                    <p class="text-black text-base font-semibold">Start entering individuals’ names</p>
                </div>

                <!-- User list with checkboxes, filtered by search query -->
                <ul class="mt-4 adddeduser space-y-6 border py-4"
                    x-show="(isFocused && query.length > 1) || firstFocus" @scroll="handleScroll($event)">
                    <template x-for="user in filteredUsers()" :key="user.id">
                        <li class="flex justify-between {
">
                            <div class="flex items-center pl-1">
                                <div class="grid grid-cols-8 gap-2 items-center">
                                    <div class="border col-span-1 rounded-full w-10 h-10 text-white flex justify-center items-center text-center bg-cyan-500"
                                        x-text="`${user.forename.charAt(0).toUpperCase()}${user.surname.charAt(0).toUpperCase()}`">
                                    </div>
                                    <div class="space-y-1 col-span-7 flex-grow">
                                        <span class="text-sm font-semibold block"
                                            x-text="`${user.forename} ${user.surname}`"></span>
                                        <span class="text-xs text-gray-500 block"
                                            x-text="user.latest_role ? (user.employee_id ? `${user.latest_role} (${user.employee_id})` : user.latest_role) : ''"></span>
                                    </div>
                                </div>
                            </div>
                            <input type="checkbox" :value="user.id" @change="toggleSelection(user)"
                                :checked="isSelected(user.id)"
                                :disabled="isUserInOrganisation(user.id)" 
                                class="form-checkbox h-4 w-4 mr-4 checkbx transition duration-150 ease-in-out">
                        </li>
                    </template>
                </ul>

                <div class="flex justify-end mt-4">
                    <button @click="individualPopup = false" type="button"
                        class="p-2 bg-cyan-500 text-white rounded-lg">Continue</button>
                </div>
            </div>
        </div>
    </div>
</div>
