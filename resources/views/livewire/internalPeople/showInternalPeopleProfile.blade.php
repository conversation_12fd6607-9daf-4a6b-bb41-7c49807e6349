<div x-data="skillsComponent(selectedItem)">
    <div x-show="isPopupVisible" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40"
        style="display:none">
        <!-- Modal background with a higher z-index -->
        <div class="modal-background blur-sm fixed inset-0 bg-slate-400 opacity-50 z-40"></div>
        <!-- Modal content with a lower z-index -->
        <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50"
            x-show="isPopupVisible" x-transition>
            <!-- Modal content -->
            <div class="flex flex-col h-full">
                <div class="flex justify-between border-b px-2 py-2">
                    <h3 class="text-base font-bold mb-3"><span x-text="selectedItem.company_name"></span></h3>
                    <img @click="isPopupVisible = false;" class="h-5 w-5 cursor-pointer"
                        src="{{ asset('images/cross.svg') }}" alt="">
                </div>
                <div class="grid grid-cols-2 gap-x-2 pt-3 flex-1">
                    <div class="flex flex-1 flex-col justify-between">
                        <div class="flex px-2 py-2 justify-between">
                            <div class="flex flex-col gap-y-2">
                                <h3 class="text-base font-bold" x-text="`${selectedItem.forename} ${selectedItem.surname}`"></h3>
                                </h3>
                                <h3 class="text-base text-gray-700"><span x-text="selectedItem.latest_role"></span></h3>
                            </div>
                            <div class="flex gap-x-2 items-center justify-center">
                                <!-- Update Button to Trigger the Skills Popup -->
                                <div @click=" updateSkills(selectedItem); updateInternalpeoplePopup = true;"
                                    class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg hover:bg-gray-100 transition"
                                    role="button"
                                    aria-label="Update Skills">
                                    <img class="h-4 w-4 mr-2" src="{{ asset('images/editicon.svg') }}" alt="Edit Icon">
                                    <h2 class="text-sm font-semibold">Update</h2>
                                </div>

                                <!-- Link to LinkedIn Profile if available -->
                                <a id="linkedin"
                                    class="flex items-center justify-center hover:scale-105"
                                    :href="selectedItem.linkedinURL && selectedItem.linkedinURL !== 'NA' ? selectedItem.linkedinURL : '#'"
                                    x-show="selectedItem.linkedinURL && selectedItem.linkedinURL !== 'NA'"
                                    target="_blank">
                                    <img class="h-6 w-6" src="{{ asset('images/linkedinlogo.png') }}" alt="LinkedIn">
                                </a>

                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-2 grid-rows-4 flex-grow">
                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Function</h3>
                                <h3 class="text-xs text-gray-700">
                                    <span x-text="selectedItem.function ? selectedItem.function : 'Not Applicable'"></span>
                                </h3>
                            </div>
                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Division</h3>
                                <h3 class="text-xs text-gray-700">
                                    <span x-text="selectedItem.division ? selectedItem.division : 'Not Applicable'"></span>
                                </h3>
                            </div>
                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Tenure in Role</h3>
                                <h3 class="text-xs text-gray-700">
                                    <span x-text="selectedItem.tenure !== 0 ? selectedItem.tenure : 'Less than a year'"></span>
                                </h3>
                            </div>

                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Readiness</h3>

                                <h3 class="text-xs text-gray-700">
                                    <div
                                        :style="{
                                        backgroundColor: selectedItem.readiness === 'Ready' 
                                                        ? '#008000'  // Green for Ready
                                                        : selectedItem.readiness === 'Not Ready' 
                                                        ? '#ff0000'  // Amber for Not Applicable
                                                        : '#FFBF00', // Red for default
                                        height: '35px', 
                                        width: '70px',
                                        borderRadius: '10px'
                                        }">
                                    </div>
                                </h3>
                            </div>

                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Tenure in Company</h3>
                                <h3 class="text-xs text-gray-700">
                                    <span x-text="selectedItem.tenure_in_company !== 0 ? selectedItem.tenure_in_company : 'Less than a year'"></span>
                                </h3>
                            </div>
                            <div class="border flex flex-col justify-between rounded-xl text-start py-4 px-2">
                                <h3 class="text-sm font-bold chart-heading">Registrations</h3>
                                <h3 class="text-xs text-gray-700">
                                    <span x-text="selectedItem.other_tags ? selectedItem.other_tags : 'Not Applicable'"></span>
                                </h3>
                            </div>
                        </div>

                    </div>

                    <div x-data="{ tab: 'summary', nopen: @entangle('nopen'), nineboxGridOpen: @entangle('nineboxGridOpen') }" class="flex flex-col pt-3 gap-y-2">
                        <div class="flex border-b-2">
                            <!-- Summary Tab -->
                            <button id="summaryButton" @click="tab = 'summary'" class="w-full text-sm py-3 font-medium"
                                :class="{'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab === 'summary', 'chart-heading font-semibold': tab !== 'summary'}">
                                Summary
                            </button>

                            <!-- Career History Tab -->
                            <button @click="tab = 'career_history'"
                                class="w-full text-sm py-3 font-medium"
                                :class="{'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab === 'career_history', 'chart-heading font-semibold': tab !== 'career_history'}">
                                Career History
                            </button>

                            <!-- Skills Tab -->
                            <button @click="tab = 'skills'"
                                class="w-full text-sm py-3 font-medium"
                                :class="{'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab === 'skills', 'chart-heading font-semibold': tab !== 'skills'}">
                                Skills
                            </button>

                            <!-- Notes Tab -->
                            <button @click="tab = 'notes'"
                                class="w-full text-sm py-3 font-medium"
                                :class="{'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab === 'notes', 'chart-heading font-semibold': tab !== 'notes'}">
                                Notes
                            </button>
                            <button @click="tab = 'plans'"
                                class="w-full text-sm py-3 font-medium"
                                :class="{'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab === 'plans', 'chart-heading font-semibold': tab !== 'plans'}">
                                Plans
                            </button>
                            <button x-show="canShowAssessmentButton()" @click="tab = 'assessmentCriteria'" class="w-full text-sm py-3  font-medium"
                                :class="{
                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                            'assessmentCriteria',
                                        'chart-heading font-semibold': tab != 'assessmentCriteria'
                                    }">
                                Assessment Criteria
                            </button>
                            <button x-show="canShowAssessmentButton()" @click="tab = '9boxGrid'" class="w-full text-sm py-3  font-medium"
                                :class="{
                                        'text-cyan-500 font-semibold border-b-2 border-b-cyan-500': tab ==
                                            '9boxGrid',
                                        'chart-heading font-semibold': tab != '9boxGrid'
                                    }">
                                9boxGrid
                            </button>
                        </div>
                        <div x-show="tab === 'summary'"
                            class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Summary</h4>

                            <p class="text-sm" x-text="selectedItem.summary ? selectedItem.summary : 'Not Applicable'">
                            </p>
                        </div>
                        <div x-show="tab == 'career_history'"
                            class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Career History</h4>

                            <template x-if="selectedItem.career_history && selectedItem.career_history.length > 0">
                                <div class="flex flex-col items-start w-full">
                                    <template x-for="(careerHistory, index) in selectedItem.career_history" :key="index">
                                        <div class="flex h-max items-start justify-center mb-1">
                                            <div class="flex flex-col mt-1 items-center justify-center h-full w-2">
                                                <div class="rounded-full blueBalls bg-mainBlue"></div>
                                                <div class="flex-grow border-l border-mainBlue"></div>
                                            </div>
                                            <div class="flex flex-col items-start justify-start pl-4">
                                                <h4 class="text-sm font-semibold text-gray-900" x-text="careerHistory.role"></h4>
                                                <div class="flex gap-x-2">
                                                    <span class="text-sm font-base text-gray-700"
                                                        x-text="careerHistory.company ? careerHistory.company.name : 'N/A'">
                                                    </span>
                                                </div>
                                                <div class="flex gap-x-2 mb-4">
                                                    <span class="text-xs font-base GrayText">
                                                        <span x-text="formatDate(careerHistory.start_date)"></span> -
                                                        <span x-text="careerHistory.end_date ? formatDate(careerHistory.end_date) : 'Present'"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </template>

                            <template x-if="!selectedItem.career_history || selectedItem.career_history.length === 0">
                                <p class="text-sm text-gray-500 mt-5">No career history available</p>
                            </template>
                        </div>
                        <div x-show="tab == 'skills'"
                            class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Skills</h4>

                            <template x-for="(skillsArr, skillType) in selectedItem.internal_skills" :key="skillType">
                                <div>
                                    <h4 class="text-sm text-gray-700 font-medium chart-heading" x-text="`${skillType} Skills`"></h4>
                                    <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                        <template x-for="(skill, index) in skillsArr" :key="index">
                                            <div class="flex items-center text-xs p-2 rounded-xl skill-container chart-heading">
                                                <span x-text="skill.skill_name"></span>

                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </div>

                        <div x-show="tab == 'notes'"
                            class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                            <div class="flex justify-between">
                                <h4 class="text-lg text-gray-700 font-semibold chart-heading pt-1">Notes</h4>
                                <button @click="nopen = !nopen" type="button"
                                    class="flex justify-center items-center border p-2 rounded-md text-sm">
                                    <img class="h-4 w-4" src="{{ asset('images/Plus.svg') }}">
                                    <span class="block text-black pl-1" x-text="nopen ? 'Cancel' : 'Add Notes'"></span>
                                </button>
                            </div>
                            <div class="mt-2" x-show="nopen" x-transition>
                                <textarea wire:model="filteredPnotes" rows="4"
                                    class="bg-gray-50 p-2 text-sm font-normal border border-gray-300 text-gray-900 form-input block w-full rounded-md hover:text-cyan-500"
                                    placeholder="Enter your note here"></textarea>
                                <div class="flex gap-2 mt-2">

                                    <button wire:click.prevent="addNotes(selectedItem.id)"
                                        type="button"
                                        class="flex justify-center items-center border p-2 rounded-md text-sm bg-mainBlue text-white">
                                        <span class="block pl-1">Save</span>
                                    </button>
                                </div>
                            </div>

                            <template x-if="selectedItem.notes?.length > 0">
                                <div>
                                    <template x-for="note in selectedItem.notes" :key="note.id">
                                        <div class="mt-3">
                                            <div class="flex justify-between">
                                                <h3 class="text-xl md:text-base font-medium text-black" x-text="note.user.name"></h3>
                                                <div class="flex justify-between">
                                                    <span class="text-sm text-gray-500 font-normal" x-text="formatDate(note.created_at, true)"></span>
                                                </div>
                                            </div>
                                            <p class="text-sm text-gray-600 font-normal" x-text="note.Notes"></p>
                                        </div>
                                    </template>
                                </div>
                            </template>
                            <div x-show="!nopen && selectedItem.notes?.length === 0" class="mt-2">
                                <p class="text-sm">No notes available</p>
                            </div>
                        </div>
                        <div x-show="tab == 'plans'"
                            class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                            <h4 class="text-lg text-gray-700 font-semibold chart-heading">Plans</h4>

                            <template x-if="selectedItem.internalUserPlans?.length > 0">
                                <div>
                                    <div class="flex py-2 flex-wrap gap-x-4 gap-y-2">
                                        <template x-for="plan in selectedItem.internalUserPlans" :key="plan.id">
                                            <div class="flex items-center text-xs p-2 rounded-xl skill-container chart-heading">
                                                <span x-text="plan.name"></span>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>

                            <template x-if="selectedItem.internalUserPlans?.length <= 0">
                                <div class="py-2 text-gray-500">
                                    No plans available
                                </div>
                            </template>
                        </div>
                        <div x-data="{
                            viewNotesPopup: false,
                            selectedNotes: '',
                            fetchNotes(criteriaId) {
                                let criteria = this.criterias.find(c => c.criteria_id === criteriaId);

                                if (criteria && criteria.notes) {
                                    this.selectedNotes = criteria.notes;
                                    this.viewNotesPopup = true;
                                } else {
                                    this.selectedNotes = 'No notes available';
                                    this.viewNotesPopup = true;
                                }
                            },
                        }">
                            <div x-show="tab == 'assessmentCriteria' && canShowAssessmentButton()" class="h-96 overflow-x-auto flex flex-col border rounded-xl gap-y-2 text-start p-2">
                                <div x-show="!nopen">
                                    <!-- Display Assessment Criteria Section -->
                                    <div x-show="criterias.length > 0" class="flex justify-between">
                                        <h4 class="text-sm font-bold chart-heading">Assessment Criteria</h4>
                                        <button @click="nopen = !nopen" type="button" class="flex gap-2 justify-center items-center border p-2 rounded-md text-sm font-bold">
                                            <img class="h-4 w-4" src="{{ asset('images/editicon.svg') }}">
                                            Edit
                                        </button>
                                    </div>

                                    <!-- Criteria List -->
                                    <template x-if="criterias.length > 0">
                                        <div class="creteriaList">
                                            <template x-for="(criteria, index) in criterias" :key="criteria.criteria_id">
                                                <div class="mt-3">
                                                    <div class="flex justify-between padding20 border-b">
                                                        <h3 class="text-lg md:text-base font-medium text-black" x-text="criteria.label"></h3>
                                                        <div class="flex items-center">
                                                            <!-- Yes/No Badge -->
                                                            <template x-if="criteria.isChecked === 'Yes'">
                                                                <span class="text-xs py-2 px-3 rounded-lg font-normal redlinessReaddy ml-1">Yes</span>
                                                            </template>
                                                            <template x-if="criteria.isChecked !== 'Yes'">
                                                                <span class="text-xs py-2 px-3 rounded-lg font-normal ml-1 RedBG text-red-500">No</span>
                                                            </template>

                                                            <!-- View Notes Link -->
                                                            <div @click="fetchNotes(criteria.criteria_id)" class="flex justify-between cursor-pointer ml-4">
                                                                <span class="text-sm text-cyan-500 font-bold">View Notes</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </template>

                                    <!-- Fallback for No Criteria -->
                                    <div x-show="criterias.length === 0" class="mt-2">
                                        <p class="text-sm">No Assessment Criteria Available</p>
                                    </div>
                                </div>

                                <!-- Edit Mode -->
                                <div x-show="nopen">
                                    <form wire:submit.prevent="editIndividualCriteria">
                                        <div class="flex justify-between items-center">
                                            <h4 class="text-sm font-bold chart-heading">Edit Assessment Criteria</h4>
                                            <div class="flex gap-3">
                                                <button @click="nopen = false;closeIndividualCriteriaPopup(selectedItem.id)" type="button" class="flex gap-2 justify-center items-center border p-2 rounded-md text-sm font-bold">
                                                    Cancel
                                                </button>
                                                <button @click="editIndividualCriteria(criterias, selectedItem.id)" type="button" class="text-white flex justify-center gap-5 items-center bg-mainBlue p-2 rounded-md">
                                                    <span class="block font-semibold text-sm">Save</span>
                                                </button>
                                            </div>
                                        </div>

                                        <template x-if="criterias.length > 0">
                                            <div class="editAssessment">
                                                <template x-for="(criteria, index) in criterias" :key="criteria.criteria_id">
                                                    <div class="mt-3 innerEditAssessment">
                                                        <!-- Criteria Label -->
                                                        <h3 class="text-lg md:text-base font-medium text-black mb-2" x-text="criteria.label"></h3>

                                                        <div class="flex justify-between padding20 gap-3 border-b">
                                                            <!-- Checkbox for Yes/No -->
                                                            <div class="switch-toggle">
                                                                <input type="checkbox"
                                                                    :checked="criteria.isChecked === 'Yes'"
                                                                    @change="criteria.isChecked = $event.target.checked ? 'Yes' : 'No'"
                                                                    class="switch-toggle-checkbox" />
                                                                <label class="switch-toggle-label">
                                                                    <span>No</span>
                                                                    <span>Yes</span>
                                                                </label>
                                                            </div>

                                                            <!-- Textarea for Notes -->
                                                            <textarea x-model="criteria.notes" placeholder="Enter Notes"
                                                                class="placeholder:text-gray-400 block w-full p-2 outline-none border border-gray-300 rounded-md resize-y"
                                                                rows="1"></textarea>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>

                                    </form>
                                </div>
                            </div>

                            <!-- Include Notes Popup -->
                            @include('livewire.internalPeople.viewNotesPopup')
                        </div>
                        <div x-show="canShowAssessmentButton() && tab == '9boxGrid'" class="h-96 border rounded-xl p-2">
                        <div x-show="!nineboxGridOpen">

                                <div class="flex justify-between items-center">
                                    <div class="text-lg font-semibold">9 Box Grid</div>
                                    <div @click="nineboxGridOpen = !nineboxGridOpen"
                                        class="flex cursor-pointer px-2 h-8 py-2 border border-[#EAECF0] justify-center gap-x-1 items-center bg-white rounded-lg">
                                        <img class="h-4 w-4 mr-2" src="/images/editicon.svg" alt="">
                                        <h2 class="text-sm font-semibold">Update</h2>
                                    </div>
                                </div>
                                <div class="p-3 relative">

                                    <div class="flex gap-5 items-center">
                                        <img class="" src="/images/yAxis.svg" alt="">
                                        <!-- Grid container -->
                                        <template x-if="isPopupVisible && nineBoxGridData.length > 0">
                                            <div class="relative grid grid-cols-3 gap-3 w-full">
                                                <template x-for="(nineBox, index) in nineBoxGridData" :key="nineBox.criteria_id">
                                                    <div :class="(nineBox.response == 1) ? 'bg-cyan-500' : 'orangeBG'"
                                                        class="text-white boxSize text-center rounded"
                                                        x-text="nineBox.label">
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </div>
                                    {{-- <img class="mt-3" src="/images/xAxis.svg" alt="">
                                     --}}
                                     <div class="flex flex-col  items-center w-full mt-3 mx-3">
                                        <!-- Line with Arrow and Labels -->
                                        <div class="relative w-full flex gap-1 items-center">
                                            <!-- Left Label (LOW) -->
                                            <span class="text-xs text-gray-700">LOW</span>
                                    
                                            <!-- Line -->
                                            <img class="xaxiss" src="/images/xAxis.svg" alt="">
                                    
                                            <!-- Right Arrow Label (HIGH) -->
                                            <span class="text-xs  text-gray-700">HIGH</span>
                                          
                                        </div>
                                    
                                        <!-- Performance Label -->
                                        <span class="text-gray-600 mt-2 text-xs">Performance</span>
                                    </div>
                                    

                                </div>
                            </div>

                            <div x-show="nineboxGridOpen">

                                <div class="flex justify-between items-center">
                                    <div class="text-lg font-semibold">Update 9 Box Grid</div>
                                    <div class="flex gap-3">
                                        <button @click="nineboxGridOpen = false; closeNineBoxGridPopup(selectedItem.id)" type="button"
                                            class="flex gap-2 justify-center items-center border p-2 rounded-md text-sm font-bold">
                                            Cancel
                                        </button>
                                        <button @click="edit9BoxGrid(nineBoxGridData, selectedItem.id)" type="button"
                                            class="text-white flex justify-center gap-5 items-center bg-mainBlue p-2 rounded-md">
                                            <span class="block font-semibold text-sm">Save</span>
                                        </button>
                                    </div>
                                </div>

                                <div class="p-3 relative">
                                    <div class="flex gap-5 items-center">
                                        <img src="/images/yAxis.svg" alt="">
                                        <!-- Grid container -->
                                        <template x-if="isPopupVisible && nineBoxGridData.length > 0">
                                            <div class="relative grid grid-cols-3 gap-3 w-full"
                                                x-data="{ 
                                                selectedOption: null,
                                                init() {
                                                    // Set selectedOption based on response values
                                                    this.selectedOption = this.nineBoxGridData.findIndex(nineBox => nineBox.response == 1);
                                                },
                                                updateResponse(index) {
                                                    // Set all responses to 0
                                                    this.nineBoxGridData.forEach(nineBox => nineBox.response = 0);
                                                    // Set the clicked one to 1
                                                    this.nineBoxGridData[index].response = 1;
                                                    // Update the selectedOption
                                                    this.selectedOption = index;
                                                }
                                            }"
                                                x-init="init()">
                                                <!-- Iterate through nineBoxGridData -->
                                                <template x-for="(nineBox, index) in nineBoxGridData" :key="nineBox.criteria_id">
                                                    <label
                                                        class="relative text-black boxSize text-center rounded cursor-pointer"
                                                        :class="{ 'grayLightBorder': selectedOption != index, 'afterCheckBorder afterBG': selectedOption == index }">
                                                        <!-- Radio input -->
                                                        <input type="radio" name="selection" :value="index"
                                                            class="absolute Blue-radio top-1 right-2"
                                                            x-model="selectedOption"
                                                            @click="updateResponse(index)">

                                                        <!-- Display label -->
                                                        <span x-text="nineBox.label"></span>
                                                    </label>
                                                </template>
                                            </div>
                                        </template>



                                    </div>
                                    <img class="w-full mt-3" src="/images/xAxis.svg" alt="">
                                </div>


                            </div>
                        </div>


                    </div>

                </div>

            </div>
        </div>
    </div>

    <div x-show="updateInternalpeoplePopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" x-cloak>
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

        <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50 w-full relative">
            <!-- cross icon -->
            <img x-on:click="updateInternalpeoplePopup = false;closePopup(selectedItem)" class="absolute top-2 right-2 w-6 h-6 cursor-pointer"
                src="{{ asset('images/cross.svg') }}" alt="Close Icon">

            <h2 class="font-semibold px-4" x-text="`Edit ${selectedItem.forename} ${selectedItem.surname}`"></h2>
            <div class="w-full border-t mt-3 border-gray-200"></div>

            <div class="grid grid-cols-3 gap-4 mt-4">
                <div>
                    <label for="updateProfileFunction" class="text-base font-medium labelcolor">Function</label>
                    <input type="text" x-model="selectedItem.function" id="updateProfileFunction" placeholder="Function"
                        class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                </div>
                <div>
                    <label for="updateProfileDivision" class="text-base font-medium labelcolor">Division</label>
                    <input type="text" x-model="selectedItem.division" id="updateProfileDivision" placeholder="Division"
                        class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                </div>
                <div>
                    <label for="updateReadiness" class="text-base font-medium labelcolor">Readiness</label>
                    <fieldset>
                        <ul class="readiness mt-1">
                            <li class="ready">
                                <input type="radio" id="Ready" name="readiness" value="Ready"
                                    x-model="selectedItem.readiness" />
                                <label for="Ready"
                                    class="text-center font-semibold labelcolor"></label>
                            </li>
                            <li class="notReady">
                                <input type="radio" id="NotReady" name="readiness" value="Not Ready"
                                    x-model="selectedItem.readiness" />
                                <label for="NotReady" class="text-center font-semibold labelcolor"></label>
                            </li>
                            <li class="nearlyReady">
                                <input type="radio" id="NotApplicable" name="readiness" value="Not Applicable"
                                    x-model="selectedItem.readiness" />
                                <label for="NotApplicable" class="text-center font-semibold labelcolor"></label>
                            </li>
                        </ul>
                    </fieldset>
                </div>
            </div>

            <div x-data="skillsComponent">
                <div class="grid grid-cols-3 gap-4 mt-4">

                    <!-- Specialised Skills Section -->
                    <div>
                        <label for="Specialised" class="text-base font-medium labelcolor">Specialised</label>
                        <input type="text"
                            class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                            placeholder="Enter specialised skills"
                            x-model="newSpecialised"
                            @keydown.enter.prevent="addSkill('specialised', selectedItem)"
                            @blur="addSkill('specialised')">

                        <div class="mt-2">
                            <template x-for="(skill, index) in selectedItem.internal_skills?.Specialised" :key="index">
                                <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2">
                                    <span x-text="skill.skill_name"></span>
                                    <button type="button" class="ml-1" @click="removeSkill('specialised', index , selectedItem)">&times;</button>
                                </span>
                            </template>
                        </div>
                    </div>

                    <!-- Common Skills -->
                    <div>
                        <label for="Common" class="text-base font-medium labelcolor">Common</label>
                        <input type="text"
                            class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                            placeholder="Enter common skills"
                            x-model="newCommon"
                            @keydown.enter.prevent="addSkill('common', selectedItem)"
                            @blur="addSkill('common')">

                        <div class="mt-2">
                            <template x-for="(skill, index) in selectedItem.internal_skills?.Common" :key="index">
                                <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2">
                                    <span x-text="skill.skill_name"></span>
                                    <button type="button" class="ml-1" @click="removeSkill('common', index, selectedItem)">&times;</button>
                                </span>
                            </template>
                        </div>
                    </div>

                    <!-- Certifications -->
                    <div>
                        <label for="Certification" class="text-base font-medium labelcolor">Certification</label>
                        <input type="text"
                            class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                            placeholder="Enter certifications"
                            x-model="newCertification"
                            @keydown.enter.prevent="addSkill('certification', selectedItem)"
                            @blur="addSkill('certification')">

                        <div class="mt-2">
                            <template x-for="(cert, index) in selectedItem.internal_skills?.Certification" :key="index">
                                <span class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2">
                                    <span x-text="cert.skill_name"></span>
                                    <button type="button" class="ml-1" @click="removeSkill('certification', index, selectedItem)">&times;</button>
                                </span>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-3 gap-4 mt-4">
                <div>
                    <label for="updateProfileLinkedInUrl" class="text-base font-medium labelcolor">LinkedIn Url</label>
                    <input type="text" x-model="selectedItem.linkedinURL" id="updateProfileLinkedInUrl" placeholder="LinkedIn Url"
                        class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                </div>
                <div>
                    <label for="updateProfileOtherTags" class="text-base font-medium labelcolor">Registrations</label>
                    <input type="text" x-model="selectedItem.other_tags" id="updateProfileOtherTags" placeholder="Registrations"
                        class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                </div>

            </div>
            <div class="grid grid-cols-1 gap-4 mt-4">
                <!-- Summary Text Area Field -->
                <div>
                    <label for="summary" class="text-base font-medium labelcolor">Summary</label>
                    <textarea id="summary"
                        class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                        placeholder="Enter a brief summary"
                        rows="4" x-model="selectedItem.summary"></textarea>
                </div>
            </div>
            <div class="w-full border-t mt-3 border-gray-200"></div>
            <div class="mt-4 flex justify-start">
                <button @click="updateInternalPeopleData(selectedItem)" type="button" class="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    Update Profile
                </button>
                <button x-on:click=" editPeopleCareerHistoryPopup =true" type="button" class="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    Update Career History
                </button>




            </div>

        </div>
    </div>

    <div x-show="editPeopleCareerHistoryPopup" class="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-40" x-cloak>
        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

        <div class="pipeline-modal-content bg-white shadow-md rounded-lg border border-gray-300 p-4 z-50 w-full max-w-5xl relative">
            <!-- cross icon -->
            <img x-on:click="editPeopleCareerHistoryPopup = false; closePopup(selectedItem)" class="absolute top-2 right-2 w-6 h-6 cursor-pointer"
                src="{{ asset('images/cross.svg') }}" alt="Close Icon">

            <h2 class="font-semibold px-4" x-text="`Edit ${selectedItem.forename} ${selectedItem.surname} Career History`"></h2>
            <div class="w-full border-t mt-3 border-gray-200"></div>

            <!-- Scrollable content area with increased height -->
            <div class="overflow-y-auto max-h-96">
                <template x-for="(careerHistory, index) in selectedItem.career_history" :key="index">
                    <div class="grid grid-cols-4 gap-4 mt-4 items-start">
                        <div>
                            <label for="editRole" class="text-base font-medium labelcolor">Role</label>
                            <input x-model="careerHistory.role" @input="validateField('role', index)" type="text" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md" placeholder="Role">
                            <span class="text-red-500 text-sm" x-show="careerHistory.errors?.role">Role is required</span>
                        </div>
                        <div>
                            <label for="editCompany" class="text-base font-medium labelcolor">Company</label>
                            <input x-model="careerHistory.company.name" @input="validateField('company', index)" type="text" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                            <span class="text-red-500 text-sm" x-show="careerHistory.errors?.company">Company is required</span>
                        </div>
                        <div>
                            <label for="editStartDate" class="text-base font-medium labelcolor">Start Date</label>
                            <input x-model="careerHistory.start_date" @input="validateField('start_date', index)" type="date" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                            <span class="text-red-500 text-sm" x-show="careerHistory.errors?.start_date">Start date is required</span>
                        </div>
                        <div class="flex flex-wrap gap-4">
                            <div class="careerHistoryEndDate">
                                <label for="editEndDate" class="text-base font-medium labelcolor">End Date</label>
                                <input x-model="careerHistory.end_date" type="date" class="outline-none block w-full bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md">
                            </div>
                            <div class="flex justify-start items-center" style="position: relative;top: 9px;">
                                <button @click="removeCareerHistoryRow(selectedItem, index)" type="button" classs="text-red">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            <span class="text-red-500 text-sm"></span>

                            </div>
                        </div>
                        <!-- Delete Button -->

                    </div>
                </template>


                <!-- Add Button -->
                <div class="flex justify-start items-center mt-5 mb-3">
                    <button @click="addCareerHistoryRow(selectedItem)" type="button" class="text-green-600 py-3 px-4 rounded-lg bg-cyan-500 font-medium text-white hover:text-green-800">
                        <i class="fas fa-plus-circle"></i> Add New Entry
                    </button>
                </div>
            </div>

            <div class="mt-4 flex justify-start">
                <button @click="validateAndSubmit(selectedItem)" type="button" class="mr-6 bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    Update Career History
                </button>
            </div>
        </div>
    </div>
</div>




<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('skillsComponent', () => ({
            newSpecialised: '',
            newCommon: '',
            newCertification: '',
            selectedItemData: {},
            previousItemData: {}, // Variable to store the previous state
            editPeopleCareerHistoryPopup: @entangle('editPeopleCareerHistoryPopup'),
            newSkillData: {
                specialised: [],
                common: [],
                certification: []
            },

            addSkill(skillType, selectedItem) {
                if (skillType === 'specialised' && this.newSpecialised !== '') {
                    selectedItem.internal_skills.Specialised.push({
                        skill_name: this.newSpecialised
                    });
                    this.newSpecialised = ''; // Clear input
                } else if (skillType === 'common' && this.newCommon !== '') {
                    selectedItem.internal_skills.Common.push({
                        skill_name: this.newCommon
                    }); // Corrected to newCommon
                    this.newCommon = ''; // Clear input
                } else if (skillType === 'certification' && this.newCertification !== '') {
                    selectedItem.internal_skills.Certification.push({
                        skill_name: this.newCertification
                    }); // Corrected to newCertification
                    this.newCertification = ''; // Clear input
                }
            },

            removeSkill(skillType, index, selectedItem) {
                if (skillType === 'specialised') {
                    selectedItem.internal_skills.Specialised.splice(index, 1);
                } else if (skillType === 'common') {
                    selectedItem.internal_skills.Common.splice(index, 1);
                } else if (skillType === 'certification') {
                    selectedItem.internal_skills.Certification.splice(index, 1);
                }
            },

            updateSkills(selectedItem) {
                // Store the current state as previous before updating
                this.previousItemData = JSON.parse(JSON.stringify(selectedItem)); // Deep copy
                this.selectedItemData = selectedItem;
            },

            closePopup(selectedItem) {
                // Restore the previous state
                Object.assign(selectedItem, this.previousItemData);
            },

            updateInternalPeopleData(selectedItem) {
                let skills = {
                    specialised: selectedItem.internal_skills.Specialised.map(skill => skill.skill_name),
                    common: selectedItem.internal_skills.Common.map(skill => skill.skill_name),
                    certification: selectedItem.internal_skills.Certification.map(skill => skill.skill_name),
                };
                const data = {
                    function: selectedItem.function,
                    division: selectedItem.division,
                    other_tags: selectedItem.other_tags,
                    readiness: selectedItem.readiness,
                    id: selectedItem.id,
                    newSkillData: skills,
                    summary: selectedItem.summary,
                    linkedinURL:selectedItem.linkedinURL
                };

                Livewire.dispatch("updateInternalPeople", {
                    data: data
                });
            },
            addCareerHistoryRow(selectedItem) {
                selectedItem.career_history.push({
                    role: '',
                    company: {
                        name: ''
                    },
                    start_date: '',
                    end_date: ''
                });
            },
            removeCareerHistoryRow(selectedItem, index) {
                selectedItem.career_history.splice(index, 1);
            },
            validateField(field, index) {
                const careerHistory = this.selectedItem.career_history[index];

                // Handle the special case for 'company' which is an object with 'name'
                if (field === 'company') {
                    if (!careerHistory.company || !careerHistory.company.name) {
                        careerHistory.errors = careerHistory.errors || {};
                        careerHistory.errors['company'] = true;
                    } else {
                        if (careerHistory.errors) delete careerHistory.errors['company'];
                    }
                } else {
                    // General case for other fields (role, start_date, etc.)
                    if (!careerHistory[field]) {
                        careerHistory.errors = careerHistory.errors || {};
                        careerHistory.errors[field] = true;
                    } else {
                        if (careerHistory.errors) delete careerHistory.errors[field];
                    }
                }
            },

            validateAndSubmit(selectedItem) {
                let isValid = true;
                selectedItem.career_history.forEach((careerHistory, index) => {
                    // Validate role
                    if (!careerHistory.role) {
                        this.validateField('role', index);
                        isValid = false;
                    }
                    // Validate company
                    if (!careerHistory.company.name) {
                        this.validateField('company', index);
                        isValid = false;
                    }
                    // Validate start date
                    if (!careerHistory.start_date) {
                        this.validateField('start_date', index);
                        isValid = false;
                    }
                });

                if (isValid) {
                    this.updateCareerHistory(selectedItem);
                }
            },
            updateCareerHistory(selectedItem) {
                const data = {
                    careerHistory: selectedItem.career_history,
                    id: selectedItem.id,
                };
                Livewire.dispatch("updateCareerHistory", {
                    data: data
                });

            }
        }));
    });

    function formatDate(date, format = false) {
        if (!date) return '';
        let options = {
            year: 'numeric',
            month: 'short'
        };

        if (format) {
            options = {
                year: 'numeric',
                month: '2-digit', // Ensure month is in two digits (e.g., 01 for January)
                day: '2-digit' // Ensure day is in two digits (e.g., 01 for 1st)
            }
        };
        return new Date(date).toLocaleDateString('en-US', options);
    }
</script>