@extends('layouts.builderapp')

@section('content1')
    <div class="container">
        <ul class="mt-3 space-y-2">
            @foreach ($topics as $topic)
                <li>
                    <div class="w-full text-gray-900 font-semibold hover:border-b-2 border-red-700 hover:text-red-700">
                        <a class="block truncate" href="{{ route('builder.show', ['builder' => $topic]) }}">{{ $topic }}</a>
                    </div>
                </li>
            @endforeach
        </ul>
    </div>
@endsection

@section('content2')
    <div class="container">
        @if ($chatHistories->count() > 0)
            <ul class="space-y-3">
                @foreach ($chatHistories as $chatHistory)
                    <li>
                        <p class="text-gray-900 font-bold">{{ $chatHistory->question }}<p>
                        @if ($chatHistory->response)
                            <p class="text-right text-red-700">{{ $chatHistory->response }}</p>
                        @else
                            <em>No answer yet.</em>
                        @endif
                    </li>
                @endforeach
            </ul>
        @else
            <p>No chat history available for this topic.</p>
        @endif
    </div>
@endsection

@section('content3')
@if (!empty($sqlResult))
    <table class="table-auto w-full mt-4">
        <thead>
            <tr>
                @foreach(array_keys(json_decode(json_encode($sqlResult[0]), true)) as $key)
                <th class="text-sm border px-4 py-2">{{ $key }}</th>
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach ($sqlResult as $row)
                <tr>
                    @foreach ($row as $value)
                        <td class="text-sm border px-4 py-2">{{ $value }}</td>
                    @endforeach
                </tr>
            @endforeach
        </tbody>
    </table>
@endif
@endsection