<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'SuccessionPlanAI') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles

    <!-- Performance Optimization Styles -->
    <style>
        /* Critical CSS for loading states */
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Skeleton loading animations */
        .skeleton-pulse {
            animation: skeleton-pulse 1.5s ease-in-out infinite;
        }
        
        @keyframes skeleton-pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.4; }
        }
        
        /* Virtual scrolling container */
        .virtual-scroll-container {
            overflow-y: auto;
            overflow-x: hidden;
            height: 400px;
            contain: strict;
        }
        
        .virtual-scroll-item {
            contain: layout style paint;
        }
        
        /* Tag system optimizations */
        .tag-system-wrapper {
            contain: layout style;
        }
        
        .filter-tag {
            will-change: transform;
            contain: layout style paint;
        }
        
        /* Modal optimizations */
        .modal-backdrop {
            backdrop-filter: blur(4px);
            contain: layout style paint;
        }
        
        /* Chart container optimizations */
        .chart-container {
            contain: layout style;
            will-change: contents;
        }
        
        /* Performance hints */
        .performance-container {
            contain: layout style paint;
        }
        
        /* Intersection observer targets */
        .lazy-load-target {
            min-height: 200px;
        }
    </style>
</head>

<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <!-- Page Content -->
        <main>
            {{ $slot }}
        </main>
    </div>

    <!-- JavaScript Dependencies (in order of priority) -->
    
    <!-- Core Libraries -->
    <script src="{{ asset('js/alpine.js') }}" defer></script>
    
    <!-- Chart Libraries -->
    <script src="{{ asset('js/chart.umd.min.js') }}"></script>
    <script src="{{ asset('js/apexcharts.js') }}"></script>
    
    <!-- Performance Optimization Components -->
    <script src="{{ asset('js/components/VirtualTagSystem.js') }}"></script>
    <script src="{{ asset('js/components/ModalManager.js') }}"></script>
    <script src="{{ asset('js/components/NavigationStateManager.js') }}"></script>
    <script src="{{ asset('js/components/VirtualTable.js') }}"></script>
    <script src="{{ asset('js/components/LazyCharts.js') }}"></script>
    
    <!-- Application Initialization -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize performance monitoring
            if ('PerformanceObserver' in window) {
                const observer = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach((entry) => {
                        if (entry.entryType === 'largest-contentful-paint') {
                            console.debug('LCP:', entry.startTime);
                        }
                        if (entry.entryType === 'first-input') {
                            console.debug('FID:', entry.processingStart - entry.startTime);
                        }
                        if (entry.entryType === 'layout-shift') {
                            console.debug('CLS:', entry.value);
                        }
                    });
                });
                
                observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
            }
            
            // Initialize navigation state management
            if (window.navigationStateManager) {
                window.navigationStateManager.setCurrentPage(window.location.pathname);
            }
            
            // Preload critical resources
            const preloadLinks = [
                '{{ route("plan.index") }}',
                '{{ asset("images/ArrowLeftBlue.svg") }}',
                '{{ asset("images/peoplesearchIcon.svg") }}'
            ];
            
            preloadLinks.forEach(href => {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = href;
                document.head.appendChild(link);
            });
            
            // Setup intersection observer for lazy loading
            if ('IntersectionObserver' in window) {
                const lazyImageObserver = new IntersectionObserver((entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.removeAttribute('data-src');
                                lazyImageObserver.unobserve(img);
                            }
                        }
                    });
                });
                
                document.querySelectorAll('img[data-src]').forEach((img) => {
                    lazyImageObserver.observe(img);
                });
            }
        });
        
        // Performance utilities
        window.performanceUtils = {
            measureAsync: async function(name, fn) {
                performance.mark(`${name}-start`);
                const result = await fn();
                performance.mark(`${name}-end`);
                performance.measure(name, `${name}-start`, `${name}-end`);
                return result;
            },
            
            debounce: function(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            },
            
            throttle: function(func, limit) {
                let inThrottle;
                return function() {
                    const args = arguments;
                    const context = this;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                }
            }
        };
        
        // Global error handling for performance components
        window.addEventListener('error', function(e) {
            if (e.filename && e.filename.includes('components/')) {
                console.warn('Performance component error:', e.message, e.filename);
                // Graceful degradation - could disable virtualization, etc.
            }
        });
        
        // Resource loading optimization
        window.addEventListener('load', function() {
            // Mark critical resources as loaded
            document.body.classList.add('resources-loaded');
            
            // Start non-critical resource loading
            requestIdleCallback(() => {
                // Load non-critical images
                document.querySelectorAll('img[data-lazy]').forEach(img => {
                    img.src = img.dataset.lazy;
                });
            });
        });
    </script>

    <!-- Livewire Scripts -->
    @livewireScripts
    
    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>