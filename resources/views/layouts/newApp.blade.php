<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
    <title>{{ env('APP_NAME') }}</title>

    <!-- Choices.js CSS -->
    <link rel="stylesheet" type="text/css" href="{{ asset('css/choices.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('css/toastr.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('css/select2.min.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="icon" href="{{ asset('images/favicon.ico') }}" type="image/x-icon">


    <link rel="stylesheet" type="text/css" href="{{ asset('css/customTailwind.css?v=1.2') }}">

    @stack('style')
</head>

<body class="bg-gray-100">
    <div class="w-screen h-screen flex " id="previous-dashboard">
        @if(!request()->is('plan/*/final'))
            <!-- sidebar started -->
            @include('layouts.sidebar')
            <!-- sidebar ended -->
        @endif

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
            @if(!request()->is('plan/*/final'))
                <!-- Navbar -->
                @include('layouts.navbar')
            @endif

            @yield('contentTitle')

            <!-- Content -->
            <div class="flex-1 h-full" id="dashboard-container">
                <!-- content started -->
                @include('components.error-message')
                @yield('content')
                @livewire('job-notifier')
                <!-- content ended -->
            </div>
        </div>
    </div>

    <script src="{{asset('js/popper.min.js')}}"></script>
    <script src="{{asset('js/tippy-bundle.umd.min.js')}}"></script>
    <script src="{{asset('js/apexcharts.js')}}"></script>
    <script src="{{asset('js/chart.umd.min.js')}}"></script>
    <script src="{{asset('js/chartjs-plugin-datalabels.min.js')}}"></script>
    <script src="{{asset('js/sweetalert2.js')}}"></script>
    <script src="{{asset('js/html2canvas.min.js')}}"></script>
    <script src="{{asset('js/jspdf.umd.min.js')}}"></script>

    <script>
        function toggleDropdown() {
            const dropdownContent = document.getElementById('dropdownContent');
            dropdownContent.classList.toggle('hidden');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdownButton = document.getElementById('dropdownButton');
            const dropdownContent = document.getElementById('dropdownContent');
            // if (!dropdownButton?.contains(event.target) && !dropdownContent?.contains(event.target)) {
            //     dropdownContent?.classList.add('hidden');
            // }
        });

        window.addEventListener('beforeunload', function () {
            navigator.sendBeacon("{route('logout')}"); // Trigger server-side logout
        });
    </script>

    <!-- Choices.js JS -->

    <script src="{{asset('js/choices.min.js')}}"></script>

    <script src="{{asset('js/jquery.min.js')}}"></script>
    <script src="{{asset('js/alpine.js')}}" integrity="sha384-JWxW8nATMNzFMp/fvBYSuPLmx/x0HfepYyp11MQeEL9iabCCcikqAqCO6gWdFEn9" crossorigin="anonymous"></script>
    <script src="{{asset('js/toastr.min.js')}}"></script>
    <script src="{{asset('js/validate.min.js')}}"></script>
    <script src="{{asset('js/select2.min.js')}}"></script>



    <script src="{{asset('js/common.js')}}"></script>

    @stack('script')

    @livewireScripts
</body>

</html>