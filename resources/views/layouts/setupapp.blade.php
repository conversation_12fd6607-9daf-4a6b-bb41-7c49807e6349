<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <title>Succession</title>
</head>

<body class="bg-gray-50">
    <header>
    <nav class="bg-white border-b border-gray-300 shadow-sm">
        <div class="px-2 sm:px-6 lg:px-8">
            <div class="relative flex h-16 items-center justify-between">
                <div class="flex flex-1 items-center justify-center sm:items-stretch sm:justify-start">
                    <div class="flex flex-shrink-0 items-center">
                        <img class="h-10 w-auto" src="{{ asset('images/LogoSmall.png') }}" alt="Your Company">
                    </div>
                    <div class="hidden sm:ml-10 sm:block">
                    <div class="flex space-x-8">
                        <a id="HomePage" href="{{ route('home.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm" >
                            <img id="gif-element-home" class="h-8 w-auto" src="{{ asset('images/home.png') }}">
                        </a>
                        <a id="Planner" href="{{ route('plan.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm" >
                            <img id="gif-element-plan" class="h-8 w-auto" src="{{ asset('images/diagram.png') }}">
                        </a>
                        <a id="MyRec" href="{{ route('job.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm" >
                            <img id="gif-element-Rec" class="h-8 w-auto" src="{{ asset('images/recruitment-png.png') }}">
                        </a>
                        <a id="MyIn" href="{{ route('company.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm" >
                            <img id="gif-element-In" class="h-8 w-auto" src="{{ asset('images/stats.png') }}">
                        </a>
                        <a id="MyOrg" href="{{ route('internalpeople.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm" >
                            <img id="gif-element-org" class="h-8 w-auto" src="{{ asset('images/building.png') }}">
                        </a>
                        <a id="MyAdmin" href="{{ route('team.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm" >
                            <img id="gif-element-admin" class="h-8 w-auto" src="{{ asset('images/id-card.png') }}">
                        </a>
                        <a id="TSPData" href="{{ route('datacenter.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm" >
                            <img id="gif-element-Da" class="h-8 w-auto" src="{{ asset('images/brain.png') }}">
                        </a>
                        <a id="Master" href="{{ route('users.create') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm" >
                            <img id="gif-element-Ma" class="h-8 w-auto" src="{{ asset('images/settings.png') }}">
                        </a>
                    </div>
                </div>
            </div>

            <div>
                <a href="{{ route('builder.index') }}" class="text-xl font-semibold text-black px-3 py-2 text-sm font-medium">
                    <img id="gif-element-SAM" class="h-14 w-auto" src="{{ asset('images/SAM.png') }}">
                </a>
            </div>
            <div class="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
                <div class="relative ml-3">
                    <!-- Dropdown Button -->
                    <button id="dropdownButton" class="flex rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800" onclick="toggleDropdown()">
                        @if($user->profile_pic === 'not provided')
                        <p>#</p>
                        @else
                        <img class="h-8 w-8 rounded-full" src="{{ asset('images/03091C3B-2335-4FBC-B520-AAE89951D1E8_1_201_a.jpeg') }}" alt="">
                        @endif
                    </button>

                    <!-- Dropdown Content -->
                    <div id="dropdownContent" class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <a class="block px-4 py-2 text-sm text-gray-700" href="{{ route('users.index') }}">Your Profile</a>
                        <a class="block px-4 py-2 text-sm text-gray-700" href="#">Sign Out</a>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </nav>

    <main>
        @yield('content')
    </main>


<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css" />
<script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>


@livewireScripts
</body>

</html>

