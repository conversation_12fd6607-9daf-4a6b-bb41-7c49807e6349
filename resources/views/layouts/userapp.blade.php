<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @vite('resources/css/app.css')
    <title>Succession</title>
</head>

<body class="bg-slate-50">
    <header>
    <nav class="bg-white border-b border-gray-300 shadow-sm">
        <div class="px-2 sm:px-6 lg:px-8">
            <div class="relative flex h-16 items-center justify-between">
                <div class="flex flex-1 items-center justify-center sm:items-stretch sm:justify-start">
                    <div class="flex flex-shrink-0 items-center">
                        <img class="h-10 w-auto" src="{{ asset('images/Black logo - no background.png') }}" alt="Your Company">
                    </div>
                <div class="hidden sm:ml-6 sm:block">
                    <div class="flex space-x-8">
                        <a href="{{ route('home.index') }}" class="text-slate-900 rounded-md px-3 py-2 text-sm">
                            <div id="HomePage" class="flex flex-col items-center justify-center">
                                <svg class="h-6 w-6 justify-items-center" fill="none" viewBox="0 0 24 24" aria-hidden="true">>@svg('heroicon-s-home-modern')</svg>
                            </div>
                        </a>
            
                        <a href="{{ route('plan.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm">
                            <div id="Planner" class="flex flex-col items-center justify-center">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" aria-hidden="true">>@svg('fileicon-api-blueprint')</svg>
                            </div>
                        </a>

                        <a href="{{ route('internalpeople.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm">
                            <div id="MyOrg" class="flex flex-col items-center justify-center">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" aria-hidden="true">>@svg('heroicon-m-puzzle-piece')</svg>
                            </div>
                        </a>
                        <a href="{{ route('team.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm">
                            <div  id="MyAdmin"class="flex flex-col items-center justify-center">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" aria-hidden="true">>@svg('fluentui-people-team-24')</svg>
                            </div>
                        </a>

                        <a href="{{ route('team.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm">
                            <div  id="MyIn"class="flex flex-col items-center justify-center">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" aria-hidden="true">>@svg('fas-binoculars')</svg>
                            </div>
                        </a>

                        <a href="{{ route('team.index') }}" class="text-slate-500 hover:text-slate-900 rounded-md px-3 py-2 text-sm">
                            <div  id="MyRec"class="flex flex-col items-center justify-center">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" aria-hidden="true">>@svg('gmdi-call')</svg>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <div>
                <a href="{{ route('builder.index') }}" class="text-xl font-semibold text-black hover:bg-black hover:text-white px-3 py-2 text-sm font-medium">Syre</a>
            </div>
            <div class="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
                <div class="relative ml-3">
                    <!-- Dropdown Button -->
                    <button id="dropdownButton" class="flex rounded-full bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800" onclick="toggleDropdown()">
                        <img class="h-8 w-8 rounded-full" src="{{ asset('images/03091C3B-2335-4FBC-B520-AAE89951D1E8_1_201_a.jpeg') }}" alt="">
                    </button>

                    <!-- Dropdown Content -->
                    <div id="dropdownContent" class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <a class="block px-4 py-2 text-sm text-gray-700" href="{{ route('users.index') }}">Your Profile</a>
                        <a class="block px-4 py-2 text-sm text-gray-700" href="#">Sign Out</a>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </nav>
    <div class="flex flex-col h-max">
        <main>
        <div class="bg-black p-4">
            <h1 class="text-white text-2xl font-bold">My Profile<h1>
        </div>
                    @yield('content1')
        </main>

    

<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>

<script>
    tippy(
        '#HomePage', {
        content: 'Home',
        arrow:false,
        });
</script>

<script>
    tippy(
        '#Planner', {
        content: 'Planner',
        arrow:false,
        });
</script>

<script>
    tippy(
        '#MyOrg', {
        content: 'My Organisation',
        arrow:false,
        });
</script>

<script>
    tippy(
        '#MyAdmin', {
        content: 'Admin',
        arrow:false,
        });
</script>

<script>
    tippy(
        '#MyIn', {
        content: 'Company Insights',
        arrow:false,
        });
</script>

<script>
    tippy(
        '#MyRec', {
        content: 'Recruiter',
        arrow:false,
        });
</script>

<script>
    function toggleDropdown() {
    const dropdownContent = document.getElementById('dropdownContent');
    dropdownContent.classList.toggle('hidden');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function (event) {
        const dropdownButton = document.getElementById('dropdownButton');
        const dropdownContent = document.getElementById('dropdownContent');
        if (!dropdownButton.contains(event.target) && !dropdownContent.contains(event.target)) {
            dropdownContent.classList.add('hidden');
        }
    });
</script> 

</body>

</html>

