<nav class="bg-white w-1/8 h-screen flex flex-col border-r border-grey-300  scrollbar overflow-auto">
    <div class="relative w-full flex flex-col h-full items-center justify-between">
        <div class="relative w-full flex flex-col items-center justify-start gap-5">

            <div class="flex flex-shrink-0 items-center w-full p-3">
                <img class="h-12 w-auto" src="{{ asset('images/LogoSmall.png') }}" alt="Your Company">
            </div>
            <div class="flex flex-col gap-6 w-full px-2">
                <a id="HomePage" href="{{ route('home.index') }}"
                    class="side-menu flex justify-start gap-5 items-center px-3 h-10 py-2 w-full {{ request()->is('home') || request()->is('home/*') ? 'ActiveBtn' : '' }} rounded-lg">
                    <img id="gif-element-home" class="h-6 w-6" src="{{ asset('images/home.svg') }}">
                    <h1 class="text-black font-medium">Home</h1>
                </a>
                <a id="MyOrg" href="{{ route('myorg.index') }}"
                    class="side-menu flex justify-start gap-5 items-center px-3 h-10 py-2 w-full {{ request()->is('internalpeople') || request()->is('internalpeople/*') ? 'ActiveBtn' : '' }} rounded-lg hover:bg-[#EDFBFC]">
                    <img id="gif-element-org" class="h-8 w-8" src="{{ asset('images/my_organization.svg') }}">
                    <h1 class="text-black font-medium">My Organization</h1>
                </a>
                <a id="Planner" href="{{ route('plan.index') }}" wire:navigate
                    class="side-menu flex justify-start gap-5 items-center px-3 h-10 py-2 w-full  rounded-lg hover:bg-[#EDFBFC] {{ request()->is('plan') || request()->is('plan/*') ? 'ActiveBtn' : '' }}">
                    <img id="gif-element-plan" class="h-6 w-8" src="{{ asset('images/plans.svg') }}">
                    <h1 class="text-black font-medium">Plans</h1>
                </a>
                <a id="MyRec" href="{{ route('job.index') }}" wire:navigate
                    class="side-menu flex justify-start gap-5 items-center px-3 h-10 py-2 w-full  rounded-lg hover:bg-[#EDFBFC] {{ request()->is('job') || request()->is('job/*') ? 'ActiveBtn' : '' }}">
                    <img id="gif-element-Rec" class="h-8 w-8" src="{{ asset('images/jobs.svg') }}">
                    <h1 class="text-black font-medium">Talent Pool</h1> 
                </a>
               <!--  <a id="MyIn" href="{{ route('company.index') }}"
                    class="side-menu flex justify-start gap-5 items-center px-3 h-10 py-2 w-full {{ request()->is('company') || request()->is('company/*') ? 'ActiveBtn' : '' }} rounded-lg hover:bg-[#EDFBFC]">
                    <img id="gif-element-In" class="h-8 w-8" src="{{ asset('images/company_insights.svg') }}">
                    <h1 class="text-black">Company Insights</h1>
                </a> -->
				<a id="Myrecruitment" href="{{ route('recruitment.index') }}"
                    class="side-menu flex justify-start gap-5 items-center px-3 h-10 py-2 w-full {{ request()->is('recruitment') || request()->is('recruitment/*') ? 'ActiveBtn' : '' }} rounded-lg hover:bg-[#EDFBFC]">
                    <img id="gif-element-org" class="h-8 w-8" src="{{ asset('images/recruitment.svg') }}">
                    <h1 class="text-black font-medium">Recruitment</h1>
                </a>
                @if (auth()->user()->role == 'Master' || auth()->user()->role == 'admin')
                    <a id="MyAdmin" href="{{ route('team.index') }}"
                        class="side-menu flex justify-start gap-5 items-center px-3 h-10 py-2 w-full {{ request()->is('team') || request()->is('team/*') ? 'ActiveBtn' : '' }} rounded-lg hover:bg-[#EDFBFC]">
                        <img id="gif-element-admin" class="h-8 w-8" src="{{ asset('images/admin.svg') }}">
                        <h1 class="text-black font-medium">Admin</h1>
                    </a>
                @endif

                @if (auth()->user()->account_id == '1')
                    <a id="TSPData" href="{{ route('datacenter.index') }}"
                        class="side-menu flex justify-start gap-5 items-center px-3 h-10 py-2 w-full {{ request()->is('datacenter') || request()->is('datacenter/*') ? 'ActiveBtn' : '' }} rounded-lg hover:bg-[#EDFBFC]">
                        <img id="gif-element-Da" class="h-8 w-8" src="{{ asset('images/tsp_center.svg') }}">
                        <h1 class="text-black font-medium">TSP Center</h1>
                    </a>
                @endif
            </div>
        </div>
        @if (auth()->user()->role == 'Master')
            <div class="flex w-full px-2 items-center">
                <a id="Master" href="{{ route('users.create') }}"
                    class="side-menu flex justify-start gap-5 items-center px-3  py-2 w-full {{ request()->is('users') || request()->is('users/*') ? 'ActiveBtn' : '' }} rounded-lg hover:bg-[#EDFBFC]">
                    <img id="gif-element-Ma" class="h-8 w-auto" src="{{ asset('images/settings.svg') }}">
                    <h1 class="text-black font-medium">Master</h1>
                </a>
            </div>
        @endif
    </div>
</nav>
