<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Reset Password</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <!-- Font Family -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet">
    
    <style>
        body {
            padding: 0;
            margin: 0;
        }

        h1,
        h2,
        h3,
        p {
            padding: 0;
            margin: 0;
        }

        /* Font Size Css */
        .fontSize32 {
            font-family: Inter;
            font-size: 32px;
            font-weight: 600;
            line-height: 40px;
            text-align: center;
        }

        .fontSize24 {
            font-family: Inter;
            font-size: 24px;
            font-weight: 600;
            line-height: 32px;
            text-align: center;
        }

        .fontSize14 {
            font-family: Inter;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            text-align: center;
        }

        .fontSize10 {
            font-family: Inter;
            font-size: 10px;
            font-weight: 400;
            line-height: 16px;
        }

        /* End Font Size */
        .parentTable {
            background: #F9FAFB;
            width: 600px;
            padding: 24px;
            margin: 0 auto;
            text-align: center;
        }

        .logoParent {
            text-decoration: none;
            text-align: center;
        }

        .successionPlanLogo {
            width: 49px;
        }

        .childTable {
    width: 552px;
    border: 1px solid #EAECF0;
    margin: 24px;
    padding: 24px;
    background: #FFFFFF;
    border-radius: 16px;
}

        .name_Heading {
            margin-top: 22px;
        }

        .passwordTxt {
            color: #667085;
            padding-top: 8px;
        }

        .otpParent {
            text-align: center;
            margin-top: 16px;
        }
        .resetPassword_Btn{
            width: 172px;
            background: #06B6D4;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            color: #FFFFFF;
            font-weight: 600;
            font-size: 16px;
        }
        .otpTxt {
            background: #EDFBFC;
            padding: 12px 8px;
            width: 64px;
            display: inline-block;
            margin-right: 24px;
            border-radius: 8px;
        }

        .expireTime {
            color: #667085;
            padding-top: 12px;
        }

        .Successionplan_Ai {
            color: #0891B2;
            font-weight: 600;
            padding-top: 5px;
        }

        .best_Regards {
            padding-top: 16px;
        }

        .all_Rights {
            color: #667085;
            padding-top: 10px;
        }

        .footer {
            text-align: center;
            padding: 0 24px 24px;
        }

        .receive_EmailTxt {
            color: #667085;
            padding-top: 24px;
        }

        .link_Parent {
            padding-top: 5px;
        }

        .privacy_Policy {
            color: #101828;
            text-decoration: underline;
            padding-right: 15px;
        }

        .inLogo {
            width: 32px;
        }
    </style>
</head>

<body style="background-color: #ffffff; margin: 0 !important; padding: 0 !important;">

    <table class="parentTable">
        <tr>
            <td>
                <div class="childTable">
                    <a href="" class="logoParent"><img src="./images/successionPlanLogo.png" alt=""
                            class="successionPlanLogo"></a>
                    <h2 class="name_Heading fontSize24">Reset Your Password</h2>
                    <p class="passwordTxt fontSize14">We’ve received a request to reset your password. Click the button below <br> to reset it now. If you didn’t make this request, ignore this email.</p>

                    <a href="{{$resetLink}}"><div class="otpParent">
                       <button class="resetPassword_Btn">Reset Password</button>
                    </div></a>

                    <p class="expireTime fontSize14">This link will expire in 60 minutes.</p>
                    <p class="best_Regards fontSize14">Best Regards, <br>
                        <span class="Successionplan_Ai"> Successionplan.ai</span>
                    </p>
                </div>
            </td>
        </tr>

        <tr>
            <td class="footer">
                <div>
                    <img src="./images/inLogo.png" alt="" class="inLogo">
                    <p class="all_Rights fontSize10">© 2024 Successionplan.ai. All rights reserved.</p>
                    <p class="receive_EmailTxt fontSize10">You are receiving this mail because you registered to join
                        the Successionplan platform as a user or a creator. This <br> also shows that you agree to our
                        Terms
                        of use and Privacy Policies. If you no longer want to receive mails from use, <br> click the
                        unsubscribe link below to unsubscribe.
                    </p>
                    <div class="link_Parent">
                        <span class="privacy_Policy fontSize10">Privacy policy</span>
                        <span class="privacy_Policy fontSize10">Terms of service</span>
                        <span class="privacy_Policy fontSize10">Help center</span>
                        <span class="privacy_Policy fontSize10">Unsubscribe</span>
                    </div>
                </div>
            </td>
        </tr>
    </table>


</body>

</html>