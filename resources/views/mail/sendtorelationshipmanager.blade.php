<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{{ $title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous" />
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet" />
    <!-- Font Family -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet" />

    <style>
        body {
            padding: 0;
            margin: 0;
        }

        h1,
        h2,
        h3,
        p {
            padding: 0;
            margin: 0;
        }

        /* Font Size Css */
        .fontSize32 {
            font-family: Inter;
            font-size: 32px;
            font-weight: 600;
            line-height: 40px;
            text-align: center;
        }

        .fontSize24 {
            font-family: Inter;
            font-size: 24px;
            font-weight: 600;
            line-height: 32px;
        }

        .fontSize16 {
            font-family: Inter;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
        }

        .fontSize14 {
            font-family: Inter;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            text-align: center;
        }

        .fontSize10 {
            font-family: Inter;
            font-size: 10px;
            font-weight: 400;
            line-height: 16px;
        }

        /* End Font Size */
        .main_Logo {
            text-align: center;
            padding: 24px 0 0;
        }

        .organisationImg {
            width: 504px;
            height: 168px;
            border-radius: 12px 0px 0px 0px;
        }

        .parentTable {
            width: 600px;
            padding: 24px;
            margin: 0 auto;
            text-align: center;
            background-image: url("{{ asset('images/Background.png') }}");
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }

        .logoParent {
            text-decoration: none;
            text-align: center;
        }

        .successionPlanLogo {
            width: 49px;
        }

        .childTable {
            width: 552px;
            border: 1px solid #eaecf0;
            margin: 24px;
            padding: 24px;
            background: #ffffff;
            border-radius: 24px;
        }

        .name_Heading {
            margin-top: 16px;
            text-align: left;
        }

        .passwordTxt {
            color: #101828;
            padding-top: 8px;
            text-align: left;
        }

        .otpParent {
            text-align: center;
            margin-top: 16px;
        }

        .resetPassword_Btn {
            width: 172px;
            background: #06b6d4;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            color: #ffffff;
            font-weight: 600;
            font-size: 16px;
        }

        .otpTxt {
            background: #edfbfc;
            padding: 12px 8px;
            width: 64px;
            display: inline-block;
            margin-right: 24px;
            border-radius: 8px;
        }

        .expireTime {
            color: #667085;
            padding-top: 12px;
        }

        .Successionplan_Ai {
            color: #0891b2;
            font-weight: 600;
            padding-top: 5px;
        }

        .best_Regards {
            padding-top: 16px;
            text-align: left;
        }

        .all_Rights {
            color: #667085;
            padding-top: 10px;
        }

        .footer {
            text-align: center;
            padding: 0 24px 24px;
        }

        .receive_EmailTxt {
            color: #667085;
            padding-top: 24px;
        }

        .link_Parent {
            padding-top: 5px;
        }

        .privacy_Policy {
            color: #101828;
            text-decoration: underline;
        }

        .privacy_Policy_Dot {
            padding: 0 5px 5px;
            color: #0891b2;
        }

        .inLogo {
            width: 32px;
        }
    </style>
</head>
<body style="
      background-color: #ffffff;
      margin: 0 !important;
      padding: 0 !important;
    ">
    <table class="parentTable">
        <tr>
            <td>
                <div class="main_Logo">
                    <a href="" class="logoParent"><img src="{{ asset('images/successionPlanLogo.png') }}" alt="" class="successionPlanLogo" /></a>
                </div>
                <div class="childTable">
                    <img src="{{ asset('images/organisationImg.png') }}" alt="organisationImg" class="organisationImg" />
                    <h2 class="name_Heading fontSize24">Hi there,</h2>
                    <p class="passwordTxt fontSize16">

                    <br>
                    <strong>User Name :</strong><br>
                    {{ $sendersName }}<br>
                    <br>
                    <strong>Sender's Company Name:</strong><br>
                    {{ $company_name }}<br>
                    <br>
                    <strong>Plan Description:</strong><br>
                    {{ $plandescription }}<br>
                    <br>
                    <strong>Plan ID:</strong>
                    {{ $planID }}
                    <br>
                    <br>
                    <strong>Requirement:</strong><br><br>
                    @foreach ($requirements as $index => $requirement)
                        {{ $index + 1 }}. {{ $requirement }}<br>
                    @endforeach
                    <br>
                    <strong>Additional Details:</strong><br>
                    {{ $emailMessage }}
                    </p>

                    <p class="expireTime fontSize14">
                        This email is sent as part of a request for Research from our platform. If you have any questions or concerns, feel free to contact us.
                    </p>
                    
                    <p class="passwordTxt fontSize16 best_Regards">
                        We are here to ensure your data and preferences are up to date!
                    </p>
                    <p class="best_Regards fontSize14">
                        Best Regards, <br />
                        <span class="Successionplan_Ai">{{ $sendersName }}</span>
                    </p>
                </div>
            </td>
        </tr>

        <tr>
            <td class="footer">
                <div>
                    <img src="{{ asset('images/inLogo.png') }}" alt="" class="inLogo" />
                    <p class="all_Rights fontSize10">
                        © 2024 Successionplan.ai. All rights reserved.
                    </p>
                    <p class="receive_EmailTxt fontSize10">
                        You are receiving this mail because you initiated a "Right to Rectification" request on our platform. If this was not you, <br />
                        please contact our support team immediately. For more information, please review our Terms of use and Privacy Policies.
                    </p>
                    <div class="link_Parent">
                        <span class="privacy_Policy fontSize10">Privacy policy</span>
                        <span class="privacy_Policy_Dot fontSize24">.</span>
                        <span class="privacy_Policy fontSize10">Terms of service</span>
                    </div>
                </div>
            </td>
        </tr>
    </table>
</body>

</html>