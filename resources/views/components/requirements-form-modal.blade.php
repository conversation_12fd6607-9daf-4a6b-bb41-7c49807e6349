<div>
<div x-data="{ open: false }">
    <!-- Trigger button inside the modal -->
    <button @click="open = true" wire:click="clearModel" class="text-sm text-black font-medium p-2 hover:bg-gray-900 hover:text-white">Add More</button>
    
    <!-- Modal container -->
    <div x-show="open" 
            class="fixed inset-0 flex items-center justify-center z-50"
            style="display:none">
        <!-- Modal background with a higher z-index -->
        <div class="modal-background fixed inset-0 bg-slate-50 opacity-50 z-40"></div>
        <!-- Modal content with a lower z-index -->
        <div class="modal-content bg-white shadow-md rounded-lg p-4 z-50">
            <!-- Modal content -->
            <form wire:submit="createRequirement">
                @csrf
                <label for="name" class="block font-bold text-gray-800">Requirement Name</label>
                <input type="text" wire:model="name" class="text-black form-input mt-1 block w-full" placeholder="skills">
                <button x-on:click="open = false" type="submit">Add</button>
                <button x-on:click="open = false" type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
            </form>
        </div>
    </div>
</div>
</div>