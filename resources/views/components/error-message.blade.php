<div>
    @if (session()->has('error_data'))
        @php
            $errorData = session('error_data');
            $type = $errorData['type'] ?? 'server';
            $message = $errorData['message'] ?? session('error');
            $actions = $errorData['actions'] ?? [];
            $code = $errorData['code'] ?? null;
            
            // Define styling based on error type
            $styles = [
                'validation' => 'bg-yellow-100 border-yellow-500 text-yellow-800',
                'authentication' => 'bg-red-100 border-red-500 text-red-800',
                'authorization' => 'bg-red-100 border-red-500 text-red-800',
                'not_found' => 'bg-gray-100 border-gray-500 text-gray-800',
                'conflict' => 'bg-orange-100 border-orange-500 text-orange-800',
                'external_service' => 'bg-blue-100 border-blue-500 text-blue-800',
                'server' => 'bg-red-100 border-red-500 text-red-800'
            ];
            
            $style = $styles[$type] ?? $styles['server'];
            
            // Define icons based on error type
            $icons = [
                'validation' => 'exclamation-triangle',
                'authentication' => 'lock',
                'authorization' => 'shield-exclamation',
                'not_found' => 'search',
                'conflict' => 'exclamation-circle',
                'external_service' => 'cloud',
                'server' => 'exclamation-circle'
            ];
            
            $icon = $icons[$type] ?? $icons['server'];
        @endphp
        
        <div x-data="{ show: true }" x-show="show" 
             x-init="setTimeout(() => { show = false }, 8000)"
             class="rounded-md border-l-4 p-4 {{ $style }} fixed top-0 right-0 mt-4 mr-4 z-50 shadow-lg max-w-md">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-{{ $icon }}"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium">{{ $message }}</h3>
                    
                    @if($code)
                        <div class="mt-1 text-xs text-gray-600">
                            Reference code: {{ $code }}
                        </div>
                    @endif
                    
                    @if(count($actions) > 0)
                        <div class="mt-3 flex space-x-4">
                            @foreach($actions as $action)
                                @if(isset($action['url']))
                                    <a href="{{ $action['url'] }}" class="text-sm font-medium underline">
                                        {{ $action['label'] }}
                                    </a>
                                @elseif(isset($action['action']))
                                    <button 
                                        onclick="{{ $action['action'] }}" 
                                        class="text-sm font-medium underline">
                                        {{ $action['label'] }}
                                    </button>
                                @endif
                            @endforeach
                        </div>
                    @endif
                </div>
                <div class="ml-auto">
                    <button @click="show = false" class="inline-flex text-gray-500 hover:text-gray-700">
                        <span class="sr-only">Dismiss</span>
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    @elseif (session()->has('error'))
        <div x-data="{ show: true }" x-show="show" 
             x-init="setTimeout(() => { show = false }, 8000)"
             class="rounded-md border-l-4 p-4 bg-red-100 border-red-500 text-red-800 fixed top-0 right-0 mt-4 mr-4 z-50 shadow-lg max-w-md">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium">{{ session('error') }}</h3>
                </div>
                <div class="ml-auto">
                    <button @click="show = false" class="inline-flex text-gray-500 hover:text-gray-700">
                        <span class="sr-only">Dismiss</span>
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>