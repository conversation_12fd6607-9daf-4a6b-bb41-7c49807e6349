<div x-data="{ open: true }">
    <!-- Trigger button inside the modal -->
    <button @click="open = true" class="text-sm text-black font-medium p-2 hover:bg-gray-900 hover:text-white flex justify-center">New Plan</button>
    
    <!-- Modal container -->
    <div x-show="open" 
            class="fixed inset-0 flex items-center justify-center z-50"
            style="display:none">
        <!-- Modal background with a higher z-index -->
        <div class="modal-background fixed inset-0 bg-slate-50 opacity-50 z-40"></div>
        <!-- Modal content with a lower z-index -->
        <div class="modal-content border border-black border-solid bg-white shadow-lg rounded-lg p-4 z-50">
            <!-- Modal content -->
            <form wire:submit="CreatePlan">
                @csrf
                <div class="mt-2 flex items-center">
                    <label for="planName" class="block text-base font-medium leading-6 text-gray-900 py-1">I'm making a plan for</label>
                    <input type="text" wire:model="name" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="Role Name in Function-Division in Country." required>
                    @error('name')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>                        
                    @enderror
                </div>
                <div class="mt-5 relative">
                    <div class="absolute inset-0 flex items-center" aria-hidden="true">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-start">
                        <span class="bg-white pr-3 text-base font-semibold leading-6 text-gray-900">Role</span>
                    </div>
                </div>
                <div class= "mt-1">
                    <div class="mt-2 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">I'm looking for people currently working as </label>
                        <input type="text" wire:model="roles" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="Head of Risk, CFO" required>
                        @error('roles')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    <div class="mt-2 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">In the Division </label>
                        <input type="text" wire:model="division" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="Investment Banking, Asset Management, Food, Household" required>
                        @error('function')
                        <p class="text-red-500 text-sm mt-1">{{ $function }}</p>
                        @enderror
                    </div>
                    <div class="mt-2 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">In the Functions </label>
                        <input type="text" wire:model="function" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="Marketing, Operations" required>
                        @error('function')
                        <p class="text-red-500 text-sm mt-1">{{ $function }}</p>
                        @enderror
                    </div>
                    <div class="mt-2 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">If I had to consider step-up candidates I would choose</label>
                        <input type="text" wire:model="stepup" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="Head of Risk, CFO, Not Applicable" required>
                        @error('roles')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    <div class="mt-2 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">I want to look for people who are working in the following companies</label>
                        <input type="text" wire:model="company" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="Company A, Company B, Not Applicable" required>
                        @error('roles')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                <div class="mt-7 relative">
                    <div class="absolute inset-0 flex items-center" aria-hidden="true">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-start">
                        <span class="bg-white pr-3 text-base font-semibold leading-6 text-gray-900">Experience</span>
                    </div>
                </div>
                <div class= "">
                    <div class="mt-1 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">I'm looking for individuals with at least</label>
                        <input type="number" wire:model="min_exp" autocomplete="minimum_Experience" class="w-24 place-items-center appearance-none ml-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="10" required>
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">years of experience in their current role.</label>
                        @error('minimum_Experience')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    <div class="mt-2 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">They should have the following qualifications </label>
                        <input type="text" wire:model="education" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="CFA-Level 3, Another Qualification">
                    </div>
                    <div class="mt-2 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">They should have the following skills </label>
                        <input type="text" wire:model="skills" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="Risk Management, Public Speaking, Not Applicable">
                    </div>
                </div>
                <div class="mt-7 relative">
                    <div class="absolute inset-0 flex items-center" aria-hidden="true">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-start">
                        <span class="bg-white pr-3 text-base font-semibold leading-6 text-gray-900">Additional Details</span>
                    </div>
                </div>
                <div class= "">
                    <div class="mt-2 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">They should be based in </label>
                        <input type="text w-full" wire:model="location" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="United Kingdom, Singapore, Not Applicable" required>
                        @error('roles')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    <div class="mt-2 flex items-center">
                        <label for="planName" class="block text-base font-medium leading-6 text-gray-900">I'm looking for individuals who may be identified as </label>
                        <input type="text" wire:model="gender" class="appearance-none ml-1 flex-1 bg-transparent pl-1 text-sky-500 placeholder:text-gray-400 text-base font-medium hover:placeholder-sky-500" placeholder="Female, Male, All" required>
                        @error('roles')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    <div class="mt-1">
                        <fieldset>
                            <legend class="text-base font-medium leading-6 text-gray-900">Is Ethnicity important?</legend>
                            <div class="flex-wrap">
                                <p class="text-xs leading-6 text-gray-600">Please note profiling through ethnicity has a low accuracy only select if this is absolutely neccessary</p>
                            </div>
                            <div class="mt-3 flex space-x-3">
                                <div class="flex items-center gap-x-3">
                                    <input type="radio" wire:model="ethnicity"  class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black" value="1">
                                    <label for="push-yes" class="block text-sm font-medium leading-6 text-gray-900">Yes</label>
                                </div>
                                <div class="flex items-center gap-x-3">
                                    <input type="radio" wire:model="ethnicity" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black" value="0">
                                    <label for="push-no" class="block text-sm font-medium text-gray-900">No</label>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>

                <div class="mt-2 col-span-full">
                    <label for="about" class="block text-sm font-medium leading-6 text-gray-900">Write a few words about your plan</label>
                    <div class="mt-1">
                        <textarea id="description" name="description" rows="2" class="bg-white block w-full rounded-md border-0 py-1.5 px-1 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" required></textarea>
                    </div>
                    @error('description')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="mt-1 flex items-center justify-end gap-x-6">
                    <button x-on:click="open =false" type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                    <button x-on:click="open =false" type="submit" class="rounded-full bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>