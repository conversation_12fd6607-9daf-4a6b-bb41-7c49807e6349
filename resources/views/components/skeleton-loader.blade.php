{{-- Skeleton Loader Component --}}
@props([
    'type' => 'default', // default, table, chart, card
    'rows' => 3,
    'class' => ''
])

<div class="animate-pulse {{ $class }}">
    @if($type === 'table')
        <!-- Table Skeleton -->
        <div class="w-full">
            <!-- Table Header -->
            <div class="flex gap-4 p-4 border-b">
                @for($i = 0; $i < 4; $i++)
                    <div class="h-4 bg-gray-200 rounded flex-1"></div>
                @endfor
            </div>
            <!-- Table Rows -->
            @for($row = 0; $row < $rows; $row++)
                <div class="flex gap-4 p-4 border-b">
                    @for($col = 0; $col < 4; $col++)
                        <div class="h-4 bg-gray-200 rounded flex-1"></div>
                    @endfor
                </div>
            @endfor
        </div>
    @elseif($type === 'chart')
        <!-- Chart Skeleton -->
        <div class="w-full h-64 bg-gray-200 rounded-lg"></div>
        <div class="mt-4 space-y-2">
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
    @elseif($type === 'card')
        <!-- Card Skeleton -->
        <div class="border rounded-lg p-4 space-y-4">
            <div class="h-6 bg-gray-200 rounded w-1/4"></div>
            <div class="space-y-2">
                <div class="h-4 bg-gray-200 rounded"></div>
                <div class="h-4 bg-gray-200 rounded w-5/6"></div>
                <div class="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
        </div>
    @elseif($type === 'profile')
        <!-- Profile Modal Skeleton -->
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center gap-4">
                <div class="w-16 h-16 bg-gray-200 rounded-full"></div>
                <div class="space-y-2">
                    <div class="h-6 bg-gray-200 rounded w-32"></div>
                    <div class="h-4 bg-gray-200 rounded w-24"></div>
                </div>
            </div>
            
            <!-- Content sections -->
            @for($section = 0; $section < 3; $section++)
                <div class="space-y-3">
                    <div class="h-5 bg-gray-200 rounded w-24"></div>
                    <div class="space-y-2">
                        @for($line = 0; $line < 3; $line++)
                            <div class="h-4 bg-gray-200 rounded w-full"></div>
                        @endfor
                    </div>
                </div>
            @endfor
        </div>
    @else
        <!-- Default Skeleton -->
        @for($i = 0; $i < $rows; $i++)
            <div class="h-4 bg-gray-200 rounded mb-2"></div>
        @endfor
    @endif
</div>