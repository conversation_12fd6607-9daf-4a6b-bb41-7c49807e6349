{{-- Loading Modal Component --}}
@props([
    'show' => false,
    'title' => 'Loading...',
    'description' => 'Please wait while we load your content.',
    'type' => 'spinner' // spinner, progress, skeleton
])

<div x-show="{{ $show }}" 
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 z-50 overflow-y-auto"
     style="display: none;">
    
    <!-- Background overlay -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
    
    <!-- Modal content -->
    <div class="flex min-h-screen items-center justify-center p-4">
        <div class="relative w-full max-w-md transform overflow-hidden rounded-lg bg-white px-6 py-8 shadow-xl transition-all">
            
            @if($type === 'spinner')
                <!-- Spinner Loading -->
                <div class="flex flex-col items-center text-center">
                    <div class="mb-4">
                        <div class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent"></div>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $title }}</h3>
                    <p class="text-sm text-gray-500">{{ $description }}</p>
                </div>
                
            @elseif($type === 'progress')
                <!-- Progress Bar Loading -->
                <div class="text-center">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $title }}</h3>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                        <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 45%"></div>
                    </div>
                    <p class="text-sm text-gray-500">{{ $description }}</p>
                </div>
                
            @else
                <!-- Skeleton Loading -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $title }}</h3>
                    <x-skeleton-loader type="profile" :rows="2" />
                </div>
            @endif
        </div>
    </div>
</div>