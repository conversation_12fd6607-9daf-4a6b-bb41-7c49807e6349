<!DOCTYPE html>
<html>
<head>
    <title>AI Chatbot</title>
    <style>
        /* Basic styling for the chat interface */
        #chat-container {
            width: 500px;
            margin: 0 auto;
        }
        #chat-window {
            border: 1px solid #ccc;
            height: 400px;
            overflow-y: scroll;
            padding: 10px;
        }
        .message {
            margin-bottom: 15px;
        }
        .message.user {
            text-align: right;
        }
        .message.assistant {
            text-align: left;
        }
        #message-input {
            width: 100%;
            box-sizing: border-box;
        }
    .plan-card {
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 15px;
        margin-top: 20px;
        background-color: #f9f9f9;
    }
    .plan-card h2 {
        margin-top: 0;
    }
    .plan-card p {
        margin: 5px 0;
    }
    </style>
    <style>
    /* Enhanced Chat Interface */
    body {
        font-family: Arial, sans-serif;
        background-color: #f4f4f9;
        margin: 0;
        padding: 0;
    }

    #chat-container {
        width: 500px;
        margin: 50px auto;
        background-color: #ffffff;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        padding: 20px;
    }

    h1 {
        text-align: center;
        color: #333;
        margin-bottom: 20px;
    }

    #chat-window {
        border: none;
        background-color: #f9f9f9;
        height: 400px;
        overflow-y: auto;
        padding: 15px;
        border-radius: 10px;
        box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .message {
        margin-bottom: 15px;
        padding: 10px;
        border-radius: 8px;
        max-width: 75%;
        word-wrap: break-word;
    }

    .message.user {
        background-color: #daf1dc;
        color: #333;
        margin-left: auto;
        text-align: right;
        border: 1px solid #b7d8b6;
    }

    .message.assistant {
        background-color: #f1f1f1;
        color: #333;
        margin-right: auto;
        text-align: left;
        border: 1px solid #ddd;
    }

    #message-input {
        width: calc(100% - 85px);
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin-right: 5px;
    }

    #send-button {
        background-color: #007bff;
        color: #fff;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    #send-button:hover {
        background-color: #0056b3;
    }

    /* Plan Card */
    .plan-card {
        border: none;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        background-color: #ffffff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .plan-card h2 {
        margin-top: 0;
        font-size: 1.5rem;
        color: #007bff;
    }

    .plan-card p {
        margin: 8px 0;
        font-size: 0.9rem;
        color: #555;
    }

    .plan-card p strong {
        color: #333;
    }

    #plan-container {
        margin-top: 30px;
    }

    /* Scrollbar Customization */
    #chat-window::-webkit-scrollbar {
        width: 8px;
    }

    #chat-window::-webkit-scrollbar-thumb {
        background-color: #bbb;
        border-radius: 10px;
    }

    #chat-window::-webkit-scrollbar-track {
        background-color: #f1f1f1;
    }
</style>

    <!-- Include jQuery (optional but helpful) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>

    <div id="chat-container">
        <h1>AI Chatbot</h1>
        <div id="chat-window">
            @if(session('messages'))
                @foreach(session('messages') as $message)
                    @if($message['role'] === 'user')
                        <div class="message user">
                            <p><strong>You:</strong> {{ $message['content'] }}</p>
                        </div>
                    @else
                        <div class="message assistant">
                            @foreach($message['content'] as $content)
                                @if($content['type'] === 'text')
                                    <p><strong>AI:</strong> {{ $content['text'] }}</p>
                                @endif
                            @endforeach
                        </div>
                    @endif
                @endforeach
            @endif

        </div>
        <textarea id="message-input" rows="3" placeholder="Type your message here..."></textarea><br>
        <button id="send-button">Send</button>

        <div id="plan-container" style="display: none;">
            <h2>Plan Created:</h2>
            <table border="1" id="plan-table">
                <tr>
                    <th>Field</th>
                    <th>Value</th>
                </tr>
                <!-- Plan data will be appended here -->
            </table>
        </div>
    </div>

    <script>
        $(document).ready(function() {
    $('#send-button').click(function() {
        var message = $('#message-input').val();
        if (message.trim() === '') {
            alert('Please enter a message.');
            return;
        }

        $('#chat-window').append('<div class="message user"><p><strong>You:</strong> ' + message + '</p></div>');
        $('#message-input').val('');

        $('#chat-window').scrollTop($('#chat-window')[0].scrollHeight);

$('#chat-window').append('<div id="loading-indicator"><p><em>AI is thinking...</em></p></div>');

$('#chat-window').scrollTop($('#chat-window')[0].scrollHeight);

        $.ajax({
            url: '{{ route("my-chatbot.send-message") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                message: message
            },
            success: function(response) {
                $('#loading-indicator').remove();

                if (response.aiResponse) {
                    var aiContent = '';
                    response.aiResponse.forEach(function(content) {
                        if (content.type === 'text') {
                            var formattedText = content.text.replace(/\n/g, '<br>');
                            aiContent += '<p><strong>AI:</strong> ' + formattedText + '</p>';
                        }
                    });
                    $('#chat-window').append('<div class="message assistant">' + aiContent + '</div>');

                    $('#chat-window').scrollTop($('#chat-window')[0].scrollHeight);
                }

                if (response.planCreated) {
                    displayPlan(response.planData);
                }
            },
            error: function(xhr) {
                $('#loading-indicator').hide();

                alert('Error: ' + xhr.responseJSON.error);
            }
        });
    });

    function displayPlan(planData) {
    console.log('Displaying plan:', planData);

    $('#plan-container').show();
    $('#plan-container').html('');

    var planCard = `
        <div class="plan-card">
            <h2>${planData.plan_name}</h2>
            <p><strong>Description:</strong> ${planData.description}</p>
            <p><strong>Target Roles:</strong> ${planData.target_roles.join(', ')}</p>
            <p><strong>Step Up Candidates:</strong> ${planData.step_up_candidates.join(', ')}</p>
            <p><strong>Minimum Tenure:</strong> ${planData.minimum_tenure || 'N/A'}</p>
            <p><strong>Gender:</strong> ${planData.gender || 'N/A'}</p>
            <p><strong>Country:</strong> ${planData.country || 'N/A'}</p>
            <p><strong>Ethnicity Important:</strong> ${planData.is_ethnicity_important !== undefined ? (planData.is_ethnicity_important ? 'Yes' : 'No') : 'N/A'}</p>
            <p><strong>Companies:</strong> ${planData.companies.join(', ')}</p>
            <p><strong>Qualifications:</strong> ${planData.qualifications.join(', ')}</p>
            <p><strong>Skills:</strong> ${planData.skills.join(', ')}</p>
        </div>
    `;

    $('#plan-container').append(planCard);

    $('html, body').animate({
        scrollTop: $('#plan-container').offset().top
    }, 1000);
}

});
    </script>

</body>
</html>
