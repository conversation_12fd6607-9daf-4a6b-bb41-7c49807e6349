@extends('layouts.forgotPassword')

@section('content')
@if (session('status'))
    <div class="alert alert-success">
        {{ session('status') }}
    </div>
@endif

<section class="reset_Password">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="form_Parent">
                    <form method="POST" action="{{ route('password.email') }}" class="forgetPassword_Form text-center">
                        @csrf
                        <img src="{{ asset('images/LogoSmall.png') }}" alt="logo" class="successionPlan_Logo">
                        <h2 class="forgotPass_Txt fontSize24 black">Forgot password?</h2>
                        <p class="enterEmailTxt grey fontSize14">Enter the email address you used to create account. We will
                            send a
                            password reset email.
                        </p>
                        <div class="input_Parent">
                            <label for="email" class="email_Label grey fontSize12">Email</label><br>
                            <input name="email" value="{{ old('email') }}" type="text" id="email" class="form-control email_Input fontSize16 @error('email') is-invalid @enderror" placeholder="Enter your email">
                            @error('email')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>
                        <button type="submit" class="sendLink_Btn fontSize14">Send reset link</button>
                        <a href="{{route('login')}}" class="backTo_Login">Back to Log In</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection

