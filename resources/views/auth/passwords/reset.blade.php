@extends('layouts.forgotPassword')

@section('content')
<section class="reset_Password">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="form_Parent">
                    <form method="POST" action="{{ route('password.update') }}" class="forgetPassword_Form text-center">
                        @csrf
                        <input type="hidden" name="token" value="{{ $token }}">

                        <img src="{{ asset('images/LogoSmall.png') }}" alt="" class="successionPlan_Logo">
                            <h2 class="forgotPass_Txt fontSize24 black">Reset your password</h2>
                        <p class="enterEmailTxt grey fontSize14">Enter the new password for Successionplan account.
                        </p>

                        <div class="input_Parent position-relative">
                            <label for="email" class="email_Label grey fontSize12">Email</label><br>
                            <input type="email" id="email" class="form-control email_Input fontSize16 @error('email') is-invalid @enderror" name="email" value="{{ $email ?? old('email') }}" required autocomplete="email" autofocus
                                placeholder="Enter email">
                            @error('email')
                            <span class="invalid-feedback" role="alert">
                                {{ $message }}
                            </span>
                            @enderror
                        </div>
                        <div class="input_Parent position-relative">
                            <label for="password" class="email_Label grey fontSize12">Password</label><br>
                            <input type="password" id="password" class="form-control email_Input fontSize16 @error('password') is-invalid @enderror" name="password" required autocomplete="new-password"
                                placeholder="Enter new password">
                            <i class="fa-solid fa-eye eyeLogo"></i>
                            @error('password')
                            <span class="invalid-feedback" role="alert">
                                {{ $message }}
                            </span>
                            @enderror
                        </div>
                        <div class="input_Parent position-relative mt-3">
                            <label for="password_confirmation" class="email_Label grey fontSize12">Repeat Password</label><br>
                            <input type="password" id="password_confirmation" class="email_Input fontSize16" name="password_confirmation" required autocomplete="new-password"
                                placeholder="Repeat new password">
                            <i class="fa-solid fa-eye eyeLogo"></i>
                        </div>
                        <button type="submit" class="sendLink_Btn fontSize14">Save new password</button>
                        <a href="{{route('login')}}" class="backTo_Login">Back to Log In</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    document.querySelectorAll('.eyeLogo').forEach(eyeLogo => {
        eyeLogo.addEventListener('click', function (e) {
            const passwordField = this.previousElementSibling;
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
            this.classList.toggle('fa-eye-slash');
        });
    });
</script>
@endpush
