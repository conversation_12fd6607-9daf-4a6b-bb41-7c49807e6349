# Caching in Laravel with Red<PERSON>: Simple Example

**Date:** January 20, 2024  
**Reading Time:** 3 mins, 490 words  
**Last Updated:** March 28, 2025 with Laravel 12

![Redis Caching](redis-caching-header.jpg)

Caching with Redis is one of the common requirements in Laravel job descriptions. But it's not complicated at all, this tutorial will show you the fundamentals.

Here's a simple example on how to use Redis in Laravel for caching Eloquent queries.

---

## 1. Redis server: install/launch

Redis is not a Laravel-specific system, it is installed separately, just follow its [official installation instructions](https://redis.io/docs/install/install-redis/).

Then, just launch it with the command `redis-server`.

---

## 2. Install Redis PHP Extension

Ensure you have the Redis PHP extension installed:

For Ubuntu based systems:

```bash
sudo apt-get install redis php8.4-redis
```

```bash
sudo systemctl restart php8.4-fpm.service
```

> If needed, replace the PHP version with the version used in your system.

---

## 3. Install Predis Package

Predis is a flexible and feature-complete Redis client for PHP. Install it via Composer:

```bash
composer require predis/predis
```

---

## 4. Configure Redis in Laravel

Change the `CACHE_STORE` to `redis` in the `.env` file:

```env
CACHE_STORE=redis
```

Edit the `.env` file to change Redis server configuration if not using the default values:

```env
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

---

## 5. Use Redis in Laravel for Caching

### 5.1 Cache an Eloquent Query

Suppose you have an Eloquent query like this:

```php
$users = User::where('active', 1)->get();
```

To cache this query result with Redis:

```php
use Illuminate\Support\Facades\Cache;

$users = Cache::remember('active_users', 60, function () {
    return User::where('active', 1)->get();
});
```

Here, `'active_users'` is the cache key, `60` is the number of seconds to cache the result, and the closure fetches the users when the cache is not found.

### 5.2 Clear Cache

To clear the cached result:

```php
use Illuminate\Support\Facades\Cache;

Cache::forget('active_users');
```

---

## 6. Display Cached Data

Here's a simple example to display active users using caching:

- Create a route in `web.php`:

```php
use Illuminate\Support\Facades\Cache;
use App\Models\User;

Route::get('/active-users', function () {
    $users = Cache::remember('active_users', 60, function () {
        return User::where('active', 1)->get();
    });

    return view('active_users', ['users' => $users]);
});
```

- Create a view `active_users.blade.php`:

```blade
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Users</title>
</head>
<body>
    <h1>Active Users</h1>
    <ul>
        @foreach($users as $user)
            <li>{{ $user->name }}</li>
        @endforeach
    </ul>
</body>
</html>
```

Now, when you visit `/active-users` in your browser, it will fetch and display active users from the database and cache the result for 60 seconds.

---

## 7. Check Current Cache

If you want to see what values are cached at the moment, you can do it with a tool called [RedisInsight](https://redis.com/redis-enterprise/redis-insight/).

![RedisInsight Screenshot](redisinsight-screenshot.png)

---

You can read much more about Redis in our premium tutorial [Redis in Laravel 101: Main Things You Need to Know](https://laraveldaily.com/post/redis-in-laravel-101-main-things-you-need-to-know?mtm_campaign=post-internal-redis-simple-example).

---

## User Comments

### Cache Time Correction (Christoph Vollmann - 1 year ago)
Just a quick correction for the Cache::remember call. "60 is the number of minutes" is not true. It's the number of seconds to cache.

See: https://laravel.com/docs/10.x/cache#retrieve-store

**Response from Nerijus:** Thanks. Updated article

### Additional Time Correction (Faraz - 1 year ago)
It has a minor typo. 60 is the number of seconds.

### Conflicting Paragraphs (Mac Danson - 1 year ago)
> Here, 'active_users' is the cache key, 60 is the number of seconds to cache the result, and the closure fetches the users when the cache is not found.

> Now, when you visit /active-users in your browser, it will fetch and display active users from the database and cache the result for 60 minutes.

Those 2 paragraphs are conflicting, kindly correct them. Otherwise thanks for tips always

### Laravel 11 Configuration Update (Flavius Constantin - 1 year ago)
This should be `CACHE_DRIVER` instead of `CACHE_STORE` =)

**Response from Modestas:** For older Laravel versions - you would be correct. But in this case - article is written on Laravel 11 and it uses `CACHE_STORE`:

https://github.com/laravel/laravel/blob/11.x/.env.example#L39

Changing the store to `redis` will automatically change the driver now. It used to be different, but this changed with Laravel 11
