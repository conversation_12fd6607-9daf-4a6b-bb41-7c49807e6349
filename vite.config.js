import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';
import livewire from '@defstudio/vite-livewire-plugin';

export default defineConfig({
  server: {
    host: '127.0.0.1',
    port: 5173,
  },
  plugins: [
    laravel({
      input: [
        'resources/css/app.css',
        'resources/js/app.js', // Entry point for the app
        'resources/js/app.jsx', // Entry point for the app
      ],
      refresh: true, // Enables live reload for Blade and JS changes
    }),
    react(),
	livewire(),
  ],
});
