{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "barryvdh/laravel-dompdf": "^3.1", "barryvdh/laravel-snappy": "^1.0", "doctrine/dbal": "^3.9", "dompdf/dompdf": "^3.0", "guzzlehttp/guzzle": "^7.7", "inertiajs/inertia-laravel": "*", "laravel/fortify": "^1.17", "laravel/framework": "^10.10", "laravel/horizon": "^5.32", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "3.0", "livewire/livewire": "^3.0", "maatwebsite/excel": "^3.1", "openai-php/client": "^0.3.5", "openai-php/laravel": "^0.3.4", "phpoffice/phpword": "^1.3", "predis/predis": "^3.0", "pusher/pusher-php-server": "^7.2", "rap2hpoutre/fast-excel": "^5.3"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["app/helpers.php"]}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}