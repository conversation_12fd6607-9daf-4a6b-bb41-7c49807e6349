// Service Worker for SuccessionPlanAI
// Version 1.0.0

const CACHE_NAME = 'succession-plan-cache-v5-disabled';
const DYNAMIC_CACHE = 'succession-plan-dynamic-v5-disabled';

// Resources to cache on install - only include assets that actually exist
const STATIC_ASSETS = [
  '/',
  '/images/Plus.svg',
  '/images/Minus.svg',
  '/images/cross.svg',
  '/images/Avatar.svg',
  '/images/DotsThreeVertival.svg',
  '/images/FloppyDisk.svg',
  '/images/Buildings.svg',
  '/images/Briefcase.svg',
  '/images/LinkedIn.svg',
  '/images/calendarblank.svg',
  '/images/calendar.svg',
  '/images/deleteIcon.svg',
  '/images/redTrashIcon.svg',
  '/images/TrashSimple.svg',
  '/images/UsersThree.svg',
  '/images/upload-icon.svg',
  '/images/download.png'
];

// API routes that should NOT be cached (dashboard data that changes frequently)
const NO_CACHE_ROUTES = [
  '/paginated-internal-peoples',
  '/organisation-details',
  '/api/',
  '/dashboard',
  '/plans',
  '/people',
  '/summary'
];

// API routes that can be cached (static data)
const API_ROUTES = [
  // Only cache truly static API endpoints here
];

// Install event - cache static assets
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching static assets');
        // Use Promise.allSettled to cache assets individually
        // This prevents failure of one asset from breaking the entire cache
        const cachePromises = STATIC_ASSETS.map(asset => {
          return fetch(asset)
            .then(response => {
              if (response.ok) {
                return cache.put(asset, response);
              }
              console.warn(`Failed to cache asset: ${asset} - ${response.status}`);
            })
            .catch(error => {
              console.warn(`Failed to fetch asset: ${asset}`, error);
            });
        });
        return Promise.allSettled(cachePromises);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.filter(cacheName => {
          return cacheName !== CACHE_NAME && cacheName !== DYNAMIC_CACHE;
        }).map(cacheName => {
          console.log('Deleting old cache:', cacheName);
          return caches.delete(cacheName);
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - disabled caching, pass through all requests
self.addEventListener('fetch', event => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') return;
  
  // Skip chrome-extension and other non-http(s) schemes
  if (!event.request.url.startsWith('http')) return;
  
  // Pass through all requests without caching
  event.respondWith(fetch(event.request));
});

// Network-first strategy for API requests
async function networkFirstStrategy(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // Clone the response before using it
    const responseToCache = networkResponse.clone();
    
    // Cache the response for future use
    caches.open(DYNAMIC_CACHE).then(cache => {
      // Only cache successful responses
      if (responseToCache.status === 200) {
        cache.put(request, responseToCache);
      }
    });
    
    return networkResponse;
  } catch (error) {
    // If network fails, try to get from cache
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If not in cache, show offline content
    return caches.match('/offline.html');
  }
}

// Cache-first strategy for static assets
async function cacheFirstStrategy(request) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    // If not in cache, get from network
    const networkResponse = await fetch(request);
    
    // Cache the response for future use
    const responseToCache = networkResponse.clone();
    caches.open(DYNAMIC_CACHE).then(cache => {
      // Only cache successful responses
      if (responseToCache.status === 200) {
        cache.put(request, responseToCache);
      }
    });
    
    return networkResponse;
  } catch (error) {
    // If both cache and network fail, show offline content
    // For images, return a placeholder image
    if (request.url.match(/\.(jpg|jpeg|png|gif|svg)$/)) {
      return caches.match('/images/placeholder.png');
    }
    
    return caches.match('/offline.html');
  }
}