body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden; /* Prevent scrolling */
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.background-image {
    background-image: url('roi-dimor-9vcwatGze1I-unsplash.jpg');
    background-size: cover;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100vh;
    opacity: 0.7;
    z-index: -1;

}

.logo {
    position: absolute;
    top: 20px;
    right: 10px;
    width: 300px; /* Adjust the width as needed */
    height:75px;
}

.login-form {
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    width: 300px;
    height: 300px;
    margin: auto;
}

.login-form h2 {
    margin-bottom: 20px;
    font-size: 30px;
    font-family: Helvetica, Arial, sans-serif;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-family: Helvetica, Arial, sans-serif;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
}

button[type="submit"] {
    background-color: black;
    font-family: Helvetica, Arial, sans-serif;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.footer {
    padding: 20px;
    text-align: center;
    position: absolute;
    bottom: 0;
    width: 100%;
}

.footer-text {
    margin-bottom: 10px;
}

.footer-links {
    display: flex;
    justify-content: center;
}

.footer-links a {
    margin: 0 10px;
    color: #666;
    text-decoration: none;
}

.text-2xl {
    font-size: 1.5rem/* 24px */;
    line-height: 2rem/* 32px */;
}

.margintop{
    margin-top: 1rem;
}

.grayText{
   color : #667085;
}

.outline-none {
 outline: none;
}


.mainblue{
    color : #0891B2;

}
