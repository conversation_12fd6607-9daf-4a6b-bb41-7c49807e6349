.mainBlue {
    color: #06B6D4;
}

.mainGreen {
    color: #12B76A;
}

.bg-mainBlue {
    background-color: #06B6D4;
}

.bordermainBlue {
    border-color: #06B6D4;
}

.GrayText {
    color: #667085;
}

.gridBorder {
    border: 1px solid var(--border-border-primary, #EAECF0);
    transition: border-color 0.3s ease;
}

.gridBorder:focus-within {
    border: 1px solid var(--brand-brand-primary, #06B6D4);
}

.bg-blue-700,.hover\:bg-blue-800:hover,.focus\:ring-blue-300:focus,.focus\:ring-4:focus {
    background-color: transparent;
	--tw-ring-color: transparent;
}

.redText {
    color: red;
}

.grayBackground {
    background: var(--surface-surface-hover, #eef3fa);

}

.silver {
    border-top: 5px solid var(--text-text-secondary, #667085)
}

.gold {
    border-top: 5px solid var(--status-status-warning-primary, #FFA347)
}

.platinum {
    border-top: 5px solid var(--brand-brand-primary, #06B6D4)
}

.BlueBackground {
    background: var(--brand-brand-light, #EDFBFC);
}

.GreenBackground {
    background: var(--brand-brand-light, #e0f5e9);
}

.RedBG {
    background: var(--status-status-error-light, #fae9e8);

}

.orangeBg {
    background: var(--status-status-warning-light, #FFF6ED);
    color: #FFA347;

}

.border-b-2 {
    border-bottom: 2px solid var(--border-border-primary, #EAECF0)
}

.bg-color {
    background: var(--surface-surface-disabled, #F2F4F7);

}

.lightgray {

    background: var(--surface-surface-background, #F9FAFB);
}

.justify-around {
    justify-content: space-around;
}


.padding-r {
    padding-right: 25px;
}

.ActiveBtn {
    background: var(--brand-brand-transparent, #06B6D459);
}

.Insights-padding {
    padding: 10px;
}

.w-20 {
    width: 5rem
        /* 80px */
    ;
}

.justify-evenly {
    justify-content: space-evenly;
}


.calc-height {
    height: calc(100vh - 88px);

}

.fontInter {
    font-family: Inter;
}

.plan-description {
    max-height: 2.5em;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.2em;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.col-span-2 {
    grid-column: span 2 / span 2;
}

.col-span-1 {
    grid-column: span 1 / span 1;
}

.col-span-3 {
    grid-column: span 3 / span 3;
}

.grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
}

/* .focus-visible\:.border-black:focus-visible {
    border-color: rgb(209 213 219 / var(--tw-ring-opacity))
} */
.calc-height {
    height: calc(100vh - 80px);
}

.borderInput {
    color: red;
}

.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.progress {
    width: 100%;
    -webkit-appearance: none;
    appearance: none;
    border: none;
    border-radius: 10px;
    height: 1rem;
}

.progress::-webkit-progress-bar {
    background-color: #f0f0f0;
}

.progress::-webkit-progress-bar,
.progress::-webkit-progress-value {
    border-radius: 10px;
}

.progress::-moz-progress-bar {
    border-radius: 10px;
}

.progress-orange::-webkit-progress-value {
    background: #FFA347;
}

.progress-blue::-webkit-progress-value {
    background: #3B82F6;
}

.progress-green::-webkit-progress-value {
    background: #12B76A;
}

.progress-purple::-webkit-progress-value {
    background: #8B5CF6;
}

.progress-sky::-webkit-progress-value {
    background: #06B6D4;
}

.hover\:bg-gray-200:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(233 233 233 / var(--tw-bg-opacity))
}

.activeColor {
    color: #12B76A;
    background: var(--status-status-success-light, #ECFDF3);

}

.DraftColor {
    color: #0891B2;
    background: var(--brand-brand-light, #EDFBFC);

}

.closedColor {
    color: #667085;
    background: var(--surface-surface-hover, #F2F4F7);

}

/* .select-items input[type="checkbox"]:checked {
    accent-color: #06B6D4;
} */

.input-container {
    position: relative;
    width: 100%;
}

.search-container {
    position: relative;

}

.input-container .search-icon {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    pointer-events: none;
}

.input-container .search-icon.people-dash {
    top: 30% !important;
}

.input-container input {
    padding-left: 2.5rem;
    /* Adjust to accommodate the icon */
}

.search-container .search-icon {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    pointer-events: none;
}

.search-container input {
    padding-left: 2.5rem;
    /* Adjust to accommodate the icon */
}

.Organisation {
    width: 432px;
    padding: 20px;
}

.organisationTitle {
    gap: 5em;
}

.OrganizationInput {
    width: 30em;
}

@media (max-width: 1250px) {
    .organisationTitle {
        gap: 10em;
    }
}

.treeOuter {
    justify-content: center;
}

.halfwidth {
    width: 50%;
}

.treeWrapper {
    gap: 30rem;
    border-bottom: 2px solid #71ADFF;
    /* padding-bottom: 19px; */
    /* margin-top: 3rem; */
}

.outermostdiv {}

.treeHeight {
    height: calc(100vh - 215px);
    overflow-y: auto;

    /* height: calc(100vh - 215px);
        overflow-y: auto;
        max-width: 1500px;
        width: 100%;
        margin: 0 auto;
        text-align: center;
        display: inline-block !important; */

}

/* .scrollable-list {
    height: 100vh;
    max-height: 650px;
    overflow-y: scroll;
} */

.scrollable-list {
    overflow-y: auto;
    height: calc(85vh - 177px);
    /* height: calc(100vh - 318px); */
}


.adddeduser {
    overflow-y: scroll;
    height: 100%;
    max-height: 45vh;
}

.adddeduser::-webkit-scrollbar {
    display: none;
}

.adddeduser input {
    margin-right: 10px;
}

.selectPeople {
    width: 450px;
}

.selectPeople .searchBox {
    height: 262px;
}

.selectPeople button {
    background: var(--surface-surface-disabled, #06B6D4);
    color: #fff;
}

.scrollable {
    width: 720px;
    height: 100%;
    max-height: 75vh;
    overflow-y: auto;
}

.addRoleWidth {
    width: 415px;
}

.buttonmargin {
    margin-bottom: 10px !important;
    margin-right: 5px !important;
}

.scrollable::-webkit-scrollbar {
    display: none;
}

.zvalue {
    z-index: 60 !important;
}

.contentHeight {
    height: calc(100vh - 200px);
    background-color: rgb(244, 243, 243);
}

.d-none {
    display: none;
}

.customHeight {
    height: calc(100vh - 78px);
}

.custom-vbar-legend {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding-left: 15%;
    padding-right: 15%;
    width: 100%;
    --dynamic-padding: 5%;
    padding-top: var(--dynamic-padding) !important;
    padding-bottom: var(--dynamic-padding) !important;
}

.custom-hbar-legend {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 20%;
}

.custom-v-legend {
    width: 80%;
    padding-left: 10px;
    padding-right: 10px;
}

.custom-v-legend li {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 10px;
}

.custom-v-legend span {
    display: flex;
    flex-direction: column;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.custom-v-legend #percentage {
    color: #667085
}

.custom-h-legend li {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 10px;
}

.custom-h-legend span {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.custom-h-legend #percentage {
    color: #667085
}

[x-cloak] {
    display: none !important;
}

.checkbx:checked {
    background-color: #06B6D4;
    border: 2px solid #06B6D4;
}

.checkbx:before {
    content: '';
    position: absolute;
    right: 50%;
    top: 50%;
    width: 5px;
    height: 10px;
    border: solid #FFF;
    border-width: 0 2px 2px 0;
    margin: -1px -1px 0 -1px;
    transform: rotate(45deg) translate(-50%, -50%);
}

.checkbx {
    position: relative;
    border: 2px solid lightgray;
    border-radius: 6px;
    cursor: pointer;
    vertical-align: text-top;
    width: 21px;
    height: 21.129px;
    flex-shrink: 0;
    -webkit-appearance: none;

}

/* add new plan step 1 */

.step1 {
    width: 480px;
}

.labelcolor {
    color: #667085;
}


.labelcolor::placeholder {
    color: #667085;
}

.Tenure {
    display: flex;
    /* justify-content: center;  */
    align-items: center !important;
    gap: 3px;
}

.Tenure img {
    height: 36px;
}

.right {
    right: 16px;
}

.left {
    left: 16px;
}

.modalscroll {
    height: 100%;
    max-height: 60vh;
    overflow-y: auto;
}

.mb-0 {
    margin-bottom: 0px;
}

.p-2 {
    padding: .5rem !important;
}

.p-0 {
    padding: 0px !important;
}

.mb-1 {
    margin-bottom: .25rem;
}

.modalscroll3 {
    height: 100%;
    max-height: 50vh;
    overflow-y: auto;
}

.addedTop {
    top: 10px;
}

.doughnut-chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    max-width: 50%;
    max-height: 80%
}

.vbar-chart-container {
    position: relative;
    width: 100%;
    height: 80%;
    max-width: 100%;
    max-height: 300px
}

.locationBar-chart-container {
    position: relative;
    width: 100%;
    height: 65%;
    max-width: 100%;
    max-height: 300px
}

.division-vbar-chart-container {
    position: relative;
    width: 78%;
    height: 100%;
    max-width: 100%;
    max-height: 200px;
}

.hbar-chart-container {
    position: relative;
    width: 80%;
    height: 90%;
    max-width: 100%;
    max-height: 300px
}

.top5-hbar-chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 200px
        /* adjust as needed */
}

.pie-chart-container {
    padding: 15px;
}

.chart-heading {
    color: #667085
}

.notifications-container {
    height: 88%;
}

.notification-container:hover {
    background-color: #F2F4F7
}

.notification {
    width: 80%
}

.notification-icon {
    width: 10%;
}

.notification-header {
    height: 10%;
}

.drawer {
    width: 20vw;
    height: 100vh;
    padding: 10px
}

.table-header {
    background-color: #EAECF0
}

.table-column-name {
    color: #667085
}

/* gender selection css */

.donate-now {
    list-style-type: none;
    display: flex;
    justify-content: space-between;
    /* margin: 25px 0 0 0; */

}

.donate-now li {
    /* float: left; */
    width: 140px;
    height: 35px;
    position: relative;
}

.donate-now label,
.donate-now input {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.donate-now input[type="radio"] {
    opacity: 0.01;
    z-index: 100;
}

.donate-now input[type="radio"]:checked+label,
.donate-now .Checked+label {
    background: #EDFBFC;
    color: #06B6D4;
    border: 1px solid #06B6D4 !important;

}

.donate-now label {
    padding: 5px;
    padding-inline: 10px;
    border: 0.5px solid #CCC;
    border-radius: 10px;
    cursor: pointer;
    z-index: 90;
}

.donate-now label:hover {
    background: #DDD;
}

.toggle input:checked[type='checkbox']+span {
    left: 1.5rem;
    top: 0;
    background-color: blue;
}




/* Hide the default checkbox */
.toggle-input {
    appearance: none;
    width: 0;
    height: 0;
    position: absolute;
    opacity: 0;
}

/* Slider background */
.toggle-slider {
    position: absolute;
    top: 0;
    right: 0;
    /* left: 5; */
    width: 2.5rem;
    height: 12px;
    background-color: lightgray;
    border-radius: 9999px;
    transition: background-color 0.2s;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
}

/* Slider circle */
.toggle-slider::before {
    content: '';
    position: absolute;
    top: 0;
    /* left: 5; */
    width: 13px;
    height: 13px;
    background-color: #06B6D4;
    border-radius: 9999px;
    transition: transform 0.2s;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    transform: scale(1.5);
}

/* Checked state for the toggle switch */
.toggle-input:checked+.toggle-slider {
    background-color: skyblue;
}

/* Move the slider circle to the right when checked */
.toggle-input:checked+.toggle-slider::before {
    transform: translateX(1.5rem) scale(1.5);
}

/* Disabled state */
.toggle-input:disabled+.toggle-slider {
    background-color: skyblue;
}


/* add internal candidiadte modal css */
.choices__list--dropdown,
.choices__list[aria-expanded] {
    height: 200px;
    border-radius: 10px;
}

.modal-content {
    width: 480px;
}

.pipeline-modal-content {
    width: calc(100vw - 200px);
    height: max-content;
}

.skill-container {
    background-color: #F2F4F7
}

.table-dropdown {
    width: max-content;
}

.modal-content2 {
    width: 642px;

}

.choices[data-type*=select-one] .choices__inner {
    background-color: white;
    border-radius: 10px;
}

.choices__list--dropdown,
.choices__list[aria-expanded] {

    z-index: 1000 !important;

}

.uploadCandidates {
    width: 780px;
    height: 303px;
}

.pb-15 {
    padding-bottom: 3rem
}

.gap-3 {
    gap: 0.75rem
        /* 12px */
    ;
}

.z-99 {
    z-index: 99;
}

.disabled {
    cursor: not-allowed;
}

.disable {
    cursor: not-allowed;
    background: #b1aeae47;
}

.selectedBg {
    background: var(--surface-surface-hover, #eef3fa);
    color: #667085;
}

/* three dot modal css */

/* Button styles */
/* #dropdownDefaultButton {
    color: white;
    font-weight: 500;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    text-align: center;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
} */
/* Dropdown menu styles */
/* .dropdown-menu {
    z-index: 10;
    display: none;
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 11rem;
    top: 25px;
    right: 10px;
} */


#dropdownDefaultButton:focus {
    outline: none;
}

/* .dropdown-menu {
    background-color: #374151;
    display: none;
}

/* Dropdown menu item styles */
.dropdown-menu ul {
    padding: 0.5rem 0;
    font-size: 0.875rem;
    color: #374151;
}

.dark .dropdown-menu ul {
    color: #E5E7EB;
}

.dropdown-menu li {
    list-style: none;
}

/* #dropdown-container:active ul {
    display: block;
} */

.dropdown-menu:active ul {
    display: block;
}

.dropdown-menu li a {
    display: block;
    padding: 0.5rem 1rem;
    color: inherit;
    text-decoration: none;
}

.dropdown-menu a:hover {
    background-color: #F3F4F6;
}

.dark .dropdown-menu li a:hover {
    background-color: #4B5563;
    color: white;
}

/* 
#dropdownDefaultButton:focus + .dropdown-menu,
#dropdownDefaultButton:active + .dropdown-menu {
    display: block;
} */

.pb-10 {
    padding-bottom: 1.5rem
}

.tagsWidth {
    width: 220px;
}

.upload_cross {
    margin-top: -25px;
    margin-left: 488px;
}

/* my organzation drag drop div */

.HRInfo {
    border: 1px solid var(--brand-brand-primary, #06B6D4);
    /* margin-left: 10px; */
}

.HRInfo-red {
    border: 1px solid #f87171;
    /* margin-left: 10px; */
}

.borderTop {
    border-top: 4px solid var(--secondary-secondary-violet, #8B5CF6);
    border-radius: 8px;
}

.borderTop-red {
    border-top: 4px solid #F87171;
    border-radius: 8px;
}

.dragIcon {
    bottom: -12px;
    left: 46%;
    width: 21px;
    height: 23px;
    text-align: center;
    bottom: -15px;
    z-index: 5;
}

.topDragIcon {
    top: -7px;
    left: 45%;
    width: 21px;
    height: 23px;
}

.lefttop {
    top: -71px;
    left: 50%;
}


.righttop {
    top: -74px;
    right: 50%;
}


.childwrapper {
    margin-top: 4.5rem;
    gap: 2rem;
}


/* my organization three dots modal */

.scrollable-list .dropdown-menu2 {
    z-index: 999 !important;
    display: none;
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 12rem;
    top: 28px;
    left: 10px;
    overflow-y: auto;
}


.dropdown-menu2 {
    z-index: 999 !important;
    display: none;
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 12rem;
    top: 28px;
    right: 10px;
    overflow-y: auto;
}

.dark .dropdown-menu2 {
    background-color: #374151;
}

/* Show dropdown when button is clicked */
#dropdownDefaultButton2:focus+#dropdown2 {
    display: block;
}

/* Dropdown menu item styles */
.dropdown-menu2 .ul {
    /* padding: 0.5rem 0; */
    font-size: 0.875rem;
    color: #374151;
}

.dark .dropdown-menu2 .ul {
    color: #E5E7EB;
}

.dropdown-menu2 .li {
    list-style: none;
}

.dropdown-menu2 .li a {
    display: block;
    /* padding: 0.5rem 1rem; */
    color: inherit;
    text-decoration: none;
}

.dropdown-menu2 .li a:hover {
    background-color: #F3F4F6;
}

.dark .dropdown-menu2 .li a:hover {
    background-color: #4B5563;
    color: white;
}

/* Hide dropdown menu by default */
.dropdown-menu2 {
    display: none;
}

/* Show dropdown when button is clicked */
#dropdownDefaultButton2:focus+#dropdown2,
.dropdown-menu2:hover {
    display: block;
}


#successMessage {
    background-color: #06B6D4;
    /* background-color: red; */
    margin-right: 15px;
}

#errorMessage {
    background-color: #871c1c;
    /* background-color: red; */
    margin-right: 15px;
}

/* plan designer page css */

.planDesignerHeight {

    height: calc(100vh - 77px);
    overflow-y: scroll;

}


@media (min-width: 1280px) {
    .col2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .flexRow {
        flex-direction: row;
    }
}


.navbg {
    background: var(--brand-brand-navigation, #083344);
}

/* master page */
.masterTable .dropdown-menu {
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 11rem;
    /* for three */
    /* top: -53px;
     */
    top: 0;

    /* for 4 */
    /* top: -70px; */
    right: 30px;
    /* top: -53px;
    right: 30px; */
    display: none;
}

.masterTable tr:not(:first-child) .dropdown-menu.five {
    top: -90px !important;
}

/* .masterTable tr:not(:first-child) .dropdown-menu.four {
    top: -70px !important;
} */

.masterTable .dropdown-menu::after {
    content: '';
    position: absolute;
    top: 10px;
    right: -10px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid gray;
}

.masterTable tr:first-child .dropdown-menu {
    top: 0px;
    right: 24px;
}

/* .masterTable tr:first-child .dropdown-menu::after {
    top: 0.5rem;
} */

.masterTable {

    position: relative;
    width: 100%;
    max-width: 100vw;
    overflow-x: auto !important;
    height: calc(100% - -6px);

}


/* job page */
.jobTable .dropdown-menu {
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 11rem;
    /* for three */
    top: -53px;

    /* for 4 */
    /* top: -70px; */
    right: 30px;
    /* top: -53px;
    right: 30px; */
    display: none;
}

/* .jobTable .dropdown-menu.four {
    top: -70px !important;
} */
/* 
.jobTable .dropdown-menu.five {
    top: -90px !important;
} */
.jobTable tr:not(:first-child) .dropdown-menu.five {
    top: -90px !important;
}

.jobTable tr:not(:first-child) .dropdown-menu.four {
    top: -70px !important;
}

/* add this one */

.jobTable .dropdown-menu::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -10px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid gray;
}

.jobTable tr:first-child .dropdown-menu {
    top: 0px;
    right: 30px;
}

.jobTable tr:first-child .dropdown-menu::after {
    top: 0.5rem;
}

/* job page  end*/
.dropdown-menu {
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 11rem;
    top: 0px;
    right: 20px;
    /* top: -53px;
    right: 30px; */
    display: none;
    z-index: 10;
}

/* JobPeopleTable from jobpage */
.JobPeopleTable .dropdown-menu {
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 11rem;
    top: -53px;
    right: 30px;
    display: none;
}

.JobPeopleTable .dropdown-menu::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -10px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid gray;
}

.JobPeopleTable tr:first-child .dropdown-menu {
    top: 0px;
    right: 30px;
}

.JobPeopleTable tr:first-child .dropdown-menu::after {
    top: 0.5rem;
}

.jobTable tr:not(:first-child) .dropdown-menu.five {
    top: -90px !important;
}

/* JobPeopleTable from jobpage end  */



/*  peopledashboardTable from plans page*/
.peopledashboardTable tr:first-child .dropdown-menu {
    top: 0px;
    right: 34px;
}

.peopledashboardTable .dropdown-menu {
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 11rem;
    /* top: 0px;
    right: 20px; */
    top: -53px;
    right: 34px;
    display: none;
}

.peopledashboardTable .dropdown-menu::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -10px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid gray;
}

.peopledashboardTable tr:first-child .dropdown-menu::after {
    top: 0.5rem;
}

/*  peopledashboardTable from plans page end*/


.SuccessionTable .dropdown-menu {
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 11rem;
    /* top: 0px;
    right: 20px; */
    top: -56px;
    right: 25px;
    display: none;
}

.SuccessionTable .dropdown-menu::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -10px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid gray;
}


.SuccessionTable tr:first-child .dropdown-menu {
    top: 0px;
    right: 25px;
}

.SuccessionTable tr:first-child .dropdown-menu::after {
    top: 0.5rem;
}

.dark .dropdown-menu {
    background-color: #374151;
}

/* Show dropdown when button is clicked */
/* #dropdownDefaultButton:focus+#dropdown {
    display: block;
} */

/* Dropdown menu item styles */
.dropdown-menu ul {
    padding: 0.5rem 0;
    font-size: 0.875rem;
    color: #374151;
}

.dark .dropdown-menu ul {
    color: #E5E7EB;
}

.dropdown-menu li {
    list-style: none;
}


.dropdown-menu li a {
    display: block;
    padding: 0.5rem 1rem;

    color: inherit;
    text-decoration: none;
}

.dropdown-menu li a:hover {
    background-color: #F3F4F6;
}

.dark .dropdown-menu li a:hover {
    background-color: #4B5563;
    color: white;
}

.dropdown-container:focus-within .dropdown-menu {
    display: block;
}

.dropdown-menu {
    display: none;
}

/* .peopledashboardTable tr:nth-last-child(-n+2) .dropdown-menu {
    top: -131px; 
    right: 17px; 
} */

.dropdown-container:active .dropdown-menu,
.dropdown-container:focus-within .dropdown-menu {
    display: block;
}

.navmargin {
    padding-right: 15px;
}

.text-danger {
    color: #F04438;
}

.deleteOrg {
    width: 480px;

}

.deleteOrg .crossTop {
    top: 10px;
}

/* Style the progress bar */
.progress-bar {
    appearance: none;
    width: 200px;
    height: 12px;
}

/* WebKit-based browsers */
.progress-bar::-webkit-progress-bar {
    background-color: #e5e7eb;
    border-radius: 5px;
}

.progressColor1::-webkit-progress-value {
    background-color: #FFA347;
    border-radius: 5px;
}

/* Mozilla Firefox */
.progressColor1::-moz-progress-bar {
    background-color: #FFA347;
    border-radius: 5px;
}

.progressColor2::-webkit-progress-value {
    background-color: #3B82F6;
    border-radius: 5px;
}

/* Mozilla Firefox */
.progressColor2::-moz-progress-bar {
    background-color: #3B82F6;
    border-radius: 5px;
}

img.absolute.right-2.crossTop.w-auto.cursor-pointer {
    margin-top: -3px;
    margin-right: 6px;
}

.contentHeight2 {
    height: calc(100vh - 212px);
    overflow-y: scroll;
}

.sortBy-width {
    width: 12%;
}

.advanceSearch-width {
    width: 15%;
}

.advanced_search_drawer {
    height: calc(100vh - 40px)
}

.bg-Color {
    background-color: rgb(244, 243, 243);
}

/* .grid-rows {
    grid-template-rows: repeat(3, minmax(0, 1fr));
} */

.pagination {
    bottom: -38px;
}

.pagination>div>nav>div:nth-of-type(2) {
    display: flex !important;
    justify-content: space-between !important;
}

.containerHeight {
    height: 200px;
}

.blueBalls {
    width: 10px;
    height: 10px;
}

.border-b-cyan-500 {
    border-bottom: 2px solid #06b6d4;
}

.advanced-search-clear {
    padding-left: 45px;
    padding-right: 45px;
}

.advanced-search-submit {
    padding-left: 28px;
    padding-right: 28px;
}

.delete-button:hover {
    background-color: #F04438
}

.delete-modal-action-container {
    padding-top: 20px;
}


/* jobs page css */

.paddingRight {
    padding-right: 3rem;
}

.tableEdges {
    border-radius: 5rem;
}

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}



.TableOuter {
    height: calc(100vh - 220px);
}

.swal2-html-container {
    margin: 1.5rem 0px 1px 0px !important;
}

.swal2-html-container .swal2-actions {
    display: flex !important;

}

.btnsWidth {
    width: 14rem !important;
}

.btnsmargin {
    margin-right: 10px !important;
}

div:where(.swal2-container) button:where(.swal2-close):hover {
    color: #ccc !important;
}

.borderRadius {
    border-radius: 20px;
    border-color: lightgray;
}

.choices__input {
    background-color: unset !important;
}

.overflow-auto {
    overflow: auto;
}

.TableOuter {
    height: calc(100vh - 220px);
    /* overflow-x: auto !important; */
}

.MasterTableOuter {
    height: calc(100vh - 220px);
}

.masterContainer {
    margin: 23px;
}

.table_wrapper {
    /* position: relative;
    display: table;
    overflow-x: auto !important;
    max-width: 100% !important;
    width: 100% !important; */
    position: relative;
    width: 100%;
    max-width: 100vw;
    overflow-x: auto !important;
    height: calc(100% - 50px);
}

.table_wrapper.people-dashboard {
    height: calc(100% - 70px) !important;
}

.table_wrapper table {
    position: relative;
    display: table;
    min-width: max-content;
    overflow-x: auto !important;
}

/* organization chart */

.person {
    border: 1px solid black;
    padding: 10px;
    min-width: 150px;
    background-color: #FFFFFF;
    display: inline-block;
}

.person.female {
    border-color: #F45B69;
}

.person.male {
    border-color: #456990;
}

.person div {
    text-align: center;
}

.person .name {
    font-size: 16px;
}

/* Tree */

.parent>ul>li>div {
    margin-right: 2px;
}

.parents {
    margin-left: 61px;
}

.anotherParent {
    position: relative;
    z-index: -1;
}

.istli {
    /* position: relative;
    z-index: 10; */
}

.mr-6 {
    margin-right: 1rem;
}

.textRed {
    color: red;
}

/* jobs page pagination */

.paginationContainer>select {
    padding-left: 5px;
    padding-right: 20px
}

.paginationContainer {
    margin-right: 2rem;
}

.paginationContainer>div>div>nav>div:nth-of-type(2) {
    /* Your styles here */
    margin-right: 2rem;
}


.Onactive {
    background-color: #06B6D4;
    color: white;
}

.overflow-y-auto {
    overflow-y: auto;
}

.table_wrapper thead,
.table_wrapper thead th {
    position: -webkit-sticky;
    /* For Safari */
    position: sticky;
    top: -1px;
    z-index: 1;
    /* border-bottom: 2px solid lightgrey; */
}

#jobsTable .dropdown-menu {
    right: 53px !important;
}

/* view Report page css */

.mainouter {
    padding-bottom: 2.5rem;
}

.istSection {
    height: 80vh;
    /* background-size: 100%; */
    background-size: cover;
    background-position: center;
    margin-top: 2.5rem;
    background-image: url('/images/MapBackground.png');
    background-repeat: no-repeat;
}

.istSection .istSectionH2 {
    font-size: 55px;
    line-height: 87.14px;
    text-align: left;
}

.istSection>.istdiv {
    bottom: 6rem;
    left: 5rem;
}

@media (max-width: 1250px) {
    .istSection>.istdiv {
        bottom: 3rem;
    }
}

@media (max-width: 1350px) {

    .istSection .istSectionH2 {
        font-size: 50px;
        line-height: 50px;
    }
}


.overview {
    background-image: url('/images/overviewBG.png');
    height: 388px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.overview>img {
    position: absolute;
    right: 5rem;
    top: 5rem;
}

.overviewPara {
    height: 392px;
    gap: 10.5rem;
}

.overviewPara>p {
    width: auto;
}

@media (max-width: 1250px) {
    .overviewPara {
        gap: 2rem;
        /* Gap for screens below 1024px */
    }

}

@media (min-width: 1250px) {
    .overviewPara {
        flex-direction: row;
    }
}

.LongListed {
    background-image: url('/images/longlistBG.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.LongListedH1 {
    font-size: 55px;
    font-weight: 500;
    line-height: 77.45px;
    text-align: left;
    margin-top: 6rem;
    margin-left: 2rem;

}

.LongListedIstDiv {
    margin-top: 5rem;
    margin-bottom: 8rem;
    gap: 2rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 1150px) {
    .LongListedIstDiv {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }


}


@media (max-width: 1350px) {
    .LongListedH1 {
        font-size: 45px;
        line-height: 50px;
    }
}


@media (max-width: 1150px) {
    .LongListedH1 {
        font-size: 30px;
        line-height: 30px;
    }
}


.ShortListed {
    /* background-image: url('/images/WhiteBlurBg.png'); */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.ShortListedHeading {
    padding-inline: 7rem;
    font-size: 54px;
    font-weight: 500;
    line-height: 77.45px;
    text-align: left;
    padding-block: 4rem;
}

@media (max-width: 1350px) {
    .ShortListedHeading {
        font-size: 45px;
        line-height: 50px;
    }
}

@media (max-width: 1150px) {
    .ShortListedHeading {
        font-size: 30px;
        line-height: 30px;
    }
}


.ShortListedIstDiv {
    gap: 2rem;
    margin-bottom: 8rem;
    margin-inline: 8rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 1150px) {
    .ShortListedIstDiv {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

.tableSection {
    /* margin-inline: 3.5rem; */
    background-image: url('/images/WhiteBlurBg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}


.tableHeading {
    padding-inline: 5rem;
    font-size: 54px;
    font-weight: 500;
    line-height: 77.45px;
    text-align: left;
    padding-block: 4rem;
}

@media (max-width: 1350px) {
    .tableHeading {
        font-size: 35px;
        line-height: 50px;
    }
}



.tableOuter {
    margin-inline: 5rem;
    margin-bottom: 8rem;
    margin-top: 2rem;
}

.istTR {
    background: var(--surface-surface-background, #F9FAFB);
    color: #667085;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    text-align: left;
}

.BodyTR {
    /* font-family: Inter; */
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: left;
    color: #101828;
}

.CandidateDetails {
    /* margin-inline: 3.5rem; */
    background-image: url('/images/WhiteBlurBg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    /* margin-bottom: 5rem; */
    /* height: 100%;    */
    padding-bottom: 3rem;
}

.candidateHeading {
    /* padding-inline: 3rem; */
    font-size: 54px;
    font-weight: 500;
    line-height: 77.45px;
    text-align: left;
    padding-block: 4rem;
}

@media (max-width: 1350px) {
    .candidateHeading {
        font-size: 35px;
        line-height: 50px;
    }
}

.CandidateDetails .istH2 {
    /* font-family: Inter; */
    font-size: 20px;
    font-weight: 600;
    line-height: 40px;
    text-align: left;
    color: #667085;
}

.CandidateDetails .detail {
    /* font-family: Inter; */
    font-size: 18px;
    font-weight: 400;
    line-height: 38.73px;
    text-align: left;
    color: #101828;
}

.cards {
    margin-inline: 3rem;
    gap: 2rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 1250px) {
    .cards {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

.cardContainer {
    padding-bottom: 3rem;
    padding-right: 3rem;
    padding-top: 1rem;
    /* width: 15rem; */
    /* margin-inline: 2rem; */
}

.cardContainer3 {
    /* width: 30rem; */
    /* grid-column: span 2 / span 2; */
}

@media (min-width: 1250px) {
    .cardContainer3 {
        grid-column: span 2 / span 2;
    }
}

.Summary {
    margin-inline: 3rem;
    padding: 1rem;
}

.ParaH2 {
    font-size: 15px;
    font-weight: 400;
    text-align: left;
    color: #101828;
}

.career-history-box {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin: auto;
    position: relative;
}

.career-history-box h2 {
    color: #667085;
    margin-bottom: 20px;
    font-weight: 600;
    font-size: 24px;
}

.timeline {
    display: flex;
    flex-direction: row;
    position: relative;
    justify-content: space-around;
}

.timeline {
    border-top: 2px solid #EAECF0;

}

.timeline-dot {
    width: 10px;
    height: 10px;
    background-color: #06B6D4;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 50%;
    top: -0.3rem;
    transform: translateX(-50%);
}

@media (max-width: 1360px) {
    .timeline {
        flex-direction: column;
        border-top: none;
        border-left: 2px solid #EAECF0;
    }

    .timeline-dot {
        top: 50%;
        left: -0.35rem;
        transform: translateY(-50%);
    }
}

.timeline-item {
    position: relative;
}

.timeline-content {
    padding: 15px;
    border-radius: 8px;
    flex: 1;
    position: relative;
}

.timeline-content h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.timeline-content p {
    margin: 5px 0 5px;
    font-size: 16px;
    color: #666;
}

.timeline-content span {
    font-size: 14px;
    color: #999;
}

.syan-bl-3 {
    border-left: 3px solid #06B6D4;
}

.switches-container {
    width: 16rem;
    position: relative;
    display: flex;
    padding: 0;
    position: relative;
    background: #06B6D4;
    line-height: 3rem;
    border-radius: 3rem;
    margin-left: auto;
    margin-right: auto;
}

/* input (radio) for toggling. hidden - use labels for clicking on */
.switches-container input {
    visibility: hidden;
    position: absolute;
    top: 0;
}

/* labels for the input (radio) boxes - something to click on */
.switches-container label {
    width: 50%;
    padding: 0;
    margin: 0;
    text-align: center;
    cursor: pointer;
    color: white;
    font-weight: 500;
}

/* switch highlighters wrapper (sliding left / right) 
    - need wrapper to enable the even margins around the highlight box
*/
.switch-wrapper {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 50%;
    padding: 0.15rem;
    z-index: 3;
    transition: transform .5s cubic-bezier(.77, 0, .175, 1);
    /* transition: transform 1s; */
}

/* switch box highlighter */
.switch {
    border-radius: 3rem;
    background: white;
    height: 100%;
}

.switch div {
    width: 100%;
    text-align: center;
    opacity: 0;
    display: block;
    color: #06B6D4;
    transition: opacity .2s cubic-bezier(.77, 0, .175, 1) .125s;
    will-change: opacity;
    position: absolute;
    top: 0;
    left: 0;
}

/* slide the switch box from right to left */
.switches-container input:nth-of-type(1):checked~.switch-wrapper {
    transform: translateX(0%);
}

/* slide the switch box from left to right */
.switches-container input:nth-of-type(2):checked~.switch-wrapper {
    transform: translateX(100%);
}

/* toggle the switch box labels - first checkbox:checked - show first switch div */
.switches-container input:nth-of-type(1):checked~.switch-wrapper .switch div:nth-of-type(1) {
    opacity: 1;
}

/* toggle the switch box labels - second checkbox:checked - show second switch div */
.switches-container input:nth-of-type(2):checked~.switch-wrapper .switch div:nth-of-type(2) {
    opacity: 1;
}

.text-uppercase {
    text-transform: uppercase;
}


/* square radio button */

.square-radio input[type="radio"] {
    display: none;
}

/* Create a custom radio button */
.square-radio input[type="radio"]+label:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #06B6D4;
    border-radius: 4px;
    /* Make it a square with rounded corners */
    margin-right: 8px;
    vertical-align: middle;
    cursor: pointer;
}

/* Change the color and size when checked */
.square-radio input[type="radio"]:checked+label:before {
    background-color: #06B6D4;
    box-shadow: inset 0 0 0 4px white;
}

/* Ensure the label text is aligned properly */
.square-radio label {
    cursor: pointer;
    vertical-align: middle;
}

/* people dashboard */

.outerTable {
    position: relative;
    width: 100%;
    max-width: 100vw;
    overflow-x: auto !important;
    margin-bottom: 1rem;
}

/* .ContentContainer {
    margin-bottom: 2rem !important;
} */

.redlinessReaddy {
    color: #12B76A;
    background: var(--status-status-success-light, #ECFDF3);

    width: 4rem;
    text-align: center;

}


.placeholder\:text-xs::placeholder {
    font-size: 0.75rem
        /* 12px */
    ;
    line-height: 1rem
        /* 16px */
    ;
}

.w-20p {
    width: 20%;
}

.w-40p {
    width: 40%;
}


.pl {

    padding-left: 5.25rem;
    margin-top: 1rem;
    padding-right: 1rem;
}

.h-120 {
    height: 30rem
}

.mb-15 {
    margin-bottom: 3rem;
}

.function-card {
    background-color: #e5e7eb;
    /* Tailwind CSS equivalent for gray-200 */
    overflow: hidden;
    /* Hides overflow */
    text-overflow: ellipsis;
    /* Adds ellipsis for overflow */
    width: 100%
}

.as-footer {
    bottom: 20px;
}

.enable-loading {
    display: block !important;
}

.px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
}

.bulkCross {
    right: 15px;
    top: 15px;
}

.choices__list--dropdown .choices__list,
.choices__list[aria-expanded] .choices__list {
    max-height: 160px !important;
}

[aria-current="page"] .relative {
    background-color: #06B6D4 !important;
    /* Change to your desired color */
    color: white !important;
    /* Change to your desired text color */
}

.dc-table-container {
    height: 450px;
}

.scoreField {
    width: 13%;
}

.choices[data-type*=select-multiple] .choices__inner,
.choices[data-type*=text] .choices__inner {
    cursor: pointer !important;
}

.choices__inner {

    background-color: #fff !important;

}

.careerHistoryEndDate {
    width: 86%;
}

.toastr-full-width {
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    /* margin-left:  !important; */
    margin-right: 0 !important;
    border-radius: 0 !important;
}

.scoreBtn {
    width: 155px;
    font-size: 12px;
}



.addBtn {
    width: 156px;
    font-size: 12px;
}

.moverRow,
.moverRow td,
.moverRow td h3.table-h3 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity))
}

.d-ruby {
    display: ruby;
}

.bell-icon {
    top: -10px;
    position: relative;
}

.white-bg-2px {
    border: 2px solid white;
}

.ml-0 {
    margin-left: 0px !important;
}

/* Hide the chart initially */
.chart-container {
    opacity: 0;
    transition: opacity 0.4s ease;
}

.w-215p {
    width: 215px;
}

.w-412p {
    width: 412px;
}

.max-h-500p {
    max-height: 500px;
}

.info-badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    color: #1e3a8a;
    background-color: #dbeafe;
    border-radius: 9999px;
    line-height: 1;
}

.success-badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    background-color: #0bab4e;
    border-radius: 9999px;
    line-height: 1;
}

.bell-icon {
    transition: transform 0.3s ease;
}

.bell-icon:hover {
    transform: scale(1.2);
}

/* yes no toggle */

.switch-toggle {
    --width: 140px;
    --height: 45px;
    --offset: 2px;
    --radius: 8px;
    position: relative;
    width: var(--width);
    height: var(--height);
    padding: var(--offset);
    background: #F2F4F7;
    border-radius: var(--radius);
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    font-style: normal;

    input[type="checkbox"] {
        cursor: pointer;
        position: absolute;
        inset: 0;
        appearance: none;
        z-index: 2;

        &:checked {
            +label.switch-toggle-label {
                &:before {
                    translate: 100% 0;
                }

                span {
                    &:nth-child(1) {
                        color: gray;
                    }

                    &:nth-child(2) {
                        color: #06B6D4;
                    }
                }
            }
        }

        &+label.switch-toggle-label {
            position: absolute;
            inset: var(--offset, 0);
            padding: 10px 0;
            display: block;
            user-select: none;
            pointer-events: none;
            display: grid;
            gap: 2px;
            grid-auto-flow: column;
            grid-auto-columns: 1fr;
            place-items: center;

            &:before {
                content: "";
                position: absolute;
                width: 50%;
                inset: 0;
                background: #fff;
                border-radius: calc(var(--radius) - var(--offset));
                box-shadow: 0px 10px 20px 0px rgba(16, 39, 68, 0.1);
                translate: 0 0;
                transition: translate 250ms cubic-bezier(0.93, 0.26, 0.07, 0.69);
            }

            span {
                position: relative;
                transition: 200ms linear;

                &:nth-child(1) {
                    color: #06B6D4;
                    font-size: 14px;
                }

                &:nth-child(2) {
                    color: gray;
                    font-size: 14px;
                }
            }
        }
    }
}

.critreaDelete {
    padding-block: 10px !important;
}

.padding20 {
    padding-bottom: 16px !important;
}

.creteriaList .mt-3:last-child .padding20 {
    border-bottom: 0 !important;
}

.editAssessment .innerEditAssessment:last-child .border-b {
    border: none !important;
}

.custom-textarea {
    resize: none;
    /* Disable dragging */
    overflow: hidden;
    /* Hide any scrollbars */
}

/* 9boxx gridd below css */

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
    --tw-translate-y: 1rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
    --tw-rotate: 90deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.left-11 {
    left: 2.75rem;
}

.x-axis {
    bottom: -4px;
    background: #EAECF0;
    height: 2px;
    left: 5.75rem;
    right: 71px;
}

@media (max-width: 1440px) {
    .x-axis {
        left: 3.75rem;
    }
}


@media (max-width: 1280px) {
    .x-axis {
        left: 2.75rem;
    }
}

.y-axis {
    width: 2px;
    background: #EAECF0;
    top: 32px;
    left: 63px;
    bottom: 19px;
}

@media (max-width: 1700px) {
    .y-axis {
        left: 54px;
    }
}

@media (max-width: 1440px) {
    .y-axis {
        left: 32px;
    }
}


@media (max-width: 1280px) {
    .y-axis {
        left: 23px;
    }
}



.bottom-8 {
    bottom: -2rem;
}

.top-12 {
    top: 9rem;
}

.ml-8 {
    margin-left: 2rem;
}

.py-9 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.left-16 {
    left: 4.5rem;
}

@media (max-width: 1700px) {
    .left-16 {
        left: 4rem;
    }
}

@media (max-width: 1440px) {
    .left-16 {
        left: 2rem;
    }
}


@media (max-width: 1280px) {
    .left-16 {
        left: 2rem;
    }
}


.low {
    bottom: -6px;
    left: 55px;
}

@media (max-width: 1440px) {
    .low {
        left: 25px;
    }
}

@media (max-width: 1280px) {
    .low {
        left: 14px;
    }
}

.y-high {
    left: 53px;
    top: 7px;
}

@media (max-width: 1700px) {
    .y-high {
        left: 43px;
    }
}

@media (max-width: 1440px) {
    .y-high {
        left: 21px;
    }
}

@media (max-width: 1280px) {
    .y-high {
        left: 13px;
    }
}

.x-high {
    right: 32px;
    bottom: -9px;
}

@media (max-width: 1280px) {
    .x-high {
        right: 2px;
    }
}

.boxSize {
    /* width: 158px; */
    height: 80px;
    padding: 8px 0px 0px 0px;
    font-size: 15px;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
}

@media (min-width: 1024px) {
    .boxSize {
        /* width: 97px; */
        height: 80px;
        padding: 8px 0px 0px 0px;
        font-size: 15px;
        display: flex;
        justify-content: center;
        align-items: center;

    }
}

@media (min-width: 1280px) {
    .boxSize {
        /* width: 144px; */
        height: 80px;
        padding: 8px 0px 0px 0px;
        font-size: 15px;
        display: flex;
        justify-content: center;
        align-items: center;

    }
}

@media (min-width: 1600px) {
    .boxSize {
        /* width: 158px; */
        height: 80px;
        padding: 8px 0px 0px 0px;
        font-size: 15px;
        display: flex;
        justify-content: center;
        align-items: center;

    }
}

.orangeBG {
    background: #FFA347;
}

.grayLightBorder {
    border: 1px solid #EAECF0;
}

.top-1 {
    top: 0.5rem;
}

.afterCheckBorder {
    border: 1.5px solid #06B6D4;
}

.afterBG {
    background: #EDFBFC;
}


.Blue-radio {
    appearance: none;
    -webkit-appearance: none;
    background-color: white;
    border: 2px solid #06B6D4;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    outline: none;
    cursor: pointer;
    display: inline-block;
    /* Ensure it behaves like a regular block-level element */
    vertical-align: middle;
    /* Align vertically */
}

.Blue-radio:checked::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #06B6D4;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* Center the circle horizontally and vertically */
}

.max-w-4xl {
    max-width: 39rem
}

.ready {
    background-color: #008000;
    margin: 0px 5px 0px 0px;
    border-radius: 10px;
}

.notReady {
    background-color: #ff0000;
    border-radius: 10px;
    margin: 0px 5px 0px 0px;


}

.nearlyReady {
    background-color: #FFBF00;
    border-radius: 10px;

}

/* gender selection css */

.readiness {
    list-style-type: none;
    display: flex;
    justify-content: space-between;
    /* margin: 25px 0 0 0; */

}

.readiness li {
    /* float: left; */
    width: 140px;
    height: 35px;
    position: relative;
}

.readiness label,
.readiness input {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.readiness input[type="radio"] {
    opacity: 0.01;
    z-index: 100;
}

.readiness input[type="radio"]:checked+label,
.readiness .Checked+label {

    border: 4px solid #00FF00 !important;

}

.readiness label {
    padding: 5px;
    padding-inline: 10px;
    border: 0.5px solid #CCC;
    border-radius: 10px;
    cursor: pointer;
    z-index: 90;
}

.readiness label:hover {
    background: #DDD;
}

.col-span-7 {
    grid-column: span 7 / span 7;
}


.col-span-1 {
    grid-column: span 1 / span 1;
}

.grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
}

.y-axis {
    background-color: #EAECF0;
    height: 0.2rem;
    /* 8px */
    ;
}

.mx-3 {
    margin-left: 0.75rem;
    margin-right: 0.75rem;
}

.xaxiss {
    width: 100%;
    max-width: 100% !important;
    min-width: 85%;
}
/* Limit the width of the cell */
.description-cell {
    max-width: 250px; /* Set the desired max width */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Apply truncation to the span */
.description-text {
    display: inline-block;
    max-width: 100%; /* Ensure it respects the parent width */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}

/* Tooltip styling */
.tooltip {
    position: relative;
    cursor: pointer;
}

.tooltip:hover::after {
    content: attr(title);
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.75);
    color: white;
    padding: 5px;
    border-radius: 4px;
    white-space: normal;
    max-width: 200px;
    z-index: 10;
}

.select2-selection {
    height: 38px !important;
    border-color: rgb(209 213 219) !important;
    border-radius: 6px !important;
}

.select2-selection__rendered{
    height: 38px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 38px !important;
}
.select2-search__field:focus{
    outline: none;
    box-shadow: none;
}
/* Define your custom gray button styles */
.bg-gray-500 {
    background-color: #6b7280; /* Gray color */
}

.bg-gray-500:hover {
    background-color: #4b5563; /* Darker gray for hover effect */
}

.cursor-not-allowed {
    cursor: not-allowed;
}
/* Loader overlay for Livewire actions */
.loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* Ensure it's above other elements */
}

/* Spinner styling */
.spinner {
    width: 160px;
    height: 160px;
    border: 16px solid rgba(0, 0, 0, 0.1); /* Light border */
    border-top-color: #3498db; /* Blue color */
    border-radius: 50%;
    animation: spin 1s linear infinite; /* Spin animation */
}



.chatbot-visible .my-5.px-4 {
    margin-top: 13%;
}
/* Container */
.chatbot-visible {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    position: relative;
}

.chatbot-visible .grayBackground {
    background-color: #f9f9f9;
    width: 100%;
    max-width: 800px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

/* Header */
.chatbot-visible .flex {
    display: flex;
    align-items: center;
}



.chatbot-visible h1 {
    font-size: 24px;
    font-weight: 600;
}

/* Chat Box */
.chatbot-visible .chat-box {
    display: flex;
    margin-bottom: 20px;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.chatbot-visible .ai-chat-img {
    flex-shrink: 0;
    margin: auto;
}

.chatbot-visible .robot-p {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #ccc;
}

.chatbot-visible .ai-chat-content {
    padding: 10px 15px;
    margin: auto;
}

.chatbot-visible .alex-title {
    margin: 0 0 5px;
    font-size: 24px;
    font-weight: 600;
    color: #06B6D4;
}

.chatbot-visible .alex-content {
    margin: 0;
    font-size: 14px;
    color: #667085;
    line-height: 1.4;
}

/* Input Form */
.chatbot-visible form {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 100px;
    width: 97%;
    left: 0;
    margin: auto;
    right: 0;
}

.chatbot-visible input[type="text"] {
    flex: 1;
    padding: 10px 15px;
    font-size: 1rem;
    border: 1px solid #06B6D4;
    border-radius: 12px;
    outline: none;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chatbot-visible input[type="text"]:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 8px rgba(74, 144, 226, 0.3);
}

.chatbot-visible button {
    margin-left: 10px;
    background: none;
    border: none;
    outline: none;
    cursor: pointer;
    position: absolute;
    right: 20px;
    width: 32px;
    height: 32px;
    object-fit: cover;
    padding: 6px;
    background-color: #EDFBFC;
    border-radius: 6px;
	display: flex;
    justify-content: center;
}

.chatbot-visible button img {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
    object-fit: cover;
}

.chatbot-visible button img:hover {
    transform: scale(1.1);
}
.chatbot-visible input[type="text"]::placeholder {
    font-size: 14px;
    color: #101828;
}
.hover\:bg-\[\#EDFBFC\]:hover{
    background-color: transparent;
}

.requirement-section input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.bg-amber-light{
	background-color: #FFFAF4;
}
.bg-custom-green-50{
	background-color: #F5FFF9;
}	
/* Spin animation */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.bg-gray-10 {
    background-color: rgba(249, 250, 251, 0.3); /* Very light gray with 10% opacity */
}

.chatbot-visible .ai-chat-content {
    padding: 10px 15px;
    width: 60%;
}

#chat-window{
    height: calc(85vh - 205px);
}

.bg-gray-20{
    background-color: #ededed;
}

.userprofile .modal-content {
    width: 1300px;
}
.interview-scheduler.bg-white .react-datepicker__month-container{ width: 255px;} .react-datepicker__header{ background-color: #FFFFFF !important; border: unset !important; } .interview-scheduler.bg-white h2.react-datepicker__current-month { margin-top: 10px; margin-bottom: 10px; } .interview-scheduler.bg-white button.react-datepicker__navigation.react-datepicker__navigation--previous,.interview-scheduler.bg-white button.react-datepicker__navigation.react-datepicker__navigation--next { margin-top: 10px; border: 1px solid #f0f0f0; padding: 6px 16px; border-radius: 6px; } .interview-scheduler.bg-white button.react-datepicker__navigation.react-datepicker__navigation--previous{    margin-left: 13px;} .interview-scheduler.bg-white button.react-datepicker__navigation.react-datepicker__navigation--next{ margin-left:13px; } .interview-scheduler.bg-white .react-datepicker__navigation-icon--next { left: 1px; } .interview-scheduler.bg-white .react-datepicker__navigation-icon--previous { right: 1px; } .interview-scheduler.bg-white button.react-datepicker__navigation.react-datepicker__navigation--next { margin-top: 10px; border: 1px solid #f0f0f0; margin-right: 13px; padding: 6px 16px; border-radius: 6px; } .interview-scheduler.bg-white .react-datepicker{ background-color: #FFFFFF !important; border-color: #f0f0f0; box-shadow: 0px 4px 8px rgb(191 191 191 / 45%); }
.min-w-96{
	min-width: 24rem;
}
.h-calc-80{
	height: calc(100% - 80px);
}	

.task-activity-btn {
    border-radius: 6px;
    padding: 6px 8px;
    margin-right: 4px;
    background-color: #bfbfbf1a;
    text-align: center;
    width: 50%;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #667085;
    font-weight: 600;
}


.task-activity-btn.active {
	background-color: #fff;
	color: #06B6D4;
	border: 2px solid #06B6D4;
}


img.w-\[20px\].h-\[20px\]{
	width: 20px;
    height: 20px;
}

.w-3\/12 {
    width: 25%;
}
.w-9\/12 {
    width: 75%;
}

.scheduleinterview {
    width: 400px !important;
}
.text-green-800 {
    color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-\[14px\] {
    font-size: 14px;
}
.uploadfilemessage {
    top: -4px;
    position: relative;
}

.w-4\/5 {
    width: 88%;
}

.my-org {
    background-image: radial-gradient(circle, #e0e0e0 2px, transparent 2px);
    background-size: 20px 20px; /* Adjust the size of dots */
  }
.w-\[54px\]{
	width:54px;
}
.text-\[\#101828\]{
	color: #101828;
}

.text-\[\#06B6D4\]{
	color: #06B6D4;
}

.rounded-\[8px\]{
    border-radius: 8px;
}
.text-red-600 {
    color: #F04438;
    font-weight: 600;
}
.text-\[12px\]{
	font-size: 12px;
}
.border-primary-tab{
	color: #06B6D4;
	border-color: #06B6D4;
	font-weight: 600;
}

.requirement-section .dropdown-menu{
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 7.5rem;
    top: 32px;
    right: -10px;
    display: none;
    z-index: 10;
}


.my-org .dropdown-menu{
    font-weight: 600;
    top: 16px;
    right: 13px;
    width: 9rem;
}

.border-b-0{
    border-bottom-width: 0px;
}	
.bg-custom-orange {
    background-color: #FFA347;
}
.bg-secondary-blue{
    background-color: #3B82F6;
}
.bg-secondary-voilet{
    background-color: #8B5CF6;
}

.h-52 {
    width: 13rem;
}

.w-52{
    width: 15rem;
}

.right-\[190px\] {
    right: 190px;
}
.w-36 {
    width: 12rem;
}
.max-h-\[36\.5rem\]{
	 max-height: 36.5rem; 
}
.h-\[3\.3rem\]{
 max-height:3.3rem;
}
.bg-slate-900{
	background-color: #083344;
	color:#fff;
}
.bottom-\[-1\.5rem\]{
	    bottom: -0.4rem;
}

.z-\[9\]{z-index: 9;}

.hover\:bg-gray-100.bg-slate-900 {
    background-color: #083344;
}
.text-\[9px\] {
    font-size: 9px;
}

.uploadfiles-section {
    width: 600px !important;
}

.recruitmenticon{
	filter: brightness(0);
}

.w-7\/12{
	width: 58%;
}

.downloadfile-section {
    width: 350px !important;
}

.competency-score {
    --width: 110px;
    --height: 45px;
    --offset: 2px;
    --radius: 8px;
    position: relative;
    width: var(--width);
    height: var(--height);
    padding: var(--offset);
    border-radius: var(--radius);
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    font-style: normal;
}

	
.homepagesection .max-h-\[100\%\].overflow-y-auto.p-4.space-y-6{
	flex: 1;
	overflow-y: auto;
	padding-bottom: 91px;
}
	
.max-h-\[100\%\]{
	max-height: 100%;
}

.homepagesection{
	height: 100%;
	display: flex;
	flex-direction: column;
}	
div#app {
	height: 100%;
}

.userprofile .modal-content .modal-content {
    width: 480px; /* or set a specific width for descendant modals */
}

.ml-0 {
    margin: 0!important;
}
.readyfeature .redyoption{
	bottom: -40px;
    right: 0;
    margin: 0px;
}

.readyfeature .node-readiness-red{
	margin: 0;
}

.performancedrop{
    top: 20px;
    left: 97px;
    width: 66px;
}

.flex-col-reverse{
	flex-direction: column-reverse;
}
.w-63 {
    width: 241px;
}

/*
.w-full.relative.contentHeight li > a + div > .tree ul > li:only-child:before {
    background: #06B6D4;
    content: '';
    height: 40px;
    width: 2px;
    position: absolute;
    top: -2px;
    display: inline-block;
    left: 50%;
}*/

.w-full.relative.contentHeight .bg-white.border.py-2.border-gray-300.rounded-lg.shadow-md.text-center.w-56.relative{ z-index: 1;}
.userprofile .updatepersonrecords {
    width: calc(100vw - 200px) !important;
    height: max-content;
}

.userprofile .updatecareerrecords {
    width: calc(100vw - 45%) !important;
    height: max-content;
}

.Potentialdrop{
    left: 64px;
    top: 20px;
    width: 88px;
}

.readynowdrop {
    left: 80px;
    top: 33px;
    width: 120px;
}
input#updatecountry_manager,input#updatecorporate_level {
    height: 32px;
    margin-top: 3px;
    margin-left: -2px;
}

				
.request-research {
    width: 1280px;
}
.request-research textarea#emailMessage {
    height: 500px;
    height: 353px;
}

.org-background{
    background-image: url('/images/OrgBackground.png');
}

.plan-development .modal-content{
   width: 800px !important;
}

.plan-development .modalscroll {
    max-height: 75vh;
}