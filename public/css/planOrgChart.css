 /*Now the CSS*/

 /* .tree * {
    margin: 0;
    padding: 0;
} */

/*added*/

.tree {
    /* min-width: 100%; */
    white-space: nowrap;
    margin: 0;
    padding: 0;
}

.tree:not(.pdf-mode) ul {
    padding-top: 20px;
    position: relative;
    transition: all 0.5s;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    height: 100%;
}

.tree li {
    text-align: center;
    list-style-type: none;
    position: relative;
    padding: 20px 5px 0 5px;
    transition: all 0.5s;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    /*added for long names*/

    float: none;
    display: inline-block;
    vertical-align: top;
    white-space: nowrap;
    margin: 0 -2px 0 -2px;
    
}

.fromleft > li:first-child {
    left: 7.8rem;
}

.fromright li:not(:first-child) {
    right: 7.8rem;
}

/*We will use ::before and ::after to draw the connectors*/

.tree li::before,
.tree li::after {
    content: '';
    position: absolute;
    top: 0;
    right: 50%;
    border-top: 2px solid #06B6D4;
    width: 50%;
    height: 26px;
}

.tree li::after {
    right: auto;
    left: 50%;
    border-left: 2px solid #06B6D4;
}

/*We need to remove left-right connectors from elements without 
any siblings*/

.tree li:only-child::after,
.tree li:only-child::before {
    display: none;
}

/*Remove space from the top of single children*/

.tree li:only-child {
    padding-top: 0;
}

/*Remove left connector from first child and 
right connector from last child*/

.tree li:first-child::before,
.tree li:last-child::after {
    border: 0 none;
}

/*Adding back the vertical connector to the last nodes*/

.tree li:last-child::before {
    border-right: 2px solid #06B6D4;
    border-radius: 0 5px 0 0;
    -webkit-border-radius: 0 5px 0 0;
    -moz-border-radius: 0 5px 0 0;
}

.tree li:first-child::after {
    border-radius: 5px 0 0 0;
    -webkit-border-radius: 5px 0 0 0;
    -moz-border-radius: 5px 0 0 0;
}

/*Time to add downward connectors from parents*/

.tree ul ul::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    border-left: 2px solid #06B6D4;
    width: 0;
    height: 23px;
}

.tree li a {
    /* border: 1px solid #ccc; */
    padding: 5px 10px;
    text-decoration: none;
    color: #666;
    font-family: arial, verdana, tahoma;
    font-size: 11px;
    display: inline-block;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    transition: all 0.5s;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
}

/*Time for some hover effects*/
/*We will apply the hover effect the the lineage of the element also*/

.tree li a:hover,
.tree li a:hover+ul li a {
    /* background: #c8e4f8;
    color: #000;
    border: 1px solid #94a0b4; */
}

/*Connector styles on hover*/

.tree li a:hover+ul li::after,
.tree li a:hover+ul li::before,
.tree li a:hover+ul::before,
.tree li a:hover+ul ul::before {
    /* border-color: #94a0b4; */
}

.husband {
    float: left;
}

.wife {
    margin-left: 10px;
}

.wife::before {
    /* pseudo CSS, will need to be modified */

    content: '';
    position: absolute;
    top: 0;
    right: 50%;
    border-top: 2px solid #06B6D4;
    width: 50%;
    height: 20px;
}

.zoom-div{
    top: 5px;
    left:5px;
}


@media print {
  .controls, .sidebar, .download-btn, .header, .footer {
    display: none !important;
  }

  .plan-org-chart {
    box-shadow: none !important;
    background: white !important;
    padding: 0;
    margin: 0 auto;
  }
}

.HRInfo, .HRInfo-red{
    height: 145px;
}
