@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

/* Font Size Css */
.fontSize24 {
    font-family: Inter;
    font-size: 24px;
    font-weight: 600;
    line-height: 32px;
}

.fontSize16 {
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
}

.fontSize14 {
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
}

.fontSize12 {
    font-family: Inter;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
}

/* End Font Css */
/* Color Css */
.grey {
    color: #667085;
}

.white {
    color: #FFFFFF;
}

.black {
    color: #101828;
}

/* End Color Css */
/* Start Reset Password Section */
.reset_Password {
    background-color: #F9FAFB;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.form_Parent {
    display: flex;
    justify-content: center;
    align-items: center;
}

.forgetPassword_Form {
    width: 408px;
    padding: 30px 24px;
    border-radius: 16px;
    border: 1px solid #EAECF0;
    box-shadow: 0px 2px 5px 0px #bfbfbf1a;
    background: #FFFFFF;
}

.successionPlan_Logo {
    width: 48px;
    height: 48px;
}

.forgotPass_Txt {
    padding-top: 16px;
}

.email_Label {
    text-align: left;
    width: 100%;
    margin-bottom: 5px;
}

.enterEmailTxt {
    margin-bottom: 24px !important;
}

input:focus-visible {
    outline: none !important;
}

.email_Input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #EAECF0;
    border-radius: 8px;
}

.sendLink_Btn {
    background: #06B6D4;
    padding: 8px 16px;
    border-radius: 8px;
    border: none;
    color: #FFFFFF;
    width: 100%;
    margin-top: 24px;
    font-weight: 600;
}

.backTo_Login {
    color: #06B6D4;
    font-weight: 600;
    text-decoration: none;
    margin-top: 24px;
    display: block;
}

/* End Reset Password Section */


/* Send Again Css */
.send_Again {
    color: #06B6D4;
    font-weight: 500;
    text-decoration: none;
}
.email_Example{
    font-weight: 600;
}
.eyeLogo {
    position: absolute;
    right: 13px;
    top: 40px;
    color: #667085;
}

/* End Send Again Css */