.is-invalid {
    /* box-shadow: 0 0 0 1px #e3342f; */
    /* color: rgb(239 68 68 / var(--tw-text-opacity)); */
    /* color: red; */
    border-color: red;
}

.redColor{
    color: red;
}
#successMessage {
    background-color: #06B6D4;
    /* background-color: red; */
    margin-right: 15px;
}

#errorMessage {
    background-color: #871c1c;
    /* background-color: red; */
    margin-right: 15px;
}



.grayText{
    color : #667085;
 }
 
 .outline-none {
  outline: none;
 }
 
 
 .mainblue{
     color : #0891B2;
 
 }

 .labelcolor{
    color: #667085;
 }

 .mainblueBG{
    background: var(--brand-brand-primary, #06B6D4);

 }

 .inputArea{
        width: 6rem;
        height: 5rem;
        border: 2px solid lightgray;
        font-size: xx-large;
 }
 /* Custom styles for info toasts */
.toast-info {
    background-color: #06B6D4 !important; /* Change this to your desired background color */
    color: #fff !important; /* Change this to your desired text color */
}