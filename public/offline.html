<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - SuccessionPlanAI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f9fc;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #2563EB;
            margin-bottom: 20px;
        }
        p {
            margin-bottom: 20px;
            line-height: 1.6;
            color: #4B5563;
        }
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: #2563EB;
        }
        .btn {
            background-color: #2563EB;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #1D4ED8;
        }
        .offline-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background-color: #EF4444;
            border-radius: 50%;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📶</div>
        <h1>You're Offline</h1>
        <p><span class="offline-indicator"></span> It seems you've lost your internet connection.</p>
        <p>SuccessionPlanAI requires an internet connection to function properly. Some features and data may be available offline, but for the best experience, please reconnect to the internet.</p>
        <p>We've saved some of your recent data so you can still view it while offline.</p>
        <button class="btn" onclick="window.location.reload()">Try Again</button>
    </div>

    <script>
        // Check if we're back online
        window.addEventListener('online', () => {
            window.location.reload();
        });
    </script>
</body>
</html>