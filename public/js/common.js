window.addEventListener('DOMContentLoaded', (event) => {
    Livewire.on('toast', (data) => {
        const type = data[0];
        const message = data[1];

        switch (type) {
            case 'success':
                toastr.success(message);
                break;
            case 'info':
                toastr.info(message);
                break;
            case 'warning':
                toastr.warning(message);
                break;
            case 'error':
                toastr.error(message);
                break;
            case 'longInfo':
                let $toast = toastr.info(message, 'Info!', {
                    closeButton: true,
                    progressBar: true,
                    timeOut: 10000, // Set the timeout to 10 seconds (10000 milliseconds)
                    extendedTimeOut: 2000, // Set the extended timeout to 2 seconds when hovering
                    showMethod: 'slideDown',
                    hideMethod: 'slideUp',
                    positionClass: 'toast-top-center',
                    onShown: function () {
                        // Apply the full-width class only to this specific toast
                        $toast.addClass('toastr-full-width');
                    }
                });
                break;
            default:
                console.error(`Unknown toast type: ${type}`);
                break;
        }
    });

    Livewire.on('customToast', (data) => {

        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": false,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            // "showDuration": "300",
            // "hideDuration": "1000",
            "timeOut": "10000",
            "extendedTimeOut": "10000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        const type = data[0];
        const message = data[1];

        switch (type) {
            case 'success':
                toastr.success(message);
                break;
            case 'info':
                toastr.info(message);
                break;
            case 'warning':
                toastr.warning(message);
                break;
            case 'error':
                toastr.error(message);
                break;
            default:
                console.error(`Unknown toast type: ${type}`);
                break;
        }
    });
});

