function initializeGenderChart() {
    var peopleCountsData = JSON.parse(document.getElementById('peopleCountsData').textContent);

    // Prepare data for Plotly chart
    var genders = peopleCountsData.map(function(entry) {
        return entry.gender;
    });
    var counts = peopleCountsData.map(function(entry) {
        return entry.total_people;
    });

    // Create a Plotly chart
    var data = [{
        labels: genders,
        values: counts,
        type: 'pie',
        textinfo: 'percent+label', // Customize the information displayed on hover
        textposition: 'outside',
        insidetextfont: {
            size: 16, // Set the font size of the label
            family: 'Arial', // Optionally, set the font family
            color: '#000', // Optionally, set the font color
            weight: 'bold' // Make the label text bold
          },
        hole: 0.7,
        marker: {
            colors: ['#82E0AA', '#3498DB', '#BDC3C7', 'purple', 'orange'],
        },
    }];

    var layout = {
        width:250,
        height:250,
        showlegend: false,
        margin: {
            l: 40, // Left margin in pixels
            r: 40, // Right margin in pixels
            t: 32, // Top margin in pixels
            b: 32, // Bottom margin in pixels
          },
    };

    Plotly.newPlot('genderChart', data, layout, {displayModeBar: false});
}

//=================================== COMPANY CHART ===========================================
function initializeCompanyChart() {
    var companyCountsData = JSON.parse(document.getElementById('companyCountsData').textContent);
  
    // Prepare data for Plotly chart
    var companies = companyCountsData.map(function (entry) {
      return entry.company_name;
    });
    var counts = companyCountsData.map(function (entry) {
      return entry.total_company;
    });
  
    // Create a Plotly chart
    var data = [{
      x: counts, // Use 'x' for horizontal bar chart
      y: companies, // Use 'y' for horizontal bar chart
      type: 'bar',
      orientation: 'h', // Set to 'h' for horizontal bar chart
      text: counts.map(String), // Display counts as text
      textposition: 'outside',
      insidetextfont: {
        size: 16, // Set the font size of the label
        family: 'Arial', // Optionally, set the font family
        color: '#000', // Optionally, set the font color
        weight: 'bold', // Make the label text bold
      },
      marker: {
        color: ['#82E0AA', '#3498DB', '#BDC3C7', 'purple', 'orange'],
      },
    }];
  
    var layout = {
      showlegend: false,
      width:500,
      height:300,
      xaxis: {
        title: 'Individuals Identified', // X-axis title
        showgrid: false, // Remove x-axis grid lines
        showline: false,
        showticklabels: false
      },
      yaxis: {
        showgrid: false, // Remove y-axis grid lines
      },
      margin: {
        l: 80, // Left margin in pixels
        r: 80, // Right margin in pixels
        t: 32, // Top margin in pixels
        b: 32, // Bottom margin in pixels
      },
    };

  
    Plotly.newPlot('companyChart', data, layout, {displayModeBar: false});
  }