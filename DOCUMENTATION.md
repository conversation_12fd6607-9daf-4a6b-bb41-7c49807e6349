# Error Handling System Documentation

## Overview

This document describes the comprehensive error handling system implemented in SuccessionPlanAI, consisting of two distinct but integrated components:

1. **Backend Error Logging**: A standardized system for logging detailed error information for developers and support teams
2. **User-Facing Error Presentation**: A consistent framework for displaying user-friendly error messages in the UI

## Backend Error Logging

### Key Components

- `ErrorLogger` class in `app/Services/ErrorHandling/ErrorLogger.php`
- `RequestIdMiddleware` in `app/Http/Middleware/RequestIdMiddleware.php`
- Enhanced exception handler in `app/Exceptions/Handler.php`

### Features

- **Standardized Error Format**: All errors use a consistent format with contextual data
- **Error Categorization**: Errors are categorized (API, database, security, etc.)
- **Request Tracking**: Each request gets a unique ID that is tracked across the system
- **Context Capturing**: User ID, URL, request parameters and other relevant data is automatically included
- **Exception Handling**: Built-in support for capturing stack traces and exception details

### Usage Examples

```php
// Basic error logging
ErrorLogger::log(
    "Failed to process payment", 
    ['amount' => $amount, 'user_id' => $userId], 
    ErrorLogger::CATEGORY_PROCESSING
);

// Exception logging
try {
    // Code that might throw an exception
} catch (\Exception $e) {
    ErrorLogger::logException(
        $e,
        ['context' => 'payment_processing'],
        ErrorLogger::CATEGORY_PROCESSING
    );
}
```

## User-Facing Error Presentation

### Key Components

- `UserErrorHandler` class in `app/Services/ErrorHandling/UserErrorHandler.php`
- Error message Blade component in `resources/views/components/error-message.blade.php`
- Frontend error handler in `resources/js/error-handler.js`

### Features

- **Consistent UI Presentation**: All errors use the same visual design pattern
- **Error Typing**: Different error types (validation, authentication, etc.) get appropriate styling
- **Actionable Messages**: Error messages can include actions for users to resolve issues
- **Error Codes**: Unique codes link user errors to backend logs for support
- **Client-Side Error Capture**: JavaScript errors are captured and logged to the server

### Usage Examples

```php
// Simple error message
UserErrorHandler::showError(
    'Unable to save your changes at this time.', 
    UserErrorHandler::TYPE_SERVER
);

// Error with actions
UserErrorHandler::showError(
    'Your session has expired.', 
    UserErrorHandler::TYPE_AUTHENTICATION,
    [['label' => 'Log In Again', 'url' => route('login')]]
);

// Exception handling with user-friendly message
try {
    // Code that might throw an exception
} catch (\Exception $e) {
    UserErrorHandler::handleException(
        $e,
        'Unable to process your request. Please try again later.',
        UserErrorHandler::TYPE_SERVER
    );
}
```

## Integration Points

- The exception handler integrates both systems, logging detailed information while presenting friendly messages
- Client-side errors are captured and sent to the backend logging system
- Error codes connect user-visible errors with detailed backend logs

## Recommended Practices

1. **Always log technical details**: Use `ErrorLogger` for all exceptions and errors
2. **Present user-friendly messages**: Never show technical errors to users
3. **Include context**: Always include relevant context data when logging errors
4. **Categorize properly**: Use the appropriate error category and type
5. **Provide recovery actions**: When possible, give users a way to recover from errors

## Future Enhancements

- Integration with an external error monitoring service
- Error analytics dashboard for tracking and resolving common issues
- Enhanced contextual data capture for specific error types
- Expanded client-side error handling