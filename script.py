import pandas as pd
import mysql.connector
import re
import pprint
import json
import requests
import sys
from datetime import datetime
from mysql.connector import Error
import openai
from anthropic import AnthropicBedrock

NUBELA_API_KEY = None
OPENAI_API_KEY = None
cursor = None

global client
client = None  # Will be initialized in main with AWS credentials from environment variables

# Code for the AI to generate a response
def get_completion(prompt, system=''):
    message = client.messages.create(
        model="anthropic.claude-3-5-sonnet-20240620-v1:0", # The name of the model we're using in our AI
        max_tokens=256,
        # Temperature is used to determine how creative the AI would be. A higher temperature means the AI is going to be more
        # creative whereas a lower temperature means the AI will be more robotic
        temperature = 0.0,
        messages=[{"role": "user", "content": prompt}],
        system = system #system gives context and instructions for the AI to follow
    )
    return message.content[0].text

# Connect to the MySQL database
def create_connection(db_host, db_port, db_username, db_password, db_name):
    try:
        connection = mysql.connector.connect(
            host = db_host,
            database = db_name,
            user = db_username,
            password = db_password,
            ssl_disabled=True
        )
        if connection.is_connected():

            print("Successfully connected to the database")
            return connection

    except Error as e:
        print(f"Error: {e}")
        return None


# def is_valid_url(url):
#     regex = re.compile(
#         r'^(?:http|ftp)s?://'  # http:// or https://
#         r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'  # domain...
#         r'localhost|'  # localhost...
#         r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|'  # ...or ipv4
#         r'\[?[A-F0-9]*:[A-F0-9:]+\]?)'  # ...or ipv6
#         r'(?::\d+)?'  # optional port
#         r'(?:/?|[/?]\S+)$', re.IGNORECASE)  # optional path

#     return re.match(regex, url) is not None

def monthToString(month):
    # List of month abbreviations
    monthsArr = [
        "", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ]
    return monthsArr[month]

def map_experiencecs_to_string(experiences):
    experiencesArr = []
    for experience in experiences:
        experienceString = ""
        if experience['starts_at'] and experience['starts_at']['month'] and experience['starts_at']['year']:
            experienceString += monthToString(experience['starts_at']['month']) + "-" + str(experience['starts_at']['year'])

        if experience['ends_at'] and experience['ends_at']['month'] and experience['ends_at']['year']:
            experienceString += " | " + monthToString(experience['ends_at']['month']) + "-" + str(experience['ends_at']['year'])
        else:
            experienceString += " | Present"

        if experience['title']:
            experienceString += ": " + experience['title']

        if experience['company']:
            experienceString += " at " + experience['company']

        if experienceString:
            experiencesArr.append(experienceString)

    return "\n".join(experiencesArr) if len(experiencesArr) > 0 else None

def map_educations_to_string(educations):
    educationsArr = []
    for education in educations:
        educationString = ""
        if education['school']:
            educationString += education['school']

        if education['field_of_study']:
            educationString += ", " + education['field_of_study']

        if education['degree_name']:
            educationString += " - " + education['degree_name']

        if education['starts_at'] and education['starts_at']['year']:
            educationString += " · " + str(education['starts_at']['year'])

        if education['ends_at'] and education['ends_at']['year']:
            educationString += " - " + str(education['ends_at']['year'])

        if educationString:
            educationsArr.append(educationString)

    return "\n".join(educationsArr) if len(educationsArr) > 0 else None

def destruct_date_and_tenure(experience):
    startDate = None
    endDate = None
    tenure = 0

    if "starts_at" in experience and experience["starts_at"]:
        startDateDic = experience["starts_at"]
        startDate = f"{startDateDic['year']}-{startDateDic['month']}-{startDateDic['day']}"

    if "ends_at" in experience and experience["ends_at"]:
        endDateDic = experience["ends_at"]
        endDate = f"{endDateDic['year']}-{endDateDic['month']}-{endDateDic['day']}"

    # Calculate the tenure
    if (startDate):
        start_date = datetime.strptime(startDate, '%Y-%m-%d')
        if endDate:
            end_date = datetime.strptime(endDate, '%Y-%m-%d')
        else:
            end_date = datetime.now()

        tenure_days = (end_date - start_date).days
        tenure = tenure_days / 365.25  # Using 365.25 to account for leap years
        tenure = round(tenure, 1)  # Round to one decimal place

    return {
        "start_date": startDate,
        "end_date": endDate,
        "tenure": tenure
    }

# This method will expect the company name as param and return the company detail
def get_or_create_company(companyName):
    # Checking that the company name is exist in db
    companyDetailQuery = "SELECT * FROM companies WHERE name = %s"
    cursor.execute(companyDetailQuery, (companyName,))
    companyDetail = cursor.fetchone()

    if companyDetail:
        return companyDetail

    else:
        # Inserting the company in DB
        queryToInsertCompany = "INSERT INTO companies (name, created_at) VALUES (%s, NOW())"
        cursor.execute(queryToInsertCompany, (companyName,))
        last_inserted_id = cursor.lastrowid

        # Retrieve the newly inserted company
        queryToGetCompany = "SELECT * FROM companies WHERE id = %s"
        cursor.execute(queryToGetCompany, (last_inserted_id,))
        companyDetail = cursor.fetchone()
        return companyDetail

def insert_skills_in_db(skills = [], peopleId = None):

    if not len(skills) > 0 or  not peopleId:
        return False

    # Prepare data for bulk insert
    bulk_skill_data = [(peopleId, skill, 'Professional') for skill in skills]

    # Insert all data into the skills table in one query
    query_to_insert_skills = "INSERT INTO skills (people_id, skill_name, skill_type) VALUES (%s, %s, %s)"
    cursor.executemany(query_to_insert_skills, bulk_skill_data)

    if len(skills) > 0:
        newSkillsString = "\n".join(skills)
        queryToUpdateSkills = "UPDATE people SET skills = %s WHERE id = %s"
        cursor.execute(queryToUpdateSkills, (newSkillsString, peopleId))

    return True

def insert_experiences_in_db(experiences = [], peopleId = None, needToInsertAllExperiences = False):

    if not len(experiences) > 0 or  not peopleId:
        return False

    for experience in experiences:

        dateAndTenure = destruct_date_and_tenure(experience)
        companyId = None
        startDate = dateAndTenure['start_date']
        endDate = dateAndTenure['end_date']
        tenure = dateAndTenure['tenure']
        role = experience['title'] if experience and experience['title'] else None

        companyDetail = get_or_create_company(experience['company'])
        if companyDetail:
            companyId = companyDetail['id']

        # If these values not found then skip the iteration. Because these value are required in DB table (career_histories)
        if not peopleId or not role or not companyId or not startDate or not tenure:
            continue

        if needToInsertAllExperiences:
            # Insert data into career histories table
            queryToInsertCareerHistory = "INSERT INTO career_histories (people_id, role, past_company_id, start_date, end_date, tenure) VALUES (%s, %s, %s, %s, %s, %s)"
            careerHistoryData = (peopleId, role, companyId, startDate, endDate, tenure)
            cursor.execute(queryToInsertCareerHistory, careerHistoryData)

        else:

            # Fetch the career histories from DB using people id and company id name
            isHistoryExistQuery = "SELECT count(id) as history_count FROM career_histories WHERE past_company_id = %s AND people_id = %s"
            cursor.execute(isHistoryExistQuery, (companyId, peopleId))
            result = cursor.fetchone()

            # Check if career history does not exist in DB then insert that career history otherwise continue the iteration
            existedHistoryCount = result['history_count'] if result else 0
            if existedHistoryCount <= 0:

                # Insert data into skills table
                queryToInsertCareerHistory = "INSERT INTO career_histories (people_id, role, past_company_id, start_date, end_date, tenure) VALUES (%s, %s, %s, %s, %s, %s)"
                careerHistoryData = (peopleId, role, companyId, startDate, endDate, tenure)
                cursor.execute(queryToInsertCareerHistory, careerHistoryData)

    return True

def create_new_pipeline(peopleId, people, planId, successionPlan, userId, user):

    isPipelineExistQuery = "SELECT count(id) as pipeline_count FROM pipelines WHERE people_id = %s AND plan_id = %s"
    cursor.execute(isPipelineExistQuery, (peopleId, planId))
    result = cursor.fetchone()

    pipelineCount = result['pipeline_count'] if result else 0

    if pipelineCount <= 0: # If people does not exis in the pipeline then create a new entry in pipeline table


        # Call OpenAI to generate the headline
        generated_headline = 'Not Applicable'

        status = 'Approved'
        if userId != successionPlan['user_id']:
            status = 'Proposed'

        # Insert into pipeline
        pipeline_query = """
            INSERT INTO pipelines (
                plan_id, user_id, people_id, first_name, last_name, middle_name,
                other_name, gender, diverse, summary, country, city, linkedinURL,
                latest_role, company_id, company_name, start_date, end_date, tenure, `function`,
                division, seniority, exco, career_history, educational_history, skills, languages,
                skills_match, education_match, location_match, role_match, gender_match,
                tenure_match, total_score, status, people_type
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        """
        pipeline_data = (
            planId, userId, peopleId, people['forename'],
            people['surname'], people['middle_name'], people['other_name'], people['gender'], people['diverse'],
            people['summary'], people['country'], people['city'], people['linkedinURL'],
            people['latest_role'], people['company_id'], people['company_name'], people['start_date'] or "1990-01-01",
            people['end_date'], people['tenure'], people['function'], people['division'], "NA", "Non Exco", "NA",
            "NA", "NA", "NA", 0, 0, 1, 1, 1, 1, 0 + 0 + 0 + 1 + 1 + 1, status, "External-User"
        )

        cursor.execute(pipeline_query, pipeline_data)

        # Get pipeline_id for success_people
        pipeline_id = cursor.lastrowid

        '''
        # For success_people
        success_people_query = """
            INSERT INTO success_people (
                pipeline_id, plan_id, user_id, people_id, headline, first_name, last_name, middle_name,
                other_name, gender, diverse, summary, country, city, linkedinURL,
                latest_role, company_id, company_name, start_date, end_date, tenure, `function`,
                division, seniority, exco, career_history, educational_history, skills, languages,
                skills_match, education_match, location_match, role_match, gender_match,
                tenure_match, total_score, type, status, notes, recruit
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        """
        success_people_data = (
            pipeline_id, planId, userId, peopleId, generated_headline, people['forename'],
            people['surname'], people['middle_name'], people['other_name'], people['gender'], people['diverse'],
            people['summary'], people['country'], people['city'], people['linkedinURL'],
            people['latest_role'], people['company_id'], people['company_name'], people['start_date'] or "1900-01-01",
            people['end_date'], people['tenure'], people['function'], people['division'], "NA", "Non Exco", "NA",
            "NA", "NA", "NA", 0, 0, 1, 1, 1, 1, 0 + 0 + 0 + 1 + 1 + 1, "External", status, "Enter notes here", 1
        )

        cursor.execute(success_people_query, success_people_data)

        '''

        # Create new notification
        if status == 'Proposed':
            notification_query = """
                INSERT INTO notifications (
                    type, plan_id, people_id, entity_name, description, user_id, user_company
                ) VALUES (%s, %s, %s, %s, %s, %s, %s);
            """
            description = f"{user['name']} has proposed {people['forename']} {people['latest_role']} from {people['company_name']} to your plan."
            notification_data = (
                'External-Proposed', planId, peopleId, 'Proposed Candidate', description,
                user['id'], user['company_id']
            )

            cursor.execute(notification_query, notification_data)

        return True

    else:
        return True


def create_new_pipeline_for_job(peopleId, people, jobId, jobDetail, userId, user):

    isPipelineExistQuery = "SELECT count(id) as pipeline_count FROM pipelines WHERE people_id = %s AND job_id = %s"
    cursor.execute(isPipelineExistQuery, (peopleId, jobId))
    result = cursor.fetchone()

    pipelineCount = result['pipeline_count'] if result else 0

    if pipelineCount <= 0: # If people does not exis in the pipeline then create a new entry in pipeline table
        pprint.pprint(f"jobDetail: {jobDetail}")

        # Prepare input array
        input_array = {
            'role': people['latest_role'],
            'tenure': people['tenure'],
            'company': people['company_name'],
        }

        # Create a formatted message for GPT-3
        message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n"
        for key, value in input_array.items():
            message += f'{key}: "{value}"\n'

        # Call OpenAI to generate the headline
        generated_headline = 'Not Applicable'
        try:
            response = openai.ChatCompletion.create(
                model='gpt-3.5-turbo',
                messages=[{'role': 'system', 'content': message}],
            )
            generated_headline = response.choices[0].message.content

        except Exception as e:
            print(f"Open AI Error Occurred: {e}")


        status = 'Approved'
        if userId != jobDetail['user_id']:
            status = 'Proposed'

        # Insert into pipeline
        pipeline_query = """
            INSERT INTO pipelines (
                job_id, user_id, people_id, first_name, last_name, middle_name,
                other_name, gender, diverse, summary, country, city, linkedinURL,
                latest_role, company_id, company_name, start_date, end_date, tenure, `function`,
                division, seniority, exco, career_history, educational_history, skills, languages,
                skills_match, education_match, location_match, role_match, gender_match,
                tenure_match, total_score, status, people_type
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        """
        pipeline_data = (
            jobId, userId, peopleId, people['forename'],
            people['surname'], people['middle_name'], people['other_name'], people['gender'], people['diverse'],
            people['summary'], people['country'], people['city'], people['linkedinURL'],
            people['latest_role'], people['company_id'], people['company_name'], people['start_date'] or "1990-01-01",
            people['end_date'], people['tenure'], people['function'], people['division'], "NA", "Non Exco", "NA",
            "NA", "NA", "NA", 0, 0, 1, 1, 1, 1, 0 + 0 + 0 + 1 + 1 + 1, status, "External-User"
        )

        cursor.execute(pipeline_query, pipeline_data)

        # Get pipeline_id for success_people
        pipeline_id = cursor.lastrowid

        '''
        # For success_people
        job_people_query = """
            INSERT INTO job_people (
                pipeline_id, job_id, user_id, people_id, headline, first_name, last_name, middle_name,
                other_name, gender, diverse, linkedinURL,
                latest_role, company_id, company_name, start_date, end_date, tenure, `function`,
                division, seniority, career_history, educational_history, skills, languages,
                readiness, other_tags, country, city, summary, status, notes
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        """
        job_people_data = (
            pipeline_id, jobId, userId, peopleId, generated_headline, people['forename'],
            people['surname'], people['middle_name'], people['other_name'], people['gender'], people['diverse'],
            people['linkedinURL'], people['latest_role'], people['company_id'],
            people['company_name'], people['start_date'] or "1900-01-01", people['end_date'], people['tenure'],
            people['function'], people['division'], people['seniority'], people['career_history'], people['educational_history'],
            people['skills'], people['languages'], people['readiness'], people['other_tags'], people['country'], people['city'],
            people['summary'], status, "Enter notes here"
        )

        cursor.execute(job_people_query, job_people_data)
        '''

        return True

    else:
        return True


# Fetch user profile from LinkedIN
def fetch_user_profile(profileUrl):

    headers = {
        'Authorization': 'Bearer ' + NUBELA_API_KEY
    }

    # Endpoint URL needs to be specific to LinkedIn's API
    api_endpoint = 'https://nubela.co/proxycurl/api/v2/linkedin'

    params = {
        'linkedin_profile_url': profileUrl,
        'extra': 'exclude',
        'github_profile_id': 'exclude',
        'facebook_profile_id': 'exclude',
        'twitter_profile_id': 'exclude',
        'personal_contact_number': 'include',
        'personal_email': 'include',
        'inferred_salary': 'exclude',
        'skills': 'include',
        'use_cache': 'if-recent',
        'fallback_to_cache': 'on-error',
    }

    response = requests.get(api_endpoint, params = params, headers = headers)

    if response.status_code == 200:
        return {
            "status": True,
            "message": "Success!",
            "data": response.json()
        }
    else:
        return {
            "status": False,
            "message": f"Error: {response.status_code} - {response.text}",
            "data": None
        }


# Insert data into the database
def insert_data_into_db(df, user_id, plan_id, job_id):

    # List of LinkedIn URLs to query
    columnList = df.columns.tolist()

    processedLinkedInUrls = []
    insertedPeopleIds = []

    successionPlan = None
    jobDetail = None

    if plan_id:
        # Fetched successionplan from succession_plans table
        queryToFetchSuccessionPlan = "SELECT * FROM succession_plans WHERE id = %s;"
        cursor.execute(queryToFetchSuccessionPlan, (plan_id,))
        successionPlan = cursor.fetchone()

    elif job_id:
        # Fetched job from jobs table
        queryToFetchJob = "SELECT * FROM jobs WHERE id = %s;"
        cursor.execute(queryToFetchJob, (job_id,))
        jobDetail = cursor.fetchone()

    # Fetched user detail from users table
    queryToFetchUser = "SELECT * FROM users WHERE id = %s;"
    cursor.execute(queryToFetchUser, (user_id,))
    user = cursor.fetchone()

    if len(columnList) > 0:

        columnName = columnList[0]
        linkedin_urls = df[columnName].dropna().tolist()

        # Fetching all the peoples from DB on the basis of given linked IN urls
        placeholders = ', '.join(['%s'] * len(linkedin_urls))
        getPeoplesQuery = f"SELECT * FROM people WHERE linkedinURL IN ({placeholders})"
        cursor.execute(getPeoplesQuery, linkedin_urls)
        peoples = cursor.fetchall()

                # if not peoples or len(peoples) <= 0:
                #     print(f"Peoples not found in DB for any of the given LinkedIn urls!")
                #     return

        # Iterate all the rows of given excel sheet
        for index, row in df.iterrows():

            if columnName not in row:
                print(f"Skipping row {index} due to missing data")
                continue

            linkedInUrl = row[columnName]
            if not linkedInUrl or pd.isna(linkedInUrl) or linkedInUrl.strip() == '':
                print(f"Skipping row {index} due to missing or invalid data")
                continue

            if linkedInUrl in processedLinkedInUrls:
                print(f"Skipping row {index} due to already processed this LinkedIn URL")
                continue

            peopleDetail = next((item for item in peoples if item['linkedinURL'] == linkedInUrl), None)
            if peopleDetail:
                print(f"Skipping row {index} due to already exist this LinkedIn URL in our Database: {linkedInUrl}")

                # Create new entry into the pipeline table
                if plan_id:
                    create_new_pipeline(peopleDetail['id'], peopleDetail, plan_id, successionPlan, user_id, user)
                elif job_id:
                    create_new_pipeline_for_job(peopleDetail['id'], peopleDetail, job_id, jobDetail, user_id, user)

                continue

            else:

                processedLinkedInUrls.append(linkedInUrl)

                userProfileResponse = fetch_user_profile(linkedInUrl)

                if userProfileResponse['status'] == False:
                    print(userProfileResponse['message'])
                    continue

                userLinkedInProfile = userProfileResponse['data']

                if not userLinkedInProfile or (not userLinkedInProfile['experiences'] and not userLinkedInProfile['skills']):
                    print(f"Experiences and skills are not found for this URL {linkedInUrl}")
                    continue

                # Insert the person details in DB if people does not exist with that URL
                currentExperience = None
                latestRole = None
                companyId = None
                companyName = None
                startDate = None
                endDate = None
                tenure = 0
                educationHistory = None
                careerHistory = None
                skills = None
                languages = " ".join(userLinkedInProfile["languages"]) if userLinkedInProfile["languages"] and len(userLinkedInProfile["languages"]) > 0 else None

                #  Experiences manupulation
                if userLinkedInProfile["experiences"] and len(userLinkedInProfile["experiences"]) > 0:
                    experiences = userLinkedInProfile["experiences"]
                    careerHistory = map_experiencecs_to_string(experiences)

                    currentExperience = next((item for item in experiences if item['ends_at'] is None), None)

                    if not currentExperience:
                        currentExperience = experiences[0]

                    if currentExperience:
                        latestRole = currentExperience["title"] if "title" in currentExperience else None
                        dateAndTenure = destruct_date_and_tenure(currentExperience)

                        startDate = dateAndTenure['start_date']
                        endDate = dateAndTenure['end_date']
                        tenure = dateAndTenure['tenure']

                        if "company" in currentExperience:
                            companyDetail = get_or_create_company(currentExperience['company'])
                            if companyDetail:
                                companyId = companyDetail['id']
                                companyName = companyDetail['name']

                #  Educations manupulation
                if userLinkedInProfile["education"] and len(userLinkedInProfile["education"]) > 0:
                    educations = userLinkedInProfile["education"]
                    educationHistory = map_educations_to_string(educations)

                #  Skills manupulation
                if userLinkedInProfile["skills"] and len(userLinkedInProfile["skills"]) > 0:
                    skillsArr = userLinkedInProfile["skills"]
                    skills = "\n".join(skillsArr)

                forename = userLinkedInProfile["first_name"]
                surname = userLinkedInProfile["last_name"]
                country = userLinkedInProfile["country_full_name"]
                city = userLinkedInProfile["city"]

                # Based on the information what is the Potential Gender
                system = "You are an expert gender studies and know about which forenames are associated to which genders. Your answers can only say Male or Female. If you are completely not sure then just say Not Applicable only"
                prompt = f"What is the gender of {forename}?"
                try:
                    gender = get_completion(prompt,system)
                    if "Not Applicable" in gender:
                        gender = "Not Applicable"
                except:
                    gender = "Not Applicable"

                # Based on the information what is the Potential Diversity
                system  = "You are an expert ethnicity studies and know about which forenames are associated to which countries. Your answer can only be the adjectival and nothing else. If you can't tell say Not Applicable only"
                prompt  = f"What is the origin of this name {surname}?"
                try:
                    diverse = get_completion(prompt,system)
                    if "Not Applicable" in diverse:
                        diverse = "Not Applicable"
                except:
                    diverse = "Not Applicable"

                # Based on the information what is the Potential Function of the role in the company
                system = """You are an experienced consultant that has worked across all industries and know about which roles work in what function of a company.
                        Your answer must only be the name of the function and nothing else. If you're not sure then say Not Applicable only. Only give one function.

                        Some additional information:
                        A Chief Investment Officer's function is Investment Management only.
                        A Chief Operations Officer's function is Operations only.
                        A Chief Risk and Compliance Officer is 'Risk and Compliance' only.
                        A Chief Executive Officer's function is Executive Management.
                        If the word "Research" is in the person's role the function is Research.
                        If the word "Quantitative" is in the person's role the function is Quant.
                        If the word "Client Partner" is in the person's role the function is "Sales"only.
                        If the word "Client Lead" is in the person's role the function is "Sales" only.
                        A Head of Pricing's function is Pricing.
                        If the word 'coverage' is in the person's role the function is "Coverage" only.
                        If "M&A" is in the person role then the function is 'M & A' only.
                        A Head of Mergers and Acquisitions function is "M & A" only.
                        A Head of Asset Management function is "Asset Management" Only.
                        If the role is related with Tax then the function is "Finance" Only.
                        A Independent Non-Executive Director's function's is Board.
                        A Board Member function's is Board.
                        A Non Executive Director function's is Board.
                        If the role is associated with Tax then the function is "Finance" only.
                        If you see the word "ESG" in role the function is "ESG" only.
                        If "Head of Product" is in the role the function is "Product" only.
                        IF "Head of Data" is in the role the function is "Data" only.
                        If the role is related with Procurement then the function is "Procurement" only this includes roles like "Vendor Management" and "Supply Chain".

                        If you see the function is "IT" call it "Technology".
                        "Corporate Banking" is a division for the function "Coverage" only.
                        If the company is a bank then for "Sales" function call it "Coverage" only.
                        A "Head of Product and Proposition" works in function is "Product & Proposition" only.
                        """
                prompt = f"Based on your understanding the individuals career history: {careerHistory} their current role: {latestRole}, the company: {companyName}. Tell me the name of the function the individual works in."
                try:
                    function = get_completion(prompt,system)
                except:
                    function = "Not Applicable"
                    if "Not Applicable" in function:
                        function = "Not Applicable"

                # Using the provided information write the summary
                system = "You are a Talent Aquisition expert with strong skills in summarising people's career histories. Your response should only be 200 words long."
                prompt = f"Write a summary for {forename} here is there career history: {careerHistory}?"
                try:
                    summary = get_completion(prompt,system)
                except:
                    summary = "Not Applicable"

                status = "Submitted"
                exco = "Non Exco"
                userId = user_id
                createdAt = datetime.now()

                queryToInsertPeopleDetail = """INSERT INTO people
                                                (forename, surname, gender, country, city, linkedinURL, latest_role, company_id, company_name, start_date, end_date, tenure, people.function, career_history, educational_history, skills, languages, status, exco, diverse, summary, user_id, created_at)
                                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,  %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
                peopleData = (forename, surname, gender, country, city, linkedInUrl, latestRole, companyId, companyName, startDate, endDate, tenure, function, careerHistory, educationHistory, skills, languages, status, exco, diverse, summary, userId, createdAt)
                print(peopleData)
                cursor.execute(queryToInsertPeopleDetail, peopleData)
                print("********  People Query Execution  *******")

                lastInsertedId = cursor.lastrowid
                insertedPeopleIds.append(lastInsertedId)


                # Retrieve the last inserted row details
                queryToFetchLastInserted = "SELECT * FROM people WHERE id = %s;"
                cursor.execute(queryToFetchLastInserted, (lastInsertedId,))
                lastInsertedPeople = cursor.fetchone()


                # Insert the skills in DB
                if userLinkedInProfile['skills'] and len(userLinkedInProfile['skills']) > 0 and lastInsertedId:
                    insert_skills_in_db(userLinkedInProfile['skills'], lastInsertedId)

                # Insert the Career Histories in DB
                if userLinkedInProfile['experiences'] and len(userLinkedInProfile['experiences']) > 0 and lastInsertedId:
                    insert_experiences_in_db(userLinkedInProfile['experiences'], lastInsertedId, True)

                # Create new entry into the pipeline table
                if plan_id:
                    create_new_pipeline(lastInsertedPeople['id'], lastInsertedPeople, plan_id, successionPlan, user_id, user)

                elif job_id:
                    create_new_pipeline_for_job(lastInsertedPeople['id'], lastInsertedPeople, job_id, jobDetail, user_id, user)

    else:
        print("Column name does not exist in the sheet.")

    cursor.close()
    print("Data inserted successfully")

# Main code execution
if __name__ == "__main__":

    # Get the arguments passed from the Laravel app
    excel_file_path = sys.argv[1]
    db_host = sys.argv[2]
    db_port = sys.argv[3]
    db_username = sys.argv[4]
    db_password = sys.argv[5]
    db_name = sys.argv[6]
    OPENAI_API_KEY = sys.argv[7]
    NUBELA_API_KEY = sys.argv[8]
    user_id = sys.argv[9]
    plan_id = sys.argv[10]
    job_id = sys.argv[11]


    # Initialize OpenAI API
    openai.api_key = OPENAI_API_KEY

    # Initialize AnthropicBedrock client with AWS credentials
    client = AnthropicBedrock(
        aws_access_key=sys.argv[12] if len(sys.argv) > 12 else None,
        aws_secret_key=sys.argv[13] if len(sys.argv) > 13 else None,
        aws_region="us-east-1"
    )

    conn = create_connection(db_host, db_port, db_username, db_password, db_name)

    if conn:
        try:
            # Read the Excel sheet
            # excel_file = 'linkedin_urls.xlsx'  # Update this path
            # sheet_name = 'Sheet1'  # Update this if necessary

            print(f"Excel file path: '{excel_file_path}'")
            # df = pd.read_excel(excel_file_path, sheet_name = sheet_name)
            df = pd.read_excel(excel_file_path)
            cursor = conn.cursor(dictionary=True, buffered=True)

            insert_data_into_db(df, user_id, plan_id, job_id)

        except FileNotFoundError:
            print(f"Error: The file '{excel_file_path}' was not found.")

        except Exception as e:
            print(f"An unexpected error occurred: {e}")

        conn.commit()
        conn.close()
    else:
        print(f"Error: Could not connected to the database")
