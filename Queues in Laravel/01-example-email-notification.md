# 01 - Why Queues? First Example - Email Notification with Queue

**Duration:** 7:44 min  
**Course:** Queues in Laravel

## Video Content

*Note: This lesson contains a Vimeo video that may not be accessible in some regions. The video URL is: https://player.vimeo.com/video/732687827*

## Lesson Overview

This lesson introduces the concept of queues in Laravel by demonstrating a practical example of sending email notifications through a queue, preventing users from having to wait for email processing.

## Important User Comments

### Laravel Excel with Queues (<PERSON> - 2 years ago)

A user shared their experience trying to use queues with Laravel Excel for processing files with 5000+ records. They encountered issues specifically with Oracle database.

**Controller Code:**
```php
public function store(StoreFileUploadRequest $request) {
    if ($request->hasFile('file')) {
        Excel::import(new FileUploadImport, $request->file('file'));
        return new FileUploadResource($request);
    }
}
```

**Model/Class Implementation:**
```php
class FileUploadImport implements ToModel, WithBatchInserts, ShouldQueue, WithChunkReading {
    public function model(array $row) {
        return new FileUpload([
            'name' => $row[0],
            'cpf' => $row[1],
            'data_nascimento' => $row[2],
        ]);
    }
    
    public function batchSize(): int {
        return 2000;
    }
    
    public function chunkSize(): int {
        return 2000;
    }
}
```

**Error Encountered:**
```
local.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at C:\\laragon\\www\\Back-end\\gateway-web-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Stringable.php:32)
```

**Alternative Approach Using queueImport:**
```php
public function store(StoreFileUploadRequest $request) {
    if ($request->hasFile('file')) {
        Excel::queueImport(new FileUploadImport, $request->file('file'));
        return new FileUploadResource($request);
    }
}
```

**Teacher's Response (Povilas Korop):**
- The error suggests an issue with data conversion
- Recommended checking for invalid data in columns/rows or empty unnecessary rows
- Noted that Oracle compatibility might be an issue
- Suggested that Excel/CSV might require at least 3 lines (potential bug/feature)
- Recommended posting on Laracasts forum for Oracle-specific debugging

### Video Access Issue (Muhsin Ahadi - 1 year ago)

A user reported that the Vimeo video was not accessible in their region.

**Moderator Response (Modestas):**
- Confirmed that Vimeo might be blocked in certain countries
- No alternative solution available (unable to migrate to YouTube)

---

[Next Lesson: Other Queueable Classes: Example of Mailables and Event Listeners →](02-mailables-event-listeners.md)
