# Queues in Laravel

## Course Information
- 📹 15 video lessons
- ⏱️ 1 h 31 min
- 📅 Jun 2021
- 🚀 Laravel 8

## About this course

**Notice**: This course is from 2021 with Laravel 8, now we have updated text-based course [Queues in Laravel 12](https://laraveldaily.com/course/queues-laravel).

---

In this 1.5-hour course, you will learn what you need to run Laravel Jobs via Queues in the background, so your users wouldn't wait for some email to be sent or a report to be processed.

We will touch on these topics:

- How to write code for Laravel jobs and how to put them on queue
- How to run and monitor the queues
- How to deal with the failed jobs: restart, log errors, notify the developers
- What are the extra tools like Supervisor and Laravel Horizon
- ...and more.

Look at the curriculum listed below, and see you inside the course!

## Course Curriculum

### First Examples of Queues

1. [Why Queues? First Example - Email Notification with Queue](01-example-email-notification.md) (7:44)
2. [Other Queueable Classes: Example of Mailables and Event Listeners](02-mailables-event-listeners.md) (6:31)
3. [Regular Jobs: Creating, Dispatching and Adding Parameters](03-jobs-dispatching.md) (5:42)

### Processing Failed Jobs

4. [Simple Example of a Failed Job and Restarting the Queue](04-failed-job-restarting-queue.md) (4:17)
5. [How to Retry the Failed Job Manually: Many Options](05-retry-failed-job.md) (7:28)
6. [Method failed(): Inform Admin About Failure and Perform Cleanup](06-method-failed.md) (6:29)
7. [Long-Running Jobs: Timeouts, Fatal Errors and a Better Way](07-long-running-jobs-timeouts.md) (11:14)
8. [Idempotent Jobs: Double-Check Everything to Avoid Failure](08-idempotent-jobs.md) (7:42)
9. [Delay Jobs if Some Condition Fails](09-delay-jobs-condition.md) (4:21)

### Extra Jobs/Queues Features and Tools

10. [Jobs into Groups: Batching and Chaining](10-jobs-batch-chain.md) (8:08)
11. [Showing Progress for Batch of Jobs](11-progress-batch.md) (4:38)
12. [Multiple Queues and Priority Jobs](12-multiple-queues-priority.md) (2:25)
13. [Queue:listen for Local Development](13-queue-listen-local.md) (3:02)
14. [Other Queue Drivers: Redis and Laravel Horizon](14-drivers-redis-horizon.md) (5:21)
15. [Supervisor, Multiple Queue Workers and Horizon Again](15-supervisor-workers.md) (6:45)
