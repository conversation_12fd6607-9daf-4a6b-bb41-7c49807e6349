# 10 - Jobs into Groups: Batching and Chaining

**Duration:** 8:08 min  
**Course:** Queues in Laravel

## Video Content

*Note: This lesson contains a Vimeo video that may not be accessible in some regions.*

## Lesson Overview

This lesson covers advanced job organization techniques, including how to batch multiple jobs together and chain them for sequential execution.

## User Comments

No comments were posted on this lesson.

---

[← Previous: Delay Jobs if Some Condition Fails](09-delay-jobs-condition.md) | [Next: Showing Progress for Batch of Jobs →](11-progress-batch.md)
