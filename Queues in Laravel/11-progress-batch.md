# 11 - Showing Progress for Batch of Jobs

**Duration:** 4:38 min  
**Course:** Queues in Laravel

## Video Content

*Note: This lesson contains a Vimeo video that may not be accessible in some regions.*

## Lesson Overview

This lesson demonstrates how to implement progress tracking for batched jobs, allowing users to monitor the completion status of multiple related jobs.

## User Comments

No comments were posted on this lesson.

---

[← Previous: Jobs into Groups: Batching and Chaining](10-jobs-batch-chain.md) | [Next: Multiple Queues and Priority Jobs →](12-multiple-queues-priority.md)
