# 06 - Method failed(): Inform Admin About Failure and Perform Cleanup

**Duration:** 6:29 min  
**Course:** Queues in Laravel

## Video Content

*Note: This lesson contains a Vimeo video that may not be accessible in some regions.*

## Lesson Overview

This lesson covers the `failed()` method in Laravel jobs, demonstrating how to notify administrators when jobs fail and perform necessary cleanup operations.

## User Comments

No comments were posted on this lesson.

---

[← Previous: How to Retry the Failed Job Manually: Many Options](05-retry-failed-job.md) | [Next: Long-Running Jobs: Timeouts, Fatal Errors and a Better Way →](07-long-running-jobs-timeouts.md)
