# 02 - Other Queueable Classes: Example of Mailables and Event Listeners

**Duration:** 6:31 min  
**Course:** Queues in Laravel

## Video Content

*Note: This lesson contains a Vimeo video that may not be accessible in some regions.*

## Lesson Overview

This lesson explores how other Laravel classes beyond regular jobs can be made queueable, specifically focusing on Mailables and Event Listeners. It demonstrates how to leverage queues with these commonly used Laravel features.

## User Comments

No comments were posted on this lesson.

---

[← Previous: Why Queues? First Example - Email Notification with Queue](01-example-email-notification.md) | [Next: Regular Jobs: Creating, Dispatching and Adding Parameters →](03-jobs-dispatching.md)
