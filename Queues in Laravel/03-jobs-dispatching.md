# 03 - Regular Jobs: Creating, Dispatching and Adding Parameters

**Duration:** 5:42 min  
**Course:** Queues in Laravel

## Video Content

*Note: This lesson contains a Vimeo video that may not be accessible in some regions.*

## Lesson Overview

This lesson covers the fundamentals of creating regular jobs in Laravel, including how to dispatch them to queues and how to pass parameters to job classes for processing.

## User Comments

No comments were posted on this lesson.

---

[← Previous: Other Queueable Classes: Example of Mailables and Event Listeners](02-mailables-event-listeners.md) | [Next: Simple Example of a Failed Job and Restarting the Queue →](04-failed-job-restarting-queue.md)
