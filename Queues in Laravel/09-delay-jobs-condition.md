# 09 - Delay Jobs if Some Condition Fails

**Duration:** 4:21 min  
**Course:** Queues in Laravel

## Video Content

*Note: This lesson contains a Vimeo video that may not be accessible in some regions.*

## Lesson Overview

This lesson demonstrates how to implement conditional delays for jobs, allowing you to postpone job execution when certain conditions are not met.

## User Comments

No comments were posted on this lesson.

---

[← Previous: Idempotent Jobs: Double-Check Everything to Avoid Failure](08-idempotent-jobs.md) | [Next: Jobs into Groups: Batching and Chaining →](10-jobs-batch-chain.md)
