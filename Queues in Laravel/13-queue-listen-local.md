# 13 - Queue:listen for Local Development

**Duration:** 3:02 min  
**Course:** Queues in Laravel

## Video Content

*Note: This lesson contains a Vimeo video that may not be accessible in some regions.*

## Lesson Overview

This lesson covers the `queue:listen` command and its use in local development environments, explaining the differences between `queue:work` and `queue:listen`.

## User Comments

No comments were posted on this lesson.

---

[← Previous: Multiple Queues and Priority Jobs](12-multiple-queues-priority.md) | [Next: Other Queue Drivers: Redis and Laravel Horizon →](14-drivers-redis-horizon.md)
