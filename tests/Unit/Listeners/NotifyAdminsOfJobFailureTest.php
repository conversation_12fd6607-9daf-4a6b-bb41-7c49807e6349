<?php

namespace Tests\Unit\Listeners;

use Tests\TestCase;
use App\Listeners\NotifyAdminsOfJobFailure;
use App\Models\User;
use App\Notifications\JobFailedNotification;
use App\Jobs\SearchCandidatesJob;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Support\Facades\Notification;
use Mockery;

class NotifyAdminsOfJobFailureTest extends TestCase
{
    use DatabaseTransactions;

    public function test_listener_notifies_only_development_email_when_job_fails()
    {
        // Create the development user
        $developmentUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'Admin'
        ]);
        
        // Create other users that should NOT be notified
        $admin1 = User::factory()->admin()->create(['email' => '<EMAIL>']);
        $admin2 = User::factory()->master()->create(['email' => '<EMAIL>']);
        $regularUser = User::factory()->user()->create();
        
        $jobPayload = ['plan_id' => 123, 'plan_name' => 'Test Plan'];
        $user = User::factory()->create();
        $exception = new \Exception('Job failed');
        
        // Create a mock job instance
        $job = Mockery::mock('Illuminate\\Contracts\\Queue\\Job');
        $job->shouldReceive('resolveName')->andReturn(SearchCandidatesJob::class);
        $job->shouldReceive('getRawBody')->andReturn(json_encode([
            'data' => [
                'command' => serialize(new SearchCandidatesJob($jobPayload, $user))
            ]
        ]));
        
        $event = new JobFailed('default', $job, $exception);
        
        $listener = new NotifyAdminsOfJobFailure();
        $listener->handle($event);
        
        // Assert ONLY the development user was notified
        $this->assertDatabaseHas('laravel_notifications', [
            'notifiable_id' => $developmentUser->id,
            'type' => JobFailedNotification::class
        ]);
        
        // Assert NO other users were notified
        $this->assertDatabaseMissing('laravel_notifications', [
            'notifiable_id' => $admin1->id,
            'type' => JobFailedNotification::class
        ]);
        
        $this->assertDatabaseMissing('laravel_notifications', [
            'notifiable_id' => $admin2->id,
            'type' => JobFailedNotification::class
        ]);
        
        $this->assertDatabaseMissing('laravel_notifications', [
            'notifiable_id' => $regularUser->id,
            'type' => JobFailedNotification::class
        ]);
    }

    public function test_listener_extracts_job_data_from_event()
    {
        $developmentUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'Admin'
        ]);
        
        $jobPayload = ['plan_id' => 456, 'plan_name' => 'Critical Plan'];
        $user = User::factory()->create();
        $exception = new \Exception('Critical failure');
        
        // Create a mock job instance
        $job = Mockery::mock('Illuminate\\Contracts\\Queue\\Job');
        $job->shouldReceive('resolveName')->andReturn(SearchCandidatesJob::class);
        $job->shouldReceive('getRawBody')->andReturn(json_encode([
            'data' => [
                'command' => serialize(new SearchCandidatesJob($jobPayload, $user))
            ]
        ]));
        
        $event = new JobFailed('default', $job, $exception);
        
        $listener = new NotifyAdminsOfJobFailure();
        $listener->handle($event);
        
        // Check notification data for development user
        $notification = \DB::table('laravel_notifications')
            ->where('notifiable_id', $developmentUser->id)
            ->where('type', JobFailedNotification::class)
            ->first();
            
        $this->assertNotNull($notification);
        
        $data = json_decode($notification->data, true);
        $this->assertEquals(SearchCandidatesJob::class, $data['job_class']);
        $this->assertEquals('Critical failure', $data['error_message']);
    }

    public function test_listener_handles_invalid_job_data_gracefully()
    {
        $developmentUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'Admin'
        ]);
        
        $exception = new \Exception('Job failed');
        
        // Create a mock job with invalid data
        $job = Mockery::mock('Illuminate\\Contracts\\Queue\\Job');
        $job->shouldReceive('resolveName')->andReturn('InvalidJobClass');
        $job->shouldReceive('getRawBody')->andReturn('invalid json');
        
        $event = new JobFailed('default', $job, $exception);
        
        $listener = new NotifyAdminsOfJobFailure();
        $listener->handle($event);
        
        // Should still create notification even with invalid data
        $this->assertDatabaseHas('laravel_notifications', [
            'notifiable_id' => $developmentUser->id,
            'type' => JobFailedNotification::class
        ]);
    }

    public function test_listener_does_not_fail_when_development_email_does_not_exist()
    {
        // Create other users but NOT the development email
        $regularUser = User::factory()->user()->create();
        $admin = User::factory()->admin()->create(['email' => '<EMAIL>']);
        
        $jobPayload = ['plan_id' => 789];
        $user = User::factory()->create();
        $exception = new \Exception('Job failed');
        
        $job = Mockery::mock('Illuminate\\Contracts\\Queue\\Job');
        $job->shouldReceive('resolveName')->andReturn(SearchCandidatesJob::class);
        $job->shouldReceive('getRawBody')->andReturn(json_encode([
            'data' => [
                'command' => serialize(new SearchCandidatesJob($jobPayload, $user))
            ]
        ]));
        
        $event = new JobFailed('default', $job, $exception);
        
        $listener = new NotifyAdminsOfJobFailure();
        
        // Should not throw exception even when development email doesn't exist
        $this->expectNotToPerformAssertions();
        $listener->handle($event);
    }
}
