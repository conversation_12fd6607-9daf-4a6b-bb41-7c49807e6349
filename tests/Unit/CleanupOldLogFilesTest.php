<?php

namespace Tests\Unit;

use App\Jobs\CleanupOldLogFiles;
use Tests\TestCase;

class CleanupOldLogFilesTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Create logs directory if it doesn't exist
        if (! is_dir(storage_path('logs'))) {
            mkdir(storage_path('logs'), 0755, true);
        }
    }

    protected function tearDown(): void
    {
        // Clean up test log files
        $testFiles = glob(storage_path('logs/laravel-test-*.log'));
        foreach ($testFiles as $file) {
            if (file_exists($file)) {
                unlink($file);
            }
        }

        parent::tearDown();
    }

    public function test_cleanup_job_removes_old_log_files(): void
    {
        // Create old log file (35 days old)
        $oldLogFile = storage_path('logs/laravel-test-old.log');
        file_put_contents($oldLogFile, 'old log content');
        touch($oldLogFile, now()->subDays(35)->timestamp);

        // Create recent log file (10 days old)
        $recentLogFile = storage_path('logs/laravel-test-recent.log');
        file_put_contents($recentLogFile, 'recent log content');
        touch($recentLogFile, now()->subDays(10)->timestamp);

        // Verify both files exist before cleanup
        $this->assertFileExists($oldLogFile);
        $this->assertFileExists($recentLogFile);

        // Run the cleanup job
        $job = new CleanupOldLogFiles;
        $job->handle();

        // Verify old file was removed but recent file remains
        $this->assertFileDoesNotExist($oldLogFile);
        $this->assertFileExists($recentLogFile);
    }

    public function test_cleanup_job_preserves_files_newer_than_30_days(): void
    {
        // Create log files of various ages
        $files = [
            'laravel-test-29days.log' => 29,
            'laravel-test-30days.log' => 30,
            'laravel-test-31days.log' => 31,
        ];

        foreach ($files as $filename => $daysOld) {
            $filepath = storage_path("logs/{$filename}");
            file_put_contents($filepath, "log content for {$daysOld} days old");
            touch($filepath, now()->subDays($daysOld)->timestamp);
        }

        // Run cleanup
        $job = new CleanupOldLogFiles;
        $job->handle();

        // Files 29 and 30 days old should remain, 31+ days should be removed
        $this->assertFileExists(storage_path('logs/laravel-test-29days.log'));
        $this->assertFileExists(storage_path('logs/laravel-test-30days.log'));
        $this->assertFileDoesNotExist(storage_path('logs/laravel-test-31days.log'));
    }

    public function test_cleanup_job_handles_empty_logs_directory(): void
    {
        // Ensure logs directory is empty of laravel-*.log files
        $existingFiles = glob(storage_path('logs/laravel-*.log'));
        foreach ($existingFiles as $file) {
            unlink($file);
        }

        // Should not throw any errors
        $job = new CleanupOldLogFiles;
        $job->handle();

        $this->assertTrue(true); // If we get here, no exception was thrown
    }

    public function test_cleanup_job_only_removes_laravel_log_files(): void
    {
        // Create a non-Laravel log file
        $otherLogFile = storage_path('logs/other-app.log');
        file_put_contents($otherLogFile, 'other app log');
        touch($otherLogFile, now()->subDays(40)->timestamp);

        // Create old Laravel log file
        $laravelLogFile = storage_path('logs/laravel-test-old.log');
        file_put_contents($laravelLogFile, 'laravel log');
        touch($laravelLogFile, now()->subDays(40)->timestamp);

        $job = new CleanupOldLogFiles;
        $job->handle();

        // Other log file should remain, Laravel log should be removed
        $this->assertFileExists($otherLogFile);
        $this->assertFileDoesNotExist($laravelLogFile);

        // Cleanup
        unlink($otherLogFile);
    }
}
