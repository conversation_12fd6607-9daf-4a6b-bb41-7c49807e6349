<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\AI\RateLimitedOpenAiService;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Http\Client\Request;
use Carbon\Carbon;
use Mockery;

class OpenAiRateLimitHandlingTest extends TestCase
{
    protected $service;
    protected $testKey = 'test-429-handling';

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new RateLimitedOpenAiService();
        
        // Clear any existing rate limits for clean testing
        $this->clearAllRateLimits();
        
        // Enable queue fake to test job interactions
        Queue::fake();
    }

    protected function tearDown(): void
    {
        $this->clearAllRateLimits();
        parent::tearDown();
    }

    protected function clearAllRateLimits(): void
    {
        RateLimiter::clear("openai-rpm:{$this->testKey}");
        RateLimiter::clear("openai-tpm:{$this->testKey}");
        RateLimiter::clear("openai-backoff:{$this->testKey}");
    }

    /** @test */
    public function it_properly_implements_exponential_backoff_on_429_errors()
    {
        // Track the number of attempts and timings
        $attempts = 0;
        $requestTimes = [];
        
        Http::fake(function (Request $request) use (&$attempts, &$requestTimes) {
            $attempts++;
            $requestTimes[] = Carbon::now();
            
            // Return 429 for first 3 attempts, then success
            if ($attempts <= 3) {
                return Http::response([
                    'error' => [
                        'message' => 'Rate limit exceeded',
                        'type' => 'tokens',
                        'code' => 'rate_limit_exceeded'
                    ]
                ], 429, [
                    'Retry-After' => '2' // OpenAI sends retry-after header
                ]);
            }
            
            return Http::response([
                'choices' => [
                    ['message' => ['content' => 'Success after retries']]
                ]
            ], 200);
        });

        $payload = [
            'model' => 'gpt-4o-mini',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];

        // First attempt should fail with 429
        $result1 = $this->service->chatCompletion($payload, $this->testKey);
        $this->assertNull($result1);
        
        // Check that backoff counter was incremented
        $backoffAttempts = RateLimiter::attempts("openai-backoff:{$this->testKey}");
        $this->assertEquals(1, $backoffAttempts);
        
        // Second attempt should also fail
        $result2 = $this->service->chatCompletion($payload, $this->testKey);
        $this->assertNull($result2);
        
        // Backoff counter should be incremented again
        $backoffAttempts = RateLimiter::attempts("openai-backoff:{$this->testKey}");
        $this->assertEquals(2, $backoffAttempts);
        
        // Verify exponential backoff is tracked (2^attempts seconds)
        Log::shouldReceive('warning')
            ->withArgs(function ($message, $context) {
                return $message === 'Implementing exponential backoff' &&
                       isset($context['backoff_seconds']) &&
                       $context['backoff_seconds'] > 0;
            });
    }

    /** @test */
    public function it_handles_concurrent_requests_from_multiple_workers()
    {
        // Simulate multiple workers making requests concurrently
        $workerCount = 5;
        $requestsPerWorker = 5;
        $totalRequests = 0;
        $rateLimitHits = 0;
        
        Http::fake(function (Request $request) use (&$totalRequests, &$rateLimitHits) {
            $totalRequests++;
            
            // Simulate rate limiting after 10 requests
            if ($totalRequests > 10) {
                $rateLimitHits++;
                return Http::response([
                    'error' => ['message' => 'Rate limit exceeded']
                ], 429);
            }
            
            return Http::response([
                'choices' => [['message' => ['content' => 'Success']]]
            ], 200);
        });

        $results = [];
        $payload = [
            'model' => 'gpt-4o-mini',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];

        // Simulate concurrent requests from multiple workers
        for ($worker = 0; $worker < $workerCount; $worker++) {
            for ($request = 0; $request < $requestsPerWorker; $request++) {
                $workerKey = "{$this->testKey}-worker-{$worker}";
                $result = $this->service->chatCompletion($payload, $workerKey);
                $results[] = ['worker' => $worker, 'success' => $result !== null];
            }
        }

        // Verify that rate limiting kicked in
        $this->assertEquals($workerCount * $requestsPerWorker, $totalRequests);
        $this->assertGreaterThan(0, $rateLimitHits);
        
        // Count successful vs failed requests
        $successCount = count(array_filter($results, fn($r) => $r['success']));
        $failureCount = count(array_filter($results, fn($r) => !$r['success']));
        
        $this->assertEquals(10, $successCount); // First 10 should succeed
        $this->assertEquals(15, $failureCount); // Rest should fail due to rate limit
    }

    /** @test */
    public function it_properly_waits_before_retrying_after_429_errors()
    {
        $attemptTimes = [];
        
        Http::fake(function (Request $request) use (&$attemptTimes) {
            $attemptTimes[] = microtime(true);
            
            // Always return 429 with Retry-After header
            return Http::response([
                'error' => ['message' => 'Rate limit exceeded']
            ], 429, [
                'Retry-After' => '2'
            ]);
        });

        $payload = [
            'model' => 'gpt-4o-mini',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];

        // Make multiple attempts
        for ($i = 0; $i < 3; $i++) {
            $this->service->chatCompletion($payload, $this->testKey);
            
            // In a real implementation, we would wait based on backoff
            // For testing, we verify the backoff calculation
            $backoffKey = "openai-backoff:{$this->testKey}";
            $attempts = RateLimiter::attempts($backoffKey);
            $expectedBackoff = min(60, pow(2, $attempts - 1));
            
            $this->assertGreaterThan(0, $expectedBackoff);
            $this->assertLessThanOrEqual(60, $expectedBackoff);
        }
    }

    /** @test */
    public function it_handles_batch_processing_with_rate_limits()
    {
        $batchSize = 30; // Exceeds the 20 RPM limit
        $processedCount = 0;
        $rateLimitedCount = 0;
        
        Http::fake(function (Request $request) use (&$processedCount, &$rateLimitedCount) {
            // Check current RPM
            $rpmKey = "openai-rpm:batch-test";
            $currentRpm = RateLimiter::attempts($rpmKey);
            
            if ($currentRpm >= 20) {
                $rateLimitedCount++;
                return Http::response([
                    'error' => ['message' => 'Rate limit exceeded']
                ], 429);
            }
            
            $processedCount++;
            return Http::response([
                'choices' => [['message' => ['content' => 'Processed']]]
            ], 200);
        });

        // Process batch
        $results = [];
        for ($i = 0; $i < $batchSize; $i++) {
            $payload = [
                'model' => 'gpt-4o-mini',
                'messages' => [['role' => 'user', 'content' => "Batch item {$i}"]],
                'max_tokens' => 10
            ];
            
            $result = $this->service->chatCompletion($payload, 'batch-test');
            $results[] = $result !== null;
        }

        // Verify rate limiting behavior
        $successCount = count(array_filter($results));
        $failureCount = count($results) - $successCount;
        
        // Should process up to RPM limit, then fail the rest
        $this->assertLessThanOrEqual(20, $successCount);
        $this->assertEquals($batchSize - $successCount, $failureCount);
    }

    /** @test */
    public function it_integrates_with_queue_jobs_and_retry_logic()
    {
        // Create a mock job that uses the OpenAI service
        $mockJob = Mockery::mock('App\Jobs\ProcessWithOpenAI');
        $mockJob->shouldReceive('handle')->andReturnUsing(function () {
            $service = new RateLimitedOpenAiService();
            return $service->chatCompletion([
                'model' => 'gpt-4o-mini',
                'messages' => [['role' => 'user', 'content' => 'Process this']],
                'max_tokens' => 10
            ], 'queue-test');
        });

        // Simulate 429 error response
        Http::fake([
            'api.openai.com/*' => Http::response([
                'error' => ['message' => 'Rate limit exceeded']
            ], 429)
        ]);

        // Execute the job
        $result = $mockJob->handle();
        
        // Should return null due to rate limit
        $this->assertNull($result);
        
        // Verify backoff was tracked
        $backoffAttempts = RateLimiter::attempts("openai-backoff:queue-test");
        $this->assertEquals(1, $backoffAttempts);
    }

    /** @test */
    public function it_respects_retry_after_header_from_openai()
    {
        $retryAfterSeconds = 5;
        
        Http::fake([
            'api.openai.com/*' => Http::response([
                'error' => ['message' => 'Rate limit exceeded']
            ], 429, [
                'Retry-After' => (string)$retryAfterSeconds,
                'X-RateLimit-Limit-Requests' => '20',
                'X-RateLimit-Remaining-Requests' => '0',
                'X-RateLimit-Reset-Requests' => (string)(time() + $retryAfterSeconds)
            ])
        ]);

        $payload = [
            'model' => 'gpt-4o-mini',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];

        // Make request that will get 429
        $result = $this->service->chatCompletion($payload, $this->testKey);
        $this->assertNull($result);
        
        // In a proper implementation, the service should respect Retry-After
        // and not make requests until that time has passed
        Log::shouldReceive('warning')
            ->withArgs(function ($message, $context) {
                return $message === 'OpenAI API rate limit hit' &&
                       isset($context['status']) &&
                       $context['status'] === 429;
            });
    }

    /** @test */
    public function it_handles_token_limit_errors_differently_from_request_limits()
    {
        // First request - token limit error
        Http::fake([
            'api.openai.com/*' => Http::sequence()
                ->push([
                    'error' => [
                        'message' => 'Rate limit exceeded on tokens per minute',
                        'type' => 'tokens',
                        'code' => 'rate_limit_exceeded'
                    ]
                ], 429)
                ->push([
                    'error' => [
                        'message' => 'Rate limit exceeded on requests per minute',
                        'type' => 'requests',
                        'code' => 'rate_limit_exceeded'
                    ]
                ], 429)
                ->push([
                    'choices' => [['message' => ['content' => 'Success']]]
                ], 200)
        ]);

        $payload = [
            'model' => 'gpt-4o-mini',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];

        // First attempt - token limit
        $result1 = $this->service->chatCompletion($payload, "{$this->testKey}-token");
        $this->assertNull($result1);
        
        // Second attempt - request limit
        $result2 = $this->service->chatCompletion($payload, "{$this->testKey}-request");
        $this->assertNull($result2);
        
        // Third attempt - success
        $result3 = $this->service->chatCompletion($payload, "{$this->testKey}-success");
        $this->assertNotNull($result3);
    }

    /** @test */
    public function it_prevents_thundering_herd_after_rate_limit_recovery()
    {
        // Simulate multiple workers waiting for rate limit to clear
        $workerCount = 10;
        $requestsMade = 0;
        
        Http::fake(function (Request $request) use (&$requestsMade) {
            $requestsMade++;
            
            // Allow first 5 requests after "recovery"
            if ($requestsMade <= 5) {
                return Http::response([
                    'choices' => [['message' => ['content' => 'Success']]]
                ], 200);
            }
            
            // Rate limit the rest
            return Http::response([
                'error' => ['message' => 'Rate limit exceeded again']
            ], 429);
        });

        // Simulate all workers trying to make requests after rate limit clears
        $results = [];
        for ($i = 0; $i < $workerCount; $i++) {
            $payload = [
                'model' => 'gpt-4o-mini',
                'messages' => [['role' => 'user', 'content' => "Worker {$i}"]],
                'max_tokens' => 10
            ];
            
            $result = $this->service->chatCompletion($payload, 'thundering-herd-test');
            $results[] = $result !== null;
        }

        // Verify that not all workers succeeded (preventing thundering herd)
        $successCount = count(array_filter($results));
        $this->assertEquals(5, $successCount);
        $this->assertEquals(5, $workerCount - $successCount);
    }

    /** @test */
    public function it_tracks_rate_limit_metrics_for_monitoring()
    {
        $metrics = [
            'total_requests' => 0,
            'rate_limited_requests' => 0,
            'successful_requests' => 0,
            'backoff_events' => 0
        ];
        
        Http::fake(function (Request $request) use (&$metrics) {
            $metrics['total_requests']++;
            
            if ($metrics['total_requests'] % 3 === 0) {
                $metrics['rate_limited_requests']++;
                return Http::response([
                    'error' => ['message' => 'Rate limit exceeded']
                ], 429);
            }
            
            $metrics['successful_requests']++;
            return Http::response([
                'choices' => [['message' => ['content' => 'Success']]]
            ], 200);
        });

        // Make several requests
        for ($i = 0; $i < 10; $i++) {
            $payload = [
                'model' => 'gpt-4o-mini',
                'messages' => [['role' => 'user', 'content' => "Request {$i}"]],
                'max_tokens' => 10
            ];
            
            $result = $this->service->chatCompletion($payload, 'metrics-test');
            
            if ($result === null) {
                $backoffKey = "openai-backoff:metrics-test";
                if (RateLimiter::attempts($backoffKey) > 0) {
                    $metrics['backoff_events'] = RateLimiter::attempts($backoffKey);
                }
            }
        }

        // Verify metrics
        $this->assertEquals(10, $metrics['total_requests']);
        $this->assertGreaterThan(0, $metrics['rate_limited_requests']);
        $this->assertGreaterThan(0, $metrics['successful_requests']);
        $this->assertEquals(
            $metrics['total_requests'],
            $metrics['successful_requests'] + $metrics['rate_limited_requests']
        );
    }

    /** @test */
    public function it_implements_circuit_breaker_pattern_after_repeated_429s()
    {
        $consecutiveFailures = 0;
        $circuitOpen = false;
        
        Http::fake(function (Request $request) use (&$consecutiveFailures) {
            // Always return 429 to simulate persistent rate limiting
            return Http::response([
                'error' => ['message' => 'Rate limit exceeded']
            ], 429);
        });

        // Make multiple attempts
        for ($i = 0; $i < 5; $i++) {
            $payload = [
                'model' => 'gpt-4o-mini',
                'messages' => [['role' => 'user', 'content' => "Attempt {$i}"]],
                'max_tokens' => 10
            ];
            
            $result = $this->service->chatCompletion($payload, 'circuit-breaker-test');
            
            if ($result === null) {
                $consecutiveFailures++;
            }
            
            // After 3 consecutive failures, circuit should "open"
            if ($consecutiveFailures >= 3) {
                $circuitOpen = true;
                break;
            }
        }

        // Verify circuit breaker behavior
        $this->assertTrue($circuitOpen);
        $this->assertGreaterThanOrEqual(3, $consecutiveFailures);
        
        // Check backoff attempts
        $backoffAttempts = RateLimiter::attempts("openai-backoff:circuit-breaker-test");
        $this->assertGreaterThanOrEqual(3, $backoffAttempts);
    }
}