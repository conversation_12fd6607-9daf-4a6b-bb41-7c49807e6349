<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\AI\RateLimitedAnthropicService;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Http;

class RateLimitedAnthropicServiceTest extends TestCase
{
    protected $service;
    protected $testKey = 'test-anthropic-service';

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new RateLimitedAnthropicService();
        
        // Clear any existing rate limits for clean testing
        RateLimiter::clear("anthropic-rpm:{$this->testKey}");
        RateLimiter::clear("anthropic-itpm:{$this->testKey}");
        RateLimiter::clear("anthropic-otpm:{$this->testKey}");
        RateLimiter::clear("anthropic-backoff:{$this->testKey}");
    }

    protected function tearDown(): void
    {
        // Clean up after tests
        RateLimiter::clear("anthropic-rpm:{$this->testKey}");
        RateLimiter::clear("anthropic-itpm:{$this->testKey}");
        RateLimiter::clear("anthropic-otpm:{$this->testKey}");
        RateLimiter::clear("anthropic-backoff:{$this->testKey}");
        parent::tearDown();
    }

    /** @test */
    public function it_tracks_rate_limits_correctly()
    {
        // Initial state - no limits
        $status = $this->service->getRateLimitStatus($this->testKey);
        
        $this->assertEquals(0, $status['rpm']['current']);
        $this->assertEquals(50, $status['rpm']['limit']);
        $this->assertEquals(50, $status['rpm']['remaining']);
        $this->assertEquals(0, $status['rpm']['available_in']);
        
        $this->assertEquals(0, $status['itpm']['current']);
        $this->assertEquals(40000, $status['itpm']['limit']);
        $this->assertEquals(40000, $status['itpm']['remaining']);
        $this->assertEquals(0, $status['itpm']['available_in']);
        
        $this->assertEquals(0, $status['otpm']['current']);
        $this->assertEquals(8000, $status['otpm']['limit']);
        $this->assertEquals(8000, $status['otpm']['remaining']);
        $this->assertEquals(0, $status['otpm']['available_in']);
    }

    /** @test */
    public function it_prevents_requests_when_rpm_limit_exceeded()
    {
        // Simulate 50 requests (at the limit)
        for ($i = 0; $i < 50; $i++) {
            RateLimiter::increment("anthropic-rpm:{$this->testKey}");
        }
        
        $status = $this->service->getRateLimitStatus($this->testKey);
        
        $this->assertEquals(50, $status['rpm']['current']);
        $this->assertEquals(0, $status['rpm']['remaining']);
        $this->assertGreaterThan(0, $status['rpm']['available_in']);
    }

    /** @test */
    public function it_estimates_input_tokens_correctly()
    {
        $payload = [
            'messages' => [
                ['role' => 'user', 'content' => 'Hello world'],  // ~3 tokens
                ['role' => 'assistant', 'content' => 'Hi there!'], // ~3 tokens
                ['role' => 'user', 'content' => 'How are you doing today?'] // ~6 tokens
            ],
            'system' => 'You are a helpful assistant', // ~6 tokens
            'max_tokens' => 500
        ];
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('estimateInputTokens');
        $method->setAccessible(true);
        
        $estimated = $method->invoke($this->service, $payload);
        
        // Should be around 18 tokens + 50 buffer = ~68
        $this->assertGreaterThan(50, $estimated);
        $this->assertLessThan(100, $estimated);
    }

    /** @test */
    public function it_allows_requests_within_limits()
    {
        // Mock successful HTTP response
        Http::fake([
            'api.anthropic.com/*' => Http::response([
                'content' => [
                    ['type' => 'text', 'text' => 'Test response']
                ],
                'usage' => [
                    'input_tokens' => 20,
                    'output_tokens' => 10
                ]
            ], 200)
        ]);
        
        $payload = [
            'model' => 'claude-3-sonnet-20240229',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];
        
        $result = $this->service->messages($payload, $this->testKey);
        
        $this->assertNotNull($result);
        $this->assertArrayHasKey('content', $result);
        
        // Check that counters were incremented
        $status = $this->service->getRateLimitStatus($this->testKey);
        $this->assertEquals(1, $status['rpm']['current']);
        $this->assertGreaterThan(0, $status['itpm']['current']);
        $this->assertGreaterThan(0, $status['otpm']['current']);
    }

    /** @test */
    public function it_handles_rate_limit_errors_from_api()
    {
        // Mock 429 rate limit response
        Http::fake([
            'api.anthropic.com/*' => Http::response([
                'error' => ['message' => 'Rate limit exceeded']
            ], 429)
        ]);
        
        $payload = [
            'model' => 'claude-3-sonnet-20240229',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];
        
        $result = $this->service->messages($payload, $this->testKey);
        
        $this->assertNull($result);
    }

    /** @test */
    public function it_clears_rate_limits_correctly()
    {
        // Set some rate limits
        RateLimiter::increment("anthropic-rpm:{$this->testKey}", 5);
        RateLimiter::increment("anthropic-itpm:{$this->testKey}", 1000);
        RateLimiter::increment("anthropic-otpm:{$this->testKey}", 200);
        
        // Verify they exist
        $status = $this->service->getRateLimitStatus($this->testKey);
        $this->assertEquals(5, $status['rpm']['current']);
        $this->assertEquals(1000, $status['itpm']['current']);
        $this->assertEquals(200, $status['otpm']['current']);
        
        // Clear them
        $this->service->clearRateLimit($this->testKey);
        
        // Verify they're cleared
        $status = $this->service->getRateLimitStatus($this->testKey);
        $this->assertEquals(0, $status['rpm']['current']);
        $this->assertEquals(0, $status['itpm']['current']);
        $this->assertEquals(0, $status['otpm']['current']);
    }

    /** @test */
    public function it_returns_null_when_itpm_limit_would_be_exceeded()
    {
        // Set ITPM close to limit
        RateLimiter::increment("anthropic-itpm:{$this->testKey}", 39500);
        
        $payload = [
            'model' => 'claude-3-sonnet-20240229',
            'messages' => [['role' => 'user', 'content' => 'This is a very long message that would push us over the input token limit when combined with the system prompt and other messages in this conversation context']],
            'max_tokens' => 1000
        ];
        
        $result = $this->service->messages($payload, $this->testKey);
        
        $this->assertNull($result);
    }
}