<?php

namespace Tests\Unit\Notifications;

use Tests\TestCase;
use App\Notifications\JobFailedNotification;
use App\Models\User;
use App\Jobs\SearchCandidatesJob;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class JobFailedNotificationTest extends TestCase
{
    use DatabaseTransactions;

    public function test_notification_contains_required_job_details()
    {
        $exception = new \Exception('Database connection failed');
        $jobData = ['plan_id' => 123, 'user_id' => 456];
        
        $notification = new JobFailedNotification(
            SearchCandidatesJob::class,
            $exception,
            $jobData
        );
        
        $admin = User::factory()->admin()->create();
        $array = $notification->toArray($admin);
        
        $this->assertEquals(SearchCandidatesJob::class, $array['job_class']);
        $this->assertEquals('Database connection failed', $array['error_message']);
        $this->assertArrayHasKey('failed_at', $array);
        $this->assertEquals(123, $array['job_data']['plan_id']);
    }

    public function test_notification_uses_correct_channels_based_on_job_criticality()
    {
        $notification = new JobFailedNotification(
            SearchCandidatesJob::class,
            new \Exception('Test'),
            []
        );
        
        $admin = User::factory()->admin()->create();
        $channels = $notification->via($admin);
        
        // External search jobs are critical, should send email + database
        $this->assertContains('mail', $channels);
        $this->assertContains('database', $channels);
    }

    public function test_non_critical_jobs_only_use_database_channel()
    {
        // Test with a non-critical job class
        $notification = new JobFailedNotification(
            'App\\Jobs\\NonCriticalJob',
            new \Exception('Test'),
            []
        );
        
        $admin = User::factory()->admin()->create();
        $channels = $notification->via($admin);
        
        $this->assertContains('database', $channels);
        $this->assertNotContains('mail', $channels);
    }
}
