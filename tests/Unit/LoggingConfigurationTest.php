<?php

namespace Tests\Unit;

use Tests\TestCase;

class LoggingConfigurationTest extends TestCase
{
    public function test_default_log_channel_uses_stack(): void
    {
        $defaultChannel = config('logging.default');
        $this->assertEquals('stack', $defaultChannel);
    }

    public function test_stack_channel_uses_daily_logging(): void
    {
        $stackChannels = config('logging.channels.stack.channels');
        $this->assertContains('daily', $stackChannels);
    }

    public function test_daily_channel_retention_is_30_days(): void
    {
        $dailyChannelDays = config('logging.channels.daily.days');
        $this->assertEquals(30, $dailyChannelDays);
    }

    public function test_daily_channel_has_correct_configuration(): void
    {
        $dailyConfig = config('logging.channels.daily');

        $this->assertEquals('daily', $dailyConfig['driver']);
        $this->assertEquals(storage_path('logs/laravel.log'), $dailyConfig['path']);
        $this->assertEquals(30, $dailyConfig['days']);
        $this->assertTrue($dailyConfig['replace_placeholders']);
    }

    public function test_logging_configuration_exists(): void
    {
        $loggingConfig = config('logging');

        $this->assertIsArray($loggingConfig);
        $this->assertArrayHasKey('default', $loggingConfig);
        $this->assertArrayHasKey('channels', $loggingConfig);
        $this->assertArrayHasKey('stack', $loggingConfig['channels']);
        $this->assertArrayHasKey('daily', $loggingConfig['channels']);
    }

    public function test_can_write_to_daily_log(): void
    {
        // Clear any existing log content
        $logFile = storage_path('logs/laravel-'.date('Y-m-d').'.log');

        $testMessage = 'Test log message for daily logging: '.uniqid();

        // Write log message
        logger()->info($testMessage);

        // Check if log file was created and contains our message
        $this->assertFileExists($logFile);
        $logContent = file_get_contents($logFile);
        $this->assertStringContainsString($testMessage, $logContent);
    }
}
