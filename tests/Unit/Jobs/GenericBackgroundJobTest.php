<?php

namespace Tests\Unit\Jobs;

use Tests\TestCase;
use App\Jobs\GenericBackgroundJob;
use App\Models\BackgroundProcess;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Mockery;
use Exception;
use InvalidArgumentException;

class GenericBackgroundJobTest extends TestCase
{
    use DatabaseTransactions;

    protected $testClass;
    protected $testMethod;
    protected $testParameters;
    protected $backgroundProcess;

    protected function setUp(): void
    {
        parent::setUp();

        $this->testClass = TestServiceClass::class;
        $this->testMethod = 'testMethod';
        $this->testParameters = ['param1' => 'value1', 'param2' => 'value2'];

        // Create test background process
        $this->backgroundProcess = BackgroundProcess::create([
            'class_name' => $this->testClass,
            'method_name' => $this->testMethod,
            'parameters' => json_encode($this->testParameters),
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function it_constructs_with_correct_properties()
    {
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $reflection = new \ReflectionClass($job);
        
        $classProperty = $reflection->getProperty('class');
        $classProperty->setAccessible(true);
        $this->assertEquals($this->testClass, $classProperty->getValue($job));

        $methodProperty = $reflection->getProperty('method');
        $methodProperty->setAccessible(true);
        $this->assertEquals($this->testMethod, $methodProperty->getValue($job));

        $parametersProperty = $reflection->getProperty('parameters');
        $parametersProperty->setAccessible(true);
        $this->assertEquals($this->testParameters, $parametersProperty->getValue($job));

        $processIdProperty = $reflection->getProperty('processId');
        $processIdProperty->setAccessible(true);
        $this->assertEquals($this->backgroundProcess->id, $processIdProperty->getValue($job));

        // Test timeout and tries are set correctly
        $this->assertEquals(1800, $job->timeout);
        $this->assertEquals(3, $job->tries);
    }

    /** @test */
    public function it_skips_when_process_not_found()
    {
        Log::spy();

        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            999 // Non-existent process ID
        );

        $job->handle();

        Log::shouldHaveReceived('warning')
            ->once()
            ->with('GENERIC JOB: Process 999 not found, skipping job', [
                'class' => $this->testClass,
                'method' => $this->testMethod
            ]);
    }

    /** @test */
    public function it_implements_idempotent_behavior_for_completed_process()
    {
        // Mark process as completed
        $this->backgroundProcess->update(['status' => 'completed']);

        Log::spy();

        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $job->handle();

        Log::shouldHaveReceived('info')
            ->once()
            ->with("GENERIC JOB: Process {$this->backgroundProcess->id} already completed, skipping", [
                'class' => $this->testClass,
                'method' => $this->testMethod
            ]);
    }

    /** @test */
    public function it_throws_exception_for_non_existent_class()
    {
        $job = new GenericBackgroundJob(
            'NonExistentClass',
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Class does not exist: NonExistentClass');

        $job->handle();

        // Check process was marked as failed
        $this->backgroundProcess->refresh();
        $this->assertEquals('failed', $this->backgroundProcess->status);
        $this->assertStringContains('Class does not exist: NonExistentClass', $this->backgroundProcess->output);
    }

    /** @test */
    public function it_throws_exception_for_non_existent_method()
    {
        $job = new GenericBackgroundJob(
            $this->testClass,
            'nonExistentMethod',
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Method does not exist: {$this->testClass}::nonExistentMethod");

        $job->handle();

        // Check process was marked as failed
        $this->backgroundProcess->refresh();
        $this->assertEquals('failed', $this->backgroundProcess->status);
    }

    /** @test */
    public function it_executes_successful_method_call()
    {
        // Create a test class that we can actually call
        $testClass = TestServiceClass::class;
        
        $job = new GenericBackgroundJob(
            $testClass,
            'successfulMethod',
            ['input' => 'test'],
            $this->backgroundProcess->id
        );

        $job->handle();

        // Check process was marked as completed
        $this->backgroundProcess->refresh();
        $this->assertEquals('completed', $this->backgroundProcess->status);
        
        $output = json_decode($this->backgroundProcess->output, true);
        $this->assertEquals(['result' => 'success', 'input' => 'test'], $output);
        $this->assertNotNull($this->backgroundProcess->completed_at);
    }

    /** @test */
    public function it_handles_method_exceptions_properly()
    {
        $job = new GenericBackgroundJob(
            TestServiceClass::class,
            'failingMethod',
            [],
            $this->backgroundProcess->id
        );

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Method failed intentionally');

        $job->handle();

        // Check process was marked as failed
        $this->backgroundProcess->refresh();
        $this->assertEquals('failed', $this->backgroundProcess->status);
        $this->assertEquals('Method failed intentionally', $this->backgroundProcess->output);
        $this->assertNotNull($this->backgroundProcess->failed_at);
    }

    /** @test */
    public function it_updates_process_to_running_before_execution()
    {
        Log::spy();

        $job = new GenericBackgroundJob(
            TestServiceClass::class,
            'successfulMethod',
            ['input' => 'test'],
            $this->backgroundProcess->id
        );

        $job->handle();

        Log::shouldHaveReceived('info')
            ->once()
            ->with('GENERIC JOB: Starting background process', [
                'process_id' => $this->backgroundProcess->id,
                'class' => TestServiceClass::class,
                'method' => 'successfulMethod'
            ]);

        // Process should have been marked as running during execution
        // (We can't check this mid-execution, but we can verify final state)
        $this->backgroundProcess->refresh();
        $this->assertEquals('completed', $this->backgroundProcess->status);
    }

    /** @test */
    public function it_logs_failure_properly_in_failed_method()
    {
        Log::spy();

        $exception = new Exception('Test failure');
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $job->failed($exception);

        Log::shouldHaveReceived('error')
            ->once()
            ->with('GENERIC JOB: Background process failed after all retry attempts', Mockery::on(function ($data) use ($exception) {
                return $data['job'] === GenericBackgroundJob::class
                    && $data['process_id'] === $this->backgroundProcess->id
                    && $data['class'] === $this->testClass
                    && $data['method'] === $this->testMethod
                    && $data['parameters'] === $this->testParameters
                    && $data['exception'] === 'Test failure'
                    && $data['max_attempts'] === 3
                    && isset($data['backoff_delays'])
                    && isset($data['retry_until']);
            }));
    }

    /** @test */
    public function it_updates_process_record_on_final_failure()
    {
        $exception = new Exception('Final failure');
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $job->failed($exception);

        $this->backgroundProcess->refresh();
        $this->assertEquals('failed', $this->backgroundProcess->status);
        $this->assertEquals('Job failed after 3 attempts: Final failure', $this->backgroundProcess->output);
        $this->assertNotNull($this->backgroundProcess->failed_at);
    }

    /** @test */
    public function it_handles_process_update_failure_gracefully()
    {
        Log::spy();

        // Delete the process to simulate update failure
        $processId = $this->backgroundProcess->id;
        $this->backgroundProcess->delete();

        $exception = new Exception('Test failure');
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $processId
        );

        $job->failed($exception);

        Log::shouldHaveReceived('warning')
            ->once()
            ->with('GENERIC JOB: Failed to update process record after job failure', [
                'process_id' => $processId,
                'error' => Mockery::any()
            ]);
    }

    /** @test */
    public function it_configures_exponential_backoff_properly()
    {
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        // Test backoff configuration through reflection
        $reflection = new \ReflectionClass($job);
        
        $baseDelayProperty = $reflection->getProperty('baseDelay');
        $baseDelayProperty->setAccessible(true);
        $this->assertEquals(30, $baseDelayProperty->getValue($job));

        $maxDelayProperty = $reflection->getProperty('maxDelay');
        $maxDelayProperty->setAccessible(true);
        $this->assertEquals(900, $maxDelayProperty->getValue($job));
    }

    /** @test */
    public function it_sets_correct_retry_until_timestamp()
    {
        $job = new GenericBackgroundJob(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        $retryUntil = $job->retryUntil();

        // Should be 2 hours from now (allowing some tolerance for test execution time)
        $expectedTime = now()->addHours(2);
        $this->assertTrue($retryUntil->diffInMinutes($expectedTime) < 1);
    }

    /** @test */
    public function it_can_be_dispatched_to_queue()
    {
        Queue::fake();

        GenericBackgroundJob::dispatch(
            $this->testClass,
            $this->testMethod,
            $this->testParameters,
            $this->backgroundProcess->id
        );

        Queue::assertPushed(GenericBackgroundJob::class);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

/**
 * Test service class for testing GenericBackgroundJob
 */
class TestServiceClass
{
    public static function successfulMethod($input = null)
    {
        return ['result' => 'success', 'input' => $input];
    }

    public static function failingMethod()
    {
        throw new Exception('Method failed intentionally');
    }

    public static function testMethod()
    {
        return ['test' => 'result'];
    }
}