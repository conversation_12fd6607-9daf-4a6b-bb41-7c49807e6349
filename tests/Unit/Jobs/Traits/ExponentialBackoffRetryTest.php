<?php

namespace Tests\Unit\Jobs\Traits;

use Tests\TestCase;
use App\Jobs\Traits\ExponentialBackoffRetry;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ExponentialBackoffRetryTest extends TestCase
{
    protected $testJob;

    protected function setUp(): void
    {
        parent::setUp();
        $this->testJob = new TestJobWithBackoff();
    }

    /** @test */
    public function it_calculates_exponential_backoff_delays_correctly()
    {
        // Test default configuration
        $delays = $this->testJob->backoff();

        $this->assertCount(5, $delays); // Default maxRetries is 5
        $this->assertEquals(30, $delays[0]); // First retry: 30 seconds
        $this->assertEquals(60, $delays[1]); // Second retry: 30 * 2^1 = 60 seconds
        $this->assertEquals(120, $delays[2]); // Third retry: 30 * 2^2 = 120 seconds
        $this->assertEquals(240, $delays[3]); // Fourth retry: 30 * 2^3 = 240 seconds
        $this->assertEquals(480, $delays[4]); // Fifth retry: 30 * 2^4 = 480 seconds
    }

    /** @test */
    public function it_respects_maximum_delay_cap()
    {
        $this->testJob->setMaxDelay(100); // Cap at 100 seconds
        $delays = $this->testJob->backoff();

        // All delays should be capped at 100 seconds
        foreach ($delays as $delay) {
            $this->assertLessThanOrEqual(100, $delay);
        }

        // Later retries should be capped
        $this->assertEquals(100, $delays[2]); // Would be 120, but capped at 100
        $this->assertEquals(100, $delays[3]); // Would be 240, but capped at 100
        $this->assertEquals(100, $delays[4]); // Would be 480, but capped at 100
    }

    /** @test */
    public function it_applies_jitter_when_enabled()
    {
        $this->testJob->enableJitter(true);
        
        // Generate multiple sets of delays to test jitter randomness
        $delays1 = $this->testJob->backoff();
        $delays2 = $this->testJob->backoff();

        // With jitter, delays should vary (this test might occasionally fail due to randomness)
        $hasVariation = false;
        for ($i = 0; $i < count($delays1); $i++) {
            if ($delays1[$i] !== $delays2[$i]) {
                $hasVariation = true;
                break;
            }
        }

        // All delays should still be positive
        foreach ($delays1 as $delay) {
            $this->assertGreaterThan(0, $delay);
        }
    }

    /** @test */
    public function it_does_not_apply_jitter_when_disabled()
    {
        $this->testJob->enableJitter(false);
        
        $delays1 = $this->testJob->backoff();
        $delays2 = $this->testJob->backoff();

        // Without jitter, delays should be identical
        $this->assertEquals($delays1, $delays2);
    }

    /** @test */
    public function it_allows_configuration_of_base_delay()
    {
        $this->testJob->setBaseDelay(60); // 1 minute base
        $delays = $this->testJob->backoff();

        $this->assertEquals(60, $delays[0]);  // First retry: 60 seconds
        $this->assertEquals(120, $delays[1]); // Second retry: 60 * 2 = 120 seconds
        $this->assertEquals(240, $delays[2]); // Third retry: 60 * 4 = 240 seconds
    }

    /** @test */
    public function it_allows_configuration_of_backoff_multiplier()
    {
        $this->testJob->setBackoffMultiplier(3); // Triple instead of double
        $delays = $this->testJob->backoff();

        $this->assertEquals(30, $delays[0]);  // First retry: 30 seconds
        $this->assertEquals(90, $delays[1]);  // Second retry: 30 * 3 = 90 seconds
        $this->assertEquals(270, $delays[2]); // Third retry: 30 * 9 = 270 seconds
    }

    /** @test */
    public function it_allows_configuration_of_max_retries()
    {
        $this->testJob->setMaxRetries(3);
        $delays = $this->testJob->backoff();

        $this->assertCount(3, $delays);
        $this->assertEquals(3, $this->testJob->tries);
    }

    /** @test */
    public function it_sets_retry_until_timestamp_correctly()
    {
        $this->testJob->setMaxRetryTime(180); // 3 hours
        $retryUntil = $this->testJob->retryUntil();

        $expectedTime = now()->addMinutes(180);
        $this->assertTrue($retryUntil->diffInMinutes($expectedTime) < 1);
    }

    /** @test */
    public function it_handles_minimum_values_properly()
    {
        // Test with very small values
        $this->testJob->setBaseDelay(0); // Should be adjusted to minimum 1
        $this->testJob->setMaxRetries(0); // Should be adjusted to minimum 1

        $delays = $this->testJob->backoff();
        
        $this->assertCount(1, $delays);
        $this->assertGreaterThanOrEqual(1, $delays[0]);
    }

    /** @test */
    public function it_logs_failure_with_comprehensive_information()
    {
        Log::spy();

        $exception = new \Exception('Test failure');
        $this->testJob->failed($exception);

        Log::shouldHaveReceived('error')
            ->once()
            ->with('Job failed after all retry attempts with exponential backoff', \Mockery::on(function ($data) {
                return $data['job'] === TestJobWithBackoff::class
                    && $data['exception'] === 'Test failure'
                    && isset($data['max_attempts'])
                    && isset($data['backoff_delays'])
                    && isset($data['retry_until']);
            }));
    }

    /** @test */
    public function it_provides_fluent_interface_for_configuration()
    {
        $result = $this->testJob
            ->setBaseDelay(45)
            ->setBackoffMultiplier(1.5)
            ->setMaxRetries(4)
            ->setMaxDelay(300)
            ->setMaxRetryTime(90)
            ->enableJitter(true);

        $this->assertInstanceOf(TestJobWithBackoff::class, $result);

        // Verify all configurations were applied
        $delays = $this->testJob->backoff();
        $this->assertCount(4, $delays);
        
        // With jitter enabled, the exact value may vary slightly, so we check range
        $this->assertGreaterThanOrEqual(35, $delays[0]); // Allow for jitter variation
        $this->assertLessThanOrEqual(55, $delays[0]);
    }

    /** @test */
    public function it_handles_edge_case_configurations()
    {
        // Test with unusual but valid configurations
        $this->testJob
            ->setBaseDelay(1)
            ->setBackoffMultiplier(1.1) // Very small multiplier
            ->setMaxRetries(10)
            ->setMaxDelay(5);

        $delays = $this->testJob->backoff();
        
        $this->assertCount(10, $delays);
        
        // All delays should be capped at maxDelay
        foreach ($delays as $delay) {
            $this->assertLessThanOrEqual(5, $delay);
            $this->assertGreaterThanOrEqual(1, $delay);
        }
    }

    /** @test */
    public function it_maintains_delay_progression_within_limits()
    {
        $this->testJob->setMaxDelay(1000);
        $delays = $this->testJob->backoff();

        // Check that delays generally increase (unless capped)
        for ($i = 1; $i < count($delays); $i++) {
            if ($delays[$i-1] < 1000) { // If previous wasn't capped
                $this->assertGreaterThanOrEqual($delays[$i-1], $delays[$i]);
            }
        }
    }
}

/**
 * Test job class that uses the ExponentialBackoffRetry trait
 */
class TestJobWithBackoff implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ExponentialBackoffRetry;

    protected $planData;
    protected $user;

    public function __construct()
    {
        $this->planData = ['plan_id' => 'test', 'plan_name' => 'Test Plan'];
        $this->user = (object)['id' => 1];
    }

    public function handle()
    {
        // Test job implementation
    }
}