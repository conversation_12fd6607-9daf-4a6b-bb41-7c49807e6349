<?php

namespace Tests\Unit\Jobs;

use Tests\TestCase;
use App\Jobs\TalentPoolExternalSearch;
use App\Services\AI\ExternalPeopleSearch;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Mockery;
use Exception;

class TalentPoolExternalSearchTest extends TestCase
{
    use DatabaseTransactions;

    protected $user;
    protected $planData;
    protected $mockExternalSearchService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'id' => 1,
            'email' => '<EMAIL>',
            'name' => 'Test User'
        ]);

        // Test plan data
        $this->planData = [
            'plan_id' => 123,
            'plan_name' => 'Test Succession Plan',
            'target_roles' => ['CEO', 'CTO'],
            'companies' => ['TechCorp', 'InnovateCorp'],
            'qualifications' => ['MBA', 'Engineering'],
            'skills' => ['Leadership', 'Strategy']
        ];

        // Mock the external search service
        $this->mockExternalSearchService = Mockery::mock(ExternalPeopleSearch::class);
        $this->app->instance(ExternalPeopleSearch::class, $this->mockExternalSearchService);
    }

    /** @test */
    public function it_constructs_with_correct_properties()
    {
        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);

        $reflection = new \ReflectionClass($job);
        
        $planDataProperty = $reflection->getProperty('planData');
        $planDataProperty->setAccessible(true);
        $this->assertEquals($this->planData, $planDataProperty->getValue($job));

        $userIdProperty = $reflection->getProperty('userId');
        $userIdProperty->setAccessible(true);
        $this->assertEquals($this->user->id, $userIdProperty->getValue($job));

        // Test timeout and tries are set correctly
        $this->assertEquals(2400, $job->timeout);
        $this->assertEquals(5, $job->tries);
    }

    /** @test */
    public function it_sets_correct_queue_configuration()
    {
        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        
        $this->assertEquals('external_search', $job->queue);
    }

    /** @test */
    public function it_handles_successful_external_search()
    {
        // Mock successful search results
        $mockResults = [
            ['name' => 'John Doe', 'role' => 'CEO', 'company' => 'TechCorp'],
            ['name' => 'Jane Smith', 'role' => 'CTO', 'company' => 'InnovateCorp']
        ];

        $this->mockExternalSearchService
            ->shouldReceive('searchExternalCandidates')
            ->once()
            ->with($this->planData, $this->user)
            ->andReturn($mockResults);

        // Ensure no existing notification
        DB::table('job_queues_notification')->where('user_id', $this->user->id)->delete();

        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->handle($this->mockExternalSearchService);

        // Assert notification was created
        $this->assertDatabaseHas('job_queues_notification', [
            'user_id' => $this->user->id,
            'plan_id' => 123,
            'job_type' => 'external_search',
            'status' => 'completed',
            'message' => 'External talent search completed successfully'
        ]);
    }

    /** @test */
    public function it_skips_when_user_not_found()
    {
        Log::spy();

        $job = new TalentPoolExternalSearch($this->planData, 999); // Non-existent user
        $job->handle($this->mockExternalSearchService);

        Log::shouldHaveReceived('warning')
            ->once()
            ->with('EXTERNAL SEARCH: User 999 not found, skipping job', [
                'plan_id' => 123
            ]);

        // Should not call external search service
        $this->mockExternalSearchService->shouldNotHaveReceived('searchExternalCandidates');
    }

    /** @test */
    public function it_implements_idempotent_behavior()
    {
        // Create existing completion notification
        DB::table('job_queues_notification')->insert([
            'user_id' => $this->user->id,
            'plan_id' => 123,
            'job_type' => 'external_search',
            'status' => 'completed',
            'message' => 'Already completed',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        Log::spy();

        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->handle($this->mockExternalSearchService);

        Log::shouldHaveReceived('info')
            ->once()
            ->with('EXTERNAL SEARCH: Search already processed for plan 123, skipping', [
                'plan_id' => 123,
                'user_id' => $this->user->id
            ]);

        // Should not call external search service
        $this->mockExternalSearchService->shouldNotHaveReceived('searchExternalCandidates');
    }

    /** @test */
    public function it_does_not_create_notification_for_empty_results()
    {
        $this->mockExternalSearchService
            ->shouldReceive('searchExternalCandidates')
            ->once()
            ->with($this->planData, $this->user)
            ->andReturn([]); // Empty results

        // Ensure no existing notification
        DB::table('job_queues_notification')->where('user_id', $this->user->id)->delete();

        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->handle($this->mockExternalSearchService);

        // Assert no notification was created
        $this->assertDatabaseMissing('job_queues_notification', [
            'user_id' => $this->user->id,
            'plan_id' => 123,
            'job_type' => 'external_search',
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function it_handles_service_exceptions_properly()
    {
        $exception = new Exception('External API failed');

        $this->mockExternalSearchService
            ->shouldReceive('searchExternalCandidates')
            ->once()
            ->with($this->planData, $this->user)
            ->andThrow($exception);

        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('External API failed');

        $job->handle($this->mockExternalSearchService);
    }

    /** @test */
    public function it_logs_failure_properly_in_failed_method()
    {
        Log::spy();

        $exception = new Exception('Test failure');
        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->failed($exception);

        Log::shouldHaveReceived('error')
            ->once()
            ->with('EXTERNAL SEARCH: Job failed after all retry attempts', Mockery::on(function ($data) use ($exception) {
                return $data['job'] === TalentPoolExternalSearch::class
                    && $data['plan_id'] === 123
                    && $data['plan_name'] === 'Test Succession Plan'
                    && $data['user_id'] === $this->user->id
                    && $data['exception'] === 'Test failure'
                    && $data['max_attempts'] === 5
                    && isset($data['backoff_delays'])
                    && isset($data['retry_until']);
            }));
    }

    /** @test */
    public function it_creates_error_notification_on_failure()
    {
        $exception = new Exception('Test failure');
        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->failed($exception);

        $this->assertDatabaseHas('job_queues_notification', [
            'user_id' => $this->user->id,
            'plan_id' => 123,
            'job_type' => 'external_search',
            'status' => 'failed',
            'message' => 'External talent search failed: Test failure'
        ]);
    }

    /** @test */
    public function it_cleans_up_partial_data_on_failure()
    {
        // Create some test pipeline data that should be cleaned up
        DB::table('pipeline')->insert([
            'plan_id' => 123,
            'type' => 'External-System',
            'name' => 'Test Candidate',
            'created_at' => now()->subMinutes(30), // Within cleanup window
            'completed_at' => null // Incomplete
        ]);

        $exception = new Exception('Test failure');
        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->failed($exception);

        // Assert partial data was cleaned up
        $this->assertDatabaseMissing('pipeline', [
            'plan_id' => 123,
            'type' => 'External-System',
            'name' => 'Test Candidate',
            'completed_at' => null
        ]);
    }

    /** @test */
    public function it_configures_exponential_backoff_properly()
    {
        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);

        // Test backoff configuration through reflection
        $reflection = new \ReflectionClass($job);
        
        $baseDelayProperty = $reflection->getProperty('baseDelay');
        $baseDelayProperty->setAccessible(true);
        $this->assertEquals(60, $baseDelayProperty->getValue($job));

        $maxDelayProperty = $reflection->getProperty('maxDelay');
        $maxDelayProperty->setAccessible(true);
        $this->assertEquals(1800, $maxDelayProperty->getValue($job));

        $jitterEnabledProperty = $reflection->getProperty('jitterEnabled');
        $jitterEnabledProperty->setAccessible(true);
        $this->assertTrue($jitterEnabledProperty->getValue($job));
    }

    /** @test */
    public function it_sets_correct_retry_until_timestamp()
    {
        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $retryUntil = $job->retryUntil();

        // Should be 4 hours from now (allowing some tolerance for test execution time)
        $expectedTime = now()->addHours(4);
        $this->assertTrue($retryUntil->diffInMinutes($expectedTime) < 1);
    }

    /** @test */
    public function it_can_be_dispatched_to_queue()
    {
        Queue::fake();

        TalentPoolExternalSearch::dispatch($this->planData, $this->user->id);

        Queue::assertPushed(TalentPoolExternalSearch::class, function ($job) {
            return $job->queue === 'external_search';
        });
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}