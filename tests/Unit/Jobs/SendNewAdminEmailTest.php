<?php

namespace Tests\Unit\Jobs;

use Tests\TestCase;
use App\Jobs\SendNewAdminEmail;
use App\Mail\NewadminEmail;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Mockery;
use Exception;

class SendNewAdminEmailTest extends TestCase
{
    use DatabaseTransactions;

    protected $adminEmail;
    protected $inviteUrl;
    protected $name;

    protected function setUp(): void
    {
        parent::setUp();

        $this->adminEmail = '<EMAIL>';
        $this->inviteUrl = 'https://example.com/invite/abc123';
        $this->name = 'John Admin';
    }

    /** @test */
    public function it_constructs_with_correct_properties()
    {
        $job = new SendNewAdminEmail($this->adminEmail, $this->inviteUrl, $this->name);

        $reflection = new \ReflectionClass($job);
        
        $emailProperty = $reflection->getProperty('adminEmail');
        $emailProperty->setAccessible(true);
        $this->assertEquals($this->adminEmail, $emailProperty->getValue($job));

        $urlProperty = $reflection->getProperty('inviteUrl');
        $urlProperty->setAccessible(true);
        $this->assertEquals($this->inviteUrl, $urlProperty->getValue($job));

        $nameProperty = $reflection->getProperty('name');
        $nameProperty->setAccessible(true);
        $this->assertEquals($this->name, $nameProperty->getValue($job));

        // Test timeout and tries are set correctly
        $this->assertEquals(300, $job->timeout);
        $this->assertEquals(3, $job->tries);
    }

    /** @test */
    public function it_sends_email_successfully()
    {
        Mail::fake();
        Log::spy();

        $job = new SendNewAdminEmail($this->adminEmail, $this->inviteUrl, $this->name);
        $job->handle();

        // Assert email was sent
        Mail::assertSent(NewadminEmail::class, function ($mail) {
            return $mail->hasTo($this->adminEmail);
        });

        // Assert logging
        Log::shouldHaveReceived('info')
            ->once()
            ->with('EMAIL JOB: Sending new admin invitation email', [
                'email' => $this->adminEmail,
                'name' => $this->name
            ]);

        Log::shouldHaveReceived('info')
            ->once()
            ->with('EMAIL JOB: Admin invitation email sent successfully', [
                'email' => $this->adminEmail,
                'name' => $this->name
            ]);
    }

    /** @test */
    public function it_handles_mail_sending_exceptions()
    {
        Mail::shouldReceive('to')
            ->with($this->adminEmail)
            ->andReturnSelf();
        
        Mail::shouldReceive('send')
            ->andThrow(new Exception('SMTP server unavailable'));

        Log::spy();

        $job = new SendNewAdminEmail($this->adminEmail, $this->inviteUrl, $this->name);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('SMTP server unavailable');

        $job->handle();

        // Assert error logging
        Log::shouldHaveReceived('error')
            ->once()
            ->with('EMAIL JOB: Failed to send admin invitation email', [
                'email' => $this->adminEmail,
                'name' => $this->name,
                'error' => 'SMTP server unavailable'
            ]);
    }

    /** @test */
    public function it_logs_failure_properly_in_failed_method()
    {
        Log::spy();

        $exception = new Exception('Mail service failure');
        $job = new SendNewAdminEmail($this->adminEmail, $this->inviteUrl, $this->name);
        $job->failed($exception);

        Log::shouldHaveReceived('error')
            ->once()
            ->with('EMAIL JOB: New admin email job failed after all retry attempts', Mockery::on(function ($data) use ($exception) {
                return $data['job'] === SendNewAdminEmail::class
                    && $data['admin_email'] === $this->adminEmail
                    && $data['name'] === $this->name
                    && $data['exception'] === 'Mail service failure'
                    && $data['max_attempts'] === 3
                    && isset($data['backoff_delays'])
                    && isset($data['retry_until']);
            }));
    }

    /** @test */
    public function it_configures_exponential_backoff_properly()
    {
        $job = new SendNewAdminEmail($this->adminEmail, $this->inviteUrl, $this->name);

        // Test backoff configuration through reflection
        $reflection = new \ReflectionClass($job);
        
        $baseDelayProperty = $reflection->getProperty('baseDelay');
        $baseDelayProperty->setAccessible(true);
        $this->assertEquals(30, $baseDelayProperty->getValue($job));

        $maxDelayProperty = $reflection->getProperty('maxDelay');
        $maxDelayProperty->setAccessible(true);
        $this->assertEquals(300, $maxDelayProperty->getValue($job));

        $maxRetryTimeProperty = $reflection->getProperty('maxRetryTime');
        $maxRetryTimeProperty->setAccessible(true);
        $this->assertEquals(60, $maxRetryTimeProperty->getValue($job));
    }

    /** @test */
    public function it_sets_correct_retry_until_timestamp()
    {
        $job = new SendNewAdminEmail($this->adminEmail, $this->inviteUrl, $this->name);
        $retryUntil = $job->retryUntil();

        // Should be 1 hour from now (allowing some tolerance for test execution time)
        $expectedTime = now()->addHour();
        $this->assertTrue($retryUntil->diffInMinutes($expectedTime) < 1);
    }

    /** @test */
    public function it_can_be_dispatched_to_queue()
    {
        Queue::fake();

        SendNewAdminEmail::dispatch($this->adminEmail, $this->inviteUrl, $this->name);

        Queue::assertPushed(SendNewAdminEmail::class, function ($job) {
            $reflection = new \ReflectionClass($job);
            
            $emailProperty = $reflection->getProperty('adminEmail');
            $emailProperty->setAccessible(true);
            
            $urlProperty = $reflection->getProperty('inviteUrl');
            $urlProperty->setAccessible(true);
            
            $nameProperty = $reflection->getProperty('name');
            $nameProperty->setAccessible(true);
            
            return $emailProperty->getValue($job) === $this->adminEmail
                && $urlProperty->getValue($job) === $this->inviteUrl
                && $nameProperty->getValue($job) === $this->name;
        });
    }

    /** @test */
    public function it_passes_correct_parameters_to_mailable()
    {
        Mail::fake();

        $job = new SendNewAdminEmail($this->adminEmail, $this->inviteUrl, $this->name);
        $job->handle();

        Mail::assertSent(NewadminEmail::class, function ($mail) {
            // Check if the mailable was constructed with correct parameters
            $reflection = new \ReflectionClass($mail);
            
            // These properties might not be accessible, but we can check the mailable was sent
            // The actual parameter validation would depend on how NewadminEmail is implemented
            return $mail->hasTo($this->adminEmail);
        });
    }

    /** @test */
    public function it_handles_invalid_email_addresses_gracefully()
    {
        Mail::shouldReceive('to')
            ->with('invalid-email')
            ->andThrow(new Exception('Invalid email address'));

        Log::spy();

        $job = new SendNewAdminEmail('invalid-email', $this->inviteUrl, $this->name);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid email address');

        $job->handle();

        Log::shouldHaveReceived('error')
            ->once()
            ->with('EMAIL JOB: Failed to send admin invitation email', [
                'email' => 'invalid-email',
                'name' => $this->name,
                'error' => 'Invalid email address'
            ]);
    }

    /** @test */
    public function it_uses_correct_timeout_for_email_operations()
    {
        $job = new SendNewAdminEmail($this->adminEmail, $this->inviteUrl, $this->name);
        
        // Email operations should have a shorter timeout than other job types
        $this->assertEquals(300, $job->timeout); // 5 minutes
        $this->assertLessThan(1800, $job->timeout); // Less than generic jobs (30 min)
        $this->assertLessThan(2400, $job->timeout); // Less than external search jobs (40 min)
    }

    /** @test */
    public function it_has_reasonable_retry_configuration_for_emails()
    {
        $job = new SendNewAdminEmail($this->adminEmail, $this->inviteUrl, $this->name);
        
        // Email jobs should have fewer retries than heavy operations
        $this->assertEquals(3, $job->tries);
        
        $backoff = $job->backoff();
        $this->assertCount(3, $backoff); // Should have 3 retry delays
        $this->assertEquals(30, $backoff[0]); // First retry after 30 seconds
        $this->assertLessThanOrEqual(300, max($backoff)); // Max delay shouldn't exceed 5 minutes
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}