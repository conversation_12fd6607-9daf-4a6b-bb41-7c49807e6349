<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\AI\RateLimitedOpenAiService;
use App\Jobs\GenericBackgroundJob;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Cache;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Queue\Events\JobProcessing;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Support\Facades\Event;
use Carbon\Carbon;

class OpenAiRateLimitIntegrationTest extends TestCase
{
    protected $service;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new RateLimitedOpenAiService();
        
        // Clear caches and rate limits
        Cache::flush();
        $this->clearAllRateLimits();
        
        // Enable real queue events
        Event::fake([JobProcessed::class, JobFailed::class, JobProcessing::class]);
    }

    protected function tearDown(): void
    {
        $this->clearAllRateLimits();
        Cache::flush();
        parent::tearDown();
    }

    protected function clearAllRateLimits(): void
    {
        // Clear all possible rate limit keys used in tests
        $testKeys = [
            'test-1', 'test-2', 'shared-limit', 'circuit-test', 
            'retry-after-test', 'token-bucket-test', 'distributed-test',
            'header-tracking-test', 'adaptive-test', 'queue-job-test',
            'metrics-test', 'circuit-breaker-test', 'thundering-herd-test'
        ];
        
        foreach ($testKeys as $key) {
            RateLimiter::clear("openai-rpm:{$key}");
            RateLimiter::clear("openai-tpm:{$key}");
            RateLimiter::clear("openai-backoff:{$key}");
        }
        
        // Also clear numeric test keys
        for ($i = 0; $i < 10; $i++) {
            RateLimiter::clear("openai-rpm:test-{$i}");
            RateLimiter::clear("openai-tpm:test-{$i}");
            RateLimiter::clear("openai-backoff:test-{$i}");
        }
    }

    /** @test */
    public function it_exposes_issue_with_immediate_retry_after_429()
    {
        // This test exposes that the service doesn't actually wait before retrying
        $requestTimes = [];
        
        Http::fake(function ($request) use (&$requestTimes) {
            $requestTimes[] = microtime(true);
            return Http::response(['error' => 'Rate limited'], 429);
        });

        // Make two quick successive requests
        $this->service->chatCompletion(['messages' => []], 'test-1');
        $this->service->chatCompletion(['messages' => []], 'test-1');
        
        // Calculate time between requests
        if (count($requestTimes) >= 2) {
            $timeBetween = $requestTimes[1] - $requestTimes[0];
            
            // ISSUE: Requests happen immediately without any delay
            // This assertion SHOULD fail with proper backoff implementation
            $this->assertLessThan(0.1, $timeBetween, 'Requests should happen immediately (current bug)');
        }
    }

    /** @test */
    public function it_exposes_issue_with_no_actual_sleep_on_backoff()
    {
        // This test shows the service calculates backoff but doesn't enforce it
        Http::fake(['*' => Http::response(['error' => 'Rate limited'], 429)]);
        
        $startTime = microtime(true);
        
        // Make multiple attempts that should trigger exponential backoff
        for ($i = 0; $i < 3; $i++) {
            $this->service->chatCompletion(['messages' => []], 'test-2');
        }
        
        $totalTime = microtime(true) - $startTime;
        
        // With proper exponential backoff: 1 + 2 + 4 = 7 seconds minimum
        // ISSUE: All requests complete almost instantly
        $this->assertLessThan(1, $totalTime, 'All requests complete too quickly (current bug)');
    }

    /** @test */
    public function it_exposes_concurrent_request_collision_issues()
    {
        // This test shows that concurrent requests can exceed rate limits
        $concurrentRequests = [];
        $requestCount = 0;
        
        Http::fake(function ($request) use (&$requestCount) {
            $requestCount++;
            // Simulate that only 5 requests should be allowed
            if ($requestCount > 5) {
                return Http::response(['error' => 'Rate limited'], 429);
            }
            return Http::response(['choices' => [['message' => ['content' => 'OK']]]], 200);
        });
        
        // Simulate 10 concurrent requests from different workers
        $results = [];
        for ($i = 0; $i < 10; $i++) {
            // Each "worker" uses the same rate limit key
            $result = $this->service->chatCompletion(['messages' => []], 'shared-limit');
            $results[] = $result !== null;
        }
        
        // ISSUE: More than 5 requests might succeed due to race conditions
        $successCount = count(array_filter($results));
        
        // This might pass sometimes and fail others due to race conditions
        $this->assertLessThanOrEqual(10, $requestCount, 'Race condition allows too many requests');
    }

    /** @test */
    public function it_exposes_issue_with_queue_job_retry_not_respecting_backoff()
    {
        Queue::fake();
        
        // Create a job that uses OpenAI service
        $job = new class('TestClass', 'testMethod', [], 1) extends GenericBackgroundJob {
            public int $tries = 3;
            
            public function handle(): void
            {
                $service = app(RateLimitedOpenAiService::class);
                $result = $service->chatCompletion([
                    'messages' => [['role' => 'user', 'content' => 'Test']]
                ], 'queue-job-test');
                
                if ($result === null) {
                    throw new \Exception('OpenAI request failed');
                }
            }
        };
        
        Http::fake(['*' => Http::response(['error' => 'Rate limited'], 429)]);
        
        // Dispatch the job
        dispatch($job);
        
        // Process the queue
        Queue::assertPushed(get_class($job));
        
        // ISSUE: The job fails immediately without respecting OpenAI's retry-after
        // In reality, it should wait based on the 429 response headers
    }

    /** @test */
    public function it_exposes_missing_circuit_breaker_implementation()
    {
        // This test shows there's no circuit breaker to stop hammering the API
        $attemptCount = 0;
        
        Http::fake(function ($request) use (&$attemptCount) {
            $attemptCount++;
            return Http::response(['error' => 'Rate limited'], 429);
        });
        
        // Make many failed attempts
        for ($i = 0; $i < 10; $i++) {
            $this->service->chatCompletion(['messages' => []], 'circuit-test');
        }
        
        // ISSUE: All 10 requests are made even though we keep getting 429s
        $this->assertEquals(10, $attemptCount, 'No circuit breaker stops repeated failures');
    }

    /** @test */
    public function it_exposes_issue_with_no_retry_after_header_parsing()
    {
        $retryAfterValue = null;
        
        Http::fake(function ($request) use (&$retryAfterValue) {
            return Http::response(
                ['error' => 'Rate limited'],
                429,
                ['Retry-After' => '5.5'] // OpenAI sends decimal seconds
            );
        });
        
        // Make a request that gets 429
        $this->service->chatCompletion(['messages' => []], 'retry-after-test');
        
        // ISSUE: The service doesn't parse or use the Retry-After header
        // Check if the service stored or used the retry-after value
        $this->assertNull($retryAfterValue, 'Retry-After header is not parsed or used');
    }

    /** @test */
    public function it_exposes_token_bucket_implementation_issues()
    {
        // Test that token counting doesn't properly implement a token bucket
        $results = [];
        
        Http::fake(function ($request) {
            // Always succeed to test our local rate limiting
            return Http::response(['choices' => [['message' => ['content' => 'OK']]]], 200);
        });
        
        // Initial status check
        $initialStatus = $this->service->getRateLimitStatus('token-bucket-test');
        $this->assertEquals(0, $initialStatus['tpm']['current']);
        
        // Make requests that should consume tokens
        for ($i = 0; $i < 3; $i++) {
            $result = $this->service->chatCompletion([
                'messages' => [['role' => 'user', 'content' => str_repeat('word ', 100)]],
                'max_tokens' => 500 // Each request ~625 tokens total
            ], 'token-bucket-test');
            
            $results[] = $result !== null;
        }
        
        // Check token consumption
        $afterRequests = $this->service->getRateLimitStatus('token-bucket-test');
        $tokensUsed = $afterRequests['tpm']['current'];
        
        // ISSUE: Token counting is broken - it counts requests, not tokens!
        // We made 3 requests but it shows 3 tokens instead of ~1875
        $this->assertEquals(3, $tokensUsed, 'Token counter is actually counting requests, not tokens!');
        
        // ISSUE: Token bucket doesn't refill over time
        // In a proper implementation, after 60 seconds, the token counter should reset
        // But Laravel's RateLimiter doesn't automatically refill - it uses a sliding window
        
        // This demonstrates that our implementation doesn't have a proper token bucket
        // that refills at a steady rate, which is what OpenAI actually uses
        $this->assertTrue($tokensUsed > 0, 'Token bucket does not implement refill mechanism');
    }

    /** @test */
    public function it_exposes_missing_distributed_rate_limiting()
    {
        // Test that rate limiting doesn't work properly across multiple servers
        // Simulate requests from different "servers" (using different cache stores)
        
        $server1Results = [];
        $server2Results = [];
        
        Http::fake(function ($request) {
            static $count = 0;
            $count++;
            if ($count > 10) {
                return Http::response(['error' => 'Rate limited'], 429);
            }
            return Http::response(['choices' => [['message' => ['content' => 'OK']]]], 200);
        });
        
        // Simulate Server 1 making requests
        for ($i = 0; $i < 10; $i++) {
            $result = $this->service->chatCompletion(['messages' => []], 'distributed-test');
            $server1Results[] = $result !== null;
        }
        
        // Simulate Server 2 making requests (in reality, would use different Redis instance)
        for ($i = 0; $i < 10; $i++) {
            $result = $this->service->chatCompletion(['messages' => []], 'distributed-test');
            $server2Results[] = $result !== null;
        }
        
        // ISSUE: Without proper distributed rate limiting, both servers
        // might each use the full rate limit
        $totalSuccesses = count(array_filter($server1Results)) + count(array_filter($server2Results));
        
        // This might allow up to 20 requests when only 10 should be allowed globally
        // ISSUE: Without distributed rate limiting, we get exactly the limit (not more due to our fake)
        $this->assertEquals(10, $totalSuccesses, 'Both servers hit local limits separately');
    }

    /** @test */
    public function it_exposes_issue_with_no_rate_limit_header_tracking()
    {
        // OpenAI sends rate limit info in headers that we should track
        $capturedHeaders = null;
        
        Http::fake(function ($request) use (&$capturedHeaders) {
            return Http::response(
                ['choices' => [['message' => ['content' => 'OK']]]], 
                200,
                [
                    'X-RateLimit-Limit-Requests' => '20',
                    'X-RateLimit-Remaining-Requests' => '15',
                    'X-RateLimit-Reset-Requests' => (string)(time() + 60),
                    'X-RateLimit-Limit-Tokens' => '10000',
                    'X-RateLimit-Remaining-Tokens' => '8500',
                    'X-RateLimit-Reset-Tokens' => (string)(time() + 60),
                ]
            );
        });
        
        $this->service->chatCompletion(['messages' => []], 'header-tracking-test');
        
        // ISSUE: The service doesn't read or use the rate limit headers
        // Check if we're tracking actual vs estimated limits
        $status = $this->service->getRateLimitStatus('header-tracking-test');
        
        // Our status shows our conservative estimates, not actual API limits
        $this->assertEquals(20, $status['rpm']['limit'], 'Using hardcoded limit instead of API headers');
    }

    /** @test */
    public function it_exposes_issue_with_no_adaptive_rate_limiting()
    {
        // Test that the service doesn't adapt to actual rate limits
        $responses = [
            // First few succeed
            Http::response(['choices' => [['message' => ['content' => 'OK']]]], 200),
            Http::response(['choices' => [['message' => ['content' => 'OK']]]], 200),
            // Then we hit rate limit at request 3 (lower than expected)
            Http::response(['error' => 'Rate limited'], 429, ['Retry-After' => '60']),
        ];
        
        Http::fake(Http::sequence($responses));
        
        // Make requests until we hit rate limit
        $results = [];
        for ($i = 0; $i < 5; $i++) {
            $result = $this->service->chatCompletion(['messages' => []], 'adaptive-test');
            $results[] = $result !== null;
            
            if ($result === null) {
                break;
            }
        }
        
        // ISSUE: The service doesn't adapt its rate limits based on actual API behavior
        // It should learn that the real limit is 2 requests, not 20
        $status = $this->service->getRateLimitStatus('adaptive-test');
        $this->assertEquals(20, $status['rpm']['limit'], 'Rate limits not adapted to actual API limits');
    }
}