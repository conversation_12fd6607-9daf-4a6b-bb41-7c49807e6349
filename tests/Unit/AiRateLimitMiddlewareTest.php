<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Http\Middleware\AiRateLimit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

class AiRateLimitMiddlewareTest extends TestCase
{
    protected $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new AiRateLimit();
    }

    /** @test */
    public function middleware_allows_requests_within_limit()
    {
        $user = User::factory()->create(['role' => 'user']);
        $request = Request::create('/ai-chat/send-message', 'POST');
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'openai');

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function middleware_blocks_requests_over_limit()
    {
        $user = User::factory()->create(['role' => 'user']);
        $request = Request::create('/ai-chat/send-message', 'POST');
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        // Mock rate limiter to simulate limit exceeded
        RateLimiter::shouldReceive('tooManyAttempts')
            ->once()
            ->with('openai:' . $user->id, 10)
            ->andReturn(true);

        RateLimiter::shouldReceive('availableIn')
            ->once()
            ->with('openai:' . $user->id)
            ->andReturn(60);

        $this->expectException(TooManyRequestsHttpException::class);

        $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'openai');
    }

    /** @test */
    public function middleware_allows_unlimited_requests_for_admins()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $request = Request::create('/ai-chat/send-message', 'POST');
        $request->setUserResolver(function () use ($admin) {
            return $admin;
        });

        // Even if rate limiter says too many attempts, admin should pass through
        RateLimiter::shouldReceive('tooManyAttempts')
            ->never();

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'openai');

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function middleware_increments_attempts_for_regular_users()
    {
        $user = User::factory()->create(['role' => 'user']);
        $request = Request::create('/ai-chat/send-message', 'POST');
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        RateLimiter::shouldReceive('tooManyAttempts')
            ->once()
            ->with('openai:' . $user->id, 10)
            ->andReturn(false);

        RateLimiter::shouldReceive('hit')
            ->once()
            ->with('openai:' . $user->id, 60);

        RateLimiter::shouldReceive('remaining')
            ->once()
            ->with('openai:' . $user->id, 10)
            ->andReturn(9);

        RateLimiter::shouldReceive('availableIn')
            ->once()
            ->with('openai:' . $user->id)
            ->andReturn(60);

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'openai');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('10', $response->headers->get('X-RateLimit-Limit'));
        $this->assertEquals('9', $response->headers->get('X-RateLimit-Remaining'));
    }

    /** @test */
    public function middleware_handles_different_ai_services()
    {
        $user = User::factory()->create(['role' => 'user']);
        $request = Request::create('/talentpoolai/send-message', 'POST');
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        RateLimiter::shouldReceive('tooManyAttempts')
            ->once()
            ->with('anthropic:' . $user->id, 8)
            ->andReturn(false);

        RateLimiter::shouldReceive('hit')
            ->once()
            ->with('anthropic:' . $user->id, 60);

        RateLimiter::shouldReceive('remaining')
            ->once()
            ->with('anthropic:' . $user->id, 8)
            ->andReturn(7);

        RateLimiter::shouldReceive('availableIn')
            ->once()
            ->with('anthropic:' . $user->id)
            ->andReturn(60);

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('OK', 200);
        }, 'anthropic');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('8', $response->headers->get('X-RateLimit-Limit'));
        $this->assertEquals('7', $response->headers->get('X-RateLimit-Remaining'));
    }
}