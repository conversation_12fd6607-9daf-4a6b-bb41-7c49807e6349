<?php

namespace Tests\Unit;

use App\Jobs\CleanupOldLogFiles;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Artisan;
use Tests\TestCase;

class ScheduledJobsTest extends TestCase
{
    public function test_cleanup_old_log_files_job_is_scheduled_monthly(): void
    {
        // Use the schedule:list command to verify our job is scheduled
        Artisan::call('schedule:list');
        $output = Artisan::output();

        // Check if our job appears in the schedule list with monthly frequency
        $this->assertStringContainsString('App\\Jobs\\CleanupOldLogFiles', $output);
        $this->assertStringContainsString('0 0 1 * *', $output); // Monthly cron expression
    }

    public function test_schedule_list_includes_cleanup_job(): void
    {
        // Run the schedule:list command and capture output
        Artisan::call('schedule:list');
        $output = Artisan::output();

        // Check if our job appears in the schedule list
        $this->assertStringContainsString('CleanupOldLogFiles', $output);
    }

    public function test_can_run_cleanup_job_manually(): void
    {
        // Create a test log file to verify the job can run
        $testLogFile = storage_path('logs/laravel-test-manual.log');
        file_put_contents($testLogFile, 'test content');
        touch($testLogFile, now()->subDays(35)->timestamp);

        $this->assertFileExists($testLogFile);

        // Dispatch the job directly
        $job = new CleanupOldLogFiles;
        $job->handle();

        // Verify the old file was cleaned up
        $this->assertFileDoesNotExist($testLogFile);
    }
}
