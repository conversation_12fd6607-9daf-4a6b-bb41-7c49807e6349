<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\AI\RateLimitedAnthropicService;
use App\Services\AI\RateLimitedExaService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class EnhancedAiServicesTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear rate limiter cache between tests
        Cache::flush();
        RateLimiter::clear('anthropic-rpm:test');
        RateLimiter::clear('anthropic-itpm:test');
        RateLimiter::clear('anthropic-otpm:test');
        RateLimiter::clear('exa-rpm:test');
    }

    /** @test */
    public function anthropic_service_can_be_instantiated()
    {
        $service = new RateLimitedAnthropicService();
        
        $this->assertInstanceOf(RateLimitedAnthropicService::class, $service);
    }

    /** @test */
    public function exa_service_can_be_instantiated()
    {
        $service = new RateLimitedExaService();
        
        $this->assertInstanceOf(RateLimitedExaService::class, $service);
    }

    /** @test */
    public function anthropic_service_has_circuit_breaker_functionality()
    {
        $service = new RateLimitedAnthropicService();
        
        // Circuit should be closed initially
        $this->assertFalse($service->isCircuitOpen('test'));
        
        // Clear any existing rate limits
        $service->clearRateLimit('test');
        
        $this->assertTrue(true); // Basic test passed
    }

    /** @test */
    public function exa_service_has_circuit_breaker_functionality()
    {
        $service = new RateLimitedExaService();
        
        // Circuit should be closed initially
        $this->assertFalse($service->isCircuitOpen('test'));
        
        // Clear any existing rate limits
        $service->clearRateLimit('test');
        
        $this->assertTrue(true); // Basic test passed
    }

    /** @test */
    public function anthropic_service_can_get_rate_limit_status()
    {
        $service = new RateLimitedAnthropicService();
        
        $status = $service->getRateLimitStatus('test');
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('rpm', $status);
        $this->assertArrayHasKey('itpm', $status);
        $this->assertArrayHasKey('otpm', $status);
        
        // Each limit should have current, limit, remaining, available_in
        foreach (['rpm', 'itpm', 'otpm'] as $limitType) {
            $this->assertArrayHasKey('current', $status[$limitType]);
            $this->assertArrayHasKey('limit', $status[$limitType]);
            $this->assertArrayHasKey('remaining', $status[$limitType]);
            $this->assertArrayHasKey('available_in', $status[$limitType]);
        }
    }

    /** @test */
    public function exa_service_can_get_rate_limit_status()
    {
        $service = new RateLimitedExaService();
        
        $status = $service->getRateLimitStatus('test');
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('rpm', $status);
        
        // RPM limit should have current, limit, remaining, available_in
        $this->assertArrayHasKey('current', $status['rpm']);
        $this->assertArrayHasKey('limit', $status['rpm']);
        $this->assertArrayHasKey('remaining', $status['rpm']);
        $this->assertArrayHasKey('available_in', $status['rpm']);
    }

    /** @test */
    public function anthropic_service_can_get_metrics()
    {
        $service = new RateLimitedAnthropicService();
        
        $metrics = $service->getMetrics('test');
        
        $this->assertIsArray($metrics);
        $this->assertArrayHasKey('total_requests', $metrics);
        $this->assertArrayHasKey('successful_requests', $metrics);
        $this->assertArrayHasKey('rate_limited_requests', $metrics);
        $this->assertArrayHasKey('total_tokens', $metrics);
        $this->assertArrayHasKey('current_tier', $metrics);
    }

    /** @test */
    public function exa_service_can_get_metrics()
    {
        $service = new RateLimitedExaService();
        
        $metrics = $service->getMetrics('test');
        
        $this->assertIsArray($metrics);
        $this->assertArrayHasKey('total_requests', $metrics);
        $this->assertArrayHasKey('successful_requests', $metrics);
        $this->assertArrayHasKey('rate_limited_requests', $metrics);
        $this->assertArrayHasKey('success_rate', $metrics);
    }

    /** @test */
    public function services_can_clear_rate_limits()
    {
        $anthropicService = new RateLimitedAnthropicService();
        $exaService = new RateLimitedExaService();
        
        // Should not throw exceptions
        $anthropicService->clearRateLimit('test');
        $exaService->clearRateLimit('test');
        
        $this->assertTrue(true); // Test passed if no exceptions
    }
}