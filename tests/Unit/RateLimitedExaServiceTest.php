<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\AI\RateLimitedExaService;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Http;

class RateLimitedExaServiceTest extends TestCase
{
    protected $service;
    protected $testKey = 'test-exa-service';

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new RateLimitedExaService();
        
        // Clear any existing rate limits for clean testing
        RateLimiter::clear("exa-rpm:{$this->testKey}");
        RateLimiter::clear("exa-backoff:{$this->testKey}");
    }

    protected function tearDown(): void
    {
        // Clean up after tests
        RateLimiter::clear("exa-rpm:{$this->testKey}");
        RateLimiter::clear("exa-backoff:{$this->testKey}");
        parent::tearDown();
    }

    /** @test */
    public function it_tracks_rate_limits_correctly()
    {
        // Initial state - no limits
        $status = $this->service->getRateLimitStatus($this->testKey);
        
        $this->assertEquals(0, $status['rpm']['current']);
        $this->assertEquals(300, $status['rpm']['limit']);
        $this->assertEquals(300, $status['rpm']['remaining']);
        $this->assertEquals(0, $status['rpm']['available_in']);
    }

    /** @test */
    public function it_prevents_requests_when_rpm_limit_exceeded()
    {
        // Simulate 300 requests (at the limit)
        for ($i = 0; $i < 300; $i++) {
            RateLimiter::increment("exa-rpm:{$this->testKey}");
        }
        
        $status = $this->service->getRateLimitStatus($this->testKey);
        
        $this->assertEquals(300, $status['rpm']['current']);
        $this->assertEquals(0, $status['rpm']['remaining']);
        $this->assertGreaterThan(0, $status['rpm']['available_in']);
    }

    /** @test */
    public function it_allows_requests_within_limits()
    {
        // Mock successful HTTP response
        Http::fake([
            'api.exa.ai/*' => Http::response([
                'results' => [
                    ['url' => 'https://linkedin.com/test', 'title' => 'Test Profile']
                ]
            ], 200)
        ]);
        
        $payload = [
            'query' => 'Software Engineer',
            'numResults' => 10,
            'type' => 'auto'
        ];
        
        $result = $this->service->search($payload, $this->testKey);
        
        $this->assertNotNull($result);
        $this->assertArrayHasKey('results', $result);
        
        // Check that counters were incremented
        $status = $this->service->getRateLimitStatus($this->testKey);
        $this->assertEquals(1, $status['rpm']['current']);
    }

    /** @test */
    public function it_handles_rate_limit_errors_from_api()
    {
        // Mock 429 rate limit response
        Http::fake([
            'api.exa.ai/*' => Http::response([
                'error' => 'Rate limit exceeded'
            ], 429)
        ]);
        
        $payload = [
            'query' => 'Software Engineer',
            'numResults' => 10
        ];
        
        $result = $this->service->search($payload, $this->testKey);
        
        $this->assertNull($result);
    }

    /** @test */
    public function it_clears_rate_limits_correctly()
    {
        // Set some rate limits
        RateLimiter::increment("exa-rpm:{$this->testKey}", 50);
        
        // Verify they exist
        $status = $this->service->getRateLimitStatus($this->testKey);
        $this->assertEquals(50, $status['rpm']['current']);
        
        // Clear them
        $this->service->clearRateLimit($this->testKey);
        
        // Verify they're cleared
        $status = $this->service->getRateLimitStatus($this->testKey);
        $this->assertEquals(0, $status['rpm']['current']);
    }

    /** @test */
    public function it_returns_null_when_rpm_limit_exceeded()
    {
        // Set RPM to limit
        for ($i = 0; $i < 300; $i++) {
            RateLimiter::increment("exa-rpm:{$this->testKey}");
        }
        
        $payload = [
            'query' => 'Software Engineer',
            'numResults' => 10
        ];
        
        $result = $this->service->search($payload, $this->testKey);
        
        $this->assertNull($result);
    }
}