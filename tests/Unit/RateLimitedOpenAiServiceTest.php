<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\AI\RateLimitedOpenAiService;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class RateLimitedOpenAiServiceTest extends TestCase
{
    protected $service;
    protected $testKey = 'test-openai-service';

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new RateLimitedOpenAiService();
        
        // Clear any existing rate limits for clean testing (but not all cache)
        $this->service->clearRateLimit($this->testKey);
        
        // Also clear any leftover rate limits from other tests
        RateLimiter::clear("openai-rpm:{$this->testKey}");
        RateLimiter::clear("openai-tpm:{$this->testKey}");
        RateLimiter::clear("openai-backoff:{$this->testKey}");
    }

    protected function tearDown(): void
    {
        // Clean up after tests
        $this->service->clearRateLimit($this->testKey);
        
        // Also clear any leftover rate limits 
        RateLimiter::clear("openai-rpm:{$this->testKey}");
        RateLimiter::clear("openai-tpm:{$this->testKey}");
        RateLimiter::clear("openai-backoff:{$this->testKey}");
        parent::tearDown();
    }

    /** @test */
    public function it_tracks_rate_limits_correctly()
    {
        // Initial state - no limits
        $status = $this->service->getRateLimitStatus($this->testKey);
        
        $this->assertEquals(0, $status['rpm']['current']);
        $this->assertEquals(20, $status['rpm']['limit']);
        $this->assertEquals(20, $status['rpm']['remaining']);
        $this->assertEquals(0, $status['rpm']['available_in']);
        
        $this->assertEquals(0, $status['tpm']['current']);
        $this->assertEquals(10000, $status['tpm']['limit']);
        $this->assertEquals(10000, $status['tpm']['remaining']);
        $this->assertEquals(0, $status['tpm']['available_in']);
    }

    /** @test */
    public function it_prevents_requests_when_rpm_limit_exceeded()
    {
        // Simulate 20 requests (at the limit)
        $this->service->setRateLimitForTesting($this->testKey, 20, 0);
        
        $status = $this->service->getRateLimitStatus($this->testKey);
        
        $this->assertEquals(20, $status['rpm']['current']);
        $this->assertEquals(0, $status['rpm']['remaining']);
        $this->assertGreaterThan(0, $status['rpm']['available_in']);
    }

    /** @test */
    public function it_prevents_requests_when_tpm_limit_exceeded()
    {
        // Simulate token usage at the limit
        $this->service->setRateLimitForTesting($this->testKey, 0, 10000);
        
        $status = $this->service->getRateLimitStatus($this->testKey);
        
        $this->assertEquals(10000, $status['tpm']['current']);
        $this->assertEquals(0, $status['tpm']['remaining']);
        $this->assertGreaterThan(0, $status['tpm']['available_in']);
    }

    /** @test */
    public function it_estimates_tokens_correctly()
    {
        $payload = [
            'messages' => [
                ['content' => 'Hello world'],  // ~3 tokens
                ['content' => 'This is a longer message with more content'] // ~10 tokens
            ],
            'max_tokens' => 500
        ];
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('estimateTokens');
        $method->setAccessible(true);
        
        $estimated = $method->invoke($this->service, $payload);
        
        // Should be around input tokens (~13) + max_tokens (500) + buffer (100) = ~613
        $this->assertGreaterThan(500, $estimated);
        $this->assertLessThan(700, $estimated);
    }

    /** @test */
    public function it_allows_requests_within_limits()
    {
        // Mock successful HTTP response
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    ['message' => ['content' => 'Test response']]
                ]
            ], 200)
        ]);
        
        $payload = [
            'model' => 'gpt-4o-mini',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];
        
        $result = $this->service->chatCompletion($payload, $this->testKey);
        
        $this->assertNotNull($result);
        $this->assertArrayHasKey('choices', $result);
        
        // Check that counters were incremented
        $status = $this->service->getRateLimitStatus($this->testKey);
        $this->assertEquals(1, $status['rpm']['current']);
        $this->assertGreaterThan(0, $status['tpm']['current']);
    }

    /** @test */
    public function it_handles_rate_limit_errors_from_api()
    {
        // Mock 429 rate limit response
        Http::fake([
            'api.openai.com/*' => Http::response([
                'error' => ['message' => 'Rate limit exceeded']
            ], 429)
        ]);
        
        $payload = [
            'model' => 'gpt-4o-mini',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];
        
        $result = $this->service->chatCompletion($payload, $this->testKey);
        
        // Should return null (rate limited) or false (circuit open)
        $this->assertTrue($result === null || $result === false);
    }

    /** @test */
    public function it_clears_rate_limits_correctly()
    {
        // Set some rate limits
        $this->service->setRateLimitForTesting($this->testKey, 5, 1000);
        
        // Verify they exist
        $status = $this->service->getRateLimitStatus($this->testKey);
        $this->assertEquals(5, $status['rpm']['current']);
        $this->assertEquals(1000, $status['tpm']['current']);
        
        // Clear them
        $this->service->clearRateLimit($this->testKey);
        
        // Verify they're cleared
        $status = $this->service->getRateLimitStatus($this->testKey);
        $this->assertEquals(0, $status['rpm']['current']);
        $this->assertEquals(0, $status['tpm']['current']);
    }

    /** @test */
    public function it_returns_null_when_rpm_limit_exceeded()
    {
        // Set RPM to limit
        $this->service->setRateLimitForTesting($this->testKey, 20, 0);
        
        $payload = [
            'model' => 'gpt-4o-mini',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 10
        ];
        
        $result = $this->service->chatCompletion($payload, $this->testKey);
        
        // Should return null (rate limited) or false (circuit open)
        $this->assertTrue($result === null || $result === false);
    }

    /** @test */
    public function it_returns_null_when_tpm_limit_would_be_exceeded()
    {
        // Set TPM close to limit
        $this->service->setRateLimitForTesting($this->testKey, 0, 9500);
        
        $payload = [
            'model' => 'gpt-4o-mini',
            'messages' => [['role' => 'user', 'content' => 'This is a very long message that would push us over the token limit when combined with the max_tokens parameter and existing token usage']],
            'max_tokens' => 1000  // This would exceed the limit
        ];
        
        $result = $this->service->chatCompletion($payload, $this->testKey);
        
        // Should return null (rate limited) or false (circuit open)
        $this->assertTrue($result === null || $result === false);
    }
}