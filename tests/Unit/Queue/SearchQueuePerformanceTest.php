<?php

namespace Tests\Unit\Queue;

use Tests\TestCase;
use App\Services\Queue\SearchQueueManager;
use App\Jobs\SearchCandidatesJob;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Log;

class SearchQueuePerformanceTest extends TestCase
{

    protected $user;
    protected $planData;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = (object) ['id' => 1, 'email' => '<EMAIL>', 'company_id' => 1];
        $this->planData = [
            'plan_id' => 'perf-test-' . time(),
            'plan_name' => 'Performance Test Plan',
            'target_roles' => ['Manager'],
            'companies' => ['Test Company'],
            'alternative_roles_titles' => [],
            'step_up_candidates' => ['none'],
            'minimum_tenure' => null,
            'gender' => 'Not required',
            'country' => ['none'],
            'is_ethnicity_important' => false,
            'qualifications' => [],
            'skills' => [],
            'acronyms' => []
        ];
    }

    /** @test */
    public function it_handles_high_volume_queue_dispatching()
    {
        Queue::fake();
        
        $startTime = microtime(true);
        
        // Dispatch 100 search jobs
        for ($i = 0; $i < 100; $i++) {
            $planData = array_merge($this->planData, [
                'plan_id' => 'bulk-test-' . $i
            ]);
            
            SearchQueueManager::dispatchInternalSearch($planData, $this->user);
            SearchQueueManager::dispatchExternalSearch($planData, $this->user);
        }
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        // Should complete within reasonable time (< 5 seconds for 200 dispatches)
        $this->assertLessThan(5.0, $executionTime, 'Queue dispatching took too long: ' . $executionTime . ' seconds');
        
        // Verify all jobs were queued
        Queue::assertPushed(\App\Jobs\SearchInternalPeopleJob::class, 100);
        Queue::assertPushed(\App\Jobs\ExternalSearchPerplexityJob::class, 100);
    }

    /** @test */
    public function it_maintains_queue_separation_under_load()
    {
        Queue::fake();
        
        // Dispatch jobs directly using SearchQueueManager
        for ($i = 0; $i < 50; $i++) {
            // Alternate between internal and external
            if ($i % 2 === 0) {
                SearchQueueManager::dispatchInternalSearch($this->planData, $this->user);
            } else {
                SearchQueueManager::dispatchExternalSearch($this->planData, $this->user);
            }
        }
        
        // Should maintain correct queue separation
        Queue::assertPushed(\App\Jobs\SearchInternalPeopleJob::class, 25);
        Queue::assertPushed(\App\Jobs\ExternalSearchPerplexityJob::class, 25);
    }

    /** @test */
    public function it_handles_memory_efficiently_with_large_datasets()
    {
        Queue::fake();
        
        $memoryBefore = memory_get_usage(true);
        
        // Create large plan data
        $largePlanData = array_merge($this->planData, [
            'target_roles' => array_fill(0, 1000, 'Role'),
            'companies' => array_fill(0, 500, 'Company'),
            'skills' => array_fill(0, 2000, 'Skill'),
            'qualifications' => array_fill(0, 100, 'Qualification'),
            'alternative_roles_titles' => array_fill(0, 200, 'Alt Role')
        ]);
        
        // Dispatch multiple large jobs
        for ($i = 0; $i < 10; $i++) {
            SearchQueueManager::dispatchInternalSearch($largePlanData, $this->user);
            SearchQueueManager::dispatchExternalSearch($largePlanData, $this->user);
        }
        
        $memoryAfter = memory_get_usage(true);
        $memoryIncrease = $memoryAfter - $memoryBefore;
        
        // Memory increase should be reasonable (< 50MB for this test)
        $maxMemoryIncrease = 50 * 1024 * 1024; // 50MB
        $this->assertLessThan($maxMemoryIncrease, $memoryIncrease, 
            'Memory usage increased too much: ' . number_format($memoryIncrease / 1024 / 1024, 2) . ' MB');
    }

    /** @test */
    public function it_logs_performance_metrics_correctly()
    {
        Log::spy();
        
        $startTime = microtime(true);
        
        // Dispatch jobs and measure time
        for ($i = 0; $i < 10; $i++) {
            SearchQueueManager::dispatchInternalSearch($this->planData, $this->user);
        }
        
        $endTime = microtime(true);
        
        // Should log configuration once
        Log::shouldHaveReceived('info')
            ->with('QUEUE MANAGER: Dispatching internal search to high-priority internal_search queue', \Mockery::any())
            ->times(10);
    }

    /** @test */
    public function it_handles_concurrent_queue_operations()
    {
        Queue::fake();
        
        // Simulate concurrent operations by rapid-fire dispatching
        $operations = [];
        
        for ($i = 0; $i < 20; $i++) {
            $operations[] = function() use ($i) {
                $planData = array_merge($this->planData, ['plan_id' => 'concurrent-' . $i]);
                SearchQueueManager::dispatchInternalSearch($planData, $this->user);
                SearchQueueManager::dispatchExternalSearch($planData, $this->user);
            };
        }
        
        // Execute all operations
        foreach ($operations as $operation) {
            $operation();
        }
        
        // All should complete successfully
        Queue::assertPushed(\App\Jobs\SearchInternalPeopleJob::class, 20);
        Queue::assertPushed(\App\Jobs\ExternalSearchPerplexityJob::class, 20);
    }

    /** @test */
    public function it_maintains_queue_statistics_accuracy()
    {
        // Get stats multiple times to ensure consistency
        $stats1 = SearchQueueManager::getQueueStats();
        $stats2 = SearchQueueManager::getQueueStats();
        $stats3 = SearchQueueManager::getQueueStats();
        
        // Should be identical each time
        $this->assertEquals($stats1, $stats2);
        $this->assertEquals($stats2, $stats3);
        
        // Verify specific performance-related settings
        $this->assertEquals(900, $stats1['internal_search']['timeout']);
        $this->assertEquals(2400, $stats1['external_search']['timeout']);
        $this->assertEquals('high', $stats1['internal_search']['priority']);
        $this->assertEquals('low', $stats1['external_search']['priority']);
    }

    /** @test */
    public function it_handles_queue_overflow_scenarios()
    {
        Queue::fake();
        
        // Simulate a scenario where many jobs are queued quickly using SearchQueueManager
        $totalJobs = 100;
        
        for ($i = 0; $i < $totalJobs; $i++) {
            $planData = array_merge($this->planData, [
                'plan_id' => "overflow-test-{$i}"
            ]);
            
            // Mix of job types to test both queues
            if ($i % 2 === 0) {
                SearchQueueManager::dispatchInternalSearch($planData, $this->user);
            } else {
                SearchQueueManager::dispatchExternalSearch($planData, $this->user);
            }
        }
        
        // Verify correct distribution
        Queue::assertPushed(\App\Jobs\SearchInternalPeopleJob::class, 50);
        Queue::assertPushed(\App\Jobs\ExternalSearchPerplexityJob::class, 50);
    }

    /** @test */
    public function it_benchmarks_queue_manager_vs_direct_dispatch()
    {
        Queue::fake();
        
        // Benchmark SearchQueueManager
        $managerStartTime = microtime(true);
        for ($i = 0; $i < 50; $i++) {
            SearchQueueManager::dispatchInternalSearch($this->planData, $this->user);
        }
        $managerEndTime = microtime(true);
        $managerTime = $managerEndTime - $managerStartTime;
        
        // Reset queue for fair comparison
        Queue::fake();
        
        // Benchmark direct dispatch
        $directStartTime = microtime(true);
        for ($i = 0; $i < 50; $i++) {
            \App\Jobs\SearchInternalPeopleJob::dispatch($this->planData, $this->user);
        }
        $directEndTime = microtime(true);
        $directTime = $directEndTime - $directStartTime;
        
        // SearchQueueManager should not add excessive overhead (< 10x slower)
        // Note: overhead is expected due to logging and additional method calls
        $maxAllowedOverhead = max($directTime * 10, 0.1); // At least 100ms allowance
        $this->assertLessThan($maxAllowedOverhead, $managerTime, 
            "SearchQueueManager overhead too high. Direct: {$directTime}s, Manager: {$managerTime}s");
            
        // Both should successfully dispatch jobs
        Queue::assertPushed(\App\Jobs\SearchInternalPeopleJob::class, 50);
    }
}