<?php

namespace Tests\Unit\Queue;

use Tests\TestCase;
use App\Services\Queue\SearchQueueManager;
use App\Jobs\SearchInternalPeopleJob;
use App\Jobs\ExternalSearchPerplexityJob;
use App\Jobs\TalentPoolExternalSearch;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class SearchQueueConfigurationTest extends TestCase
{
    protected $user;
    protected $planData;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create mock user object (no database required)
        $this->user = (object) [
            'id' => 1,
            'email' => '<EMAIL>',
            'company_id' => 1
        ];

        // Create test plan data
        $this->planData = [
            'plan_id' => 'test-plan-123',
            'plan_name' => 'Test Succession Plan',
            'target_roles' => ['Manager', 'Director'],
            'companies' => ['Test Company'],
            'alternative_roles_titles' => ['Senior Manager'],
            'step_up_candidates' => ['none'],
            'minimum_tenure' => null,
            'gender' => 'Not required',
            'country' => ['none'],
            'is_ethnicity_important' => false,
            'qualifications' => [],
            'skills' => [],
            'acronyms' => []
        ];
    }

    /** @test */
    public function it_configures_separate_queue_connections_correctly()
    {
        // Test that internal queue configuration exists
        $internalConfig = Config::get('queue.connections.redis_internal');
        $this->assertNotNull($internalConfig);
        $this->assertEquals('redis', $internalConfig['driver']);
        $this->assertEquals('internal_search', $internalConfig['queue']);
        $this->assertEquals(900, $internalConfig['retry_after']);

        // Test that external queue configuration exists
        $externalConfig = Config::get('queue.connections.redis_external');
        $this->assertNotNull($externalConfig);
        $this->assertEquals('redis', $externalConfig['driver']);
        $this->assertEquals('external_search', $externalConfig['queue']);
        $this->assertEquals(2400, $externalConfig['retry_after']);
    }

    /** @test */
    public function it_dispatches_internal_search_to_correct_queue()
    {
        Queue::fake();

        SearchQueueManager::dispatchInternalSearch($this->planData, $this->user);

        Queue::assertPushedOn('internal_search', SearchInternalPeopleJob::class);
        Queue::assertPushed(SearchInternalPeopleJob::class);
    }

    /** @test */
    public function it_dispatches_external_search_to_correct_queue()
    {
        Queue::fake();

        SearchQueueManager::dispatchExternalSearch($this->planData, $this->user);

        Queue::assertPushedOn('external_search', ExternalSearchPerplexityJob::class);
        Queue::assertPushed(ExternalSearchPerplexityJob::class);
    }

    /** @test */
    public function it_dispatches_talent_pool_external_search_to_correct_queue()
    {
        Queue::fake();

        SearchQueueManager::dispatchTalentPoolExternalSearch($this->planData, $this->user);

        Queue::assertPushedOn('external_search', TalentPoolExternalSearch::class);
        Queue::assertPushed(TalentPoolExternalSearch::class);
    }

    /** @test */
    public function it_returns_correct_queue_statistics()
    {
        $stats = SearchQueueManager::getQueueStats();

        $this->assertArrayHasKey('internal_search', $stats);
        $this->assertArrayHasKey('external_search', $stats);

        // Test internal search stats
        $internalStats = $stats['internal_search'];
        $this->assertEquals('redis_internal', $internalStats['connection']);
        $this->assertEquals('internal_search', $internalStats['queue']);
        $this->assertEquals('high', $internalStats['priority']);
        $this->assertEquals(900, $internalStats['timeout']);

        // Test external search stats
        $externalStats = $stats['external_search'];
        $this->assertEquals('redis_external', $externalStats['connection']);
        $this->assertEquals('external_search', $externalStats['queue']);
        $this->assertEquals('low', $externalStats['priority']);
        $this->assertEquals(2400, $externalStats['timeout']);
    }

    /** @test */
    public function it_logs_queue_configuration_properly()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('QUEUE CONFIGURATION: Search queue strategy active', \Mockery::type('array'));

        SearchQueueManager::logQueueConfiguration();
    }

    /** @test */
    public function internal_search_job_can_be_instantiated()
    {
        $job = new SearchInternalPeopleJob($this->planData, $this->user);
        
        $this->assertInstanceOf(SearchInternalPeopleJob::class, $job);
    }

    /** @test */
    public function external_search_job_has_correct_timeout_property()
    {
        $job = new ExternalSearchPerplexityJob($this->planData, $this->user);
        
        $this->assertEquals(2400, $job->timeout);
        $this->assertInstanceOf(ExternalSearchPerplexityJob::class, $job);
    }

    /** @test */
    public function talent_pool_external_search_job_can_be_instantiated()
    {
        $job = new TalentPoolExternalSearch($this->planData, $this->user);
        
        $this->assertInstanceOf(TalentPoolExternalSearch::class, $job);
    }

    /** @test */
    public function horizon_configuration_has_correct_supervisor_settings()
    {
        $horizonConfig = Config::get('horizon.defaults');

        // Test internal search supervisor
        $this->assertArrayHasKey('internal-search', $horizonConfig);
        $internalSupervisor = $horizonConfig['internal-search'];
        $this->assertEquals(['internal_search'], $internalSupervisor['queue']);
        $this->assertEquals(3, $internalSupervisor['maxProcesses']);
        $this->assertEquals(900, $internalSupervisor['timeout']);
        $this->assertEquals(0, $internalSupervisor['nice']); // Higher priority

        // Test external search supervisor
        $this->assertArrayHasKey('external-search', $horizonConfig);
        $externalSupervisor = $horizonConfig['external-search'];
        $this->assertEquals(['external_search'], $externalSupervisor['queue']);
        $this->assertEquals(2, $externalSupervisor['maxProcesses']);
        $this->assertEquals(2400, $externalSupervisor['timeout']);
        $this->assertEquals(5, $externalSupervisor['nice']); // Lower priority

        // Test general supervisor
        $this->assertArrayHasKey('general', $horizonConfig);
        $generalSupervisor = $horizonConfig['general'];
        $this->assertEquals(['default'], $generalSupervisor['queue']);
        $this->assertEquals(10, $generalSupervisor['nice']); // Lowest priority
    }

    /** @test */
    public function production_environment_has_correct_worker_scaling()
    {
        $prodConfig = Config::get('horizon.environments.production');

        // Internal search should have more workers in production
        $this->assertEquals(6, $prodConfig['internal-search']['maxProcesses']);
        $this->assertEquals(2, $prodConfig['internal-search']['balanceCooldown']);

        // External search should have moderate workers
        $this->assertEquals(4, $prodConfig['external-search']['maxProcesses']);
        $this->assertEquals(5, $prodConfig['external-search']['balanceCooldown']);

        // General should have basic capacity
        $this->assertEquals(3, $prodConfig['general']['maxProcesses']);
    }

    /** @test */
    public function local_environment_has_correct_worker_scaling()
    {
        $localConfig = Config::get('horizon.environments.local');

        // Local should have reasonable limits
        $this->assertEquals(3, $localConfig['internal-search']['maxProcesses']);
        $this->assertEquals(2, $localConfig['external-search']['maxProcesses']);
        $this->assertEquals(2, $localConfig['general']['maxProcesses']);
    }

    /** @test */
    public function queue_manager_logs_correct_information_for_internal_dispatch()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('QUEUE MANAGER: Dispatching internal search to high-priority internal_search queue', [
                'plan_id' => 'test-plan-123',
                'queue' => 'internal_search',
                'priority' => 'high'
            ]);

        Queue::fake();
        SearchQueueManager::dispatchInternalSearch($this->planData, $this->user);
    }

    /** @test */
    public function queue_manager_logs_correct_information_for_external_dispatch()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('QUEUE MANAGER: Dispatching external search to lower-priority external_search queue', [
                'plan_id' => 'test-plan-123',
                'queue' => 'external_search',
                'priority' => 'low'
            ]);

        Queue::fake();
        SearchQueueManager::dispatchExternalSearch($this->planData, $this->user);
    }

    /** @test */
    public function it_handles_missing_plan_id_gracefully_in_logging()
    {
        $planDataWithoutId = $this->planData;
        unset($planDataWithoutId['plan_id']);

        Log::shouldReceive('info')
            ->once()
            ->with('QUEUE MANAGER: Dispatching internal search to high-priority internal_search queue', [
                'plan_id' => 'unknown',
                'queue' => 'internal_search',
                'priority' => 'high'
            ]);

        Queue::fake();
        SearchQueueManager::dispatchInternalSearch($planDataWithoutId, $this->user);
    }

    /** @test */
    public function queue_timeout_configuration_prioritizes_internal_searches()
    {
        $internalTimeout = Config::get('queue.connections.redis_internal.retry_after');
        $externalTimeout = Config::get('queue.connections.redis_external.retry_after');
        
        // Internal searches should have shorter timeout (faster processing)
        $this->assertLessThan($externalTimeout, $internalTimeout);
        $this->assertEquals(900, $internalTimeout);   // 15 minutes
        $this->assertEquals(2400, $externalTimeout); // 40 minutes
    }

    /** @test */
    public function worker_allocation_prioritizes_internal_searches()
    {
        $horizonDefaults = Config::get('horizon.defaults');
        
        $internalProcesses = $horizonDefaults['internal-search']['maxProcesses'];
        $externalProcesses = $horizonDefaults['external-search']['maxProcesses'];
        $generalProcesses = $horizonDefaults['general']['maxProcesses'];
        
        // Internal should have more or equal processes compared to external
        $this->assertGreaterThanOrEqual($externalProcesses, $internalProcesses);
        
        // Priority should be: internal (0) > external (5) > general (10)
        $this->assertLessThan($horizonDefaults['external-search']['nice'], $horizonDefaults['internal-search']['nice']);
        $this->assertLessThan($horizonDefaults['general']['nice'], $horizonDefaults['external-search']['nice']);
    }

    /** @test */
    public function queue_strategy_follows_course_guidelines()
    {
        // Verify implementation follows lesson 12: Multiple Queues and Priority Jobs
        $queueConnections = Config::get('queue.connections');
        $this->assertArrayHasKey('redis_internal', $queueConnections);
        $this->assertArrayHasKey('redis_external', $queueConnections);
        
        // Verify implementation follows lesson 14: Redis and Laravel Horizon
        $horizonConfig = Config::get('horizon.defaults');
        $this->assertArrayHasKey('internal-search', $horizonConfig);
        $this->assertArrayHasKey('external-search', $horizonConfig);
        
        // Verify implementation follows lesson 15: Supervisor and Multiple Queue Workers
        $environments = Config::get('horizon.environments');
        $this->assertArrayHasKey('production', $environments);
        $this->assertArrayHasKey('local', $environments);
    }
}