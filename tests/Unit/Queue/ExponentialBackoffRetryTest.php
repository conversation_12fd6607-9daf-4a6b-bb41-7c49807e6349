<?php

namespace Tests\Unit\Queue;

use Tests\TestCase;
use App\Jobs\Traits\ExponentialBackoffRetry;
use App\Jobs\SearchCandidatesJob;
use App\Jobs\SearchInternalPeopleJob;
use App\Jobs\ExternalSearchPerplexityJob;
use App\Models\User;
use App\Notifications\JobFailedNotification;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Carbon\Carbon;
use Mockery;

class ExponentialBackoffRetryTest extends TestCase
{
    use DatabaseTransactions;
    
    protected $user;
    protected $planData;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = (object) [
            'id' => 1,
            'email' => '<EMAIL>',
            'company_id' => 1
        ];

        $this->planData = [
            'plan_id' => 'backoff-test-123',
            'plan_name' => 'Test Backoff Plan',
            'target_roles' => ['Manager'],
            'companies' => ['Test Company'],
            'alternative_roles_titles' => [],
            'step_up_candidates' => ['none'],
            'minimum_tenure' => null,
            'gender' => 'Not required',
            'country' => ['none'],
            'is_ethnicity_important' => false,
            'qualifications' => [],
            'skills' => [],
            'acronyms' => []
        ];
    }

    /** @test */
    public function it_calculates_exponential_backoff_delays_correctly()
    {
        // Create test job using trait
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        
        // Test default exponential backoff calculation
        $expectedDelays = [30, 60, 120, 240, 480]; // 30 * 2^(attempt-1)
        $actualDelays = $job->backoff();
        
        $this->assertEquals($expectedDelays, $actualDelays);
    }

    /** @test */
    public function it_calculates_custom_exponential_backoff_with_different_base_delay()
    {
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        $job->setBaseDelay(60); // 1 minute base
        
        $expectedDelays = [60, 120, 240, 480, 960]; // 60 * 2^(attempt-1)
        $actualDelays = $job->backoff();
        
        $this->assertEquals($expectedDelays, $actualDelays);
    }

    /** @test */
    public function it_calculates_custom_exponential_backoff_with_different_multiplier()
    {
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        $job->setBackoffMultiplier(3); // Triple instead of double
        $job->setMaxDelay(3000); // Increase max delay to accommodate test
        
        $expectedDelays = [30, 90, 270, 810, 2430]; // 30 * 3^(attempt-1)
        $actualDelays = $job->backoff();
        
        $this->assertEquals($expectedDelays, $actualDelays);
    }

    /** @test */
    public function it_limits_maximum_retry_attempts()
    {
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        $job->setMaxRetries(3);
        
        $expectedDelays = [30, 60, 120]; // Only 3 attempts
        $actualDelays = $job->backoff();
        
        $this->assertEquals($expectedDelays, $actualDelays);
        $this->assertCount(3, $actualDelays);
    }

    /** @test */
    public function it_caps_maximum_delay_to_prevent_extremely_long_waits()
    {
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        $job->setMaxDelay(300); // 5 minutes max
        $job->setMaxRetries(10);
        
        $delays = $job->backoff();
        
        // All delays should be <= 300 seconds
        foreach ($delays as $delay) {
            $this->assertLessThanOrEqual(300, $delay);
        }
        
        // Later attempts should be capped at max delay
        $this->assertEquals(300, end($delays));
    }

    /** @test */
    public function it_sets_retry_until_timestamp_correctly()
    {
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        $job->setMaxRetryTime(120); // 2 hours
        
        Carbon::setTestNow(Carbon::parse('2023-01-01 12:00:00'));
        
        $retryUntil = $job->retryUntil();
        $expectedTime = Carbon::parse('2023-01-01 14:00:00'); // 2 hours later
        
        $this->assertEquals($expectedTime->timestamp, $retryUntil->timestamp);
    }

    /** @test */
    public function search_candidates_job_uses_exponential_backoff()
    {
        $job = new SearchCandidatesJob($this->planData, $this->user);
        
        // Should have exponential backoff trait
        $this->assertTrue(method_exists($job, 'backoff'));
        $this->assertTrue(method_exists($job, 'retryUntil'));
        
        // Should have default retry configuration
        $this->assertEquals(5, $job->tries);
        $this->assertIsArray($job->backoff());
        $this->assertInstanceOf(\Carbon\Carbon::class, $job->retryUntil());
    }

    /** @test */
    public function search_internal_people_job_uses_exponential_backoff()
    {
        $job = new SearchInternalPeopleJob($this->planData, $this->user);
        
        // Should have exponential backoff trait
        $this->assertTrue(method_exists($job, 'backoff'));
        $this->assertTrue(method_exists($job, 'retryUntil'));
        
        // Should have shorter retry configuration for internal operations
        $this->assertEquals(3, $job->tries);
        $delays = $job->backoff();
        $this->assertLessThan(600, max($delays)); // Max 10 minutes for internal
    }

    /** @test */
    public function external_search_perplexity_job_uses_exponential_backoff()
    {
        $job = new ExternalSearchPerplexityJob($this->planData, $this->user);
        
        // Should have exponential backoff trait
        $this->assertTrue(method_exists($job, 'backoff'));
        $this->assertTrue(method_exists($job, 'retryUntil'));
        
        // Should have longer retry configuration for external operations
        $this->assertEquals(5, $job->tries);
        $delays = $job->backoff();
        $this->assertGreaterThan(60, max($delays)); // At least 1 minute delays
    }

    /** @test */
    public function it_logs_retry_attempts_with_backoff_information()
    {
        Log::spy();
        
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        $job->simulateFailure(); // Trigger retry logging
        
        Log::shouldHaveReceived('warning')
            ->with('Job retry attempt with exponential backoff', \Mockery::any())
            ->once();
    }

    /** @test */
    public function it_handles_zero_base_delay_gracefully()
    {
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        $job->setBaseDelay(0);
        
        $delays = $job->backoff();
        
        // Should default to minimum 1 second delays
        foreach ($delays as $delay) {
            $this->assertGreaterThanOrEqual(1, $delay);
        }
    }

    /** @test */
    public function it_handles_negative_retry_counts_gracefully()
    {
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        $job->setMaxRetries(-1);
        
        $delays = $job->backoff();
        
        // Should default to at least 1 retry
        $this->assertGreaterThanOrEqual(1, count($delays));
    }

    /** @test */
    public function it_provides_jitter_option_to_prevent_thundering_herd()
    {
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        $job->enableJitter(true);
        
        $delays1 = $job->backoff();
        $delays2 = $job->backoff();
        
        // With jitter, delays should be slightly different between calls
        $this->assertNotEquals($delays1, $delays2);
        
        // But should be within reasonable range of base delays
        for ($i = 0; $i < count($delays1); $i++) {
            $expectedBase = 30 * pow(2, $i);
            $this->assertGreaterThan($expectedBase * 0.5, $delays1[$i]);
            $this->assertLessThan($expectedBase * 1.5, $delays1[$i]);
        }
    }

    /** @test */
    public function it_integrates_with_laravel_queue_retry_mechanism()
    {
        Queue::fake();
        
        $job = new TestJobWithExponentialBackoff($this->planData, $this->user);
        
        // Simulate job dispatch and verify queue properties
        $job::dispatch($this->planData, $this->user);
        
        Queue::assertPushed(TestJobWithExponentialBackoff::class, function ($dispatchedJob) {
            return $dispatchedJob->tries === 5 &&
                   is_array($dispatchedJob->backoff()) &&
                   $dispatchedJob->retryUntil() instanceof \Carbon\Carbon;
        });
    }

    /** @test */
    public function it_notifies_development_email_when_job_fails_after_all_retries()
    {
        // Create the development user with the specific email
        $developmentUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'Admin'
        ]);
        
        // Create other admin users that should NOT be notified
        $otherAdmin = User::factory()->admin()->create(['email' => '<EMAIL>']);
        $masterUser = User::factory()->master()->create(['email' => '<EMAIL>']);
        
        $planData = ['plan_id' => 123, 'plan_name' => 'Test Plan'];
        $user = User::factory()->create();
        
        // Create a simple test job that uses the trait
        $job = new class($planData, $user) {
            use ExponentialBackoffRetry;
            
            public $planData;
            public $user;
            public $tries = 3;
            
            public function __construct($planData, $user)
            {
                $this->planData = $planData;
                $this->user = $user;
            }
            
            public function handle() 
            {
                // Test job
            }
        };
        
        $exception = new \Exception('Test failure');
        
        // Call the failed method directly
        $job->failed($exception);
        
        // Check that ONLY the development email was notified
        $this->assertDatabaseHas('laravel_notifications', [
            'notifiable_id' => $developmentUser->id,
            'notifiable_type' => User::class,
            'type' => JobFailedNotification::class
        ]);
        
        // Ensure other admins were NOT notified
        $this->assertDatabaseMissing('laravel_notifications', [
            'notifiable_id' => $otherAdmin->id,
            'type' => JobFailedNotification::class
        ]);
        
        $this->assertDatabaseMissing('laravel_notifications', [
            'notifiable_id' => $masterUser->id,
            'type' => JobFailedNotification::class
        ]);
    }

    /** @test */
    public function it_includes_job_context_in_development_notification()
    {
        // Create the development user
        $developmentUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'Admin'
        ]);
        
        $planData = ['plan_id' => 456, 'plan_name' => 'Critical Plan'];
        $user = User::factory()->create();
        
        $job = new SearchCandidatesJob($planData, $user);
        $exception = new \Exception('Critical system failure');
        
        $job->failed($exception);
        
        // Check database for the notification sent to development email
        $notification = \DB::table('laravel_notifications')
            ->where('notifiable_id', $developmentUser->id)
            ->where('type', JobFailedNotification::class)
            ->first();
            
        $this->assertNotNull($notification, 'Notification should exist in database');
        
        $data = json_decode($notification->data, true);
        $this->assertEquals(SearchCandidatesJob::class, $data['job_class']);
        $this->assertEquals('Critical system failure', $data['error_message']);
        $this->assertArrayHasKey('job_data', $data);
    }

    /** @test */
    public function it_only_notifies_development_email_for_job_failures()
    {
        // Create the development user
        $developmentUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'Admin'
        ]);
        
        $planData = ['plan_id' => 789, 'plan_name' => 'Test Plan'];
        $user = User::factory()->create();
        
        // Create users with different roles that should NOT be notified
        $admin = User::factory()->admin()->create(['email' => '<EMAIL>']);
        $master = User::factory()->master()->create(['email' => '<EMAIL>']);
        $regularUser1 = User::factory()->user()->create();
        $regularUser2 = User::factory()->create(['role' => 'SomeOtherRole']);
        
        $job = new SearchCandidatesJob($planData, $user);
        $exception = new \Exception('Job failure');
        
        $job->failed($exception);
        
        // Check that ONLY the development user was notified
        $this->assertDatabaseHas('laravel_notifications', [
            'notifiable_id' => $developmentUser->id,
            'type' => JobFailedNotification::class
        ]);
        
        // Check that NO other users were notified
        $this->assertDatabaseMissing('laravel_notifications', [
            'notifiable_id' => $admin->id,
            'type' => JobFailedNotification::class
        ]);
        
        $this->assertDatabaseMissing('laravel_notifications', [
            'notifiable_id' => $master->id,
            'type' => JobFailedNotification::class
        ]);
        
        $this->assertDatabaseMissing('laravel_notifications', [
            'notifiable_id' => $regularUser1->id,
            'type' => JobFailedNotification::class
        ]);
        
        $this->assertDatabaseMissing('laravel_notifications', [
            'notifiable_id' => $regularUser2->id,
            'type' => JobFailedNotification::class
        ]);
    }
}

/**
 * Test job class that uses ExponentialBackoffRetry trait
 * This will be implemented in the GREEN phase
 */
class TestJobWithExponentialBackoff implements \Illuminate\Contracts\Queue\ShouldQueue
{
    use \Illuminate\Bus\Queueable;
    use \Illuminate\Queue\SerializesModels;
    use \Illuminate\Foundation\Bus\Dispatchable;
    use \Illuminate\Queue\InteractsWithQueue;
    // This trait doesn't exist yet - will be created in GREEN phase
    use ExponentialBackoffRetry;

    public $tries = 5;
    protected $planData;
    protected $user;

    public function __construct($planData, $user)
    {
        $this->planData = $planData;
        $this->user = $user;
    }

    public function handle()
    {
        // Test job logic
    }

    public function simulateFailure()
    {
        $this->logRetryAttempt();
    }
}