<?php

namespace Tests\Feature\Queue;

use Tests\TestCase;
use App\Jobs\SearchCandidatesJob;
use App\Jobs\SearchInternalPeopleJob;
use App\Jobs\ExternalSearchPerplexityJob;
use App\Models\User;
use App\Models\Company;
use App\Models\InternalPeople;
use App\Models\People;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;

class SearchQueueIntegrationTest extends TestCase
{
    use DatabaseTransactions;

    protected $user;
    protected $company;
    protected $planData;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test company
        $this->company = Company::factory()->create([
            'name' => 'Test Company',
            'status' => 'Active'
        ]);

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'company_id' => $this->company->id
        ]);

        // Create test plan data
        $this->planData = [
            'plan_id' => 'integration-test-' . time(),
            'plan_name' => 'Integration Test Succession Plan',
            'target_roles' => ['Software Engineer', 'Senior Developer'],
            'companies' => ['Test Company', 'Another Company'],
            'alternative_roles_titles' => ['Lead Developer'],
            'step_up_candidates' => ['Junior Developer'],
            'minimum_tenure' => 2,
            'gender' => 'Not required',
            'country' => ['United States', 'Canada'],
            'is_ethnicity_important' => false,
            'qualifications' => ['Bachelor\'s Degree', 'Master\'s Degree'],
            'skills' => ['PHP', 'Laravel', 'JavaScript'],
            'acronyms' => ['API', 'SQL']
        ];
    }

    /** @test */
    public function it_processes_complete_search_workflow_through_separate_queues()
    {
        // Create some test data
        $this->createTestPeople();

        Queue::fake();

        // Dispatch the master search job
        SearchCandidatesJob::dispatch($this->planData, $this->user, SearchCandidatesJob::SEARCH_BOTH);

        // Verify that both internal and external searches are queued correctly
        Queue::assertPushedOn('internal_search', SearchInternalPeopleJob::class);
        Queue::assertPushedOn('external_search', ExternalSearchPerplexityJob::class);

        // Verify the jobs have correct data
        Queue::assertPushed(SearchInternalPeopleJob::class, function ($job) {
            return $job->queue === 'internal_search';
        });

        Queue::assertPushed(ExternalSearchPerplexityJob::class, function ($job) {
            return $job->queue === 'external_search' && $job->timeout === 2400;
        });
    }

    /** @test */
    public function it_can_process_internal_search_with_real_data()
    {
        $this->createTestPeople();

        // Process internal search job directly (without queue)
        $job = new SearchInternalPeopleJob($this->planData, $this->user);
        
        // Should not throw exceptions
        $this->expectNotToPerformAssertions();
        $job->handle();
    }

    /** @test */
    public function it_respects_queue_priorities_in_configuration()
    {
        // Test that Redis connections are properly configured
        $this->assertEquals('redis', Config::get('queue.connections.redis_internal.driver'));
        $this->assertEquals('redis', Config::get('queue.connections.redis_external.driver'));
        
        // Test that timeouts are different to reflect priority
        $internalTimeout = Config::get('queue.connections.redis_internal.retry_after');
        $externalTimeout = Config::get('queue.connections.redis_external.retry_after');
        
        $this->assertEquals(900, $internalTimeout);   // 15 minutes for internal
        $this->assertEquals(2400, $externalTimeout); // 40 minutes for external
        $this->assertLessThan($externalTimeout, $internalTimeout); // Internal should be faster
    }

    /** @test */
    public function it_can_handle_queue_configuration_caching()
    {
        // Clear config cache
        Artisan::call('config:clear');
        
        // Verify queues are still configured correctly
        $this->assertEquals('internal_search', Config::get('queue.connections.redis_internal.queue'));
        $this->assertEquals('external_search', Config::get('queue.connections.redis_external.queue'));
        
        // Cache config
        Artisan::call('config:cache');
        
        // Verify configuration persists after caching
        $this->assertEquals('internal_search', Config::get('queue.connections.redis_internal.queue'));
        $this->assertEquals('external_search', Config::get('queue.connections.redis_external.queue'));
    }

    /** @test */
    public function it_handles_large_plan_data_efficiently()
    {
        // Create a large plan with many parameters
        $largePlanData = array_merge($this->planData, [
            'target_roles' => array_fill(0, 50, 'Manager'),
            'companies' => array_fill(0, 100, 'Company'),
            'skills' => array_fill(0, 200, 'Skill'),
            'qualifications' => array_fill(0, 20, 'Qualification')
        ]);

        Queue::fake();

        // Should still dispatch correctly even with large data
        SearchCandidatesJob::dispatch($largePlanData, $this->user, SearchCandidatesJob::SEARCH_BOTH);

        Queue::assertPushed(SearchInternalPeopleJob::class);
        Queue::assertPushed(ExternalSearchPerplexityJob::class);
    }

    /** @test */
    public function it_maintains_separate_queue_metrics()
    {
        Queue::fake();

        // Dispatch multiple jobs to different queues
        for ($i = 0; $i < 5; $i++) {
            SearchCandidatesJob::dispatch($this->planData, $this->user, SearchCandidatesJob::SEARCH_INTERNAL);
        }

        for ($i = 0; $i < 3; $i++) {
            SearchCandidatesJob::dispatch($this->planData, $this->user, SearchCandidatesJob::SEARCH_EXTERNAL);
        }

        // Verify correct number of jobs in each queue
        Queue::assertPushed(SearchInternalPeopleJob::class, 5);
        Queue::assertPushed(ExternalSearchPerplexityJob::class, 3);
    }

    /** @test */
    public function it_handles_queue_connection_failures_gracefully()
    {
        // Simulate connection failure by using invalid queue config
        Config::set('queue.connections.redis_internal.connection', 'nonexistent');
        
        Queue::fake();

        // Should still attempt to dispatch (queue fake prevents actual connection)
        SearchCandidatesJob::dispatch($this->planData, $this->user, SearchCandidatesJob::SEARCH_INTERNAL);

        Queue::assertPushed(SearchInternalPeopleJob::class);
    }

    /** @test */
    public function it_supports_horizon_supervisor_configuration()
    {
        $horizonDefaults = Config::get('horizon.defaults');
        
        // Verify all required supervisors exist
        $this->assertArrayHasKey('internal-search', $horizonDefaults);
        $this->assertArrayHasKey('external-search', $horizonDefaults);
        $this->assertArrayHasKey('general', $horizonDefaults);

        // Verify priority ordering (lower nice = higher priority)
        $internalNice = $horizonDefaults['internal-search']['nice'];
        $externalNice = $horizonDefaults['external-search']['nice'];
        $generalNice = $horizonDefaults['general']['nice'];

        $this->assertLessThan($externalNice, $internalNice);
        $this->assertLessThan($generalNice, $externalNice);
    }

    /** @test */
    public function it_scales_workers_appropriately_by_environment()
    {
        $environments = Config::get('horizon.environments');

        // Production should have more workers than local
        $prodInternal = $environments['production']['internal-search']['maxProcesses'];
        $localInternal = $environments['local']['internal-search']['maxProcesses'];
        $this->assertGreaterThan($localInternal, $prodInternal);

        $prodExternal = $environments['production']['external-search']['maxProcesses'];
        $localExternal = $environments['local']['external-search']['maxProcesses'];
        $this->assertGreaterThan($localExternal, $prodExternal);

        // Internal should have more workers than external in both environments
        $this->assertGreaterThan($prodExternal, $prodInternal);
        $this->assertGreaterThan($localExternal, $localInternal);
    }

    /** @test */
    public function it_handles_concurrent_search_requests()
    {
        Queue::fake();

        // Simulate concurrent requests
        $plans = [];
        for ($i = 0; $i < 10; $i++) {
            $planData = array_merge($this->planData, [
                'plan_id' => 'concurrent-test-' . $i,
                'plan_name' => 'Concurrent Plan ' . $i
            ]);
            $plans[] = $planData;
        }

        // Dispatch all at once
        foreach ($plans as $plan) {
            SearchCandidatesJob::dispatch($plan, $this->user, SearchCandidatesJob::SEARCH_BOTH);
        }

        // Should handle all requests
        Queue::assertPushed(SearchInternalPeopleJob::class, 10);
        Queue::assertPushed(ExternalSearchPerplexityJob::class, 10);
    }

    private function createTestPeople()
    {
        // Create internal people
        InternalPeople::factory()->create([
            'company_id' => $this->company->id,
            'forename' => 'John',
            'surname' => 'Doe',
            'latest_role' => 'Software Engineer',
            'company_name' => 'Test Company',
            'country' => 'United States'
        ]);

        InternalPeople::factory()->create([
            'company_id' => $this->company->id,
            'forename' => 'Jane',
            'surname' => 'Smith',
            'latest_role' => 'Senior Developer',
            'company_name' => 'Test Company',
            'country' => 'Canada'
        ]);

        // Create external people
        People::factory()->create([
            'company_id' => 999, // Different company
            'forename' => 'Bob',
            'surname' => 'Johnson',
            'latest_role' => 'Lead Developer',
            'company_name' => 'Another Company',
            'country' => 'United States',
            'status' => 'Active'
        ]);
    }
}