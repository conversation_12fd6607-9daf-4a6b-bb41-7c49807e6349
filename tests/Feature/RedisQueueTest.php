<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Redis;
use App\Jobs\SearchCandidatesJob;
use App\Models\User;
use Illuminate\Support\Facades\Config;

class RedisQueueTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set queue connection to redis for tests
        Config::set('queue.default', 'redis');
    }

    /** @test */
    public function it_can_connect_to_redis()
    {
        try {
            Redis::ping();
            $this->assertTrue(true, 'Redis connection successful');
        } catch (\Exception $e) {
            $this->markTestSkipped('Redis server not available: ' . $e->getMessage());
        }
    }

    /** @test */
    public function it_can_dispatch_jobs_to_redis_queue()
    {
        Queue::fake();
        
        $user = User::factory()->create();
        $planData = json_decode(file_get_contents(base_path('shared/search_params_synthetic_01.json')), true);
        
        SearchCandidatesJob::dispatch($planData, $user, 'internal');
        
        Queue::assertPushed(SearchCandidatesJob::class);
    }

    /** @test */
    public function it_can_serialize_complex_job_data_for_redis()
    {
        $user = User::factory()->create();
        $planData = json_decode(file_get_contents(base_path('shared/search_params_synthetic_01.json')), true);
        
        $job = new SearchCandidatesJob($planData, $user, 'both');
        
        // Test that job can be serialized
        $serialized = serialize($job);
        $unserialized = unserialize($serialized);
        
        $this->assertInstanceOf(SearchCandidatesJob::class, $unserialized);
        // Access planData using reflection since it's protected
        $reflection = new \ReflectionClass($unserialized);
        $planDataProperty = $reflection->getProperty('planData');
        $planDataProperty->setAccessible(true);
        $unserializedPlanData = $planDataProperty->getValue($unserialized);
        
        $this->assertEquals($planData['plan_name'], $unserializedPlanData['plan_name']);
    }

    /** @test */
    public function it_handles_redis_queue_connection_errors_gracefully()
    {
        // Test with invalid Redis configuration
        Config::set('database.redis.default.host', 'invalid-host');
        
        $user = User::factory()->create();
        $planData = json_decode(file_get_contents(base_path('shared/search_params_synthetic_01.json')), true);
        
        try {
            SearchCandidatesJob::dispatch($planData, $user, 'internal');
        } catch (\Exception $e) {
            $this->assertStringContainsString('invalid-host', $e->getMessage());
        }
    }

    /** @test */
    public function it_respects_job_timeout_settings_in_redis()
    {
        $user = User::factory()->create();
        $planData = json_decode(file_get_contents(base_path('shared/search_params_synthetic_01.json')), true);
        
        $job = new SearchCandidatesJob($planData, $user, 'both');
        
        // Verify the job has the expected timeout
        $this->assertEquals(1800, $job->timeout); // 30 minutes
    }

    /** @test */
    public function it_can_store_and_retrieve_job_data_from_redis()
    {
        $testKey = 'test:job:data';
        $testData = [
            'plan_name' => 'Test Plan',
            'complex_array' => [
                'nested' => ['data' => 'value']
            ]
        ];
        
        Redis::set($testKey, json_encode($testData));
        $retrieved = json_decode(Redis::get($testKey), true);
        
        $this->assertEquals($testData, $retrieved);
        
        // Cleanup
        Redis::del($testKey);
    }
}