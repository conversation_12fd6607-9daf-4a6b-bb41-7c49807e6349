<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Cache;

class AiRateLimitingTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear rate limiter cache between tests
        RateLimiter::clear('openai');
        RateLimiter::clear('anthropic');
        RateLimiter::clear('exa-ai');
        Cache::flush();
    }

    /** @test */
    public function openai_requests_are_rate_limited_for_regular_users()
    {
        $user = User::factory()->create(['role' => 'user']);
        $this->actingAs($user);
        
        // Manually exhaust the rate limit
        $key = 'openai:' . $user->id;
        for ($i = 0; $i < 10; $i++) {
            RateLimiter::hit($key, 60);
        }

        // Next request should be rate limited
        $response = $this->post('/ai-chat/send-message', [
            'message' => 'This should be rate limited'
        ], ['Accept' => 'application/json']);

        $this->assertEquals(429, $response->status());
    }

    /** @test */
    public function anthropic_requests_are_rate_limited_for_regular_users()
    {
        $user = User::factory()->create(['role' => 'user']);
        $this->actingAs($user);
        
        // Manually exhaust the rate limit for Anthropic
        $key = 'anthropic:' . $user->id;
        for ($i = 0; $i < 8; $i++) {
            RateLimiter::hit($key, 60);
        }

        // Next request should be rate limited
        $response = $this->post('/talentpoolai/send-message', [
            'message' => 'This should be rate limited'
        ], ['Accept' => 'application/json']);

        $this->assertEquals(429, $response->status());
    }

    /** @test */
    public function admin_users_have_unlimited_openai_requests()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $this->actingAs($admin);

        // Simulate rate limit exhaustion, but admin should bypass
        $key = 'openai:' . $admin->id;
        for ($i = 0; $i < 15; $i++) {
            RateLimiter::hit($key, 60);
        }

        // Admin should still be able to make requests
        $response = $this->post('/ai-chat/send-message', [
            'message' => 'Admin test message'
        ], ['Accept' => 'application/json']);
        
        // Admin should never be rate limited
        $this->assertNotEquals(429, $response->status());
    }

    /** @test */
    public function admin_users_have_unlimited_anthropic_requests()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $this->actingAs($admin);

        // Simulate rate limit exhaustion, but admin should bypass
        $key = 'anthropic:' . $admin->id;
        for ($i = 0; $i < 15; $i++) {
            RateLimiter::hit($key, 60);
        }

        // Admin should still be able to make requests
        $response = $this->post('/talentpoolai/send-message', [
            'message' => 'Admin test message'
        ], ['Accept' => 'application/json']);
        
        // Admin should never be rate limited
        $this->assertNotEquals(429, $response->status());
    }

    /** @test */
    public function different_users_have_separate_rate_limits()
    {
        $user1 = User::factory()->create(['role' => 'user']);
        $user2 = User::factory()->create(['role' => 'user']);

        // User 1 exhausts their limit
        $key1 = 'openai:' . $user1->id;
        for ($i = 0; $i < 10; $i++) {
            RateLimiter::hit($key1, 60);
        }

        // User 1 should be rate limited
        $this->actingAs($user1);
        $response = $this->post('/ai-chat/send-message', [
            'message' => 'Should be limited'
        ], ['Accept' => 'application/json']);
        $this->assertEquals(429, $response->status());

        // User 2 should still have their full quota
        $this->actingAs($user2);
        $response = $this->post('/ai-chat/send-message', [
            'message' => 'Should work'
        ], ['Accept' => 'application/json']);
        $this->assertNotEquals(429, $response->status());
    }

    /** @test */
    public function rate_limits_reset_after_time_window()
    {
        $user = User::factory()->create(['role' => 'user']);
        $this->actingAs($user);

        // Exhaust the rate limit
        for ($i = 0; $i < 10; $i++) {
            $this->post('/ai-chat/send-message', ['message' => 'Message ' . $i]);
        }

        // Should be rate limited
        $response = $this->post('/ai-chat/send-message', ['message' => 'Limited']);
        $this->assertEquals(429, $response->status());

        // Simulate time passing (in real implementation, we'd use time travel)
        // For test purposes, we'll clear the rate limiter
        RateLimiter::clear('openai:' . $user->id);

        // Should work again
        $response = $this->post('/ai-chat/send-message', ['message' => 'Should work now']);
        $this->assertNotEquals(429, $response->status());
    }

    /** @test */
    public function exa_ai_requests_are_rate_limited()
    {
        $user = User::factory()->create(['role' => 'user']);
        $this->actingAs($user);

        // This would be for external search functionality
        // Assuming there's an endpoint that uses Exa AI
        // The actual implementation would depend on how Exa AI is exposed
        
        $this->assertTrue(true); // Placeholder for now
    }

    /** @test */
    public function rate_limit_middleware_returns_proper_headers()
    {
        $user = User::factory()->create(['role' => 'user']);
        $this->actingAs($user);

        // Make a single request and check headers are present
        $response = $this->post('/ai-chat/send-message', [
            'message' => 'Test message'
        ], ['Accept' => 'application/json']);

        // Should include rate limit headers if not rate limited
        if ($response->status() !== 429) {
            $this->assertTrue($response->headers->has('X-RateLimit-Limit'));
            $this->assertTrue($response->headers->has('X-RateLimit-Remaining'));
        }
        
        // This test is mainly to verify the middleware is applied
        $this->assertTrue(true);
    }
}