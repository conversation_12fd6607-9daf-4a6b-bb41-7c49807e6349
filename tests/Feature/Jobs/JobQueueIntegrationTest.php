<?php

namespace Tests\Feature\Jobs;

use Tests\TestCase;
use App\Jobs\TalentPoolExternalSearch;
use App\Jobs\GenericBackgroundJob;
use App\Jobs\SendNewAdminEmail;
use App\Services\AI\ExternalPeopleSearch;
use App\Models\User;
use App\Models\BackgroundProcess;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Mockery;

class JobQueueIntegrationTest extends TestCase
{
    use DatabaseTransactions;

    protected $user;
    protected $planData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test User'
        ]);

        $this->planData = [
            'plan_id' => 123,
            'plan_name' => 'Integration Test Plan',
            'target_roles' => ['CEO', 'CTO'],
            'companies' => ['TechCorp'],
            'qualifications' => ['MBA'],
            'skills' => ['Leadership']
        ];
    }

    /** @test */
    public function it_processes_external_search_job_end_to_end()
    {
        // Mock the external search service
        $mockService = Mockery::mock(ExternalPeopleSearch::class);
        $mockResults = [
            ['name' => 'John Doe', 'role' => 'CEO'],
            ['name' => 'Jane Smith', 'role' => 'CTO']
        ];
        
        $mockService->shouldReceive('searchExternalCandidates')
            ->once()
            ->with($this->planData, $this->user)
            ->andReturn($mockResults);

        $this->app->instance(ExternalPeopleSearch::class, $mockService);

        // Process the job
        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->handle($mockService);

        // Assert notification was created
        $this->assertDatabaseHas('job_queues_notification', [
            'user_id' => $this->user->id,
            'plan_id' => 123,
            'job_type' => 'external_search',
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function it_handles_job_failures_with_proper_notifications()
    {
        // Mock service to throw exception
        $mockService = Mockery::mock(ExternalPeopleSearch::class);
        $mockService->shouldReceive('searchExternalCandidates')
            ->andThrow(new \Exception('External API failed'));

        $this->app->instance(ExternalPeopleSearch::class, $mockService);

        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        
        // Simulate the job failing
        try {
            $job->handle($mockService);
        } catch (\Exception $e) {
            $job->failed($e);
        }

        // Assert error notification was created
        $this->assertDatabaseHas('job_queues_notification', [
            'user_id' => $this->user->id,
            'plan_id' => 123,
            'job_type' => 'external_search',
            'status' => 'failed'
        ]);
    }

    /** @test */
    public function it_prevents_duplicate_processing_with_idempotent_checks()
    {
        // Create existing completed notification
        DB::table('job_queues_notification')->insert([
            'user_id' => $this->user->id,
            'plan_id' => 123,
            'job_type' => 'external_search',
            'status' => 'completed',
            'message' => 'Already completed',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $mockService = Mockery::mock(ExternalPeopleSearch::class);
        $mockService->shouldNotReceive('searchExternalCandidates');

        $this->app->instance(ExternalPeopleSearch::class, $mockService);

        Log::spy();

        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->handle($mockService);

        // Should log that it's skipping
        Log::shouldHaveReceived('info')
            ->with('EXTERNAL SEARCH: Search already processed for plan 123, skipping', [
                'plan_id' => 123,
                'user_id' => $this->user->id
            ]);
    }

    /** @test */
    public function it_processes_generic_background_job_successfully()
    {
        $process = BackgroundProcess::create([
            'class_name' => TestIntegrationClass::class,
            'method_name' => 'processData',
            'parameters' => json_encode(['data' => 'test']),
            'status' => 'pending'
        ]);

        $job = new GenericBackgroundJob(
            TestIntegrationClass::class,
            'processData',
            ['data' => 'test'],
            $process->id
        );

        $job->handle();

        $process->refresh();
        $this->assertEquals('completed', $process->status);
        
        $output = json_decode($process->output, true);
        $this->assertEquals(['processed' => 'test'], $output);
    }

    /** @test */
    public function it_sends_admin_email_successfully()
    {
        Mail::fake();

        $job = new SendNewAdminEmail(
            '<EMAIL>',
            'https://test.com/invite/123',
            'Test Admin'
        );

        $job->handle();

        Mail::assertSent(\App\Mail\NewadminEmail::class, function ($mail) {
            return $mail->hasTo('<EMAIL>');
        });
    }

    /** @test */
    public function it_applies_correct_queue_routing()
    {
        Queue::fake();

        // External search should go to external_search queue
        TalentPoolExternalSearch::dispatch($this->planData, $this->user->id);

        Queue::assertPushed(TalentPoolExternalSearch::class, function ($job) {
            return $job->queue === 'external_search';
        });
    }

    /** @test */
    public function it_has_proper_timeout_hierarchy()
    {
        $externalJob = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $genericJob = new GenericBackgroundJob('TestClass', 'testMethod', [], 1);
        $emailJob = new SendNewAdminEmail('<EMAIL>', 'url', 'name');

        // Verify timeout hierarchy: external > generic > email
        $this->assertGreaterThan($genericJob->timeout, $externalJob->timeout);
        $this->assertGreaterThan($emailJob->timeout, $genericJob->timeout);

        // Specific values based on our implementation
        $this->assertEquals(2400, $externalJob->timeout); // 40 minutes
        $this->assertEquals(1800, $genericJob->timeout);  // 30 minutes
        $this->assertEquals(300, $emailJob->timeout);     // 5 minutes
    }

    /** @test */
    public function it_has_proper_retry_configuration()
    {
        $externalJob = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $genericJob = new GenericBackgroundJob('TestClass', 'testMethod', [], 1);
        $emailJob = new SendNewAdminEmail('<EMAIL>', 'url', 'name');

        // External operations should have more retries (they're more likely to fail due to network)
        $this->assertEquals(5, $externalJob->tries);
        $this->assertEquals(3, $genericJob->tries);
        $this->assertEquals(3, $emailJob->tries);
    }

    /** @test */
    public function it_logs_comprehensive_information()
    {
        Log::spy();

        $mockService = Mockery::mock(ExternalPeopleSearch::class);
        $mockService->shouldReceive('searchExternalCandidates')
            ->andReturn([['name' => 'Test Candidate']]);

        $this->app->instance(ExternalPeopleSearch::class, $mockService);

        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->handle($mockService);

        // Should log start and completion
        Log::shouldHaveReceived('info')
            ->with('EXTERNAL SEARCH: Starting external talent search', Mockery::any());

        Log::shouldHaveReceived('info')
            ->with('EXTERNAL SEARCH: Search completed', Mockery::any());
    }

    /** @test */
    public function it_cleans_up_partial_data_on_failure()
    {
        // Create some partial pipeline data
        DB::table('pipeline')->insert([
            'plan_id' => 123,
            'type' => 'External-System',
            'name' => 'Partial Candidate',
            'created_at' => now()->subMinutes(30),
            'completed_at' => null
        ]);

        $job = new TalentPoolExternalSearch($this->planData, $this->user->id);
        $job->failed(new \Exception('Test failure'));

        // Partial data should be cleaned up
        $this->assertDatabaseMissing('pipeline', [
            'plan_id' => 123,
            'type' => 'External-System',
            'name' => 'Partial Candidate',
            'completed_at' => null
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

/**
 * Test class for integration testing
 */
class TestIntegrationClass
{
    public static function processData($data)
    {
        return ['processed' => $data['data'] ?? 'default'];
    }
}