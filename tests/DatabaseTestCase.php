<?php

namespace Tests;

use Illuminate\Foundation\Testing\DatabaseTransactions;

/**
 * Base test case for database tests with SAFE isolation
 * Uses DatabaseTransactions instead of RefreshDatabase to prevent data loss
 */
abstract class DatabaseTestCase extends TestCase
{
    use DatabaseTransactions;
    
    /**
     * Setup the test environment with safety checks
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Safety check: ensure we're not using production data
        $this->assertSafeTestEnvironment();
    }
    
    /**
     * Ensure we're not accidentally affecting production data
     */
    protected function assertSafeTestEnvironment(): void
    {
        $connection = config('database.default');
        $database = config("database.connections.{$connection}.database");
        
        // Block if using common production database names
        $productionNames = ['successionplan', 'production', 'live', 'main'];
        
        foreach ($productionNames as $prodName) {
            if (str_contains(strtolower($database), $prodName) && !str_contains(strtolower($database), 'test')) {
                $this->markTestSkipped("Refusing to run test against potential production database: {$database}");
            }
        }
        
        // Log which database we're using for transparency
        $this->addToAssertionCount(1); // Prevent risky test warnings
    }
}