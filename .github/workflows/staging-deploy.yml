name: Deploy to Staging

on:
  push:
    branches: [ staging ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Deploy to Staging Server
      run: |
        echo "Starting Staging Deployment..."
        
        # Create SSH directory and set permissions
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        
        # Decode and save the EC2 private key
        echo "${{ secrets.STAGING_SSH_PRIVATE_KEY }}" | base64 -d > ~/.ssh/ec2-key
        chmod 600 ~/.ssh/ec2-key
        
        # Add staging EC2 to known hosts
        ssh-keyscan -H ec2-54-84-11-94.compute-1.amazonaws.com >> ~/.ssh/known_hosts
        
        # SSH into the server and deploy
        ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2-key <EMAIL> "bash -s" <<EOF
        set -e
        
        echo "Changing to project directory"
        cd /var/www/successionplanai
        
        echo "Setting remote origin"
        git remote set-url origin https://${{ secrets.GH_PAT }}@github.com/SuccessionplanAI/successionplan-ai.git
        
        echo "Fixing permissions before git operations"
        sudo chown -R ubuntu:www-data /var/www/successionplanai/storage
        sudo chmod -R 775 /var/www/successionplanai/storage
        sudo chmod -R 775 /var/www/successionplanai/bootstrap/cache
        
        echo "Fetching and resetting to remote branch"
        git fetch origin
        git reset --hard origin/staging
        # Clean but exclude the virtual environment directory
        git clean -fd -e venv/
        
        echo "Fixing permissions after git operations"
        sudo chown -R ubuntu:www-data /var/www/successionplanai/storage
        sudo chmod -R 775 /var/www/successionplanai/storage
        
        echo "Creating and setting permissions for shared directory"
        sudo mkdir -p /var/www/successionplanai/shared
        sudo chown -R ubuntu:www-data /var/www/successionplanai/shared
        sudo chmod -R 775 /var/www/successionplanai/shared
        
        echo "Running Composer"
        composer install --no-interaction --prefer-dist --no-dev
        
        echo "Installing NPM packages"
        npm install --legacy-peer-deps
        
        echo "Running inertiajs"
        npm install @inertiajs/inertia
        
        echo "Putting application in maintenance mode"
        php artisan down || echo "Application was not running"

        echo "Running migrations"
        php artisan migrate --force
        
        echo "Install sweetalert2"
        npm install sweetalert2
        
        echo "Install react-window"
        npm install react-window
        
        echo "Building assets"
        npm run build
        
        echo "Running fast-excel Composer"
        composer require rap2hpoutre/fast-excel
        
        echo "Running PHPWord Composer"
        composer require phpoffice/phpword
        
        echo "Final permission check for shared directories"
        ls -la /var/www/successionplanai/shared/
        sudo chown -R www-data:www-data /var/www/successionplanai/shared
        sudo chmod -R 777 /var/www/successionplanai/shared
        echo "Shared directory permissions updated to ensure PHP and Python can both access"

        echo "Setting up Supervisor for queue management"
        sudo bash /var/www/successionplanai/deployment/scripts/setup-supervisor.sh

        echo "Terminating Horizon gracefully before restart"
        php artisan horizon:terminate || echo "Horizon was not running"

        echo "Restarting queue workers"
        php artisan queue:restart

        echo "Starting Horizon"
        sudo supervisorctl start laravel-horizon:* || echo "Horizon supervisor not configured yet"

        echo "Clearing application cache"
        php artisan cache:clear
        php artisan config:clear
        php artisan route:clear
        php artisan view:clear

        echo "Restarting PHP-FPM"
        sudo service php8.3-fpm restart || sudo service php8.2-fpm restart || sudo service php8.1-fpm restart || echo "Could not restart PHP-FPM"

        echo "Bringing application back online"
        php artisan up

        echo "Deployment completed successfully!"
        EOF
