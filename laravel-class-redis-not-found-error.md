# Laravel: Class "Redis" Not Found Error - What To Do?

**Date:** August 08, 2023  
**Reading Time:** 1 min, 178 words

When you set up a new Laravel application to use Redis as a cache driver or broadcast driver, you may encounter the following error:

```
Class "Redis" not found
```

---

## Why Does This Error Occur?

The reason for this error is usually either a missing Composer package or a missing Redis installation. In both cases, it appears once you try to use Redis in your system for the following:

- Cache driver by setting `CACHE_DRIVER=redis`
- Broadcast driver by setting `BROADCAST_DRIVER=redis`

Before going into the solutions, make sure you need Redis in your system. If you don't use it, you should change your drivers to something else.

---

## Missing Composer Package

This is the most common reason to have this error. To fix it, run the following command:

```bash
composer require predis/predis
```

And once the package is installed, you should be able to use Redis as a cache or broadcast driver.

---

## Missing Redis Installation

If you have installed the package, but the error is still there, you may need the Redis installation. To install Redis, follow the instructions on the [official Redis website](https://redis.io/docs/getting-started/installation/).

---

## User Comments

### Important Configuration Note (porrapat - 1 year ago)
Just noted, if you do all of that above and still see the error.

Maybe, you have to add this too your Laravel .env configuration.

```bash
REDIS_CLIENT=predis
```

After that it will work.

**Community Response:** 👍 5 😍 1

### Additional Responses:
- **IRESIS** (1 year ago): Thanks!
- **Sjoerd24** (7 months ago): Thanks! This also fixed my problem!

### PHP Extension Solution (duaneandrea - 2 weeks ago)
```bash
sudo apt-get install php8.x-redis
sudo service apache2 restart
```

and you are done  
**N.B: - 8.x for your version**
