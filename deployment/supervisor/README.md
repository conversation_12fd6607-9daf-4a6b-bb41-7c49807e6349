# Supervisor Configuration for Laravel Queues

This directory contains supervisor configuration files for managing Laravel queue workers in production.

## Files

- `laravel-horizon.conf` - Configuration for Laravel Horizon (Redis queue management)
- `laravel-worker.conf` - Configuration for basic Laravel queue workers (fallback/database queues)

## Installation

### Ubuntu/Debian

1. Install Supervisor:
```bash
sudo apt-get install supervisor
```

2. Copy configuration files:
```bash
sudo cp deployment/supervisor/*.conf /etc/supervisor/conf.d/
```

3. Update supervisor configuration:
```bash
sudo supervisorctl reread
sudo supervisorctl update
```

4. Start the processes:
```bash
sudo supervisorctl start laravel-horizon:*
sudo supervisorctl start laravel-worker:*
```

### Fedora/RHEL

1. Install Supervisor:
```bash
sudo dnf install supervisor
```

2. Copy configuration files (note the .ini extension):
```bash
sudo cp deployment/supervisor/laravel-horizon.conf /etc/supervisord.d/laravel-horizon.ini
sudo cp deployment/supervisor/laravel-worker.conf /etc/supervisord.d/laravel-worker.ini
```

3. Update supervisor configuration:
```bash
sudo supervisorctl reread
sudo supervisorctl update
```

## Management Commands

### Check Status
```bash
sudo supervisorctl status
```

### Start/Stop/Restart
```bash
sudo supervisorctl start laravel-horizon:*
sudo supervisorctl stop laravel-horizon:*
sudo supervisorctl restart laravel-horizon:*

sudo supervisorctl start laravel-worker:*
sudo supervisorctl stop laravel-worker:*
sudo supervisorctl restart laravel-worker:*
```

### View Logs
```bash
sudo tail -f /var/log/supervisor/laravel-horizon.log
sudo tail -f /var/log/supervisor/laravel-worker.log
```

## Configuration Notes

- **laravel-horizon.conf**: Manages Redis queues through Horizon. Only 1 process needed as Horizon manages workers internally.
- **laravel-worker.conf**: Fallback for database queues or when Horizon is not used. Configured for 8 processes.
- **stopwaitsecs**: Set to 3600 seconds to allow long-running jobs to complete before force-killing.
- **user**: Set to 'ubuntu' - adjust based on your server setup.
- **directory**: Set to '/var/www/successionplanai' - adjust based on your deployment path.

## Deployment Integration

These configurations should be deployed and activated as part of your deployment process. See the updated GitHub Actions workflow for automated deployment.

## Troubleshooting

1. **Permission Issues**: Ensure the user specified in the config has proper permissions to the Laravel directory.
2. **Log Files**: Check supervisor logs if processes fail to start.
3. **PHP Path**: Ensure PHP is in the system PATH or specify full path in command.
4. **Environment**: Ensure .env file is properly configured for the production environment.
