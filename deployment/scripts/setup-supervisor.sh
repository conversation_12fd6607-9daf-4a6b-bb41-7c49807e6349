#!/bin/bash

# Setup Supervisor for Laravel Queues
# This script should be run with sudo privileges

set -e

echo "🔧 Setting up Supervisor for Laravel Queues..."

# Check if supervisor is installed
if ! command -v supervisorctl &> /dev/null; then
    echo "❌ Supervisor is not installed. Installing..."
    
    # Detect OS and install supervisor
    if [[ -f /etc/debian_version ]]; then
        # Ubuntu/Debian
        apt-get update
        apt-get install -y supervisor
    elif [[ -f /etc/redhat-release ]]; then
        # RHEL/Fedora/CentOS
        dnf install -y supervisor || yum install -y supervisor
    else
        echo "❌ Unsupported operating system"
        exit 1
    fi
fi

# Ensure supervisor is enabled and started
echo "🚀 Enabling and starting supervisor service..."
systemctl enable supervisor
systemctl start supervisor

# Create log directory if it doesn't exist
mkdir -p /var/log/supervisor

# Detect OS for configuration path
if [[ -f /etc/debian_version ]]; then
    # Ubuntu/Debian - uses .conf files
    CONF_DIR="/etc/supervisor/conf.d"
    echo "📁 Using Ubuntu/Debian configuration directory: $CONF_DIR"
    
    # Copy configuration files
    cp /var/www/successionplanai/deployment/supervisor/laravel-horizon.conf $CONF_DIR/
    cp /var/www/successionplanai/deployment/supervisor/laravel-worker.conf $CONF_DIR/
    
elif [[ -f /etc/redhat-release ]]; then
    # RHEL/Fedora/CentOS - uses .ini files
    CONF_DIR="/etc/supervisord.d"
    echo "📁 Using RHEL/Fedora configuration directory: $CONF_DIR"
    
    # Copy and rename configuration files to .ini
    cp /var/www/successionplanai/deployment/supervisor/laravel-horizon.conf $CONF_DIR/laravel-horizon.ini
    cp /var/www/successionplanai/deployment/supervisor/laravel-worker.conf $CONF_DIR/laravel-worker.ini
fi

# Set proper permissions
chmod 644 $CONF_DIR/laravel-*

echo "📖 Reading new supervisor configurations..."
supervisorctl reread

echo "🔄 Updating supervisor with new configurations..."
supervisorctl update

echo "✅ Supervisor setup completed!"
echo ""
echo "📊 Current supervisor status:"
supervisorctl status

echo ""
echo "🎯 Next steps:"
echo "1. Check that processes are running: sudo supervisorctl status"
echo "2. View logs: sudo tail -f /var/log/supervisor/laravel-horizon.log"
echo "3. Restart if needed: sudo supervisorctl restart laravel-horizon:*"
echo ""
echo "📚 For more information, see: deployment/supervisor/README.md"
