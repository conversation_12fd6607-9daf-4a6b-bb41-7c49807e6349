<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class RightToRectificationEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $sendersName;
    public $title;
    public $emailContent;

    public function __construct($sendersName, $title, $emailContent)
    {
        $this->sendersName = $sendersName;
        $this->title = $title;
        $this->emailContent = $emailContent;
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->title, 
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.right_to_rectification',
            with:[
                'sendersName' => $this->sendersName,
                'emailContent'  => $this->emailContent,
               
            ]
        );
    }

    public function attachments(): array
    {
        return [];
    }
	
	
}
