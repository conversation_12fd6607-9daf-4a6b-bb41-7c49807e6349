<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class TwoFactorCode extends Mailable
{
    use Queueable, SerializesModels;
    public $code;
    public $email;


    /**
     * Create a new message instance.
     */
    public function __construct($code, $email)
    {
        $this->code = $code;
        $this->email = $email;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Two Factor Code',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $user = User::where(['email' => $this->email])->first();
        return new Content(
            view: 'mail.twoFactorCode',
            with:[
                'code' => $this->code,
                'user' => $user
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
