<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SendToRelationshipManager extends Mailable
{
    use Queueable, SerializesModels;

    public $sendersName;
    public $emailMessage;
    public $requirements;
    public $plandescription;
    public $company_name;
    public $planID;
    public $title;

    public function __construct($emailMessage, $sendersName,$requirements,$plandescription,$company_name,$planID)
    {   
        $this->sendersName = $sendersName;
        $this->emailMessage = $emailMessage;
        $this->requirements = $requirements;
        $this->plandescription = $plandescription;
        $this->company_name = $company_name;
        $this->planID = $planID;
        $this->title = 'Request Research';
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject:  $this->title, 
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'mail.sendtorelationshipmanager',
            with:[
                'title' =>  $this->title,
                'sendersName' => $this->sendersName,
                'emailMessage'  => $this->emailMessage,
                'requirements'  => $this->requirements,
                'plandescription'  => $this->plandescription,
                'company_name'  => $this->company_name,
                'planID'  => $this->planID,
            ]
        );
    }

    public function attachments(): array
    {
        return [];
    }
	
	
}
