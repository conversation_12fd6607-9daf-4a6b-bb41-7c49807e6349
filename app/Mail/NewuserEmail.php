<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewuserEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $sendersname;
 
    public $inviteUrl;
    public $role;

    public function __construct($inviteUrl, $sendersname, $role)
    {
        $this->inviteUrl = $inviteUrl;
        $this->sendersname = $sendersname;
        $this->role = $role;

    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Welcome to The Succession Plan', 
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'mail.newUser',
            with:[
                'inviteUrl' => $this->inviteUrl,
                'role'      => $this->role,
               
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
