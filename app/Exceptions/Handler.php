<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Services\ErrorHandling\ErrorLogger;
use App\Services\ErrorHandling\UserErrorHandler;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            // Use the new ErrorLogger to log all reportable exceptions
            ErrorLogger::logException($e);
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function render($request, Throwable $exception)
    {
        // Handle various exception types with appropriate user messages
        if ($exception instanceof TokenMismatchException) {
            UserErrorHandler::showError(
                'Your session has expired. Please try again.', 
                UserErrorHandler::TYPE_AUTHENTICATION,
                [['label' => 'Try Again', 'action' => 'window.location.reload();']]
            );
            return redirect()->back()->withInput();
        }
        
        if ($exception instanceof ValidationException) {
            // Laravel already handles validation exceptions well, just log them
            ErrorLogger::logException($exception, [], ErrorLogger::CATEGORY_UI, 'warning');
            return parent::render($request, $exception);
        }
        
        if ($exception instanceof AuthenticationException) {
            UserErrorHandler::handleException(
                $exception,
                'You need to log in to access this page.',
                UserErrorHandler::TYPE_AUTHENTICATION
            );
        }
        
        if ($exception instanceof AuthorizationException) {
            UserErrorHandler::handleException(
                $exception,
                'You don\'t have permission to access this resource.',
                UserErrorHandler::TYPE_AUTHORIZATION
            );
        }
        
        if ($exception instanceof ModelNotFoundException || $exception instanceof NotFoundHttpException) {
            UserErrorHandler::handleException(
                $exception,
                'The requested resource could not be found.',
                UserErrorHandler::TYPE_NOT_FOUND
            );
        }

        // Let the parent handler deal with other exception types
        return parent::render($request, $exception);
    }
}
