<?php

namespace App\Exports;

use App\Models\CareerHistories;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CareerHistoriesExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */

    public function __construct()
    {
        $this->columnsArr = [
            'id',
            'people_id',
            'role',
            'past_company_id',
            'start_date',
            'end_date',
            'tenure'
        ];
    }

    public function collection()
    {
        return CareerHistories::select($this->columnsArr)->get();
    }

    public function headings(): array
    {
        return $this->columnsArr;
    }
}
