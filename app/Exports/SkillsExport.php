<?php

namespace App\Exports;

use App\Models\Skills;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithBatchInserts;

class SkillsExport implements FromQuery, WithHeadings, WithChunkReading, WithBatchInserts
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public $columnsArr = [];

    public function __construct()
    {
        $this->columnsArr = [
            'id',
            'people_id',
            'skill_name',
            'skill_type'
        ];
    }

    public function query()
    {
        return Skills::select($this->columnsArr);
    }

    public function headings(): array
    {
        return $this->columnsArr;
    }

    public function chunkSize(): int
    {
        return 10000; // Customize the chunk size as needed
    }

    public function batchSize(): int
    {
        return 10000; // Customize the batch size for insert operations
    }
}
