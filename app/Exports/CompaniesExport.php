<?php

namespace App\Exports;

use App\Models\Company;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CompaniesExport implements FromCollection, WithHeadings
{

    public function __construct()
    {
        $this->columnsArr = [
            'id',
            'name',
            'other_name',
            'name_abbreviation',
            'description',
            'corporate_hq_country',
            'corporate_hq_address',
            'website',
            'corporate_hq_phone_number',
            'chair',
            'ceo',
            'type',
            'industry',
            'sector',
            'stock_symbol',
            'Annual_Revenue', // Keep or remove based on your requirement
            'Annual_Net_Profit_Margin', // Keep or remove based on your requirement
            'Annual_Net_Expenses', // Keep or remove based on your requirement
            'Annual_YOY_Revenue_Change', // Keep or remove based on your requirement
            'company_employee_count',
            'image',
            'status',
            'parent_company',
            'annual_expenses', // Check for typos in your database column names
            'Currency',
            'Earnings_Before_Interest_Taxes'
        ];
    }

    public function collection()
    {

        return Company::select($this->columnsArr)->get();
    }

    public function headings(): array
    {
        return $this->columnsArr;
    }
}

