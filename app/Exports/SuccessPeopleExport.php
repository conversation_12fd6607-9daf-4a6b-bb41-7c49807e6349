<?php

namespace App\Exports;

use App\Models\People;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use App\Models\SuccessPeople;
use App\Models\SuccessionPlan;
use App\Models\pipeline;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class SuccessPeopleExport implements FromCollection, WithHeadings, WithColumnWidths, ShouldAutoSize
{
    /**
    * @return \Illuminate\Support\Collection
    */

    public function __construct($filterArr)
    {
        $this->filterArr = $filterArr;
        $this->columnsArr = [
            'Name',
            'Country',
            'City',
            'Company',
            'Function',
            'Potential Sex',
            'LinkedIn Url',
            'Role',
            'Start Date',
            'End Date',
            'Tenure',
            'Readiness',
            'Regisration Status',
            'Division',
            'Exco',
            'Career History',
            'Educational History',
            'Skills',
            'Languages',
            'Summary'
        ];

        $this->dbColumnsArr = [
            DB::raw("CONCAT(pipelines.first_name, ' ', pipelines.last_name) AS name"),
            'pipelines.country',
            'pipelines.city',
            'pipelines.company_name',
            'pipelines.function',
            'pipelines.gender',
            'pipelines.linkedinURL',
            'pipelines.latest_role',
            'pipelines.start_date',
            'pipelines.end_date',
            'pipelines.tenure',
            'pipelines.readiness',
            'pipelines.other_tags',
            'pipelines.division',
            'pipelines.exco',
            'pipelines.career_history',
            'pipelines.educational_history',
            'pipelines.skills',
            'pipelines.languages',
            'pipelines.summary'
        ];
    }

    public function collection()
    {
        $successionPlan = SuccessionPlan::where('id', $this->filterArr['planId'])->first();
        $successPeopleIds = SuccessPeople::where('plan_id', $this->filterArr['planId'])->pluck('people_id')->toArray();

        $refinedDBColumnArr = $this->dbColumnsArr;
        if($successionPlan && $successionPlan->ethnicity == 1){
            array_splice($refinedDBColumnArr, 5, 0, 'pipelines.diverse');
        }

        $pipelinePeopleQuery = pipeline::select($refinedDBColumnArr)
                                    // ->leftJoin('people', 'pipelines.people_id', '=', 'people.id')
                                    ->where('plan_id', $this->filterArr['planId'])
                                    ->whereNotIn('people_id', $successPeopleIds);

        if ($this->filterArr['sortBy'] && $this->filterArr['sortDirection']) {
            if($this->filterArr['sortBy'] == 'skills_match'){
                $pipelinePeopleQuery = $pipelinePeopleQuery->orderBy($this->filterArr['sortBy'], $this->filterArr['sortDirection'])->orderBy('id', 'desc');
            }
            else {
                $pipelinePeopleQuery = $pipelinePeopleQuery->orderBy($this->filterArr['sortBy'], $this->filterArr['sortDirection']);
            }
        }

        if ($this->filterArr['searchByKeyword']) {
            $searchKeywords = explode(",", $this->filterArr['searchByKeyword']);
            $pipelinePeopleQuery = $pipelinePeopleQuery->where(function ($query) use ($searchKeywords) {
                foreach ($searchKeywords as $searchword) {
                    $query->orWhere(function ($subQuery) use ($searchword) {
                        $subQuery->orWhere('first_name', 'like', '%' . $searchword . '%')
                            ->orWhere('middle_name', 'like', '%' . $searchword . '%')
                            ->orWhere('last_name', 'like', '%' . $searchword . '%')
                            ->orWhere('latest_role', 'like', '%' . $searchword . '%')
                            ->orWhere('company_name', 'like', '%' . $searchword . '%')
                            ->orWhere('function', 'like', '%' . $searchword . '%')
                            ->orWhere('division', 'like', '%' . $searchword . '%')
                            ->orWhere('country', 'like', '%' . $searchword . '%')
                            ->orWhere('summary', 'like', '%' . $searchword . '%')
                            ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                    });
                }
            });
        }
        else if ($this->filterArr['search']) {
            if ($this->filterArr['forename']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('first_name', 'like', '%' . $this->filterArr['forename'] . '%');
            }
            if ($this->filterArr['surname']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('last_name', 'like', '%' . $this->filterArr['surname'] . '%');
            }
            if ($this->filterArr['gender']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('gender', '=', $this->filterArr['gender']);
            }
            if ($this->filterArr['_role']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('latest_role', 'like', '%' . $this->filterArr['_role'] . '%');
            }
            if ($this->filterArr['company']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('company_name', 'like', '%' . $this->filterArr['company'] . '%');
            }
            if ($this->filterArr['function']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('function', 'like', '%' . $this->filterArr['function'] . '%');
            }
            if ($this->filterArr['division']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('division', 'like', '%' . $this->filterArr['division'] . '%');
            }
            if ($this->filterArr['location']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('location', 'like', '%' . $this->filterArr['location'] . '%');
            }
            if ($this->filterArr['regBodies']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('other_tags', 'like', '%' . $this->filterArr['regBodies'] . '%');
            }
            if ($this->filterArr['moverFilter']) {
                if($this->filterArr['moverFilter'] == 'Mover'){
                    $pipelinePeopleQuery = $pipelinePeopleQuery->where('mover', 'Mover');
                }
                
                if($this->filterArr['moverFilter'] == 'Non Mover'){
                    $pipelinePeopleQuery = $pipelinePeopleQuery->where(function($query) {
                                                        $query->whereNull('mover')
                                                            ->orWhereIn('mover', ['', 'Non Mover']);
                                                    });
                }
            }
            if ($this->filterArr['min_exp']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('tenure', '>=', $this->filterArr['min_exp']);
            }
        }

        $pipelinePeople = $pipelinePeopleQuery->get();
        return $pipelinePeople;
    }

    public function headings(): array
    {
        $successionPlan = SuccessionPlan::where('id', $this->filterArr['planId'])->first();
        $refinedColumnArr = $this->columnsArr;
        if($successionPlan && $successionPlan->ethnicity == 1){
            array_splice($refinedColumnArr, 5, 0, 'Potential Diversity');
        }
        return $refinedColumnArr;
    }

    // public function styles(Worksheet $sheet)
    // {
    //     return [
    //         // Enable text wrapping for large text in the summary column
    //         'A' => ['alignment' => ['wrapText' => true]],
    //     ];
    // }

    public function columnWidths(): array
    {
        return [
            'T' => 1000, // Set width to handle large text
        ];
    }
}
