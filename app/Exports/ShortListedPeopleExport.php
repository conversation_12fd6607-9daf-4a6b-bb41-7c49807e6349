<?php

namespace App\Exports;

use App\Models\People;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use App\Models\JobPeople;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;


class ShortListedPeopleExport implements FromCollection, WithHeadings, WithColumnWidths, ShouldAutoSize
{
    /**
    * @return \Illuminate\Support\Collection
    */

    public function __construct($filterArr)
    {

        $this->filterArr = $filterArr;
        $this->columnsArr = [
            'Name',
            'Country',
            'City',
            'Company',
            'Function',
            'Potential Diversity',
            'Potential Sex',
            'LinkedIn Url',
            'Role',
            'Start Date',
            'End Date',
            'Tenure',
            'Readiness',
            'Regisration Status',
            'Division',
            'Career History',
            'Educational History',
            'Skills',
            'Languages',
            'Notes',
            'Summary'
        ];

        $this->dbColumnsArr = [
            DB::raw("CONCAT(first_name, ' ', last_name) AS name"),
            'country',
            'city',
            'company_name',
            'function',
            'diverse',
            'gender',
            'linkedinURL',
            'latest_role',
            'start_date',
            'end_date',
            'tenure',
            'readiness',
            'other_tags',
            'division',
            'career_history',
            'educational_history',
            'skills',
            'languages',
            'notes',
            'summary'
        ];
    }

    public function collection()
    {
        $jobPeopleQuery = JobPeople::select($this->dbColumnsArr)
                                    ->where('job_people.job_id', $this->filterArr['jobId']);

        if ($this->filterArr['sortBy'] && $this->filterArr['sortDirection']) {
            if($this->filterArr['sortBy'] == 'skills_match'){
                $jobPeopleQuery = $jobPeopleQuery->orderBy($this->filterArr['sortBy'], $this->filterArr['sortDirection'])->orderBy('id', 'desc');
            }
            else {
                $jobPeopleQuery = $jobPeopleQuery->orderBy($this->filterArr['sortBy'], $this->filterArr['sortDirection']);
            }
        }

        if ($this->filterArr['searchByKeyword']) {
            $searchKeywords = explode(",", $this->filterArr['searchByKeyword']);
            $jobPeopleQuery = $jobPeopleQuery->where(function ($query) use ($searchKeywords) {
                foreach ($searchKeywords as $searchword) {
                    $query->orWhere(function ($subQuery) use ($searchword) {
                        $subQuery->orWhere('first_name', 'like', '%' . $searchword . '%')
                            ->orWhere('middle_name', 'like', '%' . $searchword . '%')
                            ->orWhere('last_name', 'like', '%' . $searchword . '%')
                            ->orWhere('latest_role', 'like', '%' . $searchword . '%')
                            ->orWhere('company_name', 'like', '%' . $searchword . '%')
                            ->orWhere('function', 'like', '%' . $searchword . '%')
                            ->orWhere('division', 'like', '%' . $searchword . '%')
                            ->orWhere('location', 'like', '%' . $searchword . '%')
                            ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                    });
                }
            });
        }
        else if ($this->filterArr['search']) {
            if ($this->filterArr['forename']) {
                $jobPeopleQuery = $jobPeopleQuery->where('first_name', 'like', '%' . $this->filterArr['forename'] . '%');
            }
            if ($this->filterArr['surname']) {
                $jobPeopleQuery = $jobPeopleQuery->where('last_name', 'like', '%' . $this->filterArr['surname'] . '%');
            }
            if ($this->filterArr['gender']) {
                $jobPeopleQuery = $jobPeopleQuery->where('gender', '=', $this->filterArr['gender']);
            }
            if ($this->filterArr['role']) {
                $jobPeopleQuery = $jobPeopleQuery->where('latest_role', 'like', '%' . $this->filterArr['role'] . '%');
            }
            if ($this->filterArr['company']) {
                $jobPeopleQuery = $jobPeopleQuery->where('company_name', 'like', '%' . $this->filterArr['company'] . '%');
            }
            if ($this->filterArr['function']) {
                $jobPeopleQuery = $jobPeopleQuery->where('function', 'like', '%' . $this->filterArr['function'] . '%');
            }
            if ($this->filterArr['division']) {
                $jobPeopleQuery = $jobPeopleQuery->where('division', 'like', '%' . $this->filterArr['division'] . '%');
            }
            if ($this->filterArr['slocation']) {
                $jobPeopleQuery = $jobPeopleQuery->where('location', 'like', '%' . $this->filterArr['slocation'] . '%');
            }
            if ($this->filterArr['regBodies']) {
                $jobPeopleQuery = $jobPeopleQuery->where('other_tags', 'like', '%' . $this->filterArr['regBodies'] . '%');
            }
            if ($this->filterArr['moverFilter']) {
                if($this->filterArr['moverFilter'] == 'Mover'){
                    $jobPeopleQuery = $jobPeopleQuery->where('mover', 'Mover');
                }
                
                if($this->filterArr['moverFilter'] == 'Non Mover'){
                    $jobPeopleQuery = $jobPeopleQuery->where(function($query) {
                                                        $query->whereNull('mover')
                                                            ->orWhereIn('mover', ['', 'Non Mover']);
                                                    });
                }
            }
            if ($this->filterArr['min_exp']) {
                $jobPeopleQuery = $jobPeopleQuery->where('tenure', '>=', $this->filterArr['min_exp']);
            }
        }

        $jobPeople = $jobPeopleQuery->get();

        return $jobPeople;
    }

    public function headings(): array
    {
        return $this->columnsArr;
    }

    // public function styles(Worksheet $sheet)
    // {
    //     return [
    //         // Enable text wrapping for large text in the summary column
    //         'A' => ['alignment' => ['wrapText' => true]],
    //     ];
    // }

    public function columnWidths(): array
    {
        return [
            'U' => 1000, // Set width to handle large text
        ];
    }
}
