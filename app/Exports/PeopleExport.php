<?php

namespace App\Exports;

use App\Models\People;
use Illuminate\Support\Facades\DB;
use Rap2hpoutre\FastExcel\FastExcel;
use Illuminate\Database\Eloquent\Builder;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Facades\Log;

class PeopleExport
{
    protected $columnsArr;
    protected $filters = [];

    public function __construct()
    {
        /*$this->columnsArr = [
            'id', 'forename', 'surname', 'middle_name', 'other_name', 'gender', 'diverse',
            'country', 'city', 'linkedinURL', 'parent_role', 'latest_role', 'exco',
            'company_id', 'company_name', 'start_date', 'end_date', 'tenure', 'function',
            'division', 'seniority', 'educational_history', 'career_history', 'skills',
            'languages', 'other_tags', 'status', 'readiness', 'user_id', 'summary',
            'created_at', 'updated_at'
        ];*/
        $this->columnsArr = [
            'id', 'linkedinURL'
        ];
    }

    public function setFilters(array $filters)
    {
        $this->filters = $filters;
        return $this;
    }

    public function download(): StreamedResponse
    {
        $filename = 'people_export_' . date('Y-m-d_His') . '.xlsx';
       
        return response()->stream(
            function () {
                try {
                    $generator = function () {
                        $query = $this->getOptimizedQuery();
                        $count = 0;
                        foreach ($query->cursor() as $record) {
                            $count++;
                            yield $this->formatRecord($record);
                        }
                        //Log::info("Total records fetched: $count");
                    };
                   
                    (new FastExcel($generator()))->export('php://output');
                } catch (\Exception $e) {
                    //Log::error("Error during export: " . $e->getMessage());
                    throw $e;
                }
            },
            200,
            [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]
        );
    }

    protected function getOptimizedQuery(): Builder
    {
        $query = People::select($this->columnsArr)
            ->when(DB::connection()->getDriverName() === 'mysql', function ($query) {
                return $query->fromRaw('people FORCE INDEX (PRIMARY)');
            })
            ->orderBy('id')
            ->withoutGlobalScopes();

        foreach ($this->filters as $column => $value) {
            if (is_array($value)) {
                $query->whereIn($column, $value);
            } else {
                $query->where($column, $value);
            }
        }

        return $query;
    }

    protected function formatRecord($record): array
    {
        $data = $record->toArray();

        /*$data['created_at'] = $data['created_at'] ? date('Y-m-d H:i:s', strtotime($data['created_at'])) : null;
        $data['updated_at'] = $data['updated_at'] ? date('Y-m-d H:i:s', strtotime($data['updated_at'])) : null;
        $data['start_date'] = $data['start_date'] ? date('Y-m-d', strtotime($data['start_date'])) : null;
        $data['end_date'] = $data['end_date'] ? date('Y-m-d', strtotime($data['end_date'])) : null;

        $jsonColumns = ['educational_history', 'career_history', 'skills', 'languages', 'other_tags'];
        foreach ($jsonColumns as $column) {
            if (isset($data[$column]) && is_string($data[$column])) {
                $data[$column] = json_decode($data[$column], true);
            }
            $data[$column] = is_array($data[$column]) ? implode(', ', $data[$column]) : $data[$column];
        }
*/
        return $data;
    }
}