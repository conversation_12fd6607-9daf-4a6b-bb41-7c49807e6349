<?php

namespace App\Exports;

use App\Models\People;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use App\Models\JobPeople;
use App\Models\pipeline;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class LongListedPeopleExport implements FromCollection, WithHeadings, WithColumnWidths, ShouldAutoSize
{
    /**
    * @return \Illuminate\Support\Collection
    */

    public function __construct($filterArr)
    {
        $this->filterArr = $filterArr;
        $this->columnsArr = [
            'Name',
            'Country',
            'City',
            'Company',
            'Function',
            'Potential Diversity',
            'Potential Sex',
            'LinkedIn Url',
            'Role',
            'Start Date',
            'End Date',
            'Tenure',
            'Readiness',
            'Regisration Status',
            'Division',
            'Career History',
            'Educational History',
            'Skills',
            'Languages',
            'Summary'
        ];

        $this->dbColumnsArr = [
            DB::raw("CONCAT(first_name, ' ', last_name) AS name"),
            'country',
            'city',
            'company_name',
            'function',
            'diverse',
            'gender',
            'linkedinURL',
            'latest_role',
            'start_date',
            'end_date',
            'tenure',
            'readiness',
            'other_tags',
            'division',
            'career_history',
            'educational_history',
            'skills',
            'languages',
            'summary'
        ];
    }

    public function collection()
    {
        $jobPeopleIds = JobPeople::where('job_id', $this->filterArr['jobId'])->pluck('people_id')->toArray();

        $pipelinePeopleQuery = pipeline::select($this->dbColumnsArr)
                                    ->where('job_id', $this->filterArr['jobId'])
                                    ->whereNotIn('people_id', $jobPeopleIds);

        if ($this->filterArr['sortBy'] && $this->filterArr['sortDirection']) {
            if($this->filterArr['sortBy'] == 'skills_match'){
                $pipelinePeopleQuery = $pipelinePeopleQuery->orderBy($this->filterArr['sortBy'], $this->filterArr['sortDirection'])->orderBy('id', 'desc');
            }
            else {
                $pipelinePeopleQuery = $pipelinePeopleQuery->orderBy($this->filterArr['sortBy'], $this->filterArr['sortDirection']);
            }
        }

        if ($this->filterArr['searchByKeyword']) {
            $searchKeywords = explode(",", $this->filterArr['searchByKeyword']);
            $pipelinePeopleQuery = $pipelinePeopleQuery->where(function ($query) use ($searchKeywords) {
                foreach ($searchKeywords as $searchword) {
                    $query->orWhere(function ($subQuery) use ($searchword) {
                        $subQuery->orWhere('first_name', 'like', '%' . $searchword . '%')
                            ->orWhere('middle_name', 'like', '%' . $searchword . '%')
                            ->orWhere('last_name', 'like', '%' . $searchword . '%')
                            ->orWhere('latest_role', 'like', '%' . $searchword . '%')
                            ->orWhere('company_name', 'like', '%' . $searchword . '%')
                            ->orWhere('function', 'like', '%' . $searchword . '%')
                            ->orWhere('division', 'like', '%' . $searchword . '%')
                            ->orWhere('location', 'like', '%' . $searchword . '%')
                            ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                    });
                }
            });
        }
        else if ($this->filterArr['search']) {
            if ($this->filterArr['forename']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('first_name', 'like', '%' . $this->filterArr['forename'] . '%');
            }
            if ($this->filterArr['surname']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('last_name', 'like', '%' . $this->filterArr['surname'] . '%');
            }
            if ($this->filterArr['gender']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('gender', '=', $this->filterArr['gender']);
            }
            if ($this->filterArr['role']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('latest_role', 'like', '%' . $this->filterArr['role'] . '%');
            }
            if ($this->filterArr['company']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('company_name', 'like', '%' . $this->filterArr['company'] . '%');
            }
            if ($this->filterArr['function']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('function', 'like', '%' . $this->filterArr['function'] . '%');
            }
            if ($this->filterArr['division']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('division', 'like', '%' . $this->filterArr['division'] . '%');
            }
            if ($this->filterArr['location']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('location', 'like', '%' . $this->filterArr['location'] . '%');
            }
            if ($this->filterArr['regBodies']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('other_tags', 'like', '%' . $this->filterArr['regBodies'] . '%');
            }
            if ($this->filterArr['moverFilter']) {
                if($this->filterArr['moverFilter'] == 'Mover'){
                    $pipelinePeopleQuery = $pipelinePeopleQuery->where('mover', 'Mover');
                }
                
                if($this->filterArr['moverFilter'] == 'Non Mover'){
                    $pipelinePeopleQuery = $pipelinePeopleQuery->where(function($query) {
                                                        $query->whereNull('mover')
                                                            ->orWhereIn('mover', ['', 'Non Mover']);
                                                    });
                }
            }
            if ($this->filterArr['min_exp']) {
                $pipelinePeopleQuery = $pipelinePeopleQuery->where('tenure', '>=', $this->filterArr['min_exp']);
            }
        }
                                    
        $pipelinePeople = $pipelinePeopleQuery->get();

        return $pipelinePeople;
    }

    public function headings(): array
    {
        return $this->columnsArr;
    }

    // public function styles(Worksheet $sheet)
    // {
    //     return [
    //         // Enable text wrapping for large text in the summary column
    //         'A' => ['alignment' => ['wrapText' => true]],
    //     ];
    // }

    public function columnWidths(): array
    {
        return [
            'T' => 1000, // Set width to handle large text
        ];
    }
}
