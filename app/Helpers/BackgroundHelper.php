<?php
namespace App\Helpers;

use App\Jobs\GenericBackgroundJob;
use App\Models\BackgroundProcess;

class BackgroundHelper
{
    public static function runInBackground($class, $method, $parameters = [])
    {
        // Store process in DB
        $process = BackgroundProcess::create([
            'class_name' => $class,
            'method_name' => $method,
            'parameters' => json_encode($parameters),
            'status' => 'pending'
        ]);

        // Dispatch job
        GenericBackgroundJob::dispatch($class, $method, $parameters, $process->id);

        return $process->id;
    }
}

