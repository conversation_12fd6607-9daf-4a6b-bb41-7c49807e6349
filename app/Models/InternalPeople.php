<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InternalPeople extends Model
{
    use HasFactory;
    
    protected $fillable = [
    'employee_id',
    'forename',
    'surname',
    'gender',
    'location',
    'summary',
    'role_id',
    'reports_to',
    'company_id',
    'company_name',
    'start_date',
    'tenure',
    'tenure_in_company',
    'readiness',
    'function',
    'division',
    'user_id',
    'latest_role',
    'diverse',
    'city',
    'country',
    'linkedinURL',
    'division',
    'status',
    'other_tags',
    'flight_risk',
    'is_relocatable',
    ];

    public function user()
    {   
        return $this->belongsTo(user::class);
    }

    public function role()
    {   
        return $this->belongsTo(Role::class);
    }

    public function internalSkills()
    {
        return $this->hasMany(InternalSkills::class,'internal_people','id');
    }

    public function careerHistory()
    {
        return $this->hasMany(internal_career_histories::class,'people_id','id')->orderBy('start_date', 'desc');;
    }

    public function notes()
    {
        return $this->hasMany(UserNotes::class,'entity_id','id')->where('entity_type', 'internal_peoples')->orderBy('id', 'desc');
    }

    public function plan()
    {
        return $this->hasOne(SuccessionPlan::class,'tagged_individual','id');
    }
}
