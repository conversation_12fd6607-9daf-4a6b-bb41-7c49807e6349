<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobPeople extends Model
{
    protected $fillable = [
        'pipeline_id',
        'job_id',
        'user_id',
        'people_id',
        'headline',
        'first_name',
        'last_name',
        'middle_name',
        'other_name',
        'gender',
        'diverse',
        'location',
        'linkedinURL',
        'latest_role',
        'company_id',
        'company_name',
        'start_date',
        'end_date',
        'tenure',
        'function',
        'division',
        'seniority',
        'career_history',
        'educational_history',
        'skills',
        'languages',
        'skills_match',
        'education_match',
        'location_match',
        'role_match',
        'gender_match',
        'tenure_match',
        'total_score',
        'notes',
        'readiness',
        'other_tags',
        'city',
        'country',
        'summary',
        'status'
    ];
    use HasFactory;
}
