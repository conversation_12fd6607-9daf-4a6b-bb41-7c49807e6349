<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\PlanScores;
use Illuminate\Support\Facades\DB;

class Recruitment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = ['user_id','recruitment_name', 'viewer_shared_with', 'editor_shared_with'];
	
	public function user()
    {
        return $this->belongsTo(User::class);
    }
	public function stages(){
		return $this->hasMany(RecruitmentStage::class, 'recruitment_project_id');
		// return $this->hasManyThrough(
			// RecruitmentStage::class, 
			// Recruitment::class,      
			// 'id',                     
			// 'recruitment_project_id', 
			// 'id',                     
			// 'id'                     
		// );
	}
	
	public function recruitments(){
		return $this->hasMany(Recruitment::class, 'id');
	}
	
	public function recruitmentPipeline(){
		return $this->hasMany(RecruitmentPipeline::class, 'recruitment_project_id');
	}
	
	public function deleteWithRelations(){
		$this->stages()->delete();
		$this->recruitmentPipeline()->delete();
		return parent::delete();
	}
	
	public function GetPlanScore(array $planId)
	{
		$recruitment_project_ids = array_keys($planId);
	 
		// Query the database and fetch the required data
		$results = DB::table('RecruitmentPipeline as r')
			->join('success_people as s', 'r.Candidate_ID', '=', 's.people_id')
			->select(
				'r.recruitment_project_id',
				DB::raw("SUM(CASE WHEN s.gender = 'Male' THEN 1 ELSE 0 END) / COUNT(r.Candidate_ID) * 100 as Male_Ratio"),
				DB::raw("SUM(CASE WHEN s.gender = 'Female' THEN 1 ELSE 0 END) / COUNT(r.Candidate_ID) * 100 as Female_Ratio")
			)
			->whereIn('r.recruitment_project_id', $recruitment_project_ids)
			->groupBy('r.recruitment_project_id')
			->get();
	 
		// Transform the results into the desired structure
		$transformedResults = $results->mapWithKeys(function ($item) {
			return [
				$item->recruitment_project_id => [
					'Female-ratio' => round($item->Female_Ratio, 2),
					'Male-Ratio' => round($item->Male_Ratio, 2),
				],
			];
		});
	 
	 
	 
		return $transformedResults;
	}


}
