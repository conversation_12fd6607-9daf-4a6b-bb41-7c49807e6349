<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TemporaryOrganisationChild extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $table = "temporary_organisation_childs";

    /**
     * Get the parent organization.
     */
    public function parent()
    {
        return $this->belongsTo(TemporaryOrganisationChild::class, 'parent_id');
    }

    /**
     * Get the child organizations.
     */
    public function children()
    {
        return $this->hasMany(TemporaryOrganisationChild::class, 'parent_id');
    }

    /**
     * Get the internal people associated with the organization.
     */
    public function internalPeople()
    {
        return $this->belongsTo(InternalPeople::class, 'internal_people_id');
    }

    /**
     * Recursive relationship to get all ancestors.
     */
    public function ancestors()
    {
        return $this->parent()->with('ancestors');
    }

    /**
     * Recursive relationship to get all descendants.
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }
}

