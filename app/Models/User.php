<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Mail\ResetPasswordMail; // Import the Mailable class
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;



class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable,SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'team',
        'role',
        'email',
        'password',
        'image_url',
        'company_id',
        'account_id',
        'profile_pic',
        'last_activity'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the entity's notifications.
     * Override to use custom table name since 'notifications' table is used for something else
     */
    public function notifications()
    {
        return $this->morphMany(\App\Models\DatabaseNotification::class, 'notifiable')
                    ->orderBy('created_at', 'desc');
    }

    public function sendPasswordResetNotification($token)
    {
        $resetLink = route('password.reset', ['token' => $token]);
        Mail::to($this->email)->send(new ResetPasswordMail($resetLink));
    }

    public static function isAdminUser() {
        $user = auth()->user();
    
        // Check if user is authenticated and has a 'team' attribute
        if (!$user || !isset($user->role)) {
            return false;
        }
    
        // Check if the user's team is 'Admin'
        return strtolower($user->role) === "admin";
    }

    public static function isMasterUser() {
        $user = auth()->user();
    
        // Check if user is authenticated and has a 'team' attribute
        if (!$user || !isset($user->role)) {
            return false;
        }
    
        // Check if the user's team is 'Master'
        return strtolower($user->role) === "master";

    }
    
    // Define the relationship
    public function people()
    {
        return $this->hasMany(People::class); // Assuming the Person model is in the same namespace
    }
}
