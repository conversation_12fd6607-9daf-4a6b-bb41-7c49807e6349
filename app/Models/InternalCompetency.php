<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InternalCompetency extends Model
{
    use HasFactory;

    protected $table = 'internal_competencies'; // Specify the table name

    protected $fillable = [
        'organisation_people_id',
        'competency_id',
        'score',
    ];

    /**
     * Get the organisation person associated with the competency.
     */
    public function organisationPerson()
    {
        return $this->belongsTo(OrganisationPeople::class, 'organisation_people_id');
    }

    /**
     * Get the competency associated with this record.
     */
    public function competency()
    {
        return $this->belongsTo(Competency::class, 'competency_id');
    }
}
