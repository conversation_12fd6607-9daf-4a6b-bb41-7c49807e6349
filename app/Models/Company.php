<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'other_name', 'parent_company', 'name_abbreviation','description', 'stock_symbol', 'corporate_hq_country', 'corporate_hq_address', 'corporate_hq_phone_number','website', 'Annual_Revenue', 'Annual_Net_Profit_Margin', 'Annual_Net_Expenses','Annual_YOY_Revenue_Change', 'status','industry','sector','image','annual_expenses','Currency','type','ceo','chair','company_employee_count','Earnings_Before_Interest_Taxes'];

    public function location()
    {
        return $this->belongsTo(location::class);
    }
}
