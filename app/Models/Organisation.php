<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Organisation extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    public function organisationPeople()
    {
        return $this->hasMany(OrganisationPeople::class,'organisation_id');
    }
}
