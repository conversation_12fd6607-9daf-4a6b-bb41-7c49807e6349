<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class People extends Model
{
    use HasFactory;
    protected $fillable = [
        'id',
        'forename',
        'surname',
        'country',
        'city',
        'middle_name',
        'other_name',
        'gender',
        'diverse',
        'location',
        'summary',
        'linkedinURL',
        'latest_role',
        'company_id',
        'company_name',
        'start_date',
        'end_date',
        'tenure',
        'function',
        'division',
        'seniority',
        'career_history',
        'educational_history',
        'skills',
        'languages',
        'other_tags',
        'status',
        'user_id',
        'readiness',
        'notes',
        'exco'
    ];

    public function hasMatchingEducation($requirement)
    {
        $educationLevels = explode(',', $this->education);
        $educationLevels = array_map('trim', $educationLevels);
        
        return in_array($requirement, $educationLevels);
    }

    public function careerHistories()
    {
        return $this->hasMany(CareerHistories::class, 'people_id', 'id');
    }

    // Define the relationship
    public function user()
    {
        return $this->belongsTo(User::class); // Assuming the User model is in the same namespace
    }
}
