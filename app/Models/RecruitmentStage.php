<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RecruitmentStage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = ['user_id','recruitment_project_id', 'stage_name', 'stage_number'];
	
	public function user()
    {
        return $this->belongsTo(User::class);
    }
	public function recruitment(){
		return $this->belongsTo(Recruitment::class, 'recruitment_project_id');
	}

}
