<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CareerHistories extends Model
{
    protected $fillable = ['people_id','role','past_company_id','start_date','end_date','tenure'];
    use HasFactory;
    
    public function people()
    {
        return $this->belongsTo(People::class, 'people_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'past_company_id', 'id');
    }
}
