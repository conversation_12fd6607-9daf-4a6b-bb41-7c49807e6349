<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InternalSkills extends Model
{
    use HasFactory;

    protected $fillable = [
        'internal_people',
        'company_id',
        'skill_name',
        'skill_type',
        'skill_rating',
    ];

    public function internal_people()
    {
        return $this->belongsTo(InternalPeople::class);
    }
}
