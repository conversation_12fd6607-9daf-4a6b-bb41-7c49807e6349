<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Account extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = ['type', 'company_id','Account_name','account_type', 'users_limit','active_users','relationship_manager_id','industry_interest','sector_interest','company_of_interest'];

    public function relationManager()
    {
        // Assuming $this->relationship_manager_id is a string like "1,2,3"
        $user_ids = explode(',', $this->relationship_manager_id);
    
        // Fetch users where id is in $user_ids array
        $users = User::whereIn('id', $user_ids)->pluck('name')->toArray();
        
    
        return  implode(', ',$users);
    }
    
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }
}
