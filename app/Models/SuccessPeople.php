<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SuccessPeople extends Model
{
    use HasFactory;

    protected $fillable = [
        'pipeline_id',
        'plan_id',
        'user_id',
        'people_id',
        'headline',
        'first_name',
        'last_name',
        'middle_name',
        'other_name',
        'gender',
        'diverse',
        'location',
        'summary',
        'linkedinURL',
        'latest_role',
        'company_id',
        'company_name',
        'start_date',
        'end_date',
        'tenure',
        'function',
        'division',
        'seniority',
        'exco',
        'career_history',
        'educational_history',
        'skills',
        'languages',
        'skills_match',
        'education_match',
        'location_match',
        'role_match',
        'gender_match',
        'tenure_match',
        'total_score',
        'type',
        'notes',
        'recruit',
        'status',
        'other_tags',
        'readiness',
        'city',
        'country',
        'mover'
    ];

    public function location()
    {
        return $this->belongsTo(location::class);
    }

    public function company()
    {
        return $this->belongsTo(company::class);
    }

    public function user()
    {   
        return $this->belongsTo(user::class);
    }

    public function SuccessionPlan()
    {
        return $this->belongsTo(SuccessionPlan::class);
    }
}
