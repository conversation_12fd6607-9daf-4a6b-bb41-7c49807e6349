<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Competency extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'competencies'; // Explicitly defining the table name (optional)

    protected $fillable = [
        'company_id',
        'org_id',
        'label',
        'type',
        'score',
        'created_by',
    ];

    /**
     * Define relationship with Company
     */
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    /**
     * Define relationship with User (creator)
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Define relationship with Organization
     */
    public function organization()
    {
        return $this->belongsTo(Organisation::class, 'id');
    }
}
