<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrganisationPeople extends Model
{
    use HasFactory;
    protected $table = "organisation_peoples";
    protected $guarded = [];

    public function internalPeople()
    {
        return $this->belongsTo(InternalPeople::class, 'internal_people_id');
    }

    public static function internalPeopleGenderCount($organisationId)
    {
        $peopleIds = self::where('organisation_id', $organisationId)
            ->pluck('internal_people_id')
            ->toArray();

        // Make sure the array is not empty to avoid an unnecessary query
        if (empty($peopleIds)) {
            return ['Female' => 0, 'Male' => 0];
        }

        $peoples = InternalPeople::whereIn('id', $peopleIds)
            ->selectRaw('gender, COUNT(*) as total')
            ->groupBy('gender')
            ->get()
            ->pluck('total', 'gender')
            ->toArray();

        return $peoples;
    }

    public function parent()
    {
        return $this->belongsTo(OrganisationPeople::class, 'parent_id');
    }

    /**
     * Get the child organizations.
     */
    public function children()
    {
        return $this->hasMany(OrganisationPeople::class, 'parent_id');
    }
    public function internalRequirementData()
    {
        return $this->hasMany(InternalRequirement::class, 'organisation_people_id');
    }

    public function ancestors()
    {
        return $this->parent()->with('ancestors');
    }

    /**
     * Recursive relationship to get all descendants.
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }
}
