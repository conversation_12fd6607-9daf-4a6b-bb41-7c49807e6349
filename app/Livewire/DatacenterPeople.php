<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\People;
use App\Models\Location;
use App\Models\Company;
use App\Models\CareerHistories;
use App\Models\Skills;
use App\Models\pipeline;
use App\Models\SuccessPeople;
use App\Models\JobPeople;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\LOG;
use Carbon\Carbon;
use Livewire\WithPagination;


class DatacenterPeople extends Component
{
    use WithPagination;
    const STATUS_SUBMITTED = "Submitted";
    const STATUS_REPORTED = "Reported";
    public $perPage =   50; // Number of items per page
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $search = false;
    public $searchByKeyword = '';
    public $searchterm;
    public $personRoles = [];
    public $functionsValue = [];
    public $divisions = [];
    public $locations = [];
    public $companies = [];
    public $selectedFunctionsValue;
    public $selectedCompany;
    public $companiesListToSearch = [];


    public $totalPeople;
    public $totalCompanies;
    public $divisionLabels = [];

    public $divisionData = [];
    public $companyLabels = [];
    public $companyData = [];
    public $genderLabels = [];
    public $genderData = [];
    public $locationLabels = [];
    public $locationData = [];

    // Variables for filters
    public $forename;
    public $surname;
    public $gender;
    public $role = [];
    public $previousRole;
    public $company = [];
    public $function = [];
    public $division = [];
    public $location = [];
    public $regBodies;
    public $tenure;
    public $min_exp =   null;
    public $selectedIndividualID;
    public $selectedIndividual;
    public $selectedIndividualSkills;
    public $selectedIndividualCareerHistory = [];
    public $problem;
    public $editPeoplePopup = false;
    public $editPeopleCareerHistoryPopup = false;
    public $editPeopleSkillsPopup = false;
    public $editIndividualID;
    public $editForename;
    public $editSurname;
    public $editGender;
    public $editDiverse;
    public $editCountry;
    public $editCity;
    public $editLinkedIn;
    public $editLatestRole;
    public $editExco;
    public $editCompany;
    public $editStartDate;
    public $editTenure;
    public $editFunction;
    public $editDivision;
    public $editOtherTags;
    public $editStatus;
    public $editReadiness;
    public $editSummary;
    public $selectedCompanies = [];
    public $editPeopleCareerHistory = [];
    public $editPeopleSkills = [];



    protected $listeners = [
        'deletePeople'
    ];

    public function mount()
    {

        $this->totalPeople = People::whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])->count();
        $this->totalCompanies = People::whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->distinct('company_id')
            ->count('company_id');

        $divisionCountsData = DB::table('people')
            ->select('exco', DB::raw('COUNT(*) as total_people'))
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->groupBy('exco')
            ->get();

        foreach ($divisionCountsData as $data) {
            $this->divisionLabels[] = $data->exco;
            $this->divisionData[] = $data->total_people;
        }

        $CompanyCountsData = DB::table('people')
            ->select('company_name', DB::raw('COUNT(*) as total_people'))
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->groupBy('company_name')
            ->orderBy('total_people', 'desc')
            ->take(5)
            ->get();

        foreach ($CompanyCountsData as $data) {
            $this->companyLabels[] = $data->company_name;
            $this->companyData[] = $data->total_people;
        }


        //---------------------------- Gender Split -------------------------------//

        $peopleCountsData = DB::table('people')
            ->select('gender', DB::raw('COUNT(*) as total_people'))
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->groupBy('gender')
            ->get();

        foreach ($peopleCountsData as $data) {
            // $this->genderLabels[] = $data->gender;
            // $this->genderData[] = $data->total_people;

            $colorCode = "#F9C21A";
            if ($data->gender == "Male") {
                $colorCode = "#0891B2";
            } else if ($data->gender == "Female") {
                $colorCode = "#8B5CF6";
            }
            $this->genderLabels[] = $data->gender;
            $this->genderData[] = [
                "x" => $data->gender,
                "y" => $data->total_people,
                "fillColor" => $colorCode
            ];
        }

        //--------------------------- Location Split -------------------------------//
        $LocationCountsData = DB::table('people')
            ->select('country', DB::raw('COUNT(*) as total_people'))
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->groupBy('country')
            ->orderBy('total_people', 'desc')
            ->take(5)
            ->get();

        //dd($LocationCountsData);

        foreach ($LocationCountsData as $data) {
            $this->locationLabels[] = $data->country;
            $this->locationData[] = $data->total_people;
        }

        $this->locations = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $companiesArr = array_unique(People::whereNotNull('company_id')->pluck('company_id')->toArray());

        $this->companies = Company::where('id', '!=', auth()->user()->company_id)->whereIn('id', $companiesArr)->get()->map(function ($company) {
            return [
                'value' => $company->name,
                'label' => $company->name,
            ];
        })->toArray();
        
        $companiesArrToSearch = People::whereNotNull('company_id')
                                        ->whereNotNull('company_name')
                                        ->where('company_name', '!=', "")
                                        ->distinct()
                                        ->orderBy('company_name', 'ASC')
                                        ->pluck('company_name', 'company_id')
                                        ->toArray();
        foreach($companiesArrToSearch as $companyId => $companyName){
            $this->companiesListToSearch[] = ['value' => $companyId, 'label' => $companyName];
        }

        // $this->functionsValue = DB::table('pipelines')->distinct()
        //     ->pluck('function')
        //     ->map(function ($function) {
        //         return [
        //             'value' => $function,
        //             'label' => $function,
        //         ];
        //     })->toArray();

        $this->functionsValue = pipeline::whereNotNull('function')
                                        ->where('function', '!=', "")
                                        ->distinct()
                                        ->orderBy('function', 'ASC')
                                        ->pluck('function')
                                        ->map(function ($function) {
                                            return [
                                                'value' => $function,
                                                'label' => $function,
                                            ];
                                        })
                                        ->toArray();
    }
    public function render()
    {
        $peoples = People::where('status', '!=', 'Submitted')
            ->where('status', '!=', 'Reported');

        if ($this->sortBy && $this->sortDirection) {
            $peoples = $peoples->orderBy($this->sortBy, $this->sortDirection);
        }

        if ($this->search) {
            if ($this->forename) {
                $peoples = $peoples->where('forename', 'like', '%' . $this->forename . '%');
            }
            if ($this->surname) {
                $peoples = $peoples->where('surname', 'like', '%' . $this->surname . '%');
            }
            if ($this->gender) {
                $peoples = $peoples->where('gender', '=', $this->gender);
            }
            if ($this->role) {
                $peoples = $peoples->where('latest_role', 'like', '%' . $this->role . '%');
            }
            if ($this->previousRole) {
                $peoples = $peoples->where('parent_role', 'like', '%' . $this->previousRole . '%');
            }
            if ($this->selectedCompany) {
                $peoples = $peoples->where('company_id', $this->selectedCompany);
            }
            if ($this->selectedFunctionsValue) {
                $peoples = $peoples->where('function', 'like', '%' . $this->selectedFunctionsValue . '%');
            }
            if ($this->division) {
                $peoples = $peoples->where('division', 'like', '%' . $this->division . '%');
            }
            if ($this->location) {
                $peoples = $peoples->where('country', 'like', '%' . $this->location . '%');
            }
            if ($this->regBodies) {
                $peoples = $peoples->where('other_tags', 'like', '%' . $this->regBodies . '%');
            }
            if ($this->tenure) {
                $peoples = $peoples->where('tenure', 'like', '%' . $this->tenure . '%');
            }
        }

        if (!empty($this->searchByKeyword)) {
            $searchKeywords = explode(",", $this->searchByKeyword);
            $peoples = $peoples->where(function ($query) use ($searchKeywords) {
                foreach ($searchKeywords as $searchword) {
                    $query->orWhere(function ($subQuery) use ($searchword) {
                        $subQuery->orWhere('forename', 'like', '%' . $searchword . '%')
                            ->orWhere('surname', 'like', '%' . $searchword . '%')
                            ->orWhere('latest_role', 'like', '%' . $searchword . '%')
                            ->orWhere('company_name', 'like', '%' . $searchword . '%')
                            ->orWhere('function', 'like', '%' . $searchword . '%')
                            ->orWhere('division', 'like', '%' . $searchword . '%')
                            ->orWhere('country', 'like', '%' . $searchword . '%')
                            ->orWhere('summary', 'like', '%' . $searchword . '%')
                            ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                    });
                }
            });
        }
        $peoples = $peoples->paginate($this->perPage);

        return view('livewire.datacenterPeople.index', compact('peoples'));
    }

    public function runfilters()
    {
        $this->search = true;
        $this->resetPage();
    }
    public function searchRecordByKeyword()
    {
        $this->resetPage();
    }

    public function viewIndividual($id)
    {

        $this->selectedIndividualID = $id;
        $this->selectedIndividual = People::where('id', $id)->first();
        $this->selectedIndividualSkills = Skills::where('people_id', '=', $id)->get();

        // Get the career histories of the individual
        $peoplescareer = CareerHistories::where('people_id', $id)
            ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
            ->select('career_histories.*', 'companies.name as company_name')
            ->get();

        $this->selectedIndividualCareerHistory = $peoplescareer;
    }

    public function editIndividual($id)
    {
        $people = People::where('id', $id)->first();
        $this->editPeopleCareerHistory = CareerHistories::with('company')->where(['people_id' => $people->id])->whereHas('company')->orderBy('start_date', "DESC")->get()->toArray();
        $this->editPeopleSkills = Skills::where(['people_id' => $people->id])->get()->toArray();
        $this->editIndividualID = $people->id;
        $this->editForename = $people->forename;
        $this->editSurname = $people->surname;
        $this->editGender = $people->gender;
        $this->editDiverse = $people->diverse;
        $this->editCountry = $people->country;
        $this->editCity = $people->city;
        $this->editLinkedIn = $people->linkedinURL;
        $this->editLatestRole = $people->latest_role;
        $this->editExco = $people->exco;
        $this->editCompany = $people->company_name;
        $this->editStartDate = $people->start_date;
        $this->editTenure = $people->tenure;
        $this->editFunction = $people->function;
        $this->editDivision = $people->division;
        $this->editOtherTags = $people->other_tags;
        $this->editStatus = $people->status;
        $this->editReadiness = $people->readiness;
        $this->editSummary = $people->summary;

        $this->editPeoplePopup = true;
    }

    public function updateIndividual()
    {


        if ($this->editIndividualID) {
            $company = Company::where('name', $this->editCompany)->first();

            if (empty($company)) {
                $company = Company::create([
                    'name'        =>  $this->editCompany,
                    'location_id' =>  1784674685,
                    'status'      => 'submited'
                ]);
            }

            $people = People::find($this->editIndividualID);
            if($company->name != $people->company_name || $this->editLatestRole != $people->latest_role) {
                $editFields = [
                    'forename'      => $this->editForename,
                    'surname'       => $this->editSurname,
                    'gender'        => $this->editGender,
                    'diverse'       => $this->editDiverse,
                    'country'       => $this->editCountry,
                    'city'          => $this->editCity,
                    'linkedinURL'   => $this->editLinkedIn,
                    'latest_role'   => $this->editLatestRole,
                    'exco'          => $this->editExco,
                    'company_id'    => $company->id,
                    'company_name'  => $company->name,
                    'start_date'    => $this->editStartDate,
                    'tenure'        => $this->editTenure,
                    'function'      => $this->editFunction,
                    'division'      => $this->editDivision,
                    'other_tags'    => $this->editOtherTags,
                    'status'        => "Mover",
                    'readiness'     => $this->editReadiness,
                    'summary'       => $this->editSummary,
                ];
            }
            else{
                $editFields = [
                    'forename'      => $this->editForename,
                    'surname'       => $this->editSurname,
                    'gender'        => $this->editGender,
                    'diverse'       => $this->editDiverse,
                    'country'       => $this->editCountry,
                    'city'          => $this->editCity,
                    'linkedinURL'   => $this->editLinkedIn,
                    'latest_role'   => $this->editLatestRole,
                    'exco'          => $this->editExco,
                    'company_id'    => $company->id,
                    'company_name'  => $company->name,
                    'start_date'    => $this->editStartDate,
                    'tenure'        => $this->editTenure,
                    'function'      => $this->editFunction,
                    'division'      => $this->editDivision,
                    'other_tags'    => $this->editOtherTags,
                    'status'        => $this->editStatus,
                    'readiness'     => $this->editReadiness,
                    'summary'       => $this->editSummary,
                ];
            };
            $people->update($editFields);
            $this->dispatch('toast', 'info', "People updated successfully");
            $this->editPeoplePopup = false;
        }
    }

    public function reportPerson($id)
    {
        $user = auth()->user();
        $reportPerson = People::findorfail($id);
        $reportPerson->update([
            'status' => 'Reported',
            'user_id' => $user->id
        ]);

        $this->reset(['selectedIndividualID', 'selectedIndividual', 'selectedIndividualSkills', 'selectedIndividualCareerHistory', 'problem']);
    }

    public function removeCareerHistoryRow($index)
    {
        // Remove the specific row
        unset($this->editPeopleCareerHistory[$index]);

        // Reindex the array to prevent issues with the subsequent rows
        $this->editPeopleCareerHistory = array_values($this->editPeopleCareerHistory);
    }

    public function addCareerHistoryRow()
    {
        $this->editPeopleCareerHistory[] = [
            'role' => '',
            'company' => [
                "name" => ''
            ],
            'start_date' => '',
            'end_date' => '',
        ];
    }

    public function addSkillsRow() {
        $this->editPeopleSkills[] = [
            'skill_name' => ''
        ];

    }

    public function removeSkillsRow($index)
    {
        // Remove the specific row
        unset($this->editPeopleSkills[$index]);

        // Reindex the array to prevent issues with the subsequent rows
        $this->editPeopleSkills = array_values($this->editPeopleSkills);
    }

    public function updateSkills() {
        Skills::where(['people_id' => $this->editIndividualID])->delete();

        foreach ($this->editPeopleSkills as $index => $skill) {
            Skills::create([
                'skill_name' => $skill['skill_name'],
                'skill_type' => 'Professional',
                'people_id' => $this->editIndividualID
            ]);
        }
        $this->editPeopleSkillsPopup = false;
        $this->dispatch('toast', 'info', 'Skill updated successfully.');
    }
    

    public function updateCareerHistory()
    {
        // Delete existing career histories for the individual
        CareerHistories::where(['people_id' => $this->editIndividualID])->delete();
    
        foreach ($this->editPeopleCareerHistory as $index => $history) {

            // Find or create the company
            $company = Company::where('name', trim($history['company']['name']))->first();
    
            if (empty($company)) {
                $company = Company::create([
                    'name' => $history['company']['name'],
                    'location_id' => 1784674685,
                    'status' => 'submited',
                ]);
            }
    
            // Create new career history entry
            $careerHistory = new CareerHistories;
            $careerHistory->people_id = $this->editIndividualID;
            $careerHistory->role = $history['role'];
            $careerHistory->past_company_id = $company->id;
            $careerHistory->start_date = $history['start_date'];
            $careerHistory->end_date = !empty($history['end_date']) ? $history['end_date'] : Null;
    
            // Calculate Tenure
            if (!empty($history['start_date'])) {
                $startDate = Carbon::parse($history['start_date']);
                if (!empty($history['end_date'])) {
                    // End date is present
                    $endDate = Carbon::parse($history['end_date']);
                    $tenure = $startDate->diffInYears($endDate);
                } else {
                    // End date is empty, use current date
                    $endDate = Carbon::now();
                    $tenure = $startDate->diffInYears($endDate);
                }
                $careerHistory->tenure = $tenure;
            }
    
            $careerHistory->save();
        }
        $this->editPeopleCareerHistoryPopup = false;
        $this->dispatch('toast', 'info', 'Career history updated successfully.');
    }

    public function deletePeople($peopleId) {
        CareerHistories::where(['people_id' => $peopleId])->delete();
        Skills::where(['people_id' => $peopleId])->delete();
        pipeline::where(['people_id' => $peopleId])->delete();
        SuccessPeople::where(['people_id' => $peopleId])->delete();
        JobPeople::where(['people_id' => $peopleId])->delete();
        People::where(['id' => $peopleId])->delete();

        $this->dispatch('toast', 'info', "People deleted successfully!");


    }
    
}
