<?php

namespace App\Livewire;
use App\Mail\DeactivateAccount;
use App\Models\Account;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use App\Mail\NewuserEmail;
use Illuminate\Support\Facades\URL;
use App\Models\Invite;
use Illuminate\Support\Facades\Log;
use Livewire\WithPagination;


use Livewire\Component;

class TeamAdmin extends Component
{
    use WithPagination;
    // The form variables that will be used
    public $showinviteForm = false;
    public $showteamForm = false;
    public $email ='';

    public $AccountDetails;
    public $uopen = false;

    protected $listeners = [
        'removeUser',
    ];


    public function mount() {
        $user = auth()->user();
        //dd($user);

        $this->AccountDetails = Account::where('id',$user->account_id)->first();
        //dd($this->AccountDetails);
    }

    public function render()
    {
        $user = auth()->user();
        $licenses = User::where('id','!=',$user->id)->where('company_id',$user->company_id)->paginate(10);
        
        return view('livewire.team-admin',compact('licenses'));
    }

    public function removeUser($id)
    {
        $deletingUser = User::findOrFail($id);

        $updateAccount = Account::where('id',$this->AccountDetails->id)->first();
        $updateAccount->update([
            'active_users' => $updateAccount->active_users - 1,
        ]);
        
        $deletingUser->delete();
        $this->dispatch('toast', 'info', "User deleted successfully!");

    }

    //Functions to trigger the form containers
    public function ToggleInviteForm()
    {
        $this->showinviteForm = !$this->showinviteForm;
    }

    public function ToggleNewTeamForm()
    {
        $this->showteamForm = !$this->showteamForm;
    }

    //Functions to close the forms
    public function CloseInviteForm()
    {
        $this->reset('showinviteForm');
    }

    public function sendInvite(){

        $this->validate([
            'email' => 'required|unique:users,email,NULL,id,deleted_at,NULL',
        ]);
        $user = auth()->user();
        $id = $user->account_id;
        $company = $user->company_id;
        $role = "user";
        $email = $this->email;
        $sendersname = $user->name;
        
        $inviteUrl = URL::temporarySignedRoute(
            'invite.sendInvite', now()->addDays(2), ['id' => $id, 'email' => $email, 'company' => $company, 'role' => $role]
        );

        $invite = Invite::create([
            'email' => $email,
            'user_id' => $id,
            'company_id' => $company,
            'url' => $inviteUrl
        ]);

        Mail::to($this->email)->send(new NewuserEmail($inviteUrl, $sendersname, $role));

        $this->reset(['email']);

        $this->dispatch('toast', 'info', "Invitation link has been sent to email");
        $this->uopen = false;

    
    }

    public function sendInviteViewer(){

        $this->validate([
            'email' => 'required|unique:users,email,NULL,id,deleted_at,NULL',
        ]);
        $user = auth()->user();
        $id = $user->account_id;
        $role = "Viewer";
        $company = $user->company_id;
        $email = $this->email;
        $sendersname = $user->name;
        
        $inviteUrl = URL::temporarySignedRoute(
            'invite.sendInvite', now()->addDays(2), ['id' => $id, 'email' => $email, 'company' => $company, 'role' => $role]
        );

        $invite = Invite::create([
            'email' => $email,
            'user_id' => $id,
            'company_id' => $company,
            'url' => $inviteUrl
        ]);

        //dd($inviteUrl);

        Mail::to($this->email)->send(new NewuserEmail($inviteUrl, $sendersname, $role));

        $this->reset(['email']);

        $this->dispatch('toast', 'info', "Invitation link has been sent to email");
        $this->uopen = false;

    
    }

    public function sendDeactivate(){
        $user = auth()->user();
        $account = Account::where('id',$this->AccountDetails->id)->pluck('Account_name');

        Mail::to('<EMAIL>')->send(new DeactivateAccount($account));
    }


}
