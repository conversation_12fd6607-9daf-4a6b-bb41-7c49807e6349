<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;





class ResetPassword extends Component
{ 
    public $token = "68912e54ed19716b125f208bcffbfe38126e4bf60c45843b5cfc26e225ff0bd6?email=dhimans273%40gmail.com";

    public $email = "<EMAIL>";
    public $password;
    public $password_confirmation;

    protected $rules = [
        'password' => 'required|string|min:8',
        'password_confirmation' => 'required|string|same:password',
    ];
    public function mount()
    {
        $this->token = Request::query('token');
    }

    public function resetPassword()
    { 
        $this->validate();

        $status = Password::reset(
            [
                'email' => $this->email,
                'password' => $this->password,
                'password_confirmation' => $this->password_confirmation,
                'token' => $this->token,
            ],
            function ($user, $password) {
                $user->password = Hash::make($password);
                $user->save();
            }
        );

        if ($status == Password::PASSWORD_RESET) {
            session()->flash('message', 'Password reset successfully.');
        } else {
            session()->flash('error', "Password reset failed: $status.");
        }
    }


    public function render()
    {
        return view('livewire.reset-password');
    }
}
