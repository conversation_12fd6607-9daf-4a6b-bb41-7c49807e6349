<?php

namespace App\Livewire;

use App\Imports\PeopleImport;
use Livewire\Component;
use Livewire\WithPagination;
use OpenAI\Laravel\Facades\OpenAI;
use App\Models\Job;
use App\Models\Job_Scores;
use App\Models\Company;
use App\Models\JobPeople;
use App\Models\JobRequirement;
use App\Models\Skills;
use App\Models\CareerHistories;
use App\Models\Location;
use App\Models\Role;
use App\Models\InternalPeople;
use App\Models\SuccessPeople;
use App\Models\SuccessionPlan;
use App\Models\People;
use App\Models\pipeline;
use App\Models\notifications;
use App\Models\Account;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

class JobexternalPeople extends Component
{
    use WithFileUploads;
    use WithPagination;
    const STATUS_SUBMITTED = "Submitted";
    const STATUS_REPORTED = "Reported";

    protected $listeners = [
        'clearFilters',
        'clearSelectedIndividuals',
        'addPersonModalClosed',
        'uploadCandidatesModalClosed'
    ];

    public $job;
    public $openPlanPopup=false;
    public $openTalentPopup=false;
    public $disableDispatchforMultipleSelect=false;
    public $selectedPerson = [];
    public $jobdetails;
    public $pipeline;
    public $pipelineid;
    public $yourCompany;
    public $selectedIndividualID;
    public $selectedIndividual;
    public $selectedIndividualSkills;
    public $selectedIndividualCareerHistory = [];
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $search = false;
    public $searchterm;

    //Function tester
    public $tester;

    // Variables for filters
    public $forename;
    public $surname;
    public $gender;
    public $role;
    public $company;
    public $selectedCompany;
    public $function;
    public $division;
    public $slocation;
    public $regBodies;
    public $tenure;

    //Variables for Visualisation
    public $TotalPeople;
    public $TotalCompanies;
    public $genderLabels = [];
    public $genderData = [];
    public $totalScoreLabels = [];
    public $totalScoreData = [];
    public $companyLabels = [];
    public $companyData = [];
    public $locationLabels = [];
    public $locationData = [];
    public $functionLabels = [];
    public $functionData = [];
    public $divisionLabels = [];
    public $divisionData = [];
    public $roleLabels = [];
    public $roleData = [];
    public $selectedFunctionValue;

    //Variables for Advanced Search


    //Variables for Add Person Form
    public $Suggestion = [];
    public $countries;
    public $addForename = '';
    public $addSurname = '';
    public $addMiddlename = '';
    public $addOtherName = '';
    public $addRole = '';
    public $addStartDate;
    public $addCompany = '';
    public $addFunction = '';
    public $addDivision = '';

    public $addLocation = '';

    public $addGender = 'Not Applicable';
    public $addLinkedinURL = '';
    public $companies = [];
    public $companiesListToSearch = [];
    public $selectedCompanies   = [];
    public $errormessage;

    //Variables for adding users to job with a button
    public $SuccessSkills;
    public $SuccessRoles;
    public $SuccessStepup;
    public $SuccessLocation;
    public $SuccessGender;
    public $step = 1;
    public $perPage =   50; // Number of items per page

    public $locations = [];
    public $personRoles = [];
    public $functionsValue = [];
    public $problem;
    public $reportDescription;
    public $searchByKeyword = '';
    public $previousRole;
    public $selectedRole = [];
    public $location = [];
    //Variables for bulk uploads
    public $errormessage1;
    public $errormessage2;
    public $errormessage3;
    public $csvFile;
    public $csvSkills;
    public $uploadPopup = false;
    public $duplicateUploadPopup = false;
    public $addPersonPopup = false;
    public $user;
    public $min_exp = 0;
    public $isPeopleAlreadyExists = [];

    public $copySelectedCompaniesValue;
    public $peopleAreadyExistsPopup = false;
    public $existingPeopleField;
    public $duplicateRecords = [];
    public $selectedRecords = [];
    public $userJobs = [];
    public $vopen = false;
    public $userPlans = false;
    public $addToTalentPoolPopup = false;
    public $talentPoolsList = [];
    public $addToTalentPoolArray = [];
    public $addToPlanPopup = false;
    public $plansList = [];
    public $addToPlansArray = [];



    public function mount()
    {

        $user = auth()->user();

        // Code to get the companies of interest for a specific account
        $accountObj = Account::where('id', $user->account_id)->first();

        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        if($accountObj){
            if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }

        $companyIds = [];
        if($interestedCompanies && count($interestedCompanies) > 0){
            $companyIds = $interestedCompanies;
        }
        else if($interestedIndustries && count($interestedIndustries) > 0){
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if($interestedSectors && count($interestedSectors) > 0){
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
        }


        $this->jobdetails = Job::where('id', $this->job)->first();
        $this->user = $user;
        $companiesArr = array_unique(People::whereNotNull('company_id')->pluck('company_id')->toArray());
        
        
        $this->companies = Company::where('id', '!=', $this->user->company_id)
                                    ->whereIn('id', $companiesArr)->get()->map(function ($company) {
            return [
                'value' => $company->name,
                'label' => $company->name,
            ];
        })->toArray();
        

        $companiesArrToSearch = People::whereNotNull('company_id')
                                        ->whereNotNull('company_name')
                                        ->where('company_id', '!=', $this->user->company_id)
                                        ->when(!empty($companyIds),function($query) use($companyIds){
                                            $query->whereIn('company_id', $companyIds);
                                        })
                                        ->where('company_name', '!=', "")
                                        ->distinct()
                                        ->orderBy('company_name', 'ASC')
                                        ->pluck('company_name', 'company_id')
                                        ->toArray();
        foreach($companiesArrToSearch as $companyId => $companyName){
            $this->companiesListToSearch[] = ['value' => $companyId, 'label' => $companyName];
        }

        // $this->companies = Company::orderBy('name')->pluck('name')->toArray();

        //$this->refcompanies = Company::select('id', 'name')->orderBy('name')->get();

        $this->yourCompany = DB::table('companies')->where('id', $user->company_id)->value('name');

        $this->countries = Location::distinct()->pluck('country_name')->toArray();

        $this->pipelineid = Pipeline::where('plan_id', $this->job)->value('id');

        $this->SuccessSkills = jobRequirement::where('job_id', $this->job)
            ->where('type', 'professional_skill')
            ->get();

        $this->SuccessRoles = jobRequirement::where('job_id', $this->job)
            ->where('type', 'Role')
            ->get();

        $this->SuccessLocation = jobRequirement::where('job_id', $this->job)
            ->where('type', 'Location')
            ->get();

        $this->SuccessGender = jobRequirement::where('job_id', $this->job)
            ->where('type', 'Gender')
            ->get();

        $this->SuccessStepup = jobRequirement::where('job_id', $this->job)
            ->where('type', 'step_up')
            ->get();

        $this->locations = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $this->personRoles = Role::distinct()->pluck('title')->map(function ($role) {
            return [
                'value' => $role,
                'label' => $role,
            ];
        })->toArray();

        // $this->functionsValue = DB::table('pipelines')->distinct()
        //     ->pluck('function')
        //     ->map(function ($function) {
        //         return [
        //             'value' => $function,
        //             'label' => $function,
        //         ];
        //     })->toArray();

        $this->functionsValue = People::whereNotNull('function')
                                    ->where('function', '!=', "")
                                    ->distinct()
                                    ->orderBy('function', 'ASC')
                                    ->pluck('function')
                                    ->map(function ($function) {
                                        return [
                                            'value' => $function,
                                            'label' => $function,
                                        ];
                                    })
                                    ->toArray();
        
            //---------------------------- Gender Split -------------------------------//
            $this->genderLabels = [];
            $this->genderData = [];
            $peopleCountsData = DB::table('people')
                ->select('gender', DB::raw('COUNT(*) as total_people'))
                ->where('company_id', '!=', $this->user->company_id)
                ->when(!empty($companyIds),function($query) use($companyIds){
                    $query->whereIn('company_id', $companyIds);
                })
                ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
                ->groupBy('gender')
                ->get();
    
            foreach ($peopleCountsData as $data) {
                $this->genderLabels[] = $data->gender;
                $this->genderData[] = $data->total_people;
            }
    
            //--------------------------- Company Split -------------------------------//
            $CompanyCountsData = DB::table('people')
                ->select('company_name', DB::raw('COUNT(*) as total_people'))
                ->where('company_id', '!=', $this->user->company_id)
                ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
                ->when(!empty($companyIds),function($query) use($companyIds){
                    $query->whereIn('company_id', $companyIds);
                })
                ->groupBy('company_name')
                ->orderBy('total_people', 'desc')
                ->take(5)
                ->get();
    
            foreach ($CompanyCountsData as $data) {
                $this->companyLabels[] = $data->company_name;
                $this->companyData[] = $data->total_people;
            }
    
            //--------------------------- Location Split -------------------------------//
            $LocationCountsData = DB::table('people')
                ->select('country', DB::raw('COUNT(*) as total_people'))
                ->where('company_id', '!=', $this->user->company_id)
                ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
                ->when(!empty($companyIds),function($query) use($companyIds){
                    $query->whereIn('company_id', $companyIds);
                })
                ->groupBy('country')
                ->orderBy('total_people', 'desc')
                ->take(5)
                ->get();
    
            //dd($LocationCountsData);
    
            foreach ($LocationCountsData as $data) {
                $this->locationLabels[] = $data->country;
                $this->locationData[] = $data->total_people;
            }
    
            //-------------------------- Function Split -------------------------------//
            $functionCountsData = DB::table('people')
                ->select('function', DB::raw('COUNT(*) as total_people'))
                ->where('company_id', '!=', $this->user->company_id)
                ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
                ->when(!empty($companyIds),function($query) use($companyIds){
                    $query->whereIn('company_id', $companyIds);
                })
                ->groupBy('function')
                ->orderBy('total_people', 'desc')
                ->get();
    
            foreach ($functionCountsData as $data) {
                $this->functionLabels[] = $data->function;
                $this->functionData[] = $data->total_people;
            }
    
            //-------------------------- Division Split -------------------------------//
            $divisionCountsData = DB::table('people')
                ->select('exco', DB::raw('COUNT(*) as total_people'))
                ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
                ->when(!empty($companyIds),function($query) use($companyIds){
                    $query->whereIn('company_id', $companyIds);
                })
                ->groupBy('exco')
                ->get();
    
            foreach ($divisionCountsData as $data) {
                $this->divisionLabels[] = $data->exco;
                $this->divisionData[] = $data->total_people;
            }
    }

    public function render()
    {
        $user = auth()->user();

        $accountObj = Account::where('id', $user->account_id)->first();

        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        if($accountObj){
            if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }

        $companyIds = [];
        if($interestedCompanies && count($interestedCompanies) > 0){
            $companyIds = $interestedCompanies;
        }
        else if($interestedIndustries && count($interestedIndustries) > 0){
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if($interestedSectors && count($interestedSectors) > 0){
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
        }

        $this->pipeline = pipeline::where('job_id', $this->job)->pluck('people_id')->toArray();
 

        $People = People::whereNotIn('id', $this->pipeline)
            ->where('status', '!=', 'Submitted')
            ->where('company_id', '!=', $this->user->company_id)
            ->when(!empty($companyIds),function($query) use($companyIds){
                $query->whereIn('company_id', $companyIds);
            })
            ->where('status', '!=', 'Reported')
            ->select('id', 'forename','surname','gender','diverse','latest_role','company_name','country','people.function','tenure','readiness', 'other_tags');

        if ($this->sortBy && $this->sortDirection) {
            $People = $People->orderBy($this->sortBy, $this->sortDirection);
        }

        if ($this->search) {
            if ($this->forename) {
                $People = $People->where('forename', 'like', '%' . $this->forename . '%');
            }
            if ($this->surname) {
                $People = $People->where('surname', 'like', '%' . $this->surname . '%');
            }
            if ($this->gender) {
                $People = $People->where('gender', '=', $this->gender);
            }
            if ($this->previousRole) {
                $People = $People->where('parent_role', 'like', '%' . $this->previousRole . '%');
            }
            if ($this->selectedRole) {
                $People = $People->where('latest_role', 'like', '%' . $this->selectedRole . '%');
            }
            if ($this->selectedCompany) {
                $People = $People->where('company_id', $this->selectedCompany);
            }
            if ($this->selectedFunctionValue) {
                $People = $People->where('function', 'like', '%' . $this->selectedFunctionValue . '%');
            }
            if ($this->location) {
                $People = $People->where('country', 'like', '%' . $this->location . '%');
            }
            if ($this->regBodies) {
                $People = $People->where('other_tags', 'like', '%' . $this->regBodies . '%');
            }
            if ($this->min_exp) {
                $People = $People->where('tenure', '=', $this->min_exp);
            }
        }

        if (!empty($this->searchByKeyword)) {
            $searchKeywords = explode(",", $this->searchByKeyword);
            $People = $People->where(function ($query) use ($searchKeywords) {
                foreach ($searchKeywords as $searchword) {
                    $query->orWhere(function ($subQuery) use ($searchword) {
                        $subQuery->orWhere('forename', 'like', '%' . $searchword . '%')
                            ->orWhere('surname', 'like', '%' . $searchword . '%')
                            ->orWhere('latest_role', 'like', '%' . $searchword . '%')
                            ->orWhere('company_name', 'like', '%' . $searchword . '%')
                            ->orWhere('function', 'like', '%' . $searchword . '%')
                            ->orWhere('division', 'like', '%' . $searchword . '%')
                            ->orWhere('country', 'like', '%' . $searchword . '%')
                            ->orWhere('summary', 'like', '%' . $searchword . '%')
                            ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                    });
                }
            });
        }

        $filteredPeople = People::where(function ($query) {
            $query->whereNotIn('id', $this->pipeline)
                ->where('id', $this->selectedIndividualID);
        })->get();


        //dd($filteredPeopleid);
        //dd($scoreskills);

        // Getting the skills and the career histories
        $filteredPeopleid = $filteredPeople->pluck('id')->toArray();

        // Get the skills of the individual
        $peopleskills = Skills::whereIn('people_id', $filteredPeopleid)->get();

        // Get the career histories of the individual
        $peoplescareer = CareerHistories::whereIn('people_id', $filteredPeopleid)
            ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
            ->select('career_histories.*', 'companies.name as company_name')
            ->get();

        $groupedPeoplesCareer = $peoplescareer->groupBy('company_name')->map(function ($items) {
            return [
                'roles' => $items->pluck('role')->toArray(),
                'start_dates' => $items->pluck('start_date')->map(function ($date) {
                    return Carbon::parse($date)->format('M Y');
                })->toArray(),
                'end_dates' => $items->pluck('end_date')->map(function ($date) {
                    return Carbon::parse($date)->format('M Y');
                })->toArray(),
            ];
        });

        $this->TotalPeople = People::whereNotIn('id', $this->pipeline)
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->where('company_id', '!=', $this->user->company_id)
            ->when(!empty($companyIds),function($query) use($companyIds){
                $query->whereIn('company_id', $companyIds);
            })
            ->count();

        $this->TotalCompanies = People::whereNotIn('id', $this->pipeline)
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->where('company_id', '!=', $this->user->company_id)
            ->when(!empty($companyIds),function($query) use($companyIds){
                $query->whereIn('company_id', $companyIds);
            })
            ->distinct('company_id')
            ->count('company_id');
        /*

        //---------------------------- Gender Split -------------------------------//
        $this->genderLabels = [];
        $this->genderData = [];
        $peopleCountsData = DB::table('people')
            ->select('gender', DB::raw('COUNT(*) as total_people'))
            ->whereNotIn('id', $this->pipeline)
            ->where('company_id', '!=', $this->user->company_id)
            ->when(!empty($companyIds),function($query) use($companyIds){
                $query->whereIn('company_id', $companyIds);
            })
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->groupBy('gender')
            ->get();

        foreach ($peopleCountsData as $data) {
            $this->genderLabels[] = $data->gender;
            $this->genderData[] = $data->total_people;
        }

        //--------------------------- Company Split -------------------------------//
        $CompanyCountsData = DB::table('people')
            ->select('company_name', DB::raw('COUNT(*) as total_people'))
            ->whereNotIn('id', $this->pipeline)
            ->where('company_id', '!=', $this->user->company_id)
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->when(!empty($companyIds),function($query) use($companyIds){
                $query->whereIn('company_id', $companyIds);
            })
            ->groupBy('company_name')
            ->orderBy('total_people', 'desc')
            ->take(5)
            ->get();

        foreach ($CompanyCountsData as $data) {
            $this->companyLabels[] = $data->company_name;
            $this->companyData[] = $data->total_people;
        }

        //--------------------------- Location Split -------------------------------//
        $LocationCountsData = DB::table('people')
            ->select('country', DB::raw('COUNT(*) as total_people'))
            ->whereNotIn('id', $this->pipeline)
            ->where('company_id', '!=', $this->user->company_id)
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->when(!empty($companyIds),function($query) use($companyIds){
                $query->whereIn('company_id', $companyIds);
            })
            ->groupBy('country')
            ->orderBy('total_people', 'desc')
            ->take(5)
            ->get();

        //dd($LocationCountsData);

        foreach ($LocationCountsData as $data) {
            $this->locationLabels[] = $data->country;
            $this->locationData[] = $data->total_people;
        }

        //-------------------------- Function Split -------------------------------//
        $functionCountsData = DB::table('people')
            ->select('function', DB::raw('COUNT(*) as total_people'))
            ->whereNotIn('id', $this->pipeline)
            ->where('company_id', '!=', $this->user->company_id)
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->when(!empty($companyIds),function($query) use($companyIds){
                $query->whereIn('company_id', $companyIds);
            })
            ->groupBy('function')
            ->orderBy('total_people', 'desc')
            ->get();

        foreach ($functionCountsData as $data) {
            $this->functionLabels[] = $data->function;
            $this->functionData[] = $data->total_people;
        }

        //-------------------------- Division Split -------------------------------//
        $divisionCountsData = DB::table('people')
            ->select('exco', DB::raw('COUNT(*) as total_people'))
            ->whereNotIn('id', $this->pipeline)
            ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED])
            ->when(!empty($companyIds),function($query) use($companyIds){
                $query->whereIn('company_id', $companyIds);
            })
            ->groupBy('exco')
            ->get();

        foreach ($divisionCountsData as $data) {
            $this->divisionLabels[] = $data->exco;
            $this->divisionData[] = $data->total_people;
        }
            */

        $peopleData = $People->paginate($this->perPage);

        return view('livewire.jobPeople.index', [
            'People' => $peopleData,
            'filteredPeople' => $filteredPeople,
            'peopleskills' => $peopleskills,
            'groupedPeoplesCareer' => $groupedPeoplesCareer
        ]);
    }

    public function runfilters()
    {
        $this->search = true;
        $this->resetPage();
    }

    public function searchRecordByKeyword()
    {
        $this->resetPage();
    }

    public function Ordermytable($column)
    {
        if ($column === $this->sortBy) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function clearFilters()
    {
        $this->search = false;
        // Reset all form input properties
        $this->reset(['forename', 'surname', 'role', 'companies', 'functionsValue', 'slocation', 'gender', 'regBodies', 'tenure', 'previousRole', 'selectedRole', 'locations']);
    }

    public function viewIndividual($id)
    {

        $this->selectedIndividualID = $id;
        $this->selectedIndividual = People::where('id', $id)->first();
        $this->selectedIndividualSkills = Skills::where('people_id', '=', $id)->get();

        // Get the career histories of the individual
        $peoplescareer = CareerHistories::where('people_id', $id)
            ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
            ->select('career_histories.*', 'companies.name as company_name')
            ->orderBy('career_histories.start_date', 'desc')
            ->get();

        $this->selectedIndividualCareerHistory = $peoplescareer;

        $user = auth()->user();
        $jobIds = JobPeople::where('people_id', $id)->pluck('job_id')->toArray();
        $this->userJobs = Job::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
        })
        ->whereIn('id', $jobIds)
        ->get();

        $planIds = SuccessPeople::where(['people_id' => $id, 'type' => 'External'])->pluck('plan_id')->toArray();
        $this->userPlans = SuccessionPlan::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
        })
        ->whereIn('id', $planIds)
        ->get();

        $this->talentPoolsList = getTalentPoolList($id, $this->job);
        $this->plansList = getPlansList($id);

       
        $this->vopen = true;

    }

    public function addpeopleToPlans() {

        if(empty($this->addToPlansArray)) {
            $this->dispatch('toast', 'info', 'Please select plans!');
            $this->skipRender();
            return;
        }
        foreach($this->addToPlansArray as $key => $value) {
            if($value) {
                $PlanPerson = SuccessPeople::where(['people_id' => $this->selectedIndividualID,'plan_id' => $key])->first();
              
                if($PlanPerson) {
                  continue;
                }
                addToPlan($this->selectedIndividualID, $key);
            }
        }
        if(!$this->disableDispatchforMultipleSelect) {
            $this->dispatch('toast', 'info', 'Added to selected plans!');
            $this->addToPlanPopup = false;
            $this->vopen = false;
        }

    }

    public function showAddToPlanPopup($peopleId) {
        $this->selectedIndividualID = $peopleId;
        $this->plansList = getPlansList($peopleId);
        $this->addToPlanPopup = true;

    }

    public function showAddToTalentPoolPopup($peopleId) {
        $this->selectedIndividualID = $peopleId;
        $this->talentPoolsList = getTalentPoolList($peopleId, $this->job);
        $this->addToTalentPoolPopup = true;

    }

    public function updatedaddCompany()
    {
        $this->Suggestion = $this->getSuggestion($this->addCompany, $this->companies);
    }

    // Function for getting the companies names as the user inputs them into the form
    private function getSuggestion($search, $array)
    {
        $search = strtolower($search);
        $matches = [];
        $exactMatch = null;

        foreach ($array as $item) {
            if (strtolower($item) === $search) {
                $exactMatch = $item;
                break;
            }

            if (strpos(strtolower($item), $search) !== false) {
                $matches[] = $item;

                if (count($matches) >= 5) {
                    break;
                }
            }
        }
        return $exactMatch !== null ? null : $matches;
    }

    public function reportPerson($id)
    {
        $user = auth()->user();
        $reportPerson = People::findorfail($id);
        $reportPerson->update([
            'status' => 'Reported',
            'user_id' => $user->id,
            'notes' => $this->reportDescription
        ]);

        $this->reset(['selectedIndividualID', 'selectedIndividual', 'selectedIndividualSkills', 'selectedIndividualCareerHistory', 'problem', 'reportDescription']);
    }


    public function AddPerson()
    {

        $this->validate(
            [
                'addLinkedinURL' => 'url'
            ],
            [
                'addLinkedinURL.url' => 'Please enter a valid LinkedIn url or leave it empty.'
            ]
        );

        $user = auth()->user();

        if ($this->selectedCompanies === 'Internal') {
            $internalcompany = Company::where('id', $user->company_id)->first();
            $roleExists = Role::where('title', $this->addRole)->first();
            if (empty($roleExists)) {
                $roleExists = new Role();
                $roleExists->title = $this->addRole;
                $roleExists->save();
            }
            $internalPerson = InternalPeople::create([
                'forename'    => $this->addForename,
                'surname'     => $this->addSurname,
                'middle_name' => $this->addMiddlename,
                'other_name'  => $this->addOtherName,
                'role_id'     => $roleExists->id,
                'gender'      => $this->addGender,
                'linkedinURL' => $this->addLinkedinURL,
                'created_by'  => $user->id,
                'company_id'  => $internalcompany->id,
                'company_name' => $internalcompany->name,
                'start_date'  => $this->addStartDate,
                'location'    => $this->addLocation,
                'user_id'     => $user->id
            ]);

            $startDate = Carbon::parse($this->addStartDate);
            $currentDate = Carbon::now();
            $tenure = $currentDate->diffInYears($startDate);

            $roleai = $this->addRole;
            $locationai = $this->addLocation;
            $functionai = $this->addFunction;
            $divisionai = $this->addDivision;
            //$companyai = $itemData->company_name;

            $inputArray = [
                'role'     => $roleai,
                'location' => $locationai,
                'function' => $functionai,
                'division' => $divisionai
            ];

            // Create a formatted message for GPT-3
            $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
            foreach ($inputArray as $key => $value) {
                $message .= "$key: \"$value\"\n";
            }

            // Call GPT-3 to generate the headline
            $response = OpenAI::chat()->create([
                'model' => 'gpt-3.5-turbo',
                'messages' => [['role' => 'system', 'content' => $message]],
            ]);

            $generatedHeadline = $response->choices[0]->message->content;

            $internalPersonId = $internalPerson ? $internalPerson->id : null;

            JobPeople::create([
                'pipeline_id'        => $this->pipelineid,
                'plan_id'            => $this->job,
                'user_id'            => $user->id,
                'people_id'          => $internalPersonId,
                'headline'           => $generatedHeadline,
                'first_name'         => $this->addForename,
                'last_name'          => $this->addSurname,
                'middle_name'        => "NA",
                'other_name'         => "NA",
                'gender'             => $this->addGender,
                'diverse'            => "NA",
                'location'           => $this->location,
                'linkedinURL'        => $this->addLinkedinURL,
                'latest_role'        => $this->addRole,
                'company_id'         => $user->company_id,
                'company_name'       => $this->yourCompany,
                'start_date'         => $this->addStartDate,
                'tenure'             => $tenure,
                'function'           => $this->addFunction,
                'division'           => $this->addDivision,
                'seniority'          => "NA",
                'career_history'     => "NA",
                'educational_history' => "NA",
                'skills'             => "NA",
                'languages'          => "NA",
                'skills_match'       => 0,
                'education_match'    => 0,
                'location_match'     => 1,
                'role_match'         => 1,
                'gender_match'       => 1,
                'tenure_match'       => 1,
                'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                'type'               => "Internal",
                'notes'              => "Enter notes here",
                'recruit'            => 1,
                'status'             => "3"
            ]);

            pipeline::create([
                'id'                 => $this->pipelineid,
                'job_id'            => $this->job,
                'user_id'            => $user->id,
                'people_id'          => $internalPersonId,
                'headline'           => $generatedHeadline,
                'first_name'         => $this->addForename,
                'last_name'          => $this->addSurname,
                'middle_name'        => "NA",
                'other_name'         => "NA",
                'gender'             => $this->addGender,
                'diverse'            => "NA",
                'location'           => $this->location,
                'linkedinURL'        => $this->addLinkedinURL,
                'latest_role'        => $this->addRole,
                'company_id'         => $user->company_id,
                'company_name'       => $this->yourCompany,
                'start_date'         => $this->addStartDate,
                'tenure'             => $tenure,
                'function'           => $this->addFunction,
                'division'           => $this->addDivision,
                'seniority'          => "NA",
                'career_history'     => "NA",
                'educational_history' => "NA",
                'skills'             => "NA",
                'languages'          => "NA",
                'skills_match'       => 0,
                'education_match'    => 0,
                'location_match'     => 1,
                'role_match'         => 1,
                'gender_match'       => 1,
                'tenure_match'       => 1,
                'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                'people_type'        => "Internal-User"
            ]);
        }

        // Adding External People into the sytem
        else {


            $createdCompany = Company::where('name', $this->selectedCompanies)->first();

            if (empty($createdCompany)) {
                $createdCompany = Company::create([
                    'name'          => $this->selectedCompanies,
                    'location_id'   =>  1784674685,
                    'status'        => 'submited'
                ]);
            }

            if ($this->addStartDate !== null) {
                $startDate = Carbon::parse($this->addStartDate);
                $currentDate = Carbon::now();
                $tenure = $currentDate->diffInYears($startDate);
            } else {
                $tenure = 0;
            }

            $ExternalPeople = People::create([

                'forename'    => $this->addForename,
                'surname'     => $this->addSurname,
                'middle_name' => $this->addMiddlename,
                'other_name'  => $this->addOtherName,
                'gender'      => $this->addGender,
                'diverse'     => "NA",
                'linkedinURL' => $this->addLinkedinURL,
                'latest_role' => $this->addRole,
                'company_id'  => $createdCompany->id,
                'company_name' => $createdCompany->name,
                'function'    => $this->addFunction,
                'division'    => $this->addDivision,
                'tenure'      => $tenure,
                'status'      => "Submitted",
                'exco'        => "Non Exco",
                'user_id'     => $user->id,
            ]);

            $this->addToJob($ExternalPeople->id);
        }

        $this->addPersonPopup = false;
        $this->step = 1;
        $this->reset(['addForename', 'addSurname', 'addMiddlename', 'addOtherName', 'addStartDate', 'addRole', 'addGender', 'addLinkedinURL', 'addLocation']);
    }

    public function addTojob($id, $showToast = true)
    {
        $user = auth()->user();

        //dd($this->job);

        // Get the details of the external person
        $ExternalPerson = People::where('id', $id)->first();
        $jobDetail = Job::find($this->job);

        if (!$jobDetail) {
            $this->dispatch('toast', 'error', "Job not found!");
        }

        $pipelineData = [
            'job_id'            => $this->job,
            'user_id'            => $user->id,
            'people_id'          => $ExternalPerson->id,
            'first_name'         => $ExternalPerson->forename,
            'last_name'          => $ExternalPerson->surname,
            'middle_name'        => $ExternalPerson->middle_name,
            'other_name'         => $ExternalPerson->other_name,
            'gender'             => $ExternalPerson->gender,
            'diverse'            => $ExternalPerson->diverse,
            'location'           => $ExternalPerson->location,
            'linkedinURL'        => $ExternalPerson->linkedinURL,
            'latest_role'        => $ExternalPerson->latest_role,
            'company_id'         => $ExternalPerson->company_id,
            'company_name'       => $ExternalPerson->company_name,
            'start_date'         => $ExternalPerson->start_date,
            'end_date'           => $ExternalPerson->end_date,
            'tenure'             => $ExternalPerson->tenure,
            'function'           => $ExternalPerson->function,
            'division'           => $ExternalPerson->division,
            'seniority'          => $ExternalPerson->seniority,
            'career_history'     => $ExternalPerson->career_history,
            'educational_history' => $ExternalPerson->educational_history,
            'skills'             => $ExternalPerson->skills,
            'languages'          => $ExternalPerson->languages,
            'readiness'          => $ExternalPerson->readiness,
            'other_tags'         => $ExternalPerson->other_tags,
            'country'            => $ExternalPerson->country,
            'city'               => $ExternalPerson->city,
            'summary'            => $ExternalPerson->summary,
            'exco'               => $ExternalPerson->exco,
            'skills_match'       => 0,
            'education_match'    => 0,
            'location_match'     => 0,
            'role_match'         => 0,
            'gender_match'       => 0,
            'tenure_match'       => 0,
            'total_score'        => 0,
            'people_type'     => 'External-User',
        ];

        if($jobDetail->user_id != auth()->user()->id) {
            $pipelineData['status'] = "Proposed";
        }

        $newPipeline = pipeline::create($pipelineData);

        $dataToCreate = [
            'pipeline_id'        => $newPipeline->id,
            'job_id'             => $this->job,
            'user_id'            => $user->id,
            'people_id'          => $ExternalPerson->id,
            'headline'           => 'Not Applicable',
            'first_name'         => $ExternalPerson->forename,
            'last_name'          => $ExternalPerson->surname,
            'middle_name'        => $ExternalPerson->middle_name,
            'other_name'         => $ExternalPerson->other_name,
            'gender'             => $ExternalPerson->gender,
            'diverse'            => $ExternalPerson->diverse,
            'location'           => $ExternalPerson->country,
            'linkedinURL'        => $ExternalPerson->linkedinURL,
            'latest_role'        => $ExternalPerson->latest_role,
            'company_id'         => $ExternalPerson->company_id,
            'company_name'       => $ExternalPerson->company_name,
            'start_date'         => $ExternalPerson->start_date,
            'end_date'           => $ExternalPerson->end_date,
            'tenure'             => $ExternalPerson->tenure,
            'function'           => $ExternalPerson->function,
            'division'           => $ExternalPerson->division,
            'seniority'          => $ExternalPerson->seniority,
            'career_history'     => $ExternalPerson->career_history,
            'educational_history' => $ExternalPerson->educational_history,
            'skills'             => $ExternalPerson->skills,
            'languages'          => $ExternalPerson->languages,
            'readiness'          => $ExternalPerson->readiness,
            'other_tags'         => $ExternalPerson->other_tags,
            'country'            => $ExternalPerson->country,
            'city'               => $ExternalPerson->city,
            'summary'            => $ExternalPerson->summary,
            'notes'              => "Enter notes here",
            'status'             => "Approved"
        ];

        if($jobDetail->user_id != auth()->user()->id) {
            $dataToCreate['status'] = "Proposed";
        }

        $JobPeople = JobPeople::create($dataToCreate);
        if ($showToast) {
            $this->dispatch('toast', 'info', "Added to job");
        }


        // Add requirements for chart for skills radar
    }

    public function validateStepOne()
    {
        $this->validate(
            [
                'addForename' => 'required|string',
                'addSurname' => 'required|string',
                'addRole' => 'required|string',
                'selectedCompanies' => 'required|string'
            ],
            [
                'addForename.required' => 'The forename field is required.',
                'addForename.string' => 'The forename must be a string.',
                'addSurname.required' => 'The surname field is required.',
                'addSurname.string' => 'The surname must be a string.',
                'addRole.required' => 'The role field is required.',
                'addRole.string' => 'The role must be a string.',
                'selectedCompanies.required' => 'The company field is required.',
                'selectedCompanies.string' => 'The company must be a string.'
            ]
        );

        $createdCompany = Company::where('name', $this->selectedCompanies)->first();

        $this->copySelectedCompaniesValue = $this->selectedCompanies;

        if (!empty($createdCompany)) {
            $this->isPeopleAlreadyExists = People::where([
                'forename' => $this->addForename,
                'surname' => $this->addSurname,
                'company_id' => $createdCompany->id
            ])->get();

            if (count($this->isPeopleAlreadyExists) > 0) {
                $this->peopleAreadyExistsPopup = true;
            }
        }

        $this->step++;
    }

    public function previousStep()
    {
        // Move to the previous step
        $this->step--;
        $this->selectedCompanies = $this->copySelectedCompaniesValue;
    }

    public function uploadCSV()
    {
        $this->validate([
            'csvFile' => 'required|mimes:csv,xlsx,xls|max:2048', // accept only csv and excel files with a max size of 2MB
        ], [
            'csvFile.required' => "Please select excel or csv file."
        ]);
        try {
            $user = auth()->user();
            $this->processCSV($this->csvFile, $user);
        } catch (Exception $e) {

            $this->dispatch('toast', 'error', $e->getMessage());
        }
    }

    public function downloadCSV()
    {
        $headerRow = ['forename', 'surname', 'other_name', 'gender', 'city', 'country', 'linkedinURL', 'role', 'start_date(m/d/y)', 'company_name'];

        // Dummy data
        $dataRows = [
            [
                'forename' => 'Tom',
                'surname' => 'sam',
                'other_name' => "",
                'gender' => 'male',
                'city' => "CA",
                "country" => "UK",
                'linkedinURL' => 'linkedin.com',
                'role' => 'Manager',
                'start_date' => '01/01/2024',
                'company_name' => 'Google',
            ],
            [
                'forename' => 'Brendon',
                'surname' => 'king',
                'other_name' => "",
                'gender' => 'male',
                'city' => "CA",
                "country" => "UK",
                'linkedinURL' => 'linkedin.com',
                'role' => 'Developer',
                'start_date' => '02/03/2022',
                'company_name' => 'Google',
            ],
        ];

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="csv_template.csv"',
        ];

        return Response::stream(function () use ($headerRow, $dataRows) {
            $handle = fopen('php://output', 'w');
            fputcsv($handle, $headerRow);
            foreach ($dataRows as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
        }, 200, $headers);
    }

    private function processCSV($csvFile, $user)
    {

        // $csvData = array_map('str_getcsv', file($csvFile->getRealPath()));
        // $expectedHeaders = ['forename', 'surname', 'other_name', 'gender', 'city', 'country', 'linkedinURL', 'role', 'start_date(m/d/y)', 'company_name'];
        // $headers = $csvData[0];
        // if ($headers !== $expectedHeaders) {
        //     $this->errormessage2 = "Please upload the correct file with the proper headers.";
        //     return redirect()->back()->with('error', 'CSV file format is invalid.')->withInput();
        // }

        $excelData = Excel::toArray(new PeopleImport, $csvFile->getRealPath());

        $duplicates = [];
        $excelData = $excelData[0];
        Log::info($excelData);
        foreach ($excelData as $key=> $row) {
            // if ($skipFirstRow) {
            //     $skipFirstRow = false; // Set the flag to false after the first iteration
            //     continue;
            // }
            // Check if the required columns exist in the current row
            // if (count($row) < 10) {
            //     $this->errormessage2 = " Please upload the correct people template";
            //     return;
            // }

            $forename = $row['forename'];
            $surname = $row['surname'];
            $otherName = $row['other_name'];
            $gender = $row['gender'];
            $city = $row['city'];
            $country = $row['country'];
            $linkedinURL = $row['linkedinurl'];
            $role = $row['role'];
            $startDate = $row['start_datemdy'];
            $companyName = $row['company_name'];
            $inputArray = [
                'role'     =>  $row['role'],
                // 'location' =>$location,
                // 'function' => $functionai,
                // 'division' => $divisionai
            ];

            // Create a formatted message for GPT-3
            $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
            // foreach ($inputArray as $key => $value) {
            //     $message .= "$key: \"$value\"\n";
            // }

            // Call GPT-3 to generate the headline
            $response = OpenAI::chat()->create([
                'model' => 'gpt-3.5-turbo',
                'messages' => [['role' => 'system', 'content' => $message]],
            ]);

            $generatedHeadline = $response->choices[0]->message->content;
            if (strtolower($companyName) === 'internal') {
                $internalcompany = Company::where('id', $user->company_id)->first();
                $roleExists = Role::where('title',$row['role'])->first();
                if (empty($roleExists)) {
                    $roleExists = new Role();
                    $roleExists->title = $row['role'];
                    $roleExists->save();
                }
                $internalPerson = InternalPeople::create([
                    'name'        => $forename,
                    'role_id'        => $roleExists->id,
                    'gender'      => $gender,
                    // 'function'    => $this->addFunction,
                    // 'division'    => $this->addDivision,
                    'start_date' => Carbon::parse($startDate)->format('Y-m-d'),
                    // 'location'    => $this->addLocation,
                    'created_by'  => $user->id,
                    'company_id' => $internalcompany->id,
                    'company_name' => $internalcompany->name,
                    'user_id' => $user->id

                ]);

                $startDate = Carbon::parse($this->addStartDate);
                $currentDate = Carbon::now();
                $tenure = $currentDate->diffInYears($startDate);

                $internalPersonId = $internalPerson ? $internalPerson->id : null;

                $job = JobPeople::create([
                    'pipeline_id'        => $this->pipelineid,
                    'job_id'            => $this->job,
                    'user_id'            => $user->id,
                    'people_id'          => $internalPersonId,
                    'headline'           => $generatedHeadline,
                    'first_name'         => $forename,
                    'last_name'          => $surname,
                    'other_name'         => $otherName,
                    'gender'             => $gender,
                    'diverse'            => "NA",
                    'location'           => "N/A",
                    'linkedinURL'        => $linkedinURL,
                    'latest_role'        => $role,
                    'company_id'         => $user->company_id,
                    'company_name'       => $companyName,
                    'start_date'         => $startDate,
                    'tenure'             => $tenure,
                    'function'           => "N/A",
                    'division'           => "N/A",
                    'seniority'          => "NA",
                    'career_history'     => "NA",
                    'educational_history' => "NA",
                    'skills'             => "NA",
                    'languages'          => "NA",
                    'notes'              => "Enter notes here",
                ]);
                dd($job);
                pipeline::create([
                    'id'                 => $this->pipelineid,
                    'job_id'            => $this->job,
                    'user_id'            => $user->id,
                    'people_id'          => $internalPersonId,
                    'headline'           => $generatedHeadline,
                    'first_name'         => $forename,
                    'last_name'          => $surname,
                    'middle_name'        => "NA",
                    'other_name'         => $otherName,
                    'gender'             => $gender,
                    'diverse'            => "NA",
                    // 'location'           => $this->location,
                    'linkedinURL'        => $linkedinURL,
                    'latest_role'        => $role,
                    'company_id'         => $user->company_id,
                    'company_name'       => $companyName,
                    'start_date'         => $startDate,
                    'tenure'             => $tenure,
                    'function'           => "N/A",
                    'division'           => "N/A",
                    'seniority'          => "NA",
                    'career_history'     => "NA",
                    'educational_history' => "NA",
                    'skills'             => "NA",
                    'languages'          => "NA",
                    'skills_match'       => 0,
                    'education_match'    => 0,
                    'location_match'     => 1,
                    'role_match'         => 1,
                    'gender_match'       => 1,
                    'tenure_match'       => 1,
                    'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                    'people_type'        => "Internal-User"
                ]);
            } else {

                $createdCompany = Company::where('name', trim( $row['company_name']))->first();

                if (empty($createdCompany)) {
                    $createdCompany = Company::create([
                        'name'          =>  $row['company_name'],
                        'location_id'   =>  1784674685,
                        'status'        => 'submited'
                    ]);
                }

                // Check if a record with the given employee_id exists
                $existingRecord = People::where([
                    'forename' => $forename,
                    'surname' => $surname,
                    'company_id' => $createdCompany->id
                ])->get();

                if ($existingRecord->isNotEmpty()) {
                    $duplicates["$forename-$surname-$role-$companyName"] = (object) [
                        'forename'    => $forename,
                        'surname'     => $surname,
                        'other_name'  => $otherName,
                        'gender'      => $gender,
                        'linkedinURL' => $linkedinURL,
                        'latest_role' => $role,
                        'companyName' => $companyName,
                        "startDate"   => $startDate,
                        'duplicate'   =>  json_decode(json_encode($existingRecord->toArray()), false)
                    ];
                } else {

                    $startDate = Carbon::parse($row['start_datemdy']);
                    $currentDate = Carbon::now();
                    $tenure = $currentDate->diffInYears($startDate);
                    $startDate = Carbon::createFromFormat('m/d/Y', trim($row['start_datemdy']))->format('Y-m-d');
                    $externalPeople = People::create([

                        'forename'    => $forename,
                        'surname'     => $surname,
                        'other_name'  => $otherName,
                        'gender'      => $gender,
                        'diverse'     => "NA",
                        'linkedinURL' => $linkedinURL,
                        'latest_role' => $role,
                        'company_id'  => $createdCompany->id,
                        'company_name' => $createdCompany->name,
                        'tenure'        => $tenure,
                        'exco'          => "Non Exco",
                        'status'        => 'Submitted',
                        'start_date'    => $startDate,
                        'user_id'     => $user->id,
                    ]);

                    $this->addTojob($externalPeople->id, false);
                }
            }
        }

        $this->uploadPopup = false;
        if (count($duplicates) > 0) {
            $this->duplicateRecords = (object) $duplicates;
            $this->duplicateUploadPopup = true;
            return;
        }
        $this->dispatch('toast', 'info',  "File uploaded successfully");

        $this->reset(['errormessage1', 'errormessage2', 'errormessage3', 'csvFile']);
    }

    public function getSummaryHeadline($inputArray)
    {
        $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
        foreach ($inputArray as $key => $value) {
            $message .= "$key: \"$value\"\n";
        }

        // Call GPT-3 to generate the headline
        $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
        ]);

        return $response->choices[0]->message->content;
    }

    public function useExistingPeople()
    {
        $this->validate([
            'existingPeopleField' => 'required',
        ]);
        $this->addTojob($this->existingPeopleField);
        $this->peopleAreadyExistsPopup = false;
        $this->addPersonPopup = false;
        $this->step = 1;
        $this->reset(['addForename', 'addSurname', 'addMiddlename', 'addOtherName', 'addStartDate', 'addRole', 'addGender', 'addLinkedinURL', 'addLocation']);
    }

    public function saveSelectedRecords()
    {

        foreach ($this->duplicateRecords as $key => $duplicateRecord) {

            if (in_array($key, $this->selectedRecords)) {
                $this->createPeopleAndAddToJob($duplicateRecord);
                continue;
            }

            $isExistingUserUsed = false;

            foreach ($duplicateRecord->duplicate as $people) {
                if (in_array($people->id, $this->selectedRecords)) {
                    $isExistingUserUsed = true;
                    $this->addTojob($people->id, false);
                }
            }

            if (!$isExistingUserUsed) {
                $this->createPeopleAndAddToJob($duplicateRecord);
            }
        }
        $this->dispatch('toast', 'info',  "File uploaded successfully");
        $this->duplicateUploadPopup = false;
        $this->reset(['errormessage1', 'errormessage2', 'errormessage3', 'csvFile']);
        $this->selectedRecords = [];
    }

    public function createPeopleAndAddToJob($record)
    {
        $createdCompany = Company::where('name', trim($record->companyName))->first();

        if (empty($createdCompany)) {
            $createdCompany = Company::create([
                'name'          => $record->companyName,
                'location_id'   =>  1784674685,
                'status'        => 'submited'
            ]);
        }

        $user = auth()->user();
        $startDate = Carbon::parse($record->startDate);
        $currentDate = Carbon::now();
        $tenure = $currentDate->diffInYears($startDate);
        $startDate = Carbon::createFromFormat('m/d/Y', trim($record->startDate))->format('Y-m-d');
        $externalPeople = People::create([

            'forename'    => $record->forename,
            'surname'     => $record->surname,
            'other_name'  => $record->other_name,
            'gender'      => $record->gender,
            'start_date'      => $startDate,
            'diverse'     => "NA",
            'linkedinURL' => $record->linkedinURL,
            'latest_role' => $record->latest_role,
            'company_id'  => $createdCompany->id,
            'company_name' => $createdCompany->name,
            'tenure'        => $tenure,
            'exco'          => "Non Exco",
            'status'        => 'Submitted',
            'user_id'     => $user->id,
        ]);

        $this->addTojob($externalPeople->id, false);
    }

    public function clearSelectedIndividuals()
    {
        $this->reset(['selectedIndividualID', 'selectedIndividual', 'selectedIndividualSkills', 'selectedIndividualCareerHistory', 'problem', 'reportDescription']);
    }

    public function addPersonModalClosed()
    {
        $this->step = 1;
        $this->reset(['addForename', 'addSurname', 'addMiddlename', 'addOtherName', 'addRole', 'addStartDate', 'selectedCompanies', 'addLocation', 'addGender', 'addLinkedinURL']);
    }

    public function uploadCandidatesModalClosed()
    {
        $this->hasCandidatesUploaded = false;
        $this->reset(['errormessage1', 'errormessage2', 'errormessage3', 'csvFile']);
    }

    public function addpeopleToTalentPools() {
        if(empty($this->addToTalentPoolArray)) {
            $this->dispatch('toast', 'info', 'Please select talent pool!');
            $this->skipRender();
            return;
        }

        $selectedTalentPool = $this->job;
        foreach($this->addToTalentPoolArray as $key => $value) {
            if($value) {
                $JobPerson = JobPeople::where(['people_id' => $this->selectedIndividualID,'job_id' => $key])->first();
                if($JobPerson) {
                    continue;
                }
                $this->job = $key;
                $this->addToJob($this->selectedIndividualID, false);
            }
        }


        $femaleRatio = DB::table('job_people')
                ->select('job_id', DB::raw('(SUM(gender = "Female")/Count(*))*100 as female_ratio'))
                ->groupBy('job_id')
                ->where('job_id', $this->job)
                ->first();

                Job_Scores::updateOrInsert(
                ['job_id' => $this->job, 'metric_name' => 'Female-ratio'],
                ['score' => $femaleRatio->female_ratio]
            );

            $maleRatio = DB::table('job_people')
                ->select('job_id', DB::raw('(SUM(gender = "Male")/Count(*))*100 as male_ratio'))
                ->groupBy('job_id')
                ->where('job_id', $this->job)
                ->first();


            Job_Scores::updateOrInsert(
                ['job_id' => $this->job, 'metric_name' => 'Male-Ratio'],
                ['score' => $maleRatio->male_ratio]
            );


        $this->job = $selectedTalentPool;
        if(!$this->disableDispatchforMultipleSelect) {
        $this->dispatch('toast', 'info', 'Added to selected talent pools!');
        $this->addToTalentPoolPopup = false;
        $this->vopen = false;
        $this->addToTalentPoolArray = [];
        }
    }


    
    public function addSelectedToTalentPool()
    {  
        $this->talentPoolsList = collect(); 
        $processed = [];
        foreach ($this->selectedPerson as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->talentPoolsList = $this->talentPoolsList->merge(getTalentPoolList($peopleId));
                $processed[] = $peopleId;
            }
        }
       
        $this->talentPoolsList = $this->talentPoolsList->unique('id')->values();
        $this->addToTalentPoolPopup = true;
        $this->openTalentPopup = true;
    }

    public function addSelectedPersonToTalentPool()
    {  
        $this->disableDispatchforMultipleSelect = true;
        foreach($this->selectedPerson as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;
            $this->addpeopleToTalentPools();
        }
        $this->openTalentPopup = false;
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected talent pools!');
        $this->addToTalentPoolPopup = false;
        $this->vopen = false;
        $this->addToTalentPoolArray = [];
        $this->selectedPerson = [];
       
    }
    

    public function addSelectedToPlan()
    { 
        $this->plansList = collect(); 
        $processed = [];
        foreach ($this->selectedPerson as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->plansList = $this->plansList->merge(getPlansList($peopleId));
                $processed[] = $peopleId;
            }
        }
        
        $this->plansList = $this->plansList->unique('id')->values();
       
        $this->addToPlanPopup = true;
        $this->openPlanPopup = true;
        
    }
    public function addSelectedPersonToPlans()
    {  
      $this->disableDispatchforMultipleSelect = true;
       foreach ($this->selectedPerson as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;

            $this->addpeopleToPlans();
       }
        $this->openPlanPopup = false;
        $this->selectedPerson = [];
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected plans!');
        $this->addToPlanPopup = false;
       
        $this->vopen = false;
        $this->addToPlansArray = [];
    }
  /*  public function deleteSelected()
    {
        $selectedIds = $this->selectedPerson;
        foreach ($selectedIds as $id) {
            People::where('id', $id)->delete();
        }
        $this->dispatch('toast', 'success', 'Individual has deleted successfully.');
        $this->selectedPerson = [];
    }*/

    public function updatingPage() {
        $this->selectedPerson = [];
    }
}
