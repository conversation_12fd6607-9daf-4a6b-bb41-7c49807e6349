<?php
namespace App\Livewire;

use Livewire\Component;
use App\Models\Company;
use Illuminate\Support\Facades\Redirect;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use App\Models\Job;
use App\Models\Job_Scores;
use App\Models\Skills;
use App\Models\User;
use App\Models\JobPeople;
use App\Models\JobRequirement;
use App\Models\People;
use App\Models\Location;
use App\Models\CareerHistories;
use App\Models\pipeline;
use App\Models\notifications;
use App\Models\Account;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;



class JobTable extends Component
{
    protected $listeners = [
        'createJobModalClosed',
        'updateJobModalClosed',
        'deletejob'
    ];

    // Public variables for the filters of the page
    public $status = '';
    public $user;
    public $search = '';
    public $newjob;
    public $shouldRedirect = false;

    // Public variables for the form
    public $name                =    '';
    public $roles               =    [];
    public $roleTag             =    '';
    public $companies;
    public $selectedCompanies   =    [];
    public $division            =    '';
    public $function            =    '';
    public $stepup              =    [];
    public $stepupTag           =    '';
    public $descriptions        =    '';
    public $min_exp             =    null;
    public $education           =    '';
    public $location            =    '';
    public $gender              =    null;
    public $ethnicity           =    0;
    public $selectedCountries   =   [];
    public $countries;
    public $searchCountries = '';

    //References to update job details
    public $job_name;
    public $job_description;
    public $ustatus = 'Draft';
    public $minExp;
    public $uroles ='';
    public $jobstatus;
    public $ueducation;
    public $ulocation;
    public $uselectedCountries = [];
    public $ucountries;
    public $contributers;
    public $selectedcontributers = [];
    public $jobArray;
    public $updateskills;
    public $updateRole ='';
    public $updateGender = 0;
    public $updateSteprole; 

    public $job;

    // Chart labels and data
    public $scoreLables = [];
    public $scoreData = [];

    public $selectedStatuses = [];
    public $isActiveSelected = false;
    public $isClosedSelected = false;
    public $isDraftSelected = false;

    public $step                =   1;
    public $qualificationTag    =   '';
    public $qualifications      =   [];
    public $skillTag            =   '';
    public $skills              =   [];
    public $perPage             =   10; // Number of items per page
    public $page                =   1; // Current page number
    public $selectedJobId       =   "";
    public $hasDataPopulated    =   false;
    public $selectedJob         =   "";
    public $selectedColleagues = [];

    public $newSkillData = [
        'qualifications' => [],
        'skills' => [],
        'targetRoles' => [],
        'stepUpCandidate' => [],
        'keyword' => []
    ];

    public $sectors = [];
    public $industries = [];
    public $selectedSectors     =    [];
    public $selectedIndustries  =    [];
    public $interestedCompaniesarray = [];

    use WithPagination;

    public function mount(){

        $this->user = auth()->user();
        // dd($this->user);

        // Code to get the companies of interest for a specific account
        $accountObj = Account::where('id', $this->user->account_id)->first();

        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        if($accountObj){
            if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }

        $companyIds = [];
        if($interestedCompanies && count($interestedCompanies) > 0){
            $companyIds = $interestedCompanies;
        }
        else if($interestedIndustries && count($interestedIndustries) > 0){
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if($interestedSectors && count($interestedSectors) > 0){
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
        }

        //$this->countries = Location::distinct()->pluck('country_name')->toArray();
        $this->countries = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $this->interestedCompaniesarray = $companyIds;

        $companiesArr = array_unique(People::whereNotNull('company_id')->pluck('company_id')->toArray());

        $this->companies = Company::where('id','!=',$this->user->company_id)
                                    ->when(!empty($companyIds),function($query) use($companyIds){
                                        $query->whereIn('id', $companyIds);
                                    })
                                    ->whereIn('id', $companiesArr)
                                    ->get()
                                    ->map(function ($company) {
            return [
                'value' => $company->name,
                'label' => $company->name,
                'industry' => $company->industry,
                'sector' => $company->sector
            ];
        })->toArray();

        // $sectors = Company::whereNotNull('sector')
        //                     ->where('sector', '!=', 'NA')
        //                     ->where('sector', '!=', '0')
        //                     ->when(!empty($companyIds),function($query) use($companyIds){
        //                         $query->whereIn('id', $companyIds);
        //                     })
        //                     ->distinct()
        //                     ->orderBy('sector', 'ASC')
        //                     ->get(['id', 'sector'])
        //                     ->groupBy('sector')
        //                     ->toArray();
        // foreach($sectors as $key => $sector){
        //     $this->sectors[] = ['value' => $key, 'label' => $key];
        // }

        $industries = Company::whereNotNull('industry')
                                ->where('industry', '!=', 'NA')
                                ->where('industry', '!=', '0')
                                ->when(!empty($companyIds),function($query) use($companyIds){
                                    $query->whereIn('id', $companyIds);
                                })
                                ->distinct()
                                ->orderBy('industry', 'ASC')
                                ->get(['id', 'industry'])
                                ->groupBy('industry')
                                ->toArray();
        foreach($industries as $key => $industry){
            $this->industries[] = ['value' => $key, 'label' => $key];
        }
    }
  

    public function render()
    {

        $this->countries = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $user = auth()->user();

        $this->contributers = User::where('company_id', $user->company_id)
                                    ->where('id','!=',$user->id)
                                    ->get()->map(function ($user) {
                                        return [
                                            'value' => $user->id,
                                            'label' => $user->name,
                                        ];
                                    })->toArray();
        

        $accountIds = Account::where('relationship_manager_id', 'LIKE', '%' . $user->id . '%')->pluck('id')->toArray();
        $clientuser = [];
        if(count($accountIds) > 0){
            $clientuser = User::whereIn('account_id', $accountIds)->pluck('id')->toArray();
        }
        $clientuser[] = $user->id;
        
        $query = Job::query()->orderBy('id','desc');

        // Filter by status
        // if (!empty($this->status)) {
        //     $query->where('Status', $this->status);
        // } else {
        //     $query->whereNotNull('id');
        // }

        if (!empty($this->selectedStatuses) && !in_array('all', $this->selectedStatuses)) {
            $query->whereIn('status', $this->selectedStatuses);
        }
        else {
            $query->whereNotNull('id');
        }


        // Filter by searched name
        if (!empty($this->search)) {
            $query->where('name', 'LIKE', '%' . $this->search . '%');
        }

        $query = $query
        // ->where('user_id', $user->id)
        ->whereIn('user_id', $clientuser)
        ->orWhereRaw('JSON_CONTAINS(shared_with, ?)', [json_encode($user->id)]); // Check if user ID exists in the shared_with JSON array

        // $jobs = $query->get(); // Adjust the number of items per page as needed
        $jobs = $query->paginate($this->perPage); // Adjust the number of items per page as needed

        // Extract the IDs from the paginated jobs
        $jobIds = $jobs->pluck('id');

        // Use the extracted plan IDs to filter PlanScore
        $Job_Scores = Job_Scores::whereIn('job_id', $jobIds)->get();
        $groupedJob_Scores = $Job_Scores->groupBy('job_id');
        $groupedJob_Scores->transform(function ($item) {
            return [
                'metric_names' => $item->pluck('metric_name')->toArray(),
                'scores' => $item->pluck('score')->toArray(),
            ];
        });

        // Loop through the plans and attach corresponding data from groupedJob_Scores
        $jobs->each(function ($job) use ($groupedJob_Scores) {
            $successionJobId = $job->id; // Replace with the actual column name
            if (isset($groupedJob_Scores[$successionJobId])) {
                $metricNames = $groupedJob_Scores[$successionJobId]['metric_names'];
                $scoreData = $groupedJob_Scores[$successionJobId]['scores'];
                $combinedScoreData = array_combine($metricNames, $scoreData);

                $job->scoreLabels = $metricNames;
                $job->scoreData = $scoreData;
                $job->combinedScoreData = $combinedScoreData;
            } else {
                $job->scoreLabels = [];
                $job->scoreData = [];
                $job->combinedScoreData = [];
            }
        });



        // Get the pipeline counts and succession counts that would be used
        $pipelinePeople = DB::table('pipelines')
        ->select('job_id', DB::raw('COUNT(*) as pipelinecount'))
        ->whereIn('job_id', $jobIds)
        ->groupBy('job_id')
        ->get();
                            
                            $pipelineCountsMap = $pipelinePeople->keyBy('job_id');
                            // dd($pipelineCountsMap);

        // $jobs = $jobs->map(function ($job) use ($pipelineCountsMap) {
        //     $job->pipelinecount = $pipelineCountsMap[$job->id]->pipelinecount ?? 0;
        //     return $job;
        // });

        // Get the succession counts
        $jobPeople = DB::table('job_people')
                    ->select('job_id', DB::raw('Count(*) as jobcount'))
                    ->groupby('job_id') 
                    ->whereIn('job_id', $jobIds)
                    ->get();

        $jobCountsMap = $jobPeople->keyBy('job_id');
        //dd($jobCountsMap);

        // $jobs = $jobs->map(function ($job) use ($jobCountsMap) {
        //     $job->jobcount = $jobCountsMap[$job->id]->jobcount ?? 0;
        //     return $job;  
        // });

        $user = auth()->user()->get();


        if($this->selectedJobId && !$this->hasDataPopulated){
            $this->hasDataPopulated = true;
            $this->selectedJob = Job::where('id', $this->selectedJobId)->first();
            $this->name = $this->selectedJob->name;
            $this->descriptions = $this->selectedJob->description;
            $this->ethnicity = $this->selectedJob->ethnicity;
            $this->min_exp = $this->selectedJob->minimum_experience;
            $this->ustatus = $this->selectedJob->status;

            $jobRequirements = JobRequirement::where('job_id', $this->selectedJob->id)->get();

            if($jobRequirements->isNotEmpty()){
                foreach($jobRequirements as $requirement){
                    if($requirement->type == 'Role' && $requirement->name){
                        $this->roles[] = $requirement->name;
                    }
    
                    if($requirement->type == 'step_up' && $requirement->name){
                        $this->stepup[] = $requirement->name;
                    }
    
                    if($requirement->type == 'education' && $requirement->name){
                        $this->qualifications[] = $requirement->name;
                    }
    
                    if($requirement->type == 'professional_skill' && $requirement->name){
                        $this->skills[] = $requirement->name;
                    }
    
                    if($requirement->type == 'Minimum_Tenure' && $requirement->name){
                        $this->min_exp = $requirement->name;
                    }
    
                    if($requirement->type == 'location' && $requirement->name){
                        $this->selectedCountries[] = $requirement->name;
                    }
    
                    if($requirement->type == 'Gender' && $requirement->name){
                        $this->gender = $requirement->name;
                    }
    
                    if($requirement->type == 'Company' && $requirement->name){
                        $this->selectedCompanies[] = $requirement->name;
                    }

                    if($requirement->type == 'Industry' && $requirement->name){
                        $this->selectedIndustries[] = $requirement->name;
                    }

                    // if($requirement->type == 'Sector' && $requirement->name){
                    //     $this->selectedSectors[] = $requirement->name;
                    // }
                }
            }
        }

        $colleagues = User::where('company_id', $user[0]->company_id)
        ->where('id', '!=', $user[0]->id)
        ->get()
        ->map(function ($user) {
            return [
                'value' => $user->id,
                'label' => $user->name,
            ];
        })
        ->toArray();
        
        return view('livewire.job-table', compact('jobs', 'user', 'pipelineCountsMap', 'jobCountsMap', 'colleagues'));
    }

    protected function filteredCountries()
    {
        return array_filter($this->availableCountries, function ($country) {
            return stripos($country, $this->searchCountry) !== false;
        });
    }


    public function Createjob()
    {
        // Define validation rules
        $rules = [
            'name' => 'required|string',
            'descriptions' => 'required|string'
        ];

        // Validate the data
        $validator = Validator::make($this->validate($rules), $rules);

        // Check if validation fails
        if ($validator->fails()) {
            // Reset step to 1 if validation fails
            $this->step = 1;
            return;
        }
        // Get the user if for the individual in the current session
        $user = auth()->user();

        //dd('form triggeering function');

        // Add the job details to the jobs table
        $jobData = [
            'name'               => $this->name,
            'description'        => $this->descriptions,
            'minimum_experience' => $this->min_exp,
            'step_up'            => "1",
            'ethnicity'          => $this->ethnicity,
            'age'                => 0,
            'status'             => "Draft",
            'user_id'            => $user->id,
            'candidate_status'   => "Nothing",
        ];
        // To store the collegues or contributors
        if (!empty($this->selectedColleagues)) {
            $jobData["shared_with"] = json_encode($this->selectedColleagues);
        }
        $job = Job::create($jobData);


        if (!empty($this->selectedColleagues)) {
            foreach ($this->selectedColleagues as $colleague) {
                Notifications::create([
                    'type'              => "Job_Shared",
                    'plan_id'           => $job->id,
                    'entity_name'       => $this->name,
                    'description'       => "{$user->name} has shared job {$job->name} with you.",
                    'user_id'           => $colleague,
                    'user_company'      => $user->company_id
                ]);
            }
        
        }


        // Create the notification that a job has been created
        Notifications::create([
            'type'              => "job_Created",
            'job_id'            => $job->id,
            'entity_name'       => $this->name,
            'description'       => $this->descriptions,
            'user_id'           => $user->id,
            'user_company'      => $user->company_id
        ]);


        //------------------  Success Requirements Table ---------------//
        
        // Roles are seperated by commas this expands them into a list ** this field is mandatory **
        foreach ($this->newSkillData['targetRoles'] as $role) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($role),
                'type' => "Role",
            ]);
        }
                    // According to the new UI, We are not taking it from user
                    // // Getting any Functions
                    // if ($this->function) {
                    //     $functions = explode(',', $this->function);
                    //     foreach ($functions as $function) {
                    //         $function = trim($function);
                    //         JobRequirement::create([
                    //             'job_id' => $job->id,
                    //             'name' => $function,
                    //             'type' => "Function"
                    //         ]);
                    //     }
                    // }

                    // According to the new UI, We are not taking it from user
                    // // Getting any Division
                    // if ($this->division) {
                    //     $divisions = explode(',', $this->division);
                    //     foreach ($divisions as $division) {
                    //         $division = trim($division);
                    //         JobRequirement::create([
                    //             'job_id' => $job->id,
                    //             'name' => $division,
                    //             'type' => "Division"
                    //         ]);
                    //     }
                    // }

        // Getting any step_up candidates
        foreach ($this->newSkillData['stepUpCandidate'] as $stepUpCandidate) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($stepUpCandidate),
                'type' => "step_up"
            ]);
        }

        // Getting keyword
        foreach ($this->newSkillData['keyword'] as $keyword) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($keyword),
                'type' => "keyword"
            ]);
        }

        // Get any tenure requirements
        if ($this->min_exp) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => $this->min_exp,
                'type' => "Minimum_Tenure"
            ]);
        }

        if(!empty($this->selectedCompanies)){
            foreach($this->selectedCompanies as $cr){
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name'    => trim($cr),
                    'type'    => 'Company',
                ]);
            }
        }

        if($this->gender){
            JobRequirement::create([
                'job_id' => $job->id,
                'name'    => trim($this->gender),
                'type'    => 'Gender',
            ]);
        }

        // Get any another skills into the requirements table
        foreach ($this->newSkillData['skills'] as $skill) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($skill),
                'type' => "professional_skill"
            ]);
        }

        // This will be use for qualifications
        // Getting any Educational Qualifications Requirements
        foreach ($this->newSkillData['qualifications'] as $qualification) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($qualification),
                'type' => "education"
            ]);
        }

        // Getting any location requirements
        if (!empty($this->selectedCountries)) {
            foreach($this->selectedCountries as $country) {
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name' => $country,
                    'type' => "location"
                ]);
            }
        }

        // if (!empty($this->selectedSectors)) {
        //     foreach ($this->selectedSectors as $sector) {
        //         JobRequirement::create([
        //             'job_id'  => $job->id,
        //             'name'    => trim($sector),
        //             'type'    => 'Sector',
        //         ]);
        //     }
        // }

        if (!empty($this->selectedIndustries)) {
            foreach ($this->selectedIndustries as $industry) {
                JobRequirement::create([
                    'job_id'  => $job->id,
                    'name'    => trim($industry),
                    'type'    => 'Industry',
                ]);
            }
        }

        //------------------------ Make the pipeline table -------------------//
        
        //---- Get the data ready for the scoring ---//

        // Roles list
        $rawRoles = $this->newSkillData['targetRoles'];
        $proles = array_map('trim', $rawRoles);

        // Division list
        $rawDivs = $this->division;
        $pDivs = array_map('trim', explode(',', $rawDivs));

        // Function list
        $rawFunc = $this->function;
        $pFuncs = array_map('trim', explode(',', $rawFunc));

        // Step-up List
        $rawsteps = $this->newSkillData['stepUpCandidate'];
        $psteps = array_map('trim', $rawsteps);

        $rawKeyword = $this->newSkillData['keyword'];
        $pKeyword = array_map('trim', $rawKeyword);

        // Qualifcications list
        $rawEdQual = $this->newSkillData['qualifications'];
        $pEdQual = array_map('trim', $rawEdQual);

        // Location list
        $ploc = $this->selectedCountries;

        // Skills List
        $rawskill = $this->newSkillData['skills'];
        $pskills = array_map('trim', $rawskill);
        $skillCount = count($pskills);

        //See if a gender was chosen
        if($this->gender == 'Female'){
            $gen = 'Female';
        }
        elseif($this->gender == 'Male'){
            $gen = 'Male';
        }
        else{
            $gen = '';
        }

        //--------------- Start the filtering for the pipeline --------------//

        
        $scountry = $this->selectedCountries;

        // Getting the array for the companies using the companies, sector and industry 
        $filteredCompanyIdsArr = [];
        // if (!empty($this->selectedSectors) && !empty($this->selectedIndustries)) {
        //     $filteredCompanyIdsArr = Company::whereIn('industry', $this->selectedIndustries)
        //                                         ->whereIn('sector', $this->selectedSectors)
        //                                         ->pluck('id')
        //                                         ->toArray();
        // }
        // else if (!empty($this->selectedSectors)) {
        //     $filteredCompanyIdsArr = Company::whereIn('sector', $this->selectedSectors)
        //                                     ->pluck('id')
        //                                     ->toArray();
        // }
        // else if (!empty($this->selectedIndustries)) {
        if (!empty($this->selectedIndustries)) {
            $filteredCompanyIdsArr = Company::whereIn('industry', $this->selectedIndustries)
                                                ->pluck('id')
                                                ->toArray();
        }

        // This will filter the People table where the roles, gender, company_name, country and city 
        if(!empty($proles) || !empty($this->selectedCompanies) || $gen != '' || !empty($scountry)){
            $filteredPeople = People::query()
                                    ->where('status','!=','Submitted')
                                    ->where('status','!=','Reported')
                                    ->where('company_id','!=',$this->user->company_id)
                                    ->where(function ($query) use ($proles, $gen, $scountry, $filteredCompanyIdsArr) {
                                        // The statement below will filter by the company_name
                                        if(!empty($proles)){
                                            $query->whereIn('latest_role', $proles);
                                        }
                                        // The statement below will filter by the company_name
                                        if (!empty($this->selectedCompanies))  {
                                            $query->WhereIn('company_name', $this->selectedCompanies);
                                        }
                                        else if(count($filteredCompanyIdsArr) > 0){
                                            $query->WhereIn('company_id', $filteredCompanyIdsArr);
                                        }
                                        if (!empty($this->interestedCompaniesarray)){
                                            $query->WhereIn('company_id',$this->interestedCompaniesarray);
                                        }
                                        if ($gen != '') {
                                            $query->where('gender', $gen);
                                        }
                                        if (!empty($scountry)) {
                                            $query->WhereIn('country', $scountry);
                                        }
                                    })
                                    ->get();

            $filteredPeople = $filteredPeople->map(function($item){
                $item['role_score'] = 1;
                return $item;
            });
        }
        else {
            $filteredPeople = new Collection();
        }

        //dd($filteredPeople);

        $filteredPeopleidslvl1 = $filteredPeople->pluck('id');

        /* If a step up candidate has been proposed the system will only search for step candidates as well ensuring that
           people already identified from roles do not come through and uses the same filtering logic as roles
        */
        if($rawsteps){
            $sfilteredPeople = People::query()
            ->where('status','!=','Submitted')
            ->where('status','!=','Reported')
            ->where('company_id','!=',$this->user->company_id)
            ->whereNotIn('id',$filteredPeopleidslvl1)
            ->when($rawsteps, function($query) use ($psteps, $gen, $scountry, $filteredCompanyIdsArr) {
                $query->whereIn('latest_role', $psteps);
                if ($this->gender !== '') {$query->where('gender', $gen);}
                if (!empty($this->selectedCompanies))  {
                    $query->WhereIn('company_name', $this->selectedCompanies);
                }
                else if(count($filteredCompanyIdsArr) > 0){
                    $query->WhereIn('company_id', $filteredCompanyIdsArr);
                }
                if (!empty($this->interestedCompaniesarray)){
                    $query->WhereIn('company_id',$this->interestedCompaniesarray);
                }
                if (!empty($scountry)) {$query->WhereIn('country', $scountry);}
                if (!empty($scities)) {$query->WhereIn('city', $scities);}
            })
            ->get();
    
            $sfilteredPeople = $sfilteredPeople->map(function($item){
                $item['role_score'] = 0.75;
                return $item;
            });

            //dd($sfilteredPeople);
        
            $sfilteredPeopleidslvl1 = $sfilteredPeople->pluck('id');
        }
        else {
            $sfilteredPeople = [];
            $sfilteredPeopleidslvl1 =[];
        }
        

        if (!empty($pKeyword)) {
            $kfilteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id','!=',$this->user->company_id)
                ->whereNotIn('id', $filteredPeopleidslvl1)
                ->whereNotIn('id', $sfilteredPeopleidslvl1)
                ->when($rawsteps !== "", function ($query) use ($pKeyword, $gen, $scountry, $filteredCompanyIdsArr) {
                    $query->where(function ($subquery) use ($pKeyword) {
                        // Loop through each keyword and add an orWhere clause
                        foreach ($pKeyword as $keyword) {
                            $subquery->orWhere('latest_role', 'like', '%' . $keyword . '%');
                        }
                    });

                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->whereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                    if (!empty($this->interestedCompaniesarray)){
                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                    }
                    if (!empty($scountry)) {
                        $query->whereIn('country', $scountry);
                    }
                    // if (!empty($scities)) {
                    //     $query->whereIn('city', $scities);
                    // }
                })
                ->get();
            $kfilteredPeople = $kfilteredPeople->map(function ($item) {
                $item['role_score'] = 0.25;
                return $item;
            });

            //dd($sfilteredPeople);

            $kfilteredPeopleidslvl1 = $kfilteredPeople->pluck('id');
        } else {
            $kfilteredPeople = [];
            $kfilteredPeopleidslvl1 = [];
        }


        // May be relevant candidates
        $career_history_filtered = CareerHistories::query()
            ->whereNotIn('id',$filteredPeopleidslvl1)
            ->whereNotIn('id',$sfilteredPeopleidslvl1)
            ->whereNotIn('id',$kfilteredPeopleidslvl1)
            ->where(function ($query) use ($proles, $psteps) {
                $query->WhereIn('role', $proles)
                      ->orWhereIn('role', $psteps);
            })
            ->get();
        
        // First, get the relevant IDs from $career_history_filtered
        $filteredCareerHistoryIds = $career_history_filtered->pluck('people_id');

        if($filteredCareerHistoryIds !== null){
            // Then, use these IDs to filter the people table
            $careerPeople = People::query()
                            ->whereIn('id', $filteredCareerHistoryIds)
                            ->where('company_id','!=',$this->user->company_id)
                            ->when($filteredCareerHistoryIds  !== null, function($query) use ($proles, $gen, $scountry, $filteredCompanyIdsArr) {
                                if ($this->gender !== '') {$query->where('gender', $gen);}
                                if (!empty($scountry)) {$query->WhereIn('country', $scountry);}
                                if (!empty($this->selectedCompanies))  {
                                    $query->WhereIn('company_name', $this->selectedCompanies);
                                }
                                else if(count($filteredCompanyIdsArr) > 0){
                                    $query->WhereIn('company_id', $filteredCompanyIdsArr);
                                }
                                if (!empty($this->interestedCompaniesarray)){
                                    $query->WhereIn('company_id',$this->interestedCompaniesarray);
                                }
                            })
                            ->get();

            $careerPeople = $careerPeople->map(function($item) {
                $item['role_score'] = 0.5;
                return $item;
            });

            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
            $filteredPeople = $filteredPeople->concat($careerPeople);
            $filteredPeople = $filteredPeople->concat($kfilteredPeople);
        }
        else{
            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
        }
        
        //dd($filteredPeople);

        //------------------------------  Make all the tables --------------------------------//

        if($filteredPeople->count() >0){
            $filteredPeople->each(function ($person) use ($job, $user) {
                pipeline::create([
                    'job_id'            => $job->id,
                    'user_id'            => $user->id,
                    'people_id'          => $person->id,
                    'first_name'         => $person->forename,
                    'last_name'          => $person->surname,
                    'middle_name'        => $person->middle_name,
                    'other_name'         => $person->other_name,
                    'gender'             => $person->gender,
                    'diverse'            => $person->diverse,
                    'location'           => $person->country,
                    'country'            => $person->country,
                    'city'               => $person->city,
                    'linkedinURL'        => $person->linkedinURL,
                    'latest_role'        => $person->latest_role,
                    'company_id'         => $person->company_id,
                    'company_name'       => $person->company_name,
                    'start_date'         => $person->start_date,
                    'end_date'           => $person->end_date,
                    'tenure'             => $person->tenure,
                    'function'           => $person->function,
                    'division'           => $person->division,
                    'summary'            => $person->summary,
                    'other_tags'         => $person->other_tags,
                    'seniority'          => $person->seniority,
                    'career_history'     => $person->career_history,
                    'exco'               => $person->exco,
                    'educational_history'=> $person->educational_history,
                    'skills'             => $person->skills,
                    'languages'          => $person->languages,
                    'skills_match'       => $person->skill_score,
                    'readiness'          => $person->readiness,
                    'status'             => "approved",
                    'education_match'    => 0,
                    'location_match'     => 0,
                    'role_match'         => 0,
                    'gender_match'       => 0,
                    'tenure_match'       => 0,
                    'people_type'        => 'External-System'
                ]);
            });
        }

        $this->step++;

        $this->job = $job;
        
        $this->newjob = $job->id;

        $this->reset(['name', 'roles', 'qualifications', 'skills', 'division','function', 'stepup', 'descriptions', 'min_exp','education','location','gender','ethnicity','selectedCountries','selectedCompanies', 'selectedIndustries', 'newSkillData']);
    
        $this->newSkillData = [
            'qualifications' => [],
            'skills' => [],
            'targetRoles' => [],
            'stepUpCandidate' => [],
            'keyword' => []
        ];
    }

    public function CreatejobTravel()
    {
        
        // Get the user if for the individual in the current session
        $user = auth()->user();

        //dd('form triggeering function');

        // Add the job details to the jobs table
        $job = Job::create([
            'name'               => $this->name,
            'description'        => $this->descriptions,
            'minimum_experience' => $this->min_exp,
            'step_up'            => "1",
            'ethnicity'          => $this->ethnicity,
            'age'                => 0,
            'status'             => "Draft",
            'candidate_status'   => "Nothing",
            'user_id'            => $user->id
        ]);


        // Create the notification that a job has been created
        Notifications::create([
            'type'              => "job_Created",
            'job_id'           => $job->id,
            'entity_name'       => $this->name,
            'description'       => $this->descriptions,
            'user_id'           => $user->id,
            'user_company'      => $user->company_id
        ]);


        //------------------  Success Requirements Table ---------------//
        
        // Roles are seperated by commas this expands them into a list ** this field is mandatory **
        if($this->roles){
            foreach ($this->roles as $role) {
                $role = trim($role);
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name' => $role,
                    'type' => "Role",
                ]);
            }
        }

        // Getting any Functions
        if ($this->function) {
            $functions = explode(',', $this->function);
            foreach ($functions as $function) {
                $function = trim($function);
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name' => $function,
                    'type' => "Function"
                ]);
            }
        }

        // Getting any Division
        if ($this->division) {
            $divisions = explode(',', $this->division);
            foreach ($divisions as $division) {
                $division = trim($division);
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name' => $division,
                    'type' => "Division"
                ]);
            }
        }

        // Getting any step_up candidates
        if ($this->stepup) {
            foreach ($this->stepup as $step_up) {
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name' => trim($step_up),
                    'type' => "step_up"
                ]);
            }
        }

        // Getting any Division
        if ($this->division) {
            $divisions = explode(',', $this->division);
            foreach ($divisions as $division) {
                $division = trim($division);
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name' => $division,
                    'type' => "Division"
                ]);
            }
        }

        // Get any tenure requirements
        if ($this->min_exp) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => $this->min_exp,
                'type' => "Minimum_Tenure"
            ]);
        }

        // Getting any Educational Qualifications Requirements
        if ($this->education) {
            $educations = explode(',', $this->education);
            foreach ($educations as $education) {
                $education = trim($education);
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name' => $education,
                    'type' => "education"
                ]);
            }
        }

        // Getting any location requirements
        if ($this->location) {
            $locations = explode(',', $this->location);
            foreach ($locations as $location) {
                $location = trim($location);
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name' => $location,
                    'type' => "location"
                ]);
            }
        }

        // Get any another skills into the requirements table
        if ($this->skills) {
            foreach ($this->skills as $skill) {
                $skill = trim($skill);
                JobRequirement::create([
                    'job_id' => $job->id,
                    'name' => $skill,
                    'type' => "professional_skill"
                ]);
            }
        }

        //------------------------ Make the pipeline table -------------------//
        
        //---- Get the data ready for the scoring ---//
        
        // Roles list
        $rawRoles = $this->roles;
        $proles = array_map('trim', explode(',', $rawRoles));
        //dd($proles);

        // Function list
        $rawFunc = $this->function;
        $pFuncs = array_map('trim', explode(',', $rawFunc));

        // Step-up List
        $rawsteps = $this->stepup;
        $psteps = array_map('trim', explode(',', $rawsteps));

        // Qualifcications list
        $rawEdQual = $this->education;
        $pEdQual = array_map('trim', explode(',', $rawEdQual));

        // Location list
        $ploc = $this->selectedCountries;

        // Skills List
        $rawskill = $this->skills;
        $pskills = array_map('trim', explode(',', $rawskill));
        $skillCount = count($pskills);

        //See if a gender was chosen
        if($this->gender == 1){
            $gen = 'Female';
        }
        elseif($this->gender == 2){
            $gen = 'Male';
        }
        else{
            $gen = '';
        }

        //--------------- Start the filtering for the pipeline --------------//
        $scountry = $this->selectedCountries;
        // This will filter the People table where the roles, gender, company_name, country and city 
        $filteredPeople = People::query()
        ->where('status','!=','Submitted')
        ->where('status','!=','Reported')
        ->where('company_id','!=',$this->user->company_id)
        ->where(function ($query) use ($proles, $gen, $scountry) {
            $query->whereIn('latest_role', $proles);
            // The statement below will filter by the company_name
            if (!empty($this->selectedCompanies))  {$query->WhereIn('company_name', $this->selectedCompanies);}
            if ($gen != '') {$query->where('gender', $gen);}
            if (!empty($scountry)) {$query->WhereIn('country', $scountry);}
        })
        ->get();
        
        $filteredPeople = $filteredPeople->map(function($item){
            $item['role_score'] = 1;
            return $item;
        });

        //dd($filteredPeople);

        $filteredPeopleidslvl1 = $filteredPeople->pluck('id');

        /* If a step up candidate has been proposed the system will only search for step candidates as well ensuring that
           people already identified from roles do not come through and uses the same filtering logic as roles
        */
        if($this->stepup){
            $sfilteredPeople = People::query()
            ->where('status','!=','Submitted')
            ->where('status','!=','Reported')
            ->where('company_id','!=',$this->user->company_id)
            ->whereNotIn('id',$filteredPeopleidslvl1)
            ->when($this->stepup, function($query) use ($psteps, $gen, $scountry) {
                $query->whereIn('latest_role', $psteps);
                if ($gen != '') {$query->where('gender', $gen);}
                if (!empty($this->selectedCompanies))  {$query->WhereIn('company_name', $this->selectedCompanies);}
                if (!empty($scountry)) {$query->WhereIn('country', $scountry);}
                })
            ->get();
    
        $sfilteredPeople = $sfilteredPeople->map(function($item){
            $item['role_score'] = 0.75;
            return $item;
        });

        //dd($sfilteredPeople);
    
        $sfilteredPeopleidslvl1 = $sfilteredPeople->pluck('id');
        }
        else {
            $sfilteredPeople = [];
            $sfilteredPeopleidslvl1 =[];
        }
        
        // May be relevant candidates
        $career_history_filtered = CareerHistories::query()
            ->whereNotIn('id',$filteredPeopleidslvl1)
            ->whereNotIn('id',$sfilteredPeopleidslvl1)
            ->where(function ($query) use ($proles, $psteps) {
                $query->WhereIn('role', $proles)
                      ->orWhereIn('role', $psteps);
            })
            ->get();
        
        // First, get the relevant IDs from $career_history_filtered
        $filteredCareerHistoryIds = $career_history_filtered->pluck('people_id');

        if($filteredCareerHistoryIds !== null){
            // Then, use these IDs to filter the people table
            $careerPeople = People::query()
                            ->whereIn('id', $filteredCareerHistoryIds)
                            ->where('company_id','!=',$this->user->company_id)
                            ->when($filteredCareerHistoryIds  !== null, function($query) use ($proles, $gen, $scountry) {
                            if ($gen != '') {$query->where('gender', $gen);}
                            if (!empty($scountry)) {$query->WhereIn('country', $scountry);}
                            if (!empty($this->selectedCompanies))  {$query->WhereIn('company_name', $this->selectedCompanies);}
                            })
                            ->get();

            $careerPeople = $careerPeople->map(function($item) {
                $item['role_score'] = 0.5;
                return $item;
            });

            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
            $filteredPeople = $filteredPeople->concat($careerPeople);
        }
        else{
            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
        }

        

        //------------------------------  Make all the tables --------------------------------//

       // Put that data into the successionpeople list
        $filteredPeople->each(function ($person) use ($job, $user) {
            pipeline::create([
                'job_id'            => $job->id,
                'user_id'            => $user->id,
                'people_id'          => $person->id,
                'first_name'         => $person->forename,
                'last_name'          => $person->surname,
                'middle_name'        => $person->middle_name,
                'other_name'         => $person->other_name,
                'gender'             => $person->gender,
                'diverse'            => $person->diverse,
                'location'           => $person->location,
                'country'            => $person->country,
                'city'               => $person->city,
                'linkedinURL'        => $person->linkedinURL,
                'latest_role'        => $person->latest_role,
                'company_id'         => $person->company_id,
                'company_name'       => $person->company_name,
                'start_date'         => $person->start_date,
                'end_date'           => $person->end_date,
                'exco'               => $person->exco,
                'tenure'             => $person->tenure,
                'function'           => $person->function,
                'division'           => $person->division,
                'seniority'          => $person->seniority,
                'career_history'     => $person->career_history,
                'educational_history'=> $person->educational_history,
                'skills'             => $person->skills,
                'languages'          => $person->languages,
                'status'             => "approved",
                'people_type'        => 'External-System'

            ]);
        });

        $this->newjob = $job->id;

        return Redirect::to(route('job.long.index', ['job' => $job->id]));
    
    }

    public function updateJob()
    {
        $pipelinePeople = pipeline::where('job_id', $this->selectedJobId);
        $peopleIdsArr = $pipelinePeople->pluck('people_id')->toArray();
        //dd($this->ustatus);
        $this->selectedJob = Job::where('id', $this->selectedJobId)->first();
        $user = auth()->user();
        $job = $this->selectedJob;

        $dataToUpdate = [
            'name'               => $this->name,
            'description'        => $this->descriptions,
            'minimum_experience' => $this->min_exp,
            'ethnicity'          => $this->ethnicity,
            'status'             => $this->ustatus
        ];

        if (!empty($this->selectedColleagues)) {
            $dataToUpdate["shared_with"] = json_encode($this->selectedColleagues);
        }

        $job->update($dataToUpdate);

        //------------------  Job Requirements Table ---------------//
        
        JobRequirement::where('job_id', $job->id)->where('type', 'role')->delete();
        // Add the new requirements into success requirements
        foreach ($this->newSkillData['targetRoles'] as $role) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($role),
                'type' => "Role",
            ]);
        }

        JobRequirement::where('job_id', $job->id)->where('type', 'step_up')->delete();
        foreach ($this->newSkillData['stepUpCandidate'] as $stepUpCandidate) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($stepUpCandidate),
                'type' => "step_up"
            ]);
        }

        JobRequirement::where('job_id', $job->id)->where('type', 'keyword')->delete();
        foreach ($this->newSkillData['keyword'] as $keyword) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($keyword),
                'type' => "keyword"
            ]);
        }

        JobRequirement::where('job_id', $this->selectedJob->id)->where('type', 'Minimum_Tenure')->delete();
        if ($this->min_exp) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => $this->min_exp,
                'type' => "Minimum_Tenure"
            ]);
        }

        JobRequirement::where('job_id', $this->selectedJob->id)->where('type', 'Company')->delete();
        if(!empty($this->selectedCompanies)){
            $companyarray = [];
            foreach($this->selectedCompanies as $cr){
                $companyarray[] = [
                    'job_id' => $this->selectedJob->id,
                    'name'    => trim($cr),
                    'type'    => 'Company',
                ];
            }
            JobRequirement::insert($companyarray);
        }

        // JobRequirement::where('job_id', $this->selectedJob->id)->where('type', 'Sector')->delete();
        // if (!empty($this->selectedSectors)) {
        //     foreach ($this->selectedSectors as $sector) {
        //         JobRequirement::create([
        //             'job_id' => $this->selectedJob->id,
        //             'name'    => trim($sector),
        //             'type'    => 'Sector',
        //         ]);
        //     }
        // }

        JobRequirement::where('job_id', $this->selectedJob->id)->where('type', 'Industry')->delete();
        if (!empty($this->selectedIndustries)) {
            foreach ($this->selectedIndustries as $industry) {
                JobRequirement::create([
                    'job_id' => $this->selectedJob->id,
                    'name'    => trim($industry),
                    'type'    => 'Industry',
                ]);
            }
        }

        // Because a plan may have only one gender
        if($this->gender){
            JobRequirement::updateOrInsert(
                ['job_id'=>$this->selectedJob->id, 'type' => 'Gender'],
                [
                    'job_id' => $this->selectedJob->id,
                    'name'    => trim($this->gender),
                    'type'    => 'Gender',
                ]
            );
        }
        else {
            JobRequirement::where('job_id', $this->selectedJob->id)->where('type', 'Gender')->delete();
        }

        JobRequirement::where('job_id', $this->selectedJob->id)->where('type', 'location')->delete();
        if (!empty($this->selectedCountries)){
            $countriesarray = [];
            foreach($this->selectedCountries as $ucountry){
                $countriesarray = [
                    'job_id' => $this->selectedJob->id,
                    'name' => $ucountry,
                    'type' => 'location'
                    // Add other requirement properties here
                ];
            }
            JobRequirement::insert($countriesarray);
        }

        JobRequirement::where('job_id',  $job->id)->where('type', 'professional_skill')->delete();
        // Get any another skills into the requirements table
        foreach ($this->newSkillData['skills'] as $skill) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($skill),
                'type' => "professional_skill"
            ]);
        }

        JobRequirement::where('job_id', $job->id)->where('type', 'education')->delete();
        // This will be use for qualifications
        // Getting any Educational Qualifications Requirements
        foreach ($this->newSkillData['qualifications'] as $qualification) {
            JobRequirement::create([
                'job_id' => $job->id,
                'name' => trim($qualification),
                'type' => "education"
            ]);
        }

        //------------------------ Make the pipeline table -------------------//
        
        //---- Get the data ready for the scoring ---//
        //dd($this->updateRole);
        // Roles list
        $rawRoles = $this->newSkillData['targetRoles'];
        $proles = array_map('trim', $rawRoles);
        //dd($proles);

        // Function list
        $rawFunc = $this->function;
        $pFuncs = array_map('trim', explode(',', $rawFunc));

        // Step-up List
        $rawsteps = $this->newSkillData['stepUpCandidate'];
        $psteps = array_map('trim', $rawsteps);

        $rawKeyword = $this->newSkillData['keyword'];
        $pKeyword = array_map('trim', $rawKeyword);

        // Qualifcications list
        $rawEdQual = $this->newSkillData['qualifications'];
        $pEdQual = array_map('trim', $rawEdQual);

        // Location list
        $ploc = $this->selectedCountries;

        // Skills List
        $rawskill = $this->newSkillData['skills'];
        $pskills = array_map('trim', $rawskill);
        $skillCount = count($pskills);

        //dd("working");

        //dd($this->updateGender);
        //See if a gender was chosen
        if($this->gender == 'Female'){
            $gen = 'Female';
        }
        elseif($this->gender == 'Male'){
            $gen = 'Male';
        }
        else{
            $gen = '';
        }
        //dd($sgen);

        //--------------- Start the filtering for the pipeline --------------//
        $scountry = $this->selectedCountries;

        $filteredCompanyIdsArr = [];
        // if (!empty($this->selectedSectors) && !empty($this->selectedIndustries)) {
        //     $filteredCompanyIdsArr = Company::whereIn('industry', $this->selectedIndustries)
        //                                         ->whereIn('sector', $this->selectedSectors)
        //                                         ->pluck('id')
        //                                         ->toArray();
        // }
        // else if (!empty($this->selectedSectors)) {
        //     $filteredCompanyIdsArr = Company::whereIn('sector', $this->selectedSectors)
        //                                     ->pluck('id')
        //                                     ->toArray();
        // }
        // else if (!empty($this->selectedIndustries)) {
        if (!empty($this->selectedIndustries)) {
            $filteredCompanyIdsArr = Company::whereIn('industry', $this->selectedIndustries)
                                                ->pluck('id')
                                                ->toArray();
        }

        // This will filter the People table where the roles, gender, company_name, country and city 
        $filteredPeople = People::query()
                                ->where('status','!=','Submitted')
                                ->where('status','!=','Reported')
                                ->where('company_id','!=',$this->user->company_id)
                                ->whereNotIn('id', $peopleIdsArr)
                                ->where(function ($query) use ($proles, $gen, $scountry, $filteredCompanyIdsArr) {
                                    $query->whereIn('latest_role', $proles);
                                    // The statement below will filter by the company_name
                                    if (!empty($this->selectedCompanies))  {
                                        $query->WhereIn('company_name', $this->selectedCompanies);
                                    }
                                    else if(count($filteredCompanyIdsArr) > 0){
                                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                                    }
                                    if (!empty($this->interestedCompaniesarray)){
                                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                                    }
                                    if ($gen != '') {$query->where('gender', $gen);}
                                    if (!empty($scountry)) {$query->WhereIn('country', $scountry);}
                                    if (!empty($scities)) {$query->WhereIn('city', $scities);}
                                })
                                ->get();

        //dd($filteredPeople);

        $filteredPeopleidslvl1 = $filteredPeople->pluck('id');

        /* If a step up candidate has been proposed the system will only search for step candidates as well ensuring that
           people already identified from roles do not come through and uses the same filtering logic as roles
        */
        if($rawsteps){
            $sfilteredPeople = People::query()
            ->where('status','!=','Submitted')
            ->where('status','!=','Reported')
            ->where('company_id','!=',$this->user->company_id)
            ->whereNotIn('id',$filteredPeopleidslvl1)
            ->when($rawsteps, function($query) use ($psteps, $gen, $scountry, $filteredCompanyIdsArr) {
                $query->whereIn('latest_role', $psteps);
                if ($gen != '') {$query->where('gender', $gen);}
                if (!empty($this->selectedCompanies))  {
                    $query->WhereIn('company_name', $this->selectedCompanies);
                }
                else if(count($filteredCompanyIdsArr) > 0){
                    $query->WhereIn('company_id', $filteredCompanyIdsArr);
                }
                if (!empty($this->interestedCompaniesarray)){
                    $query->WhereIn('company_id',$this->interestedCompaniesarray);
                }
                if (!empty($scountry)) {$query->WhereIn('country', $scountry);}
                })
            ->get();
    
            $sfilteredPeople = $sfilteredPeople->map(function($item){
                $item['role_score'] = 0.75;
                return $item;
            });

            //dd($sfilteredPeople);
        
            $sfilteredPeopleidslvl1 = $sfilteredPeople->pluck('id');
        }
        else {
            $sfilteredPeople = [];
            $sfilteredPeopleidslvl1 =[];
        }
        
        if (!empty($pKeyword)) {
            $kfilteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id','!=',$this->user->company_id)
                ->whereNotIn('id', $filteredPeopleidslvl1)
                ->whereNotIn('id', $sfilteredPeopleidslvl1)
                ->when($rawsteps !== "", function ($query) use ($pKeyword, $gen, $scountry, $filteredCompanyIdsArr) {
                    $query->where(function ($subquery) use ($pKeyword) {
                        // Loop through each keyword and add an orWhere clause
                        foreach ($pKeyword as $keyword) {
                            $subquery->orWhere('latest_role', 'like', '%' . $keyword . '%');
                        }
                    });

                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->whereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                    if (!empty($this->interestedCompaniesarray)){
                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                    }
                    if (!empty($scountry)) {
                        $query->whereIn('country', $scountry);
                    }
                    // if (!empty($scities)) {
                    //     $query->whereIn('city', $scities);
                    // }
                })
                ->get();
            $kfilteredPeople = $kfilteredPeople->map(function ($item) {
                $item['role_score'] = 0.25;
                return $item;
            });

            //dd($sfilteredPeople);

            $kfilteredPeopleidslvl1 = $kfilteredPeople->pluck('id');
        } else {
            $kfilteredPeople = [];
            $kfilteredPeopleidslvl1 = [];
        }

        // May be relevant candidates
        $career_history_filtered = CareerHistories::query()
            ->whereNotIn('id',$filteredPeopleidslvl1)
            ->whereNotIn('id',$sfilteredPeopleidslvl1)
            ->whereNotIn('id',$kfilteredPeopleidslvl1)
            ->where(function ($query) use ($proles, $psteps) {
                $query->WhereIn('role', $proles)
                      ->orWhereIn('role', $psteps);
            })
            ->get();
        
        // First, get the relevant IDs from $career_history_filtered
        $filteredCareerHistoryIds = $career_history_filtered->pluck('people_id');

        if($filteredCareerHistoryIds !== null){
            // Then, use these IDs to filter the people table
            $careerPeople = People::query()
                            ->whereIn('id', $filteredCareerHistoryIds)
                            ->where('company_id','!=',$this->user->company_id)
                            ->when($filteredCareerHistoryIds  !== null, function($query) use ($proles, $gen, $scountry, $filteredCompanyIdsArr) {
                                if ($gen != '') {$query->where('gender', $gen);}
                                if (!empty($scountry)) {$query->WhereIn('country', $scountry);}
                                if (!empty($this->selectedCompanies))  {
                                    $query->WhereIn('company_name', $this->selectedCompanies);
                                }
                                else if(count($filteredCompanyIdsArr) > 0){
                                    $query->WhereIn('company_id', $filteredCompanyIdsArr);
                                }
                                if (!empty($this->interestedCompaniesarray)){
                                    $query->WhereIn('company_id',$this->interestedCompaniesarray);
                                }
                            })
                            ->get();

            $careerPeople = $careerPeople->map(function($item) {
                $item['role_score'] = 0.5;
                return $item;
            });

            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
            $filteredPeople = $filteredPeople->concat($careerPeople);
            $filteredPeople = $filteredPeople->concat($kfilteredPeople);
        }
        else{
            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
        }
    
        
        if($filteredPeople->count() > 0){

       // Put that data into the jobionpeople list
        $filteredPeople->each(function ($person) use ($job, $user) {
        pipeline::create([
            'job_id'            => $job->id,
            'user_id'            => $user->id,
            'people_id'          => $person->id,
            'first_name'         => $person->forename,
            'last_name'          => $person->surname,
            'middle_name'        => $person->middle_name,
            'other_name'         => $person->other_name,
            'gender'             => $person->gender,
            'diverse'            => $person->diverse,
            'location'           => $person->location,
            'country'            => $person->country,
            'city'               => $person->city,
            'linkedinURL'        => $person->linkedinURL,
            'latest_role'        => $person->latest_role,
            'company_id'         => $person->company_id,
            'company_name'       => $person->company_name,
            'start_date'         => $person->start_date,
            'end_date'           => $person->end_date,
            'exco'               => $person->exco,
            'tenure'             => $person->tenure,
            'function'           => $person->function,
            'division'           => $person->division,
            'seniority'          => $person->seniority,
            'career_history'     => $person->career_history,
            'educational_history'=> $person->educational_history,
            'skills'             => $person->skills,
            'languages'          => $person->languages,
            'summary'            => $person->summary,
            'status'             => "approved",
            'skills_match'       => 0,
            'education_match'    => 0,
            'location_match'     => 0,
            'role_match'         => 0,
            'gender_match'       => 0,
            'tenure_match'       => 0,
            'people_type'        => 'External-System' 
            ]);
        });
        }
            
        $this->reset(['name', 'roles', 'qualifications', 'skills', 'division','function', 'stepup', 'descriptions', 'min_exp','education','location','gender','ethnicity','selectedCountries','selectedCompanies', 'selectedIndustries', 'newSkillData']);

        $this->step++;
        
        $this->newSkillData = [
            'qualifications' => [],
            'skills' => [],
            'targetRoles' => [],
            'stepUpCandidate' => [],
            'keyword' => []
        ];
    }

    public function clearModel()
    {
        $this->name = ''; // Clear the 'name' property
        $this->type = '';
    }
    public function deletejob($id)
    {
        $requirement = job::findOrFail($id);

        if(auth()->user()->id != $requirement->user_id) {
            $this->dispatch('toast', "error", "You are not allowed to perform this action!");
            return;
        }
        
        $requirement->delete();
    }

    public function onSelectStatus($value)
    {   
        if (in_array($value, $this->selectedStatuses)) {
            // Remove value from array
            if($value == 'all'){
                $this->selectedStatuses = [];
            }
            else {
                $this->selectedStatuses = array_diff($this->selectedStatuses, [$value]);
            }
        } else {
            // Add value to array
            if($value == 'all'){
                $this->selectedStatuses = ['all', 'active', 'draft', 'closed'];
            }
            else {
                $this->selectedStatuses[] = $value;
            }
        }

        in_array('active', $this->selectedStatuses) ? $this->isActiveSelected = true : $this->isActiveSelected = false;
        in_array('draft', $this->selectedStatuses) ? $this->isDraftSelected = true : $this->isDraftSelected = false;
        in_array('closed', $this->selectedStatuses) ? $this->isClosedSelected = true : $this->isClosedSelected = false;
    }

    public function addSkillTag()
    {
        if (!empty($this->skillTag)) {
            $this->skills[] = $this->skillTag;
            $this->skillTag = '';
        }
    }

    public function removeSkillTag($index)
    {
        unset($this->skills[$index]);
        $this->skills = array_values($this->skills); // Re-index the array
    }

    public function addQualificationTag()
    {
        if (!empty($this->qualificationTag)) {
            $this->qualifications[] = $this->qualificationTag;
            $this->qualificationTag = '';
        }
    }

    public function removeQualificationTag($index)
    {
        unset($this->qualifications[$index]);
        $this->qualifications = array_values($this->qualifications); // Re-index the array
    }

    public function addStepupTag()
    {
        if (!empty($this->stepupTag)) {
            $this->stepup[] = $this->stepupTag;
            $this->stepupTag = '';
        }
    }

    public function removeStepupTag($index)
    {
        unset($this->stepup[$index]);
        $this->stepup = array_values($this->stepup); // Re-index the array
    }
    
    public function addRoleTag()
    {
        if (!empty($this->roleTag)) {
            $this->roles[] = $this->roleTag;
            $this->roleTag = '';
        }
    }

    public function removeRoleTag($index)
    {
        unset($this->roles[$index]);
        $this->roles = array_values($this->roles); // Re-index the array
    }

    public function changeMinimumTenure($flag)
    {   
        if($flag == 'decrease'){
            $this->min_exp = $this->min_exp <= 1 ? null : ($this->min_exp - 1);
        }
        else if($flag == 'increase'){
            $this->min_exp = !$this->min_exp ? 1 : ($this->min_exp + 1);
        }
    }

    public function createJobModalClosed()
    {
        $this->step = 1;
        $this->reset(['name', 'roles', 'qualifications', 'skills', 'division','function', 'stepup', 'descriptions', 'min_exp','education','location','gender','ethnicity','selectedCountries','selectedCompanies', 'selectedIndustries', 'newSkillData']);
    }

    public function updateJobModalClosed()
    {
        $this->step = 1;
        $this->selectedJobId = "";
        $this->selectedJob = "";
        $this->hasDataPopulated = false;
        $this->reset(['name', 'roles', 'qualifications', 'skills', 'division','function', 'stepup', 'descriptions', 'min_exp','education','location','gender','ethnicity','selectedCountries','selectedCompanies', 'selectedIndustries', 'newSkillData']);

    }

    // Update the pagination view after perPage property changes
    public function updatingPerPage($value)
    {
        $this->resetPage();
    }

    public function onSelectJob($id)
    {
        $this->selectedJobId = $id;
        $job = Job::find($id);
        $this->newSkillData = [
            'qualifications'  => JobRequirement::where(['job_id' => $this->selectedJobId, 'type' => 'education'])->pluck('name')->toArray(),
            'skills'          => JobRequirement::where(['job_id' => $this->selectedJobId, 'type' => 'professional_skill'])->pluck('name')->toArray(),
            'targetRoles'     => JobRequirement::where(['job_id' => $this->selectedJobId, 'type' => 'Role'])->pluck('name')->toArray(),
            'stepUpCandidate' => JobRequirement::where(['job_id' => $this->selectedJobId, 'type' => 'step_up'])->pluck('name')->toArray(),
            'keyword' => JobRequirement::where(['job_id' => $this->selectedJobId, 'type' => 'keyword'])->pluck('name')->toArray()
        ];
        $this->selectedColleagues = !empty($job->shared_with) ? json_decode($job->shared_with) : [];

    }
}

