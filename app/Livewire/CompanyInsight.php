<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Location;
use App\Models\People;
use App\Models\Account;
use App\Models\Company;
use App\Models\CareerHistories;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;


class CompanyInsight extends Component
{
    // Variables for filters
    public $filteredCompany;

    public $countries;
    public $selectedCountries = [];
    public $sector;
    public $selectedSector = [];
    public $industry;
    public $selectedIndustries = [];
    public $companies;
    public $companyidarrays = [];
    public $selectedCompanies = [];
    public $sortBy = 'name';
    public $sortDirection = 'desc';
    public $search = false;
    public $searchterm;

    //Variables to sort by

    //Variables for visualisation
    public $PeopleCount;
    public $GenderCount;
    public $averageTenure;
    public $faverageTenure;
    public $excoCount;
    public $fexcoCount;
    public $peopleInsystem;
    public $excoSum;
    public $companyCount;
    public $generalAverage;
    public $companiesWithMoreThanZeroPeople;


    public $genderLabels = [];
    public $genderData = [];
    public $peopleLabels = [];
    public $peopleData = [];
    public $locationLabels = [];
    public $locationData = [];
    public $netprofitLabels = [];
    public $netprofitData = [];
    public $companyLabels = [];
    public $companyData = [];
    public $functionLabels = [];
    public $functionData = [];
    public $divisionLabels = [];
    public $divisionData = [];
    public $roleLabels = [];
    public $roleData = [];
    public $tenureLabels = [];
    public $tenureData = [];


    use WithPagination;
    public function mount()
    {
        // Filters to get the sectors, industries and companies of interest
        $Accountinfo = Account::where("id","=",auth()->user()->account_id)->first();
        $InterestedSectors =  array_filter(explode(',',$Accountinfo->sector_interest));
        $interestedIndustries = array_filter(explode(',',$Accountinfo->industry_interest));
        $interestedCompanies = array_filter(explode(',',$Accountinfo->company_of_interest));
        //dd($interestedCompanies);
        //dd($InterestedSectors);

        $this->companyidarrays = Company::query()
                             ->where(function($query) use ($InterestedSectors, $interestedIndustries, $interestedCompanies){
                                if(!empty($InterestedSectors)) {
                                   $query->whereIn('sector',$InterestedSectors); 
                                }
                                if(!empty($interestedIndustries)){
                                    $query->whereIn('industry',$interestedIndustries); 
                                }
                                if(!empty($interestedCompanies)){
                                    $query->whereIn('name',$interestedCompanies);
                                    //dd("this is running"); 
                                }
                             })->pluck('id')->toArray();

                             
        //dd($this->companyidarrays);
        $companyidarrays = $this->companyidarrays;
        //dd($companyidarrays);

        $PeopleCount = People::query()
        ->where('status','!=','Submitted')
        ->where('status','!=','Reported')
        ->where(function($query) use ($companyidarrays){
            if(!empty($this->companyidarrays)){
                $query->whereIn('company_id',$companyidarrays);
            }
        })
            ->groupBy('company_id')
            ->selectRaw('people.company_id, COUNT(people.id) as PeopleCount')
            ->having('PeopleCount', '>', 0)
            ->get();

        $this->PeopleCount = [];
        foreach ($PeopleCount as $result) {
            $this->PeopleCount[$result->company_id] = $result->PeopleCount;
        }
        // dd($this->PeopleCount);
        //dd($this->PeopleCount);
        $this->companiesWithMoreThanZeroPeople = $PeopleCount->pluck('company_id')->toArray();
        // dd($companiesWithMoreThanZeroPeople);

        /*$this->GenderCount = People::query()
                                    ->groupBy('company_id','gender')
                                    ->selectRaw('people.company_id, people.gender, COUNT(*) as GenderCount')
                                    ->get()
                                    ->toArray(); */
        //dd($this->GenderCount);

        $averageTenure = People::query()
            ->groupBy('company_id')
            ->selectRaw('people.company_id, AVG(people.tenure) as AverageTenure')
            ->get();

        $this->averageTenure = [];
        foreach ($averageTenure as $result) {
            $this->averageTenure[$result->company_id] = $result->AverageTenure;
        }

        //dd($this->averageTenure);

        $this->excoCount = DB::table('people')
            ->where('status','!=','Submitted')
            ->where('status','!=','Reported')
            ->where(function($query) use ($companyidarrays){
                if(!empty($this->companyidarrays)){
                    $query->whereIn('company_id',$companyidarrays);
                }
            })
            ->where('exco', 'Exco')
            ->groupBy('company_id')
            ->selectRaw('people.company_id, COUNT(*) as ExcoCount')
            ->pluck('ExcoCount','company_id');

            // dd($this->excoCount);
    }

    public function render()
    {
        $companyidarrays = $this->companyidarrays;

        // Query companies only once and apply all conditions upfront
        $companiesQuery = Company::when(!empty($companyidarrays), function ($query) use ($companyidarrays) {
            return $query->whereIn('id', $companyidarrays);
        })
        ->whereIn('id', $this->companiesWithMoreThanZeroPeople)
        ->where('id', '!=', 1); // Exclude company with id 1

        // If needed, apply search conditions directly in the query
        if ($this->search) {
            if ($this->selectedCompanies) {
                $companiesQuery->where('name', $this->selectedCompanies);
            }
            if ($this->selectedIndustries) {
                $companiesQuery->where('industry', $this->selectedIndustries);
            }
            // Uncomment if sector filter is needed
            if ($this->selectedSector) {
                $companiesQuery->where('sector', $this->selectedSector);
            }
            if ($this->selectedCountries) {
                $companiesQuery->where('corporate_hq_country', $this->selectedCountries);
            }
        }

        // Get the required fields at once for companies, sector, and industry
        $companiesData = $companiesQuery->distinct()->get(['id', 'name', 'sector', 'industry']);

        // Collect the distinct sectors and industries from the retrieved data
        $this->sector = $companiesData->pluck('sector')->unique()->map(fn($sector) => ['value' => $sector, 'label' => $sector])->toArray();
        $this->industry = $companiesData->pluck('industry')->unique()->map(fn($industry) => ['value' => $industry, 'label' => $industry])->toArray();

        // Mapping the company data for the results (with counts and tenure)
        $companiesData = $companiesData->map(function ($company) {
            $company->Peopleinsystem = $this->PeopleCount[$company->id] ?? 0;
            $company->average_tenure = $this->averageTenure[$company->id] ?? 0;
            $company->exco_count = $this->excoCount[$company->id] ?? 0;
            return $company;
        });

        // Sort the companies by Peopleinsystem
        $company = $companiesData->sortByDesc('Peopleinsystem');

        // dd($companies);
        if ($this->filteredCompany !== null) {
            //Visualisations are only for deep dives into companies so we'll start with the same type of if condition and get the Data and Labels
            $chosenCompany = Company::where('id', $this->filteredCompany)->get();
            $chosenPeople = People::where('company_id', $this->filteredCompany)->where('exco', 'Exco')
                // ->where('latest_role', 'like', '%chief%')
                ->get();
            // dd($chosenPeople);
            // Check if the chosenCompany and chosenPeople collections are not empty
            if ($chosenCompany->isNotEmpty() && $chosenPeople->isNotEmpty()) {
                // Assuming one company has multiple people
                $chosenCompany = $chosenCompany->map(function ($company) use ($chosenPeople) {
                    $company->people = $chosenPeople->map(function ($person) {
                        return [
                            'latest_role' => $person->latest_role,
                            'start_date' => Carbon::parse($person->start_date)->format('Y'),
                            'forename' => $person->forename,
                            'surname' => $person->surname,
                            'function' => $person->function,
                            'status' => $person->status,
                            'updated_at' => $person->updated_at
                        ];
                    })->all();

                    return $company;
                });
            }
            
            $PeopleMap = $this->PeopleCount;
            $excoMap = $this->excoCount;
            $tenureMap = $this->averageTenure;


            // Joining the maps back to the main table
            $chosenCompany = $chosenCompany->map(function ($comp) use ($PeopleMap) {
                $comp->Peopleinsystem = $PeopleMap[$comp->id] ?? 0;
                return $comp;
            });

            //dd($company[4]);

            $chosenCompany = $chosenCompany->map(function ($comp) use ($tenureMap) {
                $comp->average_tenure = $tenureMap[$comp->id] ?? 0;
                return $comp;
            });

            //dd($company[100]);

            $chosenCompany = $chosenCompany->map(function ($comp) use ($excoMap) {
                $comp->exco_count = $excoMap[$comp->id] ?? 0;
                return $comp;
            });
            // dd($chosenCompany);


            //------------------Gender Chart-------------------//
            $genderLabels = [];
            $genderData = [];

            $insightPeople = People::where('company_id', $this->filteredCompany)->get();
            //dd($insightPeople);

            $genderCountsData = People::query()
                ->groupBy('company_id', 'gender')
                ->where('company_id', $this->filteredCompany)
                ->selectRaw('people.gender as gender, COUNT(*) as PeopleCount')
                ->get();
            //dd($genderCountsData);


            //dd($peopleCountsData);
            foreach ($genderCountsData as $data) {
                $genderLabels[] = $data->gender;
                $genderData[] = $data->total_people;
            }
            //dd($genderLabels);

            // Un refactored code
            // $moverPeopleIds = People::where('status', 'Mover')->pluck('id')->toArray();
            // $popularDestinationsArr = [];
            // $popularSourcesArr = [];
            // foreach($moverPeopleIds as $moverPeopleId){
            //     $careerHistories = CareerHistories::where('people_id', $moverPeopleId)
            //                                         ->orderBy('start_date', 'desc')
            //                                         ->limit(2)
            //                                         ->get()
            //                                         ->toArray();
    
            //     if(count($careerHistories) > 0 && isset($careerHistories[0]) && isset($careerHistories[1])){
            //         $record1 = $careerHistories[0];
            //         $record2 = $careerHistories[1];
                    
            //         // Popular destination of each movers
            //         if(isset($record1['past_company_id'])){
            //             $popularDestination = CareerHistories::where('past_company_id', '!=', $record1['past_company_id'])
            //                                                     ->select('past_company_id', \DB::raw('COUNT(*) as count'))
            //                                                     ->groupBy('past_company_id')
            //                                                     ->orderBy('count', 'desc')
            //                                                     ->first();
            //             $popularDestinationsArr[] = $popularDestination ? $popularDestination->past_company_id : null;
            //         }
                    
            //         // Source of each movers
            //         if(isset($record2['past_company_id'])){
            //             $popularSource = CareerHistories::where('past_company_id', '!=', $record2['past_company_id'])
            //                                                     ->select('past_company_id', \DB::raw('COUNT(*) as count'))
            //                                                     ->groupBy('past_company_id')
            //                                                     ->orderBy('count', 'desc')
            //                                                     ->first();
            //             $popularSourcesArr[] = $popularSource ? $popularSource->past_company_id : null;
            //         }
            //     }
            // }
    
            // $isPopularDestination = false;
            // if(in_array($this->filteredCompany, $popularDestinationsArr)){
            //     $isPopularDestination = true;
            // }
    
            // $isPopularSource = false;
            // if(in_array($this->filteredCompany, $popularSourcesArr)){
            //     $isPopularSource = true;
            // }

            
            // Refactored code
            // // Step 1: Retrieve all mover people IDs
            // $moverPeopleIds = People::pluck('id')->toArray();

            // // Step 2: Retrieve all career histories for these movers
            // $careerHistories = CareerHistories::whereIn('people_id', $moverPeopleIds)
            //     ->orderBy('start_date', 'desc')
            //     ->get()
            //     ->groupBy('people_id');

            // // Step 3: Collect past_company_ids to exclude from popularity calculations
            // $excludedDestinations = [];
            // $excludedSources = [];

            // foreach ($careerHistories as $peopleId => $histories) {
            //     // Limit to most recent 2 records per people_id
            //     $recentHistories = $histories->take(2);

            //     if ($recentHistories->count() > 0) {
            //         $record1 = $recentHistories->first();
            //         if ($record1 && isset($record1['past_company_id'])) {
            //             $excludedDestinations[] = $record1['past_company_id'];
            //         }

            //         if ($recentHistories->count() > 1) {
            //             $record2 = $recentHistories->get(1);
            //             if ($record2 && isset($record2['past_company_id'])) {
            //                 $excludedSources[] = $record2['past_company_id'];
            //             }
            //         }
            //     }
            // }

            // // Ensure we are not querying with empty exclusions
            // if (!empty($excludedDestinations)) {
            //     // Step 4: Retrieve popular destinations, excluding the collected past_company_ids
            //     $popularDestinations = CareerHistories::whereNotIn('past_company_id', $excludedDestinations)
            //         ->select('past_company_id', \DB::raw('COUNT(*) as count'))
            //         ->groupBy('past_company_id')
            //         ->orderBy('count', 'desc')
            //         ->get()
            //         ->pluck('past_company_id')
            //         ->toArray();
            // } else {
            //     $popularDestinations = []; // No exclusions, so no popular destinations
            // }

            // if (!empty($excludedSources)) {
            //     // Step 5: Retrieve popular sources, excluding the collected past_company_ids
            //     $popularSources = CareerHistories::whereNotIn('past_company_id', $excludedSources)
            //         ->select('past_company_id', \DB::raw('COUNT(*) as count'))
            //         ->groupBy('past_company_id')
            //         ->orderBy('count', 'desc')
            //         ->get()
            //         ->pluck('past_company_id')
            //         ->toArray();
            // } else {
            //     $popularSources = []; // No exclusions, so no popular sources
            // }

            // // Step 6: Check if the filtered company is popular
            // $isPopularDestination = in_array($this->filteredCompany, $popularDestinations);
            // $isPopularSource = in_array($this->filteredCompany, $popularSources);

            $chosenCompany = $chosenCompany->map(function ($comp) use ($genderLabels, $genderData) {
                // Check if genderLabels and genderData are defined for each item
                $comp->genderLabels = $genderLabels ?? [];
                $comp->genderData = $genderData ?? [];
                // $comp->isPopularDestination = $isPopularDestination ?? false;
                // $comp->isPopularSource = $isPopularSource ?? false;
                return $comp;
            });


            //dd($chosenCompany);
        } else {
            $chosenCompany = null;
        }

        $this->peopleInsystem = $company->sum('Peopleinsystem');
        // dd($company);
        $this->companyCount = $company->count();
        $this->excoSum = $company->sum('exco_count');
        // dd($this->excoSum);
        if ($this->peopleInsystem === 0) {
            $this->generalAverage = 0;
        } else {
            $this->generalAverage = number_format($company->sum('average_tenure') / $company->sum('Peopleinsystem'), 2);
        }


        return view('livewire.company-insight', compact('company', 'chosenCompany'));
    }

    public function runfilters()
    {
        $this->search = true;
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = false;
        // Reset all form input properties
        $this->reset(['selectedCompanies', 'selectedIndustries', 'selectedSector', 'selectedCountries']);
    }

    public function addCompany($id)
    {
        //dd('working');
        $this->filteredCompany = $id;

        //dd($this->chosenCompany);

        //------------------Gender Chart-------------------//
        $this->genderLabels = [];
        $this->genderData = [];
        $genderCountsData = DB::table('people')
            ->select('gender', DB::raw('COUNT(*) as total_people'))
            ->where('company_id', $this->filteredCompany)
            ->groupBy('gender')
            ->get();
        //dd($peopleCountsData);
        foreach ($genderCountsData as $data) {
            $this->genderLabels[] = $data->gender;
            $this->genderData[] = $data->total_people;
        }

        //------------------Function Chart-------------------//
        $this->functionLabels = [];
        $this->functionData = [];
        $functionCountsData = DB::table('people')
            ->select('function', DB::raw('COUNT(*) as total_people'))
            ->where('company_id', '=', $this->filteredCompany)
            ->groupBy('function')
            ->orderBy('total_people','desc')
            ->limit(5)
            ->get();
        //dd($peopleCountsData);
        foreach ($functionCountsData as $data) {
            $this->functionLabels[] = $data->function;
            $this->functionData[] = $data->total_people;
        }

        //------------------Division Chart-------------------//
        $this->divisionLabels = [];
        $this->divisionData = [];
        $divisionCountsData = DB::table('people')
            ->select('exco', DB::raw('COUNT(*) as total_people'))
            ->where('company_id', '=', $this->filteredCompany)
            ->groupBy('exco')
            ->get();
        //dd($peopleCountsData);
        foreach ($divisionCountsData as $data) {
            $this->divisionLabels[] = $data->exco;
            $this->divisionData[] = $data->total_people;
        }

        //------------------Location Chart-------------------//
        $this->locationLabels = [];
        $this->locationData = [];
        $locationCountsData = DB::table('people')
            ->select('country', DB::raw('COUNT(*) as total_people'))
            ->where('company_id', '=', $this->filteredCompany)
            ->groupBy('country')
            ->orderBy('total_people','desc')
            ->limit(5)
            ->get();
        //dd($peopleCountsData);
        foreach ($locationCountsData as $data) {
            $this->locationLabels[] = $data->country;
            $this->locationData[] = $data->total_people;
        }

        //---------------ExcCount----------------------//
        $this->fexcoCount = DB::table('people')
            ->select('company_name', DB::raw('COUNT(*) as excoCount'))
            ->where('company_id', '=', $this->filteredCompany)
            ->where('exco', 'Exco')
            ->groupBy('company_name')
            ->pluck('excoCount');

        // dd($this->fexcoCount);

        //----------------Average Tenure---------------//
        $this->faverageTenure = DB::table('people')
            ->select('company_name', DB::raw('AVG(people.tenure) as AverageTenure'))
            ->where('company_id', '=', $this->filteredCompany)
            ->groupBy('company_name')
            ->pluck('AverageTenure');
        
        $this->dispatch('createCompanyData', $this->divisionData, $this->divisionLabels, $this->genderData, $this->genderLabels, $this->functionData, $this->functionLabels, $this->locationData, $this->locationLabels);
        // dd($this->divisionData, $this->divisionLabels);

    }


    public function Ordermytable($column)
    {
        if ($column === $this->sortBy) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }
}
