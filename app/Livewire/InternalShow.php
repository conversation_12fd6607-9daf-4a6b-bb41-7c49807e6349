<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;
use OpenAI\Laravel\Facades\OpenAI;
use App\Models\InternalPeople;
use App\Models\InternalSkills;
use App\Models\SuccessPeople;
use App\Models\SuccessSkills;
use App\Models\UserNotes;
use Livewire\WithFileUploads;
use App\Models\SuccessionPlan;
use App\Models\SuccessRequirements;
use App\Models\pipeline;
use App\Models\PlanScores;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class InternalShow extends Component
{
    public $topPerson;
    public $subordinate;
    public $plan;
    public $Curplan;
    public $id;
    public $individualPerson;
    public $individualSelected;
    public $individualPlans;
    public $individualSkills;
    public $individualSkillslabels = [];
    public $individualSkillsdata = [];
    public $individualCommon = [];
    public $individualSpecials = [];
    public $individualCerts =[];
    public $test;

    // Variables for updateInternal Candidate Form
    public $specialisedskills = [];
    public $commonskills = [];
    
    public $certifications = [];
    public $updateRole;
    public $updateFunction;
    public $updateDivision;
    public $updateReadiness;
    public $updateReportTo;
    public $updateSpecialisedSkills;
    public $updateCertifications;

    //Variables to add to plans
    public $selectedPlans =[];
    public $plans;

    public $individualNotes;
    public $newNotes;



    //protected $listeners = ['individualSelected'];
    
   

    public function mount($topPerson = null)
    {
        $this->id = $topPerson;
        $user = auth()->user();
        $this->plan = SuccessionPlan::where('user_id',$user->id)->get();
  
    }

    public function test() {

    }

    #[On('individualSelected')]
    public function render($id = null)
    {   
        if($this->topPerson === null) {
            $this->topPerson = $this->subordinate;
        }

        $user = auth()->user();

        $this->individualSelected = $id;

        if($this->individualSelected === $this->topPerson->id) {
            // This is the view for internal show where a person can view the details of a person
            $this->individualPerson = InternalPeople::where('id',$this->topPerson->id)->first();

            //dd($this->individualPerson);
            
            $EnrichedSuccessPeople = SuccessPeople::leftJoin('succession_plans','success_people.plan_id', '=','succession_plans.id')
            ->where('success_people.type','Internal')
            ->where('success_people.people_id',$this->topPerson->id)
            ->select('success_people.*','succession_plans.name')
            ->pluck('plan_id');

            $this->plans = SuccessionPlan::where('user_id',$user->id)
                                        ->whereNotIn('id',$EnrichedSuccessPeople)
                                        ->distinct()
                                        ->get()
                                        ->map(function ($plan) {
                                            return ['value' => $plan->id, 'label' => $plan->name];
                                        }) 
                                        ->toArray();
            //dd($this->plans);

            $this->plan = SuccessionPlan::whereNotIn('id',$EnrichedSuccessPeople)
                                        ->where('user_id',$user->id)
                                        ->get();

            $this->Curplan = SuccessionPlan::whereIn('id',$EnrichedSuccessPeople)
                                        ->where('user_id',$user->id)
                                        ->get();
            //dd($this->plan);   
            //dd($this->individualPlans);

            $individualSkills = InternalSkills::where('internal_people',$this->topPerson->id)->get();
            $this->individualSkills = InternalSkills::where('internal_people',$this->topPerson->id)->get();
            //dd($individualSkills);

            $this->individualCommon = $individualSkills->where('skill_type','Common');
            foreach($this->individualCommon as $indCom) {
                $this->commonskills[] = [ 'name'     => $indCom->skill_name,
                                         'rating'   => $indCom->skill_rating,
                                         'id'       => $indCom->id ];
            }

            $this->individualSpecials = $individualSkills->where('skill_type','Specialised');
            foreach($this->individualSpecials as $indSp) {
                $this->specialisedskills[] = [ 'name'     => $indSp->skill_name,
                                               'rating'   => $indSp->skill_rating,
                                               'id'       => $indSp->id ];
            }

            $this->individualCerts = $individualSkills->where('skill_type','Certification');
            foreach($this->individualCerts as $indCert) {
                $this->certifications[] = [    'name'     => $indCert->skill_name,
                                               'rating'   => $indCert->skill_rating,
                                               'id'       => $indCert->id ];
            }

            $this->individualSkillslabels = [];
            $this->individualSkillsdata = [];

            foreach ($individualSkills as $individualSkill) {
                $this->individualSkillslabels[] = $individualSkill->skill_name;
                $this->individualSkillsdata[]   = $individualSkill->skill_rating;
            }

            $this->individualNotes = UserNotes::where('entity_id', $this->topPerson->id)->get();
            //dd($this->individualNotes);
        }

        return view('livewire.internal-show');
    
    }

    public function addCommonSkill(){
        $this->commonskills[] = ['id' => '', 'name' => '', 'rating' => ''];
    }

    public function removeCommonSkill($index)
    {
        unset($this->commonskills[$index]);
        $this->commonskills = array_values($this->commonskills); // Reindex the array
    }

    public function addspecialisedSkill(){
        $this->specialisedskills[] = ['id'=>'', 'name' => '', 'rating' => ''];
    }

    public function removespecialisedSkill($index)
    {
        unset($this->specialisedskills[$index]);
        $this->specialisedskills = array_values($this->specialisedskills); // Reindex the array
    }

    public function addcertification(){
        $this->certifications[] = ['id'=>'', 'name' => '', 'rating' => ''];
    }

    public function removecertification($index)
    {
        unset($this->certification[$index]);
        $this->certification = array_values($this->certification); // Reindex the array
    }


    //------------- Update Internal Candidates --------------//
    public function updateInternalCandidate($id){
        //dd('working');
        $user = auth()->user();
        $internalPerson = InternalPeople::find($id);

        //Updating an individuals role, function and division
        // Check if the inputs are empty and use placeholders if necessary
        $role       = empty($this->updateRole)      ? $this->individualPerson->role         : $this->updateRole;
        $function   = empty($this->updateFunction)  ? $this->individualPerson->function     : $this->updateFunction;
        $division   = empty($this->updateDivision)  ? $this->individualPerson->division     : $this->updateDivision;
        $readiness  = empty($this->updateReadiness) ? $this->individualPerson->readiness    : $this->updateReadiness;

        if(empty($this->updateReportTo)){
            if($this->individualPerson->reports_to === null){
                $reportTo  =0;
            }
            else{
                $reportingTo = $this->individualPerson->reports_to;
            }
        }
        else{

            if($this->updateReportTo === 'NA'){
                $internalPerson->update([
                    'role'      => $role,
                    'function'  => $function,
                    'division'  => $division,
                    'readiness' => $readiness,
                    'reports_to'=> null
                ]);
            }

            else{
                $reportingTo = InternalPeople::where(function ($query) {
                    $query->whereIn('role', explode(' ', $this->reportTo))
                          ->orWhereIn('surname', explode(' ', $this->reportTo))
                          ->orWhereIn('forename', explode(' ', $this->reportTo));
                })->first();

                $internalPerson->update([
                    'role'      => $role,
                    'function'  => $function,
                    'division'  => $division,
                    'readiness' => $readiness,
                    'reports_to'=> $reportingTo->id
                ]);
            }
            
        }

        $internalPerson->update([
            'role'      => $role,
            'function'  => $function,
            'division'  => $division,
            'readiness' => $readiness,
        ]);

        //Updating an individuals skills
        //Common Skills
        if(!empty($this->commonskills)){
            foreach ($this->commonskills as $cskill){
                InternalSkills::updateOrCreate(
                    ['skill_name' => $cskill['name'], 'skill_rating' => $cskill['rating']],
                    ['internal_people'  => $id,
                     'company_id'       => $internalPerson->company_id,
                     'skill_type'   => 'Common']
                );
            }
        }

        //Specialised Skills
        if(!empty($this->specialisedskills)){
            foreach ($this->specialisedskills as $Sskill){
                InternalSkills::updateOrCreate(
                    ['skill_name' => $Sskill['name'], 'skill_rating' => $Sskill['rating']],
                    ['internal_people'  => $id,
                    'company_id'       => $internalPerson->company_id,
                    'skill_type'   => 'Specialised']
                );
            }
        }

        //Certification Skills
        if(!empty($this->certification)){
            foreach ($this->certification as $Cert){
                InternalSkills::updateOrCreate(
                    ['skill_name' => $Cert['name'], 'skill_rating' => $Cert['rating']],
                    ['internal_people'  => $id,
                    'company_id'       => $internalPerson->company_id,
                    'skill_type'   => 'Certification']
                );
            }

        }

    }

    public function addNotes($id){
        $user = auth()->user();

        UserNotes::create([
                            'entity_id' => $id,
                            'entity_type' => 'Internal_Person',
                            'Notes'       => $this->newNotes,
                            'author'      =>$user->id
        ]);

    }

    public function removeSkill($name) {
        dd('working');
        $internalskills = InternalSkills::where('internal_people',$this->individualPerson->id)
                                        ->where('name',$name)->delete();
    }

    public function addtoPlan() {

        $user = auth()->user();
        //dd($this->selectedPlans);

        foreach ($this->selectedPlans as $planI){
            //dd($planI);

            $planRequirements = SuccessRequirements::where('plan_id', $planI)->get();
            //dd($planRequirements);

            $planRole = SuccessRequirements::where('plan_id', $planI)
                                              ->where('type', 'Role')
                                              ->get();
            
            $planStep = SuccessRequirements::where('plan_id', $planI)
                                              ->where('type', 'step_up')
                                              ->get();
            $planSkills = SuccessRequirements::where('plan_id', $planI)
                            ->where('type', 'professional_skill')
                            ->get();
            $planGender = SuccessRequirements::where('plan_id', $planI)
                                            ->where('type', 'Gender')
                                            ->get();
            
            $planLocation = SuccessRequirements::where('plan_id', $planI)
                                            ->where('type', 'professional_skill')
                                            ->get();

            // Get the scores ready for pipeline and plans
            // Role Score
            $RoleScore=0;
            if($planRole->contains('name', $this->individualPerson->role))
            {
                $RoleScore = 1;
            } 
            else {
                if($planStep->contains('name', $this->individualPerson->role)) {
                    $RoleScore = 0.75;
                }
                else {
                    $RoleScore = 0;
                }
            }

            //Skill Score
            $SkillScore = 0;
            if(!empty($this->SuccessSkills) && $this->SuccessSkills->count() > 0){
                foreach ($this->individualSkills as $skill) {
                    foreach ($planSkills as $planSkill) {
                        if($skill->name == $planSkill->skill_name) {
                            $SkillScore++;
                            break;
                        }
                    }
                
                    $SkillScore /= $planSkills->count();
            }
        }

            
            //Gender Match Score
            $Gender_Match = 0;
            if ($planGender !== null && !$planGender->isEmpty()) {
                if ($planGender->contains('name', $this->individualPerson->gender)) {
                    $Gender_Match = 1;
                } else {
                    $Gender_Match = 0;
                }
            } else {
                $Gender_Match = 1;
            }
            //dd($Gender_Match);

            //Location Match
            $Location_Match = 0;
            if ($planLocation !== null && !$planLocation->isEmpty()) {
                if ($planLocation->contains('name', $this->individualPerson->location)) {
                    $Location_Match = 1;
                } else {
                    $Location_Match = 0;
                }
            } else {
                $Location_Match = 1;
            }

            //Tenure Match

            // Call GPT-3 to generate the headline
            $roleai     = $this->individualPerson->role;
            $locationai = $this->individualPerson->location;
            $functionai = $this->individualPerson->function;
            $divisionai = $this->individualPerson->division;

            $inputArray = [
                'role'     => $roleai,
                'location' =>$locationai,
                'function' => $functionai,
                'division' => $divisionai
                        ];
            
            // Create a formatted message for GPT-3
            $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
            foreach ($inputArray as $key => $value) {
                    $message .= "$key: \"$value\"\n";
                                }
                                        
            // Call GPT-3 to generate the headline
            $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
            ]);

            $generatedHeadline = $response->choices[0]->message->content;

            $newPipeline = pipeline::create([
                'plan_id'            => $planI,
                'user_id'            => $user->id,
                'people_id'          => $this->individualPerson->id,
                'headline'           => $generatedHeadline,
                'first_name'         => $this->individualPerson->forename,
                'last_name'          => $this->individualPerson->surname,
                'middle_name'        => $this->individualPerson->middle_name,
                'other_name'         => $this->individualPerson->other_name,
                'gender'             => $this->individualPerson->gender,
                'diverse'            => 1,
                'location'           => $this->individualPerson->location,
                'linkedinURL'        => 'NA',
                'latest_role'        => $this->individualPerson->role,
                'company_id'         => $this->individualPerson->company_id,
                'company_name'       => $this->individualPerson->company_name,
                'start_date'         => $this->individualPerson->start_date,
                'end_date'           => $this->individualPerson->end_date,
                'tenure'             => $this->individualPerson->tenure,
                'function'           => $this->individualPerson->function,
                'division'           => $this->individualPerson->division,
                'seniority'          => 'NA',
                'career_history'     => 'NA',
                'educational_history'=> 'NA',
                'skills'             => 'NA',
                'languages'          => 'NA',
                'skills_match'       => $SkillScore,
                'education_match'    => 0,
                'location_match'     => $Location_Match,
                'role_match'         => $RoleScore,
                'gender_match'       => $Gender_Match,
                'tenure_match'       => 1,
                'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
                'people_type'        => 'Internal-User'
            ]);   
            
            $SuccessPeople = SuccessPeople::create([
                'pipeline_id'        => $newPipeline->id,
                'plan_id'            => $planI,
                'user_id'            => $user->id,
                'people_id'          => $this->individualPerson->id,
                'headline'           => $generatedHeadline,
                'first_name'         => $this->individualPerson->forename,
                'last_name'          => $this->individualPerson->surname,
                'middle_name'        => $this->individualPerson->middle_name,
                'other_name'         => $this->individualPerson->other_name,
                'gender'             => $this->individualPerson->gender,
                'diverse'            => 1,
                'location'           => $this->individualPerson->location,
                'linkedinURL'        => 'NA',
                'latest_role'        => $this->individualPerson->role,
                'company_id'         => $this->individualPerson->company_id,
                'company_name'       => $this->individualPerson->company_name,
                'start_date'         => $this->individualPerson->start_date,
                'end_date'           => $this->individualPerson->end_date,
                'tenure'             => $this->individualPerson->tenure,
                'function'           => $this->individualPerson->function,
                'division'           => $this->individualPerson->division,
                'seniority'          => 'NA',
                'career_history'     => 'NA',
                'educational_history'=> 'NA',
                'skills'             => 'NA',
                'languages'          => 'NA',
                'skills_match'       => $SkillScore,
                'education_match'    => 0,
                'location_match'     => $Location_Match,
                'role_match'         => $RoleScore,
                'gender_match'       => $Gender_Match,
                'tenure_match'       => 1,
                'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
                'type'               => "Internal",
                'notes'              => "Enter notes here",
                'recruit'            => 1,
                  
            ]);

        //------------------- Get the new Gender Diversity Score -----------------//
        $femaleRatio = DB::table('success_people')
        ->select('plan_id', DB::raw('(SUM(gender = "female")/Count(*))*100 as female_ratio'))
        ->groupBy('plan_id')
        ->where('plan_id',$planI)
        ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id'=>$planI, 'metric_name' => 'Female-ratio'],
            ['score'=> $femaleRatio->female_ratio]
        );

        $maleRatio = DB::table('success_people')
        ->select('plan_id', DB::raw('(SUM(gender = "male")/Count(*))*100 as male_ratio'))
        ->groupBy('plan_id')
        ->first();
 

        PlanScores::updateOrInsert(
            ['succession_plan_id'=>$planI, 'metric_name' => 'Male-Ratio'],
            ['score'=> $maleRatio->male_ratio]
        );

        $InternalRatio = DB::table('success_people')
        ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
        ->groupBy('plan_id')
        ->where('plan_id',$planI)
        ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id'=>$planI, 'metric_name' => 'Internal-External Ratio'],
            ['score'=> $InternalRatio->internal_ratio]
        );

        $averageskillscore = DB::table('success_people')
        ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
        ->groupBy('plan_id')
        ->where('plan_id', $planI)
        ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id'=>$planI, 'metric_name' => 'Skill Score'],
            ['score'=> $averageskillscore->average_skill_score]
        );

        $averagetenurescore = DB::table('success_people')
        ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
        ->groupBy('plan_id')
        ->where('plan_id', $planI)
        ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id'=>$planI, 'metric_name' => 'Tenure Score'],
            ['score'=> $averagetenurescore->average_tenure_score]
        );

        // Add requirements for chart for skills radar
        
        }


    }


    public function removeaddtoPlan() {
        $this->selectedPlans=[];
    }


    public function resetInternalPerson(){
        //dd($this->individualSelected);
        $this->reset('individualPerson','individualSkillslabels','individualSkillsdata');
    }
}
