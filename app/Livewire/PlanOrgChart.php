<?php

namespace App\Livewire;

use App\Models\CareerHistories;
use App\Models\internal_career_histories;
use App\Models\InternalPeople;
use App\Models\PlanScores;
use App\Models\Recruitment;
use App\Models\RecruitmentPipeline;
use App\Models\RecruitmentStage;
use App\Models\Skills;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\SuccessSkills;
use App\Models\User;
use App\Models\UserNotes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class PlanOrgChart extends Component
{

    protected $listeners = [
        'modalClosed',
        'removeSuccessPerson',
        'approveSuccessPerson',
        'deleteUserNote',
        'viewDetailModalClosed', 
        'recruitModalClosed',
        'SaveProject',
    ];

    public $plan;
    public $user;
    public $successPeople;
    public $successpeople;
    public $successArray;
    public $pipelinepeople;
    //public $recruitmentProjectId;

    public $savedPeople = [];
    public $skills;
    public $SuccessSkills;

    public $peoplescareer;

    // Update Scores variables
    public $updatelocScore;
    public $updategenScore;
    public $updaterolScore;
    public $updateskillScore;
    public $updatetenureScore;
    public $filteredPnotes;
    public $successPersonNotes;
    public $successPersonSummary;
    public $selectedpersonId;
    public $interviewCount = 3;
    public $interviewfields = [];
    public $viewRootId = null;
    public $externalTree = [];
    public $internalTree = [];
    public $finalStructure = 0;

    public function mount() // Ensure you receive the id parameter
    {
        $user = auth()->user();
        $this->user = $user;

        $this->successPeople = SuccessPeople::where('plan_id', $this->plan->id)
            ->where('status', '!=', 'Proposed')
            ->whereNotNull('people_id') // Ensures the individual is tagged
            ->get();

        $plan = SuccessionPlan::select('succession_plans.*', 'internal_people.forename', 'internal_people.surname', 'internal_people.latest_role', 'internal_people.company_name', 'internal_people.linkedinURL')
            ->leftJoin('internal_people', 'succession_plans.tagged_individual', '=', 'internal_people.id')
            ->where('succession_plans.id', $this->plan->id)
            ->firstOrFail();    

        // Separate by type
        $externalList = $this->successPeople->where('type', 'External')->values();
        //dd($externalList);
        $internalList = $this->successPeople->where('type', 'Internal')->values();    

        // Generate final structure
        $this->externalTree = $this->buildLinearTree($externalList);
        $this->internalTree =  $this->buildLinearTree($internalList);

        $this->finalStructure = [
            'id' => $plan->tagged_individual,
            'people_id' => null,
            'first_name' => $plan->forename,
            'last_name' => $plan->surname,
            'latest_role' => $plan->latest_role,
            'linkedinURL' => null,
            'company_id' => null,
            'company_name' => $plan->company_name,
            'type' => 'root',
            'children' => array_filter([$this->externalTree, $this->internalTree]) // ignore nulls
        ];
    }

    // Helper to build nested tree
    function buildLinearTree($people)
    {
        $tree = null;

        // loop from end to beginning to wrap the next person as child
        for ($i = count($people) - 1; $i >= 0; $i--) {
            $person = $people[$i];
            $tree = [
                'id' => $person->id,
                'people_id' => $person->people_id,
                'first_name' => $person->first_name ?? '',
                'last_name' => $person->last_name ?? '',
                'latest_role' => $person->latest_role ?? '',
                'linkedinURL' => $person->linkedinURL ?? '',
                'company_id' => $person->company_id ?? '',
                'company_name' => $person->company_name ?? '',
                'type' => $person->type,
                'children' => $tree ? [ $tree ] : [],
            ];
        }

        return $tree;
    }

    public function render()
    {

        $this->SuccessSkills = SuccessSkills::where("succession_plan_id", $this->plan->id)
            ->get();

        $recruitmentProjectId = '';
		$planRecruitmentPipeline = RecruitmentPipeline::where('Plan_id', $this->plan->id)->first();
		if ($planRecruitmentPipeline) {
			$recruitmentProjectId = $planRecruitmentPipeline->recruitment_project_id;
		}

        //dd($recruitmentProjectId);
        //dd($this->plan);

        $plan = SuccessionPlan::select('succession_plans.*', 'internal_people.forename', 'internal_people.surname', 'internal_people.latest_role', 'internal_people.company_name', 'internal_people.linkedinURL')
                    ->leftJoin('internal_people', 'succession_plans.tagged_individual', '=', 'internal_people.id')
                    ->where('succession_plans.id', $this->plan->id)
                    ->firstOrFail();

        $this->plan = $plan;    
    
        // if($this->savedPeople){
        //     dd($plan);
        // }

        // Get the variables for looking at a specific individual
        $filteredPeople = null;
        if($this->savedPeople!=null){
            $filteredPeople =  SuccessPeople::rightJoin('succession_plans', 'success_people.plan_id', '=', 'succession_plans.id')
            ->leftJoin('people', 'success_people.people_id', '=', 'people.id')
            ->where('succession_plans.id', $this->plan->id)
            ->where('success_people.id', $this->savedPeople)
            ->get(['success_people.*', 'succession_plans.name as plan_name', 'succession_plans.description', 'people.company_name as current_company', 'people.latest_role as current_role']);

            // Get the variables for the visuals for an individual person
            if ($filteredPeople->isNotEmpty()) {
                $skills_list = $filteredPeople ? Skills::where('people_id', $filteredPeople[0]->people_id)->get() : collect();
            
                
                // Skills
                $scoreskills = $this->SuccessSkills->whereIn('success_people_id', $filteredPeople[0]->id);
                //dd($scoreskills);

                $groupedSkillScores = $scoreskills->groupBy('success_people_id');
                //dd($groupedSkillScores);

                $groupedSkillScores->transform(function ($item) {
                    return [

                        'skill_name' => $item->pluck('skill_name')->toArray(),
                        'scores' => $item->pluck('score')->toArray(),
                    ];
                });
                //dd($groupedSkillScores);

                // Loop through the plans and attach corresponding data from groupedPlanScores
                $filteredPeople->each(function ($person) use ($groupedSkillScores) {
                    $PersonId = $person->id; // Replace with the actual column name
                    if (isset($groupedSkillScores[$PersonId])) {
                        $person->scoreLabels = $groupedSkillScores[$PersonId]['skill_name'];
                        $person->scoreData = $groupedSkillScores[$PersonId]['scores'];
                    } else {
                        $person->scoreLabels = [];
                        $person->scoreData = [];
                    }
                });

                //Getting the career histories
                $filterExternal = $filteredPeople->pluck('people_id');
                $filteredPeople->each(function ($person) use ($skills_list) {
                    $person->skills_list = $skills_list ? $skills_list->toArray() : null;
                });   
                // Loop through the filtered people and attach corresponding data
                $filteredPeople->each(function ($person) {
                    // Initialize arrays to store match labels and scores
                    $matchLabels = [];
                    $matchData = [];

                    $matchLabels[] = 'Skills';  // Add label for skills
                    $matchData[] = $person->skills_match;

                    $matchLabels[] = 'Roles';
                    $matchData[] = $person->role_match;

                    $matchLabels[] = 'Location';
                    $matchData[] = $person->location_match;

                    $matchLabels[] = 'Gender';
                    $matchData[] = $person->gender_match;

                    $matchLabels[] = 'Tenure';
                    $matchData[] = $person->tenure_match;

                    // Assign the match_labels and match_data fields to the person
                    $person->match_labels = $matchLabels;
                    $person->match_data = $matchData;
                    
                });
            }
        }
        
        $user = $this->user;

        $colleagues = User::where('company_id', $user)
            ->get()
            ->map(function ($user) {
                return [
                    'value' => $user->id,
                    'label' => $user->name,
                ];
            })
            ->toArray();

            $internalPeoplesCount = InternalPeople::where('company_id', $this->user->company_id)->count();

            if ($this->savedPeople!=null) {
                $userNotes = UserNotes::select('user_notes.*', 'users.name as user_name')
                    ->where('entity_id', $this->savedPeople)
                    ->where('entity_type', 'success_person')
                    ->join('users', 'user_notes.author', '=', 'users.id')
                    ->orderBy('user_notes.id', 'desc')
                    ->get();
    
                $redSuc = SuccessPeople::where('id', $this->savedPeople)->first();
    
                $this->updategenScore = $this->updategenScore ?? $redSuc->gender_match;
                $this->updaterolScore = $this->updaterolScore ?? $redSuc->role_match;
                $this->updatetenureScore = $this->updatetenureScore ?? $redSuc->tenure_match;
                $this->updatelocScore = $this->updatelocScore ?? $redSuc->location_match;
                $this->updateskillScore = $this->updateskillScore ?? $redSuc->skills_match;
                // $this->filteredPnotes = $filteredPeople[0]->notes && $filteredPeople[0]->notes != '' && $filteredPeople[0]->notes != 'Enter notes here' ? $filteredPeople[0]->notes : null;
                $this->successPersonNotes = $userNotes;
                $this->successPersonSummary = $filteredPeople && isset($filteredPeople[0]) ? $filteredPeople[0]->summary : null;
            }    
      
        return view('livewire.plan-org-chart', compact('plan', 'recruitmentProjectId', 'filteredPeople', 'colleagues'));
    }

    // Dealing with Success People
    public function removeSuccessPerson($id)
    {
        $removalcandidate = SuccessPeople::findOrFail($id);

        $removalcandidate->delete();

        // Update the scores
        //------------------- Get the new Gender Diversity Score -----------------//
        $femaleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Female")/Count(*))*100 as female_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        if ($femaleRatio === null) {
            PlanScores::updateOrInsert(
                ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Female-ratio'],
                ['score' => 0]
            );
        } else {
            PlanScores::updateOrInsert(
                ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Female-ratio'],
                ['score' => $femaleRatio->female_ratio]
            );
        };


        $maleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "male")/Count(*))*100 as male_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        if ($maleRatio === null) {
            PlanScores::updateOrInsert(
                ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Male-Ratio'],
                ['score' => 0]
            );
        } else {
            PlanScores::updateOrInsert(
                ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Male-Ratio'],
                ['score' => $maleRatio->male_ratio]
            );
        }

        $InternalRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Internal-External Ratio'],
            ['score' => $InternalRatio->internal_ratio ?? 0]
        );

        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Skill Score'],
            ['score' => $averageskillscore->average_skill_score ?? 0]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Tenure Score'],
            ['score' => $averagetenurescore->average_tenure_score ?? 0]
        );

        $this->mount($this->plan->id); // Refresh the successPeople data
    }

    public function viewSelectedPeople($id, $peopleId, $type)
    {
        $this->savedPeople = $id;
        if($id == null){
            $this->viewRootId = $peopleId;
        }

        if(strtolower($type) == "internal") {
            $this->peoplescareer = internal_career_histories::where('people_id', $peopleId)
            ->join('companies', 'internal_career_histories.past_company_id', '=', 'companies.id')
            ->select('internal_career_histories.*', 'companies.name as company_name')
            ->orderBy('internal_career_histories.start_date', 'desc')
            ->get();           

        } else {
         // Get the career histories of the individual
         $this->peoplescareer = CareerHistories::where('people_id', $peopleId)
         ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
         ->select('career_histories.*', 'companies.name as company_name')
         ->orderBy('career_histories.start_date', 'desc')
         ->get();
        }
    }

    public function updateScore($id)
    {

        $updateSuccessor = SuccessPeople::where('id', $id);
        $redSuc = SuccessPeople::where('id', $id)->first();

        $this->updategenScore = $this->updategenScore ?? $redSuc->gender_match;
        $this->updaterolScore = $this->updaterolScore ?? $redSuc->role_match;
        $this->updatetenureScore = $this->updatetenureScore ?? $redSuc->tenure_match;
        $this->updatelocScore = $this->updatelocScore ?? $redSuc->location_match;
        $this->updateskillScore = $this->updateskillScore ?? $redSuc->skills_match;

        $updateSuccessor->update([
            'gender_match' => $this->updategenScore,
            'role_match'   => $this->updaterolScore,
            'tenure_match' => $this->updatetenureScore,
            'location_match'   => $this->updatelocScore,
            'skills_match' => $this->updateskillScore,
            'total_score' => (+$this->updategenScore) + (+$this->updaterolScore) + (+$this->updatetenureScore) + (+$this->updatelocScore)  + (+$this->updateskillScore)
        ]);

        //
        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        //dd($averageskillscore);

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Skill Score'],
            ['score' => $averageskillscore->average_skill_score]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Tenure Score'],
            ['score' => $averagetenurescore->average_tenure_score]
        );
    }

    public function updateSummary($id)
    {
        $successPerson = SuccessPeople::findOrFail($id);
        $successPerson->update(['summary' => $this->successPersonSummary]);
    }

    public function addNotes($id)
    {
        // $successPerson = SuccessPeople::findOrFail($id);
        // $successPerson->update(['notes' => $this->filteredPnotes]);

        $user = auth()->user();
        $userNoteData = [
            'entity_id' => $id,
            'entity_type' => 'success_person',
            'Notes' => $this->filteredPnotes,
            'author' => $user->id
        ];
        UserNotes::insert($userNoteData);
    }

    public function SaveProject() {
		// dd();
		$userId = Auth::id();
		// if($this->recruitmentTitle){
			$recruitment = Recruitment::create([
				'user_id' => $userId,
				'recruitment_name' =>$this->plan->name,
				'shared_with' => '',
			]);
			$counter=0;
			foreach ($this->interviewfields as $stage) {
				$counter++;
				RecruitmentStage::create([
					'user_id' => $userId, 
					'recruitment_project_id' => $recruitment->id, 
					'stage_name' => $stage,
					'stage_number' => $counter,
				]);
			}
			// $this->selectedProject=$recruitment->id;
		// }
		$PlanexistingPipeline = RecruitmentPipeline::where('Recruitment_project_id', $recruitment->id)
								->whereNotNull('Plan_id')
								->where('Plan_id', '!=', '')
								->first();
	    // dd($PlanexistingPipeline->Plan_id); die;
		if ($PlanexistingPipeline && $PlanexistingPipeline->Plan_id!=$this->plan->id) {
			$this->dispatch('toast', 'info', 'This Recruitment project has already been assigned to another plan.');
		}else{
			 // $jobId = JobPeople::where('people_id', $this->selectedpersonId)->value('job_id');
			$existingPipeline = RecruitmentPipeline::where('Candidate_ID', $this->selectedpersonId)
				->where('Recruitment_project_id', $recruitment->id)
				->where('Plan_id', $this->plan->id)
				->first();
			if ($existingPipeline) {
				$this->dispatch('toast', 'info', 'Record already exists!');
			} else {
				RecruitmentPipeline::create([
					'Recruitment_project_id' => $recruitment->id,
					'Plan_id' => $this->plan->id,
					'Job_id' => 0, 
					'Candidate_ID' => $this->selectedpersonId,
					'Phone_number' => '',
					'Email' =>'',
					'Address' => '',
					'Link_cv' => '',
					'Status' =>'',
					'Consent_document' => '',
				]);
				
				$this->dispatch('project-saved');
				$this->dispatch('toast', 'success', 'Candidate has been added Successfully!');
			}
		}
	}

    public function updatedInterviewCount(){
		$this->interviewCount = max(1, (int)$this->interviewCount); // Ensure a minimum of 1
		$this->initializeFields();
	}

	private function initializeFields(){
		$this->interviewfields = array_map(fn($i) => $this->interviewfields[$i] ?? '', range(0, $this->interviewCount - 1));
	}

    public function increment(){
        $this->interviewCount++;
        $this->initializeFields();
    }

    public function decrement(){
        if ($this->interviewCount > 1) {
            $this->interviewCount--;
            $this->initializeFields();
        }
    }

    public function savePlanPeopleId($people_id,$recruitmentProjectId){
		$existingPipeline = RecruitmentPipeline::where('Candidate_ID', $people_id)
				->where('Recruitment_project_id', $recruitmentProjectId)
				->where('Plan_id', $this->plan->id)
				->first();
			if ($existingPipeline) {
				$this->dispatch('toast', 'info', 'Record already exists!');
			}else {
				RecruitmentPipeline::create([
					'Recruitment_project_id' => $recruitmentProjectId,
					'Plan_id' => $this->plan->id,
					'Job_id' => 0, 
					'Candidate_ID' => $people_id,
					'Phone_number' => '',
					'Email' =>'',
					'Address' => '',
					'Link_cv' => '',
					'Status' =>'',
					'Consent_document' => '',
				]);
			$this->dispatch('project-saved');
			$this->dispatch('toast', 'success', 'Candidate has been added Successfully!');
			}

    }

    public function approveSuccessPerson($id)
    {
        $successPerson = SuccessPeople::findOrFail($id);
        $user = auth()->user();

        if ($successPerson->company_id === $user->company_id) {
            $successPerson->update(['status' => 'Approved']);
        } else {
            $successPerson->update(['status' => 'Approved']);
        }

        $proposedpeople = SuccessPeople::where('plan_id', $this->plan->id)
            ->where('status', '=', 'Proposed');


        if ($proposedpeople->count() == 0) {
            $plan = SuccessionPlan::find($this->plan->id);
            $plan->update([
                'candidate_status'          => 'Approved',
            ]);
        }

        $this->successpeople = SuccessPeople::where('plan_id', $this->plan->id)
            ->orderBy('total_score', 'desc')
            ->get();
    }
}
