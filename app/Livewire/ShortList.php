<?php

namespace App\Livewire;

use Livewire\Component;
use Carbon\Carbon;
use App\Models\Job;
use Livewire\WithPagination;
use App\Models\JobPeople;
use App\Models\Skills;
use App\Models\CareerHistories;
use App\Models\JobRequirement;
use App\Models\Location;
use App\Models\Role;
use App\Models\People;
use App\Models\Company;
use App\Models\pipeline;
use App\Models\SuccessPeople;
use App\Models\SuccessionPlan;
use App\Models\UserNotes;
use Illuminate\Support\Facades\DB;
use App\Exports\ShortListedPeopleExport;
use Maatwebsite\Excel\Facades\Excel;

class ShortList extends Component
{
    // References for jobPeople table
    public $jobdetails;
    public $openPlanPopup=false;
    public $openTalentPopup=false;
    public $disableDispatchforMultipleSelect=false;
    public $selectedPerson = [];
    public $job;
    public $jobdesc;
    public $selectedPeople = [];
    public $selectedIndividualID;
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $search = false;
    public $searchByKeyword = '';
    public $personRoles;
    public $companies;
    public $successPersonNotes;
    public $filteredPnotes;


    // Variables for filters
    public $forename = "";
    public $surname = "";
    public $gender = "";
    public $role;
    public $_role = [];
    public $previousRole = [];
    public $company = [];
    public $function = [];
    public $division = [];
    public $slocation = [];
    public $regBodies = "";
    public $moverFilter = "";
    public $tenure;
    public $totalPeople;
    public $totalCompanies;
    public $locations;
    public $functions;
    public $divisions;


    // References for charts
    public $genderLabels = [];
    public $genderData = [];
    public $totalScoreLabels = [];
    public $totalScoreData = [];
    public $companyLabels = [];
    public $companyData = [];
    public $functionLabels = [];
    public $functionData = [];
    public $divisionLabels = [];
    public $divisionData = [];
    public $roleLabels = [];
    public $roleData = [];
    public $min_exp =   null;

    //References for adding to jobs
    public $SuccessSkills;
    public $jobDetail;
    public $shortListUsers;
    public $userPlansData = [];
    public $vopen = false;
    public $addToPlanPopup = false;
    public $plansList = [];
    public $addToPlansArray = [];
    public $addToTalentPoolArray = [];
    public $addToTalentPoolPopup = false;
    public $talentPoolsList = [];


    public function mount()
    {
        $this->jobdetails = Job::where('id', $this->job)->first();

        $this->SuccessSkills = JobRequirement::where('job_id', $this->job)
            ->where('type', 'professional_skill')
            ->get();
        //dd($this->SuccessSkills);

        //---------------------------- Gender Split -------------------------------//
        $peopleCountsData = DB::table('job_people')
            ->select('gender', DB::raw('COUNT(*) as total_people'))
            ->where('job_id', $this->job)
            ->groupBy('gender')
            ->get();

        foreach ($peopleCountsData as $data) {
            $this->genderLabels[] = $data->gender;
            $this->genderData[] = $data->total_people;
        }

        //--------------------------- Company Split -------------------------------//
        $CompanyCountsData = DB::table('job_people')
            ->select('company_name', DB::raw('COUNT(*) as total_people'))
            ->where('job_id', $this->job)
            ->groupBy('company_name')
            ->orderBy('total_people', 'desc')
            ->take(5)
            ->get();

        foreach ($CompanyCountsData as $data) {
            $this->companyLabels[] = $data->company_name;
            $this->companyData[] = $data->total_people;
        }

        //-------------------------- Function Split -------------------------------//
        $functionCountsData = DB::table('job_people')
            ->select('function', DB::raw('COUNT(*) as total_people'))
            ->where('job_id', $this->job)
            ->groupBy('function')
            ->orderBy('total_people', 'desc')
            ->get();

        foreach ($functionCountsData as $data) {
            $this->functionLabels[] = $data->function;
            $this->functionData[] = $data->total_people;
        }

        $this->locations = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $this->personRoles = Role::distinct()->pluck('title')->map(function ($role) {
            return [
                'value' => $role,
                'label' => $role,
            ];
        })->toArray();

        // $this->companies = Company::distinct()->pluck('name')->map(function ($company) {
        //     return [
        //         'value' => $company,
        //         'label' => $company,
        //     ];
        // })->toArray();
        $companiesArr = pipeline::whereNotNull('company_id')
                                ->whereNotNull('company_name')
                                ->distinct()
                                ->orderBy('company_name', 'ASC')
                                ->pluck('company_name', 'company_id')
                                ->toArray();

        foreach($companiesArr as $companyId => $companyName){
            $this->companies[] = ['value' => $companyId, 'label' => $companyName];
        }

        $this->functions = pipeline::whereNotNull('function')
                                    ->where('function', '!=', "")
                                    ->distinct()
                                    ->orderBy('function', 'ASC')
                                    ->pluck('function')
                                    ->map(function ($function) {
                                        return [
                                            'value' => $function,
                                            'label' => $function,
                                        ];
                                    })
                                    ->toArray();

        $this->divisions = DB::table('pipelines')
                                ->whereNotNull('division')
                                ->where('division', '!=', "")
                                ->distinct()
                                ->orderBy('division', 'ASC')
                                ->pluck('division')
                                ->map(function ($division) {
                                    return [
                                        'value' => $division,
                                        'label' => $division,
                                    ];
                                })
                                ->toArray();

        //-------------------------- Division Split -------------------------------//
        $divisionCountsData = DB::table('job_people')
            ->select('country', DB::raw('COUNT(*) as locations'))
            ->where('job_id', $this->job)
            ->groupBy('country')
            ->orderBy('locations', 'desc')
            ->take(5)
            ->get();

        foreach ($divisionCountsData as $data) {
            $this->divisionLabels[] = $data->country;
            $this->divisionData[] = $data->locations;
        }

        $this->jobdesc = Job::where('id', $this->job)->get();
    }

    use WithPagination;
    public function render()
    {
        logCurrentDateTime("render started");

        $jobPeople = JobPeople::select('job_people.*', 'pipelines.mover as people_mover', 'people.company_name as current_company', 'people.latest_role as current_role')
                                ->leftJoin('pipelines', 'job_people.pipeline_id', '=', 'pipelines.id')
                                ->leftJoin('people', 'job_people.people_id', '=', 'people.id')
                                ->where('job_people.job_id', $this->job)
                                ->orderBy($this->sortBy, $this->sortDirection);

        //dd($jobPeople);

        if ($this->search) {
            if ($this->forename) {
                $jobPeople = $jobPeople->where('job_people.first_name', 'like', '%' . $this->forename . '%');
            }
            if ($this->surname) {
                $jobPeople = $jobPeople->where('job_people.last_name', 'like', '%' . $this->surname . '%');
            }
            if ($this->gender) {
                $jobPeople = $jobPeople->where('job_people.gender', '=', $this->gender);
            }
            if ($this->role) {
                $jobPeople = $jobPeople->where('job_people.latest_role', 'like', '%' . $this->role . '%');
            }
            if ($this->company) {
                $jobPeople = $jobPeople->where('job_people.company_id', $this->company);
            }
            if ($this->function) {
                $jobPeople = $jobPeople->where('job_people.function', 'like', '%' . $this->function . '%');
            }
            if ($this->division) {
                $jobPeople = $jobPeople->where('job_people.division', 'like', '%' . $this->division . '%');
            }
            if ($this->slocation) {
                $jobPeople = $jobPeople->where('job_people.country', 'like', '%' . $this->slocation . '%');
            }
            if ($this->regBodies) {
                $jobPeople = $jobPeople->where('job_people.other_tags', 'like', '%' . $this->regBodies . '%');
            }
            if ($this->moverFilter) {
                if($this->moverFilter == 'Mover'){
                    $jobPeople = $jobPeople->where('pipelines.mover', 'Mover');
                }
                
                if($this->moverFilter == 'Non Mover'){
                    $jobPeople = $jobPeople->where(function($query) {
                                                        $query->whereNull('pipelines.mover')
                                                            ->orWhereIn('pipelines.mover', ['', 'Non Mover']);
                                                    });
                }
            }
            if ($this->min_exp) {
                $jobPeople = $jobPeople->where('job_people.tenure', '>=', $this->min_exp);
            }
        }

        if ($this->searchByKeyword != "") {
            $jobPeople = $this->searchPeople();
        }

        $filteredPeople = JobPeople::where('people_id', $this->selectedIndividualID)
            ->get();

        // Getting the skills and the career histories
        $filteredPeopleid = $filteredPeople->pluck('people_id')->toArray();

        $this->locations = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        // $this->companies = Company::distinct()->pluck('name')->map(function ($company) {
        //     return [
        //         'value' => $company,
        //         'label' => $company,
        //     ];
        // })->toArray();

        $companiesArr = pipeline::whereNotNull('company_id')
                                ->whereNotNull('company_name')
                                ->distinct()
                                ->orderBy('company_name', 'ASC')
                                ->pluck('company_name', 'company_id')
                                ->toArray();

        foreach($companiesArr as $companyId => $companyName){
            $this->companies[] = ['value' => $companyId, 'label' => $companyName];
        }

        if (!empty($filteredPeopleid)) {

            // Get the skills of the individual
            $peopleskills = Skills::whereIn('people_id', $filteredPeopleid)->get();

            // Get the career histories of the individual
            $peoplescareer = CareerHistories::whereIn('people_id', $filteredPeopleid)
                ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
                ->select('career_histories.*', 'companies.name as company_name')
                ->orderBy('start_date', 'desc')
                ->get();
        } else {
            $peopleskills = null;
            // $groupedPeoplesCareer = null;
            $peoplescareer = null;
        }
        if ($this->selectedIndividualID) {
            $userNotes = UserNotes::select('user_notes.*', 'users.name as user_name')
                ->where('entity_id', $this->selectedIndividualID)
                ->where('entity_type', 'success_person')
                ->join('users', 'user_notes.author', '=', 'users.id')
                ->orderBy('user_notes.id', 'desc')
                ->get();
            // dd($userNotes);
            $this->successPersonNotes = $userNotes;
        }

        $this->dispatch('updateChart');
        logCurrentDateTime("render ended");

        return view('livewire.short-list', ['jobPeople' => $jobPeople->paginate(50), 'filteredPeople' => $filteredPeople, 'peopleskills' => $peopleskills, 'groupedPeoplesCareer' => $peoplescareer]);
    }

    public function searchPeople()
    {
        // dd($this->searchByKeyword);
        $jobPeople = JobPeople::where('job_id', $this->job)
            ->orderBy($this->sortBy, $this->sortDirection);

        $searchKeywords = explode(",", $this->searchByKeyword);
        $jobPeople = $jobPeople->where(function ($query) use ($searchKeywords) {
            foreach ($searchKeywords as $searchword) {
                $query->orWhere(function ($subQuery) use ($searchword) {
                    $subQuery->orWhere('first_name', 'like', '%' . $searchword . '%')
                        ->orWhere('middle_name', 'like', '%' . $searchword . '%')
                        ->orWhere('last_name', 'like', '%' . $searchword . '%')
                        ->orWhere('latest_role', 'like', '%' . $searchword . '%')
                        ->orWhere('company_name', 'like', '%' . $searchword . '%')
                        ->orWhere('function', 'like', '%' . $searchword . '%')
                        ->orWhere('division', 'like', '%' . $searchword . '%')
                        ->orWhere('country', 'like', '%' . $searchword . '%')
                        ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                });
            }
        });
        $this->dispatch('updateChart');
        return $jobPeople;
    }

    public function runfilters()
    {
        $this->search = true;
        $this->resetPage();
        $this->dispatch('updateChart');
    }

    public function Ordermytable($column)
    {
        if ($column === $this->sortBy) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
        $this->dispatch('updateChart');
    }

    public function changeMinimumTenure($flag)
    {
        if ($flag == 'decrease') {
            $this->min_exp = $this->min_exp <= 1 ? null : ($this->min_exp - 1);
        } else if ($flag == 'increase') {
            $this->min_exp = !$this->min_exp ? 1 : ($this->min_exp + 1);
        }
    }

    public function addNotes($id)
    {
        // $successPerson = SuccessPeople::findOrFail($id);
        // $successPerson->update(['notes' => $this->filteredPnotes]);

        $user = auth()->user();
        $userNoteData = [
            'entity_id' => $id,
            'entity_type' => 'success_person',
            'Notes' => $this->filteredPnotes,
            'author' => $user->id
        ];
        UserNotes::insert($userNoteData);
        $this->dispatch('updateChart');
    }

    public function viewIndividual($id)
    {

        $this->selectedIndividualID = $id;
        $this->jobDetail = Job::find($this->job);
        $user = auth()->user();
        $jobIds = JobPeople::where(['people_id' => $id])->pluck('job_id')->toArray();
        $this->shortListUsers = Job::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
        })
        ->whereIn('id', $jobIds)
        ->get();
        $planIds = SuccessPeople::where(['people_id' => $id, 'type' => 'External'])->pluck('plan_id')->toArray();
        $this->userPlansData = SuccessionPlan::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
        })
        ->whereIn('id', $planIds)
        ->get();
        $this->plansList = getPlansList($id);
        $this->talentPoolsList = getTalentPoolList($id, $this->job);
        $this->dispatch('updateChart');
    }
    public function showAddToPlanPopup($peopleId) {
        $this->selectedIndividualID = $peopleId;
        $this->plansList = getPlansList($peopleId);
        $this->addToPlanPopup = true;

    }

    public function showAddToTalentPoolPopup($peopleId) {
        $this->selectedIndividualID = $peopleId;
        $this->talentPoolsList = getTalentPoolList($peopleId, $this->job);
        $this->addToTalentPoolPopup = true;
    }

    public function addpeopleToTalentPools() {
        if(empty($this->addToTalentPoolArray)) {
            $this->dispatch('toast', 'info', 'Please select talent pool!');
            $this->skipRender();
            return;
        }
        $peopleId = JobPeople::where('id','=',$this->selectedIndividualID)->pluck('people_id')->first();
       
        foreach($this->addToTalentPoolArray as $key => $value) {
            if($value) {
                $JobPerson = JobPeople::where(['people_id' => $peopleId,'job_id' => $key])->first();
          
                if($JobPerson) {
                    continue;
                }
                addToJob($peopleId, $key);
            }
        }
        if(!$this->disableDispatchforMultipleSelect) {
            $this->dispatch('toast', 'info', 'Added to selected talent pools!');
            $this->addToTalentPoolPopup = false;
            $this->vopen = false;
            $this->addToTalentPoolArray = [];
        }
    }

    public function addpeopleToPlans() {

        if(empty($this->addToPlansArray)) {
            $this->dispatch('toast', 'info', 'Please select plans!');
            $this->skipRender();
            return;
        }
        foreach($this->addToPlansArray as $key => $value) {
            if($value) {                 
                $PlanPerson = SuccessPeople::where(['people_id' => $this->selectedIndividualID,'plan_id' => $key])->first();
                if($PlanPerson) {
                  continue;
                }
             
                addToPlan($this->selectedIndividualID, $key);
            }
        }
        if(!$this->disableDispatchforMultipleSelect) {
            $this->dispatch('toast', 'info', 'Added to selected plans!');
            $this->addToPlanPopup = false;
            $this->vopen = false;
            $this->addToPlansArray = [];
         }

    }

    public function refreshMover($jobPeople){
        $people = People::where('id', $jobPeople['people_id'])->first();

        // Update Job People
        $jobPeopleDataToUpdate = [
            "first_name" => $people->forename,
            "last_name" => $people->surname,
            "middle_name" => $people->middle_name,
            "other_name" => $people->other_name,
            "gender" => $people->gender,
            "diverse" => $people->diverse,
            "country" => $people->country,
            "city" => $people->city,
            "linkedinURL" => $people->linkedinURL,
            "latest_role" => $people->latest_role,
            "company_id" => $people->company_id,
            "company_name" => $people->company_name,
            "start_date" => $people->start_date,
            "end_date" => $people->end_date,
            "tenure" => $people->tenure,
            "function" => $people->function,
            "division" => $people->division,
            "seniority" => $people->seniority,
            "career_history" => $people->career_history,
            "educational_history" => $people->educational_history,
            "skills" => $people->skills,
            "languages" => $people->languages,
            "other_tags" => $people->other_tags,
            "readiness" => $people->readiness,
            "summary" => $people->summary
        ];
        JobPeople::where('id', $jobPeople['id'])->update($jobPeopleDataToUpdate);

        // Update pipeline
        $pipelineDataToUpdate = [
            'first_name' => $people->forename,
            'last_name' => $people->surname,
            'middle_name' => $people->middle_name,
            'other_name' => $people->other_name,
            'gender' => $people->gender,
            'diverse' => $people->diverse,
            'country' => $people->country,
            'city' => $people->city,
            'mover' => 'Non Mover',
            'linkedinURL' => $people->linkedinURL,
            'latest_role' => $people->latest_role,
            'company_id' => $people->company_id,
            'company_name' => $people->company_name,
            'exco' => $people->exco,
            'start_date' => $people->start_date,
            'end_date' => $people->end_date,
            'tenure' => $people->tenure,
            'function' => $people->function,
            'division' => $people->division,
            'seniority' => $people->seniority,
            'career_history' => $people->career_history,
            'educational_history' => $people->educational_history,
            'skills' => $people->skills,
            'languages' => $people->languages,
            'other_tags' => $people->other_tags,
            'readiness' => $people->readiness,
            'summary' => $people->summary
        ];
        pipeline::where('id', $jobPeople['pipeline_id'])
                    ->where('job_id', $this->job)
                    ->update($pipelineDataToUpdate);

        

        $pipelineMoverCount = pipeline::where('job_id', $this->job)
                                        ->where('mover', 'Mover')
                                        ->count();

        if($pipelineMoverCount <= 0){
            Job::where('id', $this->job)->update(['mover' => 'Non Mover']);
        }

        return;
    }

    public function clearFilters()
    {
        $this->search = false;
        // Reset all form input properties
        $this->reset(['forename', 'surname', 'role', 'company', 'function', 'division', 'slocation', 'gender', 'regBodies', 'tenure']);
        $this->dispatch('updateChart');
    }
    public function removepipPerson($id)
    {
        // dd($id);
        $person = JobPeople::where('id', $id)->firstOrFail();
        // Delete the person from the JobPeople table
        $person->delete();
        $this->dispatch('updateChart');
    }

    public function approveJobPeople($id) {
        $job = Job::find($this->job);
        $person = JobPeople::where('id', $id)->firstOrFail();

        if(($job->user_id != auth()->user()->id)) {
            $this->dispatch('toast', 'error', 'You are not allowed to perform this action!');
            return;
        }

        if(!$person) {
            $this->dispatch('toast', 'error', 'Data not found!');
            return;
        }

        $pipeline = Pipeline::find($person->pipeline_id);

        if(!$pipeline) {
            $this->dispatch('toast', 'error', 'pipeline not found!');
            return;
        }
        
        $person->update(['status' => 'Approved']);
        $pipeline->update(['status' => 'Approved']);
        $this->dispatch('toast', 'info', 'Status apporved successfully!');
    }

    public function downloadShortListedPeople($filterArr)
    {
        // Create an instance of SuccessPeopleExport with the provided filter
        $export = new ShortListedPeopleExport($filterArr);

        // Check if there is any data to export
        $pipelinePeople = $export->collection();

        if ($pipelinePeople->isEmpty()) {
            // Return a response indicating no data available
            session()->flash('error', "No records found to be download!");
            // $this->dispatch('toast', 'info', "No records found to be download!");
            return;
        }

        return Excel::download($export, 'short-list.xlsx');
    }

    
    public function addSelectedToTalentPool()
    {  
        $this->talentPoolsList = collect(); 
        $processed = [];
        foreach ($this->selectedPerson as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->talentPoolsList = $this->talentPoolsList->merge(getTalentPoolList($peopleId));
                $processed[] = $peopleId;
            }
        }
       
        $this->talentPoolsList = $this->talentPoolsList->unique('id')->values();
        $this->addToTalentPoolPopup = true;
        $this->openTalentPopup = true;
    }

    public function addSelectedPersonToTalentPool()
    { 

        $this->disableDispatchforMultipleSelect = true;
        foreach($this->selectedPerson as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;
            $this->addpeopleToTalentPools();
        }
        $this->openTalentPopup = false;
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected talent pools!');
        $this->addToTalentPoolPopup = false;
        $this->vopen = false;
        $this->addToTalentPoolArray = [];
        $this->selectedPerson = [];
       
    }
    

    public function addSelectedToPlan()
    { 
        
        $peopleIds = JobPeople::whereIn('id', $this->selectedPerson)->pluck('people_id')->unique();
        $this->plansList = collect(); 
        $processed = [];
        
        foreach ($peopleIds as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->plansList = $this->plansList->merge(getPlansList($peopleId));
                $processed[] = $peopleId;
            }
        }
        
        $this->plansList = $this->plansList->unique('id')->values();
       
        $this->addToPlanPopup = true;
        $this->openPlanPopup = true;
        
    }
    public function addSelectedPersonToPlans()
    {  
      $peopleIds = JobPeople::whereIn('id', $this->selectedPerson)->pluck('people_id')->unique();
      $this->disableDispatchforMultipleSelect = true;
       foreach ($peopleIds as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;
            $this->addpeopleToPlans();
       }
        $this->openPlanPopup = false;
        $this->selectedPerson = [];
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected plans!');
        $this->addToPlanPopup = false;
       
        $this->vopen = false;
        $this->addToPlansArray = [];
    }
    public function deleteSelected()
    {
        $selectedIds = $this->selectedPerson;
        foreach ($selectedIds as $id) {
            JobPeople::where('id', $id)->delete();
        }
        $this->dispatch('toast', 'success', 'Individual has deleted successfully.');
        $this->selectedPerson = [];
    }
    public function updatingPage() {
        $this->selectedPerson = [];
    }
}
