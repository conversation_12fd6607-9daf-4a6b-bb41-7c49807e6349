<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Renderless;
use App\Models\InternalPeople;
use App\Models\InternalSkills;
use App\Models\SuccessPeople;
use Livewire\WithFileUploads;
use App\Models\SuccessionPlan;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;


class OrganisationChart extends Component
{   
    public $id;
    public $plans;
    public $topPerson;
    public $subordinate;
    public $topPeople;
    public $topIndividual;
    public $empid = "";
    public $forename ="";
    public $surname = "";
    public $roles = "";
    public $function = "";
    public $division = "";
    public $reportTo = "";
    public $location = "";
    public $gender = "";
    public $start_date = "";

    public $skills ="";

    

    // Variables for updateInternal Candidate Form
    public $specialisedskills = [];
    public $commonskills = [];
    public $certification = [];
    public $updateRole;
    public $updateFunction;
    public $updateDivision;
    public $commonSkillNames = [];
    public $commonSkillRatings = [];
    public $updateSpecialisedSkills;
    public $updateCertifications;

    //Variables for views
    public $internalPeople;
    public $individualPlans;
    public $internalPerson;
    public $individualSkillslabels =[];
    public $individualSkillsdata= [];
    public $iopen = false;
    public $selectedOrganisationID;
    public $test;
    public $nextID;

    public function mount($topPerson = null)
    {
        $this->id = $topPerson;

        //dd($this->id);

        $user = auth()->user();
        $this->plans = SuccessionPlan::where('user_id', $user->id)->get()->pluck('id');
    }
    

    #[On('organisationSelected')]
    public function render($id = null)
    {   
        
        if ($this->nextID === null){

            if ($id !== null) {
                $user = auth()->user();
    
                $this->topIndividual = InternalPeople::where('company_id', $user->company_id)
                                                        ->where('id',$id)  
                                                        ->first();
                
                $successpeople = SuccessPeople::whereIn('plan_id',$this->plans)
                                          ->where('type','Internal')
                                          ->pluck('people_id');
    
                // Get the 
                $topPeople = InternalPeople::where('company_id', $user->company_id)
                                                ->where('reports_to',$id)  
                                                ->get();
            
                $topPeople = $topPeople->map(function ($person) use ($successpeople) {
                    $person->isSuccessPerson = $successpeople->contains($person->id) ? 1 : 0;
                    return $person;
                    });
    
                $topPeopleIds = $topPeople->pluck('id');
    
                $this->topPeople = $topPeople->map(function ($person) use ($topPeopleIds) {
                    $subordinateCount = InternalPeople::where('company_id', auth()->user()->company_id)
                    ->where('reports_to', $person->id)
                    ->groupBy('reports_to')
                    ->count();
            
                    $person->subordinate_count = $subordinateCount;
            
                    return $person;
                    });
            
                $immediateSubordinates = InternalPeople::where('company_id', $user->company_id)
                    ->whereIn('reports_to', $topPeopleIds)  
                    ->get();
                    //dd($immediateSubordinates);
            
                $immediateSubordinateIds = $immediateSubordinates->pluck('id');
            
                $immediateSubordinates = $immediateSubordinates->map(function ($sperson) use ($immediateSubordinateIds) {
                        $subordinateCount = InternalPeople::where('company_id', auth()->user()->company_id)
                        ->where('reports_to', $sperson->id)
                        ->groupBy('reports_to')
                        ->count();
                
                        $sperson->subordinate_count = $subordinateCount;
                
                        return $sperson;
                        });
            
                    // Getting them into arrays 
                    $groupedSubordinates = $immediateSubordinates->groupBy('reports_to');
            
                    // Mapping subordinates back to top people
                    $this->topPeople = $topPeople->map(function ($topPerson) use ($groupedSubordinates) {
                        // Access subordinates using reports_to value
                        $topPerson->subordinates = $groupedSubordinates[$topPerson->id] ?? collect();
            
                        return $topPerson;
                    });
    
                    //dd($topPeople);
    
                }
        }

        else {
            $user = auth()->user();
    
            $this->topIndividual = InternalPeople::where('company_id', $user->company_id)
                                                    ->where('id',$this->nextID)  
                                                    ->first();
            
            $successpeople = SuccessPeople::whereIn('plan_id',$this->plans)
                                      ->where('type','Internal')
                                      ->pluck('people_id');

            // Get the 
            $topPeople = InternalPeople::where('company_id', $user->company_id)
                                            ->where('reports_to',$this->nextID)  
                                            ->get();
        
            $topPeople = $topPeople->map(function ($person) use ($successpeople) {
                $person->isSuccessPerson = $successpeople->contains($person->id) ? 1 : 0;
                return $person;
                });

            $topPeopleIds = $topPeople->pluck('id');

            $this->topPeople = $topPeople->map(function ($person) use ($topPeopleIds) {
                $subordinateCount = InternalPeople::where('company_id', auth()->user()->company_id)
                ->where('reports_to', $person->id)
                ->groupBy('reports_to')
                ->count();
        
                $person->subordinate_count = $subordinateCount;
        
                return $person;
                });
        
            $immediateSubordinates = InternalPeople::where('company_id', $user->company_id)
                ->whereIn('reports_to', $topPeopleIds)  
                ->get();
                //dd($immediateSubordinates);
        
            $immediateSubordinateIds = $immediateSubordinates->pluck('id');
        
            $immediateSubordinates = $immediateSubordinates->map(function ($sperson) use ($immediateSubordinateIds) {
                    $subordinateCount = InternalPeople::where('company_id', auth()->user()->company_id)
                    ->where('reports_to', $sperson->id)
                    ->groupBy('reports_to')
                    ->count();
            
                    $sperson->subordinate_count = $subordinateCount;
            
                    return $sperson;
                    });
        
                // Getting them into arrays 
                $groupedSubordinates = $immediateSubordinates->groupBy('reports_to');
        
                // Mapping subordinates back to top people
                $this->topPeople = $topPeople->map(function ($topPerson) use ($groupedSubordinates) {
                    // Access subordinates using reports_to value
                    $topPerson->subordinates = $groupedSubordinates[$topPerson->id] ?? collect();
        
                    return $topPerson;
                });
        }
        

        return view('livewire.organisation-chart');
    }

    public function showOrganisation($id){

        $this->nextID = $id;

        //dd($this->nextID);
    }

    #[Renderless] 
    public function showIndividual($Individualid){
        
        $this->selectedIndividualID = $Individualid;
        //dd($this->selectedIndividualID);
        $this->dispatch('individualSelected', $Individualid)->to(InternalShow::class);
    }

}
