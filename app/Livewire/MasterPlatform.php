<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Company;
use App\Models\People;
use App\Models\InternalPeople;
use App\Models\User;
use App\Models\Account;
use Illuminate\Support\Facades\URL;
use App\Models\Invite;
use Carbon\Carbon;
use App\Jobs\SendNewAdminEmail;



class MasterPlatform extends Component
{
    //Step-1 & 2 Variables
    public $account_name;
    public $updateAccounttype;
    public $accountType;
    public $companyName;
    public $managerRelation;
    public $companyLocation;
    public $email;
    public $name;
    public $Suggestion = [];
    public $companies;
    public $nopen = false;
    public $uopen = false;
    public $openModal = false;
    public $sector_interest = [];
    public $industry_interest = [];
    public $company_interest = [];
    public $userLimit;

    //Step-3 Variables
    public $managers;
    public $selectedManager;
    public $selectedAccount;
    public $accounts;
    public $sectors;
    public $industry;
    public $companyInterest = [];
    public $sectorInterest = [];
    public $industryInterest = [];
    public $step = 1;
    public $users_limit;

    public $perPage = 10;
    public $accountIdToUpdate = null;
    public $addNewAccount;
    public $addAdminAccountPopup = false;
    public $adminUsers = [];
    public $companiesOfInterest = [];
    protected $listeners = [
        'deleteAccount',
        'addAccount',
        'deleteAdminUser'
    ];

    public function mount()
    {
        $this->managers = User::where('team', '=', 'Master')->pluck('email')->map(function ($email) {
            return ['value' => $email, 'label' => $email];
        })->toArray();

        $this->accounts = Account::pluck('account_name')->map(function ($name) {
            return ['value' => $name, 'label' => $name];
        })->toArray();

        $companyIds = People::select('company_id')->distinct()->pluck('company_id');

        // Fetch only the companies with IDs matching those found in $companyIds
        $this->companies = $this->companiesOfInterest = Company::whereIn('id', $companyIds)
            ->orderBy('name')
            ->get(['id', 'name'])
            ->map(fn($company) => ['value' => $company->id, 'label' => $company->name])
            ->toArray();

        $this->sectors = Company::where('sector', '!=', "NA")->distinct('sector')->pluck('sector')->map(function ($sector) {
            return ['value' => $sector, 'label' => $sector];
        })->toArray();
        $this->industry = Company::where('sector', '!=', "NA")->distinct('industry')->pluck('industry')->map(function ($industry) {
            return ['value' => $industry, 'label' => $industry];
        })->toArray();
    }

    public function render()
    {
        $Saccounts = Account::leftJoin('users', 'accounts.relationship_manager_id', '=', 'users.id')
            ->select('accounts.*', 'users.name as manager_name', 'users.email as manager_email')
            ->where('accounts.account_type', '!=', 'Master')
            ->paginate(15);

        return view('livewire.master.index', compact(['Saccounts']));
    }

    public function addAccount()
    {
        $this->validate([
            'accountType' => 'required|string',
            'account_name' => 'required|string',
            'email' => 'required|unique:users,email,NULL,id,deleted_at,NULL',
            'companyName' => 'required',
            'managerRelation' => 'required',
            'userLimit' => 'nullable|integer|min:1'

        ]);

        $user = auth()->user();
        $id = $user->account_id;
        $email = $this->email;
        $companyName = $this->companyName;
        $accountName = $this->account_name;
        $accountType = $this->accountType;
        $sectorInterest = $this->sectorInterest;
        $industryInterest = $this->industryInterest;
        $companyInterest = $this->companyInterest;
        $userLimit = !empty($this->userLimit) ? $this->userLimit : 1;

        $manager_id = User::whereIn('email', $this->managerRelation)->pluck('id')->toArray();
        $this->addAdminAccount(
            $email, 
            $companyName, 
            $accountName, 
            $accountType, 
            $manager_id, 
            $sectorInterest, 
            $industryInterest, 
            $companyInterest, 
            $userLimit
        );
        
        $this->nopen = false;
    }

    public function addAdminAccount($email, $companyName, $accountName, $accountType, $managerId, $sectorInterest, $industryInterest, $companyInterest, $userLimit, $accountId = null) {
        $user = auth()->user();
        $id = $user->account_id;
        $inviteData = base64_encode(json_encode([
            'id'              => $id, 
            'email'           => $email, 
            'company'         => $companyName, 
            'account_name'    => $accountName, 
            'account_type'    => $accountType, 
            'manager_id'      => $managerId, 
            'sector'          => $sectorInterest, 
            'industry'        => $industryInterest, 
            'companyInterest' => $companyInterest, 
            'userLimit'       => $userLimit,
            'accountId'       => $accountId
        ]));
        $inviteUrl = URL::temporarySignedRoute(
            'invite.adminInvite',
            now()->addDays(2),
            ['data' => $inviteData]
        );
        $invite = Invite::create([
            'email' => $email,
            'user_id' => $id,
            'company_id' => $companyName,
            'url' => $inviteUrl
        ]);

        SendNewAdminEmail::dispatch($email, $inviteUrl, $user->name)
        ->delay(Carbon::now()->addSeconds(2));

        $this->step = 1;
        $this->reset(['account_name', 'accountType', 'managerRelation', 'email', 'companyName', 'userLimit', 'companyInterest', 'industryInterest', 'sectorInterest']);
        $this->dispatch('toast', 'info', "Invitation mail sent successfully.");


    }

    public function addAdmin()
    {
        $accountid = $this->account->id;
        $email = $this->email;
        $name = $this->name;

        $company = Company::where('id', $this->companyName)->get();
        $company = $company->id;
        Mail::to($this->email)->send(new NewadminEmail($email, $name, $accountid, $company));
    }

    public function updateAccount($id = null)
    { 
        $account = Account::find($this->accountIdToUpdate);
        if ($this->selectedManager === null) {
            $managerId = $account->relationship_manager_id;
        } else {
            $manager = User::where('team', '=', 'Master')
                ->whereIn('email', $this->selectedManager)
                ->pluck('id')->toArray();
            $managerId = implode(',', $manager);
        }

        if ($this->updateAccounttype === null) {
            $account_type = $account->account_type;
        } else {
            $account_type = $this->updateAccounttype;
        }
        $sector = is_array($this->sector_interest) ? implode(',', $this->sector_interest) : $account->sector_interest;
        $industry = is_array($this->industry_interest) ? implode(',', $this->industry_interest) : $account->industry_interest;
        $company_of_interest = is_array($this->company_interest) ? implode(',', $this->company_interest) : $account->company_of_interest;

        if ($account_type === 'silver') {
            $plan_limits = ********;
        } elseif ($account_type === 'gold') {
            $plan_limits = ********;
        } elseif ($account_type === 'platinum') {
            $plan_limits = ********;
        }
        $account->update([
            'account_type'              => $account_type,
            'relationship_manager_id'   => $managerId,
            'plans_limit'               => $plan_limits,
            'sector_interest'           => $sector,
            'industry_interest'         => $industry,
            'company_of_interest'       => $company_of_interest,
            'users_limit'               => $this->users_limit
        ]);
        $this->uopen = false;
        $this->openModal = null;
        $this->reset(['updateAccounttype', 'selectedManager', 'company_interest', 'industry_interest', 'sector_interest', 'users_limit']);
        $this->dispatch('toast', 'info', "Updated successfully.");
    }

    public function addNewAdmin() {

        $this->validate([
            'addNewAccount' => 'required|unique:users,email,NULL,id,deleted_at,NULL',
        ], [
            'addNewAccount.required' => 'The email field is required.',
            'addNewAccount.unique' => 'The email address is already in use.',
        ]);
    
    
     
        $account = Account::find($this->accountIdToUpdate);
        $email = $this->addNewAccount;
        $companyName = $account->company_id;
        $accountName = $account->Account_name;
        $accountType = $account->account_type;
        $managerId = explode(',', $account->relationship_manager_id);
        $sectorInterest = !empty($account->sector_interest) ? explode(',', $account->sector_interest) : [];
        $industryInterest = !empty($account->industry_interest) ? explode(',', $account->industry_interest) : [];
        $companyInterest = !empty($account->company_of_interest) ? explode(',', $account->company_of_interest) : [];
        $userLimit = $account->users_limit;

        $this->addAdminAccount(
            $email, 
            $companyName, 
            $accountName, 
            $accountType, 
            $managerId, 
            $sectorInterest, 
            $industryInterest, 
            $companyInterest, 
            $userLimit,
            $account->id
        );
        
        $this->nopen = false;
        $this->uopen = false;
        $this->openModal = null;
    }

    public function deleteAccount($id)
    {
        $requirement = Account::findOrFail($id);
        User::where('account_id', $id)->delete();
        $requirement->delete();
    }

    public function onSelectAccount($id)
    {
        $this->accountIdToUpdate = $id;
        $account = Account::findOrFail($id);
        $user_ids = explode(',', $account->relationship_manager_id);

        $user = User::where(['role' => 'admin', 'account_id' => $account->id])->first();
        if(empty($user)) {
            $this->dispatch('toast', 'info', 'User does not found!');
        }
        $this->updateAccounttype = $account->account_type;

        $this->selectedManager = User::whereIn('id', $user_ids)->pluck('email')->toArray();
        $this->sector_interest = $account->sector_interest ? explode(',', $account->sector_interest) : [];
        $this->industry_interest = $account->industry_interest ? explode(',', $account->industry_interest) : [];
        $this->company_interest = $account->company_of_interest ? array_map('intval', explode(',', $account->company_of_interest)) : [];        
        $this->users_limit = $account->users_limit;
        $this->adminUsers = User::where(['role' => 'admin', 'account_id' => $account->id])->get();
        $this->openModal = true;
    }

    public function validateStepOne()
    {
        $this->validate([
            'accountType' => 'required|string',
            'account_name' => 'required|string',
            'companyName' => 'required',
            'managerRelation' => 'required',
            'userLimit' => 'nullable|integer|min:1'
        ]);

        $this->step++;
    }

    public function previousStep()
    {
        $this->step--;
    }

    public function deleteAdminUser($id) {
        User::where('id', $id)->delete();
        $this->adminUsers = User::where(['role' => 'admin', 'account_id' => $this->accountIdToUpdate])->get();
        $this->dispatch('toast', 'info', 'Admin user deleted successfully!');
    }
}
