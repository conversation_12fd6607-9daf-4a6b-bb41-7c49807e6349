<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Location;
use App\Models\Company;
use App\Models\People;
use App\Models\pipeline;
use App\Models\JobPeople;
use Carbon\Carbon;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Http;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Process\Process;
use Maatwebsite\Excel\Facades\Excel;


class JobPeopleUpload extends Component
{
    use WithFileUploads;

    public $job;
    public $csvFile;
    public $tempUrl;
    public $company;
    public $role;
    public $country;
    public $countries = [];
    public $peoplesList = [];
    public $showPeopleList = false;
    public $nextPageUrl;
    public $apiKey;

    public $searchData = [
        'companies'  => [],
        'roles'      => [],
        'forenames'  => [],
        'surnames'   => []
    ];

    protected $listeners = [
        'searchPeople',
        'addPeoples',
        'loadMorePeople'
    ];
    public function __construct()
    {
        ini_set('max_execution_time', '1200');
        $this->apiKey = config('ai.nubela.api_key');
    }

    public function mount($job)
    {
        $this->job = $job;
        $this->countries = Location::distinct()
            ->whereNotNull('country_code')
            ->get(['country_name', 'country_code'])
            ->map(function ($location) {
                return [
                    'value' => $location->country_code,
                    'label' => $location->country_name,
                ];
            })
            ->toArray();
    }
    public function render()
    {
        return view('livewire.job-people-upload');
    }
    public function uploadFile()
    {
        try {
            // Validate the file
            $this->validate([
                'csvFile' => 'required|file|max:10240', // max 10MB
            ]);

            // Load the file and count rows
            $path = $this->csvFile->getRealPath();

            $rowCount = Excel::toCollection(null, $path)
                ->first()
                ->count();

            if ($rowCount > 500) {
                $this->dispatch('toast', 'error', 'Rows cannot be greater then 500!');
                $this->csvFile = null;
                $this->dispatch('uploadComplete');
                return;
            }

            // Store the file temporarily
            $tempFilePath = $this->csvFile->store('temp');

            // Pass the file and credentials to the Python script
            $this->runPythonScript(storage_path('app/' . $tempFilePath));

            // Delete the temporary file
            Storage::delete($tempFilePath);

            $this->dispatch('customToast', 'success', 'File uploaded and data stored successfully.');
            $this->csvFile = null;
            $this->dispatch('uploadComplete');
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setErrorBag($e->validator->errors()); // Populate Livewire's $errors property
            $this->dispatch('uploadComplete');
        } catch (ProcessFailedException $e) {
            $this->dispatch('uploadComplete');

            // Log the error for debugging purposes
            Log::error('Python script error: ' . $e->getMessage());

            // Delete the temporary file
            Storage::delete($tempFilePath);

            // Set error message to display to the user
            $this->dispatch('customToast', 'error', 'An error occurred while processing the file. Please try again.');
        }
    }

    private function runPythonScript($filePath)
    {
        // Path to your Python script
        // $pythonScript = '/path/to/your/python/script.py';
        $pythonScript = base_path('script.py');
        // Log::info('File path: ' . $pythonScript);

        // Create a Process instance and execute the Python script
        $userId = auth()->user()->id;
        // Database credentials from .env
        $dbHost = env('DB_HOST');
        $dbPort = env('DB_PORT');
        $dbUsername = env('DB_USERNAME');
        $dbPassword = env('DB_PASSWORD');
        $dbName = env('DB_DATABASE');
        $openApiKey = env('OPENAI_API_KEY');
        $nubelaApiKey = env('NUBELA_API_KEY');

        $process = new Process([
            'python3',
            $pythonScript,
            $filePath,
            $dbHost,
            $dbPort,
            $dbUsername,
            $dbPassword,
            $dbName,
            $openApiKey,
            $nubelaApiKey,
            $userId,
            null,
            $this->job->id
        ]);

        $process->setTimeout(timeout: 1200); // 20 minutes

        $process->run();

        // Check if the process ran successfully
        if (!$process->isSuccessful()) {
            throw new ProcessFailedException($process);
        }

        // Optionally, get output from the Python script
        $output = $process->getOutput();
        Log::info('Python script output: ' . $output);
    }

    public function searchPeople()
    {
        $this->showPeopleList = true;

        $companiesString = !empty($this->searchData['companies']) ? implode(' OR ', $this->searchData['companies']) : null;
        $roleString = !empty($this->searchData['roles']) ? implode(' OR ', $this->searchData['roles']) : null;
        $forenameString = !empty($this->searchData['forenames']) ? implode(' OR ', $this->searchData['forenames']) : null;
        $surnameString = !empty($this->searchData['surnames']) ? implode(' OR ', $this->searchData['surnames']) : null;

        $apiUrl = 'https://nubela.co/proxycurl/api/v2/search/person';

        try {
            // Make GET request for each company
            $response = Http::withToken($this->apiKey)
                ->get($apiUrl, array_filter([
                    'enrich_profiles' => 'enrich',
                    'current_company_name' => $companiesString,
                    'current_role_title' => $roleString,
                    'country' => $this->country,
                    'first_name'=> $forenameString,
                    'last_name'=> $surnameString,
                    'use_cache' => 'if-present',
                ]));

            if ($response->successful()) {
                $data = $response->json();

                // Merge the results into peoplesList
                if (isset($data['results'])) {
                    $this->peoplesList = $data['results'];
                    $this->insertPeoples($this->peoplesList);
                }

                // Handle pagination (if necessary)
                if (isset($data['next_page'])) {
                    $this->nextPageUrl = $data['next_page'];
                }
            } else {

                $this->peoplesList[] = [
                    'error' => 'API call failed for company: ' . $companiesString,
                    'status' => $response->status(),
                    'message' => $response->body(),
                ];
            }
        } catch (\Exception $e) {
            $this->peoplesList[] = ['error' => 'An error occurred: ' . $e->getMessage()];
        }
    }
    public function loadMorePeople()
    {
        if (!$this->nextPageUrl) {
            return;
        }

        $apiUrl = $this->nextPageUrl;

        try {
            // Make GET request to fetch the next page
            $response = Http::withToken($this->apiKey)->get($apiUrl);

            if ($response->successful()) {
                $data = $response->json();
                $this->insertPeoples($data['results'] ?? []);
                $this->peoplesList = array_merge(
                    $this->peoplesList ?? [],
                    $data['results'] ?? []
                );
                $this->nextPageUrl = $data['next_page'];
            } else {
                $this->nextPageUrl = null;
            }
        } catch (\Exception $e) {
            $this->nextPageUrl = null;
        }
    }

    public function insertPeoples(array $peoples)
    {
        $user = auth()->user();

        if (!empty($peoples)) {
            $linkedinUrls = array_column($peoples, 'linkedin_profile_url');
            $existingPeoples = People::whereIn('linkedinURL', $linkedinUrls)->get()->keyBy('linkedinURL');

            foreach ($this->peoplesList as $peopleData) {

                if (isset($existingPeoples[$peopleData['linkedin_profile_url']])) {
                    continue;
                }
                $matchingRecord = $peopleData['profile'];
                $currentExperience = $matchingRecord['experiences'][0];
                $startAndEndDate = $this->convertFirstExperienceDate($currentExperience);
                $startDate = $startAndEndDate['startDate'];
                $endDate = $startAndEndDate['endDate'];

                $startDate = $startDate ? Carbon::parse($startDate) : Carbon::parse("1900-01-01");
                $endDate = $endDate ? Carbon::parse($endDate) : Carbon::now();
                $tenure = $startDate->diffInYears($endDate);

                $company = Company::where(['name' => $currentExperience['company']])->first();
                if (empty($company)) {
                    $company = Company::create([
                        'name' => $currentExperience['company']
                    ]);
                }

                $skills = null;

                if (isset($matchingRecord['skills']) && count($matchingRecord['skills']) > 0) {
                    $skillsArr = $matchingRecord['skills'];
                    $skills = implode("\n", $skillsArr);
                }
                $languages = null;
                if (isset($matchingRecord['languages']) && count($matchingRecord['languages']) > 0) {
                    $languagesArr = $matchingRecord['languages'];
                    $languages = implode("\n", $languagesArr);
                }
                $peopleData = [
                    'forename' => $matchingRecord['first_name'] ?? null,
                    'surname' => $matchingRecord['last_name'] ?? null,
                    'gender' => $matchingRecord['gender'] ?? "Not Applicable",
                    'country' => $matchingRecord['country_full_name'] ?? "Not Applicable",
                    'city' => $matchingRecord['city'] ?? "Not Applicable",
                    'linkedinURL' => $peopleData['linkedin_profile_url'] ?? null,
                    'latest_role' => $currentExperience['title'] ?? null,
                    'company_id' => $company->id ?? null,
                    'company_name' => $company->name ?? null,
                    'start_date' => $startAndEndDate['startDate'] ?? "1900-01-01",
                    'end_date' => $startAndEndDate['endDate'] ?? null,
                    'tenure' => $tenure ?? null,
                    'career_history' => $this->mapExperiencesToString($matchingRecord['experiences']),
                    'educational_history' =>  $this->mapEducationsToString($matchingRecord['education']),
                    'skills' => $skills,
                    'languages' => $languages,
                    'status' => "Submitted" ?? null,
                    'exco' => "Non Exoc" ?? null,
                    'diverse' => "NA" ?? null,
                    'summary' => $matchingRecord['summary'] ?? null,
                    'user_id' => $user->id ?? null,

                ];
                $people = People::create($peopleData);
                $this->insertExperiencesInDb($matchingRecord['experiences'], $people->id);
                $this->insertSkillsInDb($matchingRecord['skills'], $people->id);
            }
        }
    }


    public function addPeoples($selectedData)
    {
        try {
            DB::beginTransaction();
            $user = auth()->user();
            $jobDetail = $this->job;

            if (!$jobDetail) {
                $this->dispatch('toast', 'error', "Job not found!");
            }
            if (empty($selectedData)) {
                return $this->dispatch('toast', 'info', "Please select peoples!");
            }

            $linkedinUrls = $selectedData;
            $existingPeoples = People::whereIn('linkedinURL', $linkedinUrls)->get()->keyBy('linkedinURL');

            if ($user->id !== $jobDetail->user_id) {
                $status = 'Proposed';
            } else {
                $status = 'Approved';
            }


            foreach ($linkedinUrls as $linkedInUrl) {

                if (isset($existingPeoples[$linkedInUrl])) {
                    $people = $existingPeoples[$linkedInUrl];

                    $pipeline = pipeline::where(['people_id' => $people->id, 'job_id' => $jobDetail->id])->first();

                    if (!empty($pipeline))
                        continue;

                    $inputArray = [
                        'role'     => $people->latest_role,
                        'location' => $people->location,
                        'tenure'   => $people->tenure,
                        'company'  => $people->company_name,
                    ];

                    // Create a formatted message for GPT-3
                    $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
                    foreach ($inputArray as $key => $value) {
                        $message .= "$key: \"$value\"\n";
                    }

                    // Call GPT-3 to generate the headline
                    $response = OpenAI::chat()->create([
                        'model' => 'gpt-3.5-turbo',
                        'messages' => [['role' => 'system', 'content' => $message]],
                    ]);

                    $generatedHeadline = $response->choices[0]->message->content;

                    $pipeline = pipeline::create([
                        'job_id'             => $jobDetail->id,
                        'user_id'            => $user->id,
                        'people_id'          => $people->id,
                        'headline'           => $generatedHeadline,
                        'first_name'         => $people->forename,
                        'last_name'          => $people->surname,
                        'middle_name'        => $people->middle_name,
                        'other_name'         => $people->other_name,
                        'gender'             => $people->gender,
                        'diverse'            => $people->diverse,
                        'location'           => $people->location,
                        'summary'            => $people->summary,
                        'country'            => $people->country,
                        'city'               => $people->city,
                        'linkedinURL'        => $people->linkedinURL,
                        'latest_role'        => $people->latest_role,
                        'company_id'         => $people->company_id,
                        'company_name'       => $people->company_name,
                        'start_date'         => $people->start_date ?? "1990-01-01",
                        'end_date'           => $people->end_date,
                        'tenure'             => $people->tenure,
                        'function'           => $people->function,
                        'division'           => $people->division,
                        'seniority'          => "NA",
                        'exco'               => "Non Exco",
                        'career_history'     => "NA",
                        'educational_history' => "NA",
                        'skills'             => "NA",
                        'languages'          => "NA",
                        'skills_match'       => 0,
                        'education_match'    => 0,
                        'location_match'     => 1,
                        'role_match'         => 1,
                        'gender_match'       => 1,
                        'tenure_match'       => 1,
                        'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                        'status'             => $status,
                        'people_type'        => "External-User"
                    ]);

                    /*
                    $jobPeople = JobPeople::create([
                        'pipeline_id'        => $pipeline->id,
                        'job_id'             => $jobDetail->id,
                        'user_id'            => $user->id,
                        'people_id'          => $people->id,
                        'headline'           => $generatedHeadline,
                        'first_name'         => $people->forename,
                        'last_name'          => $people->surname,
                        'middle_name'        => $people->middle_name,
                        'other_name'         => $people->other_name,
                        'gender'             => $people->gender,
                        'diverse'            => $people->diverse,
                        'location'           => $people->location,
                        'linkedinURL'        => $people->linkedinURL,
                        'latest_role'        => $people->latest_role,
                        'company_id'         => $people->company_id,
                        'company_name'       => $people->company_name,
                        'start_date'         => $people->start_date ?? "1900-01-01",
                        'end_date'           => $people->end_date,
                        'tenure'             => $people->tenure,
                        'function'           => $people->function,
                        'division'           => $people->division,
                        'seniority'          => $people->seniority,
                        'career_history'     => $people->career_history,
                        'educational_history'=> $people->educational_history,
                        'skills'             => $people->skills,
                        'languages'          => $people->languages,
                        'readiness'          => $people->readiness,
                        'other_tags'         => $people->other_tags,
                        'country'            => $people->country,
                        'city'               => $people->city,
                        'summary'            => $people->summary,
                        'status'             => $status,
                        'notes'              => "Enter notes here",
                    ]);
                    */

                }
            }
            $this->peoplesList = array_filter($this->peoplesList, function ($person) use ($linkedinUrls) {
                return !in_array($person['linkedin_profile_url'], $linkedinUrls);
            });

            $this->peoplesList = array_values($this->peoplesList);
            $this->dispatch('updateSelectedProp');
            DB::commit();
            $this->dispatch('toast', 'info', 'People added to job successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            $this->dispatch('error', 'info', $e->getMessage());
        }
    }

    public function mapEducationsToString($educations)
    {
        $educationsArr = [];

        foreach ($educations as $education) {
            $educationString = '';

            // Append school name if exists
            if (isset($education['school'])) {
                $educationString .= $education['school'];
            }

            // Append field of study if exists
            if (isset($education['field_of_study'])) {
                $educationString .= ', ' . $education['field_of_study'];
            }

            // Append degree name if exists
            if (isset($education['degree_name'])) {
                $educationString .= ' - ' . $education['degree_name'];
            }

            // Append start year if exists
            if (isset($education['starts_at']['year'])) {
                $educationString .= ' · ' . $education['starts_at']['year'];
            }

            // Append end year if exists
            if (isset($education['ends_at']['year'])) {
                $educationString .= ' - ' . $education['ends_at']['year'];
            }

            // Add the formatted string to the result array if not empty
            if (!empty($educationString)) {
                $educationsArr[] = $educationString;
            }
        }

        // Return the formatted string or null if the array is empty
        return !empty($educationsArr) ? implode("\n", $educationsArr) : null;
    }

    public function monthToString($month)
    {
        $months = [
            1 => 'Jan',
            2 => 'Feb',
            3 => 'Mar',
            4 => 'Apr',
            5 => 'May',
            6 => 'Jun',
            7 => 'Jul',
            8 => 'Aug',
            9 => 'Sep',
            10 => 'Oct',
            11 => 'Nov',
            12 => 'Dec'
        ];

        return $months[$month] ?? '';
    }

    public function mapExperiencesToString($experiences)
    {
        $experiencesArr = [];

        foreach ($experiences as $experience) {
            $experienceString = '';

            // Check if start date exists and has valid month and year
            if (isset($experience['starts_at']) && isset($experience['starts_at']['month']) && isset($experience['starts_at']['year'])) {
                $experienceString .= $this->monthToString($experience['starts_at']['month']) . '-' . $experience['starts_at']['year'];
            }

            // Check if end date exists and has valid month and year
            if (isset($experience['ends_at']) && isset($experience['ends_at']['month']) && isset($experience['ends_at']['year'])) {
                $experienceString .= ' | ' . $this->monthToString($experience['ends_at']['month']) . '-' . $experience['ends_at']['year'];
            } else {
                $experienceString .= ' | Present';
            }

            // Append title if exists
            if (isset($experience['title'])) {
                $experienceString .= ': ' . $experience['title'];
            }

            // Append company name if exists
            if (isset($experience['company'])) {
                $experienceString .= ' at ' . $experience['company'];
            }

            // Add the formatted string to the result array if it is not empty
            if (!empty($experienceString)) {
                $experiencesArr[] = $experienceString;
            }
        }

        // Return the formatted string or null if the array is empty
        return !empty($experiencesArr) ? implode("\n", $experiencesArr) : null;
    }

    public function convertFirstExperienceDate($currentExperience)
    {
        $startDate = null;
        if (isset($currentExperience['starts_at']) && !empty($currentExperience['starts_at'])) {
            // Construct the start date for the first index only
            $startDate = sprintf(
                '%04d-%02d-%02d', // Format as YYYY-MM-DD
                $currentExperience['starts_at']['year'],
                $currentExperience['starts_at']['month'],
                $currentExperience['starts_at']['day']
            );
        }
        $endDate = null;
        if (isset($currentExperience['ends_at']) && !empty($currentExperience['ends_at'])) {
            // Construct the start date for the first index only
            $endDate = sprintf(
                '%04d-%02d-%02d', // Format as YYYY-MM-DD
                $currentExperience['ends_at']['year'],
                $currentExperience['ends_at']['month'],
                $currentExperience['ends_at']['day']
            );
        }

        return [
            "startDate" => $startDate,
            "endDate"   => $endDate
        ];
    }

    public function insertSkillsInDb(array $skills = [], int $peopleId = null)
    {
        if (empty($skills) || !$peopleId) {
            return false;
        }

        foreach ($skills as $skill) {
            // Insert all skills without checking for existence
            DB::table('skills')->insert([
                'people_id' => $peopleId,
                'skill_name' => $skill,
                'skill_type' => 'Professional',
            ]);
        }

        return true;
    }


    function destructDateAndTenure(array $experience)
    {
        $startDate = null;
        $endDate = null;
        $tenure = 0;

        if (isset($experience['starts_at']) && !empty($experience['starts_at'])) {
            $startDateArray = $experience['starts_at'];
            $startDate = sprintf(
                '%04d-%02d-%02d',
                $startDateArray['year'] ?? 0,
                $startDateArray['month'] ?? 0,
                $startDateArray['day'] ?? 0
            );
        }

        if (isset($experience['ends_at']) && !empty($experience['ends_at'])) {
            $endDateArray = $experience['ends_at'];
            $endDate = sprintf(
                '%04d-%02d-%02d',
                $endDateArray['year'] ?? 0,
                $endDateArray['month'] ?? 0,
                $endDateArray['day'] ?? 0
            );
        }

        // Calculate tenure
        if ($startDate) {
            $start_date = Carbon::parse($startDate);

            $end_date = $endDate ? Carbon::parse($endDate) : Carbon::now();

            $tenure_days = $start_date->diffInDays($end_date);
            $tenure = round($tenure_days / 365.25, 1); // Dividing by 365.25 to include leap years
        }

        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'tenure' => $tenure,
        ];
    }

    function getOrCreateCompany(string $companyName)
    {
        // Check if the company exists in the database
        $company = Company::where('name', $companyName)->first();

        if (empty($company)) {
            // Create a new company record
            $company = Company::create([
                'name' => $companyName,
            ]);
        }

        return $company;
    }

    function insertExperiencesInDb(array $experiences = [], int $peopleId = null)
    {
        if (empty($experiences) || !$peopleId) {
            return false;
        }

        foreach ($experiences as $experience) {
            $dateAndTenure = $this->destructDateAndTenure($experience); // Custom helper function to destruct dates and tenure
            $companyId = null;
            $startDate = $dateAndTenure['start_date'] ?? null;
            $endDate = $dateAndTenure['end_date'] ?? null;
            $tenure = $dateAndTenure['tenure'] ?? null;
            $role = $experience['title'] ?? null;

            // Use helper function to get or create a company
            $companyDetail = $this->getOrCreateCompany($experience['company'] ?? null);
            if ($companyDetail) {
                $companyId = $companyDetail['id'];
            }

            // Skip iteration if required values are missing
            if (!$peopleId || !$role || !$companyId || !$startDate) {
                continue;
            }

            // Insert experience without checking for existence
            DB::table('career_histories')->insert([
                'people_id' => $peopleId,
                'role' => $role,
                'past_company_id' => $companyId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'tenure' => $tenure,
            ]);
        }

        return true;
    }

    public function downloadCSV()
    {
        $headerRow = ['linkedinURL'];

        // Dummy data
        $dataRows = [
            [
                'linkedinURL' => 'https://www.linkedin.com/in/xxxx-xxxx-xxxx',
            ],
        ];

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="csv_template.csv"',
        ];

        return Response::stream(function () use ($headerRow, $dataRows) {
            $handle = fopen('php://output', 'w');
            fputcsv($handle, $headerRow);
            foreach ($dataRows as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
        }, 200, $headers);
    }
}
