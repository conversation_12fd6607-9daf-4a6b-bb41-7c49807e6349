<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Company;
use App\Models\Skills;
use App\Models\CareerHistories;
use App\Models\People;
use App\Models\User;
use App\Models\pipeline;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\Job;
use App\Models\JobPeople;
use Carbon\Carbon;
use Livewire\WithFileUploads;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Exception;
use PhpOffice\PhpSpreadsheet\Reader\Exception as ReaderException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\PeopleImport;
use App\Exports\CompaniesExport;
use App\Exports\SkillsExport;
use App\Exports\CareerHistoriesExport;
use App\Exports\PeopleExport;
use Illuminate\Support\Facades\Cache;


class DataCenter extends Component
{
    use WithFileUploads;

    //Variables for updating external people proposed to the system
    public $externalPeople;
    public $summary;
    public $forename = "";
    public $surname = "";
    public $middlename = "";
    public $name = "";
    public $other_name = "";
    public $parent_name = "";
    public $name_abbr = "";
    public $stock_symbol = "";
    public $country = "";
    public $city = "";
    public $co_company_hq = "";
    public $co_company_add = "";
    public $co_company_no = "";
    public $website = "";
    public $annual_revenue;
    public $annual_profit;
    public $annual_expense;
    public $annual_yoy_change;
    public $industry = "";
    public $sector = "";
    public $othername = "";
    public $company;
    public $location;
    public $educational_history;
    public $gender = 1;
    public $exco;
    public $diverse;
    public $linkedinURL = "";
    public $latest_role;
    public $start_date;
    public $end_date;
    public $function;
    public $division;
    public $seniority;
    public $status;
    public $notes;
    public $readiness;
    public $skills = "";
    public $languages = "";
    public $other_tags;
    public $peopleSummary;
    public $user;
    public $previousExperience = [];
    public $refCompany;
    public $step = 1;
    public $registration_status = "";
    public $companyCsvFile;
    public $peopleCsvFile;
    public $updatePeopleCsvFile;
    public $updateSkillsCsvFile;
    public $updateCareerHistoryCsvFile;
    public $updateCompaniesCsvFile;
    public $skillsCsvFile;
    public $chCsvFile;
    public $description = "";
    public $companyId;
    public $career_history;
    public $updatePopup = false;
    public $duplicateUploadPopup = false;
    public $uploadPopup = false;
    public $editPeoplePopup = false;
    public $editPeopleCareerHistoryPopup = false;
    public $editPeopleSkillsPopup = false;
    public $personId;
    public $company_id;
    public $editPeopleCareerHistory = [];
    public $editPeopleSkills = [];
    public $search = '';
    public $page = 1;
    public $recordsPerPage = 100;
    public $selectedCompany = null;
    public $addInd = false;


    protected $listeners = [
        'closeModal',
        'deleteReportedPerson',
        'loadMoreCompanies',
        'submitPerson'
    ];

    public $plans;
    public $jobs;
    public $refreshKey;
    public $personIdReplacedWith = '';
    public $personIdToDelete = '';
    public $deleteSubmittedPeoplePopup = false;
    public $editPeopleId;
    use WithPagination;


    public function __construct()
    {
        ini_set('memory_limit', '1G');  // Set to 1GB
    }

    public function mount()
    {
        $this->refreshKey = now()->timestamp;

    }
    public function render()
    {
        $this->user = auth()->user();
        $externalPeople = DB::table('people')->whereIn('status', ['Submitted', 'Reported']);
        $companies = DB::table('companies')->where('status', 'submitted');


        $account_ids = DB::table('accounts')->whereRaw("FIND_IN_SET(?,relationship_manager_id)", [$this->user->id])->pluck('id')->toArray();
        $user_ids = DB::table('users')->whereIn('account_id', $account_ids)->pluck('id')->toArray();
        $this->plans = DB::table("succession_plans")->whereIn('user_id', $user_ids)->get();

        $this->jobs = DB::table('jobs')->whereIn('user_id', $user_ids)->get();
        $jobIds = DB::table('jobs')->pluck('id')->toArray();
        $jobPeople = DB::table('job_people')
            ->select('job_id', DB::raw('Count(*) as jobcount'))
            ->groupby('job_id')
            ->whereIn('job_id', $jobIds)
            ->get();

        $jobCountsMap = $jobPeople->keyBy('job_id');

        $pipelinePeople = DB::table('pipelines')
            ->select('job_id', DB::raw('COUNT(*) as pipelinecount'))
            ->whereIn('job_id', $jobIds)
            ->groupBy('job_id')
            ->get();

        $pipelineCountsMap = $pipelinePeople->keyBy('job_id');
        // dd($companies);
        $externalcount = People::count();
        // $_externalPeople = $this->externalPeople->paginate();
        $externalupdatecount = DB::table('people')->where('status', 'Submitted')->count();

        $now = Carbon::now();
        $currentQuarterStart = Carbon::createFromDate($now->year, $now->quarter * 3 - 2, 1);
        $currentQuarterEnd = $currentQuarterStart->copy()->endOfQuarter();

        // $topUsers = Cache::remember('topUsers', now()->addHour(), function () use ($currentQuarterStart, $currentQuarterEnd) {
        //     return User::select('users.*')
        //         ->withCount([
        //             'people as created_people_count' => function ($query) use ($currentQuarterStart, $currentQuarterEnd) {
        //                 $query->whereBetween('created_at', [$currentQuarterStart, $currentQuarterEnd])
        //                     ->whereNull('updated_at');
        //             },
        //             'people as updated_people_count' => function ($query) use ($currentQuarterStart, $currentQuarterEnd) {
        //                 $query->whereBetween('updated_at', [$currentQuarterStart, $currentQuarterEnd])
        //                     ->whereNotNull('updated_at');
        //             }
        //         ])
        //         ->get();
        // });

        // // Retrieve the top users from the cached data
        // $dataChampionUser = $topUsers->sortByDesc('created_people_count')->first();
        // $researchChampionUser = $topUsers->sortByDesc('updated_people_count')->first();

        // // Retrieve the users with the highest counts for each metric
        // $dataChampionUser = $topUsers->sortByDesc('created_people_count')->first();
        // $researchChampionUser = $topUsers->sortByDesc('updated_people_count')->first();
        return view('livewire.dataCenter.data-center', [
            'externalcount' => $externalcount, 
            'externalupdatecount' => $externalupdatecount, 
            'externalPeoples' => $externalPeople->paginate(10), 
            'companies' => $companies->paginate(10), 
            'jobs' => $this->jobs, 
            'jobCountsMap' => $jobCountsMap, 
            'pipelineCountsMap' => $pipelineCountsMap
        ]);
    }

    public function loadCompanies($pageNo = 1, $searchQuery = null)
    {
        $offset = ($pageNo - 1) * $this->recordsPerPage;
        $query = Company::query()->whereNotNull('name');

        // Apply search filter
        if (!empty($searchQuery)) {
            $query->where('name', 'LIKE', '%' . $searchQuery . '%');
        }

        // Load companies with offset and limit
        $companies = $query->offset($offset)
                              ->limit($this->recordsPerPage)
                              ->get(['id as value', 'name as label']);

        $this->dispatch('options', $companies);
    }

    public function loadMoreCompanies($pageNo, $query)
    {
        $this->loadCompanies($pageNo, $query);
    }

    public function fetchPeopleDetails($id)
    {
        $this->editPeopleId = $id;
        // Find the people by ID
        $person = People::find($id);
        // If people not found, set error flash message and return
        if (!$person) {
            session()->flash('error', 'People not found!');
            return;
        }

        // Initialize previousExperience array
        $previousExperience = [];


        $this->editPeopleCareerHistory = CareerHistories::with('company')->where(['people_id' => $person->id])->orderBy('start_date', "DESC")->get()->toArray();
        $this->editPeopleSkills = Skills::where(['people_id' => $person->id])->get()->toArray();

        // Populate the form fields with fetched person details
        $this->forename = $person->forename;
        $this->surname = $person->surname;
        $this->middlename = $person->middle_name;
        $this->other_name = $person->other_name;
        $this->gender = $person->gender;
        $this->diverse = $person->diverse;
        $this->country = $person->country;
        $this->city = $person->city;
        $this->linkedinURL = $person->linkedinURL;
        $this->latest_role = $person->latest_role;
        $this->exco = $person->exco;
        $this->company_id = $person->company_id;
        $this->start_date = $person->start_date;
        $this->end_date = $person->end_date;
        $this->function = $person->function;
        $this->division = $person->division;
        $this->seniority = $person->seniority;
        $this->career_history = $person->career_history;
        $this->educational_history = $person->educational_history;
        $this->skills = $person->skills;
        $this->languages = $person->languages;
        $this->other_tags = $person->other_tags;
        $this->summary = $person->summary;
        $this->status = $person->status;
        $this->readiness = $person->readiness;
        $this->notes = $person->notes;
        $this->previousExperience = $previousExperience;
        $this->personId = $person->id;
        $this->editPeoplePopup = true;
        $this->skipRender();
        $company = Company::find($person->company_id);

        $this->dispatch('showCompanyDropdown', $company->id, $company->name, 'updateCompanyId');
        // $this->loadCompanies();

    }

    public function updatePerson()
    {
        // Find the person by ID
        $person = People::find($this->personId); // Assuming $this->personId is set in your component

        // If person not found, set error flash message and return
        if (!$person) {
            session()->flash('error', 'Person not found!');
            return;
        }

        $selectedCompanyId = $this->company_id;
        $company = Company::find($person->company_id);

        try {
            $person->update([
                'forename' => $this->forename,
                'surname' => $this->surname,
                'gender' => $this->gender,
                // 'middle_name' => $this->middle_name,
                'other_name' => $this->other_name,
                'diverse' => $this->diverse,
                'country' => $this->country,
                'city' => $this->city,
                'linkedinURL' => $this->linkedinURL,
                // 'parent_role' => $this->parent_role,
                'latest_role' => $this->latest_role,
                'exco' => $this->exco,
                'company_id' => $this->company_id,
                'company_name' => $company->name ?? null,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                // 'tenure' => $this->tenure,
                'function' => $this->function,
                'division' => $this->division,
                'seniority' => $this->seniority,
                'career_history' => $this->career_history,
                'educational_history' => $this->educational_history,
                'skills' => $this->skills,
                'languages' => $this->languages,
                'other_tags' => $this->other_tags,
                'status' => 'Approved',
                'readiness' => $this->readiness,
                'notes' => $this->notes,
                'user_id' => $this->user->id,
                // 'created_at' => $this->created_at,
                // 'updated_at' => $this->updated_at,
                'summary' => $this->summary,
                // Add other attributes as needed
            ]);

            // // Update or create career histories based on previousExperience array
            // foreach ($this->previousExperience as $experience) {
            //     $company_id = DB::table('companies')->where('name', '=', $experience['company'])->get();
            //     CareerHistories::updateOrCreate(
            //         [
            //             'people_id' => $person->id,
            //             'role' => $experience['role'],
            //             'start_date' => $experience['start_date'],
            //             'end_date' => $experience['end_date'],
            //             'tenure' => $experience['tenure'],
            //             'past_company_id' => $company_id,
            //             // Add other unique keys if necessary
            //         ],
            //         [
            //             // Update or create attributes
            //             'people_id' => $person->id,
            //             'role' => $experience['role'],
            //             'start_date' => $experience['start_date'],
            //             'end_date' => $experience['end_date'],
            //             'tenure' => $experience['tenure'],
            //             'past_company_id' => $company_id,
            //             // Add other career history attributes as needed
            //         ]
            //     );
            // }

            // Set success flash message and close the modal
            $this->dispatch('toast', 'info', 'Person updated successfully!');
            $this->editPeoplePopup = false;
            $this->reset(['forename', 'surname', 'middlename', 'othername', 'country', 'company', 'latest_role', 'city', 'seniority', 'exco', 'linkedinURL', 'skills', 'registration_status', 'languages', 'summary', 'notes', 'readiness', 'status', 'start_date', 'end_date']);
            // $this->closeModal('person');
        } catch (Exception $e) {
            // Catch any exceptions and set error flash message
            session()->flash('error', $e->getMessage());
        }
    }

    public function updateCareerHistory()
    {
        // Delete existing career histories for the individual
        CareerHistories::where(['people_id' => $this->personId])->delete();

        foreach ($this->editPeopleCareerHistory as $index => $history) {

            // Find or create the company
            $company = Company::where('name', trim($history['company']['name']))->first();

            if (empty($company)) {
                $company = Company::create([
                    'name' => $history['company']['name'],
                    'location_id' => 1784674685,
                    'status' => 'submited',
                ]);
            }

            // Create new career history entry
            $careerHistory = new CareerHistories;
            $careerHistory->people_id = $this->personId;
            $careerHistory->role = $history['role'];
            $careerHistory->past_company_id = $company->id;
            $careerHistory->start_date = $history['start_date'];
            $careerHistory->end_date = $history['end_date'] ? $history['end_date'] : null;

            // Calculate Tenure
            if (!empty($history['start_date'])) {
                $startDate = Carbon::parse($history['start_date']);
                if (!empty($history['end_date'])) {
                    // End date is present
                    $endDate = Carbon::parse($history['end_date']);
                    $tenure = $startDate->diffInYears($endDate);
                } else {
                    // End date is empty, use current date
                    $endDate = Carbon::now();
                    $tenure = $startDate->diffInYears($endDate);
                }
                $careerHistory->tenure = $tenure;
            }

            $careerHistory->save();
            
        }
        $this->editPeopleCareerHistoryPopup = false;
        $this->dispatch('toast', 'info', 'Career history updated successfully.');
    }

    public function addCareerHistoryRow()
    {
        $this->editPeopleCareerHistory[] = [
            'role' => '',
            'company' => [
                "name" => ''
            ],
            'start_date' => '',
            'end_date' => '',
        ];
    }

    public function removeCareerHistoryRow($index)
    {
        // Remove the specific row
        unset($this->editPeopleCareerHistory[$index]);

        // Reindex the array to prevent issues with the subsequent rows
        $this->editPeopleCareerHistory = array_values($this->editPeopleCareerHistory);
    }

    public function addSkillsRow()
    {
        $this->editPeopleSkills[] = [
            'skill_name' => ''
        ];
    }

    public function removeSkillsRow($index)
    {
        // Remove the specific row
        unset($this->editPeopleSkills[$index]);

        // Reindex the array to prevent issues with the subsequent rows
        $this->editPeopleSkills = array_values($this->editPeopleSkills);
    }

    public function updateSkills()
    {
        Skills::where(['people_id' => $this->personId])->delete();

        foreach ($this->editPeopleSkills as $index => $skill) {
            Skills::create([
                'skill_name' => $skill['skill_name'],
                'skill_type' => 'Professional',
                'people_id' => $this->personId
            ]);
        }
        $this->editPeopleSkillsPopup = false;
        $this->dispatch('toast', 'info', 'Skill updated successfully.');
    }

    public function fetchCompanyDetails($id)
    {
        $this->companyId = $id;
        // Find the company by ID
        $company = Company::find($id);

        // If company not found, set error flash message and return
        if (!$company) {
            session()->flash('error', 'Company not found!');
            return;
        }

        // Populate the form fields with fetched company details
        $this->name = $company->name;
        $this->other_name = $company->other_name;
        $this->name_abbr = $company->name_abbreviation;
        $this->stock_symbol = $company->stock_symbol;
        $this->parent_name = $company->parent_name;
        $this->description = $company->description;
        $this->co_company_hq = $company->corporate_hq_country;
        $this->co_company_add = $company->corporate_hq_address;
        $this->co_company_no = $company->corporate_hq_phone_number;
        $this->website = $company->website;
        $this->annual_revenue = $company->Annual_Revenue;
        $this->annual_profit = $company->Annual_Net_Profit_Margin;
        $this->annual_expense = $company->Annual_Net_Expenses;
        $this->annual_yoy_change = $company->Annual_YOY_Revenue_Change;
        $this->industry = $company->industry;
        $this->sector = $company->sector;
    }

    public function updateProposedCompany()
    {
        // Find the company by ID
        $company = Company::find($this->companyId); // Assuming $this->companyId is set in your component

        // If company not found, set error flash message and return
        if (!$company) {
            session()->flash('error', 'Company not found!');
            return;
        }

        // Update company attributes based on form input or component properties
        try {
            $company->update([
                'name'    => $this->name,
                'other_name'     => $this->other_name,
                'name_abbreviation' => $this->name_abbr,
                'stock_symbol' => $this->stock_symbol,
                'parent_name'  => $this->parent_name,
                'description'      => $this->description,
                'corporate_hq_country'     => $this->co_company_hq,
                'corporate_hq_address'     => $this->co_company_add,
                'corporate_hq_phone_number' => $this->co_company_no,
                'website' => $this->website,
                'Annual_Revenue'  => (int)$this->annual_revenue,
                'Annual_Net_Profit_Margin' => (int)$this->annual_profit,
                'Annual_Net_Expenses'    => (int)$this->annual_expense,
                'Annual_YOY_Revenue_Change'    => (int)$this->annual_yoy_change,
                'industry'      => $this->industry,
                'sector'      => $this->sector,
            ]);

            // Set success flash message and close the modal
            session()->flash('success', 'Company updated successfully!');
            $this->closeModal('company');
        } catch (Exception $e) {
            // Catch any exceptions and set error flash message
            session()->flash('error', $e->getMessage());
        }
    }

    public function addPreviousExperience()
    {
        $this->previousExperience[] = ['id' => '', 'company' => '', 'role' => '', 'start_date' => '', 'end_date' => ''];
        $indexes = array_keys($this->previousExperience);
        $this->dispatch('addCompanyDropdown', $indexes);

    }

    public function removePreviousExperience($index)
    {
        // dd($index);
        unset($this->previousExperience[$index]);
        $this->previousExperience = array_values($this->previousExperience); // Reindex the array
    }

    public function updateCompany($index, $value)
    {
        $this->previousExperience[$index]['company'] = $value;
    }


    public function uploadPeopleCSV()
    {
        $this->validate([
            'peopleCsvFile' => 'required|mimes:csv,xlsx,xls|max:51200', // accept only csv and excel files with a max size of 2MB
        ], [
            'peopleCsvFile.required' => "Please select excel or csv file."
        ]);
        $this->processPeopleCSV($this->peopleCsvFile);
    }

    public function uploadBulkUpdatePeopleCSV()
    {
        // Validate the uploaded file
        $this->validate([
            'updatePeopleCsvFile' => 'required|mimes:csv,xlsx,xls|max:51200', // accept only CSV and Excel files with a max size of 2MB
        ], [
            'updatePeopleCsvFile.required' => "Please select an Excel or CSV file."
        ]);

        // Process the file
        $this->processBulkUpdatePeopleCSV($this->updatePeopleCsvFile);
    }

    /**
     * Process the uploaded CSV or Excel file for bulk updating people records.
     *
     * @param  mixed $file
     * @return void
     */
    private function processBulkUpdatePeopleCSV($peopleCSVFile)
    {
        try {
            $excelData = Excel::toArray(new PeopleImport, $peopleCSVFile->getRealPath());
            $excelData = $excelData[0];

            // Check if required columns exist
            $requiredColumns = [
                "id",
                "forename",
                "surname",
                "middle_name",
                "other_name",
                "gender",
                "diverse",
                "country",
                "city",
                "linkedinurl",
                "parent_role",
                "latest_role",
                "exco",
                "company_id",
                "company_name",
                "start_date",
                "end_date",
                "tenure",
                "function",
                "division",
                "seniority",
                "educational_history",
                "career_history",
                "skills",
                "languages",
                "other_tags",
                "status",
                "readiness",
                "user_id",
                "summary",
                "created_at",
                "updated_at"
            ];

            $missingColumns = array_diff($requiredColumns, array_keys($excelData[0]));

            if (!empty($missingColumns)) {
                $this->dispatch('customToast', 'error', "The following required columns are missing from the file: " . implode(', ', $missingColumns));
                return;
            }

            if (!$excelData || !is_array($excelData) || count($excelData) <= 0) {
                $this->dispatch('customToast', 'error', "Selected file doesn't have any records to upload.");
                return;
            }

            // Filter out rows where 'id' is null or blank
            $excelData = array_filter($excelData, function ($row) {
                return !empty($row['id']);
            });

            $ids = array_filter(array_column($excelData, 'id'));
            $existingPeopleIds = People::whereIn('id', $ids)->pluck('id')->toArray();

            // Collect all company names
            $companyNames = array_unique(array_map('trim', array_column($excelData, 'company_name')));
            $existingCompanies = Company::whereIn('name', $companyNames)->pluck('id', 'name')->toArray();

            // Prepare data for missing companies
            $newCompanies = [];
            foreach ($companyNames as $companyName) {
                if (!isset($existingCompanies[$companyName])) {
                    $newCompanies[] = [
                        'name'        => $companyName,
                        'status'      => 'Recently_Added'
                    ];
                }
            }

            // Insert new companies in bulk and merge them with existing ones
            if (!empty($newCompanies)) {
                Company::insert($newCompanies);
                $newCompanies = Company::whereIn('name', array_column($newCompanies, 'name'))->pluck('id', 'name')->toArray();
                $existingCompanies = array_merge($existingCompanies, $newCompanies);
            }

            // Collect data for bulk update
            $bulkUpdateData = [];
            $missingIds = [];
            $updatedAt = now();

            foreach ($excelData as $row) {
                // Check if the ID exists in the existing people IDs array
                if (!in_array($row['id'], $existingPeopleIds)) {
                    $missingIds[] = $row['id'];
                    continue;
                }

                // Get the company ID from existing companies, default to null if not found
                $companyId  = $existingCompanies[trim($row['company_name'])] ?? null;
                // Convert start date and calculate tenure
                $startDate  = Carbon::parse($this->convertExcelDateToDate($row['start_date']));
                $tenure     = $startDate->diffInYears(Carbon::now());

                // Prepare data for each row in the order specified
                $data = [
                    'id'                  => $row['id'],
                    'forename'            => $row['forename'],
                    'surname'             => $row['surname'],
                    'middle_name'         => $row['middle_name'],
                    'other_name'          => $row['other_name'],
                    'gender'              => $row['gender'],
                    'diverse'             => $row['diverse'],
                    'country'             => $row['country'],
                    'city'                => $row['city'],
                    'linkedinURL'         => $row['linkedinurl'],
                    'parent_role'         => $row['parent_role'],
                    'latest_role'         => $row['latest_role'],
                    'exco'                => $row['exco'],
                    'company_id'          => $companyId,
                    'company_name'        => $row['company_name'],
                    'start_date'          => $this->convertExcelDateToDate($row['start_date']),
                    'end_date'            => $row['end_date'],
                    'tenure'              => $tenure,
                    'function'            => $row['function'],
                    'division'            => $row['division'],
                    'seniority'           => $row['seniority'],
                    'educational_history' => $row['educational_history'],
                    'career_history'      => $row['career_history'],
                    'skills'              => $row['skills'],
                    'languages'           => $row['languages'],
                    'other_tags'          => $row['other_tags'],
                    'status'              => "Mover",
                    'readiness'           => $row['readiness'],
                    'user_id'             => $row['user_id'],
                    'summary'             => $row['summary'],
                    'updated_at'          => $updatedAt
                ];

                // Add the data to the bulk update array
                $bulkUpdateData[] = $data;
            }

            // Batch updates within a transaction
            DB::transaction(function () use ($existingPeopleIds, $missingIds, $bulkUpdateData) {

                foreach (array_chunk($bulkUpdateData, 1000) as $chunk) {
                    People::upsert(
                        $chunk,
                        ['id'], // Unique key for upsert operation
                        [ // Columns to update if record already exists
                            'forename',
                            'surname',
                            'middle_name',
                            'other_name',
                            'gender',
                            'diverse',
                            'country',
                            'city',
                            'linkedinURL',
                            'parent_role',
                            'latest_role',
                            'exco',
                            'company_id',
                            'company_name',
                            'start_date',
                            'end_date',
                            'tenure',
                            'function',
                            'division',
                            'seniority',
                            'educational_history',
                            'career_history',
                            'skills',
                            'languages',
                            'other_tags',
                            'status',
                            'readiness',
                            'user_id',
                            'summary',
                            'updated_at'
                        ]
                    );
                }
                // Update related tables in bulk
                pipeline::whereIn('people_id', $existingPeopleIds)
                    ->where('people_type', 'like', '%External%')
                    ->update(['mover' => "Mover"]);

                SuccessPeople::whereIn('people_id', $existingPeopleIds)
                    ->where('type', 'like', '%External%')
                    ->update(['mover' => "Mover"]);

                $planIds = pipeline::whereIn('people_id', $existingPeopleIds)
                    ->where('people_type', 'like', '%External%')
                    ->whereNotNull('plan_id')
                    ->pluck('plan_id')
                    ->toArray();

                $jobIds = pipeline::whereIn('people_id', $existingPeopleIds)
                    ->where('people_type', 'like', '%External%')
                    ->whereNotNull('job_id')
                    ->pluck('job_id')
                    ->toArray();

                SuccessionPlan::whereIn('id', $planIds)->update(['mover' => "Mover"]);

                Job::whereIn('id', $jobIds)->update(['mover' => "Mover"]);

                if (count($missingIds) > 0) {
                    $this->dispatch('toast', 'longInfo', "Missing Ids not updated: " . implode(", ", $missingIds));
                } else {
                    $this->dispatch('customToast', 'info', "Data updated successfully.");
                }
                $this->updatePeopleCsvFile = null;
            });
        } catch (Exception $e) {
            Log::error($e);
            $this->dispatch('toast', 'error', $e->getMessage());
        }
    }

    private function updatePersonDetails(People $person, array $data)
    {
        try {
            $oldCompany = $person->company_name;
            $status = '';
            if ($oldCompany != $data['company_name']) {
                $status = 'Mover';
            }
            $person->update([
                'forename' => $data['forename'],
                'surname' => $data['surname'],
                'country' => $data['country'],
                'city' => $data['city'],
                'middle_name' => $data['middle_name'],
                'other_name' => $data['other_name'],
                'gender' => $data['gender'],
                'diverse' => $data['diverse'],
                'summary' => $data['summary'],
                'linkedinURL' => $data['linkedinURL'],
                'latest_role' => $data['latest_role'],
                'company_id' => $data['company_id'],
                'company_name' => $data['company_name'],
                'start_date' => $data['start_date'],
                'end_date' => $data['end_date'],
                'tenure' => $data['tenure'],
                'function' => $data['function'],
                'division' => $data['division'],
                'status'   => $status,
                'seniority' => $data['seniority'],
                'educational_history' => $data['educational_history'],
                'skills' => $data['skills'],
                'languages' => $data['languages'],
                'other_tags' => $data['other_tags'],
                'user_id' => $data['user_id'],
                'readiness' => $data['readiness'],
                'exco' => $data['exco'],
            ]);
        } catch (Exception $exception) {
            session()->flash('error', $exception->getMessage());
        }
    }

    public function uploadBulkUpdateSkillsCSV()
    {
        $this->validate([
            'updateSkillsCsvFile' => 'required|mimes:csv,xlsx,xls|max:51200', // accept only csv and excel files with a max size of 2MB
        ], [
            'updateSkillsCsvFile.required' => "Please select a CSV or Excel file."
        ]);
        $this->processBulkUpdateSkillsCSV($this->updateSkillsCsvFile);
    }

    private function processBulkUpdateSkillsCSV($updateSkillsCsvFile)
    {

        try {
            $excelData = Excel::toArray(new PeopleImport, $updateSkillsCsvFile->getRealPath());
            $excelData = $excelData[0];

            // Check if required columns exist
            $requiredColumns = ['people_id', 'skill_name'];
            $missingColumns = array_diff($requiredColumns, array_keys($excelData[0]));

            if (!empty($missingColumns)) {
                $this->dispatch('customToast', 'error', "The following required columns are missing from the file: " . implode(', ', $missingColumns));
                return;
            }

            if (!$excelData || !is_array($excelData) || count($excelData) <= 0) {
                $this->dispatch('customToast', 'error', "Selected file doesn't have any records to upload.");
                return;
            }

            // Collect unique people_ids
            $peopleIds = array_unique(array_column($excelData, 'people_id'));

            // Delete existing records for these people_ids
            Skills::whereIn('people_id', $peopleIds)->delete();

            // Prepare new skills to insert
            $skillsToInsert = [];
            foreach ($excelData as $skill) {
                $skillsToInsert[] = [
                    'people_id'  => $skill['people_id'],
                    'skill_name' => $skill['skill_name'],
                    'skill_type' => 'Professional',
                ];
            }

            foreach (array_chunk($skillsToInsert, 2000) as $chunk) {
                Skills::insert($chunk);
            }
            $this->updateSkillsCsvFile = null;
            $this->dispatch('customToast', 'info', "Skills updated successfully.");
        } catch (Exception $e) {
            Log::error($e);
            $this->dispatch('customToast', 'error', $e->getMessage());
        }
    }

    private function updateOrCreateSkill(Skills $skill, array $data)
    {
        $skill->update(
            [
                'people_id' => $data['people_id'],
                'skill_name' => $data['skill_name'],
                'skill_type' =>  $data['skill_type'],
            ]
        );
    }

    public function uploadBulkUpdateCareerHistoryCSV()
    {
        $this->validate([
            'updateCareerHistoryCsvFile' => 'required|mimes:csv,xlsx,xls|max:51200', // accept only csv and excel files with a max size of 2MB
        ], [
            'updateCareerHistoryCsvFile.required' => "Please select a CSV or Excel file."
        ]);

        $this->processBulkUpdateCareerHistoryCSV($this->updateCareerHistoryCsvFile);
    }

    private function processBulkUpdateCareerHistoryCSV($updateCareerHistoryCsvFile)
    {
        try {
            $excelData = Excel::toArray(new PeopleImport, $updateCareerHistoryCsvFile->getRealPath());
            $excelData = $excelData[0];

            // Check if required columns exist
            $requiredColumns = ['id', 'people_id', 'role', 'past_company_id', 'start_date', 'end_date', 'tenure'];
            $missingColumns = array_diff($requiredColumns, array_keys($excelData[0]));

            if (!empty($missingColumns)) {
                $this->dispatch('customToast', 'error', "The following required columns are missing from the file: " . implode(', ', $missingColumns));
                return;
            }

            if (!$excelData || !is_array($excelData) || count($excelData) <= 0) {
                $this->dispatch('customToast', 'error', "Selected file doesn't have any records to upload.");
                return;
            }

            // Collect unique people_ids
            $peopleIds = array_unique(array_column($excelData, 'people_id'));

            // Delete existing records for these people_ids
            CareerHistories::whereIn('people_id', $peopleIds)->delete();

            // Load all companies to avoid multiple queries
            $companyIds = array_unique(array_column($excelData, 'past_company_id'));
            $companies = Company::whereIn('id', $companyIds)->pluck('id')->toArray();

            $careerHistoryToInsert = [];
            $missingComanies = [];
            foreach ($excelData as $careerHistory) {

                if (empty($careerHistory['past_company_id'])) {
                    continue;
                }
                if (!in_array($careerHistory['past_company_id'], $companies)) {
                    $missingComanies[] = $careerHistory['past_company_id'];
                    continue;
                }

                // Convert dates from Excel format
                $startDate = !empty($careerHistory['start_date']) ? $this->convertExcelDateToDate($careerHistory['start_date']) : null;
                $endDate = (!empty($careerHistory['end_date']) && strtolower($careerHistory['end_date']) != 'present') ? $this->convertExcelDateToDate($careerHistory['end_date']) : 'present';

                // Calculate tenure
                $tenure = 0;
                if (!empty($startDate)) {
                    $startDateCarbon = Carbon::parse($startDate);
                    $endDateCarbon = $endDate == 'present' ? Carbon::now() : Carbon::parse($endDate);
                    $tenure = $startDateCarbon->floatDiffInYears($endDateCarbon);
                }

                // Prepare the data to insert
                $careerHistoryToInsert[] = [
                    "people_id"       => $careerHistory['people_id'],
                    "role"            => $careerHistory['role'],  // Correctly assign role
                    "past_company_id" => $careerHistory['past_company_id'],
                    "start_date"      => $startDate,
                    "end_date"        => $endDate == 'present' ? null : $endDate,
                    "tenure"          => $tenure
                ];
            }

            // Insert the career histories in bulk
            if (!empty($careerHistoryToInsert)) {
                CareerHistories::insert($careerHistoryToInsert);
            }
            $this->updateCareerHistoryCsvFile = null;

            if (count($missingComanies) > 0) {
                $this->dispatch('customToast', 'error', "Companies does not exists in our system. " . implode(',', $missingComanies));
            }
            $this->dispatch('customToast', 'info', "Career histories updated successfully.");
        } catch (Exception $e) {
            Log::error($e);
            $this->dispatch('customToast', 'error', $e->getMessage());
        }
    }

    private function updateOrCreateCareerHistory(CareerHistories $careerHistory, array $data)
    {
        $careerHistory->update(
            [
                'people_id'    => $data['people_id'],
                'past_company_id' => $data['past_company_id'],
                'role'     => $data['role'],
                'start_date'   => $data['start_date'],
                'end_date'     => $data['end_date'],
                'tenure'     => $data['tenure'],
            ]
        );
    }

    public function uploadBulkUpdateCompaniesCSV()
    {
        $this->validate([
            'updateCompaniesCsvFile' => 'required|mimes:csv,xlsx,xls|max:51200', // accept only csv and excel files with a max size of 2MB
        ], [
            'updateCompaniesCsvFile.required' => "Please select a CSV or Excel file."
        ]);

        $this->processBulkUpdateCompaniesCSV($this->updateCompaniesCsvFile);
    }

    private function processBulkUpdateCompaniesCSV($updateCompaniesCsvFile)
    {
        try {
            $excelData = Excel::toArray(new PeopleImport, $updateCompaniesCsvFile->getRealPath());
            $excelData = $excelData[0];

            // Check if required columns exist
            $requiredColumns = [
                'id',
                'name',
                'other_name',
                'name_abbreviation',
                'description',
                'corporate_hq_country',
                'corporate_hq_address',
                'website',
                'corporate_hq_phone_number',
                'chair',
                'ceo',
                'type',
                'industry',
                'sector',
                'stock_symbol',
                'annual_revenue',
                'annual_net_profit_margin',
                'annual_net_expenses',
                'annual_yoy_revenue_change',
                'company_employee_count',
                'image',
                'status',
                'parent_company',
                'annual_expenses',
                'currency',
                'earnings_before_interest_taxes'
            ];
            $missingColumns = array_diff($requiredColumns, array_keys($excelData[0]));

            if (!empty($missingColumns)) {
                $this->dispatch('customToast', 'error', "The following required columns are missing from the file: " . implode(', ', $missingColumns));
                return;
            }

            if (!$excelData || !is_array($excelData) || count($excelData) <= 0) {
                $this->dispatch('customToast', 'error', "Selected file doesn't have any records to upload.");
                return;
            }

            // Extract company names from the Excel data
            $companyNames = array_column($excelData, 'name');

            // Fetch all existing companies in one query
            $existingCompanies = Company::whereIn('name', $companyNames)->get()->keyBy('name');

            // Initialize arrays to hold the data for insertion or update
            $companiesToUpdate = [];
            $companiesToInsert = [];

            foreach ($excelData as $key => $row) {
                if (empty($row['name'])) {
                    continue;
                }
                // Common data structure
                $companyData = [
                    'name'                      => $row['name'],
                    'other_name'                => $row['other_name'],
                    'name_abbreviation'         => $row['name_abbreviation'],
                    'description'               => $row['description'],
                    'corporate_hq_country'      => $row['corporate_hq_country'],
                    'corporate_hq_address'      => $row['corporate_hq_address'],
                    'website'                   => $row['website'],
                    'corporate_hq_phone_number' => $row['corporate_hq_phone_number'],
                    'chair'                     => $row['chair'],
                    'ceo'                       => $row['ceo'],
                    'type'                      => $row['type'],
                    'industry'                  => $row['industry'],
                    'sector'                    => $row['sector'],
                    'stock_symbol'              => $row['stock_symbol'],
                    'Annual_Revenue'            => $row['annual_revenue'],
                    'Annual_Net_Profit_Margin'  => $row['annual_net_profit_margin'],
                    'Annual_Net_Expenses'       => $row['annual_net_expenses'],
                    'Annual_YOY_Revenue_Change' => $row['annual_yoy_revenue_change'],
                    'company_employee_count'    => $row['company_employee_count'],
                    'image'                     => $row['image'],
                    'status'                    => $row['status'],
                    'parent_company'            => $row['parent_company'],
                    'annual_expenses'           => $row['annual_expenses'],
                    'Currency'                  => $row['currency'],
                ];

                // Check if annual_net_expenses is numeric and clean if needed
                if (isset($companyData['Annual_Net_Expenses'])) {
                    $companyData['Annual_Net_Expenses'] = is_numeric($companyData['Annual_Net_Expenses']) ? $companyData['Annual_Net_Expenses'] : null;
                }

                if (isset($existingCompanies[$row['name']])) {
                    // If the company exists, add the 'id' for update
                    $companyData['id'] = $existingCompanies[$row['name']]->id;
                    $companiesToUpdate[] = $companyData;
                } else {
                    // If the company doesn't exist, prepare for insertion
                    $companyData['created_at'] = now();
                    $companiesToInsert[] = $companyData;
                }
            }

            //Use bulk update or insert operations to minimize queries
            if (!empty($companiesToUpdate)) {
                $chunks = array_chunk($companiesToUpdate, 2000);

                foreach ($chunks as $chunk) {
                    DB::table('companies')->upsert($chunk, ['id'], array_keys($chunk[0]));
                }
            }

            if (!empty($companiesToInsert)) {
                Company::insert($companiesToInsert);
            }

            $this->updateCompaniesCsvFile = null;

            $this->dispatch('customToast', 'info', 'Companies have been successfully updated or inserted.');
        } catch (Exception $e) {
            Log::error($e);
            $this->dispatch('customToast', 'error', $e->getMessage());
        }
    }

    private function updateOrCreateCompany(Company $company, array $data)
    {
        $company->update(
            [
                'name'                       => $data['name'],
                'other_name'                 => $data['other_name'],
                'name_abbreviation'          => $data['name_abbreviation'],
                'description'                => $data['description'],
                'corporate_hq_country'       => $data['corporate_hq_country'],
                'corporate_hq_address'       => $data['corporate_hq_address'],
                'website'                    => $data['website'],
                'corporate_hq_phone_number'  => $data['corporate_hq_phone_number'],
                'chair'                      => $data['chair'],
                'ceo'                        => $data['ceo'],
                'type'                       => $data['type'],
                'industry'                   => $data['industry'],
                'sector'                     => $data['sector'],
                'stock_symbol'               => $data['stock_symbol'],
                'annual_revenue'             => $data['annual_revenue'],
                'annual_net_profit_margin'   => $data['annual_net_profit_margin'],
                'annual_net_expenses'        => $data['annual_net_expenses'],
                'annual_yoy_revenue_change'  => $data['annual_yoy_revenue_change'],
                'company_employee_count'     => $data['company_employee_count'],
                'image'                      => $data['image'],
                'status'                     => $data['status'],
                'parent_company'             => $data['parent_company'],
                'annual_expenses'            => $data['annual_expenses'],
                'currency'                   => $data['currency'],
                'earnings_before_interest_taxes' => $data['earnings_before_interest_taxes'],
            ]
        );
    }

    public function downloadCompaniesCSV()
    {

        return Excel::download(new CompaniesExport, 'companies.csv', \Maatwebsite\Excel\Excel::CSV);

        // // Retrieve all company data from the database
        // $companies = Company::all();

        // // Generate CSV content
        // $csvData = $this->generateCompaniesCSV($companies);

        // // Set headers for CSV download
        // $headers = [
        //     'Content-Type' => 'text/csv',
        //     'Content-Disposition' => 'attachment; filename="companies.csv"',
        // ];

        // // Return the response as a download
        // return response()->streamDownload(function () use ($csvData) {
        //     echo $csvData;
        // }, 'companies.csv', $headers);
    }

    // private function generateCompaniesCSV($data)
    // {
    //     // Initialize CSV content
    //     $csv = '';

    //     // Add headers to CSV
    //     $csv .= "ID,Name,Other Name,Name Abbreviation,Description,Corporate HQ Country,Corporate HQ Address,Website,Corporate HQ Phone Number,Chair,CEO,Type,Industry,Sector,Stock Symbol,Annual Revenue,Annual Net Profit Margin,Annual Net Expenses,Annual YOY Revenue Change,Company Employee Count,Image,Status,Parent Company,Annual Expenses,Currency,Earnings Before Interest Taxes\n";

    //     // Add data rows to CSV
    //     foreach ($data as $company) {
    //         $csv .= "{$this->escapeCSVField($company->id)},";
    //         $csv .= "{$this->escapeCSVField($company->name)},";
    //         $csv .= "{$this->escapeCSVField($company->other_name)},";
    //         $csv .= "{$this->escapeCSVField($company->name_abbreviation)},";
    //         $csv .= "{$this->escapeCSVField($company->description)},";
    //         $csv .= "{$this->escapeCSVField($company->corporate_hq_country)},";
    //         $csv .= "{$this->escapeCSVField($company->corporate_hq_address)},";
    //         $csv .= "{$this->escapeCSVField($company->website)},";
    //         $csv .= "{$this->escapeCSVField($company->corporate_hq_phone_number)},";
    //         $csv .= "{$this->escapeCSVField($company->chair)},";
    //         $csv .= "{$this->escapeCSVField($company->ceo)},";
    //         $csv .= "{$this->escapeCSVField($company->type)},";
    //         $csv .= "{$this->escapeCSVField($company->industry)},";
    //         $csv .= "{$this->escapeCSVField($company->sector)},";
    //         $csv .= "{$this->escapeCSVField($company->stock_symbol)},";
    //         $csv .= "{$company->Annual_Revenue},";
    //         $csv .= "{$company->Annual_Net_Profit_Margin},";
    //         $csv .= "{$company->Annual_Net_Expenses},";
    //         $csv .= "{$company->Annual_YOY_Revenue_Change},";
    //         $csv .= "{$company->company_employee_count},";
    //         $csv .= "{$this->escapeCSVField($company->image)},";
    //         $csv .= "{$this->escapeCSVField($company->status)},";
    //         $csv .= "{$this->escapeCSVField($company->parent_company)},";
    //         $csv .= "{$this->escapeCSVField($company->annual_exepnses)},";
    //         $csv .= "{$this->escapeCSVField($company->Currency)},";
    //         $csv .= "{$this->escapeCSVField($company->Earnings_Before_Interest_Taxes)}\n";
    //     }

    //     return $csv;
    // }

    public function downloadSkillsCSV()
    {

        return Excel::download(new SkillsExport, 'skills.csv', \Maatwebsite\Excel\Excel::CSV);

        // // Retrieve all skills data from the database
        // $skills = Skills::all();

        // // Generate CSV content
        // $csvData = $this->generateSkillsCSV($skills);

        // // Set headers for CSV download
        // $headers = [
        //     'Content-Type' => 'text/csv',
        //     'Content-Disposition' => 'attachment; filename="skills.csv"',
        // ];

        // // Return the response as a download
        // return response()->streamDownload(function () use ($csvData) {
        //     echo $csvData;
        // }, 'skills.csv', $headers);
    }

    // private function generateSkillsCSV($data)
    // {
    //     // Initialize CSV content
    //     $csv = '';

    //     // Add headers to CSV
    //     $csv .= "ID,People ID,Skill Name,Skill Type\n";

    //     // Add data rows to CSV
    //     foreach ($data as $skill) {
    //         $csv .= "{$skill->id},";
    //         $csv .= "{$skill->people_id},";
    //         $csv .= "{$this->escapeCSVField($skill->skill_name)},";
    //         $csv .= "{$this->escapeCSVField($skill->skill_type)}\n";
    //     }

    //     return $csv;
    // }

    public function downloadCareerHistoriesCSV()
    {
        ini_set('memory_limit', '2G');  // Set to 1GB
        return Excel::download(new CareerHistoriesExport, 'career_histories.csv', \Maatwebsite\Excel\Excel::CSV);

        // // Retrieve all career histories data from the database
        // $careerHistories = CareerHistories::all();

        // // Generate CSV content
        // $csvData = $this->generateCareerHistoriesCSV($careerHistories);

        // // Set headers for CSV download
        // $headers = [
        //     'Content-Type' => 'text/csv',
        //     'Content-Disposition' => 'attachment; filename="career_histories.csv"',
        // ];

        // // Return the response as a download
        // return response()->streamDownload(function () use ($csvData) {
        //     echo $csvData;
        // }, 'career_histories.csv', $headers);
    }

    // private function generateCareerHistoriesCSV($data)
    // {
    //     // Initialize CSV content
    //     $csv = '';

    //     // Add headers to CSV
    //     $csv .= "ID,People ID,Role,Past Company ID,Start Date,End Date,Tenure\n";

    //     // Add data rows to CSV
    //     foreach ($data as $careerHistory) {
    //         $csv .= "{$careerHistory->id},";
    //         $csv .= "{$careerHistory->people_id},";
    //         $csv .= "{$this->escapeCSVField($careerHistory->role)},";
    //         $csv .= "{$careerHistory->past_company_id},";
    //         $csv .= "{$careerHistory->start_date},";
    //         $csv .= "{$careerHistory->end_date},";
    //         $csv .= "{$careerHistory->tenure}\n";
    //     }

    //     return $csv;
    // }

    public function downloadPeopleCSV()
    {
        $exporter = new PeopleExport();
        return $exporter->download('people_export.xlsx');
        
        //return Excel::download(new PeopleExport, 'people.csv',   \Maatwebsite\Excel\Excel::CSV);

        // // Retrieve all people data from the database
        // $people = People::all();

        // // Generate CSV content
        // $csvData = $this->generatePeopleCSV($people);

        // // Set headers for CSV download
        // $headers = [
        //     'Content-Type' => 'text/csv',
        //     'Content-Disposition' => 'attachment; filename="people.csv"',
        // ];

        // // Return the response as a download
        // return response()->streamDownload(function () use ($csvData) {
        //     echo $csvData;
        // }, 'people.csv', $headers);
    }

    // private function generatePeopleCSV($data)
    // {
    //     // Initialize CSV content
    //     $csv = '';

    //     // Add headers to CSV
    //     $csv .= "ID,Forename,Surname,Middle Name,Other Name,Gender,Diverse,Country,City,LinkedIn URL,Parent Role,Latest Role,Exco,Company ID,Company,Start Date,End Date,Tenure,Function,Division,Seniority,Educational History,Career History,Skills,Languages,Other Tags,Status,Readiness,User ID,Summary,Created At,Updated At\n";

    //     // Add data rows to CSV
    //     foreach ($data as $person) {
    //         $csv .= "{$person->id},";
    //         $csv .= "{$this->escapeCSVField($person->forename)},";
    //         $csv .= "{$this->escapeCSVField($person->surname)},";
    //         $csv .= "{$this->escapeCSVField($person->middle_name)},";
    //         $csv .= "{$this->escapeCSVField($person->other_name)},";
    //         $csv .= "{$this->escapeCSVField($person->gender)},";
    //         $csv .= "{$this->escapeCSVField($person->diverse)},";
    //         $csv .= "{$this->escapeCSVField($person->country)},";
    //         $csv .= "{$this->escapeCSVField($person->city)},";
    //         $csv .= "{$this->escapeCSVField($person->linkedinURL)},";
    //         $csv .= "{$this->escapeCSVField($person->parent_role)},";
    //         $csv .= "{$this->escapeCSVField($person->latest_role)},";
    //         $csv .= "{$this->escapeCSVField($person->exco)},";
    //         $csv .= "{$this->escapeCSVField($person->company_id)},";
    //         $csv .= "{$this->escapeCSVField($person->company_name)},";
    //         $csv .= "{$person->start_date},";
    //         $csv .= "{$person->end_date},";
    //         $csv .= "{$person->tenure},";
    //         $csv .= "{$person->function},";
    //         $csv .= "{$person->division},";
    //         $csv .= "{$this->escapeCSVField($person->seniority)},";
    //         $csv .= "{$this->escapeCSVField($person->educational_history)},";
    //         $csv .= "{$this->escapeCSVField($person->career_history)},";
    //         $csv .= "{$this->escapeCSVField($person->skills)},";
    //         $csv .= "{$this->escapeCSVField($person->languages)},";
    //         $csv .= "{$this->escapeCSVField($person->other_tags)},";
    //         $csv .= "{$this->escapeCSVField($person->status)},";
    //         $csv .= "{$this->escapeCSVField($person->readiness)},";
    //         $csv .= "{$person->user_id},";
    //         $csv .= "{$this->escapeCSVField($person->summary)},";
    //         $csv .= "{$person->created_at},";
    //         $csv .= "{$person->updated_at}\n";
    //     }

    //     return $csv;
    // }

    private function escapeCSVField($value)
    {
        // If the value contains a comma, newline, or double quote, enclose it in double quotes and escape any existing double quotes
        if (strpos($value, ',') !== false || strpos($value, "\n") !== false || strpos($value, '"') !== false) {
            return '"' . str_replace('"', '""', $value) . '"';
        }
        return $value;
    }

    public function bulkUpdatePeople()
    {
        $this->validate([
            'peopleCsvFile' => 'required|mimes:csv,xlsx,xls|max:2048', // validate file
        ], [
            'peopleCsvFile.required' => "Please select a CSV or Excel file.",
        ]);

        try {
            $filePath = $this->peopleCsvFile->getRealPath();
            $reader = IOFactory::createReaderForFile($filePath);
            $spreadsheet = $reader->load($filePath);
            $sheet = $spreadsheet->getActiveSheet();

            // Extract data from the sheet as an array
            $peopleData = $sheet->toArray();

            foreach ($peopleData as $key => $row) {
                // Assuming your CSV columns are named accordingly
                $person = People::find($row['id']);

                if ($person) {
                    $person->forename = $row['forename'] ?? $person->forename;
                    $person->surname = $row['surname'] ?? $person->surname;
                    $person->middle_name = $row['middle_name'] ?? $person->middle_name;
                    $person->other_name = $row['other_name'] ?? $person->other_name;
                    $person->gender = $row['gender'] ?? $person->gender;
                    $person->diverse = $row['diverse'] ?? $person->diverse;
                    $person->country = $row['country'] ?? $person->country;
                    $person->city = $row['city'] ?? $person->city;
                    $person->linkedinURL = $row['linkedinURL'] ?? $person->linkedinURL;
                    $person->parent_role = $row['parent_role'] ?? $person->parent_role;
                    $person->latest_role = $row['latest_role'] ?? $person->latest_role;
                    $person->exco = $row['exco'] ?? $person->exco;
                    $person->company_id = $row['company_id'] ?? $person->company_id;
                    $person->company_name = $row['company_name'] ?? $person->company_name;
                    $person->start_date = (int) $row['start_date'] ?? $person->start_date;
                    $person->end_date = (int) $row['end_date'] ?? $person->end_date;
                    $person->tenure = (int) $row['tenure'] ?? $person->tenure;
                    $person->function = (int) $row['function'] ?? $person->function;
                    $person->division = (int) $row['division'] ?? $person->division;
                    $person->seniority = $row['seniority'] ?? $person->seniority;
                    $person->career_history = $row['career_history'] ?? $person->career_history;
                    $person->educational_history = $row['educational_history'] ?? $person->educational_history;
                    $person->skills = $row['skills'] ?? $person->skills;
                    $person->languages = $row['languages'] ?? $person->languages;
                    $person->other_tags = $row['other_tags'] ?? $person->other_tags;
                    $person->status = $row['status'] ?? $person->status;
                    $person->readiness = $row['readiness'] ?? $person->readiness;
                    $person->user_id = $this->user->id;
                    $person->created_at = $row['created_at'] ?? $person->created_at;
                    $person->updated_at = $row['updated_at'] ?? $person->updated_at;
                    $person->summary = $row['summary'] ?? $person->summary;

                    $person->save();
                }
            }

            session()->flash('success', "Bulk update completed successfully.");
            $this->reset(['peopleCsvFile']);
        } catch (\Exception $exception) {
            session()->flash('error', $exception->getMessage());
        }
    }

    public function uploadSkillsCSV()
    {
        $this->validate([
            'skillsCsvFile' => 'required|mimes:csv,xlsx,xls|max:51200', // accept only csv and excel files with a max size of 50MB
        ], [
            'skillsCsvFile.required' => "Please select excel or csv file."
        ]);
        $this->processSkillsCSV($this->skillsCsvFile);
    }

    public function uploadCHCSV()
    {
        $this->validate([
            'chCsvFile' => 'required|mimes:csv,xlsx,xls|max:51200', // accept only csv and excel files with a max size of 2MB
        ], [
            'chCsvFile.required' => "Please select excel or csv file."
        ]);
        $this->processCHCSV($this->chCsvFile);
    }

    public function uploadCompanyCSV()
    {
        $this->validate([
            'companyCsvFile' => 'required|mimes:csv,xlsx,xls|max:2048', // accept only csv and excel files with a max size of 2MB
        ], [
            'companyCsvFile.required' => "Please select excel or csv file."
        ]);
        $this->processCompanyCSV($this->companyCsvFile);
    }

    private function processPeopleCSV($peopleCSVFile)
    {
        try {
            ini_set('memory_limit', '2G');  // Set to 1GB
            $excelData = Excel::toArray(new PeopleImport, $peopleCSVFile->getRealPath());
            $excelData = $excelData[0];

            // Filter out rows where 'id' is null or blank
            $excelData = array_filter($excelData, function ($row) {
                return !empty($row['id']);
            });

            if (empty($excelData) || !is_array($excelData)) {
                $this->dispatch('customToast', 'error', "Selected file doesn't have any records to upload.");
                return;
            }

            if (!isset($excelData[0]['forename']) || !isset($excelData[0]['surname'])) {
                $this->dispatch('customToast', 'error', "Invalid file format.");
                return;
            }

            $ids = array_column($excelData, 'id');
            $forenameSurnameRole = array_map(function ($row) {
                return [
                    'forename' => $row['forename'],
                    'surname' => $row['surname'],
                    'linkedinurl' => $row['linkedinurl'],
                    'latest_role' => $row['latest_role']
                ];
            }, $excelData);

            // Batch load existing records by ID and by forename, surname, linkedinURL, and latest_role
            $existingRecordsById = People::whereIn('id', $ids)->get()->keyBy('id');
            $existingRecordsByDetail = People::where(function ($query) use ($forenameSurnameRole) {
                foreach ($forenameSurnameRole as $detail) {
                    $query->orWhere($detail);
                }
            })->get()->keyBy(function ($item) {
                return $item->forename . '-' . $item->surname . '-' . $item->linkedinURL . '-' . $item->latest_role;
            });

            // Load all companies to avoid multiple queries
            $companyNames = array_unique(array_column($excelData, 'company_name'));
            $companies = Company::whereIn('name', $companyNames)->get()->keyBy('name');

            $newPeople = [];
            $duplicates = [];

            foreach ($excelData as $key => $row) {
                $forename = $row['forename'];
                $surname = $row['surname'];
                $role = $row['latest_role'];

                $recordId = $row['id'];
                $recordKey = $forename . '-' . $surname . '-' . $row['linkedinurl'] . '-' . $role;

                // Check if the record exists by ID or by detailed attributes
                if (isset($existingRecordsById[$recordId]) || isset($existingRecordsByDetail[$recordKey])) {
                    $duplicates[] = $existingRecordsById[$recordId]->id ?? $existingRecordsByDetail[$recordKey]->forename ?? null;
                    continue;
                }

                // Get or create company
                $companyName = trim($row['company_name']);
                if (!isset($companies[$companyName])) {
                    $companies[$companyName] = Company::create([
                        'name'        => $companyName,
                        'status'      => 'Recently_Added'
                    ]);
                }

                $startDate = Carbon::parse($this->convertExcelDateToDate($row['start_date']));
                $tenure = $startDate->diffInYears(Carbon::now());

                // Prepare the new people data
                $newPeople[] = [
                    'id'          => $row['id'],
                    'forename'    => $row['forename'],
                    'middle_name' => $row['middle_name'],
                    'surname'     => $row['surname'],
                    'gender'      => $row['gender'],
                    'diverse'     => $row['diverse'],
                    'country'     => $row['country'],
                    'city'        => $row['city'],
                    'linkedinURL' => $row['linkedinurl'],
                    'company_id'  => $companies[$companyName]->id,
                    'company_name' => $companies[$companyName]->name,
                    'latest_role' => $row['latest_role'],
                    'exco'        => $row['exco'],
                    'start_date'  => $this->convertExcelDateToDate($row['start_date']),
                    'tenure'      => $tenure,
                    'function'    => $row['function'],
                    'summary'     => $row['summary'],
                    'other_tags'  => $row['other_tags'],
                    'skills'      => $row['skills'],
                    'user_id'     => auth()->user()->id,
                    'status'      => 'Recently_Added',
                    'readiness'   => $row['readiness'],
                    'created_at'  => now()
                ];
            }

            // Insert new people in one batch
            if (!empty($newPeople)) {
                People::insert($newPeople);
            }

            $this->peopleCsvFile = null;

            if (count($duplicates) > 0) {
                $this->dispatch('toast', 'longInfo', "Duplicate records not uploaded: " . implode(", ", $duplicates));
            } else {
                $this->dispatch('customToast', 'info', "File uploaded successfully.");
            }
        } catch (Exception $exception) {
            // dd($exception);
            Log::error($exception->getMessage(), ['trace' => $exception->getTraceAsString()]);
            session()->flash('error', "An error occurred while processing the file: " . $exception->getMessage());
        }
    }

    private function processSkillsCSV($skillsCSVFile)
    {
        try {

            $excelData = Excel::toArray(new PeopleImport, $skillsCSVFile->getRealPath());

            $duplicates = [];
            $excelData = $excelData[0];

            if (!$excelData || !is_array($excelData) || count($excelData) <= 0) {
                $this->dispatch('customToast', 'error', "Selected file don't have any record to upload.");
                return;
            }

            if (!isset($excelData[0]['people_id']) || !isset($excelData[0]['skill_name'])) {
                $this->dispatch('customToast', 'error', "Invalid file format.");
                return;
            }

            $skillsDataArr = [];
            $combinations = [];
            $invalidRecordCount = 0;

            foreach ($excelData as $key => $row) {

                $peopleId = $row['people_id'];
                $skillName = $row['skill_name'];
                $skillType = isset($row['skill_type']) ? $row['skill_type'] : "Professional";

                if (!$peopleId || !$skillName) {
                    $invalidRecordCount = $invalidRecordCount  + 1;
                    continue;
                }

                $combinations[] = [
                    'people_id' => $peopleId,
                    'skill_name' => $skillName,
                    'skill_type' => $skillType
                ];
            }

            // Remove duplicates in combinations (optional)
            $combinations = array_unique($combinations, SORT_REGULAR);

            $existingRecords = Skills::whereIn('people_id', array_column($combinations, 'people_id'))
                ->whereIn('skill_name', array_column($combinations, 'skill_name'))
                ->get()
                ->groupBy(function ($item) {
                    return $item->people_id . '|' . $item->skill_name;
                });

            foreach ($combinations as $combination) {
                $key = $combination['people_id'] . '|' . $combination['skill_name'];

                if (isset($existingRecords[$key])) {
                    $duplicates[] = $combination['skill_name'];
                } else {
                    $skillsDataArr[] = [
                        'people_id' => $combination['people_id'],
                        'skill_name' => $combination['skill_name'],
                        'skill_type' => $combination['skill_type'],
                        'created_at' => now()
                    ];
                }
            }

            if ($skillsDataArr && count($skillsDataArr) > 0) {
                Skills::insert($skillsDataArr);
            }

            $this->skillsCsvFile = null;

            if ($invalidRecordCount > 0) {
                $this->dispatch('customToast', 'info', "There was " . $invalidRecordCount . " invalid records. (Either people_id or skill_name are missing)");
            }

            if (count($duplicates) > 0) {
                $this->dispatch('customToast', 'info', "There was " . count($duplicates) . " duplicate records");
            }

            $this->dispatch('customToast', 'success', "File uploaded successfully");

            return;
        } catch (Exception $exception) {
            Log::error($exception);
            session()->flash('error', $exception->getMessage());
            return;
        }
    }

    private function processCHCSV($chCSVFile)
    {
        try {

            $excelData = Excel::toArray(new PeopleImport, $chCSVFile->getRealPath());

            $duplicates = [];
            $excelData = $excelData[0];

            if (!$excelData || !is_array($excelData) || count($excelData) <= 0) {
                $this->dispatch('customToast', 'error', "Selected file don't have any record to upload.");
                return;
            }

            if (!isset($excelData[0]['people_id']) || !isset($excelData[0]['past_company_id']) || !isset($excelData[0]['role'])) {
                $this->dispatch('customToast', 'error', "Invalid file format.");
                return;
            }

            $combinations = [];
            $companyMap = []; // To cache company lookups
            $companyIds = []; // To collect all pastCompanyNames for batch lookup
            $invalidRecordCount = 0;

            foreach ($excelData as $key => $row) {
                $peopleId = $row['people_id'];
                $role = $row['role'];
                $pastCompanyId = $row['past_company_id'];
                $startDate = isset($row['start_date']) && $row['start_date'] ? $this->convertExcelDateToDate($row['start_date']) : null;
                $endDate = isset($row['end_date']) && $row['end_date']
                    ? ($row['end_date'] == 'Present' ? 'Present' : $this->convertExcelDateToDate($row['end_date']))
                    : null;
                $tenure = $row['tenure'];

                if ($peopleId && $pastCompanyId) {
                    $combinations[] = [
                        'people_id' => $peopleId,
                        'role' => $role,
                        'past_company_id' => $pastCompanyId
                    ];
                }
            }

            // Step 2: Check existing records in bulk
            $chunkSize = 500; // Adjust the chunk size based on your needs
            $peopleIds = array_column($combinations, 'people_id');
            $roles = array_column($combinations, 'role');
            $pastCompanyIds = array_column($combinations, 'past_company_id');

            // Create a collection to store existing records
            $existingRecords = collect();

            // Process in chunks
            foreach (array_chunk($peopleIds, $chunkSize) as $peopleIdChunk) {
                $existingRecordsChunk = CareerHistories::whereIn('people_id', $peopleIdChunk)
                    ->whereIn('role', array_slice($roles, 0, count($peopleIdChunk)))
                    ->whereIn('past_company_id', array_slice($pastCompanyIds, 0, count($peopleIdChunk)))
                    ->get()
                    ->map(function ($item) {
                        return $item->people_id . '|' . $item->role . '|' . $item->past_company_id;
                    });

                $existingRecords = $existingRecords->merge($existingRecordsChunk);
            }

            // Convert to flip
            $existingRecords = $existingRecords->flip();
            // $existingRecords = CareerHistories::whereIn('people_id', array_column($combinations, 'people_id'))
            //                                     ->whereIn('role', array_column($combinations, 'role'))
            //                                     ->whereIn('past_company_id', array_column($combinations, 'past_company_id'))
            //                                     ->get()
            //                                     ->map(function ($item) {
            //                                         return $item->people_id . '|' . $item->role . '|' . $item->past_company_id;
            //                                     })
            //                                     ->flip();

            // Step 3: Process the results
            $toInsert = [];

            foreach ($excelData as $key => $row) {
                $peopleId = $row['people_id'];
                $role = $row['role'];
                $startDate = isset($row['start_date']) && $row['start_date'] ? $this->convertExcelDateToDate($row['start_date']) : null;
                $endDate = isset($row['end_date']) && $row['end_date']
                    ? ($row['end_date'] == 'Present' ? 'Present' : $this->convertExcelDateToDate($row['end_date']))
                    : null;
                $tenure = $row['tenure'];
                $pastCompanyId = $row['past_company_id'];

                if ($peopleId && $pastCompanyId) {
                    $key = $peopleId . '|' . $role . '|' . $pastCompanyId;

                    if (isset($existingRecords[$key])) {
                        $duplicates[] = $pastCompanyId;
                    } else {
                        if (!$tenure && $startDate) {
                            $startDateCarbon = Carbon::parse($startDate);
                            $endDateCarbon = $endDate == 'Present' || !$endDate ? Carbon::now() : Carbon::parse($endDate);

                            // Calculate the difference in years with decimal precision
                            $totalDays = $startDateCarbon->diffInDays($endDateCarbon);
                            $tenure = number_format($totalDays / 365.25, 13); // Adjust precision as needed
                        }

                        $toInsert[] = [
                            'people_id' => $peopleId,
                            'role' => $role,
                            'past_company_id' => $pastCompanyId,
                            'start_date' => $startDate,
                            'end_date' => $endDate == 'Present' ? null : $endDate,
                            'tenure' => $tenure,
                            'created_at' => now()
                        ];
                    }
                } else {
                    $invalidRecordCount = $invalidRecordCount  + 1;
                }
            }

            // Insert all new records at once
            // if (count($toInsert) > 0) {
            //     CareerHistories::insert($toInsert);
            // }

            // Define chunk size
            $chunkSize = 500; // Adjust based on your needs and database configuration

            // Check if there is data to insert
            if (count($toInsert) > 0) {
                // Chunk the array into smaller batches
                foreach (array_chunk($toInsert, $chunkSize) as $chunk) {
                    // Perform the insert operation for each chunk
                    CareerHistories::insert($chunk);
                }
            }

            $this->chCSVFile = null;

            if ($invalidRecordCount > 0) {
                $this->dispatch('customToast', 'info', "There was " . $invalidRecordCount . " invalid records. (Either people_id or past_company_name are missing)");
            }

            if (count($duplicates) > 0) {
                $this->dispatch('customToast', 'info', "There was " . count($duplicates) . " duplicate records");
            }

            $this->dispatch('customToast', 'success', "File uploaded successfully");
            return;
        } catch (Exception $exception) {
            Log::error($exception);
            session()->flash('error', $exception->getMessage());
            return;
        }
    }

    private function processCompanyCSV($companyCsvFile)
    {
        try {

            $excelData = Excel::toArray(new PeopleImport, $companyCsvFile->getRealPath());

            $duplicates = [];
            $excelData = $excelData[0];

            if (!$excelData || !is_array($excelData) || count($excelData) <= 0) {
                $this->dispatch('customToast', 'error', "Selected file don't have any record to upload.");
                return;
            }

            if (!isset($excelData[0]['name'])) {
                $this->dispatch('customToast', 'error', "Invalid file format.");
                return;
            }

            $companiesDataArr = [];
            $combinations = [];
            $invalidRecordCount = 0;

            foreach ($excelData as $key => $row) {

                $companyName = $row['name'];

                if (!$companyName) {
                    continue;
                }

                $combinations[] = [
                    'company_name' => $companyName
                ];
            }

            // Remove duplicates in combinations (optional)
            $combinations = array_unique($combinations, SORT_REGULAR);

            $existingRecords = Company::whereIn('name', array_column($combinations, 'company_name'))
                ->get()
                ->groupBy(function ($item) {
                    return $item->name;
                });


            $toInsert = [];

            foreach ($excelData as $key => $row) {
                // dd($row);
                $companyName = $row['name'];
                $otherName = $row['other_name'];
                $nameAbbreviation = $row['name_abbreviation'];
                $parentCompany = $row['parent_company'];
                $description = $row['description'];
                $corporateHqCountry = $row['corporate_hq_country'];
                $corporateHqAddress = $row['corporate_hq_address'];
                $website = $row['website'];
                $corporateHqPhoneNumber = $row['corporate_hq_phone_number'];
                $chair = $row['chair'];
                $ceo = $row['ceo'];
                $type = $row['type'];
                $industry = $row['industry'];
                $sector = $row['sector'];
                $stockSymbol = $row['stock_symbol'];
                $annualRevenue = $row['annual_revenue'];
                $annualNetProfitMargin = $row['annual_net_profit_margin'];
                $annualNetExpenses = $row['annual_net_expenses'];
                $annualExpenses = $row['annual_expenses'];
                $annualYOYRevenueChange = $row['annual_yoy_revenue_change'];
                $companyEmployeeCount = $row['company_employee_count'];
                $currency = $row['currency'];
                $image = $row['image'];
                $status = $row['status'];
                $earningsBeforeInterestTaxes = $row['earnings_before_interest_taxes'];

                if ($companyName) {

                    if (isset($existingRecords[$companyName])) {
                        $duplicates[] = $companyName;
                    } else {
                        $toInsert[] = [
                            'name'                   => $companyName ?? null,
                            'other_name'             => $otherName ?? null,
                            'name_abbreviation'      => $nameAbbreviation ?? null,
                            'parent_name'            => $parentCompany ?? null,
                            'description'            => $description ?? null,
                            'corporate_hq_country'   => $corporateHqCountry ?? null,
                            'corporate_hq_address'   => $corporateHqAddress ?? null,
                            'website'                => $website ?? null,
                            'corporate_hq_phone_number' => $corporateHqPhoneNumber ?? null,
                            'chair'                  => $chair ?? null,
                            'ceo'                    => $ceo ?? null,
                            'type'                   => $type ?? null,
                            'industry'               => $industry ?? null,
                            'sector'                 => $sector ?? null,
                            'stock_symbol'           => $stockSymbol ?? null,
                            'Annual_Revenue'         => (int) $annualRevenue ?? 0,
                            'Annual_Net_Profit_Margin'  => (int) $annualNetProfitMargin ?? 0,
                            'Annual_Net_Expenses'       => (int) $annualNetExpenses ?? 0,
                            'annual_expenses'           =>  $annualExpenses,
                            'Annual_YOY_Revenue_Change' => (int) $annualYOYRevenueChange ?? 0,
                            'company_employee_count'    => (int)$companyEmployeeCount ?? null,
                            'image'                  => $image ?? null,
                            'status'                 => $status ?? null,
                            'parent_company'         => $parentCompany ?? null,
                            'Currency'               => $currency ?? null,
                            'Earnings_Before_Interest_Taxes' => $earningsBeforeInterestTaxes ?? null,
                            'created_at'             => now()
                        ];
                    }
                } else {
                    $invalidRecordCount = $invalidRecordCount  + 1;
                }
            }

            // Insert all new records at once
            if (count($toInsert) > 0) {
                Company::insert($toInsert);
            }

            $this->companyCsvFile = null;

            if ($invalidRecordCount > 0) {
                $this->dispatch('customToast', 'info', "There was " . $invalidRecordCount . " invalid records. (Company name are missing)");
            }

            if (count($duplicates) > 0) {
                $this->dispatch('customToast', 'info', "There was " . count($duplicates) . " duplicate records");
            }

            $this->dispatch('customToast', 'success', "File uploaded successfully");
            return;
        } catch (Exception $exception) {
            Log::error($exception);
            session()->flash('error', $exception->getMessage());
            return;
        }
    }

    public function submitCompany($id)
    {
        if ($id === null) {
            try {
                $company = Company::create([

                    'name'    => $this->name,
                    'other_name'     => $this->other_name,
                    'name_abbreviation' => $this->name_abbr,
                    'stock_symbol' => $this->stock_symbol,
                    'parent_name'  => $this->parent_name,
                    'description'      => $this->description,
                    'corporate_hq_country'     => $this->co_company_hq,
                    'corporate_hq_address'     => $this->co_company_add,
                    'corporate_hq_phone_number' => $this->co_company_no,
                    'website' => $this->website,
                    'Annual_Revenue'  => (int)$this->annual_revenue,
                    'Annual_Net_Profit_Margin' => (int)$this->annual_profit,
                    'Annual_Net_Expenses'    => (int)$this->annual_expense,
                    'Annual_YOY_Revenue_Change'    => (int)$this->annual_yoy_change,
                    'industry'      => $this->industry,
                    'sector'      => $this->sector,
                    'status' => 'Approved'
                ]);
                $this->closeModal('company');

                session()->flash('success', 'Company added successfully!');
                // $this->closeModal('company');
            } catch (Exception $e) {
                session()->flash('error', $e->getMessage());
            }
        }
    }

    public function submitPerson($experience)
    {
        try {
            DB::transaction(function () {

                $career_history = [];
                if (!empty($experience)) {
                    $this->previousExperience = json_decode($experience, true);
                    foreach ($this->previousExperience as $experience) {
                        $career_history[] = $experience['company'] . '-' . $experience['role'] . '-' . $experience['start_date'] . '-' . $experience['end_date'];
                    }
                }

                $career_history_str = implode(', ', $career_history);
                $companyObj = Company::find($this->company);
              
                $startDate = Carbon::parse($this->start_date);
                $currentDate = !empty($this->end_date) ? $this->end_date :Carbon::now();
                $tenure = $currentDate->diffInYears($startDate);
                if ($this->gender == 1) {
                    $gender = 'Male';
                } elseif ($this->gender == 2) {
                    $gender = 'Female';
                } else {
                    $gender = 'Not Applicable';
                }

                $people = People::create([
                    'forename'              =>      $this->forename,
                    'surname'               =>      $this->surname,
                    'middle_name'           =>      $this->middlename,
                    'other_name'            =>      $this->othername,
                    'gender'                =>      $gender,
                    'country'               =>      $this->country,
                    'city'                  =>      $this->city,
                    'linkedinURL'           =>      $this->linkedinURL,
                    'latest_role'           =>      $this->latest_role,
                    'exco'                  =>      $this->exco,
                    'company_name'          =>      $companyObj->name,
                    'company_id'            =>      $companyObj->id,
                    'start_date'            =>      $this->start_date,
                    'end_date'              =>      $this->end_date,
                    'tenure'                =>      $tenure,
                    'function'              =>      $this->function,
                    'division'              =>      $this->division,
                    'seniority'             =>      $this->seniority,
                    'career_history'        =>      $career_history_str,
                    'educational_history'   =>      $this->educational_history,
                    'skills'                =>      $this->skills,
                    'languages'             =>      $this->languages,
                    'other_tags'            =>      $this->registration_status,
                    'user_id'               =>      $this->user->id,
                    'summary'               =>      $this->peopleSummary,
                    'status'                =>      'Submitted',
                    'readiness'             =>      'Not Ready'
                ]);
                if (!empty($this->previousExperience)) {
                    foreach ($this->previousExperience as $pExp) {
                        $startDate = Carbon::parse($pExp['start_date']);
                        $currentDate = Carbon::now();
                        $tenure = $currentDate->diffInYears($pExp['end_date']);
                        CareerHistories::updateOrCreate(
                            [
                                'people_id'        => $people->id,
                                'role'             => $pExp['role'],
                                'past_company_id'  => $pExp['company'],
                                'start_date'       => $pExp['start_date'],
                                'end_date'         => $pExp['end_date'],
                                'tenure'           => $tenure,
                            ]

                        );
                    }
                }

                if ($this->skills !== null) {
                    $skills = explode(',', $this->skills);
                    foreach ($skills as $skill) {
                        $skill = trim($skill);
                        Skills::create([
                            'skill_name' => $skill,
                            'people_id' => $people->id,
                            'name' => $skill,
                            'skill_type' => "Professional",
                            'skill_age' => 1,
                        ]);
                    }
                }
                $this->addInd = false;
                $this->closeModal('user');
                $this->dispatch('toast', 'info', 'Individual added successfully!');
            });
        } catch (Exception $e) {
            \Log::info($e);
            $this->dispatch('toast', 'error', $e->getMessage());
            $this->skipRender();
        }
        
    }
    public function validateStepOne($for)
    {
        if ($for === "person") {
            $this->validate([
                'forename' => 'required|string',
                'surname' =>  'required|string',
                'middlename' => 'required|string',
                'othername' => 'required|string',
                'country' => 'required|string',
                'city'    => 'required|string'
            ]);
            $this->step++;
        } else {
            $this->validate([
                'name' => 'required|string',
                'other_name' => 'required|string',
                'stock_symbol' => 'required|string',
                'parent_name' => 'required|string',
                'name_abbr' => 'required|string',
                "description" => 'required|string'
            ]);
            $this->step++;
        }
        $this->skipRender();
    }

    public function validateStepTwo($for)
    {
        if ($for === "company") {
            $this->validate([
                'co_company_hq' => 'required|string',
                'co_company_add' =>  'required|string',
                'co_company_no' => 'required|string',
                'website' => 'required|string',
            ]);
        } else {
            $this->validate([
                'latest_role' => 'required|string',
                'company' =>  'required|string',
                'start_date' => 'required|string',
                'end_date' => 'required|string',
                'seniority' => 'required|string',
                'exco' => 'required|string',
            ]);
        }
        $this->step++;
    }

    public function validateStepThree($for)
    {
        if ($for === "person") {
            $this->validate([
                'linkedinURL' => 'required|string',
                'skills' =>  'required|string',
                'registration_status' => 'required|string',
                'languages' => 'required|string',
            ]);
        } else {
            $this->validate([
                'co_company_hq' => 'required|string',
                'co_company_add' =>  'required|string',
                'co_company_no' => 'required|string',
                'website' => 'required|string',
            ]);
        }
        $this->step++;
    }

    public function previousStep()
    {
        // Move to the previous step
        $this->step--;
    }

    public function closeModal($for)
    {
        if ($for === "company") {
            $this->step = 1;
            $this->reset(['name', 'other_name', 'description', 'parent_name', 'name_abbr', 'stock_symbol', 'co_company_hq', 'co_company_add', 'co_company_no', 'website', 'annual_revenue', 'annual_profit', 'annual_expense', 'annual_yoy_change', 'industry', 'sector']);
        } else {
            $this->step = 1;
            $this->reset(['forename', 'surname', 'middlename', 'othername', 'country', 'company', 'latest_role', 'city', 'seniority', 'exco', 'linkedinURL', 'skills', 'registration_status', 'languages', 'summary', 'start_date', 'end_date', 'peopleSummary']);
        }
    }

    public function convertExcelDateToDate($excelDateValue)
    {
        if (!is_numeric($excelDateValue) || $excelDateValue < 1) {
            return $excelDateValue;
        }

        // Excel epoch starts on January 1, 1900
        $excelEpoch = new \DateTime('1899-12-30'); // Start from Excel's day 0

        // Add the Excel date value as days to the epoch
        $date = $excelEpoch->add(new \DateInterval("P{$excelDateValue}D"));

        return $date->format('Y-m-d');
    }

    public function deleteReportedPerson($personId)
    {

        $hasPeopleDeleted = People::where('id', $personId)->delete();
        if (!$hasPeopleDeleted) {
            $this->dispatch('toast', "error", "Unable to delete!");
            return;
        }

        SuccessPeople::where(['people_id' => $personId])->delete();
        JobPeople::where(['people_id' => $personId])->delete();
        pipeline::where(['people_id' => $personId])->delete();
        CareerHistories::where(['people_id' => $personId])->delete();
        Skills::where(['people_id' => $personId])->delete();

        $this->dispatch('toast', "success", "People deleted successfully!");
        return;
    }

    public function deleteSubmittedPerson($personId)
    {
        $this->personIdToDelete = $personId;
        $this->personIdReplacedWith = "";
        $this->deleteSubmittedPeoplePopup = true;
    }

    public function deleteSubmittedPersonConfirm()
    {
        if ($this->personIdReplacedWith) {
            return false;
        }

        $hasPeopleDeleted = People::where('id', $this->personIdToDelete)->delete();
        if (!$hasPeopleDeleted) {
            $this->dispatch('toast', "error", "Unable to delete!");
            return;
        }

        SuccessPeople::where(['people_id' => $this->personIdToDelete])->delete();
        JobPeople::where(['people_id' => $this->personIdToDelete])->delete();
        pipeline::where(['people_id' => $this->personIdToDelete])->delete();
        CareerHistories::where(['people_id' => $this->personIdToDelete])->delete();
        Skills::where(['people_id' => $this->personIdToDelete])->delete();

        $this->dispatch('toast', "success", "People deleted successfully!");

        $this->personIdToDelete = "";
        $this->personIdReplacedWith = "";
        $this->deleteSubmittedPeoplePopup = false;

        return;
    }

    public function replaceSubmittedPerson()
    {
        if (!$this->personIdToDelete || $this->personIdToDelete == '') {
            $this->dispatch('toast', "error", "Unable to replace");
            return;
        }

        if (!$this->personIdReplacedWith || $this->personIdReplacedWith == '') {
            $this->dispatch('toast', "error", "Please enter relevant id to replace individuals");
            return;
        }

        $peopleObj = People::find($this->personIdReplacedWith);
        if (!$peopleObj) {
            $this->dispatch('toast', "error", "Invalid relevant id!");
            return;
        }

        $deletePersonPipeline = pipeline::where('people_id', $this->personIdToDelete)->first();
        // Need to check existance. If exis tthen no need tpo update otherwise update it (All the info should be update)

        $newPersonPipelineQuery = pipeline::where('people_id', $peopleObj->id);
        if ($deletePersonPipeline && $deletePersonPipeline->plan_id) {
            $newPersonPipelineQuery = $newPersonPipelineQuery->where('plan_id', $deletePersonPipeline->plan_id);
        }
        $newPersonPipelineObj = $newPersonPipelineQuery->first();

        if (!$newPersonPipelineObj) {
            pipeline::where(['people_id' => $this->personIdToDelete])->update([
                'people_id'         => $peopleObj->id,
                // 'id'                 => $this->pipelineid,
                // 'job_id'            => $this->job,
                // 'user_id'            => $user->id,
                // 'headline'           => $generatedHeadline,
                'first_name'         => $peopleObj->forename,
                'last_name'          => $peopleObj->surname,
                'middle_name'        => $peopleObj->middle_name,
                'other_name'         => $peopleObj->other_name,
                'gender'             => $peopleObj->gender,
                'diverse'            => $peopleObj->diverse,
                'country'            => $peopleObj->country,
                'city'               => $peopleObj->city,
                // 'location'           => $this->location,
                'linkedinURL'        => $peopleObj->linkedinURL,
                'latest_role'        => $peopleObj->latest_role,
                'company_id'         => $peopleObj->company_id,
                'company_name'       => $peopleObj->company_name,
                'start_date'         => $peopleObj->start_date,
                'end_date'           => $peopleObj->end_date,
                'tenure'             => $peopleObj->tenure,
                'function'           => $peopleObj->function,
                'division'           => $peopleObj->division,
                'seniority'          => $peopleObj->seniority,
                'exco'               => $peopleObj->exco,
                'career_history'     => $peopleObj->career_history,
                'educational_history' => $peopleObj->educational_history,
                'skills'             => $peopleObj->skills,
                'languages'          => $peopleObj->languages,
                'summary'            => $peopleObj->summary,
                'readiness'          => $peopleObj->readiness,
                // 'skills_match'       => 0,
                // 'education_match'    => 0,
                // 'location_match'     => 1,
                // 'role_match'         => 1,
                // 'gender_match'       => 1,
                // 'tenure_match'       => 1,
                // 'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                'people_type'        => "External-System"
            ]);
        } else {
            pipeline::where(['people_id' => $this->personIdToDelete])->delete();
        }

        // Need to check existance. If exis then no need tpo update otherwise update it (All the info should be update)
        $successPeopleObj = SuccessPeople::where('people_id', $peopleObj->id)->first();
        if (!$successPeopleObj) {
            SuccessPeople::where(['people_id' => $this->personIdToDelete])->update([
                'people_id'          => $peopleObj->id,
                // 'pipeline_id'        => $this->pipelineid,
                // 'plan_id'            => $this->plan,
                // 'user_id'            => $user->id,
                // 'headline'           => $generatedHeadline,
                'first_name'         => $peopleObj->forename,
                'last_name'          => $peopleObj->surname,
                'middle_name'        => $peopleObj->middle_name,
                'other_name'         => $peopleObj->other_name,
                'gender'             => $peopleObj->gender,
                'diverse'            => $peopleObj->diverse,
                'country'            => $peopleObj->country,
                'city'               => $peopleObj->city,
                // 'location'           => $this->location,
                'linkedinURL'        => $peopleObj->linkedinURL,
                'latest_role'        => $peopleObj->latest_role,
                'company_id'         => $peopleObj->company_id,
                'company_name'       => $peopleObj->company_name,
                'start_date'         => $peopleObj->start_date,
                'end_date'           => $peopleObj->end_date,
                'tenure'             => $peopleObj->tenure,
                'function'           => $peopleObj->function,
                'division'           => $peopleObj->division,
                'seniority'          => $peopleObj->seniority,
                'exco'               => $peopleObj->exco,
                'career_history'     => $peopleObj->career_history,
                'educational_history' => $peopleObj->educational_history,
                'skills'             => $peopleObj->skills,
                'languages'          => $peopleObj->languages,
                'summary'            => $peopleObj->summary,
                'readiness'          => $peopleObj->readiness
                // 'skills_match'       => 0,
                // 'education_match'    => 0,
                // 'location_match'     => 1,
                // 'role_match'         => 1,
                // 'gender_match'       => 1,
                // 'tenure_match'       => 1,
                // 'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                // 'type'               => "Internal",
                // 'notes'              => "Enter notes here",
                // 'recruit'            => 1,
                // 'status'             => $peopleObj->status
            ]);
        } else {
            SuccessPeople::where(['people_id' => $this->personIdToDelete])->delete();
        }


        // Need to check existance. If exis tthen no need tpo update otherwise update it (All the info should be update)
        $jobPeopleObj = JobPeople::where('people_id', $peopleObj->id)->first();
        if (!$jobPeopleObj) {
            JobPeople::where(['people_id' => $this->personIdToDelete])->update([
                'people_id'          => $peopleObj->id,
                // 'pipeline_id'        => $this->pipelineid,
                // 'plan_id'            => $this->job,
                // 'user_id'            => $user->id,
                // 'headline'           => $generatedHeadline,
                'first_name'         => $peopleObj->forename,
                'last_name'          => $peopleObj->surname,
                'middle_name'        => $peopleObj->middle_name,
                'other_name'         => $peopleObj->other_name,
                'gender'             => $peopleObj->gender,
                'diverse'            => $peopleObj->diverse,
                'country'            => $peopleObj->country,
                'city'               => $peopleObj->city,
                // 'location'           => $this->location,
                'linkedinURL'        => $peopleObj->linkedinURL,
                'latest_role'        => $peopleObj->latest_role,
                'company_id'         => $peopleObj->company_id,
                'company_name'       => $peopleObj->company_name,
                'start_date'         => $peopleObj->start_date,
                'end_date'           => $peopleObj->end_date,
                'tenure'             => $peopleObj->tenure,
                'function'           => $peopleObj->function,
                'division'           => $peopleObj->division,
                'seniority'          => $peopleObj->seniority,
                'career_history'     => $peopleObj->career_history,
                'educational_history' => $peopleObj->educational_history,
                'skills'             => $peopleObj->skills,
                'languages'          => $peopleObj->languages,
                'summary'            => $peopleObj->summary,
                'readiness'          => $peopleObj->readiness
                // 'skills_match'       => 0,
                // 'education_match'    => 0,
                // 'location_match'     => 1,
                // 'role_match'         => 1,
                // 'gender_match'       => 1,
                // 'tenure_match'       => 1,
                // 'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                // 'type'               => "Internal",
                // 'notes'              => "Enter notes here",
                // 'recruit'            => 1,
                // 'status'             => $peopleObj->status
            ]);
        } else {
            JobPeople::where(['people_id' => $this->personIdToDelete])->delete();
        }

        // Delete the records from people table, career histories and skills table
        // Should I delete that peson data from people, CareerHistories and Skills
        People::where('id', $this->personIdToDelete)->delete();
        CareerHistories::where(['people_id' => $this->personIdToDelete])->delete();
        Skills::where(['people_id' => $this->personIdToDelete])->delete();

        $this->dispatch('toast', "success", "Relevant id replaced successfully!");

        $this->personIdToDelete = "";
        $this->personIdReplacedWith = "";
        $this->deleteSubmittedPeoplePopup = false;
        return;
    }

}
