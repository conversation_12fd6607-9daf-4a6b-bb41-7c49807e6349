<?php

namespace App\Livewire;

use App\Models\SuccessSkills;
use Livewire\Component;
use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use Livewire\WithPagination;
use App\Models\Job;
use App\Models\SuccessRequirements;
use App\Models\CareerHistories;
use App\Models\PlanScores;
use App\Models\SuccessPeople;
use App\Models\pipeline;
use App\Models\internal_career_histories;
use App\Models\People;
use Carbon\Carbon;
use App\Models\Account;
use Illuminate\Support\Facades\DB;
use TCPDF;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Dompdf\Dompdf;
use Dompdf\Options;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;

class SuccessPeotable extends Component
{
    public $plan;
    public $selectedPeople = [];
    public $selectedJobs = [];
    public $jobs;
    public $savedPeople = [];
    public $skills;
    public $SuccessSkills;

    // Chart labels and data
    public $scoreLables = [];
    public $scoreData = [];
    public $genderLabels = [];
    public $genderData = [];
    public $LLgenderLabels = [];
    public $LLgenderData = [];
    public $typeLabels = [];
    public $typeData =[];
    public $LLtypeLabels = [];
    public $LLtypeData =[];
    public $companyLabels = [];
    public $companyData = [];
    public $LLcompanyLabels = [];
    public $LLcompanyData = [];
    public $roleLabels =[];
    public $roleData =[];
    public $locationLabels = [];
    public $locationData = [];
    public $LLlocationLabels = [];
    public $LLlocationData = [];
    public $pipelinePeople = [];
    public $successPeople = [];
    public $LLFromCompanyLabels = [];
    public $LLFromCompanyData = [];

    // Update Scores variables
    public $updatelocScore;
    public $updategenScore;
    public $updaterolScore;
    public $updateskillScore;
    public $updatetenureScore;

    public function mount(){

        $user = auth()->user();
        $this->jobs = Job::where('user_id',$user->id)->get();

        $this->jobs = Job::where('user_id',$user->id)
        //->whereNotIn('id',$user->id)
        ->distinct()
        ->pluck('name')
        ->map(function ($job) {
          return ['value' => $job, 'label' => $job];
        })
        ->toArray();
        
        $this->SuccessSkills = SuccessSkills::where("succession_plan_id", $this->plan->id)
                                            ->get();
        //dd($this->SuccessSkills);

        //$SuccessRequirements = SuccessRequirements::where("plan_id", $this->plan->id);
        //dd($this->SuccessSkills);
    }
    
    use WithPagination;
    public function render()
    { 
        //dd('check');
        $plandetails = SuccessionPlan::where('id',$this->plan->id)->get();
        $planrequirements = SuccessRequirements::where('plan_id',$this->plan->id)->get();

        $groupedPlanrequirements  = $planrequirements->groupBy('type')->map(function ($items) {
            return [
                'type' => $items->pluck('type')->toArray(),
                'name' => $items->pluck('name')->toArray(),
            ];
        });

        //dd($groupedPlanrequirements);

        $finalSLPeople =  SuccessPeople::where('plan_id', $this->plan->id)
                                      ->where('type','!=','Proposed');
        
        /*---------------------------------------------------------- Charts ----------------------------------------------------------*/
        // Gender Chart
        $SLpeopleCountsData = DB::table('success_people')
        ->select('gender', DB::raw('COUNT(*) as total_people'))
        ->where('plan_id', $this->plan->id)
        ->groupBy('gender')
        ->get();
        //dd($SLpeopleCountsData);

        foreach ($SLpeopleCountsData as $data) {

            $colorCode = "#8B5CF6";
            if($data->gender == "Male"){
                $colorCode = "#3B82F6";
            }
            else if($data->gender == "Female"){
                $colorCode = "#FFA347";
            }

            $this->genderLabels[] = $data->gender;
            $this->genderData[] = [
                "x" => $data->gender,
                "y" => $data->total_people,
                "fillColor" => $colorCode
            ];
        }
        
        //Type Chart
        $SLpeopleCountsData = DB::table('success_people')
        ->select('type', DB::raw('COUNT(*) as total_people'))
        ->where('plan_id', $this->plan->id)
        ->groupBy('type')
        ->get();
        //dd($SLpeopleCountsData);

        foreach ($SLpeopleCountsData as $data) {
            $this->typeLabels[] = $data->type;
            $this->typeData[] = $data->total_people;
        }

        // Location Chart
        $SLpeopleCountsData = DB::table('success_people')
                                ->select('country', DB::raw('COUNT(*) as total_people'))
                                ->where('plan_id', $this->plan->id)
                                ->groupBy('country')
                                ->orderBy('total_people', 'desc')  // Order by total_people in descending order
                                ->limit(10)  // Limit the results to 10
                                ->get();
        //dd($SLpeopleCountsData);

        foreach ($SLpeopleCountsData as $data) {
            $this->locationLabels[] = $data->country;
            $this->locationData[] = $data->total_people;
        }
        

        // Company Chart
        $SLpeopleCountsData = DB::table('success_people')
        ->select('company_name', DB::raw('COUNT(*) as total_people'))
        ->where('plan_id', $this->plan->id)
        ->groupBy('company_name')
        ->get();
        // dd($SLpeopleCountsData);

        foreach ($SLpeopleCountsData as $data) {
            $this->companyLabels[] = $data->company_name;
            $this->companyData[] = $data->total_people;
        }

        // Readiness
        
        $finalIds = $finalSLPeople->pluck('id')->toArray();
        //dd($finalIds);
        $finalSLPeople = $finalSLPeople->get();
        
        $scoreskills = $this->SuccessSkills->whereIn('success_people_id',$finalIds);
        //dd($scoreskills);

        $groupedSkillScores = $scoreskills->groupBy('success_people_id');
        //dd($groupedSkillScores);

        //dd($groupedSkillScores);
        $groupedSkillScores->transform(function ($item) {
            return [
                
                'skill_name' => $item->pluck('skill_name')->toArray(),
                'scores' => $item->pluck('score')->toArray(),
            ];
        });

        //dd($groupedSkillScores);

        // Loop through the plans and attach corresponding data from groupedPlanScores
        $finalSLPeople->each(function ($person) use ($groupedSkillScores) {
            $PersonId = $person->id; // Replace with the actual column name
            if (isset($groupedSkillScores[$PersonId])) {
                $person->scoreLabels = $groupedSkillScores[$PersonId]['skill_name'];
                $person->scoreData = $groupedSkillScores[$PersonId]['scores'];
            } else {
                $person->scoreLabels = [];
                $person->scoreData = [];
            }
        });

        $finalLLPeople =  pipeline::where('plan_id', $this->plan->id);

        /*---------------------------------------------------------- Charts ----------------------------------------------------------*/
        // Gender Chart
        $LLpeopleCountsData = DB::table('pipelines')
                                ->select('gender', DB::raw('COUNT(*) as total_people'))
                                ->where('plan_id', $this->plan->id)
                                ->groupBy('gender')
                                ->get();
        //dd($LLpeopleCountsData);

        foreach ($LLpeopleCountsData as $data) {
            $colorCode = "#8B5CF6";
            if($data->gender == "Male"){
                $colorCode = "#3B82F6";
            }
            else if($data->gender == "Female"){
                $colorCode = "#FFA347";
            }

            $this->LLgenderLabels[] = $data->gender;
            $this->LLgenderData[] = [
                "x" => $data->gender,
                "y" => $data->total_people,
                "fillColor" => $colorCode
            ];
        }

        // Location Chart
        $LLpeopleCountsData = DB::table('pipelines')
                                ->select('country', DB::raw('COUNT(*) as total_people'))
                                ->where('plan_id', $this->plan->id)
                                ->groupBy('country')
                                ->orderBy('total_people', 'desc')  // Order by total_people in descending order
                                ->limit(10)  // Limit the results to 10
                                ->get();
        // dd($LLpeopleCountsData);

        foreach ($LLpeopleCountsData as $data) {
            $this->LLlocationLabels[] = $data->country ? $data->country : "Not Available";
            $this->LLlocationData[] = $data->total_people;
        }
        
        // Company Chart
        $LLpeopleCountsData = DB::table('pipelines')
                                ->select('company_name', DB::raw('COUNT(*) as total_people'))
                                ->where('plan_id', $this->plan->id)
                                ->groupBy('company_name')
                                ->get();
        // dd($LLpeopleCountsData);

        foreach ($LLpeopleCountsData as $data) {
            $this->LLcompanyLabels[] = $data->company_name;
            $this->LLcompanyData[] = $data->total_people;
        }

        //Getting the career histories
        $filterExternal = $finalSLPeople->pluck('type', 'people_id');

        // Filter and get internal IDs
        $internalIds = $filterExternal->filter(function ($type) {
            return strtolower($type) === 'internal';
        })->keys()->toArray();
        
        // Filter and get external IDs
        $externalIds = $filterExternal->filter(function ($type) {
            return strtolower($type) !== 'internal';
        })->keys()->toArray();
        
        $peoplescareer = collect(); // Initialize an empty collection

       if ($internalIds) {
            $internalCareer = internal_career_histories::whereIn('people_id', $internalIds)
                ->join('companies', 'internal_career_histories.past_company_id', '=', 'companies.id')
                ->select('internal_career_histories.*', 'companies.name as company_name')
                ->get();
            
            $peoplescareer = $peoplescareer->merge($internalCareer); // Merge internal career histories
        }

        if ($externalIds) {
            // dd($externalIds);
            $externalCareer = CareerHistories::whereIn('people_id', $externalIds)
                ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
                ->select('career_histories.*', 'companies.name as company_name')
                ->get();

            $peoplescareer = $peoplescareer->merge($externalCareer); // Merge external career histories
        }
      
      

        // $groupedPeoplesCareer = $peoplescareer->groupBy('people_id')->map(function ($items) {
        //     return $items->groupBy('company_name')->map(function ($companyItems) {
        //         return [
        //             'roles' => $companyItems->pluck('role')->toArray(),
        //             'start_dates' => $companyItems->map(function ($item) {
        //                 return Carbon::parse($item['start_date'])->format('M Y');
        //             })->toArray(),
        //             'end_dates' => $companyItems->map(function ($item) {
        //                 return Carbon::parse($item['end_date'])->format('M Y');
        //             })->toArray(),
        //         ];
        //     });
        // });

        //dd($groupedPeoplesCareer);
        
        if($peoplescareer && $peoplescareer->isNotEmpty()){
            $finalSLPeople->each(function ($person) use ($peoplescareer) {
                $personId = $person->people_id; // Replace with the actual column name
            
                $filteredCareerHistories = $peoplescareer->filter(function ($career) use ($personId) {
                    return $career->people_id == $personId;
                });

                // Convert the array of models to a Laravel Collection
                $collection = collect($filteredCareerHistories);

                // Sort the collection by `start_date`
                $sortedCollection = $collection->sortByDesc('start_date');

                // Get the top 10 records
                $top10CareerHistories = $sortedCollection->take(10);

                $person->career_histories = $top10CareerHistories;
            }); 
        }

        $this->pipelinePeople = DB::table('pipelines')
        ->select(DB::raw('Count(*) as pipelinecount'))
        // ->groupby('plan_id')
        ->where('plan_id', $this->plan->id)
        ->get();

        // dd($this->pipelinePeople);

        $this->successPeople = DB::table('success_people')
        ->select(DB::raw('Count(*) as successcount'))
        // ->groupby('plan_id')
        ->where('plan_id', $this->plan->id)
        ->get();

        // dd($finalLLPeople); 

        // Loop through the filtered people and attach corresponding data
        $finalSLPeople->each(function ($person) {
            
            // Initialize arrays to store match labels and scores
            $matchLabels = [];
            $matchData = [];
            
            $matchLabels[] = 'Skills';  // Add label for skills
            $matchData[] = $person->skills_match; 

            $matchLabels[] = 'Roles';
            $matchData[] = $person->role_match;

            $matchLabels[] = 'Location';
            $matchData[] = $person->location_match;

            $matchLabels[] = 'Gender';
            $matchData[] = $person->gender_match;

            $matchLabels[] = 'Tenure';
            $matchData[] = $person->tenure_match;
            
            // Assign the match_labels and match_data fields to the person
            $person->match_labels = $matchLabels;
            $person->match_data = $matchData;
                
        });

        if($finalSLPeople->isNotEmpty()){
            $peopleIds = array_map(function($person) {
                return $person['id'];
            }, $finalSLPeople->toArray());

            // From Companies Chart
            $LLFromComaniesData = DB::table('success_people')
                                    ->select('company_name', DB::raw('COUNT(*) as total_people'))
                                    // ->whereIn('people_id', $peopleIds)
                                    ->where('plan_id', $this->plan->id)
                                    ->groupBy('company_name')
                                    ->get();

            foreach ($LLFromComaniesData as $data) {
                $this->LLFromCompanyLabels[] = $data->company_name;
                $this->LLFromCompanyData[] = $data->total_people;
            }
        }
        $user = auth()->user();
        $account = $user ? Account::where('id', $user->account_id)->first() : null;
            //dd($finalSLPeople);
        return view('livewire.success-peotable' ,['plandetails'=>$plandetails,'groupedPlanrequirements'=>$groupedPlanrequirements,'finalPeople'=>$finalSLPeople,'account'=>$account]);
    }

    public function saveSelectedPeople()
    {
        $this->savedPeople = $this->selectedPeople;
    }

    public function updateScore($id){


        $updateSuccessor = SuccessPeople::where('id',$id);
        $redSuc = SuccessPeople::where('id',$id)->first();

        $this->updategenScore = $this->updategenScore ?? $redSuc->gender_match;
        $this->updaterolScore = $this->updaterolScore ?? $redSuc->role_match;
        $this->updatetenureScore = $this->updatetenureScore ?? $redSuc->tenure_match;
        $this->updatelocScore = $this->updatelocScore ?? $redSuc->location_match;
        $this->updateskillScore = $this->updateskillScore ?? $redSuc->skills_match;
        
        $updateSuccessor->update([
            'gender_match' => $this->updategenScore,
            'role_match'   => $this->updaterolScore,
            'tenure_match' => $this->updatetenureScore,
            'location_match'   => $this->updatelocScore,
            'skills_match' => $this->updateskillScore,
            'total_score'=> $this->updategenScore + $this->updaterolScore + $this->updatetenureScore + $this->updatelocScore  + $this->updateskillScore
        ]);

        //
        $averageskillscore = DB::table('success_people')
        ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
        ->groupBy('plan_id')
        ->where('plan_id', $this->plan->id)
        ->first();

        //dd($averageskillscore);

        PlanScores::updateOrInsert(
            ['succession_plan_id'=>$this->plan->id, 'metric_name' => 'Skill Score'],
            ['score'=> $averageskillscore->average_skill_score]
        );

        $averagetenurescore = DB::table('success_people')
        ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
        ->groupBy('plan_id')
        ->where('plan_id', $this->plan->id)
        ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id'=>$this->plan->id, 'metric_name' => 'Tenure Score'],
            ['score'=> $averagetenurescore->average_tenure_score]
        );

        
    }

    public function clearSavedPeople()
    {
        // Clear the savedPeople array
        $this->savedPeople = [];
        $this->selectedPeople =[];
    }

    public function addtoJob($id){
        $user = auth()->user();
    }


    public function removeSuccessPerson($id)
    {
            $removalcandidate = SuccessPeople::findOrFail($id);
            

            // Update the scores
            //------------------- Get the new Gender Diversity Score -----------------//
            $femaleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "female")/Count(*))*100 as female_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id',$this->plan->id)
            ->first();

            //dd($femaleRatio);

            if ($femaleRatio === null) {
                PlanScores::updateOrInsert(
                    ['succession_plan_id'=>$this->plan->id, 'metric_name' => 'Female-ratio'],
                    ['score'=> 0]
                );
            }
            else {
                    PlanScores::updateOrInsert(
                        ['succession_plan_id'=>$this->plan->id, 'metric_name' => 'Female-ratio'],
                        ['score'=> $femaleRatio->female_ratio]
                    );
                };
            

            $maleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "male")/Count(*))*100 as male_ratio'))
            ->groupBy('plan_id')
            ->first();

            if ($maleRatio === null) {
                PlanScores::updateOrInsert(
                    ['succession_plan_id'=>$this->plan->id, 'metric_name' => 'Male-Ratio'],
                    ['score'=> 0]
                );}
            else {
                PlanScores::updateOrInsert(
                    ['succession_plan_id'=>$this->plan->id, 'metric_name' => 'Male-Ratio'],
                    ['score'=> $maleRatio->male_ratio]
                );
            }

            $InternalRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id',$this->plan->id)
            ->first();

            PlanScores::updateOrInsert(
                ['succession_plan_id'=>$this->plan->id, 'metric_name' => 'Internal-External Ratio'],
                ['score'=> $InternalRatio->internal_ratio]
            );

            $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

            PlanScores::updateOrInsert(
                ['succession_plan_id'=>$this->plan->id, 'metric_name' => 'Skill Score'],
                ['score'=> $averageskillscore->average_skill_score]
            );

            $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

            PlanScores::updateOrInsert(
                ['succession_plan_id'=>$this->plan->id, 'metric_name' => 'Tenure Score'],
                ['score'=> $averagetenurescore->average_tenure_score]
            );

            $removalcandidate->delete();
        
    }
    
    // public function generatePDF()
    // {
    //     //dd('check');

    //     $plandetails = SuccessionPlan::where('id',$this->plan->id)->get();
    //     $planrequirements = SuccessRequirements::where('plan_id',$this->plan->id)->get();

    //     $groupedPlanrequirements  = $planrequirements->groupBy('type')->map(function ($items) {
    //         return [
    //             'type' => $items->pluck('type')->toArray(),
    //             'name' => $items->pluck('name')->toArray(),
    //         ];
    //     });

    //     //dd($groupedPlanrequirements);

    //     $finalSLPeople =  SuccessPeople::where('plan_id', $this->plan->id)
    //                                   ->where('type','!=','Proposed');
        
    //     /*---------------------------------------------------------- Charts ----------------------------------------------------------*/
    //     // Gender Chart
    //     $SLpeopleCountsData = DB::table('success_people')
    //     ->select('gender', DB::raw('COUNT(*) as total_people'))
    //     ->where('plan_id', $this->plan->id)
    //     ->groupBy('gender')
    //     ->get();
    //     //dd($SLpeopleCountsData);

    //     foreach ($SLpeopleCountsData as $data) {
    //         $this->genderLabels[] = $data->gender;
    //         $this->genderData[] = $data->total_people;
    //     }
        
    //     //Type Chart
    //     $SLpeopleCountsData = DB::table('success_people')
    //     ->select('type', DB::raw('COUNT(*) as total_people'))
    //     ->where('plan_id', $this->plan->id)
    //     ->groupBy('type')
    //     ->get();
    //     //dd($SLpeopleCountsData);

    //     foreach ($SLpeopleCountsData as $data) {
    //         $this->typeLabels[] = $data->type;
    //         $this->typeData[] = $data->total_people;
    //     }

    //     // Location Chart
    //     $SLpeopleCountsData = DB::table('success_people')
    //     ->select('location', DB::raw('COUNT(*) as total_people'))
    //     ->where('plan_id', $this->plan->id)
    //     ->groupBy('location')
    //     ->get();
    //     //dd($SLpeopleCountsData);

    //     foreach ($SLpeopleCountsData as $data) {
    //         $this->locationLabels[] = $data->location;
    //         $this->locationData[] = $data->total_people;
    //     }

    //     // Company Chart
    //     $SLpeopleCountsData = DB::table('success_people')
    //     ->select('company_name', DB::raw('COUNT(*) as total_people'))
    //     ->where('plan_id', $this->plan->id)
    //     ->groupBy('company_name')
    //     ->get();
    //     // dd($SLpeopleCountsData);

    //     foreach ($SLpeopleCountsData as $data) {
    //         $this->companyLabels[] = $data->company_name;
    //         $this->companyData[] = $data->total_people;
    //     }

    //     // Readiness
        
    //     $finalIds = $finalSLPeople->pluck('id')->toArray();
    //     //dd($finalIds);
    //     $finalSLPeople = $finalSLPeople->get();
        
    //     $scoreskills = $this->SuccessSkills->whereIn('success_people_id',$finalIds);
    //     //dd($scoreskills);

    //     $groupedSkillScores = $scoreskills->groupBy('success_people_id');
    //     //dd($groupedSkillScores);

    //     //dd($groupedSkillScores);
    //     $groupedSkillScores->transform(function ($item) {
    //         return [
                
    //             'skill_name' => $item->pluck('skill_name')->toArray(),
    //             'scores' => $item->pluck('score')->toArray(),
    //         ];
    //     });

    //     //dd($groupedSkillScores);

    //             // Loop through the plans and attach corresponding data from groupedPlanScores
    //             $finalSLPeople->each(function ($person) use ($groupedSkillScores) {
    //                 $PersonId = $person->id; // Replace with the actual column name
    //                 if (isset($groupedSkillScores[$PersonId])) {
    //                     $person->scoreLabels = $groupedSkillScores[$PersonId]['skill_name'];
    //                     $person->scoreData = $groupedSkillScores[$PersonId]['scores'];
    //                 } else {
    //                     $person->scoreLabels = [];
    //                     $person->scoreData = [];
    //                 }
    //             });

    //     $finalLLPeople =  pipeline::where('plan_id', $this->plan->id);

    //     /*---------------------------------------------------------- Charts ----------------------------------------------------------*/
    //     // Gender Chart
    //     $LLpeopleCountsData = DB::table('pipelines')
    //     ->select('gender', DB::raw('COUNT(*) as total_people'))
    //     ->where('plan_id', $this->plan->id)
    //     ->groupBy('gender')
    //     ->get();
    //     //dd($LLpeopleCountsData);

    //     foreach ($LLpeopleCountsData as $data) {
    //         $this->LLgenderLabels[] = $data->gender;
    //         $this->LLgenderData[] = $data->total_people;
    //     }

    //     // Location Chart
    //     $LLpeopleCountsData = DB::table('pipelines')
    //     ->select('location', DB::raw('COUNT(*) as total_people'))
    //     ->where('plan_id', $this->plan->id)
    //     ->groupBy('location')
    //     ->get();
    //     // dd($LLpeopleCountsData);

    //     foreach ($LLpeopleCountsData as $data) {
    //         $this->LLlocationLabels[] = $data->location;
    //         $this->LLlocationData[] = $data->total_people;
    //     }

    //     // Company Chart
    //     $LLpeopleCountsData = DB::table('pipelines')
    //     ->select('company_name', DB::raw('COUNT(*) as total_people'))
    //     ->where('plan_id', $this->plan->id)
    //     ->groupBy('company_name')
    //     ->get();
    //     // dd($LLpeopleCountsData);

    //     foreach ($LLpeopleCountsData as $data) {
    //         $this->LLcompanyLabels[] = $data->company_name;
    //         $this->LLcompanyData[] = $data->total_people;
    //     }

    //     //Getting the career histories
    //     $filterExternal = $finalSLPeople->where('type','External')->pluck('people_id');

    //     $careerhistories = CareerHistories::whereIn('people_id',$filterExternal)
    //                                           ->get();

    //     // Get the career histories of the individual
    //     $peoplescareer = CareerHistories::whereIn('people_id', $filterExternal)
    //     ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
    //     ->select('career_histories.*', 'companies.name as company_name')
    //     ->get();

    //     $groupedPeoplesCareer = $peoplescareer->groupBy('people_id')->map(function ($items) {
    //         return $items->groupBy('company_name')->map(function ($companyItems) {
    //             return [
    //                 'roles' => $companyItems->pluck('role')->toArray(),
    //                 'start_dates' => $companyItems->map(function ($item) {
    //                     return Carbon::parse($item['start_date'])->format('M Y');
    //                 })->toArray(),
    //                 'end_dates' => $companyItems->map(function ($item) {
    //                     return Carbon::parse($item['end_date'])->format('M Y');
    //                 })->toArray(),
    //             ];
    //         });
    //     });

    //     //dd($groupedPeoplesCareer);
        
    //     $finalSLPeople->each(function ($person) use ($groupedPeoplesCareer) {
    //         $personId = $person->people_id; // Replace with the actual column name
        
    //         if ($groupedPeoplesCareer->has($personId)) {
    //             $person->career_data = $groupedPeoplesCareer[$personId]->all();
    //         }
    //     }); 

    //     $this->pipelinePeople = DB::table('pipelines')
    //     ->select(DB::raw('Count(*) as pipelinecount'))
    //     // ->groupby('plan_id')
    //     ->where('plan_id', $this->plan->id)
    //     ->get();

    //     // dd($this->pipelinePeople);

    //     $this->successPeople = DB::table('success_people')
    //     ->select(DB::raw('Count(*) as successcount'))
    //     // ->groupby('plan_id')
    //     ->where('plan_id', $this->plan->id)
    //     ->get();

    //     // dd($finalLLPeople); 

    //     // Loop through the filtered people and attach corresponding data
    //     $finalSLPeople->each(function ($person) {
    //         // Initialize arrays to store match labels and scores
    //         $matchLabels = [];
    //         $matchData = [];
            
    //         $matchLabels[] = 'Skills';  // Add label for skills
    //         $matchData[] = $person->skills_match; 

    //         $matchLabels[] = 'Roles';
    //         $matchData[] = $person->role_match;

    //         $matchLabels[] = 'Location';
    //         $matchData[] = $person->location_match;

    //         $matchLabels[] = 'Gender';
    //         $matchData[] = $person->gender_match;

    //         $matchLabels[] = 'Tenure';
    //         $matchData[] = $person->tenure_match;
            
    //         // Assign the match_labels and match_data fields to the person
    //         $person->match_labels = $matchLabels;
    //         $person->match_data = $matchData;
                
    //         });


    //     // ini_set('display_errors', 1);
    //     // ini_set('display_startup_errors', 1);
    //     // error_reporting(E_ALL);
        
    //     // $pdfOptions = new Options();
    //     // $pdfOptions->set('defaultFont', 'Arial');
    //     // $pdfOptions->set('isHtml5ParserEnabled', true);
    //     // $pdfOptions->set('isRemoteEnabled', true);

    //     // $pdfOptions->set('isHtml5ParserEnabled', true);
    //     // $pdfOptions->set('debugPng', true);
    //     // $pdfOptions->set('debugKeepTemp', true);

    //     // $dompdf = new Dompdf($pdfOptions);

    //     // // Convert your image to Base64
    //     // // $html = view('livewire.success-peotable-pdf-template')->render();

    //     // $imagePath = public_path() . '/images/minified/MapBackground.png';
    //     // $imageData = base64_encode(file_get_contents($imagePath));
    //     // $imageSrc = 'data:image/png;base64,' . $imageData;

    //     // $html = view('livewire.success-peotable-pdf-template', [
    //     //     'imageSrc' => $imageSrc
    //     // ])->render();
    //     // // $html = view('livewire.success-peotable-pdf', [
    //     // //         'plan' => $plandetails[0],
    //     // //         'pipelinePeople' => $this->pipelinePeople,
    //     // //         'successPeople' => $this->successPeople,
    //     // //         'LLgenderData' => $this->LLgenderData,
    //     // //         'LLgenderLabels' => $this->LLgenderLabels,
    //     // //         'LLlocationData' => $this->LLlocationData,
    //     // //         'LLlocationLabels' => $this->LLlocationLabels,
    //     // //         'plandetails'=>$plandetails,
    //     // //         'groupedPlanrequirements'=>$groupedPlanrequirements,
    //     // //         'finalPeople' => $finalSLPeople,
    //     // //         'genderData' => $this->genderData,
    //     // //         'genderLabels' => $this->genderLabels,
    //     // //         'typeData' => $this->typeData,
    //     // //         'typeLabels' => $this->typeLabels,
    //     // //         'LLFromCompanyData' => $this->LLFromCompanyData,
    //     // //         'LLFromCompanyLabels' => $this->LLFromCompanyLabels,
    //     // //         'locationData' => $this->locationData,
    //     // //         'locationLabels' => $this->locationLabels
    //     // //     ])->render();

    //     // // $html = view('livewire.pdf-export', [
    //     // //     'title' => "Test Title"
    //     // // ])->render();

    //     // // Convert the HTML to a format suitable for Dompdf
      
    //     // $html = mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8');

    //     // $dompdf->loadHtml($html, 'UTF-8');
    //     // $dompdf->setPaper('A4', 'landscape');
    //     // $dompdf->render();

    //     // $output = $dompdf->output();
    //     // $fileName = 'document.pdf';
    //     // Storage::put('public/'.$fileName, $output);

    //     // // return response()->download(storage_path('app/public/'.$fileName));
    //     // return Response::download(storage_path('app/public/'.$fileName), $fileName, [
    //     //     'Content-Type' => 'application/pdf',
    //     // ]);


    //     // $pdf = PDF::loadView('livewire.success-peotable-pdf', ['plan' => $plandetails[0], 'pipelinePeople' => $this->pipelinePeople, 'successPeople' => $this->successPeople, 'LLgenderData' => $this->LLgenderData, 'LLgenderLabels' => $this->LLgenderLabels, 'LLlocationData' => $this->LLlocationData, 'LLlocationLabels' => $this->LLlocationLabels, 'plandetails'=>$plandetails,'groupedPlanrequirements'=>$groupedPlanrequirements,'finalPeople'=>$finalSLPeople]);

    //     // return $pdf->download('document.pdf');

    //                     // The code to convert html intot pdf without color and images
    //                         $pdf = new \TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    //                         $pdf->SetTitle('Invoice');
    //                         $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
    //                         $pdf->setPrintHeader(false);
    //                         $pdf->setPrintFooter(false);
    //                         $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
    //                         $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

    //                         // set default font subsetting mode
    //                         $pdf->setFontSubsetting(true);

    //                         // add a page
    //                         $pdf->AddPage();
    //                         $utf8text =  view('livewire.success-peotable-pdf-template', [
    //                             'plan' => $plandetails[0],
    //                             'pipelinePeople' => $this->pipelinePeople,
    //                             'successPeople' => $this->successPeople,
    //                             'LLgenderData' => $this->LLgenderData,
    //                             'LLgenderLabels' => $this->LLgenderLabels,
    //                             'LLlocationData' => $this->LLlocationData,
    //                             'LLlocationLabels' => $this->LLlocationLabels,
    //                             'plandetails'=>$plandetails,
    //                             'groupedPlanrequirements'=>$groupedPlanrequirements,
    //                             'finalPeople' => $finalSLPeople,
    //                             'genderData' => $this->genderData,
    //                             'genderLabels' => $this->genderLabels,
    //                             'typeData' => $this->typeData,
    //                             'typeLabels' => $this->typeLabels,
    //                             'LLFromCompanyData' => $this->LLFromCompanyData,
    //                             'LLFromCompanyLabels' => $this->LLFromCompanyLabels,
    //                             'locationData' => $this->locationData,
    //                             'locationLabels' => $this->locationLabels
    //                         ]);

    //                         // write the text
    //                         $pdf->writeHTML($utf8text, true, false, true, false, '');
    //                         if (ob_get_length() > 0) {
    //                             ob_clean();
    //                         }      //Close and output PDF document
    //                         $pdfContent = $pdf->Output('generated_pdf.pdf', 'S');

    //                         // Download the PDF
    //                         return response()->streamDownload(function() use ($pdfContent) {
    //                             echo $pdfContent;
    //                         }, 'generated_pdf.pdf');

    //     // ini_set('memory_limit', '512M');
    //     // ini_set('max_execution_time', 600); // 600 seconds

    //     // // Ensure you have the required data passed to the view
    //     // $htmlContent = view('livewire.success-peotable-pdf', [
    //     //     'plan' => $plandetails[0],
    //     //     'pipelinePeople' => $this->pipelinePeople,
    //     //     'successPeople' => $this->successPeople,
    //     //     'LLgenderData' => $this->LLgenderData,
    //     //     'LLgenderLabels' => $this->LLgenderLabels,
    //     //     'LLlocationData' => $this->LLlocationData,
    //     //     'LLlocationLabels' => $this->LLlocationLabels,
    //     //     'plandetails' => $plandetails,
    //     //     'groupedPlanrequirements' => $groupedPlanrequirements,
    //     //     'finalPeople' => $finalSLPeople,
    //     //     'genderData' => $this->genderData,
    //     //     'genderLabels' => $this->genderLabels,
    //     //     'typeData' => $this->typeData,
    //     //     'typeLabels' => $this->typeLabels,
    //     //     'LLFromCompanyData' => $this->LLFromCompanyData,
    //     //     'LLFromCompanyLabels' => $this->LLFromCompanyLabels,
    //     //     'locationData' => $this->locationData,
    //     //     'locationLabels' => $this->locationLabels
    //     // ])->render();

    //     // // Create PDF using Snappy
    //     // return SnappyPdf::loadHTML($htmlContent)
    //     //                 // ->setTimeout(500)
    //     //                 ->setPaper('a4')
    //     //                 // ->setOption('encoding', 'UTF-8')
    //     //                 ->setOption('no-outline', true)
    //     //                 ->stream('generated_pdf.pdf');

    // }
}
