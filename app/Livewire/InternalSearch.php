<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\InternalPeople;
use App\Models\InternalSkills;
use App\Models\SuccessPeople;
use Livewire\WithPagination;
use App\Models\SuccessRequirements;
use OpenAI\Laravel\Facades\OpenAI;
use App\Models\SuccessSkills;
use App\Models\PlanScores;
use App\Models\UserNotes;
use App\Models\pipeline;
use Livewire\WithFileUploads;
use App\Models\SuccessionPlan;
use App\Models\Location;
use App\Models\Role;
use App\Models\Company;
use App\Models\Skills;
use Carbon\Carbon;
use App\Models\internal_career_histories;
use Illuminate\Support\Facades\DB;

use function PHPSTORM_META\map;

class InternalSearch extends Component
{
    public $plandetails;
    public $openPlanPopup=false;
    public $disableDispatchforMultipleSelect=false;
    public $selectedPerson = [];
    // Variables for the table
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $search = false;
    public $searchByKeyword = '';
    public $selectedIndividualID;
    public $personRoles;

    //Variables for filters
    public $forename = "";
    public $surname = "";
    public $gender = "";
    public $role;
    public $_role = [];
    public $previousRole = [];
    public $function = [];
    public $division = [];
    public $location = [];
    public $regBodies = "";
    public $tenure;
    public $totalPeople;
    public $totalCompanies;
    public $locations;
    public $functions;
    public $divisions;

    public $perPage =   50; // Number of items per page
    public $page    =   1; // Current page number


    // Variables for charts
    public $plan;
    public $userPlans;
    public $pipeline;
    public $divisionLabels = [];
    public $divisionData = [];
    public $functionLabels = [];
    public $functionData = [];
    public $genderLabels = [];
    public $genderData = [];
    public $locationLabels = [];
    public $locationData = [];
    public $readyLabels = [];
    public $readyData   = [];

    //Variables for adding users to plan with a button
    public $SuccessSkills;
    public $SuccessRoles;
    public $SuccessStepup;
    public $SuccessLocation;
    public $SuccessGender;
    public $min_exp =   null;
    public $internalUserPlans = [];
    public $vopen = false;
    public $addToPlanPopup = false;
    public $plansList = [];
    public $addToPlansArray = [];

    public function mount()
    {
        $user = auth()->user();
        $this->userPlans = SuccessionPlan::where('user_id', $user->id)->get();
        $this->plandetails = DB::table('succession_plans')->where('id', $this->plan)->first();
        // dd($this->plandetails);

        // $this->pipelineid = Pipeline::where('plan_id', $this->plan)->value('id');

        $this->SuccessSkills = SuccessRequirements::where('plan_id', $this->plan)
            ->where('type', 'professional_skill')
            ->get();

        $this->SuccessRoles = SuccessRequirements::where('plan_id', $this->plan)
            ->where('type', 'Role')
            ->get();

        $this->SuccessLocation = SuccessRequirements::where('plan_id', $this->plan)
            ->where('type', 'Location')
            ->get();

        $this->SuccessGender = SuccessRequirements::where('plan_id', $this->plan)
            ->where('type', 'Gender')
            ->get();

        $this->SuccessStepup = SuccessRequirements::where('plan_id', $this->plan)
            ->where('type', 'step_up')
            ->get();

        $this->locations = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $this->personRoles = Role::distinct()->pluck('title')->map(function ($role) {
            return [
                'value' => $role,
                'label' => $role,
            ];
        })->toArray();

        $this->functions = DB::table('pipelines')->distinct()
            ->pluck('function')
            ->map(function ($function) {
                return [
                    'value' => $function,
                    'label' => $function,
                ];
            })
            ->toArray();

        $this->divisions = DB::table('pipelines')->distinct()
            ->pluck('division')
            ->map(function ($division) {
                return [
                    'value' => $division,
                    'label' => $division,
                ];
            })
            ->toArray();
    }

    use WithPagination;
    public function render()
    {
        //dd($this->plan); 

        $user = auth()->user();
        $plans = SuccessionPlan::where('user_id', $user->id)->get();

        $this->pipeline = pipeline::where('plan_id', $this->plan)
            ->where('people_type', 'Internal-User')
            ->pluck('people_id')->toArray();
        // dd($this->pipeline);

        $internalpeople = DB::table('internal_people')
            ->whereNotIn('id', $this->pipeline)
            ->whereNot('forename', 'Vacant')
            ->where('company_id','=',$user->company_id)
            // ->join('roles', 'internal_people.role_id', '=', 'roles.id')
            // ->select('internal_people.*', 'roles.title as role_title')
            ->orderBy($this->sortBy, $this->sortDirection);
        //dd($internalpeople);
        if ($this->search) {

            if ($this->forename) {
                $internalpeople = $internalpeople->where('forename', 'like', '%' . $this->forename . '%');
            }
            if ($this->surname) {
                $internalpeople = $internalpeople->where('surname', 'like', '%' . $this->surname . '%');
            }
            if ($this->gender) {
                $internalpeople = $internalpeople->where('gender', '=', $this->gender);
            }
            if ($this->_role) {
                $internalpeople = $internalpeople->where('latest_role', 'like', '%' . $this->_role . '%');
            }
            if ($this->function) {
                $internalpeople = $internalpeople->where('function', 'like', '%' . $this->function . '%');
            }
            if ($this->division) {
                $internalpeople = $internalpeople->where('division', 'like', '%' . $this->division . '%');
            }
            if ($this->location) {
                $internalpeople = $internalpeople->where('location', 'like', '%' . $this->location . '%');
            }
            if ($this->regBodies) {
                $internalpeople = $internalpeople->where('other_tags', 'like', '%' . $this->regBodies . '%');
            }
            if ($this->min_exp) {
                $internalpeople = $internalpeople->where('tenure', '>=', $this->min_exp);
            }
        }

        // dd($this->selectedIndividualID);

        $filteredPeople = DB::table('internal_people')->where('id', $this->selectedIndividualID)
            ->first();


        $EnrichedSuccessPeople = SuccessPeople::leftJoin('succession_plans', 'success_people.plan_id', '=', 'succession_plans.id')
            ->where('success_people.type', 'Internal')
            ->select('success_people.*', 'succession_plans.name')
            ->get();

        $groupedinternalpPlansPeople = $EnrichedSuccessPeople->groupBy('people_id');

        $groupedinternalpPlansPeople->transform(function ($item) {
            return [
                'plan_id'    => $item->pluck('plan_id')->toArray(),
                'plan_name'  => $item->pluck('name')->toArray(),
            ];
        });

        // if (!empty($filteredPeople)) {
        //     // Loop through the plans and attach corresponding data from groupedPlanScores
        //     $filteredPeople->each(function ($person) use ($groupedinternalpPlansPeople) {
        //         $PeopleId = $person->id;
        //         if (isset($groupedinternalpPlansPeople[$PeopleId])) {
        //             $person->plan_id = $groupedinternalpPlansPeople[$PeopleId]['plan_id'];
        //             $person->plan_name = $groupedinternalpPlansPeople[$PeopleId]['plan_name'];
        //         } else {
        //             $person->plan_id = [];
        //             $person->plan_name = [];
        //         }
        //     });
        // }


        if (!empty($filteredPeople)) {
            $Manager = InternalPeople::where('id', $filteredPeople->reports_to)->first();
            // Rest of your code
            // Get the career histories of the individuals
            $peoplescareer = internal_career_histories::where('people_id', $filteredPeople->id)
                ->join('companies', 'internal_career_histories.past_company_id', '=', 'companies.id')
                ->select('internal_career_histories.*', 'companies.name as company_name')
                ->orderBy('start_date', 'desc')
                ->get();


            // $groupedPeoplesCareer = $peoplescareer->groupBy('company_name')->map(function ($items) {
            //     return [
            //         'roles' => $items->pluck('role')->toArray(),
            //         'start_dates' => $items->pluck('start_date')->map(function ($date) {
            //             return Carbon::parse($date)->format('M Y');
            //         })->toArray(),
            //         'end_dates' => $items->pluck('end_date')->map(function ($date) {
            //             return Carbon::parse($date)->format('M Y');
            //         })->toArray(),
            //     ];
            // });
        } else {
            // Handle the case when $filteredPeople is empty
            $peopleskills = null;
            // $groupedPeoplesCareer = null;
            $peoplescareer = null;
            $Manager = null; // or whatever you want to set it to
        }

        //dd($Manager);

        if (!empty($filteredPeople)) {
            // dd($this->selectedIndividualID);
            //Get the associated skills of the individual
            $internalCoSkills =  DB::table('internal_skills')->where('internal_people', "=", $this->selectedIndividualID)
                ->where('skill_type', '=', 'Common')
                ->get();

            $internalSpSkills =  DB::table('internal_skills')->where('internal_people', "=", $this->selectedIndividualID)
                ->where('skill_type', '=', 'Specialised')
                ->get();

            // Loop through the notes and format the created_at timestamp using Carbon
            $internalPSkills =  DB::table('internal_skills')->where('internal_people', "=", $this->selectedIndividualID)
                ->where('skill_type', '=', 'Professional')
                ->get();
        } else {
            // Handle the case when $filteredPeople is empty
            $internalCoSkills = null;
            $internalSpSkills = null;
            $internalPSkills = null;
        }

        //dd($notes);

        //Rendering for Charts
        //Functions Chart
        $internfunctions = DB::table('internal_people')
            ->select('function', DB::raw('COUNT(*) as total_function'))
            ->where('company_id', $user->company_id)
            ->groupBy('function')
            ->limit(5)
            ->orderBy('total_function', 'desc')
            ->get();

        $this->functionData = [];
        $this->functionLabels = [];
        foreach ($internfunctions as $data) {
            $this->functionData[] = $data->total_function;
            $this->functionLabels[] = $data->function;
        }

        //Division Charts
        $interndivision = DB::table('internal_people')
            ->select('division', DB::raw('COUNT(*) as total_division'))
            ->where('company_id', $user->company_id)
            ->groupBy('division')
            ->limit(10)
            ->orderBy('total_division', 'desc')
            ->get();

        $this->divisionData = [];
        $this->divisionLabels = [];
        foreach ($interndivision as $data) {
            $this->divisionData[] = $data->total_division;
            $this->divisionLabels[] = $data->division;
        }

        //Gender Charts
        $interngender = DB::table('internal_people')
            ->select('gender', DB::raw('COUNT(*) as total_gender'))
            ->where('company_id', $user->company_id)
            ->groupBy('gender')
            ->get();

        //dd($interngender);
        $this->genderLabels = [];
        $this->genderLabels = [];
        foreach ($interngender as $data) {
            $this->genderData[] = $data->total_gender;
            if ($data->gender === null) {
                $this->genderLabels[] = 'Not Applicable';
            } else {
                $this->genderLabels[] = $data->gender;
            }
        }

        // Readiness Chart
        $internready = DB::table('internal_people')
            ->select('readiness', DB::raw('COUNT(*) as total_ready'))
            ->where('company_id', $user->company_id)
            ->groupBy('readiness')
            ->get();

        //dd($interngender);
        $this->readyLabels = [];
        $this->readyData = [];
        foreach ($internready as $data) {
            $this->readyData[] = $data->total_ready;
            if ($data->readiness === null) {
                $this->readyLabels[] = 'Not Applicable';
            } else {
                $this->readyLabels[] = $data->readiness;
            }
        }

        //Location Charts
        $internlocation = DB::table('internal_people')
            ->select('country', DB::raw('COUNT(*) as total_location'))
            ->where('company_id', $user->company_id)
            ->groupBy('country')
            ->limit(5)
            ->orderBy('total_location', 'desc')
            ->get();

        //dd($interngender);
        $this->locationData = [];
        $this->locationLabels = [];
        foreach ($internlocation as $data) {
            $this->locationData[] = $data->total_location;
            $this->locationLabels[] = $data->country;
        }

        if ($this->searchByKeyword != "") {
            $internalpeople = $this->searchPeople();
        }
        $this->dispatch('updateChart');
        // dd($internalpeople->paginate(10));

        return view('livewire.internal-search', ['internalpeople' => $internalpeople->paginate(50), 'internalCoSkills' => $internalCoSkills, 'internalSpSkills' => $internalSpSkills, 'Managers' => $Manager, 'groupedPeoplesCareer' => $peoplescareer, "internalPSkills" => $internalPSkills]);
    }

    public function searchPeople()
    {
        $user = auth()->user();
        $internalpeople = DB::table('internal_people')->where('company_id', $user->company_id)
            ->whereNotIn('id', $this->pipeline)
            ->join('roles', 'internal_people.role_id', '=', 'roles.id')
            ->select('internal_people.*', 'roles.title as role_title')
            ->orderBy($this->sortBy, $this->sortDirection);

        $searchKeywords = explode(",", $this->searchByKeyword);
        $internalpeople = $internalpeople->where(function ($query) use ($searchKeywords) {
            foreach ($searchKeywords as $searchword) {
                $query->orWhere(function ($subQuery) use ($searchword) {
                    $subQuery->orWhere('forename', 'like', '%' . $searchword . '%')
                        ->orWhere('middle_name', 'like', '%' . $searchword . '%')
                        ->orWhere('surname', 'like', '%' . $searchword . '%')
                        ->orWhere('company_name', 'like', '%' . $searchword . '%')
                        ->orWhere('function', 'like', '%' . $searchword . '%')
                        ->orWhere('division', 'like', '%' . $searchword . '%')
                        ->orWhere('country', 'like', '%' . $searchword . '%')
                        ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                });
            }
        });
        $this->dispatch('updateChart');
        return $internalpeople;
    }

    public function test()
    {
        dd('function trigger');
    }

    public function runfilters()
    {
        $this->search = true;
        $this->dispatch('updateChart');
    }

    public function viewIndividual($id)
    {

        $this->selectedIndividualID = $id;
        $this->setInternalUserPlans($id);
        $this->plansList = getPlansList($this->selectedIndividualID, $this->plan);
        $this->dispatch('updateChart');
    }
    public function setInternalUserPlans($peopleId) {
        $user = auth()->user();
        $planIds = SuccessPeople::where(['people_id' => $peopleId, 'type' => 'Internal'])->pluck('plan_id')->toArray();
        $this->internalUserPlans = SuccessionPlan::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
        })
        ->whereIn('id', $planIds)
        ->get();
        
    }
    public function addpeopleToPlans() {

        if(empty($this->addToPlansArray)) {
            $this->dispatch('toast', 'info', 'Please select plans!');
            $this->skipRender();
            return;
        }
        $selectedPlan = $this->plan;
        foreach($this->addToPlansArray as $key => $value) {
            if($value) {
                $PlanPerson = SuccessPeople::where(['people_id' => $this->selectedIndividualID,'plan_id' => $key])->first();
              
                if($PlanPerson) {
                  continue;
                }

                $this->plan = $key;
                $this->addToPlan($this->selectedIndividualID);
            }
        }
        $this->plan = $selectedPlan;
        if(!$this->disableDispatchforMultipleSelect) {
        $this->dispatch('toast', 'info', 'Added to selected plans!');
        $this->addToPlanPopup = false;
        $this->vopen = false;
        $this->addToPlansArray = [];
        }
        $this->setInternalUserPlans($this->selectedIndividualID);


    }
    public function showAddToPlanPopup($peopleId) {
        $this->selectedIndividualID = $peopleId;
        $this->plansList = getPlansList($peopleId, $this->plan);
        $this->addToPlanPopup = true;

    }

    public function Ordermytable($column)
    {
        if ($column === $this->sortBy) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
        $this->dispatch('updateChart');
    }

    public function clearFilters()
    {
        $this->search = false;
        // Reset all form input properties
        $this->reset(['forename', 'surname', '_role', 'function', 'division', 'location', 'gender', 'min_exp', 'regBodies']);
        $this->dispatch('updateChart');
    }

    public function addToPlan($id)
    {
        $user = auth()->user();

        // Get the details of the external person
        $InternalPerson = InternalPeople::where('id', $id)->first();

        //Get the skills of the person this will be used for the radar chart
        $PersonSkills = InternalSkills::where('internal_people', $id)->get();

        $successionPlan = SuccessionPlan::find($this->plan);


        $tenureMatch = 0;
        if ($InternalPerson->tenure >= $successionPlan->minimum_Experience) {
            $tenureMatch = 1;
        }


        //Get there scores ready for the pipeline and plans
        //Role Scores
        $latestRole = $InternalPerson->latest_role;

        $RoleScore = 0;
        if ($this->SuccessRoles !== null) {
            if ($this->SuccessRoles->contains('name', $latestRole)) {
                $RoleScore = 1;
            } else {
                if ($this->SuccessStepup->contains('name', $latestRole)) {
                    $RoleScore = 0.75;
                } else {
                    $RoleScore = 0;
                }
            }
        }

        //Skill Score
        $SkillScore = 0;
        if (!empty($this->SuccessSkills) && $this->SuccessSkills->count() > 0) {
            foreach ($PersonSkills as $pK) {
                foreach ($this->SuccessSkills as $sK) {
                    if ($sK->name == $pK->skill_name) {
                        $SkillScore++;
                        break;
                    }
                }
            }

            $SkillScore /= $this->SuccessSkills->count();
        }


        //Gender Match
        $Gender_Match = 0;
        if ($this->SuccessGender->contains('name', $InternalPerson->gender)) {
            $Gender_Match = 1;
        } else {
            $Gender_Match = 0;
        }

        // Location Match
        $Location_Match = 0;
        if ($this->SuccessLocation->contains('name', $InternalPerson->location)) {
            $Location_Match = 1;
        } else {
            $Location_Match = 0;
        }

        //Tenture Match


        $roleai = $InternalPerson->role;
        $locationai = $InternalPerson->location;
        $functionai = $InternalPerson->function;
        $divisionai = $InternalPerson->division;
        //$companyai = $itemData->company_name;

        $inputArray = [
            'role'     => $roleai,
            'location' => $locationai,
            'function' => $functionai,
            'division' => $divisionai
        ];

        // Create a formatted message for GPT-3
        $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
        foreach ($inputArray as $key => $value) {
            $message .= "$key: \"$value\"\n";
        }

        // Call GPT-3 to generate the headline
        $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
        ]);

        $generatedHeadline = $response->choices[0]->message->content;

        $newPipeline = pipeline::create([
            'plan_id'            => $this->plan,
            'user_id'            => $user->id,
            'people_id'          => $InternalPerson->id,
            'headline'           => $generatedHeadline,
            'first_name'         => $InternalPerson->forename,
            'last_name'          => $InternalPerson->surname,
            'middle_name'        => $InternalPerson->middle_name,
            'other_name'         => $InternalPerson->other_name,
            'gender'             => $InternalPerson->gender,
            'diverse'            => $InternalPerson->diverse,
            'exco'               => $InternalPerson->exco,
            'location'           => $InternalPerson->location,
            'country'            => $InternalPerson->country,
            'city'               => $InternalPerson->city,
            'linkedinURL'        => $InternalPerson->linkedinURL,
            'latest_role'        => $InternalPerson->latest_role ?? $InternalPerson->role,
            'company_id'         => $InternalPerson->company_id,
            'company_name'       => $InternalPerson->company_name,
            'start_date'         => $InternalPerson->start_date,
            'end_date'           => $InternalPerson->end_date,
            'tenure'             => $InternalPerson->tenure,
            'function'           => $InternalPerson->function,
            'division'           => $InternalPerson->division,
            'seniority'          => $InternalPerson->seniority,
            'career_history'     => $InternalPerson->career_history,
            'educational_history' => $InternalPerson->educational_history,
            'skills'             => $InternalPerson->skills,
            'languages'          => $InternalPerson->languages,
            'skills_match'       => $SkillScore,
            'education_match'    => 0,
            'location_match'     => $Location_Match,
            'role_match'         => $RoleScore,
            'gender_match'       => $Gender_Match,
            'tenure_match'       => $tenureMatch,
            'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
            'people_type'        => "Internal-User"
        ]);

        $dataToCreate = [
            'pipeline_id'        => $newPipeline->id,
            'plan_id'            => $this->plan,
            'user_id'            => $user->id,
            'people_id'          => $InternalPerson->id,
            'headline'           => $generatedHeadline,
            'first_name'         => $InternalPerson->forename,
            'last_name'          => $InternalPerson->surname,
            'middle_name'        => $InternalPerson->middle_name,
            'other_name'         => $InternalPerson->other_name,
            'gender'             => $InternalPerson->gender,
            'diverse'            => $InternalPerson->diverse,
            'exco'               => $InternalPerson->exco,
            'location'           => $InternalPerson->location,
            'country'            => $InternalPerson->country,
            'city'               => $InternalPerson->city,
            'summary'            => $InternalPerson->summary,
            'linkedinURL'        => $InternalPerson->linkedinURL,
            'latest_role'        => $InternalPerson->latest_role,
            'company_id'         => $InternalPerson->company_id,
            'company_name'       => $InternalPerson->company_name,
            'start_date'         => $InternalPerson->start_date,
            'end_date'           => $InternalPerson->end_date,
            'tenure'             => $InternalPerson->tenure,
            'function'           => $InternalPerson->function,
            'division'           => $InternalPerson->division,
            'seniority'          => $InternalPerson->seniority,
            'career_history'     => $InternalPerson->career_history,
            'educational_history' => $InternalPerson->educational_history,
            'skills'             => $InternalPerson->skills,
            'languages'          => $InternalPerson->languages,
            'skills_match'       => $SkillScore,
            'education_match'    => 0,
            'location_match'     => $Location_Match,
            'role_match'         => $RoleScore,
            'gender_match'       => $Gender_Match,
            'tenure_match'       => $tenureMatch,
            'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
            'type'               => "Internal",
            'notes'              => "Enter notes here",
            'recruit'            => 0,
            'status'             => 'Approved'
        ];

        if($successionPlan->user_id != auth()->user()->id) {
            $dataToCreate['status'] = "Proposed";
        }

        $SuccessPeople = SuccessPeople::create($dataToCreate);


        //------------------- Get the new Gender Diversity Score -----------------//
        $femaleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Female")/Count(*))*100 as female_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Female-ratio'],
            ['score' => $femaleRatio->female_ratio]
        );

        $maleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Male")/Count(*))*100 as male_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();


        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Male-Ratio'],
            ['score' => $maleRatio->male_ratio]
        );

        $InternalRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Internal-External Ratio'],
            ['score' => $InternalRatio->internal_ratio]
        );

        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Skill Score'],
            ['score' => $averageskillscore->average_skill_score]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Tenure Score'],
            ['score' => $averagetenurescore->average_tenure_score]
        );

        // Add requirements for chart for skills radar
        $SkillFound = false;
        foreach ($this->SuccessSkills as $sK) {
            $SkillFound = false;
            foreach ($PersonSkills as $pK) {
                if ($sK->name == $pK->skill_name) {
                    SuccessSkills::create([
                        'succession_plan_id' => $this->plan,
                        'success_people_id' => $SuccessPeople->id,
                        'success_requirements_id' => $sK->id,
                        'skill_name'        => $sK->name,
                        'score' => 1
                    ]);
                    $SkillFound = true;
                    break;
                }
            }

            if (!$SkillFound) {
                SuccessSkills::create([
                    'succession_plan_id' => $this->plan,
                    'success_people_id' => $SuccessPeople->id,
                    'success_requirements_id' => $sK->id,
                    'skill_name'        => $sK->name,
                    'score' => 0
                ]);
            }
        }
        $this->dispatch('updateChart');
    }

    public function addToPotentialCandidate($id) {
        $user = auth()->user();

        // Get the details of the external person
        $InternalPerson = InternalPeople::where('id', $id)->first();

        //Get the skills of the person this will be used for the radar chart
        $PersonSkills = InternalSkills::where('internal_people', $id)->get();

        $successionPlan = SuccessionPlan::find($this->plan);


        $tenureMatch = 0;
        if ($InternalPerson->tenure >= $successionPlan->minimum_Experience) {
            $tenureMatch = 1;
        }


        //Get there scores ready for the pipeline and plans
        //Role Scores
        $latestRole = $InternalPerson->latest_role;

        $RoleScore = 0;
        if ($this->SuccessRoles !== null) {
            if ($this->SuccessRoles->contains('name', $latestRole)) {
                $RoleScore = 1;
            } else {
                if ($this->SuccessStepup->contains('name', $latestRole)) {
                    $RoleScore = 0.75;
                } else {
                    $RoleScore = 0;
                }
            }
        }

        //Skill Score
        $SkillScore = 0;
        if (!empty($this->SuccessSkills) && $this->SuccessSkills->count() > 0) {
            foreach ($PersonSkills as $pK) {
                foreach ($this->SuccessSkills as $sK) {
                    if ($sK->name == $pK->skill_name) {
                        $SkillScore++;
                        break;
                    }
                }
            }

            $SkillScore /= $this->SuccessSkills->count();
        }


        //Gender Match
        $Gender_Match = 0;
        if ($this->SuccessGender->contains('name', $InternalPerson->gender)) {
            $Gender_Match = 1;
        } else {
            $Gender_Match = 0;
        }

        // Location Match
        $Location_Match = 0;
        if ($this->SuccessLocation->contains('name', $InternalPerson->location)) {
            $Location_Match = 1;
        } else {
            $Location_Match = 0;
        }

        //Tenture Match


        $roleai = $InternalPerson->role;
        $locationai = $InternalPerson->location;
        $functionai = $InternalPerson->function;
        $divisionai = $InternalPerson->division;
        //$companyai = $itemData->company_name;

        $inputArray = [
            'role'     => $roleai,
            'location' => $locationai,
            'function' => $functionai,
            'division' => $divisionai
        ];

        // Create a formatted message for GPT-3
        $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
        foreach ($inputArray as $key => $value) {
            $message .= "$key: \"$value\"\n";
        }

        // Call GPT-3 to generate the headline
        $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
        ]);

        $generatedHeadline = $response->choices[0]->message->content;

        $newPipeline = pipeline::create([
            'plan_id'            => $this->plan,
            'user_id'            => $user->id,
            'people_id'          => $InternalPerson->id,
            'headline'           => $generatedHeadline,
            'first_name'         => $InternalPerson->forename,
            'last_name'          => $InternalPerson->surname,
            'middle_name'        => $InternalPerson->middle_name,
            'other_name'         => $InternalPerson->other_name,
            'gender'             => $InternalPerson->gender,
            'diverse'            => $InternalPerson->diverse,
            'exco'               => $InternalPerson->exco,
            'location'           => $InternalPerson->location,
            'country'            => $InternalPerson->country,
            'city'               => $InternalPerson->city,
            'linkedinURL'        => $InternalPerson->linkedinURL,
            'latest_role'        => $InternalPerson->latest_role,
            'company_id'         => $InternalPerson->company_id,
            'company_name'       => $InternalPerson->company_name,
            'start_date'         => $InternalPerson->start_date,
            'end_date'           => $InternalPerson->end_date,
            'tenure'             => $InternalPerson->tenure,
            'function'           => $InternalPerson->function,
            'division'           => $InternalPerson->division,
            'seniority'          => $InternalPerson->seniority,
            'career_history'     => $InternalPerson->career_history,
            'educational_history' => $InternalPerson->educational_history,
            'skills'             => $InternalPerson->skills,
            'languages'          => $InternalPerson->languages,
            'skills_match'       => $SkillScore,
            'education_match'    => 0,
            'location_match'     => $Location_Match,
            'role_match'         => $RoleScore,
            'gender_match'       => $Gender_Match,
            'tenure_match'       => $tenureMatch,
            'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
            'people_type'        => "Internal-User"
        ]);
        if(!$this->disableDispatchforMultipleSelect) {
        $this->dispatch('toast', 'info', "Added to potential candidate!");
        }
    }
    public function changeMinimumTenure($flag)
    {
        if ($flag == 'decrease') {
            $this->min_exp = $this->min_exp <= 1 ? null : ($this->min_exp - 1);
        } else if ($flag == 'increase') {
            $this->min_exp = !$this->min_exp ? 1 : ($this->min_exp + 1);
        }
    }


    public function addSelectedToPotentialCandidate()
    { 
        $this->disableDispatchforMultipleSelect = true;
        foreach($this->selectedPerson as $id) { 
            $this->addToPotentialCandidate($id);
        }
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', "Added to potential candidate!");
        $this->selectedPerson = [];
    }
    

    public function addSelectedToPlan()
    { 
        $this->plansList = collect(); 
        
        $processed = [];
        
        foreach ($this->selectedPerson as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->plansList = $this->plansList->merge(getPlansList($peopleId, $this->plan));
                $processed[] = $peopleId;
            }
        }
      
        $this->plansList = $this->plansList->unique('id')->values();
        $this->addToPlanPopup = true;
        $this->openPlanPopup = true;
        
    }
    public function addSelectedPersonToPlans()
    {
      $this->disableDispatchforMultipleSelect = true;
       foreach ($this->selectedPerson as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;
            $this->addpeopleToPlans();
       }
        $this->openPlanPopup = false;
        $this->selectedPerson = [];
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected plans!');
        $this->addToPlanPopup = false;
       
        $this->vopen = false;
        $this->addToPlansArray = [];
    }
   /* public function deleteSelected()
    {
        $selectedIds = $this->selectedPerson;
        foreach ($selectedIds as $id) {
            InternalPeople::where('id', $id)->delete();
        }
        $this->dispatch('toast', 'success', 'Individual has deleted successfully.');
        $this->selectedPerson = [];
    }*/
    public function updatingPage() {
        $this->selectedPerson = [];
    }
}
