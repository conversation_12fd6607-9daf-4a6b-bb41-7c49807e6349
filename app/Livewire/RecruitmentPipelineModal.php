<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\RecruitmentPipeline;
use App\Models\Recruitment;
use App\Models\Plan;
use App\Models\Job;
use App\Models\People;

class RecruitmentPipelineForm extends Component
{
    public $recruitment;
    public $plans;
    public $jobs;
    public $candidates;

    public $Recruitment_project_id;
    public $Plan_id;
    public $Job_id;
    public $Candidate_ID;
    public $Phone_number;
    public $Email;
    public $Address;
    public $Link_cv;
    public $Status = 'pending';
    public $Consent_document;

    public function mount()
    {
        $this->recruitment = Recruitment::all();
        $this->plans = Plan::all();
        $this->jobs = Job::all();
        $this->candidates = People::all();
    }

    public function save()
    {
        $this->validate([
            'Recruitment_project_id' => 'required|exists:recruitment,id',
            'Plan_id' => 'required|exists:Plans,id',
            'Job_id' => 'required|exists:Jobs,id',
            'Candidate_ID' => 'required|exists:Candidates,id',
            'Phone_number' => 'nullable|string',
            'Email' => 'nullable|email',
            'Address' => 'nullable|string',
            'Link_cv' => 'nullable|url',
            'Consent_document' => 'nullable|string',
        ]);

        RecruitmentPipeline::create([
            'Recruitment_project_id' => $this->Recruitment_project_id,
            'Plan_id' => $this->Plan_id,
            'Job_id' => $this->Job_id,
            'Candidate_ID' => $this->Candidate_ID,
            'Phone_number' => $this->Phone_number,
            'Email' => $this->Email,
            'Address' => $this->Address,
            'Link_cv' => $this->Link_cv,
            'Status' => $this->Status,
            'Consent_document' => $this->Consent_document,
        ]);

        session()->flash('success', 'Candidate successfully added to the Recruitment Pipeline!');
        $this->reset(); // Clear the form
    }

    public function render()
    {
		
        return view('livewire.recruitment-pipeline-form');
    }
}