<?php

namespace App\Livewire;

use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use App\Models\Company;
use App\Models\Account;
use App\Models\SuccessPeople;
use App\Models\InternalPeople;
use App\Models\People;
use App\Models\notifications;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Livewire\Component;

class HomePage extends Component
{
    // Variable for feed filter
    public $type = '';
    public $user;

    public $plancountall;

    public $InternalPeopeCount;
    public $externalPeopleCount;
    public $InternalPeopleAdded;
    public $genderCount;
    public $topHirer;
    public $currentQuarterJoinings;
    public $moverplans;
    public $topMovementCompanies;

    // Variables for graphs
    public $statusLabels = [];
    public $statusData = [];
    public $jstatusLabels = [];
    public $jstatusData = [];
    public $pstatusLabels = [];
    public $pstatusData = [];
    public $candidatesLabel = [];
    public $candidatesData = [];

    public function mount()
    {
        $this->user = auth()->user();

        // Code to get the companies of interest for a specific account
        $accountObj = Account::where('id', $this->user->account_id)->first();

        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        if($accountObj){
            if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }

        $companyIds = [];
        if($interestedCompanies && count($interestedCompanies) > 0){
            $companyIds = $interestedCompanies;
        }
        else if($interestedIndustries && count($interestedIndustries) > 0){
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if($interestedSectors && count($interestedSectors) > 0){
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
        }

        $this->interestedCompaniesarray = $companyIds;
        //dd($this->interestedCompaniesarray);
        
        // Count the number of active plans
        $PlansCount = DB::table('succession_plans')
            ->select('status', DB::raw('COUNT(*) as plans_count'))
            ->where('user_id', $this->user->id)
            ->groupBy('status')
            ->get();


        foreach ($PlansCount as $pdata) {
            $this->statusLabels[] = $pdata->status;
            $this->statusData[] = $pdata->plans_count;
        }

        //Get the Job Statuses
        $JobsCount = DB::table('jobs')
            ->select('status', DB::raw('COUNT(*) as jobs_count'))
            ->where('user_id', $this->user->id)
            ->groupBy('status')
            ->get();

        foreach ($JobsCount as $jdata) {
            $this->jstatusLabels[] = $jdata->status;
            $this->jstatusData[] = $jdata->jobs_count;
        }

        //dd($JobsCount);

        // Get the status of people in the platform
        $PepCount = DB::table('people')
                        ->select('status', DB::raw('COUNT(*) as pe_count'))
                        ->where('company_id', '!=', $this->user->company_id)
                        ->when(!empty($this->interestedCompaniesarray), function($query){
                            $query->WhereIn('company_id',$this->interestedCompaniesarray);
                        })
                        ->where('status', '!=', 'Submitted')
                        ->where('status', '!=', 'Reported')
                        ->groupBy('status')
                        ->get();

        foreach ($PepCount as $pedata) {
            $this->pstatusLabels[] = $pedata->status ? str_replace('_', ' ', $pedata->status) : $pedata->status;
            $this->pstatusData[] = $pedata->pe_count;
        }

        $this->plancountall = SuccessionPlan::where('user_id', $this->user->id)->count();

        //$this->moverplans = SuccessionPlan::where('user_id', $this->user->id)->count();

        $plans = SuccessionPlan::where('user_id', $this->user->id)->pluck('id');

        $successpeople = SuccessPeople::whereIn('plan_id', $plans)
            ->where('type', 'Internal')
            ->pluck('people_id');

        // Get the 
        $InternalPeople = InternalPeople::where('company_id', $this->user->company_id)
            ->get();

        $this->InternalPeopeCount = InternalPeople::where('company_id', $this->user->company_id)->count();

        $startDate = Carbon::now()->startOfQuarter(); // Get the start of the current quarter
        $endDate = Carbon::now()->endOfQuarter();

        $this->InternalPeopleAdded = InternalPeople::where('company_id', $this->user->company_id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $this->topHirer = DB::table('people')->select('company_name', DB::raw('COUNT(*) as hirings'))->whereBetween('start_date', [$startDate, $endDate])
            ->when(!empty($this->interestedCompaniesarray), function($query){
                $query->WhereIn('company_id',$this->interestedCompaniesarray);
            })
            ->groupBy('company_name')
            ->orderBy('hirings', 'desc')
            ->limit(1)
            ->get();


        $joiningsQuery = DB::table('career_histories')
            ->select('past_company_id', DB::raw('COUNT(*) as joinings'))
            ->whereBetween('start_date', [$startDate, $endDate])
            ->groupBy('past_company_id');

        $outgoingsQuery = DB::table('career_histories')
            ->select('past_company_id', DB::raw('COUNT(*) as outgoings'))
            ->whereBetween('end_date', [$startDate, $endDate])
            ->groupBy('past_company_id');

        $this->topMovementCompanies = DB::table('companies')
            ->leftJoinSub($joiningsQuery, 'joinings', function ($join) {
                $join->on('companies.id', '=', 'joinings.past_company_id');
            })
            ->leftJoinSub($outgoingsQuery, 'outgoings', function ($join) {
                $join->on('companies.id', '=', 'outgoings.past_company_id');
            })
            ->select(
                'companies.id as company_id',
                'companies.name',
                DB::raw('COALESCE(joinings.joinings, 0) as joinings'),
                DB::raw('COALESCE(outgoings.outgoings, 0) as outgoings'),
                DB::raw('COALESCE(joinings.joinings, 0) + COALESCE(outgoings.outgoings, 0) as movements')
            )
            ->having('movements', '>', 0)
            ->when(!empty($this->interestedCompaniesarray), function($query){
                $query->WhereIn('id',$this->interestedCompaniesarray);
            })
            ->orderBy('movements', 'desc')
            ->limit(10)
            ->get();


        // dd($this->topMovementCompanies);

        $InternalPeople = $InternalPeople->map(function ($person) use ($successpeople) {
            $person->isSuccessPerson = $successpeople->contains($person->id) ? 'In Plans' : "Not In Plans";
            return $person;
        });

        $groupedInternalPeople = $InternalPeople->groupBy('isSuccessPerson')
            ->map(function ($group) {
                return count($group);
            });

        $this->externalPeopleCount = People::where('company_id', '!=', $this->user->company_id)
                                            ->when(!empty($this->interestedCompaniesarray), function($query){
                                                $query->WhereIn('company_id',$this->interestedCompaniesarray);
                                            })
                                            ->where('status', '!=', 'Submitted')
                                            ->where('status', '!=', 'Reported')
                                            ->count();

        $this->genderCount = DB::table('people')
            ->select('gender', DB::raw('COUNT(*) as gender_count'))
            ->where('company_id','!=',$this->user->company_id)
            ->when(!empty($this->interestedCompaniesarray), function($query){
                $query->WhereIn('company_id',$this->interestedCompaniesarray);
            })
            ->where('status', '!=', 'Submitted')
            ->where('status', '!=', 'Reported')
            ->groupBy('gender')
            ->get();


        // dd($this->genderCount);

        foreach ($groupedInternalPeople as $label => $count) {
            $this->candidatesLabel[] = $label;
            $this->candidatesData[] = $count;
        }

        // dd($this->pstatusData);


    }

    public function render()
    {

        $notifications = DB::table('notifications')->join('users', 'notifications.user_id', '=', 'users.id')
            ->select('notifications.*', 'users.name as user_name')
            ->where('user_id','=',$this->user->id)
            ->orderBy('created_at', 'desc')
            ->when(!empty($this->type), function ($query) {

                $query->where('notifications.type', $this->type);
            })
            ->get();

        $activePlans = SuccessionPlan::where('status', 'active')
            ->whereYear('created_at', Carbon::now()->year)
            ->orderBy('created_at', 'desc')
            ->select('Name', 'Description', 'created_at')
            ->get();

        return view('livewire.home-page', compact('activePlans', 'notifications'));
    }

    public function deleteNotification($id)
    {
        $dnotification = notifications::findOrfail($id);
        $dnotification->delete();
    }

    public function deleteAllNotification()
    {
        notifications::where('user_id', auth()->user()->id)->delete();
    }
}
