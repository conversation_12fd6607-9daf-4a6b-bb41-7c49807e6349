<?php
namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class JobNotifier extends Component
{
    public $jobCompleted = false;
    public $stopPolling = false; 
    public function checkJobStatus()
    {
        // if ($this->stopPolling) {
        //     return; // Stop execution if polling is disabled
        // }

        $userId = auth()->id();
        $jobExists = DB::table('job_queues_notification')->where('user_id', $userId)->first();
        //Log::info(print_r($jobExists, true));
        //Log::info("jobExists");
        if ($jobExists) {
            $this->jobCompleted = true;
            $this->stopPolling = true; // Stop further polling
        
                $this->dispatch('toast', 'success', 'Pipeline created successfully!');
                DB::table('job_queues_notification')->where('user_id', $userId)->delete();
        }
    }

    public function render()
    {
        return view('livewire.job-notifier');
    }
}


