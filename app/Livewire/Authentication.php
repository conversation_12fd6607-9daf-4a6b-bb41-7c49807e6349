<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\Mail\TwoFactorCode;
use Illuminate\Support\Facades\Log;


class Authentication extends Component
{
    public $email;
    public $password;
    public $code1;
    public $code2;
    public $code3;
    public $code4;
    public $step = 1;
    private $generatedCode;

    protected $rules = [
        1 => [
            'email' => 'required|email',
            'password' => 'required|min:6',
        ],
        2 => [
            'code1' => 'required|numeric',
            'code2' => 'required|numeric',
            'code3' => 'required|numeric',
            'code4' => 'required|numeric',
        ],
    ];

    public function submit()
    {
        $this->validate($this->rules[$this->step]);

        if ($this->step == 1) {
            if (Auth::validate(['email' => $this->email, 'password' => $this->password])) {
                // Generate a 4-digit code
                $this->generatedCode = rand(1000, 9999);
               $this->sendVerificationCode($this->generatedCode, $this->email); 
               $message = 'A 4 digit code has been sent to your email';
        
               if (env('APP_ENV') == "local") {
                   $message .= " code: {$this->generatedCode}";
               }    
               $this->dispatch('toast', 'info', $message);          
                // Proceed to step 2
                $this->step = 2;
                 $this->dispatch('addEventListenerOnOtp');
            } else {
                $this->addError('email', 'The provided credentials are incorrect.');
            }
        } else {
            // Verify the code
            $enteredCode = $this->code1 . $this->code2 . $this->code3 . $this->code4;
            $storedCode = session('two_factor_code');
            $expiresAt = session('two_factor_expires_at');

            if (Carbon::now()->greaterThan($expiresAt)) {
                $this->addError('code', 'The provided code has expired.');
            } elseif ($enteredCode == $storedCode) {
                // Successful login
                session()->forget('two_factor_code');
                session()->forget('two_factor_expires_at');

                if (Auth::attempt(['email' => $this->email, 'password' => $this->password])) {
                    $lastVisitedUrl = session('last_visited_url', 'home');

                    session()->forget('last_visited_url');
                    return redirect()->intended($lastVisitedUrl);
                   // return redirect('home');
                }else {
                    // Handle login failure (if needed)
                    $this->addError('email', 'Login attempt failed.');
                }
            } else {
                $this->addError('code', 'The provided code is incorrect.');
            }
        }
    }

    public function sendVerificationCode($generatedCode, $email)
    {
        $expirationTime = Carbon::now()->addMinutes(5);
        session(['two_factor_code' => $this->generatedCode, 'two_factor_expires_at' => $expirationTime]);

        // Send code to the user's email
        try {
            // Send code to the user's email
            Mail::to($this->email)->send(new TwoFactorCode($this->generatedCode, $email));
        } catch (\Exception $e) {
            Log::info($e);
            // Handle mail sending exception
        }


    }

    public function resend() {
        $this->generatedCode = rand(1000, 9999);
        $this->sendVerificationCode($this->generatedCode, $this->email);   
        $message = 'A 4 digit code has been sent to your email';
        
        if (env('APP_ENV') == "local") {
            $message .= " code: {$this->generatedCode}";
        }              
        $this->dispatch('toast', 'info', $message);          

    }


    public function render()
    {
        return view('livewire.authentication');
    }

    public function backToLogin() {
        $this->step = 1;
    }
}
