<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Illuminate\Support\Facades\Log;
use App\Models\Location;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use App\Models\Company;
use App\Models\People;
use App\Models\pipeline;
use App\Models\SuccessPeople;
use App\Models\PlanScores;
use App\Models\SuccessionPlan;
use App\Models\notifications;
use Carbon\Carbon;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;



class PeopleUpload extends Component
{
    use WithFileUploads;

    public $plan;
    public $csvFile;
    public $tempUrl;
    public $company;
    public $role;
    public $country;
    public $countries = [];
    public $peoplesList = [];
    public $showPeopleList = false;
    public $nextPageUrl;
    public $apiKey;

    public $searchData = [
        'companies'  => [],
        'roles'      => [],
        'forenames'  => [],
        'surnames'   => []
    ];

    protected $listeners = [
        'searchPeople',
        'addPeoples',
        'loadMorePeople'
    ];

    // Credentials for the API controller
    private $anthropicApiKey;
    private $modelName;
    // Variables for the AI controller
    private $prompt = '';
    private $system = ''; // Optional system parameter
    private $aiResponse; // Internal variable to store the response


    public function __construct()
    {
        ini_set('max_execution_time', '1200');
        $this->anthropicApiKey = config('ai.anthropic.api_key');
        $this->modelName = config('ai.anthropic.model');
        $this->apiKey = config('ai.nubela.api_key');
    }

    // Function for running an AI prompt
    public function getCompletion()
    {
     $payload = [
         'model' => $this->modelName,
         'max_tokens' => 256,
         'temperature' => 0.0,
         'messages' => [
             ['role' => 'user', 'content' => $this->prompt],
         ],
         'system' => $this->system
     ];

     try {
         // Log start time
         $startTime = microtime(true);
         Log::info('Starting API request to Anthropic');

         // Make the API request
         $response = Http::withHeaders([
             'x-api-key' => $this->anthropicApiKey,
             'anthropic-version' => '2023-06-01', // Correct version header
             'Content-Type' => 'application/json', // Correct Content-Type
         ])->post('https://api.anthropic.com/v1/messages', $payload);

         // Log end time
         $endTime = microtime(true);
         $duration = $endTime - $startTime;

         Log::info('API request completed', [
             'duration_seconds' => $duration,
             'status' => $response->status(),
         ]);

         $data = $response->json();
         Log::info('AI Response', ['aiResponse' => $data]);

         if ($response->successful()) {
            $this->aiResponse = $data['content'][0]['text'] ?? 'Not Applicable';
         } else {
             Log::error('Error in API response', ['error' => $data]);
             $this->aiResponse = "Not Applicable";

         }
        } catch (\Exception $e) {
            Log::error('Error in getCompletion', [
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString(),
            ]);
            $this->aiResponse = "Not Applicable";
        }
        return $this->aiResponse;
    }

    public function mount($plan)
    {
        $this->plan = $plan;
        $this->countries = Location::distinct()
            ->whereNotNull('country_code')
            ->get(['country_name', 'country_code'])
            ->map(function ($location) {
                return [
                    'value' => $location->country_code,
                    'label' => $location->country_name,
                ];
            })
            ->toArray();
    }

    public function render()
    {
        return view('livewire.people-upload');
    }

    public function uploadFile()
    {
        try {
            // Validate the file
            $this->validate([
                'csvFile' => 'required|file|max:10240', // max 10MB
            ]);

            // Load the file and count rows
            $path = $this->csvFile->getRealPath();

            $rowCount = Excel::toCollection(null, $path)
                ->first()
                ->count();

            if ($rowCount > 500) {
                $this->dispatch('toast', 'error', 'Rows cannot be greater then 500!');
                $this->csvFile = null;
                $this->dispatch('uploadComplete');
                return;
            }

            // Store the file temporarily
            $tempFilePath = $this->csvFile->store('temp');
            // $tempFilePath = Storage::url($tempPath);

            // Pass the file and credentials to the Python script
            $this->runPythonScript(storage_path('app/' . $tempFilePath));

            // Delete the temporary file
            Storage::delete($tempFilePath);

            $this->updatePlanScores($this->plan);

            $this->dispatch('customToast', 'success', 'File uploaded and data stored successfully.');
            $this->csvFile = null;
            $this->dispatch('uploadComplete');

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setErrorBag($e->validator->errors()); // Populate Livewire's $errors property
            $this->dispatch('uploadComplete');

        } catch (ProcessFailedException $e) {
            $this->dispatch('uploadComplete');

            // Log the error for debugging purposes
            Log::error('Python script error: ' . $e->getMessage());

            // Delete the temporary file
            Storage::delete($tempFilePath);

            // Set error message to display to the user
            $this->dispatch('customToast', 'error', 'An error occurred while processing the file. Please try again.');
        }
    }

    private function runPythonScript($filePath)
    {
        // Path to your Python script
        // $pythonScript = '/path/to/your/python/script.py';
        $pythonScript = base_path('script.py');
        // Log::info('File path: ' . $pythonScript);

        // Create a Process instance and execute the Python script
        $userId = auth()->user()->id;
        // Database credentials from .env
        $dbHost = env('DB_HOST');
        $dbPort = env('DB_PORT');
        $dbUsername = env('DB_USERNAME');
        $dbPassword = env('DB_PASSWORD');
        $dbName = env('DB_DATABASE');
        $openApiKey = env('OPENAI_API_KEY');
        $nubelaApiKey = env('NUBELA_API_KEY');

        $process = new Process([
            'python3',
            $pythonScript,
            $filePath,
            $dbHost,
            $dbPort,
            $dbUsername,
            $dbPassword,
            $dbName,
            $openApiKey,
            $nubelaApiKey,
            $userId,
            $this->plan->id,
            null
        ]);

        $process->setTimeout(timeout: 1200); // 20 minutes

        $process->run();

        // Check if the process ran successfully
        if (!$process->isSuccessful()) {
            throw new ProcessFailedException($process);
        }

        // Optionally, get output from the Python script
        $output = $process->getOutput();
        Log::info('Python script output: ' . $output);
    }

    public function searchPeople()
    {
        $this->showPeopleList = true;

        $companiesString = !empty($this->searchData['companies']) ? implode(' OR ', $this->searchData['companies']) : null;
        $roleString = !empty($this->searchData['roles']) ? implode(' OR ', $this->searchData['roles']) : null;
        $forenameString = !empty($this->searchData['forenames']) ? implode(' OR ', $this->searchData['forenames']) : null;
        $surnameString = !empty($this->searchData['surnames']) ? implode(' OR ', $this->searchData['surnames']) : null;

        $apiUrl = 'https://nubela.co/proxycurl/api/v2/search/person';

        try {
            // Make GET request for each company
            $response = Http::withToken($this->apiKey)
                ->get($apiUrl, array_filter([
                    'enrich_profiles' => 'enrich',
                    'current_company_name' => $companiesString,
                    'current_role_title' => $roleString,
                    'country' => $this->country,
                    'first_name'=> $forenameString,
                    'last_name'=> $surnameString,
                    'use_cache' => 'if-present',
                ]));

            if ($response->successful()) {
                $data = $response->json();

                // Merge the results into peoplesList
                if (isset($data['results'])) {
                    $this->peoplesList = $data['results'];
                    $this->insertPeoples($this->peoplesList);
                }

                // Handle pagination (if necessary)
                if (isset($data['next_page'])) {
                    $this->nextPageUrl = $data['next_page'];
                }
            } else {

                $this->peoplesList[] = [
                    'error' => 'API call failed for company: ' . $companiesString,
                    'status' => $response->status(),
                    'message' => $response->body(),
                ];
            }
        } catch (\Exception $e) {
            $this->peoplesList[] = ['error' => 'An error occurred: ' . $e->getMessage()];
        }
    }
    public function loadMorePeople()
    {
        if (!$this->nextPageUrl) {
            return;
        }

        $apiUrl = $this->nextPageUrl;

        try {
            // Make GET request to fetch the next page
            $response = Http::withToken($this->apiKey)->get($apiUrl);

            if ($response->successful()) {
                $data = $response->json();
                $this->insertPeoples($data['results'] ?? []);
                $this->peoplesList = array_merge(
                    $this->peoplesList ?? [],
                    $data['results'] ?? []
                );
                $this->nextPageUrl = $data['next_page'];
            } else {
                $this->nextPageUrl = null;
            }
        } catch (\Exception $e) {
            $this->nextPageUrl = null;
        }
    }

    public function insertPeoples(array $peoples)
    {
        $user = auth()->user();

        if (!empty($peoples)) {
            $linkedinUrls = array_column($peoples, 'linkedin_profile_url');
            $existingPeoples = People::whereIn('linkedinURL', $linkedinUrls)->get()->keyBy('linkedinURL');

            foreach ($this->peoplesList as $peopleData) {

                if (isset($existingPeoples[$peopleData['linkedin_profile_url']])) {
                    continue;
                }
                $matchingRecord = $peopleData['profile'];
                $currentExperience = $matchingRecord['experiences'][0];
                $startAndEndDate = $this->convertFirstExperienceDate($currentExperience);
                $startDate = $startAndEndDate['startDate'];
                $endDate = $startAndEndDate['endDate'];

                $startDate = $startDate ? Carbon::parse($startDate) : Carbon::parse("1900-01-01");
                $endDate = $endDate ? Carbon::parse($endDate) : Carbon::now();
                $tenure = $startDate->diffInYears($endDate);

                $company = Company::where(['name' => $currentExperience['company']])->first();
                if (empty($company)) {
                    $company = Company::create([
                        'name' => $currentExperience['company']
                    ]);
                }

                $skills = null;

                if (isset($matchingRecord['skills']) && count($matchingRecord['skills']) > 0) {
                    $skillsArr = $matchingRecord['skills'];
                    $skills = implode("\n", $skillsArr);
                }
                $languages = null;
                if (isset($matchingRecord['languages']) && count($matchingRecord['languages']) > 0) {
                    $languagesArr = $matchingRecord['languages'];
                    $languages = implode("\n", $languagesArr);
                }

                // Get the gender
                $this->system = "You are an expert gender studies and know about which forenames are associated to which genders. Your answers can only say Male or Female. If you are completely not sure then just say Not Applicable only";
                $this->prompt = "What is the gender of ".$matchingRecord['first_name'];
                $gender = $this->getCompletion();
                if (strpos($gender, "Not Applicable") !== false) {
                    $gender = "Not Applicable";
                }

                # Based on the information what is the Potential Diversity
                $this->system  = "You are an expert ethnicity studies and know about which forenames are associated to which countries. Your answer can only be the adjectival and nothing else. If you can't tell say Not Applicable only";
                $this->prompt  = "What is the origin of this name ".$matchingRecord['last_name'];
                $diverse = $this->getCompletion();
                if (strpos($diverse, "Not Applicable") !== false) {
                    $diverse = "Not Applicable";
                }

                $careerhis = $this->mapExperiencesToString($matchingRecord['experiences']);
                //dd($company->name);
                // Get the function which they may work in
                $this->system = "EOT
                        You are an experienced consultant that has worked across all industries and know about which roles work in what function of a company.
                        Your answer must only be the name of the function and nothing else. If you're not sure then say Not Applicable only. Only give one function.

                        Some additional information:
                        A Chief Investment Officer's function is Investment Management only.
                        A Chief Operations Officer's function is Operations only.
                        A Chief Risk and Compliance Officer is 'Risk and Compliance' only.
                        A Chief Executive Officer's function is Executive Management.
                        If the word 'Research' is in the person's role the function is Research.
                        If the word 'Quantitative' is in the person's role the function is Quant.
                        If the word 'Client Partner' is in the person's role the function is 'Sales'only.
                        If the word 'Client Lead' is in the person's role the function is 'Sales' only.
                        A Head of Pricing's function is Pricing.
                        If the word 'coverage' is in the person's role the function is 'Coverage' only.
                        If 'M&A' is in the person role then the function is 'M & A' only.
                        A Head of Mergers and Acquisitions function is 'M & A' only.
                        A Head of Asset Management function is 'Asset Management' Only.
                        If the role is related with Tax then the function is 'Finance' Only.
                        A Independent Non-Executive Director's function's is Board.
                        A Board Member function's is Board.
                        A Non Executive Director function's is Board.
                        If the role is associated with Tax then the function is 'Finance' only.
                        If you see the word 'ESG' in role the function is 'ESG' only.
                        If 'Head of Product' is in the role the function is 'Product' only.
                        IF 'Head of Data' is in the role the function is 'Data' only.
                        If the role is related with Procurement then the function is 'Procurement' only this includes roles like 'Vendor Management' and 'Supply Chain'.

                        If you see the function is 'IT' call it 'Technology'.
                        'Corporate Banking' is a division for the function 'Coverage' only.
                        If the company is a bank then for 'Sales function call it 'Coverage' only.
                        A 'Head of Product and Proposition' works in function is 'Product & Proposition' only";
                $this->prompt = "Based on your understanding the individuals career history:".$careerhis." their current role: ".$currentExperience['title']." the company: ".$company->name."Tell me the name of the function the individual works in.";
                $pfunction = $this->getCompletion();
                //dd($pfunction);

                // Generate a summary for them
                $this->system  = "You are a Talent Aquisition expert with strong skills in summarising people's career histories. Your response should only be 200 words long. If there is not information just keep a simple introduction about the person";
                $this->prompt  = "Write a summary for ".$matchingRecord['first_name']." here is there career history: ".$careerhis.".";
                $summary = $this->getCompletion();

                $peopleData = [
                    'forename' => $matchingRecord['first_name'] ?? null,
                    'surname' => $matchingRecord['last_name'] ?? null,
                    'gender' => $gender ?? "Not Applicable",
                    'country' => $matchingRecord['country_full_name'] ?? "Not Applicable",
                    'city' => $matchingRecord['city'] ?? "Not Applicable",
                    'linkedinURL' => $peopleData['linkedin_profile_url'] ?? null,
                    'latest_role' => $currentExperience['title'] ?? null,
                    'company_id' => $company->id ?? null,
                    'company_name' => $company->name ?? null,
                    'start_date' => $startAndEndDate['startDate'] ?? "1900-01-01",
                    'end_date' => $startAndEndDate['endDate'] ?? null,
                    'tenure' => $tenure ?? null,
                    'career_history' => $this->mapExperiencesToString($matchingRecord['experiences']),
                    'educational_history' =>  $this->mapEducationsToString($matchingRecord['education']),
                    'skills' => $skills,
                    'languages' => $languages,
                    'function'  => $pfunction,
                    'status' => "Submitted" ?? null,
                    'exco' => "Non Exco" ?? null,
                    'diverse' => $diverse ?? 'Not Applicable',
                    'summary' => $summary,
                    'user_id' => $user->id ?? null,

                ];
                $people = People::create($peopleData);
                $this->insertExperiencesInDb($matchingRecord['experiences'], $people->id);
                $this->insertSkillsInDb($matchingRecord['skills'], $people->id);
            }
        }
    }


    public function addPeoples($selectedData)
    {
        try {
            DB::beginTransaction();
            $user = auth()->user();
            $successionPlan = $this->plan;
            if (empty($selectedData)) {
                return $this->dispatch('toast', 'info', "Please select peoples!");
            }

            $linkedinUrls = $selectedData;
            $existingPeoples = People::whereIn('linkedinURL', $linkedinUrls)->get()->keyBy('linkedinURL');

            if ($user->id !== $successionPlan->user_id) {
                $status = 'Proposed';
            } else {
                $status = 'Approved';
            }


            foreach ($linkedinUrls as $linkedInUrl) {

                if (isset($existingPeoples[$linkedInUrl])) {
                    $people = $existingPeoples[$linkedInUrl];

                    $pipeline = pipeline::where(['people_id' => $people->id, 'plan_id' => $successionPlan->id])->first();

                    if (!empty($pipeline))
                        continue;

                    $inputArray = [
                        'role'     => $people->latest_role,
                        'location' => $people->location,
                        'tenure'   => $people->tenure,
                        'company'  => $people->company_name,
                    ];

                    // Create a formatted message for GPT-3
                    $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
                    foreach ($inputArray as $key => $value) {
                        $message .= "$key: \"$value\"\n";
                    }

                    // Call GPT-3 to generate the headline
                    $response = OpenAI::chat()->create([
                        'model' => 'gpt-3.5-turbo',
                        'messages' => [['role' => 'system', 'content' => $message]],
                    ]);

                    $generatedHeadline = $response->choices[0]->message->content;

                    $pipeline = pipeline::create([
                        'plan_id'            => $successionPlan->id,
                        'user_id'            => $user->id,
                        'people_id'          => $people->id,
                        'headline'           => $generatedHeadline,
                        'first_name'         => $people->forename,
                        'last_name'          => $people->surname,
                        'middle_name'        => $people->middle_name,
                        'other_name'         => $people->other_name,
                        'gender'             => $people->gender,
                        'diverse'            => $people->diverse,
                        'location'           => $people->location,
                        'summary'            => $people->summary,
                        'country'            => $people->country,
                        'city'               => $people->city,
                        'linkedinURL'        => $people->linkedinURL,
                        'latest_role'        => $people->latest_role,
                        'company_id'         => $people->company_id,
                        'company_name'       => $people->company_name,
                        'start_date'         => $people->start_date ?? "1990-01-01",
                        'end_date'           => $people->end_date,
                        'tenure'             => $people->tenure,
                        'function'           => $people->function,
                        'division'           => $people->division,
                        'seniority'          => "NA",
                        'exco'               => "Non Exco",
                        'career_history'     => "NA",
                        'educational_history' => "NA",
                        'skills'             => "NA",
                        'languages'          => "NA",
                        'skills_match'       => 0,
                        'education_match'    => 0,
                        'location_match'     => 1,
                        'role_match'         => 1,
                        'gender_match'       => 1,
                        'tenure_match'       => 1,
                        'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                        'status'             => $status,
                        'people_type'        => "External-User"
                    ]);

                    /*
                    $SuccessPeople = SuccessPeople::create([
                        'pipeline_id'        => $pipeline->id,
                        'plan_id'            => $successionPlan->id,
                        'user_id'            => $user->id,
                        'people_id'          => $people->id,
                        'headline'           => $generatedHeadline,
                        'first_name'         => $people->forename,
                        'last_name'          => $people->surname,
                        'middle_name'        => $people->middle_name,
                        'other_name'         => $people->other_name,
                        'gender'             => $people->gender,
                        'diverse'            => $people->diverse,
                        'location'           => $people->location,
                        'summary'            => $people->summary,
                        'country'            => $people->country,
                        'city'               => $people->city,
                        'linkedinURL'        => $people->linkedinURL,
                        'latest_role'        => $people->latest_role,
                        'company_id'         => $people->company_id,
                        'company_name'       => $people->company_name,
                        'start_date'         => $people->start_date ?? "1900-01-01",
                        'end_date'           => $people->end_date,
                        'tenure'             => $people->tenure,
                        'function'           => $people->function,
                        'division'           => $people->division,
                        'seniority'          => "NA",
                        'exco'               => "Non Exco",
                        'career_history'     => "NA",
                        'educational_history' => "NA",
                        'skills'             => "NA",
                        'languages'          => "NA",
                        'skills_match'       => 0,
                        'education_match'    => 0,
                        'location_match'     => 1,
                        'role_match'         => 1,
                        'gender_match'       => 1,
                        'tenure_match'       => 1,
                        'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                        'type'               => "External",
                        'status'             => $status,
                        'notes'              => "Enter notes here",
                        'recruit'            => 1,
                    ]);

                    if ($SuccessPeople->status === 'Proposed') {
                        Notifications::create([
                            'type'              => "External-Proposed",
                            'plan_id'           => $SuccessPeople->plan_id,
                            'people_id'         => $SuccessPeople->id,
                            'entity_name'       => 'Proposed Candidate',
                            'description'       => $user->name . ' has proposed ' . $SuccessPeople->forename . ' ' . $SuccessPeople->latest_role . ' from ' . $SuccessPeople->company_name . ' to your plan ',
                            'user_id'           => $user->id,
                            'user_company'      => $user->company_id
                        ]);
                    }
                    */


                    //------------------- Get the new Gender Diversity Score -----------------//
                    $this->updatePlanScores($successionPlan);

                    // $femaleRatio = DB::table('success_people')
                    //     ->select('plan_id', DB::raw('(SUM(gender = "female")/Count(*))*100 as female_ratio'))
                    //     ->groupBy('plan_id')
                    //     ->where('plan_id', $successionPlan->id)
                    //     ->first();

                    // PlanScores::updateOrInsert(
                    //     ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Female-Ratio'],
                    //     ['score' => $femaleRatio->female_ratio]
                    // );


                    // $maleRatio = DB::table('success_people')
                    //     ->select('plan_id', DB::raw('(SUM(gender = "male")/Count(*))*100 as male_ratio'))
                    //     ->groupBy('plan_id')
                    //     ->first();


                    // PlanScores::updateOrInsert(
                    //     ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Male-Ratio'],
                    //     ['score' => $maleRatio->male_ratio]
                    // );

                    // $InternalRatio = DB::table('success_people')
                    //     ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
                    //     ->groupBy('plan_id')
                    //     ->where('plan_id', $successionPlan->id)
                    //     ->first();

                    // PlanScores::updateOrInsert(
                    //     ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Internal-External Ratio'],
                    //     ['score' => $InternalRatio->internal_ratio]
                    // );

                    // $averageskillscore = DB::table('success_people')
                    //     ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
                    //     ->groupBy('plan_id')
                    //     ->where('plan_id', $successionPlan->id)
                    //     ->first();

                    // PlanScores::updateOrInsert(
                    //     ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Skill Score'],
                    //     ['score' => $averageskillscore->average_skill_score]
                    // );

                    // $averagetenurescore = DB::table('success_people')
                    //     ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
                    //     ->groupBy('plan_id')
                    //     ->where('plan_id', $successionPlan->id)
                    //     ->first();

                    // PlanScores::updateOrInsert(
                    //     ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Tenure Score'],
                    //     ['score' => $averagetenurescore->average_tenure_score]
                    // );
                }
            }
            $this->peoplesList = array_filter($this->peoplesList, function ($person) use ($linkedinUrls) {
                return !in_array($person['linkedin_profile_url'], $linkedinUrls);
            });

            $this->peoplesList = array_values($this->peoplesList);
            $this->dispatch('updateSelectedProp');
            DB::commit();
            $this->dispatch('toast', 'info', 'People added to plan successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            $this->dispatch('error', 'info', $e->getMessage());
        }
    }

    public function updatePlanScores($successionPlan){
        //------------------- Get the new Gender Diversity Score -----------------//
        $femaleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "female")/Count(*))*100 as female_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $successionPlan->id)
            ->first();

        // Check if $femaleRatio is null before accessing its property
        $score = $femaleRatio && isset($femaleRatio->female_ratio) ? $femaleRatio->female_ratio : 0;

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Female-Ratio'],
            ['score' => $score]
        );


        $maleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "male")/Count(*))*100 as male_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $successionPlan->id)
            ->first();


        // Check if $maleRatio is null before accessing its property
        $score = $maleRatio && isset($maleRatio->male_ratio) ? $maleRatio->male_ratio : 0;

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Male-Ratio'],
            ['score' => $score]
        );

        $InternalRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $successionPlan->id)
            ->first();


        $score = $InternalRatio && isset($InternalRatio->internal_ratio) ? $InternalRatio->internal_ratio : 0;

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Internal-External Ratio'],
            ['score' => $score]
        );

        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $successionPlan->id)
            ->first();

        $score = $averageskillscore && isset($averageskillscore->average_skill_score) ? $averageskillscore->average_skill_score : 0;

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Skill Score'],
            ['score' => $score]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $successionPlan->id)
            ->first();

        $score = $averagetenurescore && isset($averagetenurescore->average_tenure_score) ? $averagetenurescore->average_tenure_score : 0;

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $successionPlan->id, 'metric_name' => 'Tenure Score'],
            ['score' => $score]
        );
    }

    public function mapEducationsToString($educations)
    {
        $educationsArr = [];

        foreach ($educations as $education) {
            $educationString = '';

            // Append school name if exists
            if (isset($education['school'])) {
                $educationString .= $education['school'];
            }

            // Append field of study if exists
            if (isset($education['field_of_study'])) {
                $educationString .= ', ' . $education['field_of_study'];
            }

            // Append degree name if exists
            if (isset($education['degree_name'])) {
                $educationString .= ' - ' . $education['degree_name'];
            }

            // Append start year if exists
            if (isset($education['starts_at']['year'])) {
                $educationString .= ' · ' . $education['starts_at']['year'];
            }

            // Append end year if exists
            if (isset($education['ends_at']['year'])) {
                $educationString .= ' - ' . $education['ends_at']['year'];
            }

            // Add the formatted string to the result array if not empty
            if (!empty($educationString)) {
                $educationsArr[] = $educationString;
            }
        }

        // Return the formatted string or null if the array is empty
        return !empty($educationsArr) ? implode("\n", $educationsArr) : null;
    }

    public function monthToString($month)
    {
        $months = [
            1 => 'Jan',
            2 => 'Feb',
            3 => 'Mar',
            4 => 'Apr',
            5 => 'May',
            6 => 'Jun',
            7 => 'Jul',
            8 => 'Aug',
            9 => 'Sep',
            10 => 'Oct',
            11 => 'Nov',
            12 => 'Dec'
        ];

        return $months[$month] ?? '';
    }

    public function mapExperiencesToString($experiences)
    {
        $experiencesArr = [];

        foreach ($experiences as $experience) {
            $experienceString = '';

            // Check if start date exists and has valid month and year
            if (isset($experience['starts_at']) && isset($experience['starts_at']['month']) && isset($experience['starts_at']['year'])) {
                $experienceString .= $this->monthToString($experience['starts_at']['month']) . '-' . $experience['starts_at']['year'];
            }

            // Check if end date exists and has valid month and year
            if (isset($experience['ends_at']) && isset($experience['ends_at']['month']) && isset($experience['ends_at']['year'])) {
                $experienceString .= ' | ' . $this->monthToString($experience['ends_at']['month']) . '-' . $experience['ends_at']['year'];
            } else {
                $experienceString .= ' | Present';
            }

            // Append title if exists
            if (isset($experience['title'])) {
                $experienceString .= ': ' . $experience['title'];
            }

            // Append company name if exists
            if (isset($experience['company'])) {
                $experienceString .= ' at ' . $experience['company'];
            }

            // Add the formatted string to the result array if it is not empty
            if (!empty($experienceString)) {
                $experiencesArr[] = $experienceString;
            }
        }

        // Return the formatted string or null if the array is empty
        return !empty($experiencesArr) ? implode("\n", $experiencesArr) : null;
    }

    public function convertFirstExperienceDate($currentExperience)
    {
        $startDate = null;
        if (isset($currentExperience['starts_at']) && !empty($currentExperience['starts_at'])) {
            // Construct the start date for the first index only
            $startDate = sprintf(
                '%04d-%02d-%02d', // Format as YYYY-MM-DD
                $currentExperience['starts_at']['year'],
                $currentExperience['starts_at']['month'],
                $currentExperience['starts_at']['day']
            );
        }
        $endDate = null;
        if (isset($currentExperience['ends_at']) && !empty($currentExperience['ends_at'])) {
            // Construct the start date for the first index only
            $endDate = sprintf(
                '%04d-%02d-%02d', // Format as YYYY-MM-DD
                $currentExperience['ends_at']['year'],
                $currentExperience['ends_at']['month'],
                $currentExperience['ends_at']['day']
            );
        }

        return [
            "startDate" => $startDate,
            "endDate"   => $endDate
        ];
    }

    public function insertSkillsInDb(array $skills = [], int $peopleId = null)
    {
        if (empty($skills) || !$peopleId) {
            return false;
        }

        foreach ($skills as $skill) {
            // Insert all skills without checking for existence
            DB::table('skills')->insert([
                'people_id' => $peopleId,
                'skill_name' => $skill,
                'skill_type' => 'Professional',
            ]);
        }

        return true;
    }


    function destructDateAndTenure(array $experience)
    {
        $startDate = null;
        $endDate = null;
        $tenure = 0;

        if (isset($experience['starts_at']) && !empty($experience['starts_at'])) {
            $startDateArray = $experience['starts_at'];
            $startDate = sprintf(
                '%04d-%02d-%02d',
                $startDateArray['year'] ?? 0,
                $startDateArray['month'] ?? 0,
                $startDateArray['day'] ?? 0
            );
        }

        if (isset($experience['ends_at']) && !empty($experience['ends_at'])) {
            $endDateArray = $experience['ends_at'];
            $endDate = sprintf(
                '%04d-%02d-%02d',
                $endDateArray['year'] ?? 0,
                $endDateArray['month'] ?? 0,
                $endDateArray['day'] ?? 0
            );
        }

        // Calculate tenure
        if ($startDate) {
            $start_date = Carbon::parse($startDate);

            $end_date = $endDate ? Carbon::parse($endDate) : Carbon::now();

            $tenure_days = $start_date->diffInDays($end_date);
            $tenure = round($tenure_days / 365.25, 1); // Dividing by 365.25 to include leap years
        }

        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'tenure' => $tenure,
        ];
    }

    function getOrCreateCompany(string $companyName)
    {
        // Check if the company exists in the database
        $company = Company::where('name', $companyName)->first();

        if (empty($company)) {
            // Create a new company record
            $company = Company::create([
                'name' => $companyName,
            ]);
        }

        return $company;
    }

    function insertExperiencesInDb(array $experiences = [], int $peopleId = null)
    {
        if (empty($experiences) || !$peopleId) {
            return false;
        }

        foreach ($experiences as $experience) {
            $dateAndTenure = $this->destructDateAndTenure($experience); // Custom helper function to destruct dates and tenure
            $companyId = null;
            $startDate = $dateAndTenure['start_date'] ?? null;
            $endDate = $dateAndTenure['end_date'] ?? null;
            $tenure = $dateAndTenure['tenure'] ?? null;
            $role = $experience['title'] ?? null;

            // Use helper function to get or create a company
            $companyDetail = $this->getOrCreateCompany($experience['company'] ?? null);
            if ($companyDetail) {
                $companyId = $companyDetail['id'];
            }

            // Skip iteration if required values are missing
            if (!$peopleId || !$role || !$companyId || !$startDate) {
                continue;
            }

            // Insert experience without checking for existence
            DB::table('career_histories')->insert([
                'people_id' => $peopleId,
                'role' => $role,
                'past_company_id' => $companyId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'tenure' => $tenure,
            ]);
        }

        return true;
    }

    public function downloadCSV()
    {
        $headerRow = ['linkedinURL'];

        // Dummy data
        $dataRows = [
            [
                'linkedinURL' => 'https://www.linkedin.com/in/xxxx-xxxx-xxxx',
            ],
        ];

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="csv_template.csv"',
        ];

        return Response::stream(function () use ($headerRow, $dataRows) {
            $handle = fopen('php://output', 'w');
            fputcsv($handle, $headerRow);
            foreach ($dataRows as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
        }, 200, $headers);
    }
}
