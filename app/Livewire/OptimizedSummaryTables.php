<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use App\Models\Location;
use App\Models\User;
use App\Models\SuccessPeople;
use App\Models\InternalPeople;
use App\Models\CareerHistories;
use App\Models\Company;
use App\Models\internal_career_histories;
use App\Models\SuccessRequirements;
use App\Models\SuccessSkills;
use App\Models\People;
use App\Models\Skills;
use App\Models\Job;
use App\Models\JobRequirement;
use App\Models\Account;
use App\Models\pipeline;
use App\Models\notifications;
use App\Models\PlanScores;
use App\Models\UserNotes;
use App\Models\Recruitment;
use App\Models\RecruitmentPipeline;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\JobPeople;
use Illuminate\Support\Facades\Auth;
use App\Models\RecruitmentStage;
use Illuminate\Support\Facades\Mail;
use App\Mail\ShareWithNotification;
use Illuminate\Support\Facades\Cache;
use Livewire\Attributes\Lazy;

class OptimizedSummaryTables extends Component
{
    // Performance optimization properties
    public $isChartsLoaded = false;
    public $isCandidatesLoaded = false;
    public $isInitialized = false;
    public $modalLoading = false;
    public $showCandidateModal = false;
    public $editPlanPopup = false;
    public $currentCandidateData = null;
    public $activeRequirementFilters = [];
    
    // Keep existing listeners and properties
    protected $listeners = [
        'modalClosed',
        'removeSuccessPerson',
        'approveSuccessPerson',
        'deleteUserNote',
        'viewDetailModalClosed', 
        'recruitModalClosed',
        'SaveProject',
        'loadChartData',
        'loadCandidateData'
    ];

    public $plan;
    public $user;
    
    // Loading states
    public $isChartsLoaded = false;
    public $isCandidatesLoaded = false;
    public $isInitialized = false;
    public $showCandidateModal = false;
    public $modalLoading = false;
    public $selectedCandidate = null;

    // Essential data loaded immediately
    public $planDetails;
    public $requirements;
    public $successpeople;
    public $pipelinepeople;
    
    // Lazy-loaded data
    public $genderLabels = [];
    public $genderData = [];
    public $typeLabels = [];
    public $typeData = [];
    public $companyLabels = [];
    public $companyData = [];
    public $functionLabels = [];
    public $functionData = [];
    
    // Pagination for large datasets
    public $candidatesPerPage = 20;
    public $currentPage = 1;
    public $totalCandidates = 0;

    // Keep all existing properties that are needed immediately
    public $jobs;
    public $selectedJobs = [];
    public $showForm = false;
    public $editPlanPopup = false;
    
    // Form properties
    public $name = '';
    public $descriptions = '';
    public $status = 'Draft';
    public $selectedColleagues = [];
    public $selectedCountries = [];
    public $selectedCompanies = [];
    public $newSkillData = [
        'qualifications'  => [],
        'skills'          => [],
        'targetRoles'     => [],
        'stepUpCandidate' => [],
        'keyword'         => []
    ];

    public function mount()
    {
        $this->user = auth()->user();
        
        // Load only essential data immediately
        $this->loadEssentialData();
        
        // Mark as initialized
        $this->isInitialized = true;
    }

    protected function loadEssentialData()
    {
        // Cache key for this plan's essential data
        $cacheKey = "plan_essential_{$this->plan->id}_{$this->user->id}";
        
        $essentialData = Cache::remember($cacheKey, 300, function () { // 5 minutes cache
            return [
                'plan_details' => SuccessionPlan::where('id', $this->plan->id)->first(),
                'requirements' => SuccessRequirements::where('plan_id', $this->plan->id)->get(),
                'success_count' => SuccessPeople::where('plan_id', $this->plan->id)->count(),
                'pipeline_count' => pipeline::where('plan_id', $this->plan->id)->count()
            ];
        });

        $this->planDetails = $essentialData['plan_details'];
        $this->requirements = $essentialData['requirements'];
        $this->totalCandidates = $essentialData['success_count'] + $essentialData['pipeline_count'];

        // Load minimal candidate data for initial view
        $this->loadInitialCandidates();
    }

    protected function loadInitialCandidates()
    {
        // Load first batch of success people (top performers)
        $this->successpeople = SuccessPeople::where('plan_id', $this->plan->id)
            ->select(['id', 'first_name', 'last_name', 'latest_role', 'company_name', 'total_score', 'status'])
            ->orderBy('total_score', 'desc')
            ->take(10)
            ->get();

        // Load first batch of pipeline people
        $successArray = $this->successpeople->pluck('people_id')->toArray();
        $this->pipelinepeople = DB::table('pipelines')
            ->where('plan_id', $this->plan->id)
            ->whereNotIn('people_id', $successArray)
            ->select(['id', 'people_id', 'first_name', 'last_name', 'latest_role', 'company_name', 'total_score'])
            ->take(10)
            ->get();
    }

    public function loadChartData()
    {
        if ($this->isChartsLoaded) {
            return;
        }

        $cacheKey = "plan_charts_{$this->plan->id}";
        
        $chartData = Cache::remember($cacheKey, 600, function () { // 10 minutes cache
            $data = [];

            // Gender distribution
            $genderCounts = DB::table('success_people')
                ->select('gender', DB::raw('COUNT(*) as total_people'))
                ->where('plan_id', $this->plan->id)
                ->groupBy('gender')
                ->get();

            foreach ($genderCounts as $item) {
                $colorCode = $item->gender == "Male" ? "#3B82F6" : "#FFA347";
                $data['gender']['labels'][] = $item->gender;
                $data['gender']['data'][] = [
                    "x" => $item->gender,
                    "y" => $item->total_people,
                    "fillColor" => $colorCode
                ];
            }

            // Company distribution
            $companyCounts = DB::table('success_people')
                ->select('company_name', DB::raw('COUNT(*) as total_company'))
                ->where('plan_id', $this->plan->id)
                ->whereNotNull('company_name')
                ->where('company_name', '!=', '')
                ->groupBy('company_name')
                ->get();

            foreach ($companyCounts as $item) {
                $data['company']['labels'][] = $item->company_name;
                $data['company']['data'][] = $item->total_company;
            }

            // Function distribution
            $functionCounts = DB::table('success_people')
                ->select('function', DB::raw('COUNT(*) as total_people'))
                ->where('plan_id', $this->plan->id)
                ->whereNotNull('function')
                ->where('function', '!=', '')
                ->groupBy('function')
                ->orderBy('total_people', 'desc')
                ->get();

            foreach ($functionCounts as $item) {
                $data['function']['labels'][] = $item->function;
                $data['function']['data'][] = $item->total_people;
            }

            // Type distribution
            $typeCounts = DB::table('success_people')
                ->select('type', DB::raw('COUNT(*) as total_people'))
                ->where('plan_id', $this->plan->id)
                ->groupBy('type')
                ->get();

            foreach ($typeCounts as $item) {
                $data['type']['labels'][] = $item->type;
                $data['type']['data'][] = $item->total_people;
            }

            return $data;
        });

        // Assign chart data
        $this->genderLabels = $chartData['gender']['labels'] ?? [];
        $this->genderData = $chartData['gender']['data'] ?? [];
        $this->companyLabels = $chartData['company']['labels'] ?? [];
        $this->companyData = $chartData['company']['data'] ?? [];
        $this->functionLabels = $chartData['function']['labels'] ?? [];
        $this->functionData = $chartData['function']['data'] ?? [];
        $this->typeLabels = $chartData['type']['labels'] ?? [];
        $this->typeData = $chartData['type']['data'] ?? [];

        $this->isChartsLoaded = true;
    }

    public function loadCandidateData($page = 1)
    {
        $offset = ($page - 1) * $this->candidatesPerPage;
        
        // Load more detailed candidate data on demand
        $successpeople = SuccessPeople::where('plan_id', $this->plan->id)
            ->orderBy('total_score', 'desc')
            ->offset($offset)
            ->limit($this->candidatesPerPage)
            ->get();

        // Dispatch event to update frontend
        $this->dispatch('candidatesLoaded', [
            'candidates' => $successpeople->toArray(),
            'page' => $page,
            'hasMore' => $successpeople->count() === $this->candidatesPerPage
        ]);

        $this->isCandidatesLoaded = true;
    }

    public function showCandidateProfile($candidateId)
    {
        $this->modalLoading = true;
        $this->selectedCandidate = $candidateId;
        $this->showCandidateModal = true;

        // Load candidate data asynchronously
        $this->dispatch('loadCandidateProfile', ['candidateId' => $candidateId]);
    }

    public function loadCandidateProfile($candidateId)
    {
        $cacheKey = "candidate_profile_{$candidateId}";
        
        $candidateData = Cache::remember($cacheKey, 300, function () use ($candidateId) {
            $candidate = SuccessPeople::with(['skills', 'careerHistory'])
                ->where('id', $candidateId)
                ->first();

            if (!$candidate) {
                return null;
            }

            // Load additional data
            $skills = Skills::where('people_id', $candidate->people_id)->get();
            $careerHistory = CareerHistories::where('people_id', $candidate->people_id)
                ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
                ->select('career_histories.*', 'companies.name as company_name')
                ->orderBy('career_histories.start_date', 'desc')
                ->get();

            return [
                'candidate' => $candidate,
                'skills' => $skills,
                'careerHistory' => $careerHistory
            ];
        });

        $this->modalLoading = false;
        
        // Dispatch event with candidate data
        $this->dispatch('candidateProfileLoaded', [
            'candidate' => $candidateData['candidate'],
            'skills' => $candidateData['skills'],
            'careerHistory' => $candidateData['careerHistory']
        ]);
    }

    public function closeCandidateModal()
    {
        $this->showCandidateModal = false;
        $this->selectedCandidate = null;
        $this->modalLoading = false;
    }

    // Keep all existing methods but optimize them
    public function render()
    {
        // Minimal data for initial render
        $user = $this->user;
        $plan = $this->plan;
        $requirements = $this->requirements;
        $plandetails = [$this->planDetails];

        // Only load additional data if needed
        $shared_users = collect([]);
        if ($this->planDetails && $this->planDetails->shared_with) {
            $decoded_users = json_decode($this->planDetails->shared_with, true);
            if (is_array($decoded_users) && !empty($decoded_users)) {
                $shared_users = User::whereIn('id', $decoded_users)->pluck('name');
            }
        }

        $colleagues = [];
        $internalPeoplesCount = 0;
        $recruitmentProjectId = '';

        return view('livewire.optimized-summary-tables', compact(
            'requirements', 
            'plandetails', 
            'plan', 
            'shared_users', 
            'colleagues', 
            'internalPeoplesCount', 
            'recruitmentProjectId'
        ));
    }

    /**
     * Load chart data lazily when requested
     */
    public function loadChartData()
    {
        if ($this->isChartsLoaded) {
            return;
        }

        $cacheKey = "chart_data_{$this->plan->id}";
        $chartData = Cache::remember($cacheKey, 600, function () {
            $data = [
                'gender' => ['labels' => [], 'data' => []],
                'company' => ['labels' => [], 'data' => []],
                'function' => ['labels' => [], 'data' => []],
                'type' => ['labels' => [], 'data' => []]
            ];

            // Gender distribution
            $genderCounts = DB::table('success_people')
                ->select('gender', DB::raw('COUNT(*) as total_gender'))
                ->where('plan_id', $this->plan->id)
                ->whereNotNull('gender')
                ->groupBy('gender')
                ->get();

            foreach ($genderCounts as $item) {
                $data['gender']['labels'][] = $item->gender;
                $data['gender']['data'][] = $item->total_gender;
            }

            // Company distribution
            $companyCounts = DB::table('success_people')
                ->select('company_name', DB::raw('COUNT(*) as total_company'))
                ->where('plan_id', $this->plan->id)
                ->whereNotNull('company_name')
                ->where('company_name', '!=', '')
                ->groupBy('company_name')
                ->get();

            foreach ($companyCounts as $item) {
                $data['company']['labels'][] = $item->company_name;
                $data['company']['data'][] = $item->total_company;
            }

            // Function distribution
            $functionCounts = DB::table('success_people')
                ->select('function', DB::raw('COUNT(*) as total_people'))
                ->where('plan_id', $this->plan->id)
                ->whereNotNull('function')
                ->where('function', '!=', '')
                ->groupBy('function')
                ->orderBy('total_people', 'desc')
                ->get();

            foreach ($functionCounts as $item) {
                $data['function']['labels'][] = $item->function;
                $data['function']['data'][] = $item->total_people;
            }

            // Type distribution
            $typeCounts = DB::table('success_people')
                ->select('type', DB::raw('COUNT(*) as total_people'))
                ->where('plan_id', $this->plan->id)
                ->groupBy('type')
                ->get();

            foreach ($typeCounts as $item) {
                $data['type']['labels'][] = $item->type;
                $data['type']['data'][] = $item->total_people;
            }

            return $data;
        });

        // Assign chart data
        $this->genderLabels = $chartData['gender']['labels'] ?? [];
        $this->genderData = $chartData['gender']['data'] ?? [];
        $this->companyLabels = $chartData['company']['labels'] ?? [];
        $this->companyData = $chartData['company']['data'] ?? [];
        $this->functionLabels = $chartData['function']['labels'] ?? [];
        $this->functionData = $chartData['function']['data'] ?? [];
        $this->typeLabels = $chartData['type']['labels'] ?? [];
        $this->typeData = $chartData['type']['data'] ?? [];

        $this->isChartsLoaded = true;

        // Emit event to trigger chart rendering
        $this->dispatch('chartsLoaded', $chartData);
    }

    /**
     * Load candidate data lazily
     */
    public function loadCandidateData()
    {
        if ($this->isCandidatesLoaded) {
            return;
        }

        $cacheKey = "candidates_data_{$this->plan->id}";
        $candidatesData = Cache::remember($cacheKey, 300, function () {
            return [
                'successpeople' => $this->getSuccessPeople(),
                'pipelinepeople' => $this->getPipelinePeople()
            ];
        });

        $this->successpeople = $candidatesData['successpeople'];
        $this->pipelinepeople = $candidatesData['pipelinepeople'];
        $this->isCandidatesLoaded = true;

        $this->dispatch('candidatesLoaded');
    }

    /**
     * Update requirement filter state
     */
    public function updateRequirementFilter($requirementId, $active)
    {
        if ($active) {
            if (!in_array($requirementId, $this->activeRequirementFilters)) {
                $this->activeRequirementFilters[] = $requirementId;
            }
        } else {
            $this->activeRequirementFilters = array_filter(
                $this->activeRequirementFilters, 
                fn($id) => $id != $requirementId
            );
        }

        // Refresh candidate data with filters
        $this->refreshCandidatesWithFilters();
    }

    /**
     * Refresh candidates based on active filters
     */
    private function refreshCandidatesWithFilters()
    {
        if (empty($this->activeRequirementFilters)) {
            $this->loadCandidateData();
            return;
        }

        $cacheKey = "filtered_candidates_{$this->plan->id}_" . md5(implode(',', $this->activeRequirementFilters));
        $filteredData = Cache::remember($cacheKey, 300, function () {
            // Apply requirement filters to candidate queries
            $successPeopleQuery = SuccessPeople::where('plan_id', $this->plan->id);
            $pipelinePeopleQuery = pipeline::where('plan_id', $this->plan->id);

            // Add filtering logic based on requirements
            if (!empty($this->activeRequirementFilters)) {
                $successPeopleQuery->whereHas('requirements', function ($query) {
                    $query->whereIn('requirement_id', $this->activeRequirementFilters);
                });

                $pipelinePeopleQuery->whereHas('requirements', function ($query) {
                    $query->whereIn('requirement_id', $this->activeRequirementFilters);
                });
            }

            return [
                'successpeople' => $successPeopleQuery->get(),
                'pipelinepeople' => $pipelinePeopleQuery->get()
            ];
        });

        $this->successpeople = $filteredData['successpeople'];
        $this->pipelinepeople = $filteredData['pipelinepeople'];

        $this->dispatch('candidatesFiltered', count($this->successpeople), count($this->pipelinepeople));
    }

    /**
     * Preload candidate profile data
     */
    public function preloadCandidateProfile($candidateId)
    {
        $cacheKey = "candidate_profile_{$candidateId}";
        
        Cache::remember($cacheKey, 600, function () use ($candidateId) {
            return $this->getCandidateProfileData($candidateId);
        });
    }

    /**
     * Enhanced mount method with lazy loading
     */
    public function mount($planId)
    {
        try {
            // Only load essential data initially
            $this->plan = SuccessionPlan::findOrFail($planId);
            $this->user = Auth::user();
            $this->planDetails = $this->plan;
            $this->requirements = SuccessRequirements::where('plan_id', $planId)->get();

            // Initialize lazy loading properties
            $this->isInitialized = true;

            // Preload chart data in background if not too expensive
            $this->dispatch('initializeLazyLoading');

        } catch (\Exception $e) {
            Log::error('Failed to mount OptimizedSummaryTables: ' . $e->getMessage());
            session()->flash('error', 'Failed to load plan data.');
        }
    }

    // Keep all other existing methods...
}