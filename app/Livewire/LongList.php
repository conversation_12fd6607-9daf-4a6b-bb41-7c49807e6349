<?php

namespace App\Livewire;

use Livewire\Component;
use Carbon\Carbon;
use App\Models\Job;
use Livewire\WithPagination;
use App\Models\pipeline;
use App\Models\JobPeople;
use App\Models\Skills;
use App\Models\CareerHistories;
use App\Models\JobRequirement;
use App\Models\Location;
use App\Models\Role;
use App\Models\People;
use App\Models\SuccessPeople;
use App\Models\SuccessionPlan;
use App\Models\Company;
use App\Models\UserNotes;
use Illuminate\Support\Facades\DB;
use App\Exports\LongListedPeopleExport;
use Maatwebsite\Excel\Facades\Excel;

class LongList extends Component
{
    use WithPagination;
    public $jobdetails;
    public $openPlanPopup=false;
    public $openTalentPopup=false;
    public $disableDispatchforMultipleSelect=false;
    public $selectedPerson = [];
    public $job;
    public $jobdesc;
    public $pipeline;
    public $selectedPeople = [];
    public $selectedIndividualID;
    public $sortBy = 'skills_match';
    public $sortDirection = 'desc';
    public $search = false;
    public $searchByKeyword = '';
    public $personRoles;
    public $companies;
    public $successPersonNotes;
    public $filteredPnotes;


    // Variables for filters
    public $forename = "";
    public $surname = "";
    public $gender = "";
    public $role;
    public $_role = [];
    public $previousRole = [];
    public $company = [];
    public $function = [];
    public $division = [];
    public $location = [];
    public $regBodies = "";
    public $moverFilter = "";
    public $tenure;
    public $totalPeople;
    public $totalCompanies;
    public $locations;
    public $functions;
    public $divisions;


    // References for charts
    public $genderLabels = [];
    public $genderData = [];
    public $totalScoreLabels = [];
    public $totalScoreData = [];
    public $companyLabels = [];
    public $companyData = [];
    public $functionLabels = [];
    public $functionData = [];
    public $divisionLabels = [];
    public $divisionData = [];
    public $roleLabels = [];
    public $roleData = [];
    public $min_exp =   null;

    //References for adding to jobs
    public $SuccessSkills;
    public $jobDetail;
    public $userJobs;
    public $userPlansData = [];
    public $vopen = false;
    public $addToPlanPopup = false;
    public $plansList = [];
    public $addToPlansArray = [];
    public $addToTalentPoolArray = [];
    public $addToTalentPoolPopup = false;
    public $talentPoolsList = [];



    public function mount()
    {
        $successjob = JobPeople::where('job_id', $this->job)->pluck('people_id')->toArray();
        $this->jobdetails = Job::where('id', $this->job)->first();

        $this->SuccessSkills = JobRequirement::where('job_id', $this->job)
            ->where('type', 'professional_skill')
            ->get();
        //dd($this->SuccessSkills);

        // Use cache for chart data to avoid repeated queries
        $cacheKey = "pipeline_chart_data_job_{$this->job}";
        $chartData = cache()->remember($cacheKey, 300, function() use ($successjob) { // 5 minute cache
            
            // Single optimized query to get all chart data at once
            $allChartData = DB::table('pipelines')
                ->select([
                    'gender',
                    'company_name', 
                    'function',
                    'country',
                    DB::raw('COUNT(*) as total_count')
                ])
                ->where('job_id', $this->job)
                ->whereNotIn('people_id', $successjob)
                ->groupBy('gender', 'company_name', 'function', 'country')
                ->get();

            // Process gender data
            $genderCounts = $allChartData->groupBy('gender')->map(function($items) {
                return $items->sum('total_count');
            });

            // Process company data (top 5)
            $companyCounts = $allChartData->groupBy('company_name')->map(function($items) {
                return $items->sum('total_count');
            })->sortDesc()->take(5);

            // Process function data
            $functionCounts = $allChartData->groupBy('function')->map(function($items) {
                return $items->sum('total_count');
            })->sortDesc();

            // Process country data (top 5)
            $countryCounts = $allChartData->groupBy('country')->map(function($items) {
                return $items->sum('total_count');
            })->sortDesc()->take(5);

            return [
                'gender' => $genderCounts,
                'company' => $companyCounts,
                'function' => $functionCounts,
                'country' => $countryCounts
            ];
        });

        // Set gender data
        foreach ($chartData['gender'] as $gender => $count) {
            if ($gender) {
                $this->genderLabels[] = $gender;
                $this->genderData[] = $count;
            }
        }

        // Set company data
        foreach ($chartData['company'] as $company => $count) {
            if ($company) {
                $this->companyLabels[] = $company;
                $this->companyData[] = $count;
            }
        }

        // Set function data
        foreach ($chartData['function'] as $function => $count) {
            if ($function) {
                $this->functionLabels[] = $function;
                $this->functionData[] = $count;
            }
        }

        $this->locations = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $this->personRoles = Role::distinct()->pluck('title')->map(function ($role) {
            return [
                'value' => $role,
                'label' => $role,
            ];
        })->toArray();

        // $this->companies = Company::distinct()->pluck('name')->map(function ($company) {
        //     return [
        //         'value' => $company,
        //         'label' => $company,
        //     ];
        // })->toArray();
        $companiesArr = pipeline::whereNotNull('company_id')
                                ->whereNotNull('company_name')
                                ->distinct()
                                ->orderBy('company_name', 'ASC')
                                ->pluck('company_name', 'company_id')
                                ->toArray();

        foreach($companiesArr as $companyId => $companyName){
            $this->companies[] = ['value' => $companyId, 'label' => $companyName];
        }

        $this->functions = pipeline::whereNotNull('function')
                                ->where('function', '!=', "")
                                ->distinct()
                                ->orderBy('function', 'ASC')
                                ->pluck('function')
                                ->map(function ($function) {
                                    return [
                                        'value' => $function,
                                        'label' => $function,
                                    ];
                                })
                                ->toArray();

        $this->divisions = DB::table('pipelines')
                                ->whereNotNull('division')
                                ->where('division', '!=', "")
                                ->distinct()
                                ->orderBy('division', 'ASC')
                                ->pluck('division')
                                ->map(function ($division) {
                                    return [
                                        'value' => $division,
                                        'label' => $division,
                                    ];
                                })
                                ->toArray();


        // dd($this->locations);

        // Set division data (using country data from cache)
        foreach ($chartData['country'] as $country => $count) {
            if ($country) {
                $this->divisionLabels[] = $country;
                $this->divisionData[] = $count;
            }
        }

        $this->jobdesc = Job::where('id', $this->job)->get();

    }

    public function render()
    {
        $successjob = JobPeople::where('job_id', $this->job)->pluck('people_id')->toArray();

        $pipelinePeople = pipeline::select('pipelines.*', 'people.company_name as current_company', 'people.latest_role as current_role')
                                    ->leftJoin('people', 'pipelines.people_id', '=', 'people.id')
                                    ->where('job_id', $this->job)
                                    ->whereNotIn('people_id', $successjob)
                                    ->orderBy($this->sortBy, $this->sortDirection);

        if ($this->search) {
            if ($this->forename) {
                $pipelinePeople = $pipelinePeople->where('pipelines.first_name', 'like', '%' . $this->forename . '%');
            }
            if ($this->surname) {
                $pipelinePeople = $pipelinePeople->where('pipelines.last_name', 'like', '%' . $this->surname . '%');
            }
            if ($this->gender) {
                $pipelinePeople = $pipelinePeople->where('pipelines.gender', '=', $this->gender);
            }
            if ($this->role) {
                $pipelinePeople = $pipelinePeople->where('pipelines.latest_role', 'like', '%' . $this->role . '%');
            }
            if ($this->company) {
                $pipelinePeople = $pipelinePeople->where('pipelines.company_id', $this->company);
            }
            if ($this->function) {
                $pipelinePeople = $pipelinePeople->where('pipelines.function', 'like', '%' . $this->function . '%');
            }
            if ($this->division) {
                $pipelinePeople = $pipelinePeople->where('pipelines.division', 'like', '%' . $this->division . '%');
            }
            if ($this->location) {
                $pipelinePeople = $pipelinePeople->where('pipelines.country', 'like', '%' . $this->location . '%');
            }
            if ($this->regBodies) {
                $pipelinePeople = $pipelinePeople->where('pipelines.other_tags', 'like', '%' . $this->regBodies . '%');
            }
            if ($this->moverFilter) {
                if($this->moverFilter == 'Mover'){
                    $pipelinePeople = $pipelinePeople->where('pipelines.mover', 'Mover');
                }
                
                if($this->moverFilter == 'Non Mover'){
                    $pipelinePeople = $pipelinePeople->where(function($query) {
                                                        $query->whereNull('pipelines.mover')
                                                            ->orWhereIn('pipelines.mover', ['', 'Non Mover']);
                                                    });
                }
            }
            if ($this->min_exp) {
                $pipelinePeople = $pipelinePeople->where('pipelines.tenure', '>=', $this->min_exp);
            }
        }

        if (!empty($this->searchByKeyword)) {
            $searchKeywords = explode(",", $this->searchByKeyword);
            $pipelinePeople = $pipelinePeople->where(function ($query) use ($searchKeywords) {
                foreach ($searchKeywords as $searchword) {
                    $query->orWhere(function ($subQuery) use ($searchword) {
                        $subQuery->orWhere('first_name', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.last_name', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.latest_role', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.company_name', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.function', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.division', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.country', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.summary', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.other_tags', 'like', '%' . $searchword . '%');
                    });
                }
            });
        }

        $filteredPeople = pipeline::where('people_id', $this->selectedIndividualID)
            ->get();

        $this->locations = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $companiesArr = pipeline::whereNotNull('company_id')
                                ->whereNotNull('company_name')
                                ->distinct()
                                ->orderBy('company_name', 'ASC')
                                ->pluck('company_name', 'company_id')
                                ->toArray();

        foreach($companiesArr as $companyId => $companyName){
            $this->companies[] = ['value' => $companyId, 'label' => $companyName];
        }

        // Getting the skills and the career histories
        $filteredPeopleid = $filteredPeople->pluck('people_id')->toArray();

        if (!empty($filteredPeopleid)) {
            // Get the skills of the individual
            $peopleskills = Skills::whereIn('people_id', $filteredPeopleid)->get();

            // Get the career histories of the individual
            $peoplescareer = CareerHistories::whereIn('people_id', $filteredPeopleid)
                ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
                ->select('career_histories.*', 'companies.name as company_name')
                ->orderBy('start_date', 'desc')
                ->get();


        } else {
            $peopleskills = null;
            // $groupedPeoplesCareer = null;
            $peoplescareer = null;
        }

        return view('livewire.long-list', ['pipelinePeople' => $pipelinePeople->paginate(50), 'filteredPeople' => $filteredPeople, 'peopleskills' => $peopleskills, 'groupedPeoplesCareer' => $peoplescareer]);
    }

    public function searchPeople()
    {
        // dd($this->searchByKeyword);
        $successjob = JobPeople::where('job_id', $this->job)->pluck('people_id')->toArray();

        $pipelinePeople = pipeline::where('job_id', $this->job)
            ->whereNotIn('people_id', $successjob)
            ->orderBy($this->sortBy, $this->sortDirection);

        $searchKeywords = explode(",", $this->searchByKeyword);
        $pipelinePeople = $pipelinePeople->where(function ($query) use ($searchKeywords) {
            foreach ($searchKeywords as $searchword) {
                $query->orWhere(function ($subQuery) use ($searchword) {
                    $subQuery->orWhere('first_name', 'like', '%' . $searchword . '%')
                        ->orWhere('middle_name', 'like', '%' . $searchword . '%')
                        ->orWhere('last_name', 'like', '%' . $searchword . '%')
                        ->orWhere('latest_role', 'like', '%' . $searchword . '%')
                        ->orWhere('company_name', 'like', '%' . $searchword . '%')
                        ->orWhere('function', 'like', '%' . $searchword . '%')
                        ->orWhere('division', 'like', '%' . $searchword . '%')
                        ->orWhere('country', 'like', '%' . $searchword . '%')
                        ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                });
            }
        });
        $this->dispatch('updateChart');
        return $pipelinePeople;
    }

    public function runfilters()
    {
        $this->search = true;
        $this->resetPage();
        $this->dispatch('updateChart');
    }

    public function Ordermytable($column)
    {
        if ($column === $this->sortBy) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
        $this->dispatch('updateChart');
    }

    public function changeMinimumTenure($flag)
    {
        if ($flag == 'decrease') {
            $this->min_exp = $this->min_exp <= 1 ? null : ($this->min_exp - 1);
        } else if ($flag == 'increase') {
            $this->min_exp = !$this->min_exp ? 1 : ($this->min_exp + 1);
        }
    }

    public function viewIndividual($id)
    {

        $this->selectedIndividualID = $id;
        $this->jobDetail = Job::find($this->job);
        $user = auth()->user();
        $jobIds = JobPeople::where(['people_id' => $id])->pluck('job_id')->toArray();
        $this->userJobs = Job::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
        })
        ->whereIn('id', $jobIds)
        ->get();
        $planIds = SuccessPeople::where(['people_id' => $id, 'type' => 'External'])->pluck('plan_id')->toArray();
        $this->userPlansData = SuccessionPlan::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
        })
        ->whereIn('id', $planIds)
        ->get();
        $this->dispatch('updateChart');
     
        

        $userNotes = UserNotes::select('user_notes.*', 'users.name as user_name')
        ->where('entity_id', $this->selectedIndividualID)
        ->where('entity_type', 'success_person')
        ->join('users', 'user_notes.author', '=', 'users.id')
        ->orderBy('user_notes.id', 'desc')
        ->get();
        
        $this->successPersonNotes = $userNotes;
    }

 public function ViewTalentPoolList($id)
    {
        $this->talentPoolsList = getTalentPoolList($id, $this->job);
        $this->addToTalentPoolPopup = true;
    }


    public function ViewPlanList($id)
    {
        $this->plansList = getPlansList($id);
        $this->addToPlanPopup = true;
    }

    public function showAddToPlanPopup($peopleId) {
        $this->selectedIndividualID = $peopleId;
        $this->plansList = getPlansList($peopleId);
        $this->addToPlanPopup = true;

    }

    public function showAddToTalentPoolPopup($peopleId) {
        $this->selectedIndividualID = $peopleId;
        $this->talentPoolsList = getTalentPoolList($peopleId, $this->job);
        $this->addToTalentPoolPopup = true;
    }

    public function addpeopleToTalentPools() {
        if(empty($this->addToTalentPoolArray)) {
            $this->dispatch('toast', 'info', 'Please select talent pool!');
            $this->skipRender();
            return;
        }
       
        $peopleId = JobPeople::where('id','=',$this->selectedIndividualID)->pluck('people_id')->first();
    
        foreach($this->addToTalentPoolArray as $key => $value) {
            if($value) {
                $JobPerson = JobPeople::where(['people_id' => $peopleId,'job_id' => $key])->first();
          
                if($JobPerson) {
                    continue;
                }
                addToJob($peopleId, $key);
            }
        }
        if(!$this->disableDispatchforMultipleSelect) {
        $this->dispatch('toast', 'info', 'Added to selected talent pools!');

        $this->addToTalentPoolPopup = false;
        $this->vopen = false;
        $this->addToTalentPoolArray = [];
        }
    }

    public function addpeopleToPlans() {

        if(empty($this->addToPlansArray)) {
            $this->dispatch('toast', 'info', 'Please select plans!');
            $this->skipRender();
            return;
        }
       // $peopleId = pipeline::where('id','=',$this->selectedIndividualID)->pluck('people_id')->first();
        foreach($this->addToPlansArray as $key => $value) {
            if($value) {
                
                $PlanPerson = SuccessPeople::where(['people_id' => $this->selectedIndividualID,'plan_id' => $key])->first();
               
                if($PlanPerson) {
                  continue;
                }
                addToPlan($this->selectedIndividualID, $key);
            }
        }
        if(!$this->disableDispatchforMultipleSelect) {
        $this->dispatch('toast', 'info', 'Added to selected plans!');
        $this->addToPlanPopup = false;
        $this->vopen = false;
        $this->addToPlansArray = [];
        }


    }

    public function refreshMover($pipelinePeople){
        $people = People::where('id', $pipelinePeople['people_id'])->first();

        // Update pipeline
        $pipelineDataToUpdate = [
            'first_name'          => $people->forename,
            'last_name'           => $people->surname,
            'middle_name'         => $people->middle_name,
            'other_name'          => $people->other_name,
            'gender'              => $people->gender,
            'diverse'             => $people->diverse,
            'country'             => $people->country,
            'city'                => $people->city,
            'mover'               => 'Non Mover',
            'linkedinURL'         => $people->linkedinURL,
            'latest_role'         => $people->latest_role,
            'company_id'          => $people->company_id,
            'company_name'        => $people->company_name,
            'exco'                => $people->exco,
            'start_date'          => $people->start_date,
            'end_date'            => $people->end_date,
            'tenure'              => $people->tenure,
            'function'            => $people->function,
            'division'            => $people->division,
            'seniority'           => $people->seniority,
            'career_history'      => $people->career_history,
            'educational_history' => $people->educational_history,
            'skills'              => $people->skills,
            'languages'           => $people->languages,
            'other_tags'          => $people->other_tags,
            'readiness'           => $people->readiness,
            'summary'             => $people->summary,
            'status'              => "Approved"
        ];
        pipeline::where('id', $pipelinePeople['id'])
                    ->where('job_id', $this->job)
                    ->update($pipelineDataToUpdate);

        

        $pipelineMoverCount = pipeline::where('job_id', $this->job)
                                        ->where('mover', 'Mover')
                                        ->count();

        if($pipelineMoverCount <= 0){
            Job::where('id', $this->job)->update(['mover' => 'Non Mover']);
        }

        return;
    }

    public function clearFilters()
    {
        $this->search = false;
        // Reset all form input properties
        $this->reset(['forename', 'surname', 'role', 'company', 'function', 'division', 'location', 'gender', 'regBodies', 'tenure']);
        $this->dispatch('updateChart');
    }

    public function addNotes($id)
    {
        // $successPerson = SuccessPeople::findOrFail($id);
        // $successPerson->update(['notes' => $this->filteredPnotes]);

        $user = auth()->user();
        $userNoteData = [
            'entity_id' => $id,
            'entity_type' => 'success_person',
            'Notes' => $this->filteredPnotes,
            'author' => $user->id
        ];
        UserNotes::insert($userNoteData);
        $this->dispatch('updateChart');
    }

    public function addtojob($id)
    {

        $itemData = pipeline::where('id', $id)->first();

        //Get the skills of the person this will be used for the radar chart
        $PersonSkills = Skills::where('people_id', $id)->get();


        $JobPeople = JobPeople::create([
            'pipeline_id'        => $itemData->id,
            'job_id'            => $itemData->job_id,
            'user_id'            => $itemData->user_id,
            'people_id'          => $itemData->people_id,
            'headline'           => ' ',
            'first_name'         => $itemData->first_name,
            'last_name'          => $itemData->last_name,
            'middle_name'        => $itemData->middle_name,
            'other_name'         => $itemData->other_name,
            'gender'             => $itemData->gender,
            'diverse'            => $itemData->diverse,
            'location'           => $itemData->location,
            'country'            => $itemData->country,
            'city'               => $itemData->city,
            'linkedinURL'        => $itemData->linkedinURL,
            'latest_role'        => $itemData->latest_role,
            'company_id'         => $itemData->company_id,
            'company_name'       => $itemData->company_name,
            'start_date'         => $itemData->start_date,
            'end_date'           => $itemData->end_date,
            'tenure'             => $itemData->tenure,
            'function'           => $itemData->function,
            'division'           => $itemData->division,
            'seniority'          => $itemData->seniority,
            'career_history'     => $itemData->career_history,
            'educational_history' => $itemData->educational_history,
            'skills'             => $itemData->skills,
            'languages'          => $itemData->languages,
            'readiness'          => $itemData->readiness,
            'other_tags'         => $itemData->other_tags,
            'summary'            => $itemData->summary,
            'type'               => "External",
            'notes'              => "Enter notes here",
            'recruit'            => 1,
            'status'             => $itemData->status
        ]);
        $this->dispatch('updateChart');
    }

    public function removepipPerson($id)
    {
        $person = pipeline::findOrFail($id);
        $person->delete();
        $this->dispatch('updateChart');
    }

    public function approveJobPeople($id) {
        $job = Job::find($this->job);

        if(($job->user_id != auth()->user()->id)) {
            $this->dispatch('toast', 'error', 'You are not allowed to perform this action!');
            return;
        }


        $pipeline = Pipeline::find($id);

        if(!$pipeline) {
            $this->dispatch('toast', 'error', 'pipeline not found!');
            return;
        }
        
        $pipeline->update(['status' => 'Approved']);
        $this->dispatch('toast', 'info', 'Status approved successfully!');
    }

    public function downloadLongListedPeople($filterArr)
    {
        // Create an instance of SuccessPeopleExport with the provided filter
        $export = new LongListedPeopleExport($filterArr);

        // Check if there is any data to export
        $pipelinePeople = $export->collection();

        if ($pipelinePeople->isEmpty()) {
            // Return a response indicating no data available
            session()->flash('error', "No records found to be download!");
            // $this->dispatch('toast', 'info', "No records found to be download!");
            return;
        }

        return Excel::download($export, 'long-list.xlsx');
    }


    
    public function addSelectedToTalentPool()
    {  
        $this->talentPoolsList = collect(); 
        $processed = [];
        foreach ($this->selectedPerson as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->talentPoolsList = $this->talentPoolsList->merge(getTalentPoolList($peopleId));
                $processed[] = $peopleId;
            }
        }
       
        $this->talentPoolsList = $this->talentPoolsList->unique('id')->values();
        $this->addToTalentPoolPopup = true;
        $this->openTalentPopup = true;
    }

    public function addSelectedPersonToTalentPool()
    { 
        $this->disableDispatchforMultipleSelect = true;
        foreach($this->selectedPerson as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;
            $this->addpeopleToTalentPools();
        }
        $this->openTalentPopup = false;
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected talent pools!');
        $this->addToTalentPoolPopup = false;
        $this->vopen = false;
        $this->addToTalentPoolArray = [];
        $this->selectedPerson = [];
       
    }
    

    public function addSelectedToPlan()
    { 
        $peopleIds = pipeline::whereIn('id', $this->selectedPerson)->pluck('people_id')->unique();
        $this->plansList = collect(); 
        $processed = [];
        foreach ($peopleIds as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->plansList = $this->plansList->merge(getPlansList($peopleId));
                $processed[] = $peopleId;
            }
        }
        
        $this->plansList = $this->plansList->unique('id')->values();
       
        $this->addToPlanPopup = true;
        $this->openPlanPopup = true;
        
    }
    public function addSelectedPersonToPlans()
    {  
    $peopleIds = pipeline::whereIn('id', $this->selectedPerson)->pluck('people_id')->unique();
      $this->disableDispatchforMultipleSelect = true;
       foreach ($peopleIds as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;

            $this->addpeopleToPlans();
       }
        $this->openPlanPopup = false;
        $this->selectedPerson = [];
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected plans!');
        $this->addToPlanPopup = false;
       
        $this->vopen = false;
        $this->addToPlansArray = [];
    }
    public function deleteSelected()
    {
        $selectedIds = $this->selectedPerson;
        foreach ($selectedIds as $id) {
            pipeline::where('id', $id)->delete();
        }
        $this->dispatch('toast', 'success', 'Individual has deleted successfully.');
        $this->selectedPerson = [];
    }
    public function updatingPage() {
        $this->selectedPerson = [];
    }

}
