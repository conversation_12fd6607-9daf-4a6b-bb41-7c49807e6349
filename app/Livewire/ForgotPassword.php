<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;
use App\Models\User; // Assuming your User model is in this namespace
use App\Mail\ResetPasswordMail; // Import the Mailable class
use Illuminate\Support\Facades\Mail;


class ForgotPassword extends Component
{
    public $email;
    public $step = 1;

    public function render()
    {
        return view('livewire.forgot-password');
    }

    public function submitEmail()
    {
        // Validate email
        $this->validate([
            'email' => 'required|email',
        ]);

        $status = Password::sendResetLink(['email' => $this->email]);

        if ($status == Password::RESET_LINK_SENT) {
            $user = User::where('email', $this->email)->first();
            $resetLink = route('password.reset', ['token' => $status]);
            //Mail::to($user->email)->send(new ResetPasswordMail($resetLink));

            session()->flash('status', __($status));
        } else {
            throw ValidationException::withMessages(['email' => __($status)]);
        }
        $this->step = 2;
    }

    public function resendEmail()
    {
        $status = Password::sendResetLink(['email' => $this->email]);

        if ($status == Password::RESET_LINK_SENT) {
            $user = User::where('email', $this->email)->first();
            $resetLink = route('password.reset', ['token' => $status]);
            Mail::to($user->email)->send(new ResetPasswordMail($resetLink));

            session()->flash('status', __($status));
        } else {
            throw ValidationException::withMessages(['email' => __($status)]);
        }
        $this->step = 2;
    }
    public function resetForm()
    {
        $this->email = '';
        $this->step = 1;
    }

    public function backToLogin()
    {
        return redirect()->route('login'); 
    }
}
