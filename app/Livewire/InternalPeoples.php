<?php

namespace App\Livewire;

use App\Imports\PeopleImport;
use Livewire\Component;
use App\Models\InternalPeople;
use App\Models\Location;
use App\Models\InternalSkills;
use App\Models\Organisation;
use App\Models\OrganisationPeople;
use App\Models\TemporaryOrganisationChild;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;




use App\Models\SuccessPeople;
use App\Models\pipeline;
use Livewire\WithFileUploads;
use App\Models\SuccessionPlan;
use Illuminate\Support\Facades\Redirect;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use App\Models\Skills;
use App\Models\SuccessRequirements;
use App\Models\PlanScores;
use App\Models\People;
use App\Models\Role;
use App\Models\CareerHistories;
use App\Models\notifications;
use App\Models\Company;
use App\Models\internal_career_histories;
use App\Models\User;
use App\Models\UserNotes;
use App\Models\AssessmentCriteria;
use App\Models\InternalRequirement;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Validation\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Eloquent\Collection;

class InternalPeoples extends Component
{
    use WithFileUploads;
    use WithPagination;

    public $step = 1;
    public $plan;

    public $createdPlansId = [];
    // Step 1 data
    public $forename;
    public $surname;
    public $location;
    public $gender;
    public $user;

    // Step 2 data
    public $roles;
    public $function;
    public $linkedInUrl;
    public $division;
    public $start_date;
    public $empid;
    public $open = false;
    public $uploadPopup = false;
    public $dopenNewRole = false;
    public $individualPopup = false;
    public $isVisible = false;
    public $deleteOrganisationPeoplePopup = false;
    public $addDirectReportPopup = false;
    public $replaceUserOrgPopup = false;

    public $addReportingManagerPopup = false;
    public $showOranisationListing = true;
    public $organistaionDetails;
    public $createPlanPopup = false;
    public $addSkillsPopup = false;
    public $addCareerHistroyPopup = false;
    public $shareOrgansationPopup = false;
    public $assessmentCriteriaPopup = false;
    public $editPeopleCareerHistoryPopup = false;
    public $viewNotesPopup = false;
    public $nineboxGridPopup = false;
    public $nopen = false;
    public $nineboxGridOpen = false;
    public $updateInternalpeoplePopup = false;

    public $criteriaRows = [['criteria' => '', 'isChecked' => false]]; // Default row
    public $assessmentCriterias = [];
    public $savedAssessmentCriterias = [];
    public $editAssessmentCriterias = [];

    public $shareOrgansationPeoples = [];

    public $organisationName;
    public $pastRole;
    public $past_start_date;
    public $past_end_date;
    public $pastCompany;

    public $countries = [];
    public $organisations = [];
    public $pagination;

    public $skills = "";

    // Uploading Files
    public $csvFile;
    public $csvSkills;
    public $verify;
    public $errormessage1;
    public $errormessage2;
    public $errormessage3;
    public $newCompanyName;

    public $filteredPnotes;
    public $successPersonNotes;

    // Variables for updateInternal Candidate Form
    public $specialisedskills = [];
    public $commonskills = [];
    public $certification = [];
    public $updateRole;
    public $updateFunction;
    public $updateDivision;
    public $commonSkillNames = [];
    public $commonSkillRatings = [];
    public $updateSpecialisedSkills;
    public $updateCertifications;
    public $specifiedSkill =   '';
    public $certifications =   '';
    public $commonSkills =   '';
    public $duplicateRecords = [];
    public $duplicateUploadPopup = false;
    public $hasCandidatesUploaded = false;
    public $selectedOption = null;
    public $selectedCountries   =   [];
    //Variables for views
    public $internalPeople;
    public $iopen = false;
    public $search = false;
    public $searchterm;
    public $newRoleTitle;

    public $selectedUsers = [];
    public $selectedAddDirectReportPeoples = [];
    public $createdPlans;
    public $selectedPeopleData;

    public $searchPeople;

    public $searchAddDirectReportPeoples;
    public $selectedTaggedRole = null;
    public $selectedColleagues = [];
    public $troles;

    public $enteredRoles        =   [];
    public $stepup              =    [];

    public $selectedDirectReportPeople;

    //create plan popup
    // step 1

    public $name;
    public $descriptions;

    //step2
    public $roleTag;
    public $min_exp;
    public $stepupTag = [];
    public $companies;
    public $pastCompanies;

    //step3
    public $ethnicity = 0;
    public $qualificationTag;
    public $newSkillTag;

    public $selectedCompanies   =    [];
    public $companiesNames      =    '';
    public $education           =    '';
    public $newSkills           =   [];
    public $qualifications      =   [];
    public $cities;
    public $selectedRole;
    public $searchCountries = '';
    public $selectedCities = [];

    public $parentPeopleId;
    public $subChildPoeples = [];

    public $directReportOptions = [];
    public $nestedPeopleArray = [];
    public $uniqueId;
    public $replaceUserInOrganisationChart;
    public $temporaryOrganisationChildPrimaryKey;
    public $editingOrganisationId;

    public $addReportingManagerField;

    public $individualPeopleRow;

    public $addedPeoplesInOrganisation = [];

    public $organisationChartData = [];
    public $reachedFiveLevels = false;
    public $readiness = "Not Ready";
    public $organistaionDetailObj;
    public $isAdminUser = false;
    public $isMasterUser = false;
    public $canAccessAssessmentCriteria = false;
    public $gridLabels = [];
    public $editNineBoxGrid = [];
    public $shareWithUsers = [];

    public $updateProfileFunction;
    public $updateProfileDivision;
    public $updateProfileTenure;
    public $updateProfileReadiness;
    public $updateTenureInCompany;
    public $updateProfileOtherTags;
    protected $listeners = [
        'refreshComponent' => 'getRoles',
        'deletePeopleFromOrganization',
        'deletePeopleFromOrganizationChart',
        'deleteOrganisation',
        'updateInternalPeople',
        'updateCareerHistory',
        'editIndividualCriteria',
        'edit9BoxGrid',
        'shareOrganisation',
        'updateUserInOrganisationChart'
    ];

    protected $rules = [
        'forename' => 'required|min:3',
        'surname' => 'required|min:3',
        'gender' => 'required',
        'roles' => 'required',
        // 'start_date' => 'required|date',
        // 'specialisedskills.*.skill' => 'required',
        // 'specialisedskills.*.exp' => 'required|integer|min:1',
    ];

    public $newSkillData = [
        'specialised' => [],
        'common' => [],
        'certification' => []
    ];

    public $mutipleSelectionData = [
        'qualifications'  => [],
        'skills'          => [],
        'targetRoles'     => [],
        'stepUpCandidate' => [],
        'keyword'         => []

    ];
    
    public $selectedSectors     =    [];
    public $selectedIndustries  =    [];
    public $sectors = [];
    public $industries = [];

    public function mount()
    {
        // Generate a unique ID every time the component is mounted
        $this->uniqueId = Str::uuid()->toString();
        $this->user = auth()->user();
        $user = $this->user;
        $this->isAdminUser = User::isAdminUser();
        $this->isMasterUser = User::isMasterUser();
        $this->setAssessmentCriteria();

        $this->shareWithUsers  = User::where('company_id', $user->company_id)
        ->where('id', '!=', $user->id)->get()
        ->map(function ($user) {
            return [
                'value' => $user->id,
                'label' => $user->name,
            ];
        })
        ->toArray();

        // Initialize the 9-box grid with empty labels

        $savedLabels = AssessmentCriteria::where('company_id', $user->company_id)
        ->where('type', '9box')
        ->orderBy('response') 
        ->pluck('label', 'response') 
        ->toArray(); 

        if(!empty($savedLabels)) {
            $this->gridLabels = $savedLabels;

        } else {
            $this->gridLabels = array_fill(0, 9, '');
        }


        $this->plans = SuccessionPlan::where('user_id', $user->id)->get()->pluck('id');

        $this->countries = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();
        $companiesArr = People::whereNotNull('company_id')->pluck('company_id')->toArray();

        $batchSize = 1000; // Set the size of each chunk
        $companiesColl = collect($companiesArr); // Assuming $companiesArr is your array of company IDs
        $allCompanies = collect();  // Initialize the result collection

        $companiesColl->chunk($batchSize)->each(function ($chunk) use ($user, &$allCompanies) {
            // Query for each chunk
            $result = Company::where('id', '!=', $user->company_id)
                ->whereIn('id', $chunk)
                ->get();
        
            // Process the result as needed
            // You can merge the results, or handle them per chunk
            // For example, storing results in a collection:
            $allCompanies = $allCompanies->merge($result);
        });
        

        $this->companies = $allCompanies->map(function ($company) {
        return [
            'value' => $company->name,
            'label' => $company->name,
            'industry' => $company->industry,
            'sector' => $company->sector
        ];
        })->toArray();

        $this->pastCompanies = $allCompanies->map(function ($company) {
        return [
            'value' => $company->id,
            'label' => $company->name,
        ];
        })->toArray();

        $taggedPeople = SuccessionPlan::where('user_id', $user->id)
            ->orWhere(function ($query) use ($user) {
                $query->whereRaw("FIND_IN_SET(?, shared_with)", [$user->id]);
            });
        $taggedPeople = $taggedPeople->pluck('tagged_individual')->filter();

        // Use the plucked and filtered array in the query if it's not empty
        if ($taggedPeople->isNotEmpty()) {
            $this->troles = InternalPeople::where('company_id', $this->user->company_id)
                ->whereNotIn('id', $taggedPeople)
                ->select('id', 'forename', 'surname', 'latest_role')  // Select only necessary columns
                ->get()
                ->map(function ($person) {
                    return [
                        'value' => $person->id,
                        'label' => $person->forename . ' ' . $person->surname . ' ' . $person->role,
                    ];
                })->toArray();
        
        } else {
            $this->troles = InternalPeople::where('company_id', $this->user->company_id)
            ->select('id', 'forename', 'surname', 'latest_role')  // Select only necessary columns
            ->get()->map(function ($person) {
                return [
                    'value' => $person->id,
                    'label' => $person->forename . ' ' . $person->surname . ' ' . $person->role,
                ];
            })->toArray();
        }

        $industries = Company::whereNotNull('industry')
            ->where('industry', '!=', 'NA')
            ->where('industry', '!=', '0')
            ->distinct()
            ->orderBy('industry', 'ASC')
            ->pluck('industry')
            ->toArray();
    
        $this->industries = array_map(function($industry) {
            return ['value' => $industry, 'label' => $industry];
        }, $industries);
    }

    public function render()
    {
        $user = auth()->user();
        $selectedPeoplesCount = 0;

        $addedPeoples = $this->getAlreadyAddedPeoples();
        $directReportOptionsObject = $this->getdirectReportOptions($addedPeoples);
        $reportingManagerOptionsObject = $this->getReportingManagerOptions($addedPeoples);

        $organisationsObj = Organisation::where('created_by', $user->id)
            ->orWhereRaw("JSON_CONTAINS(IFNULL(NULLIF(shared, ''), '[]'), ?)", [json_encode($user->id)])
            ->orderByDesc('created_at')->paginate(9); 
        $organisationArr = $organisationsObj->map(function ($organisation) {
            $genderCounts = OrganisationPeople::internalPeopleGenderCount($organisation->id);
            $totalPeople = array_sum($genderCounts);

            return [
                'organisation_id' => $organisation->id,
                'organisation_title' => $organisation->title,
                'organisation_people' => $totalPeople,
                'created_by' => $organisation->created_by,
                'female_ratio' => $totalPeople > 0 ? round(($genderCounts['Female'] ?? 0) / $totalPeople * 100) : 0,
                'male_ratio' => $totalPeople > 0 ? round(($genderCounts['Male'] ?? 0) / $totalPeople * 100) : 0,
            ];
        });

        $parentPeoplesObject = InternalPeople::where('id', $this->parentPeopleId)->first();

        $colleagues = User::where('company_id', $user->company_id)
            ->where('id', '!=', $user->id)->get()
            ->map(function ($user) {
                return [
                    'value' => $user->id,
                    'label' => $user->name,
                ];
            })
            ->toArray();
        $taggedPeople = SuccessionPlan::where('user_id', $user->id)
            ->orWhere(function ($query) use ($user) {
                $query->whereRaw("FIND_IN_SET(?, shared_with)", [$user->id]);
            });

        $taggedPeople = $taggedPeople->pluck('tagged_individual')->filter();

        if ($taggedPeople->isNotEmpty()) {
            $this->troles = InternalPeople::where('company_id', $this->user->company_id)
                ->whereNotIn('user_id', $taggedPeople)
                ->get()->map(function ($person) {
                    return [
                        'value' => $person->id,
                        'label' => $person->forename . ' ' . $person->surname . ' ' . $person->role,
                    ];
                })->toArray();
        } else {
            // Handle the case when $taggedPeople is empty or only contains null values
            // For example, you might want to return all records in this case
            $this->troles = InternalPeople::where('company_id', $this->user->company_id)->get()->map(function ($person) {
                return [
                    'value' => $person->id,
                    'label' => $person->forename . ' ' . $person->surname . ' ' . $person->role,
                ];
            })->toArray();
        }
        $selectedAddDirectReportPeoples = $this->selectedAddDirectReportPeoples;

        $nestedPeopleArray = $this->nestedPeopleArray;
        $selectedDirectReportPeople = $this->selectedDirectReportPeople;

        $organizationPeoples = null;
        if (!empty($this->organistaionDetails)) {
            $organizationPeoples = OrganisationPeople::where('organisation_id', $this->organistaionDetails->id)
                ->whereNull('parent_id')
                ->with('descendants')
                ->get();
        }

        $addedpans = SuccessionPlan::where(['user_id' => auth()->user()->id,])->whereNotNull('tagged_individual')->pluck('tagged_individual')->toArray();


        $otherUsers = User::where('company_id', $user->company_id)
            ->where('id', '!=', $user->id)->get();

        $this->dispatch("handleRedirection", $this->selectedAddDirectReportPeoples, $this->editingOrganisationId ? false : true);

        return view('livewire.internalPeople.index', compact(
            'organisationArr',
            "organisationsObj",
            "colleagues",
            "parentPeoplesObject",
            "directReportOptionsObject",
            "nestedPeopleArray",
            "selectedDirectReportPeople",
            "organizationPeoples",
            "addedpans",
            "reportingManagerOptionsObject",
            "otherUsers"
        ));
    }

    public function getAlreadyAddedPeoples()
    {
        if (!$this->editingOrganisationId) {

            $addedPeoples = TemporaryOrganisationChild::where('unique_id', $this->uniqueId)
                ->distinct()
                ->pluck('internal_people_id')
                ->toArray();
        } else {
            $addedPeoples = OrganisationPeople::where('organisation_id', $this->editingOrganisationId)
                ->distinct()
                ->pluck('internal_people_id')
                ->toArray();
        }
        return $addedPeoples;
    }

    public function getReportingManagerOptions($addedPeoples)
    {

        $reportingManagerOptionsObject = InternalPeople::whereIn('id', $this->directReportOptions)
            ->whereNotIn('id', $addedPeoples);

        if (!empty($this->searchAddDirectReportPeoples)) {

            $reportingManagerOptionsObject->where(function ($query) {
                $query->where('forename', 'like', "%{$this->searchAddDirectReportPeoples}%")
                    ->orWhere('surname', 'like', "%{$this->searchAddDirectReportPeoples}%");
            });
        }
        return $reportingManagerOptionsObject->get();
    }

    public function getdirectReportOptions($addedPeoples)
    {
        $directReportOptionsObject = collect();

        if (!empty($this->directReportOptions)) {
            $directReportOptionsQuery = InternalPeople::whereIn('id', $this->directReportOptions)->whereNotIn('id', $addedPeoples);;

            if (!empty($this->searchAddDirectReportPeoples)) {

                $directReportOptionsQuery->where(function ($query) {
                    $query->where('forename', 'like', "%{$this->searchAddDirectReportPeoples}%")
                        ->orWhere('surname', 'like', "%{$this->searchAddDirectReportPeoples}%");
                });
            }
            $directReportOptionsObject = $directReportOptionsQuery->get();
        }

        return $directReportOptionsObject;
    }

    public function setOrganisationChartData()
    {

        if ($this->editingOrganisationId) {
            $this->organisationChartData = OrganisationPeople::where('organisation_id', $this->editingOrganisationId)
                ->whereNull('parent_id')
                ->with([
                    'descendants',    
                ])->get();

            $this->addedPeoplesInOrganisation =  OrganisationPeople::where('organisation_id', $this->editingOrganisationId)
                ->distinct('internal_people_id')
                ->pluck('internal_people_id')
                ->toArray();
        } else {
            $this->organisationChartData = TemporaryOrganisationChild::where('unique_id', $this->uniqueId)
                ->whereNull('parent_id')
                ->with([
                    'descendants'
                ])->get();

            $this->addedPeoplesInOrganisation = TemporaryOrganisationChild::where('unique_id', $this->uniqueId)
                ->distinct('internal_people_id')
                ->pluck('internal_people_id')
                ->toArray();
        }

        // Check if any organization has reached 5 levels deep
        foreach ($this->organisationChartData as $organization) {
            if ($this->getDepth($organization) > 5) {
                $this->reachedFiveLevels = true;
                break;
            }
        }

        $this->dispatch('updateSelection', $this->addedPeoplesInOrganisation);

    }

    public function sendAssessmentCriteriaAndNineBoxGridDataToUI() {
        $organisationPeoples =  OrganisationPeople::where('organisation_id', $this->editingOrganisationId)
        ->get();

        $assessmentCriteria = [];
        $nineBoxGrid = [];

        foreach($organisationPeoples as $organisationPeople) {
        $assessmentCriteria[$organisationPeople->internal_people_id] = $this->setEditAssessmentCriteria($organisationPeople->internal_people_id);
        $nineBoxGrid[$organisationPeople->internal_people_id] = $this->set9BoxGrid($organisationPeople->internal_people_id);
        }

        $this->dispatch('assessmentCriteriaData', $assessmentCriteria, $nineBoxGrid,  $this->isAdminUser, $this->editingOrganisationId, $this->isMasterUser, $this->canAccessAssessmentCriteria);
    }

    // Recursive function to determine the depth of the organization tree
    public function getDepth($organization, $depth = 1, $maxDepth = 20)
    {
        if ($depth > $maxDepth || $organization->descendants->isEmpty()) {
            return $depth;
        }
        $currentMaxDepth = $depth;
        foreach ($organization->descendants as $descendant) {
            $currentMaxDepth = max($currentMaxDepth, $this->getDepth($descendant, $depth + 1, $maxDepth));
        }
        return $currentMaxDepth;
    }

    public function addReportingManager()
    {
        $individualPeopleRow = $this->individualPeopleRow;
        $addReportingManagerFieldValue = $this->addReportingManagerField;

        if (!$this->editingOrganisationId) {
            $reportManager = $this->createTemporaryOrganisationChild($addReportingManagerFieldValue);
            TemporaryOrganisationChild::where(['id' => $individualPeopleRow["id"]])->update(["parent_id" => $reportManager->id]);

            $dataArray = $this->selectedAddDirectReportPeoples;
            $newPair[$addReportingManagerFieldValue] = [$this->selectedDirectReportPeople => true];
            $this->selectedAddDirectReportPeoples = $newPair + $dataArray;
        } else {
            $reportManager = $this->createOrganisationPeople($addReportingManagerFieldValue, $this->editingOrganisationId);
            OrganisationPeople::where(['id' => $individualPeopleRow["id"]])->update(["parent_id" => $reportManager->id]);
        }

        $this->addReportingManagerPopup = false;
        $this->setOrganisationChartData();
        if ($this->editingOrganisationId) {
            $this->sendAssessmentCriteriaAndNineBoxGridDataToUI();
        }


    }

    private function createTemporaryOrganisationChild($internalPeopleId)
    {
        return TemporaryOrganisationChild::create([
            "unique_id" => $this->uniqueId,
            "internal_people_id" => $internalPeopleId,
            "parent_id" => null,
        ]);
    }

    private function createOrganisationPeople($internalPeopleId, $organisationId)
    {
        return OrganisationPeople::create([
            "organisation_id" => $organisationId,
            "internal_people_id" => $internalPeopleId,
            "parent_id" => null,
            "created_by" => auth()->user()->id
        ]);
    }

    public function addInternalCandidates()
    {
         
        try {
            $this->validate([
                'forename' => 'required|string',
                'surname' => 'required|string',
                'gender' => 'required',
                'roles' => 'required|string|max:100',
                'start_date' => 'required|date',
                'empid' => 'nullable|unique:internal_people,employee_id',
            ], [
                'roles.required' => 'The role field is required.',
                'roles.string' => 'The role must be a valid string.',
            ]);
    
    
        } catch (\Illuminate\Validation\ValidationException $e) {
            $errors = $e->validator->errors();
    
            if ($errors->has('forename') || $errors->has('surname') || $errors->has('gender')) {
                $this->step = 1;
            }
            if ($errors->has('roles') || $errors->has('start_date') || $errors->has('empid')) {
                $this->step = 2;
            }
    
            throw $e;
        }
        $user = auth()->user();
        $internalcompany = Company::find($user->company_id);
        $tenure = !empty($this->start_date) ? Carbon::now()->diffInYears(Carbon::parse($this->start_date)) : 0;

        $internalPerson = new InternalPeople([
            'employee_id' => $this->empid,
            'forename' => $this->forename,
            'surname' => $this->surname,
            'gender' => $this->gender ?? 'Not Applicable',
            'latest_role' => $this->roles,
            'linkedinURL' => $this->linkedInUrl,
            'function' => $this->function ?? 'Not Applicable',
            'division' => $this->division ?? 'Not Applicable',
            'readiness' => 'Not Ready',
            'start_date' => $this->start_date,
            'tenure' => $tenure,
            'company_id' => $user->company_id,
            'company_name' => $internalcompany->name,
            'location' => $this->location ?? 'Not Applicable',
            'user_id' => $user->id
        ]);

        $internalPerson->save();

        foreach ($this->newSkillData['specialised'] as $skill) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $skill,
                    'internal_people' => $internalPerson->id,
                    'company_id' =>  $user->company_id,
                    'skill_type' => 'Specialised'
                ]
            );
        }

        // This will be use for qualifications
        // Getting any Educational Qualifications Requirements
        foreach ($this->newSkillData['common'] as $qualification) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $qualification,
                    'internal_people' => $internalPerson->id,
                    'company_id' =>  $user->company_id,
                    'skill_type' => 'Common'
                ]
            );
        }

        foreach ($this->newSkillData['certification'] as $qualification) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $qualification,
                    'internal_people' => $internalPerson->id,
                    'company_id' =>  $user->company_id,
                    'skill_type' => 'Certification'
                ]
            );
        }

        // $this->saveSkills($internalPerson->id, $user->company_id);
        $this->open = false;
        $this->step = 1;
        $this->reset(['forename', 'surname', "gender", "roles", "function", "division", "start_date", "empid", "location"]);
        $this->addIndividualPeople($internalPerson->id);
        $this->dispatch('toast', 'info', "Individual user added successfully");
        return $this->skipRender();

    }

    private function saveSkills($internalPersonId, $companyId)
    {
        $this->saveSkillSet($this->commonskills, 'Common', $internalPersonId, $companyId);
        $this->saveSkillSet($this->specialisedskills, 'Specialised', $internalPersonId, $companyId);
        $this->saveSkillSet($this->certification, 'Certification', $internalPersonId, $companyId);
    }

    private function saveSkillSet($skills, $type, $internalPersonId, $companyId)
    {
        if (!empty($skills)) {
            foreach ($skills as $skill) {
                InternalSkills::updateOrCreate(
                    [
                        'skill_name' => $skill,
                        'internal_people' => $internalPersonId,
                        'company_id' => $companyId,
                        'skill_type' => $type
                    ]
                );
            }
        }
    }

    public function closeIndividualPopup()
    {
        $this->open = false;
        $this->reset(['forename', 'surname', 'newSkillData', 'mutipleSelectionData']);
        $this->resetValidation();
    }

    public function uploadCSV()
    {
        $this->validate([
            'csvFile' => 'required|mimes:csv,xlsx,xls|max:2048', // accept only csv and excel files with a max size of 2MB
        ], [
            'csvFile.required' => "Please select excel or csv file."
        ]);
        try {
            $user = auth()->user();
            $this->processCSV($this->csvFile, $user);
        } catch (Exception $e) {

            $this->dispatch('toast', 'error', $e->getMessage());
        }
    }

    private function processExcel($file, $user)
    {
        try {
            $excelData = Excel::toArray(new PeopleImport, $file->getRealPath());

            // $expectedHeaders = ['forename', 'surname', 'role', 'start_date(m/d/y)', 'function', 'division', 'employee_id'];
            $duplicates = [];

            // Check if the Excel file has a header row
            // $headers = $excelData[0][0];
            // dd($headers);
            // if ($headers !== $expectedHeaders) {
            //     $this->errormessage2 = "Please upload the correct file with the proper headers.";
            //     $this->dispatch('toast', 'error', $this->errormessage2);
            //     return;
            // }

            // Remove the header row from the data
            // unset($excelData[0][0]);

            foreach ($excelData[0] as $row) {
                // dd($excelData);
                // $data = array_combine($expectedHeaders, $row);

                // $validationRules = [
                //     'forename' => 'required',
                //     'surname' => 'required',
                //     'role' => 'required',
                //     'function' => 'sometimes',
                //     'division' => 'sometimes',
                //     'start_datemdy' => 'required|date', // Adjusted to match Excel header
                //     'employee_id' => 'sometimes'
                // ];

                // $validator = \Validator::make($data, $validationRules);

                // if ($validator->fails()) {
                //     return redirect()->back()->withErrors($validator->errors())->withInput();
                // }

                $forename = $row['forename'];
                $surname = $row['surname'];
                $role = $row['role'];
                $startDate = $row['start_datemdy'];
                $employeeId = $row['employee_id'];

                // Check if a record with the given forename, surname, and role exists
                $existingRecord = InternalPeople::where('employee_id', $employeeId)->first();

                if ($existingRecord) {
                    // Handle duplicates
                    $duplicates["$forename-$surname-$role"] = (object) [
                        'forename'    => $forename,
                        'surname'     => $surname,
                        'role'        => $role,
                        'employeeId'      => $employeeId,
                        'start_date' => $startDate,
                        'duplicate'   => json_decode(json_encode($existingRecord->toArray()), false)
                    ];
                } else {
                    $internalcompany = Company::where('id', $user->company_id)->first();
                    $roleExists = Role::where('title', $row['role'])->first();
                    if (empty($roleExists)) {
                        $roleExists = new Role();
                        $roleExists->title = $row['role'];
                        $roleExists->save();
                    }
                    InternalPeople::create([
                        'forename' => $row['forename'],
                        'surname' => $row['surname'],
                        'latest_role' => $row['role'],
                        'function' => $row['function'],
                        'division' => $row['division'],
                        'start_date' => Carbon::parse($row['start_date'])->format('Y-m-d'),
                        'employee_id' => $employeeId,
                        'created_by' => $user->id,
                        'company_id' => $internalcompany->id,
                        'company_name' => $internalcompany->name,
                        'user_id' => $user->id
                    ]);
                }
            }

            $this->uploadPopup = false;
            if (count($duplicates) > 0) {
                $this->duplicateRecords = (object) $duplicates;
                $this->duplicateUploadPopup = true;
                return;
            } else {
                $this->hasCandidatesUploaded = true;
            }

            request()->session()->flash('success', "File uploaded successfully");
        } catch (Exception $exception) {
            request()->session()->flash('error', $exception->getMessage());
        }
    }


    private function processSkills($csvSkills)
    {
        $user = auth()->user();
        $this->validate([
            'csvSkills' => 'required|mimes:csv',
        ]);

        // Read the CSV file
        $csvData = array_map('str_getcsv', file($csvSkills->getRealPath()));
        $expectedHeaders = ['forename', 'surname', 'role', 'start_date', 'function', 'division', 'employee_id'];
        $duplicates = [];

        // Check if the CSV has a header row
        $headers = $csvData[0];
        if ($headers !== $expectedHeaders) {
            $this->errormessage2 = "Please upload the correct skills template with the proper headers.";
            return redirect()->back()->with('error', 'CSV file format is invalid.')->withInput();
        }

        // Remove the header row from the data
        unset($csvData[0]);

        foreach ($csvData as $row) {
            if (count($row) !== count($expectedHeaders)) {
                $this->errormessage2 = "Please upload the correct skills template.";
                return redirect()->back()->with('error', 'CSV file format is invalid.')->withInput();
            }

            $data = array_combine($expectedHeaders, $row);

            $validationRules = [
                'forename' => 'required',
                'surname' => 'required',
                'role' => 'required',
                'function' => 'sometimes',
                'division' => 'sometimes',
                'start_date' => 'required|date',
                'employee_id' => 'sometimes'
            ];

            $validator = \Validator::make($data, $validationRules);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator->errors())->withInput();
            }

            $employeeId = $data['employee_id'];

            if (!empty($employeeId)) {
                $existingRecord = InternalPeople::where('employee_id', $employeeId)->first();

                if ($existingRecord) {
                    $duplicates[] = $existingRecord;
                } else {
                    $internalcompany = Company::where('id', $user->company_id)->first();
                    $roleExists = Role::where('title', $data['role'])->first();
                    if (empty($roleExists)) {
                        $roleExists = new Role();
                        $roleExists->title = $data['role'];
                        $roleExists->save();
                    }
                    InternalPeople::create([
                        'forename' => $data['forename'],
                        'surname' => $data['surname'],
                        'role_id' => $roleExists->id,
                        'function' => $data['function'],
                        'division' => $data['division'],
                        'start_date' => Carbon::parse($data['start_date'])->format('Y-m-d'),
                        'employee_id' => $employeeId,
                        'created_by' => $user->id,
                        'company_id' => $internalcompany->id,
                        'company_name' => $internalcompany->name,
                        'user_id' => $user->id
                    ]);
                }
            }
        }
    }


    public function downloadCSV()
    {
        $headerRow = ['forename', 'surname', 'role', 'start_date(m/d/y)', 'function', 'division', 'employee_id'];

        // Dummy data
        $dummyData = [
            [
                'forename' => 'John',
                'surname' => 'Doe',
                'role' => 'Manager',
                'start_date' => '2022-01-01',
                'function' => 'Management',
                'division' => 'Operations',
                'employee_id' => 'EMP001',
            ],
            [
                'forename' => 'Jane',
                'surname' => 'Smith',
                'role' => 'Developer',
                'start_date' => '2023-05-15',
                'function' => 'Development',
                'division' => 'Engineering',
                'employee_id' => 'EMP002',
            ],
        ];

        $dataRows = collect($dummyData)
            ->map(function ($person) {
                return [
                    'forename' => $person['forename'],
                    'surname' => $person['surname'],
                    'role' => $person['role'],
                    'start_date' => $person['start_date'],
                    'function' => $person['function'],
                    'division' => $person['division'],
                    'employee_id' => $person['employee_id'],
                ];
            })
            ->toArray();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="csv_template.csv"',
        ];

        return Response::stream(function () use ($headerRow, $dataRows) {
            $handle = fopen('php://output', 'w');
            fputcsv($handle, $headerRow);
            foreach ($dataRows as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
        }, 200, $headers);
    }

    private function processCSV($csvFile, $user)
    {
        // try {
        $user = auth()->user();
        $excelData = Excel::toArray(new PeopleImport, $csvFile->getRealPath());
        $duplicates = [];
        $excelData = $excelData[0];
        foreach ($excelData as $key => $row) {
            $employeeId = isset($row['employee_id']) ? $row['employee_id'] : null;
            $existingRecord = InternalPeople::where('employee_id', $employeeId)->whereNotNull('employee_id')->first();

            if ($existingRecord) {
                $duplicates[] = $existingRecord->employee_id;
            } else {
                $internalcompany = Company::where('id', $user->company_id)->first();
                $roleExists = Role::where('title', $row['role'])->first();
                if (empty($roleExists)) {
                    $roleExists = new Role();
                    $roleExists->title = $row['role'];
                    $roleExists->save();
                }
                $internalPeople = InternalPeople::create([
                    'forename' => $row['forename'],
                    'surname' => $row['surname'],
                    'role_id' => $roleExists->id,
                    'function' => $row['function'],
                    'division' => $row['division'],
                    'start_date' => Carbon::parse($row['start_datemdy'])->format('Y-m-d'),
                    'employee_id' => $employeeId,
                    'created_by' => $user->id,
                    'company_id' => $internalcompany->id,
                    'company_name' => $internalcompany->name,
                    'user_id' => $user->id
                ]);
                $this->addIndividualPeople($internalPeople->id);
            }
            
        }
        $this->uploadPopup = false;
        $this->csvFile = null;
        if (count($duplicates) > 0) {
            $this->dispatch('customToast', 'info', "Duplicate employee ids are not uploaded ." . implode(",", $duplicates));

            return $this->skipRender();
        }
        $this->dispatch('customToast', 'info', "File Uploaded Successfully.");

        return $this->skipRender();


        // request()->session()->flash('success', "File uploaded successfully");
        // } catch (Exception $exception) {
        //     request()->session()->flash('error', $exception->getMessage());
        // }
    }

    public function selectedPeoples()
    {
        $this->individualPopup = false;
    }

    public function addSpecifiedSkill()
    {
        if (!empty($this->specifiedSkill)) {
            $this->specialisedskills[] = $this->specifiedSkill;
            $this->specifiedSkill = '';
        }
    }

    public function removeSpecifiedSkill($index)
    {
        unset($this->specialisedskills[$index]);
        $this->specialisedskills = array_values($this->specialisedskills); // Re-index the array
    }

    public function addCertifications()
    {
        if (!empty($this->certifications)) {
            $this->certification[] = $this->certifications;
            $this->certifications = '';
        }
    }

    public function removeCertifications($index)
    {
        unset($this->certification[$index]);
        $this->certification = array_values($this->certification); // Re-index the array
    }

    public function addCommonSkills()
    {
        if (!empty($this->commonSkills)) {
            $this->commonskills[] = $this->commonSkills;
            $this->commonSkills = '';
        }
    }

    public function removeCommonSkills($index)
    {
        unset($this->commonskills[$index]);
        $this->commonskills = array_values($this->commonskills); // Re-index the array
    }

    public function deletePeopleFromOrganization($id)
    {
        $this->selectedUsers = array_values(array_filter($this->selectedUsers, fn($item) => $item != $id));
        $this->deleteOrganisationPeoplePopup = false;
    }

    public function deletePeopleFromOrganizationChart($primaryKey, $parentId, $childId, $action)
    {

        if ($action == "vacant") {
            $role = Role::where('title', 'Vacant')->first();
            $vacantUser = $role ? InternalPeople::where('role_id', $role->id)->first() : null;

            if (!$this->editingOrganisationId) {

                $temporaryOrganisationChild = $vacantUser ? TemporaryOrganisationChild::where(['internal_people_id' => $childId, 'unique_id' => $this->uniqueId])->first() : null;

                if ($temporaryOrganisationChild) {
                    $temporaryOrganisationChild->update(['internal_people_id' => $vacantUser->id]);
                    session()->flash('success', "Vacant people set successfully.");
                } else {
                }
            } else {
                $organisationChild = $vacantUser ? OrganisationPeople::where(['internal_people_id' => $childId, 'organisation_id' => $this->editingOrganisationId])->first() : null;

                if ($organisationChild) {
                    $organisationChild->update(['internal_people_id' => $vacantUser->id]);
                    session()->flash('success', "Vacant people set successfully.");
                } else {
                    Log::info("organisationChild is empty");
                }
            }
        }

        if ($action == "replace") {
            $this->temporaryOrganisationChildPrimaryKey = $primaryKey;
            $this->replaceUserOrgPopup = true;
            $this->dispatchSelectedPeoplesForOrganisation();
        }

        if ($action == "permanent") {
            $role = Role::where('title', 'Vacant')->first();
            $vacantUser = $role ? InternalPeople::where('role_id', $role->id)->first() : null;

            if ($this->editingOrganisationId && $vacantUser) {

                $organisationChild = OrganisationPeople::where(['internal_people_id' => $childId, 'organisation_id' => $this->editingOrganisationId])->first();

                if ($organisationChild) {
                    $organisationChild->update(['internal_people_id' => $vacantUser->id]);
                    InternalPeople::find($childId)->delete();
                    $this->dispatch('toast', 'info', "User deleted form platform.");
                } else {
                    $this->dispatch('toast', 'error', "Something went wrong.");
                }
            }
        }

        if ($action === 'delete') {

            if ($primaryKey != 0) {


                $model = $this->editingOrganisationId ? OrganisationPeople::class : TemporaryOrganisationChild::class;
                $item = $model::find($primaryKey);

                if ($item) {
                    // Delete child records
                    $model::where('parent_id', $item->id)->delete();

                    $item->delete();

                    $data = TemporaryOrganisationChild::select('id', 'internal_people_id')->where('unique_id', $this->uniqueId)
                        ->whereNull('parent_id')
                        ->with(['descendants'])
                        ->get()->toArray();

                    $this->selectedAddDirectReportPeoples = $this->buildHierarchy($data);

                    // Clear the selected array if the item is a root
                    if (is_null($item->parent_id)) {
                        $this->selectedAddDirectReportPeoples = [];
                    }
                    $this->sendAssessmentCriteriaAndNineBoxGridDataToUI();
                } else {
                    Log::warning("Item with primary key $primaryKey not found.");
                }
            } else {
                Log::warning("No valid parent or child ID provided for deletion.");
            }
        }

        $this->setOrganisationChartData();
    }

    public function buildHierarchy(array $data, &$result = [], $level = 1)
    {
        if ($level > 5) return;

        foreach ($data as $item) {
            foreach ($item['descendants'] as $child) {
                $result[$item['internal_people_id']][$child['internal_people_id']] = true;
                $this->buildHierarchy([$child], $result, $level + 1);
            }
        }
        return $result;
    }

    public function updateUserInOrganisationChart($replaceUserInOrganisationChart)
    {
        $user = auth()->user();
        $model = $this->editingOrganisationId ? OrganisationPeople::class : TemporaryOrganisationChild::class;
        $temporaryOrganisationChild = $model::where("id", $this->temporaryOrganisationChildPrimaryKey)->first();

        if ($temporaryOrganisationChild) {
            if(!$this->editingOrganisationId) {
                $this->selectedAddDirectReportPeoples = $this->replaceNestedArrayWithDepthLimit($this->selectedAddDirectReportPeoples, $temporaryOrganisationChild->internal_people_id, $replaceUserInOrganisationChart);

            }
            $temporaryOrganisationChild->update(['internal_people_id' => $replaceUserInOrganisationChart]);
            $this->dispatchSelectedPeoplesForOrganisation();
            
            $this->replaceUserOrgPopup = false;
            $this->dispatch('toast', 'info', 'User replaced successfully.');
        } else {
            $this->dispatch('toast', 'error', 'Something went wrong with replace user!.');
        }
    }
    public function replaceNestedArrayWithDepthLimit(array $array, $search, $replace, $depth = 0, $maxDepth = 20): array {
        // Exit if recursion depth exceeds the maximum allowed depth
        if ($depth > $maxDepth) {
            return $array;
        }
    
        foreach ($array as $key => $value) {
            // Check if the key matches
            $newKey = ($key === $search) ? $replace : $key;
    
            // If the value is an array, recurse into it
            if (is_array($value)) {
                $array[$newKey] = $this->replaceNestedArrayWithDepthLimit($value, $search, $replace, $depth + 1, $maxDepth);
            } else {
                // Replace value if it matches
                $array[$newKey] = ($value === $search) ? $replace : $value;
            }
    
            // Remove the old key if it was changed
            if ($newKey !== $key) {
                unset($array[$key]);
            }
        }
    
        return $array;
    }
    
    

    public function dispatchSelectedPeoplesForOrganisation() {
        $user = auth()->user();
        if($this->editingOrganisationId) {
            $this->selectedUsers = OrganisationPeople::where('organisation_id', $this->editingOrganisationId)->distinct('internal_people_id')->pluck('internal_people_id')->toArray();

        } else {
            $this->selectedUsers = TemporaryOrganisationChild::where('unique_id',  $this->uniqueId)->distinct('internal_people_id')->pluck('internal_people_id')->toArray();
        }
        $selectedPeoplesForOrganisation = InternalPeople::whereIn('id', $this->selectedUsers)->with('careerHistory.company', 'internalSkills', 'notes.user')
        ->where('company_id', $user->company_id)
        ->where('forename', '!=', 'Vacant')->get()->toArray();
        foreach ($selectedPeoplesForOrganisation as &$item) {
            $user = auth()->user();
            $planIds = SuccessPeople::where(['people_id' => $item['id'], 'type' => 'Internal'])->pluck('plan_id')->toArray();
            $item['internalUserPlans'] = SuccessionPlan::where(function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
            })
            ->whereIn('id', $planIds)
            ->select('id', 'name')
            ->get()->toArray();
        }
    // dd($selectedPeoplesForOrganisation);

    $this->dispatch('selectedPeoplesForOrganisation', $selectedPeoplesForOrganisation);

    }


    public function deleteOrganisation($id)
    {
        Organisation::find($id)->delete();
    }

    public function createOrganisaton()
    {
        $this->validate([
            'organisationName' => [
                'required',
                'string',
            ]
        ]);

        if (!$this->editingOrganisationId && empty($this->selectedAddDirectReportPeoples)) {
            session()->flash('error', "Please select people to create organisation");
            return;
        }


        if ($this->editingOrganisationId) {
            $organisation = Organisation::findOrFail($this->editingOrganisationId);
            $organisation->update([
                'title' => $this->organisationName
            ]);
            session()->flash('success', 'Organisation updated successfully!');
            return;
        }

        $this->saveOrganisation($this->organisationName);

        return redirect(request()->header('Referer'));
    }

    public function saveOrganisation($organisationName, $planId = null) {

        // $filteredArray = array_filter($this->selectedshareOrgansationPeoples, function ($value) {
        //     return $value === true;
        // });

        // // Get the keys of the filtered array
        // $filteredArray = array_keys($filteredArray);

        $user = auth()->user();

        DB::beginTransaction();

        try {

            $organisation = Organisation::create([
                'title' => $organisationName,
                'created_by' => $user->id,
                // 'shared' => json_encode($filteredArray)
            ]);

            foreach ($this->selectedAddDirectReportPeoples as $parent_id => $children) {
                // Create or fetch the parent record
                $parent = OrganisationPeople::firstOrCreate(
                    ['internal_people_id' => $parent_id, 'organisation_id' => $organisation->id],
                    ['parent_id' => null, 'created_by' => $user->id]
                );
                // Create child records if they don't exist
                foreach ($children as $child_id => $value) {
                    if ($value == true) {
                        OrganisationPeople::firstOrCreate(
                            ['internal_people_id' => $child_id, 'parent_id' => $parent->id, 'organisation_id' => $organisation->id],
                            ['created_by' => $user->id]
                        );
                    }
                }
            }
            // Delete the temporary organisation children
            TemporaryOrganisationChild::where('unique_id',  $this->uniqueId)->delete();

            $date = Carbon::now()->subDay();

            TemporaryOrganisationChild::where('created_at', '<', $date)->delete();

            $this->reset(['organisationName']);
            $this->selectedUsers = [];
            $this->selectedAddDirectReportPeoples = [];

            $this->uniqueId = Str::uuid()->toString();
            $this->organisationChartData = [];

            DB::commit();

            session()->flash('success', 'Organisation created successfully!');

            return $organisation;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            session()->flash('error', $e->getMessage());
        }
    }

    public function updateDirectReportOptions($id, $rowData = null, $selectedPeoplesForOrganisationCreation = [])
    {
        if (!empty($rowData)) {
            $this->individualPeopleRow = $rowData;
        }
        if(!empty($selectedPeoplesForOrganisationCreation)) {
        $this->selectedUsers = $selectedPeoplesForOrganisationCreation;
        }
        $this->selectedDirectReportPeople = $id;
        $this->directReportOptions = array_filter($this->selectedUsers, function ($userId) use ($id) {
            return $userId != $id;
        });

        if ($this->editingOrganisationId) {
            // Initialize the selectedAddDirectReportPeoples array
            $this->selectedAddDirectReportPeoples = [];

            // Retrieve the OrganisationPeople record based on organisation_id and internal_people_id
            $organisationPeople = OrganisationPeople::where([
                'organisation_id' => $this->editingOrganisationId,
                'internal_people_id' => $id
            ])->first();

            if (!empty($organisationPeople)) {

                // Retrieve the distinct internal_people_id values for the direct reports
                $directReportPeople = OrganisationPeople::where([
                    'parent_id' => $organisationPeople->id,
                ])->distinct('internal_people_id')->pluck('internal_people_id')->toArray();

                // Add the retrieved direct report people to the selectedAddDirectReportPeoples array
                $this->selectedAddDirectReportPeoples[$id] = array_fill_keys($directReportPeople, true);

            }
        }

    }

    public function addDirectReportPeoples()
    {
        $user = auth()->user();

        $isEditing = $this->editingOrganisationId ? true : false;

        foreach ($this->selectedAddDirectReportPeoples as $parent_id => $children) {
            $parent = null;
            if ($isEditing) {
                $parent = OrganisationPeople::firstOrNew([
                    'organisation_id' => $this->editingOrganisationId,
                    'internal_people_id' => $parent_id
                ], [
                    'created_by' => $user->id
                ]);
                if (!$parent->exists) {
                    $parent->save();
                }
            } else {
                $parent = TemporaryOrganisationChild::firstOrCreate([
                    'internal_people_id' => $parent_id,
                    'unique_id' => $this->uniqueId
                ], [
                    'parent_id' => null
                ]);
            }
            $childRecords = [];
            foreach ($children as $child_id => $value) {
                if ($value) {
                    $childRecords[] = $isEditing ? [
                        'internal_people_id' => $child_id,
                        'parent_id' => $parent->id,
                        'organisation_id' => $this->editingOrganisationId,
                        'created_by' => $user->id
                    ] : [
                        'internal_people_id' => $child_id,
                        'parent_id' => $parent->id,
                        'unique_id' => $this->uniqueId
                    ];
                }
            }
            if ($isEditing) {
                foreach ($childRecords as $childData) {
                    OrganisationPeople::firstOrCreate(
                        [
                            'internal_people_id' => $childData['internal_people_id'],
                            'parent_id' => $childData['parent_id'],
                            'organisation_id' => $childData['organisation_id']
                        ],
                        ['created_by' => $childData['created_by']]
                    );
                }
            } else {
                foreach ($childRecords as $childData) {
                    TemporaryOrganisationChild::firstOrCreate(
                        [
                            'internal_people_id' => $childData['internal_people_id'],
                            'parent_id' => $childData['parent_id'],
                            'unique_id' => $childData['unique_id']
                        ]
                    );
                }
            }
        }
        $this->addDirectReportPopup = false;
        $this->setOrganisationChartData();
        if ($isEditing) {
            $this->sendAssessmentCriteriaAndNineBoxGridDataToUI();
        }

    }

    // Function to move sub-arrays dynamically
    private function moveSubArrayDynamically($array)
    {
        if (!is_array($array)) {
            return $array; // Return the original value if it's not an array
        }

        foreach ($array as $parentKey => &$subArray) {
            if (is_array($subArray)) {
                foreach ($subArray as $childKey => $value) {
                    if (isset($array[$childKey]) && is_array($array[$childKey])) {
                        // Ensure the key in the parent sub-array is an array
                        if (!isset($subArray[$childKey]) || !is_array($subArray[$childKey])) {
                            $subArray[$childKey] = [];
                        }

                        // Merge the child sub-array into the parent sub-array while preserving keys
                        $subArray[$childKey] = array_replace_recursive(
                            $subArray[$childKey],
                            $array[$childKey]
                        );

                        // Remove the original child sub-array
                        unset($array[$childKey]);
                    }
                }
            }
        }
        return $array;
    }

    public function validateStepOne()
    {
        $this->validate([
            'name' => 'required|string',
            'descriptions' => 'required|string'
        ]);

        $this->step++;
    }

    public function validateStepTwo()
    {

        $this->step++;
    }

    public function previousStep()
    {
        // Move to the previous step
        $this->step--;
    }

    public function setSelectedPeopleData($id)
    {
        $this->selectedPeopleData = InternalPeople::find($id);

        if ($this->selectedPeopleData) {
            $this->createdPlans = $this->selectedPeopleData->id;
            $this->dispatch('refreshComponent');
        } else {
            $this->addError('selectedPeopleData', 'Selected person data not found.');
        }
    }

    public function reviewPlan($planId) {
        $plan = SuccessionPlan::findOrFail($planId);

        if(!$this->editingOrganisationId) {
            $this->saveOrganisation($plan->name);
        }

        return redirect()->route('plan.show', ['plan' => $planId]);

    }


    public function createPlan($createPlanId)
    {
        // try {
        $this->validate([
            'name' => 'required|string'
        ]);

        $user = auth()->user();

        $planData = [
            'name'               => $this->name,
            'description'        => $this->descriptions,
            'minimum_experience' => $this->min_exp,
            'step_up'            => "1",
            'ethnicity'          => $this->ethnicity,
            'tagged_individual'  => $createPlanId,
            'age'                => 0,
            'status'             => "Draft",
            'candidate_status'   => "No Changes",
            'user_id'            => $user->id,
            "shared_with"        => "[]"
        ];

        if (!empty($this->selectedColleagues)) {
            $planData["shared_with"] = json_encode($this->selectedColleagues);
        }

        $plan = SuccessionPlan::create($planData);

        if (!empty($this->selectedColleagues)) {
            foreach ($this->selectedColleagues as $colleague) {
                Notifications::create([
                    'type'              => "Plan_Shared",
                    'plan_id'           => $plan->id,
                    'entity_name'       => $this->name,
                    'description'       => "{$user->name} has shared plan {$plan->name} with you.",
                    'user_id'           => $colleague,
                    'user_company'      => $user->company_id
                ]);
            }
        }

        Notifications::create([
            'type'              => "Plan_Created",
            'plan_id'           => $plan->id,
            'entity_name'       => $this->name,
            'description'       => $this->descriptions,
            'user_id'           => $user->id,
            'user_company'      => $user->company_id
        ]);

        foreach ($this->mutipleSelectionData['targetRoles'] as $role) {
            $role = trim($role);
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => $role,
                'type' => "Role",
            ]);
        }

        // Getting any step_up candidates
        foreach ($this->mutipleSelectionData['stepUpCandidate'] as $stepUpCandidate) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' =>  trim($stepUpCandidate),
                'type' => "step_up",
            ]);
        }
        // Getting keyword
        foreach ($this->mutipleSelectionData['keyword'] as $keyword) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' =>  trim($keyword),
                'type' => "keyword",
            ]);
        }

        if ($this->min_exp) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => $this->min_exp,
                'type' => "Minimum_Tenure"
            ]);
        }
        if (!empty($this->selectedCountries)) {
            foreach ($this->selectedCountries as $country) {
                SuccessRequirements::create([
                    'plan_id' => $plan->id,
                    'name' => $country,
                    'type' => "location"
                ]);
            }
        }

        // Get any another skills into the requirements table
        foreach ($this->mutipleSelectionData['skills'] as $skill) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => trim($skill),
                'type' => "professional_skill"
            ]);
        }

        // This will be use for qualifications
        // Getting any Educational Qualifications Requirements
        foreach ($this->mutipleSelectionData['qualifications'] as $qualification) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => trim($qualification),
                'type' => "education"
            ]);
        }

        if ($this->gender == 1) {
            $gen = 'Female';
        } elseif ($this->gender == 2) {
            $gen = 'Male';
        } else {
            $gen = '';
        }

        if ($gen && $gen != '') {
            SuccessRequirements::create(
                [
                    'plan_id' => $plan->id,
                    'name'    => trim($gen),
                    'type'    => 'Gender',
                ]
            );
        }

        if (!empty($this->selectedCompanies)) {
            foreach ($this->selectedCompanies as $cr) {
                SuccessRequirements::create([
                    'plan_id' => $plan->id,
                    'name'    => trim($cr),
                    'type'    => 'Company',
                ]);
            }
        }

        // if (!empty($this->selectedSectors)) {
        //     foreach ($this->selectedSectors as $sector) {
        //         SuccessRequirements::create([
        //             'plan_id' => $plan->id,
        //             'name'    => trim($sector),
        //             'type'    => 'Sector',
        //         ]);
        //     }
        // }

        if (!empty($this->selectedIndustries)) {
            foreach ($this->selectedIndustries as $industry) {
                SuccessRequirements::create([
                    'plan_id' => $plan->id,
                    'name'    => trim($industry),
                    'type'    => 'Industry',
                ]);
            }
        }

        $rawRoles = $this->mutipleSelectionData['targetRoles'];
        $proles = array_map('trim', $rawRoles);

        $rawFunc = $this->function;
        $pFuncs = array_map('trim', explode(',', $rawFunc));

        $rawsteps = $this->mutipleSelectionData['stepUpCandidate'];
        $psteps = array_map('trim', $rawsteps);

        $rawKeyword = $this->mutipleSelectionData['keyword'];
        $pKeyword = array_map('trim', $rawKeyword);

        // Qualifcications list
        $rawEdQual = $this->mutipleSelectionData['qualifications'];
        $pEdQual = array_map('trim', $rawEdQual);

        // Location list
        $ploc = $this->selectedCountries;

        // Skills List
        $rawskill = $this->mutipleSelectionData['skills'];
        $pskills = array_map('trim', $rawskill);
        $skillCount = count($pskills);

        //dd("working");

        //--------------- Start the filtering for the pipeline --------------//
        $scountry = $this->selectedCountries;
        $scities   = $this->selectedCities;

        $filteredCompanyIdsArr = [];
        // if (!empty($this->selectedSectors) && !empty($this->selectedIndustries)) {
        //     $filteredCompanyIdsArr = Company::whereIn('industry', $this->selectedIndustries)
        //                                         ->whereIn('sector', $this->selectedSectors)
        //                                         ->pluck('id')
        //                                         ->toArray();
        // }
        // else if (!empty($this->selectedSectors)) {
        //     $filteredCompanyIdsArr = Company::whereIn('sector', $this->selectedSectors)
        //                                     ->pluck('id')
        //                                     ->toArray();
        // }
        // else if (!empty($this->selectedIndustries)) {
        if (!empty($this->selectedIndustries)) {
            $filteredCompanyIdsArr = Company::whereIn('industry', $this->selectedIndustries)
                                                ->pluck('id')
                                                ->toArray();
        }

        // This will filter the People table where the roles, gender, company_name, country and city 
        if (!empty($proles)) {
            $filteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id', '!=', $user->company_id)
                ->where(function ($query) use ($proles, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                    $query->whereIn('latest_role', $proles);
                    // The statement below will filter by the company_name
                    if (!empty($this->selectedCompanies)) {
                        $query->WhereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }

                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($scountry)) {
                        $query->WhereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->WhereIn('city', $scities);
                    }
                })
                ->get();

            $filteredPeople = $filteredPeople->map(function ($item) {
                $item['role_score'] = 1;
                return $item;
            });
        } else {
            $filteredPeople = new Collection();
        }

        $filteredPeopleidslvl1 = $filteredPeople->pluck('id');


        if ($rawsteps !== "") {
            $sfilteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id', '!=', $user->company_id)
                ->whereNotIn('id', $filteredPeopleidslvl1)
                ->when($rawsteps !== "", function ($query) use ($psteps, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                    $query->whereIn('latest_role', $psteps);
                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->WhereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }

                    if (!empty($scountry)) {
                        $query->WhereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->WhereIn('city', $scities);
                    }
                })
                ->get();

            $sfilteredPeople = $sfilteredPeople->map(function ($item) {
                $item['role_score'] = 0.75;
                return $item;
            });
            $sfilteredPeopleidslvl1 = $sfilteredPeople->pluck('id');
        } else {
            $sfilteredPeople = [];
            $sfilteredPeopleidslvl1 = [];
        }

        if (!empty($pKeyword)) {
            $kfilteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id', '!=', $user->company_id)
                ->whereNotIn('id', $filteredPeopleidslvl1)
                ->whereNotIn('id', $sfilteredPeopleidslvl1)
                ->when($rawsteps !== "", function ($query) use ($pKeyword, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                    $query->where(function ($subquery) use ($pKeyword) {
                        // Loop through each keyword and add an orWhere clause
                        foreach ($pKeyword as $keyword) {
                            $subquery->orWhere('latest_role', 'like', '%' . $keyword . '%');
                        }
                    });

                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->whereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }

                    if (!empty($scountry)) {
                        $query->whereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->whereIn('city', $scities);
                    }
                })
                ->get();
            $kfilteredPeople = $kfilteredPeople->map(function ($item) {
                $item['role_score'] = 0.25;
                return $item;
            });

            //dd($kfilteredPeople);

            $kfilteredPeopleidslvl1 = $kfilteredPeople->pluck('id');
        } else {
            $kfilteredPeople = [];
            $kfilteredPeopleidslvl1 = [];
        }

        $career_history_filtered = CareerHistories::query()
            ->whereNotIn('people_id', $filteredPeopleidslvl1)
            ->whereNotIn('people_id', $sfilteredPeopleidslvl1)
            ->whereNotIn('people_id', $kfilteredPeopleidslvl1)
            ->where(function ($query) use ($proles, $psteps) {
                $query->WhereIn('role', $proles)
                    ->orWhereIn('role', $psteps);
            })
            ->get();
        $filteredCareerHistoryIds = $career_history_filtered->pluck('people_id');

        if ($filteredCareerHistoryIds !== null) {
            $careerPeople = People::query()
                ->whereIn('id', $filteredCareerHistoryIds)
                ->where('company_id', '!=', $user->company_id)
                ->when($filteredCareerHistoryIds  !== null, function ($query) use ($proles, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($scountry)) {
                        $query->WhereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->WhereIn('city', $scities);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->WhereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                })
                ->get();

            $careerPeople = $careerPeople->map(function ($item) {
                $item['role_score'] = 0.5;
                return $item;
            });

            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
            $filteredPeople = $filteredPeople->concat($careerPeople);
            $filteredPeople = $filteredPeople->concat($kfilteredPeople);
        } else {
            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
        }
        $educationCounts = People::query()
            ->whereIn('people.educational_history', $pEdQual)
            ->join('people as filtered_people', 'people.id', '=', 'filtered_people.id')
            ->groupBy('people.id')
            ->selectRaw('people.id, COUNT(filtered_people.id) as education_match_count')
            ->get();
        $educationMatchCountMap = $educationCounts->pluck('education_match_count', 'id');
        $filteredPeopleWithEducationCount = $filteredPeople->map(function ($person) use ($educationMatchCountMap) {
            $person->education_match_count = $educationMatchCountMap->get($person->id, 0);
            return $person;
        });

        //------------------------- Calculate Location Score ---------------------//

        if (!empty($this->selectedCountries)) {

            $filteredPeopleWithEdLocMatches = $filteredPeople->whereIn('country', $ploc);

            $filteredPeopleWithEdLocMatches = $filteredPeopleWithEducationCount->map(function ($person) {
                $person->location_match_count = 1;
                return $person;
            });
        } else {
            $filteredPeopleWithEdLocMatches = $filteredPeopleWithEducationCount->map(function ($person) {
                $person->location_match_count = 1;
                return $person;
            });
        }

        $skillsScoreCount = Skills::query()
            ->whereIn('skill_name', $pskills)
            ->groupBy('people_id')
            ->selectRaw('skills.people_id, COUNT(skills.people_id) / ? as skill_score', ['skillCount' => $skillCount])
            ->get();
        $skillsScoreMap = $skillsScoreCount->pluck('skill_score', 'people_id');
        $filteredPeopleWithMatches = $filteredPeopleWithEdLocMatches->map(function ($person) use ($skillsScoreMap) {
            $person->skill_score = $skillsScoreMap->get($person->id, 0);
            return $person;
        });


        $filteredPeopleWithMatches->each(function ($person) {
            $person->gender_score = ($person->gender === $this->gender) ? 1 : 0;
        });
        
        if ($this->min_exp !== null) {

            $filteredPeopleWithMatches = $filteredPeopleWithMatches->map(function ($person) {
                $tenancyDifference = $person->tenure - $this->min_exp;
                $tenancyDifferenceAbsolute = abs($tenancyDifference);

                if ($tenancyDifference < 0) {
                    if ($tenancyDifferenceAbsolute <= 2) {
                        $person->tenancy_score = 0;
                    } else {
                        $person->tenancy_score = 1;
                    }
                } else {
                    $person->tenancy_score = 1;
                }
                return $person;
            });
        } else {
            $filteredPeopleWithMatches = $filteredPeopleWithMatches->map(function ($person) {
                $person->tenancy_score = 1;
                return $person;
            });
        }

        
        $filteredPeopleWithMatches->each(function ($person) use ($plan, $user) {
            pipeline::create([
                'plan_id'            => $plan->id,
                'user_id'            => $user->id,
                'people_id'          => $person->id,
                'first_name'         => $person->forename,
                'last_name'          => $person->surname,
                'middle_name'        => $person->middle_name,
                'other_name'         => $person->other_name,
                'gender'             => $person->gender,
                'diverse'            => $person->diverse,
                'location'           => $person->location,
                'country'            => $person->country,
                'city'               => $person->city,
                'summary'            => $person->summary,
                'linkedinURL'        => $person->linkedinURL,
                'latest_role'        => $person->latest_role,
                'company_id'         => $person->company_id,
                'company_name'       => $person->company_name,
                'start_date'         => $person->start_date,
                'end_date'           => $person->end_date,
                'tenure'             => $person->tenure,
                'function'           => $person->function,
                'division'           => $person->division,
                'seniority'          => $person->seniority,
                'exco'               => $person->exco,
                'career_history'     => $person->career_history,
                'educational_history' => $person->educational_history,
                'skills'             => $person->skills,
                'languages'          => $person->languages,
                'skills_match'       => $person->skill_score,
                'education_match'    => $person->education_match_count,
                'location_match'     => $person->location_match_count,
                'role_match'         => $person->role_score,
                'readiness'          => $person->readiness,
                'other_tags'         => $person->other_tags,
                'gender_match'       => $person->gender_score,
                'tenure_match'       => $person->tenancy_score,
                'total_score'        => $person->skill_score + $person->education_match_count + $person->location_match_count + $person->role_score + $person->tenancy_score,
                'people_type'        => 'External-System'
            ]);
        });
        $this->reset(['name', 'roles', 'enteredRoles', 'selectedTaggedRole', 'selectedColleagues', 'qualifications', 'newSkills', 'division', 'function', 'stepup', 'descriptions', 'min_exp', 'education', 'skills', 'location', 'gender', 'ethnicity', 'selectedCountries', 'selectedCompanies', 'selectedIndustries', 'troles', 'newSkillData', 'mutipleSelectionData']);
        $this->step++;
        $this->plan = $plan;
        $this->createdPlans = $createPlanId;
        $this->createdPlansId[$this->createdPlans] = $plan->id;
        // } catch (Exception $e) {
        //     Log::error($e);
        // }
    }

    public function changeMinimumTenure($flag)
    {
        if ($flag == 'decrease') {
            $this->min_exp = $this->min_exp <= 1 ? null : ($this->min_exp - 1);
        } else if ($flag == 'increase') {
            $this->min_exp = !$this->min_exp ? 1 : ($this->min_exp + 1);
        }
    }

    public function modalClosed()
    {
        $this->step = 1;
        $this->reset(['forename', 'surname', "gender", "roles", "function", "division", "start_date", "empid", "location", "newSkillData", "mutipleSelectionData"]);
    }

    public function showChildPeoples($parentPeopleId)
    {

        $this->subChildPoeples = [];
        $this->parentPeopleId = null;

        if (
            isset($this->selectedAddDirectReportPeoples) &&
            array_key_exists($parentPeopleId, $this->selectedAddDirectReportPeoples)
        ) {
            $this->subChildPoeples = array_keys($this->selectedAddDirectReportPeoples[$parentPeopleId]);
            $this->parentPeopleId = $parentPeopleId;
        }
    }

    public function viewOrganisation($id)
    {
        $user = auth()->user();
        $this->showOranisationListing = false;
        $organistaionDetail = Organisation::where('id', $id)->first();
        $this->organistaionDetailObj = $organistaionDetail;

        if (empty($organistaionDetail))
            request()->session()->flash('error', "Organisation not found");
        
        if(!empty($organistaionDetail->shared)) {
            $sharedWith = json_decode($organistaionDetail->shared, true);

            if(in_array(auth()->user()->id, $sharedWith)) {
                $this->canAccessAssessmentCriteria = true;
            }
        }

        $this->editingOrganisationId = $id;

        $organisation = Organisation::find($this->editingOrganisationId);

        $this->organisationName = $organistaionDetail->title;
        $this->selectedUsers = OrganisationPeople::where('organisation_id', $id)->distinct('internal_people_id')->pluck('internal_people_id')->toArray();
        $this->dispatchSelectedPeoplesForOrganisation();
        $this->setOrganisationChartData();
        $this->sendAssessmentCriteriaAndNineBoxGridDataToUI();
        $this->dispatch('shareOrganisationData',$organisation->shared ? json_decode($organisation->shared): []);

    }

    public function setEditAssessmentCriteria($id) {
        $internalPeople = InternalPeople::find($id);
        $this->assessmentCriterias = AssessmentCriteria::where(['company_id' => $internalPeople->company_id, 'type' => 'Assessment Criteria'])->get();
        $organisationPeople = OrganisationPeople::where(['organisation_id' => $this->editingOrganisationId, 'internal_people_id' => $internalPeople->id])->first();
        if(!empty($organisationPeople)) {
            $this->editAssessmentCriterias = collect($this->assessmentCriterias)->map(function($criteria) use ($organisationPeople) {
                $savedAssessmentCriterias = InternalRequirement::where([
                    'organisation_people_id' => $organisationPeople->id,
                    'assessment_criteria_id' => $criteria->id
                ])->first();
            
                return [
                    'organisation_people_id' => $organisationPeople->id,
                    'criteria_id' => $criteria->id, // Ensure criteria_id is explicitly set
                    'label' => $criteria->label, // Ensure criteria_id is explicitly set
                    'isChecked' => $savedAssessmentCriterias ? $savedAssessmentCriterias->response : $criteria->response, // Set default or retrieved value
                    'notes' => $savedAssessmentCriterias ? $savedAssessmentCriterias->note : '', // Set notes or empty string
                ];
            })->toArray();
        }

        return $this->editAssessmentCriterias;
        
    }

    public function set9BoxGrid($id) {
        $internalPeople = InternalPeople::find($id);
        $nineBoxGridData = AssessmentCriteria::where(['company_id' => $internalPeople->company_id, 'type' => '9box'])->get();
        $organisationPeople = OrganisationPeople::where(['organisation_id' => $this->editingOrganisationId, 'internal_people_id' => $internalPeople->id])->first();
        if(!empty($organisationPeople)) {
            $this->editNineBoxGrid = collect($nineBoxGridData)->map(function($nineBoxGrid) use ($organisationPeople) {
                $saved9BoxGrid = InternalRequirement::where([
                    'organisation_people_id' => $organisationPeople->id,
                    'assessment_criteria_id' => $nineBoxGrid->id
                ])->first();
                if(!empty($saved9BoxGrid)) {
                    $this->selectedOption = $saved9BoxGrid->response;
                }
            
                return [
                    'organisation_people_id' => $organisationPeople->id,
                    'criteria_id' => $nineBoxGrid->id, // Ensure criteria_id is explicitly set
                    'label' => $nineBoxGrid->label, // Ensure criteria_id is explicitly set
                    'response' => $saved9BoxGrid->response ?? 0
                ];
            })->toArray();
        }

        return $this->editNineBoxGrid;
        
    }

    public function updateIndividualPeople($id) {
        $internalPeople = InternalPeople::with('careerHistory.company', 'internalSkills', 'notes.user')
                                        ->where(['id' => $id])
                                        ->where('forename', '!=', 'Vacant')
                                        ->first();
        $this->dispatch('updateIndividualPeople', $internalPeople);

    }

    public function addIndividualPeople($id) {
        $internalPeople = InternalPeople::with('careerHistory.company', 'internalSkills', 'notes.user')
                                        ->where(['id' => $id])
                                        ->where('forename', '!=', 'Vacant')
                                        ->first();
        $this->dispatch('addIndividualPeople', $internalPeople);

    }

    public function addNotes($id)
    {
       
        $user = auth()->user();
        $userNoteData = [
            'entity_id' => $id,
            'entity_type' => 'internal_peoples',
            'Notes' => $this->filteredPnotes,
            'author' => $user->id
        ];
        UserNotes::create($userNoteData);

        $this->updateIndividualPeople($id);
        $this->nopen = false;
    }

    public function shareOrganisation($data)
    {
        $filteredArray = array_filter($data, function ($value) {
            return $value === true;
        });

        // Get the keys of the filtered array
        $filteredArray = array_keys($filteredArray);
        $organisation = Organisation::find($this->editingOrganisationId);
        $organisation->update(['shared' => $filteredArray]);
        $this->shareOrgansationPopup = false;
        $this->dispatch('toast', 'info', "Organisation shared successfully");
    }


    public function addNewCompany()
    {
        $company = Company::create([
            'name' => $this->newCompanyName
        ]);
        $this->pastCompany = $company->id;
        $this->newCompanyName = null;
    }

    public function searchPeoples($query)
    {
        $this->searchPeople = $query;
    }

    public function setAssessmentCriteria() {
        $user = auth()->user();
        $this->criteriaRows = AssessmentCriteria::where('type', 'Assessment Criteria')
                                                ->where('company_id', $user->company_id)
                                                ->get(['id', 'label as criteria', 'response as isChecked'])
                                                ->map(function ($item) {
                                                    return [
                                                        'id' => $item->id,
                                                        'criteria' => $item->criteria,
                                                        'isChecked' => $item->isChecked,
                                                        'deleted' => false, // Initialize deleted flag to false
                                                    ];
                                                })
                                                ->toArray();
    }

    public function saveCriteria()
    {
        if (!$this->isAdminUser && !$this->isMasterUser) {
            $this->dispatch('toast', 'info', 'You are not allowed to perform this action!');
            return;
        }
        // Validation rules and messages remain the same
        $rules = [
            'criteriaRows.*.criteria' => 'required|string|max:255',
            'criteriaRows.*.isChecked' => 'in:Yes,No',
        ];
    
        $messages = [
            'criteriaRows.*.criteria.required' => 'The criteria field cannot be empty.',
            'criteriaRows.*.criteria.string' => 'The criteria must be a valid string.',
            'criteriaRows.*.criteria.max' => 'The criteria may not be greater than 255 characters.',
            'criteriaRows.*.isChecked.in' => 'The checkbox value must be "Yes" or "No".',
        ];
    
        $this->validate($rules, $messages);
    
        $user = auth()->user();
    
        foreach ($this->criteriaRows as $row) {
            // Skip rows marked for deletion
            if (!empty($row['deleted'])) {
                // If the row has an ID, delete the corresponding database entry
                if ($row['id']) {
                    AssessmentCriteria::where('id', $row['id'])->delete();
                }
                continue;
            }
    
            // Determine if the row needs to be created or updated
            if (isset($row['id']) && $row['id']) {
                // Update existing record
                AssessmentCriteria::where('id', $row['id'])->update([
                    'label' => $row['criteria'],
                    'response' => $row['isChecked'],
                    'type' => 'Assessment Criteria',
                    'company_id' => $user->company_id,
                    'created_by' => $user->id,
                ]);
            } else {
                // Create a new record
                AssessmentCriteria::create([
                    'label' => $row['criteria'],
                    'response' => $row['isChecked'],
                    'type' => 'Assessment Criteria',
                    'company_id' => $user->company_id,
                    'created_by' => $user->id,
                ]);
            }
        }
    
        // Refresh the criteria rows from the database after saving
        $this->setAssessmentCriteria();
    
        // Close the modal
        $this->assessmentCriteriaPopup = false;
    
        // Flash success message
        $this->dispatch('toast', 'info', "Criteria updated successfully!");
        return $this->skipRender();
    }

    public function editIndividualCriteria($editAssessmentCriterias, $internalPeopleId) {

    foreach ($editAssessmentCriterias as $data) {
        // Prepare the data to be inserted or updated
        $dataToUpdate = [
            'organisation_people_id' => $data['organisation_people_id'],
            'assessment_criteria_id' => $data['criteria_id'],
            'response' => ((!empty($data['isChecked'] && is_bool($data['isChecked']))) || $data['isChecked'] === "Yes") ? "Yes" : "No",
            'note' => $data['notes'],
        ];

        // Use updateOrInsert to update existing or insert new records
        InternalRequirement::updateOrInsert(
            [
                'organisation_people_id' => $data['organisation_people_id'],
                'assessment_criteria_id' => $data['criteria_id'],
            ], // Conditions to match existing records
            $dataToUpdate // Data to update or insert
        );
    }

    $this->setEditAssessmentCriteria($internalPeopleId);

    $this->nopen = false;

    $this->dispatch('toast', 'info', "Assessment criteria updated successfully!");

    }

    public function saveGridLabels()
    {
        $user = auth()->user();
        // Validate the grid labels
        $this->validate([
            'gridLabels.*' => 'required|string|max:255',
        ]);
    
        // Loop through each grid label and save it to the database
        foreach ($this->gridLabels as $index => $label) {
            AssessmentCriteria::updateOrCreate(
                [
                    'company_id' => $user->company_id,
                    'response' => $index,
                    'type' => '9box'
                ],
                [
                    'label' => $label,
                    'created_by' => $user->id
                ]
            );
        }

        $this->nineboxGridPopup = false;

        $this->dispatch('toast', 'info', 'Grid labels have been saved successfully.');
        return $this->skipRender();
    
    }

    public function edit9BoxGrid($nineBoxGridData, $internalPeopleId) {

        if(!empty($nineBoxGridData)) {
            $criteriaIds = array_column($nineBoxGridData, 'criteria_id');
            $filteredData = array_values(array_filter($nineBoxGridData, function ($item) {
                return $item['response'] === 1;
            }));
            $filteredData = reset($filteredData);

            InternalRequirement::where(['organisation_people_id' => $filteredData['organisation_people_id']])->whereIn('assessment_criteria_id', $criteriaIds)->delete();

            $dataToCreate = [
                'organisation_people_id' => $filteredData['organisation_people_id'],
                'assessment_criteria_id' => $filteredData['criteria_id'],
                'response'               => $filteredData['response']
            ];

            InternalRequirement::create($dataToCreate);
            

        }
        $this->set9BoxGrid($internalPeopleId);

        $this->nineboxGridOpen = false;
    }

    public function updateInternalPeople($data) {
        $user = auth()->user();
        $peopleId = $data['id'];

        $dataToUpdate = [
            'function' => $data['function'],
            'division' => $data['division'],
            'other_tags' => $data['other_tags'],
            'readiness' => $data['readiness'],
            'summary'   => $data['summary'],
            'linkedinURL' => $data['linkedinURL']
        ];

        $internalPeople = InternalPeople::find($peopleId);
        $internalPeople->update($dataToUpdate);

        // Save each skill type to the database

        InternalSkills::where(['skill_type' => 'Specialised', 'internal_people' => $peopleId])->delete();
        foreach ($data['newSkillData']['specialised'] as $specialised) {
            InternalSkills::create(
                [
                    'skill_name' => $specialised,
                    'internal_people' => $peopleId,
                    'company_id' => $user->company_id,
                    'skill_type' => "Specialised"
                ]
            );
        }
        InternalSkills::where(['skill_type' => 'Common', 'internal_people' => $peopleId])->delete();
        foreach ($data['newSkillData']['common'] as $common) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $common,
                    'internal_people' => $peopleId,
                    'company_id' => $user->company_id,
                    'skill_type' => "Common"
                ]
            );
        }

        InternalSkills::where(['skill_type' => 'Certification', 'internal_people' => $peopleId])->delete();
        foreach ($data['newSkillData']['certification'] as $certification) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $certification,
                    'internal_people' => $peopleId,
                    'company_id' => $user->company_id,
                    'skill_type' => "Certification"
                ]
            );
        }

        $this->updateInternalpeoplePopup = false;
        $this->dispatch('toast', 'info', 'Internal people updated successfully!');
        return $this->skipRender();

    }

    public function updateCareerHistory($data) {
        $internalPeopleId = $data['id'];
        $careerHistoryData = $data['careerHistory'];

        $internalPeople = InternalPeople::find($internalPeopleId);
    
        // Delete existing career histories for the individual
        internal_career_histories::where(['people_id' => $internalPeopleId])->delete();

    
        foreach ($careerHistoryData as $index => $history) {
            // Find or create the company
            $company = Company::where('name', trim($history['company']['name']))->first();
    
            if (empty($company)) {
                $company = Company::create([
                    'name' => $history['company']['name'],
                    'location_id' => 1784674685,
                    'status' => 'submitted',
                ]);
            }
    
            // Calculate role-specific tenure
            $startDate = Carbon::parse($history['start_date']);
            $endDate = !empty($history['end_date']) ? Carbon::parse($history['end_date']) : Carbon::now();
            $roleTenure = $startDate->diffInYears($endDate); // Tenure in this specific role
    
            $dataToInsert = [
                'people_id'       => $internalPeopleId,
                'role'            => $history['role'],
                'past_company_id' => $company->id,
                'start_date'      => $history['start_date'],
                'end_date'        => !empty($history['end_date']) ? $history['end_date'] : null,
                'tenure'          => $roleTenure, // Tenure for this role
            ];
    
            // Insert the career history record for this role
            internal_career_histories::create($dataToInsert);
    
        }

        $latestCareerHistory = internal_career_histories::where(['people_id' => $internalPeopleId])->orderBy('start_date', 'desc')->first();
        if(!empty($latestCareerHistory)) {
             // Fetch all career histories for the same company and calculate the earliest start date and latest end date
            $companyRecords = internal_career_histories::where([
                'people_id' => $internalPeopleId,
                'past_company_id' => $latestCareerHistory->past_company_id
            ])->get(['start_date', 'end_date']);

            // Determine the earliest start date and the latest end date (or current date if end_date is null)
            $earliestStartDate = $companyRecords->min('start_date');
            $latestEndDate = $companyRecords->max('end_date') ?? Carbon::now();

            foreach ($companyRecords as $record) {
                if (is_null($record->end_date)) {
                    // If any end_date is null, set latestEndDate to current date and break the loop
                    $latestEndDate = Carbon::now()->toDateString();
                    break;
                } 
            }

            // Calculate tenure in the company
            $tenureInCompany = Carbon::parse($earliestStartDate)->diffInYears(Carbon::parse($latestEndDate));

            $internalPeople->update(['tenure_in_company' => $tenureInCompany]);


        }
        $this->updateInternalpeoplePopup = false;
        $this->editPeopleCareerHistoryPopup = false;
        $this->updateIndividualPeople($internalPeopleId);
        $this->dispatch('toast', 'info', 'Career history updated successfully!');
    }

    public function updateTenureInCompany() {

        $internalPeoples = InternalPeople::get();

        foreach($internalPeoples as $internalPeople) {
            

            $latestCareerHistory = internal_career_histories::where(['people_id' => $internalPeople->id])->orderBy('start_date', 'desc')->first();
            if(!empty($latestCareerHistory)) {
                // Fetch all career histories for the same company and calculate the earliest start date and latest end date
                $companyRecords = internal_career_histories::where([
                    'people_id' => $internalPeople->id,
                    'past_company_id' => $latestCareerHistory->past_company_id
                ])->get(['start_date', 'end_date']);

                // Determine the earliest start date and the latest end date (or current date if end_date is null)
                $earliestStartDate = $companyRecords->min('start_date');
                $latestEndDate = $companyRecords->max('end_date') ?? Carbon::now();

                foreach ($companyRecords as $record) {
                    if (is_null($record->end_date)) {
                        // If any end_date is null, set latestEndDate to current date and break the loop
                        $latestEndDate = Carbon::now()->toDateString();
                        break;
                    } 
                }

                // Calculate tenure in the company
                $tenureInCompany = Carbon::parse($earliestStartDate)->diffInYears(Carbon::parse($latestEndDate));

                $internalPeople->update(['tenure_in_company' => $tenureInCompany]);


            }
        }

    }
    
    
    
    
}
