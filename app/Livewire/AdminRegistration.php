<?php

namespace App\Livewire;

use App\Models\Account;
use App\Models\Invite;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use App\Models\People;
use App\Models\InternalPeople;
use App\Models\Skills;
use App\Models\InternalSkills;
use App\Models\internal_career_histories;
use App\Models\CareerHistories;
use Illuminate\Support\Facades\DB;

class AdminRegistration extends Component
{
    public $name;
    public $data;
    public $password;
    public $confirmedpassword;
    public $company;
    public $message;

    public function mount($data)
    {
        $this->data = $data;
    }

    public function render()
    {
        return view('livewire.admin-registration');
    }

    public function sendVerification()
    {
        // dd($this->data);
        $this->validate([
            'name' => 'required|string|max:255',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*[!@#$%^&*(),.?":{}|<>])/u' // Ensure password contains at least one special character
            ],
            'confirmedpassword' => 'required|string|same:password',
        ], [
            'password.regex' => 'The password must contain at least one special character.',
            'confirmedpassword.same' => 'The password confirmation does not match.',
        ]);
        $plan_limits = 0;

        switch ($this->data['account_type']) {
            case 'Silver':
            case 'Gold':
                $plan_limits = ********;
                break;
            case 'Platinum':
                $plan_limits = ********;
                break;
        }

        $invite = Invite::where([
            'email' => $this->data['email'],
            'user_id' => $this->data['id'],
            'company_id' => $this->data['company'],
            'url' => $this->data['url']
        ])->first();

        if (!$invite || $invite && $invite->used) {
            $this->dispatch('toast', 'info', 'URL expired');
            return;
        }

        DB::transaction(function () use ($plan_limits, $invite) {
            $userExists = User::where('email', $this->data['email'])->exists();
            if ($userExists) {
                $this->dispatchBrowserEvent('toast', ['type' => 'info', 'message' => "This email already exists."]);
                return;
            }

            if (is_null($this->data['accountId'])) {
                $account = Account::create([
                    'Account_name' => $this->data['account_name'],
                    'account_type' => $this->data['account_type'],
                    'company_id'   => $this->data['company'],
                    'users_limit'  => $this->data['userLimit'],
                    'plans_limit'  => $plan_limits,
                    'active_users' => 1,
                    'relationship_manager_id' => implode(',', $this->data['manager_id']),
                    "sector_interest" => implode(",", $this->data['sector']),
                    "industry_interest" => implode(",", $this->data['industry']),
                    "company_of_interest" => implode(",", $this->data['companyInterest']),
                ]);
            } else {
                $account = Account::find($this->data['accountId']);
                if (empty($account)) {
                    $this->dispatchBrowserEvent('toast', ['type' => 'info', 'message' => "Account not found."]);
                    return;
                }
            }

            $user = User::create([
                'name'          => $this->name,
                'image_url'     => 'Not required',
                'team'          => 'Admin',
                'company_id'    => $this->data['company'],
                'account_id'    => $account->id,
                'role'          => 'admin',
                'email'         => $this->data['email'],
                'password'      => bcrypt($this->password), // Ensure the password is hashed
                'profile_pic'   => 'Not Provided'
            ]);

            $isAlreadyExists = InternalPeople::where('company_id', $this->data['company'])->first();

            if (empty($isAlreadyExists)) {
                $peoples = People::where('company_id', $this->data['company'])->get();

                // Extracting the ids for the filters
                $peopleIds = $peoples->pluck('id');

                // Made an empty list called internal People Data so that we can insert the people in bulk
                $internalPeopleData = [];
                $careerHistoriesData = [];
                $skillsData = [];
                foreach ($peoples as $people) {
                    $internalPeopleData[] = [
                        'id'                    => *********** + $people->id,
                        'forename'              => $people->forename,
                        'middle_name'           => $people->middle_name,
                        'other_name'            => $people->other_name,
                        'surname'               => $people->surname,
                        'gender'                => $people->gender,
                        'diverse'               => $people->diverse,
                        'country'               => $people->country,
                        'city'                  => $people->city,
                        'linkedinURL'           => $people->linkedinURL,
                        'latest_role'           => $people->latest_role,
                        'location'              => $people->country ?? "N/A",
                        'exco'                  => $people->exco,
                        'company_id'            => $people->company_id,
                        'company_name'          => $people->company_name,
                        'start_date'            => $people->start_date,
                        'end_date'              => $people->end_date,
                        'tenure'                => $people->tenure,
                        'function'              => $people->function,
                        'division'              => $people->division,
                        'seniority'             => $people->seniority,
                        'career_history'        => $people->career_history,
                        'educational_history'   => $people->educational_history,
                        'skills'                => $people->skills,
                        'languages'             => $people->languages,
                        'other_tags'            => $people->other_tags,
                        'status'                => $people->status,
                        'readiness'             => $people->readiness,
                        'user_id'               => $user->id,
                        'summary'               => $people->summary,
                    ];
                
                }

                
                // Inserting the internalPeopleData list into Internal People
                if (!empty($internalPeopleData)){
                    DB::transaction(function () use ($internalPeopleData) {
                        foreach (array_chunk($internalPeopleData, 2000) as $chunk) {
                            DB::table('internal_people')->insert($chunk);
                        }
                    });
                    DB::commit();
                }


                // Process career histories in chunks
                CareerHistories::whereIn('people_id', $peopleIds)
                    ->chunk(4000, function ($careerHistories) use (&$careerHistoriesData, $people) {
                        foreach ($careerHistories as $careerHistory) {
                            $careerHistoriesData[] = [
                                "people_id" => *********** + $careerHistory->people_id,
                                "role" => $careerHistory->role,
                                "past_company_id" => $careerHistory->past_company_id,
                                "start_date" => $careerHistory->start_date,
                                "end_date" => $careerHistory->end_date,
                                "tenure" => $careerHistory->tenure,
                            ];

                        // Insert career histories in batches to avoid memory overload
                        if (count($careerHistoriesData) >= 4000) {
                            internal_career_histories::insert($careerHistoriesData); // Correct model name
                            $careerHistoriesData = []; // Clear array after insert
                            }
                        }


                    });
                
                if (!empty($skillsData)) {
                    internal_career_histories::insert($careerHistoriesData);
                }

                // Process skills in chunks
                $companyData = $this->data['company']; // Assign $this->data['company'] to a variable
                
                $skills_list = Skills::whereIn('people_id', $peopleIds)
                    ->chunk(4000, function ($skills) use (&$skillsData, $people,  $companyData) {
                    foreach ($skills as $skill) {
                            $skillsData[] = [
                                "internal_people" => *********** + $skill->people_id,
                                "skill_type" => $skill->skill_type,
                                "skill_name" => $skill->skill_name,
                                "skill_rating" => $skill->skill_rating,
                                "company_id" => $companyData, // Use the variable instead of $this->data
                            ];

                    
                            // If batch size reached insert the data
                            if (count($skillsData)>=4000){
                                InternalSkills::insert($skillsData);
                                $skillsData = [];
                            }        
                    }
                    });

                    if (!empty($skillsData)) {
                        InternalSkills::insert($skillsData);
                    }
                }

            if ($invite) {
                $invite->update(['used' => true]);

                if (!Auth::check()) {
                    return redirect()->route('login');
                }
            }

            $this->dispatch('toast', 'info', "Registered Successfully.");
            // $this->dispatchBrowserEvent('toast', ['type' => 'info', 'message' => "Registered Successfully."]);
        });
    }
}
