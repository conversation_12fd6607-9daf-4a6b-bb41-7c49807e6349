<?php

namespace App\Livewire;

use App\Models\Company;
use Livewire\Component;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Database\Eloquent\Collection;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use App\Models\SuccessionPlan;
use App\Models\Skills;
use App\Models\InternalSkills;
use App\Models\SuccessPeople;
use App\Models\InternalPeople;
use App\Models\SuccessRequirements;
use App\Models\PlanScores;
use App\Models\People;
use App\Models\Location;
use App\Models\CareerHistories;
use App\Models\pipeline;
use App\Models\notifications;
use App\Models\User;
use App\Models\Account;
use Illuminate\Support\Facades\Validator;
use App\Services\DataPreloader;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\SuccessSkills;
use App\Models\UserNotes;
use App\Models\Role;
use Carbon\Carbon;
use App\Models\internal_career_histories;
use OpenAI\Laravel\Facades\OpenAI;

class PlansTable extends Component
{

    protected $listeners = [
        'modalClosed',
        'deleteplan',
        'CreatePlan'
    ];

    // Public variables for the filters of the page
    public $status = '';
    public $user;
    public $search = '';
    public $newPlan;
    public $shouldRedirect = false;
    public $selectedStatuses = [];
    public $selectedColleagues = [];
    public $selectedTaggedRole = null;
    public $isActiveSelected = false;
    public $isClosedSelected = false;
    public $isDraftSelected = false;

    // Public variables for the form
    public $name                =    '';
    public $roles               =    '';
    public $companies;
    public $selectedCompanies   =    [];
    // public $selectedSectors     =    [];
    public $selectedIndustries  =    [];
    public $division            =    '';
    public $function            =    '';

    public $companiesNames      =    '';
    public $descriptions        =    '';
    public $min_exp             =    null;
    public $education           =    '';
    public $skills              =    '';
    public $location            =    '';
    public $gender              =    null;
    public $ethnicity           =    0;

    public $roleTag             =   '';
    public $stepup              =    [];
    public $stepupTag           =    '';
    public $newSkillTag         =   '';
    public $qualificationTag    =   '';
    public $newSkills           =   [];
    public $qualifications      =   [];
    public $selectedCountries   =   [];
    public $enteredRoles        =   [];
    public $countries;
    public $troles;
    public $cities;
    public $selectedRole;
    public $searchCountries = '';
    public $selectedCities = [];
    public $sectors = [];
    public $industries = [];
    public $interestedCompaniesarray = [];
    public $existingTaggedPeople = [];

    public $plan;
    public $open = false;

    // Chart labels and data
    public $scoreLables = [];
    public $scoreData = [];

    public $step = 1;

    public $newSkillData = [
        'qualifications'  => [],
        'skills'          => [],
        'targetRoles'     => [],
        'stepUpCandidate' => [],
        'keyword'         => []
    ];


    protected $dataPreloader;
    public function mount()
    {   
        $startTime = microtime(true);
        //$this->dataPreloader = $dataPreloader;
        
        $this->user = auth()->user();
        $user = $this->user;
        /*
        $data = $this->dataPreloader->preloadPeople($this->user);
        $this->companies = $data['companies']; // Correct
        $this->industries = $data['industries'];
        */
        $cacheKey = "target_{$this->user->id}_comps";

        //*
        //$this->interestedCompaniesarray = $companyIds;
        $this->companies = cache()->remember($cacheKey, 3600, function() use ($user)
        { 
            $interestedCompanies = false;
            $interestedIndustries = false;
            $interestedSectors = false;
            $accountObj = Account::where('id', $this->user->account_id)->first();

            if($accountObj){
                if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                    $interestedCompanies = explode(",", $accountObj->company_of_interest);
                }
                else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                    $interestedIndustries = explode(",", $accountObj->industry_interest);
                }
                else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                    $interestedSectors = explode(",", $accountObj->sector_interest);
                }
            }

            $companyIds = [];
            if($interestedCompanies && count($interestedCompanies) > 0){
                $companyIds = $interestedCompanies;
            }
            else if($interestedIndustries && count($interestedIndustries) > 0){
                $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
            }
            else if($interestedSectors && count($interestedSectors) > 0){
                $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
            }

            $companiesArr = People::whereNotNull('company_id')
                                    ->distinct()
                                    ->pluck('company_id')
                                    ->toArray();

        return Company::select(['id','name','industry','sector'])
                                    ->where('id', '!=', $this->user->company_id)
                                    ->when(!empty($companyIds),function($query) use($companyIds){
                                        $query->whereIn('id', $companyIds);
                                    })
                                    ->whereIn('id', $companiesArr)
                                    ->get()->map(function ($company) {
            return [
                'value' => $company->name,
                'label' => $company->name,
                'industry' => $company->industry,
                'sector' => $company->sector
            ];
        })->toArray();
        });


        // $sectors = Company::whereNotNull('sector')
        //                     ->where('sector', '!=', 'NA')
        //                     ->where('sector', '!=', '0')
        //                     ->when(!empty($companyIds),function($query) use($companyIds){
        //                         $query->whereIn('id', $companyIds);
        //                     })
        //                     ->distinct()
        //                     ->orderBy('sector', 'ASC')
        //                     ->get(['id', 'sector'])
        //                     ->groupBy('sector')
        //                     ->toArray();
        // foreach($sectors as $key => $sector){
        //     $this->sectors[] = ['value' => $key, 'label' => $key];
        // }
        $cacheKey = "target_{$this->user->id}_industries";
        $this->industries = cache()->remember($cacheKey, 3600, function()
        { 
            $interestedCompanies = false;
            $interestedIndustries = false;
            $interestedSectors = false;
            $accountObj = Account::where('id', $this->user->account_id)->first();

            if($accountObj){
                if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                    $interestedCompanies = explode(",", $accountObj->company_of_interest);
                }
                else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                    $interestedIndustries = explode(",", $accountObj->industry_interest);
                }
                else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                    $interestedSectors = explode(",", $accountObj->sector_interest);
                }
            }

            $companyIds = [];
            if($interestedCompanies && count($interestedCompanies) > 0){
                $companyIds = $interestedCompanies;
            }
            else if($interestedIndustries && count($interestedIndustries) > 0){
                $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
            }
            else if($interestedSectors && count($interestedSectors) > 0){
                $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
            }

            $companiesArr = People::whereNotNull('company_id')
                                    ->distinct()
                                    ->pluck('company_id')
                                    ->toArray();
            
            $industries = Company::select(['industry'])
                     ->whereNotNull('industry')
                     ->where('industry', '!=', 'NA')
                     ->where('industry', '!=', '0')
                     ->when(!empty($companyIds), function($query) use($companyIds) {
                         $query->whereIn('id', $companyIds);
                     })
                     ->distinct()
                     ->orderBy('industry', 'ASC')
                     ->get()
                     ->pluck('industry') // Extract the "industry" column values
                     ->toArray(); // Convert to an array

            // Transform industries into the desired structure
            return array_map(function ($industry) {
                return [
                    'value' => $industry,
                    'label' => $industry,
                ];
            }, $industries);

        });

        /*
        
        $industries = Company::select(['industry'])
                     ->whereNotNull('industry')
                     ->where('industry', '!=', 'NA')
                     ->where('industry', '!=', '0')
                     ->when(!empty($companyIds), function($query) use($companyIds) {
                         $query->whereIn('id', $companyIds);
                     })
                     ->distinct()
                     ->orderBy('industry', 'ASC')
                     ->get()
                     ->pluck('industry') // Extract the "industry" column values
                     ->toArray(); // Convert to an array

        $this->industries = [];
        foreach ($industries as $industry) {
            $this->industries[] = ['value' => $industry, 'label' => $industry];
        }
            */
        /*/
        // Measure elapsed time
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        //dd("Query Execution Time: {$executionTime} seconds");
        */
    }

    public function loadPlanScores()
    {

        foreach ($this->plans as $plan) {
            $planId = $plan->id;
            $planScores = PlanScores::where('succession_plan_id', $planId)->get();

            $labels = [];
            $data = [];

            foreach ($planScores as $score) {
                $labels[] = $score->metric_name;
                $data[] = $score->score;
            }

            $this->scoreLables[$planId] = $labels;
            $this->scoreData[$planId] = $data;
        }
    }


    public function render()
    {
        $startTime = microtime(true);

        $user = auth()->user();
        $this->countries = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $accountIds = Account::where('relationship_manager_id', 'LIKE', '%' . $user->id . '%')->pluck('id')->toArray();
        $clientuser = [];
        if(count($accountIds) > 0){
            $clientuser = User::whereIn('account_id', $accountIds)->pluck('id')->toArray();
        }
        $clientuser[] = $user->id;

        $query = SuccessionPlan::query()->orderBy('id', 'DESC');

        // Filter by status
        // if (!empty($this->status)) {
        //     $query->where('Status', $this->status);
        // } else {
        //     $query->whereNotNull('id');
        // }

        if (!empty($this->selectedStatuses) && !in_array('all', $this->selectedStatuses)) {
            $query->whereIn('status', $this->selectedStatuses);
        } else {
            $query->whereNotNull('id');
        }


        // Filter by searched name
        if (!empty($this->search)) {
            $query->where('name', 'LIKE', '%' . $this->search . '%');
        }
        // dd($query->toSql());
        $plans = $query
        // ->where('user_id', $user->id)
        ->whereIn('user_id', $clientuser)
        ->orWhereRaw('JSON_CONTAINS(shared_with, ?)', [json_encode($user->id)]) // Check if user ID exists in the shared_with JSON array
        ->get();
        // dd($plans);
        // Adjust the number of items per page as needed

        // Extract the IDs from the paginated plans
        $planIds = $plans->pluck('id');

        // Use the extracted plan IDs to filter PlanScore
        $planScores = PlanScores::whereIn('succession_plan_id', $planIds)->get();
        $groupedPlanScores = $planScores->groupBy('succession_plan_id');
        $groupedPlanScores->transform(function ($item) {
            return [
                'metric_names' => $item->pluck('metric_name')->toArray(),
                'scores' => $item->pluck('score')->toArray(),
            ];
        });

        // Loop through the plans and attach corresponding data from groupedPlanScores
        $plans->each(function ($plan) use ($groupedPlanScores) {
            $successionPlanId = $plan->id; // Replace with the actual column name
            if (isset($groupedPlanScores[$successionPlanId])) {
                $metricNames = $groupedPlanScores[$successionPlanId]['metric_names'];
                $scoreData = $groupedPlanScores[$successionPlanId]['scores'];
                $combinedScoreData = array_combine($metricNames, $scoreData);

                $plan->scoreLabels = $metricNames;
                $plan->scoreData = $scoreData;
                $plan->combinedScoreData = $combinedScoreData;
            } else {
                $plan->scoreLabels = [];
                $plan->scoreData = [];
                $plan->combinedScoreData = [];
            }
        });

        // Get the pipeline counts and succession counts that would be used
        $pipelinePeople = DB::table('pipelines')
            ->select('plan_id', DB::raw('Count(*) as pipelinecount'))
            ->groupby('plan_id')
            ->whereIn('plan_id', $planIds)
            ->get();

        $pipelineCountsMap = $pipelinePeople->keyBy('plan_id');

        $plans = $plans->map(function ($plan) use ($pipelineCountsMap) {
            $plan->pipelinecount = $pipelineCountsMap[$plan->id]->pipelinecount ?? 0;
            return $plan;
        });

        // Get the succession counts
        $successPeople = DB::table('success_people')
            ->select('plan_id', DB::raw('Count(*) as successcount'))
            ->groupby('plan_id')
            ->whereIn('plan_id', $planIds)
            ->get();

        $successCountsMap = $successPeople->keyBy('plan_id');

        $plans = $plans->map(function ($plan) use ($successCountsMap) {
            $plan->successcount = $successCountsMap[$plan->id]->successcount ?? 0;
            return $plan;
        });

        // This takes the tagged individuals who are associated with the plans shared with or created by the user
        $this->existingTaggedPeople = SuccessionPlan::where('user_id', $user->id)
            ->orWhere(function ($query) use ($user) {
                $query->whereRaw("FIND_IN_SET(?, shared_with)", [$user->id]);
            })
            ->pluck('tagged_individual')
            ->filter();

        // Use the plucked and filtered array in the query if it's not empty
        // if ($taggedPeople->isNotEmpty()) {
        //     $this->troles = InternalPeople::where('company_id', $this->user->company_id)
        //         ->whereNotIn('user_id', $taggedPeople)
        //         ->get()->map(function ($person) {
        //             return [
        //                 'value' => $person->id,
        //                 'label' => $person->forename . ' ' . $person->surname . ' ' . $person->latest_role,
        //             ];
        //         })->toArray();
        // } else {
        //     // Handle the case when $taggedPeople is empty or only contains null values
        //     // For example, you might want to return all records in this case
        //     $this->troles = InternalPeople::where('company_id', $this->user->company_id)->get()->map(function ($person) {
        //         return [
        //             'value' => $person->id,
        //             'label' => $person->forename . ' ' . $person->surname . ' ' . $person->role,
        //         ];
        //     })->toArray();
        // }

        $colleagues = User::where('company_id', $user->company_id)
            ->where('id', '!=', $user->id)
            ->get()
            ->map(function ($user) {
                return [
                    'value' => $user->id,
                    'label' => $user->name,
                ];
            })
            ->toArray();

        $this->setTaggedRoleOptions(true);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        //dd($executionTime);

        return view('livewire.plans-table', compact('plans', 'user', 'colleagues'));
    }

    public function setTaggedRoleOptions($dispatch = false) {
        $user = $this->user;

        /*
        $taggedPeople = SuccessionPlan::where('user_id', $this->user->id)
            ->orWhere(function ($query) use ($user) {
                $query->whereRaw("FIND_IN_SET(?, shared_with)", [$user->id]);
            });

        $taggedPeople = $taggedPeople->pluck('tagged_individual')->filter();
        */


        // Use the plucked and filtered array in the query if it's not empty
        // if ( $this->existingTaggedPeople != []) {
        //     $this->troles = InternalPeople::where('company_id', $this->user->company_id)
        //         ->whereNotIn('id',  $this->existingTaggedPeople)
        //         ->get()->map(function ($person) {
        //             return [
        //                 'value' => $person->id,
        //                 'label' => $person->forename . ' ' . $person->surname . ' (' . (!empty($person->latest_role) ? $person->latest_role : "Not Provided") . ')',
        //             ];
        //         })->toArray();
        // } else {
        //     // Handle the case when $taggedPeople is empty or only contains null values
        //     // For example, you might want to return all records in this case
        //     $this->troles = InternalPeople::where('company_id', $this->user->company_id)
        //     ->get()->map(function ($person) {
        //         return [
        //             'value' => $person->id,
        //             'label' => $person->forename . ' ' . $person->surname . ' (' . (!empty($person->latest_role) ? $person->latest_role : "Not Provided") . ')',
        //         ];
        //     })->toArray();
        // }
        if($dispatch) {
            $this->dispatch('optionsUpdated', $this->troles);
        }

       // dd(count($this->troles));
        
    }


    protected function filteredCountries()
    {
        return array_filter($this->availableCountries, function ($country) {
            return stripos($country, $this->searchCountry) !== false;
        });
    }


    public function CreatePlan()
    {        
        
        // Define validation rules
        $rules = [
            'name' => 'required|string',
            'descriptions' => 'required|string'
        ];

        // Validate the data
        $validator = Validator::make($this->validate($rules), $rules);

        // Check if validation fails
        if ($validator->fails()) {
            // Reset step to 1 if validation fails
            $this->step = 1;
            return;
        }


        // Get the user if for the individual in the current session
        $user = auth()->user();

        // Add the plan details to the plans table
        $planData = [
            'name'               => $this->name,
            'description'        => $this->descriptions,
            'minimum_Experience' => $this->min_exp,
            'step_up'            => "1",
            'ethnicity'          => $this->ethnicity,
            'tagged_individual'  => $this->selectedTaggedRole,
            'age'                => 0,
            'status'             => "Draft",
            'candidate_status'   => "No Changes",
            'user_id'            => $user->id,
            "shared_with"        => "[]"
        ];

        // To store the collegues or contributors
        if (!empty($this->selectedColleagues)) {
            $planData["shared_with"] = json_encode($this->selectedColleagues);
        }
 
      
        $plan = SuccessionPlan::create($planData);
        
            $planData["target_roles"] = $this->newSkillData['targetRoles'];
            $planData["country"] = $this->selectedCountries;
            $planData["companies"] = $this->selectedCompanies;
            $planData["step_up_candidates"] = $this->newSkillData['stepUpCandidate'];
            $planData["gender"] = $this->gender;
            $planData["skills"] = $this->newSkillData['skills'];
            $planData["qualifications"] = $this->newSkillData['qualifications'];
            $planData["minimum_tenure"] = $this->min_exp;

            
            $getJobDescriptionResponse = $this->generateJobDescription($planData);
            $jobDescription = null;
            if ($getJobDescriptionResponse) {
                $data = $getJobDescriptionResponse->getData(true);
                $jobDescription = $data['job_description'] ?? '';
            }
            
            // Update the job_description column in the SuccessionPlan table
            if ($jobDescription && isset($plan->id)) {
                SuccessionPlan::where('id', $plan->id)->update(['job_description' => $jobDescription]);
            }

        if (!empty($this->selectedColleagues)) {
            foreach ($this->selectedColleagues as $colleague) {
                Notifications::create([
                    'type'              => "Plan_Shared",
                    'plan_id'           => $plan->id,
                    'entity_name'       => $this->name,
                    'description'       => "{$user->name} has shared plan {$plan->name} with you.",
                    'user_id'           => $colleague,
                    'user_company'      => $user->company_id
                ]);
            }  
        }

        // Create the notification that a plan has been created
        Notifications::create([
            'type'              => "Plan_Created",
            'plan_id'           => $plan->id,
            'entity_name'       => $this->name,
            'description'       => $this->descriptions,
            'user_id'           => $user->id,
            'user_company'      => $user->company_id
        ]);


        //------------------  Success Requirements Table ---------------//

        // Roles are seperated by commas this expands them into a list ** this field is mandatory **
        // To store the target role
        foreach ($this->newSkillData['targetRoles'] as $role) {
            $role = trim($role);
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => $role,
                'type' => "Role",
            ]);
        }

        // Getting any step_up candidates
        foreach ($this->newSkillData['stepUpCandidate'] as $stepUpCandidate) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' =>  trim($stepUpCandidate),
                'type' => "step_up",
            ]);
        }

        // Getting keyword
        foreach ($this->newSkillData['keyword'] as $keyword) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' =>  trim($keyword),
                'type' => "keyword",
            ]);
        }
        
        // Get any tenure requirements
        if ($this->min_exp) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => $this->min_exp,
                'type' => "Minimum_Tenure"
            ]);
        }

        // Getting any location requirements
        if (!empty($this->selectedCountries)) {
            foreach ($this->selectedCountries as $country) {
                SuccessRequirements::create([
                    'plan_id' => $plan->id,
                    'name' => $country,
                    'type' => "location"
                ]);
            }
        }

        // Get any another skills into the requirements table
        foreach ($this->newSkillData['skills'] as $skill) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => trim($skill),
                'type' => "professional_skill"
            ]);
        }

        // This will be use for qualifications
        // Getting any Educational Qualifications Requirements
        foreach ($this->newSkillData['qualifications'] as $qualification) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => trim($qualification),
                'type' => "education"
            ]);
        }

        if ($this->gender) {
            SuccessRequirements::create(
                [
                    'plan_id' => $plan->id,
                    'name'    => trim($this->gender),
                    'type'    => 'Gender',
                ]
            );
        }

        if (!empty($this->selectedCompanies)) {
            foreach ($this->selectedCompanies as $cr) {
                SuccessRequirements::create([
                    'plan_id' => $plan->id,
                    'name'    => trim($cr),
                    'type'    => 'Company',
                ]);
            }
        }

        // if (!empty($this->selectedSectors)) {
        //     foreach ($this->selectedSectors as $sector) {
        //         SuccessRequirements::create([
        //             'plan_id' => $plan->id,
        //             'name'    => trim($sector),
        //             'type'    => 'Sector',
        //         ]);
        //     }
        // }

        if (!empty($this->selectedIndustries)) {
            foreach ($this->selectedIndustries as $industry) {
                SuccessRequirements::create([
                    'plan_id' => $plan->id,
                    'name'    => trim($industry),
                    'type'    => 'Industry',
                ]);
            }
        }

        // // Getting any Functions
        // if ($this->function) {
        //     $functions = explode(',', $this->function);
        //     foreach ($functions as $function) {
        //         $function = trim($function);
        //         SuccessRequirements::create([
        //             'plan_id' => $plan->id,
        //             'name' => $function,
        //             'type' => "Function"
        //         ]);
        //     }
        // }

        // // Getting any Division
        // if ($this->division) {
        //     $divisions = explode(',', $this->division);
        //     foreach ($divisions as $division) {
        //         $division = trim($division);
        //         SuccessRequirements::create([
        //             'plan_id' => $plan->id,
        //             'name' => $division,
        //             'type' => "Division"
        //         ]);
        //     }
        // }


        //See if a gender was chosen
        if ($this->gender == 'Female') {
            $gen = 'Female';
        } elseif ($this->gender == 'Male') {
            $gen = 'Male';
        } else {
            $gen = '';
        }

        //------------------------ Make the pipeline table -------------------//

        //---- Get the data ready for the scoring ---//

        // Roles list
        $rawRoles = $this->newSkillData['targetRoles'];
        $proles = array_map('trim', $rawRoles);
        //dd($proles);

        // Function list
        $rawFunc = $this->function;
        $pFuncs = array_map('trim', explode(',', $rawFunc));

        // Step-up List
        $rawsteps = $this->newSkillData['stepUpCandidate'];
        $psteps = array_map('trim', $rawsteps);


        $rawKeyword = $this->newSkillData['keyword'];
        $pKeyword = array_map('trim', $rawKeyword);

        // Qualifcications list
        $rawEdQual = $this->newSkillData['qualifications'];
        $pEdQual = array_map('trim', $rawEdQual);

        // Location list
        $ploc = $this->selectedCountries;

        // Skills List
        $rawskill = $this->newSkillData['skills'];
        $pskills = array_map('trim', $rawskill);
        $skillCount = count($pskills);

        //dd("working");
        
        //--------------- Start the filtering for the pipeline --------------//
        $scountry = $this->selectedCountries;
        $scities   = $this->selectedCities;

        // Getting the array for the companies using the companies, sector and industry 
        $filteredCompanyIdsArr = [];

        if (!empty($this->selectedIndustries)) {
            $filteredCompanyIdsArr = Company::whereIn('industry', $this->selectedIndustries)
                                                ->pluck('id')
                                                ->toArray();
        }



        // This will filter the People table where the roles, gender, company_name, country and city
        if (!empty($proles)) {
            $filteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id','!=',$this->user->company_id)
                ->where(function ($query) use ($proles, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                    if (!empty($proles)) {
                        $query->where(function ($subquery) use ($proles) {
                            // Loop through each keyword and add an orWhere clause
                            foreach ($proles as $prole) {
                                $subquery->orWhere('latest_role', 'like', '%' . $prole . '%');
                            }
                        });
                    }
                    // The statement below will filter by the company_name
                    if (!empty($this->selectedCompanies)) {
                        $query->WhereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                    if (!empty($this->interestedCompaniesarray)){
                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                    }
                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($scountry)) {
                        $query->WhereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->WhereIn('city', $scities);
                    }
                })
                ->get();
            //dd($filteredPeople);

            $filteredPeople = $filteredPeople->map(function ($item) {
                $item['role_score'] = 1;
                $item['type'] = "External-System";
                return $item;
            });
        } else {
            $filteredPeople = new Collection();
        }

        //dd($filteredPeople[0]);

        $filteredPeopleidslvl1 = $filteredPeople->pluck('id');

        /* If a step up candidate has been proposed the system will only search for step candidates as well ensuring that
           people already identified from roles do not come through and uses the same filtering logic as roles
        */
        if ($rawsteps !== []) {
            //dd("Running Steps");
            $sfilteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id','!=',$this->user->company_id)
                ->whereNotIn('id', $filteredPeopleidslvl1)
                ->when($rawsteps !== "", function ($query) use ($psteps, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                    $query->where(function ($subquery) use ($psteps) {
                        // Loop through each keyword and add an orWhere clause
                        foreach ($psteps as $pstep) {
                            $subquery->orWhere('latest_role', 'like', '%' . $pstep . '%');
                        }
                    });
                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->WhereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                    if (!empty($this->interestedCompaniesarray)){
                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                    }
                    if (!empty($scountry)) {
                        $query->WhereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->WhereIn('city', $scities);
                    }
                })
                ->get();

            $sfilteredPeople = $sfilteredPeople->map(function ($item) {
                $item['role_score'] = 0.75;
                $item['type'] = "External-System";
                return $item;
            });

            //dd($sfilteredPeople);

            $sfilteredPeopleidslvl1 = $sfilteredPeople->pluck('id');
        } else {
            $sfilteredPeople = [];
            $sfilteredPeopleidslvl1 = [];
        }

        if (!empty($proles)) {
            $infilteredPeople = InternalPeople::query()
            ->where('company_id','=',$this->user->company_id)
            ->where('forename', '!=', "Vacant")
            ->when($proles !== [], function ($query) use ($proles, $gen, $scountry, $scities) {
                $query->where(function ($subquery) use ($proles) {
                    // Loop through each keyword and add an orWhere clause
                    foreach ($proles as $prole) {
                        $subquery->orWhere('latest_role', 'like', '%' . $prole . '%');
                    }
                });
                if ($gen != '') {
                    $query->where('gender', $gen);
                }
                if (!empty($scountry)) {
                    $query->whereIn('country', $scountry);
                }
                if (!empty($scities)) {
                    $query->whereIn('city', $scities);
                }
            })
            ->get(); 
        //dd(DB::getQueryLog());
        //dd($infilteredPeople);
        $infilteredPeople = $infilteredPeople->map(function ($item) {
            $item['role_score'] = 1;
            $item['type'] = "Internal-System";
            return $item;
        });

        $infilteredPeopleidslvl1 = $infilteredPeople->pluck('id');
        }
        else {
            $infilteredPeople =[];
        }

        //DB::enableQueryLog();
        // Filter the internal people table
       

        // Filter for internal_step_ups
        if ($rawsteps !== []) {
            $insfilteredPeople = InternalPeople::query()
                ->where('company_id','=',$this->user->company_id)
                ->where('forename', '!=', "Vacant")
                ->whereNotIn('id', $infilteredPeopleidslvl1)
                ->when($rawsteps !== "", function ($query) use ($psteps, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                    $query->where(function ($subquery) use ($psteps) {
                        // Loop through each keyword and add an orWhere clause
                        foreach ($psteps as $pstep) {
                            $subquery->orWhere('latest_role', 'like', '%' . $pstep . '%');
                        }
                    });
                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($scountry)) {
                        $query->WhereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->WhereIn('city', $scities);
                    }
                })
                ->get();

            $insfilteredPeople = $insfilteredPeople->map(function ($item) {
                $item['role_score'] = 0.75;
                $item['type'] = "Internal-System";
                return $item;
            });

            //dd($sfilteredPeople);
        } else {
            $insfilteredPeople = [];
        }

        if (!empty($pKeyword)) {
            $kfilteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id','!=',$this->user->company_id)
                ->whereNotIn('id', $filteredPeopleidslvl1)
                ->whereNotIn('id', $sfilteredPeopleidslvl1)
                ->when($pKeyword !== "", function ($query) use ($pKeyword, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                    $query->where(function ($subquery) use ($pKeyword) {
                        // Loop through each keyword and add an orWhere clause
                        foreach ($pKeyword as $keyword) {
                            $subquery->orWhere('latest_role', 'like', '%' . $keyword . '%');
                        }
                    });

                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->whereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                    if (!empty($this->interestedCompaniesarray)){
                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                    }
                    if (!empty($scountry)) {
                        $query->whereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->whereIn('city', $scities);
                    }
                })
                ->get();
            $kfilteredPeople = $kfilteredPeople->map(function ($item) {
                $item['role_score'] = 0.25;
                $item['type'] = "External-System";
                return $item;
            });

            //dd($kfilteredPeople);

            $kfilteredPeopleidslvl1 = $kfilteredPeople->pluck('id');
        } else {
            $kfilteredPeople = [];
            $kfilteredPeopleidslvl1 = [];
        }

        // May be relevant candidates
        $career_history_filtered = CareerHistories::query()
            ->whereNotIn('people_id', $filteredPeopleidslvl1)
            ->whereNotIn('people_id', $sfilteredPeopleidslvl1)
            ->whereNotIn('people_id', $kfilteredPeopleidslvl1)
            ->where(function ($query) use ($proles, $psteps) {
                $query->WhereIn('role', $proles)
                    ->orWhereIn('role', $psteps);
            })
            ->get();

        // First, get the relevant IDs from $career_history_filtered
        $filteredCareerHistoryIds = $career_history_filtered->pluck('people_id');

        if ($filteredCareerHistoryIds !== null) {
            // Then, use these IDs to filter the people table
            $careerPeople = People::query()
                ->whereIn('id', $filteredCareerHistoryIds)
                ->where('company_id','!=',$this->user->company_id)
                ->when($filteredCareerHistoryIds  !== null, function ($query) use ($proles, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($scountry)) {
                        $query->WhereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->WhereIn('city', $scities);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->WhereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                    if (!empty($this->interestedCompaniesarray)){
                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                    }

                })
                ->get();

            $careerPeople = $careerPeople->map(function ($item) {
                $item['role_score'] = 0.5;
                $item['type'] = "External-System";
                return $item;
            });

            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
            $filteredPeople = $filteredPeople->concat($careerPeople);
            $filteredPeople = $filteredPeople->concat($kfilteredPeople);
            //$externalPeopleid = $filteredPeople->pluck('id');
            //dd($externalPeopleid);
            $filteredPeople = $filteredPeople->concat($infilteredPeople);
            $filteredPeople = $filteredPeople->concat($insfilteredPeople);
            
        } else {
            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
            $filteredPeople = $filteredPeople->concat($infilteredPeople);
            $filteredPeople = $filteredPeople->concat($insfilteredPeople);
        }

        //dd($filteredPeople);


        //------------------------ Calculate Education Score  ------------------//

        // Query to join a filtered People table with the education requirements and summarise to get the count
        $educationCounts = People::query()
            ->whereIn('people.educational_history', $pEdQual)
            ->join('people as filtered_people', 'people.id', '=', 'filtered_people.id')
            ->groupBy('people.id')
            ->selectRaw('people.id, COUNT(filtered_people.id) as education_match_count')
            ->get();

        // Create a map of education match counts indexed by person ID
        $educationMatchCountMap = $educationCounts->pluck('education_match_count', 'id');

        // Left join to add the education_match_count column to filtered_people records
        $filteredPeopleWithEducationCount = $filteredPeople->map(function ($person) use ($educationMatchCountMap) {
            $person->education_match_count = $educationMatchCountMap->get($person->id, 0);
            return $person;
        });


        //------------------------- Calculate Location Score ---------------------//

        // Query to join the new filtered education People table with the loccation
        if (!empty($this->selectedCountries)) {

            $filteredPeopleWithEdLocMatches = $filteredPeople->whereIn('country', $ploc);

            // Join the location match counts with the filteredPeopleWithEducationCount collection
            $filteredPeopleWithEdLocMatches = $filteredPeopleWithEducationCount->map(function ($person) {
                $person->location_match_count = 1;
                return $person;
            });
        } else {
            $filteredPeopleWithEdLocMatches = $filteredPeopleWithEducationCount->map(function ($person) {
                $person->location_match_count = 1;
                return $person;
            });
        }


        //------------------------------ Calculate Skill Score -----------------------------//

        // Query to join a filtered people table with the skill requirements and summarise to 
        $skillsScoreCount = Skills::query()
            ->whereIn('skill_name', $pskills)
            ->groupBy('people_id')
            ->selectRaw('skills.people_id, COUNT(skills.people_id) / ? as skill_score', ['skillCount' => $skillCount])
            ->get();

        $internalSkillsScoreCount = InternalSkills::query()
            ->whereIn('skill_name', $pskills)
            ->groupBy('internal_people') // Use 'internal_people' instead of 'people_id'
            ->selectRaw('internal_skills.internal_people as people_id, COUNT(internal_skills.internal_people) / ? as skill_score', ['skillCount' => $skillCount])
            ->get();

        // Combine both skill score results into one collection
        $combinedSkillsScoreCount = $skillsScoreCount->merge($internalSkillsScoreCount);

        // Create a map of skill match scores indexed by person ID
        $skillsScoreMap = $combinedSkillsScoreCount
            ->groupBy('people_id')
            ->map(function ($scores) {
                // Sum the scores if multiple records exist for the same person
                return $scores->sum('skill_score');
            });

        // Join the skill score map with the filteredPeoplewithEdLocMatches collection
        $filteredPeopleWithMatches = $filteredPeopleWithEdLocMatches->map(function ($person) use ($skillsScoreMap) {
            $person->skill_score = $skillsScoreMap->get($person->id, 0);
            return $person;
        });

        //--------------------------------------- Gender ------------------------------------//

        // Calculate and assign gender score
        $filteredPeopleWithMatches->each(function ($person) {
            $person->gender_score = ($person->gender === $this->gender) ? 1 : 0;
        });

        // Calculate and assign tenancy score
        if ($this->min_exp !== null) {
            $filteredPeopleWithMatches = $filteredPeopleWithMatches->map(function ($person) {
                $tenancyDifference = $person->tenure - $this->min_exp;
                $tenancyDifferenceAbsolute = abs($tenancyDifference);

                if ($tenancyDifference < 0) {
                    if ($tenancyDifferenceAbsolute <= 2) {
                        $person->tenancy_score = 0;
                    } else {
                        $person->tenancy_score = 1;
                    }
                } else {
                    $person->tenancy_score = 1;
                }
                return $person;
            });

            //$filteredPeopleWithMatches = $filteredPeopleWithMatches->where('tenancy_score','=',1);
        } else {
            $filteredPeopleWithMatches = $filteredPeopleWithMatches->map(function ($person) {
                $person->tenancy_score = 1;
                return $person;
            });
        }

        // dd($filteredPeopleWithMatches);

        //------------------------------  Make all the tables --------------------------------//

        // Put that data into the successionpeople list
        $filteredPeopleWithMatches->each(function ($person) use ($plan, $user) {
            pipeline::create([
                'plan_id'            => $plan->id,
                'user_id'            => $user->id,
                'people_id'          => $person->id,
                'first_name'         => $person->forename,
                'last_name'          => $person->surname,
                'middle_name'        => $person->middle_name,
                'other_name'         => $person->other_name,
                'gender'             => $person->gender,
                'diverse'            => $person->diverse,
                'location'           => $person->location,
                'country'            => $person->country,
                'city'               => $person->city,
                'summary'            => $person->summary,
                'linkedinURL'        => $person->linkedinURL,
                'latest_role'        => $person->latest_role,
                'company_id'         => $person->company_id,
                'company_name'       => $person->company_name,
                'start_date'         => $person->start_date,
                'end_date'           => $person->end_date,
                'tenure'             => $person->tenure,
                'function'           => $person->function,
                'division'           => $person->division,
                'seniority'          => $person->seniority,
                'exco'               => $person->exco,
                'career_history'     => $person->career_history,
                'educational_history' => $person->educational_history,
                'skills'             => $person->skills,
                'languages'          => $person->languages,
                'skills_match'       => $person->skill_score,
                'education_match'    => $person->education_match_count,
                'location_match'     => $person->location_match_count,
                'role_match'         => $person->role_score,
                'readiness'          => $person->readiness,
                'other_tags'         => $person->other_tags,
                'gender_match'       => $person->gender_score,
                'tenure_match'       => $person->tenancy_score,
                'total_score'        => $person->skill_score + $person->education_match_count + $person->location_match_count + $person->role_score + $person->tenancy_score,
                'people_type'        => $person->type
            ]);
        });

        $this->reset(['name', 'roles', 'enteredRoles', 'selectedTaggedRole', 'selectedColleagues', 'qualifications', 'newSkills', 'division', 'function', 'stepup', 'descriptions', 'min_exp', 'education', 'skills', 'location', 'gender', 'ethnicity', 'selectedCountries', 'selectedCompanies', 'selectedIndustries', 'troles']);

        $this->newSkillData = [
            'qualifications' => [],
            'skills' => [],
            'targetRoles' => [],
            'stepUpCandidate' => [],
            'keyword'         => []
        ];
        $this->step++;
        // dd($this->ethnicity);
        $this->plan = $plan;
        $this->open = false;
    }


    public function deleteplan($planId)
    {
        $requirement = SuccessionPlan::findOrFail($planId);

        if(auth()->user()->id != $requirement->user_id) {
            $this->dispatch('toast', "error", "You are not allowed to perform this action!");
            return;
        }

        $requirement->delete();
    }

    public function onSelectStatus($value)
    {
        if (in_array($value, $this->selectedStatuses)) {
            // Remove value from array
            if ($value == 'all') {
                $this->selectedStatuses = [];
            } else {
                $this->selectedStatuses = array_diff($this->selectedStatuses, [$value]);
            }
        } else {
            // Add value to array
            if ($value == 'all') {
                $this->selectedStatuses = ['all', 'active', 'draft', 'closed'];
            } else {
                $this->selectedStatuses[] = $value;
            }
        }

        in_array('active', $this->selectedStatuses) ? $this->isActiveSelected = true : $this->isActiveSelected = false;
        in_array('draft', $this->selectedStatuses) ? $this->isDraftSelected = true : $this->isDraftSelected = false;
        in_array('closed', $this->selectedStatuses) ? $this->isClosedSelected = true : $this->isClosedSelected = false;
    }


    public function changeMinimumTenure($flag)
    {
        if ($flag == 'decrease') {
            $this->min_exp = $this->min_exp <= 1 ? null : ($this->min_exp - 1);
        } else if ($flag == 'increase') {
            $this->min_exp = !$this->min_exp ? 1 : ($this->min_exp + 1);
        }
    }

    public function modalClosed()
    {
        $this->step = 1;
        $this->reset(['name', 'roles', 'enteredRoles', 'selectedTaggedRole', 'selectedColleagues', 'qualifications', 'newSkills', 'division', 'function', 'stepup', 'descriptions', 'min_exp', 'education', 'skills', 'location', 'gender', 'ethnicity', 'selectedCountries', 'selectedCompanies', 'selectedIndustries', 'troles', 'newSkillData']);
    }

    
public function generateJobDescription($data)
{     
    // Build the prompt
    $prompt = "Generate a professional job description for the position of " . implode(" / ", $data['target_roles']) . ". 
Company: " . implode(", ", $data['companies']) . ". 
Location: " . implode(", ", $data['country']) . ". 
Plan Focus: " . $data['description'] . " 
Ideal candidates are " . $data['gender'] . " with a minimum of " . $data['minimum_tenure'] . " years of experience. 
Preferred qualifications include: " . implode(", ", $data['qualifications']) . ". 
Key skills: " . implode(", ", $data['skills']) . ". 
Internal step-up candidates may come from the role of " . implode(", ", $data['step_up_candidates']) . ".";

    

    $apiKey = env('OPENAI_API_KEY');
    $response = Http::withHeaders([
        'Authorization' => 'Bearer ' . $apiKey,
        'Content-Type' => 'application/json',
    ])->post('https://api.openai.com/v1/chat/completions', [
        'model' => 'gpt-4o',
        'messages' => [
            ['role' => 'system', 'content' => 'You are an expert HR assistant and job description generator.'],
            ['role' => 'user', 'content' => $prompt],
        ],
        'temperature' => 0.7,
    ]);

    // Check for API errors
    if ($response->failed()) {
        return response()->json([
            'error' => 'OpenAI API request failed.',
            'status' => $response->status(),
            'details' => $response->body(),
        ], $response->status());
    }
    $result = $response->json();
    return response()->json([
        'job_description' => $result['choices'][0]['message']['content'] ?? 'No response generated.',
    ]);
}

}
