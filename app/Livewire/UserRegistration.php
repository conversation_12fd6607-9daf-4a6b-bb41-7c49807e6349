<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\Account;
use Illuminate\Support\Facades\Mail;
use App\Mail\NewuserEmail;
use App\Models\Invite;
use Illuminate\Support\Facades\Auth;


class UserRegistration extends Component
{
    public $name;
    public $id;
    public $email;
    public $account;
    public $password;
    public $confirmedpassword;
    public $company;
    public $message;
    public $url;
    public $role;

    public function mount($email, $id, $company, $url, $role)
    {
        $this->account = $id;
        $this->email = $email;
        $this->company = $company;
        $this->url = $url;
        $this->role = $role;

        //dd($role);
    }

    public function render()
    {
        return view('livewire.user-registration');
    }

    public function sendVerification()
    {

        $this->validate([
            'name' => 'required|string|max:255',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*[!@#$%^&*(),.?":{}|<>])/u' // Ensure password contains at least one special character
            ],
            'confirmedpassword' => 'required|string|same:password',
        ], [
            'password.regex' => 'The password must contain at least one special character.',
            'confirmedpassword.same' => 'The password confirmation does not match.',
        ]);


        $user = User::where('account_id', $this->id);

        $invite = Invite::where([
            'email' => $this->email,
            'user_id' => $this->account,
            'company_id' => $this->company,
            'url' => $this->url
        ])->first();

        if(!$invite || $invite && $invite->used) {
            $this->dispatch('toast', 'info', 'URL expired');
            return;
        }

        

        if ($this->role !== 'Viewer'){
            $updateAccount = Account::where('id', $this->account)->first();
            $updateAccount->update([
                'active_users' => $updateAccount->active_users + 1,
            ]);
        }


        User::create([
            'name'          => $this->name,
            'image_url'     => 'Not required',
            'team'          => 'User',
            'company_id'    => $this->company,
            'account_id'    => $this->account,
            'role'          => $this->role,
            'email'         => $this->email,
            'password'      => $this->password,
            'profile_pic'   => 'Not Provided'
        ]);

        if(!empty($invite)) {
            $invite->update(['used' => true]);

            if (!Auth::check()) {
                return redirect()->route('login');
            }
        }
    }
}
