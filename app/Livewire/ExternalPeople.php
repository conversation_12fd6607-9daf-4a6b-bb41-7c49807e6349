<?php

namespace App\Livewire;

use App\Imports\PeopleImport;
use Livewire\Component;
use Livewire\WithPagination;
use OpenAI\Laravel\Facades\OpenAI;
use App\Models\SuccessionPlan;
use App\Models\Company;
use App\Models\SuccessPeople;
use App\Models\SuccessRequirements;
use App\Models\SuccessSkills;
use App\Models\PlanScores;
use App\Models\Skills;
use App\Models\CareerHistories;
use App\Models\Location;
use App\Models\InternalPeople;
use App\Models\People;
use App\Models\pipeline;
use App\Models\JobPeople;
use App\Models\Job;
use App\Models\notifications;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Livewire\WithFileUploads;
use App\Models\Role;
use App\Models\Account;
use Exception;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class ExternalPeople extends Component
{
    use WithFileUploads;

    const STATUS_SUBMITTED = "Submitted";
    const STATUS_REPORTED = "Reported";

    protected $listeners = [
        'clearFilters',
        'clearSelectedIndividuals',
        'addPersonModalClosed',
        'uploadCandidatesModalClosed'
    ];

    public $plan;
    public $openPlanPopup=false;
    public $openTalentPopup=false;
    public $disableDispatchforMultipleSelect=false;
    public $selectedPerson = [];
    public $plandetails;
    public $pipeline;
    public $pipelineid;
    public $yourCompany;
    public $selectedIndividualID;
    public $selectedIndividual;
    public $addedPerson;
    public $selectedIndividualSkills;
    public $selectedIndividualCareerHistory;
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $search = false;
    public $searchByKeyword = '';
    public $searchterm;

    //Variables for bulk uploads
    public $errormessage1;
    public $errormessage2;
    public $errormessage3;
    public $csvFile;
    public $csvSkills;
    public $hasCandidatesUploaded = false;


    public $bopen = false;
    public $duplicateUploadPopup = false;
    public $duplicateRecords = [];
    public $selectedRecords = [];

    //Function tester
    public $tester;

    // Variables for filters
    public $forename;
    public $surname;
    public $gender;
    public $role = [];
    public $previousRole;
    public $company = [];
    public $function = [];
    public $division = [];
    public $location = [];
    public $regBodies;
    public $tenure;
    public $min_exp =   null;

    //Variables for Visualisation
    public $TotalPeople;
    public $TotalCompanies;
    public $genderLabels = [];
    public $genderData = [];
    public $totalScoreLabels = [];
    public $totalScoreData = [];
    public $companyLabels = [];
    public $companyData = [];
    public $locationLabels = [];
    public $locationData = [];
    public $functionLabels = [];
    public $functionData = [];
    public $divisionLabels = [];
    public $divisionData = [];
    public $roleLabels = [];
    public $roleData = [];

    //Variables for Advanced Search


    //Variables for Add Person Form
    public $Suggestion = [];
    public $countries;
    public $addForename = '';
    public $addSurname = '';
    public $addMiddlename = '';
    public $addOtherName = '';
    public $addLinkedinURL = '';
    public $addRole = '';
    public $addStartDate;
    public $addCompany = '';
    public $addFunction = '';
    public $addDivision = '';
    public $companies = [];
    public $companiesListToSearch = [];
    public $selectedCompanies   = [];
    public $addLocation = '';

    public $addGender = 'Not Applicable';
    public $errormessage;

    //Variables for adding users to plan with a button
    public $SuccessSkills;
    public $SuccessRoles;
    public $SuccessStepup;
    public $SuccessLocation;
    public $SuccessGender;

    // Variables for reporting
    public $problem;
    public $reportDescription;

    public $perPage =   50; // Number of items per page
    public $page    =   1; // Current page number
    public $functions = [];
    public $divisions = [];
    public $locations = [];

    public $step = 1;
    public $peopleAreadyExistsPopup = false;
    public $existingPeopleField;
    public $isPeopleAlreadyExists = [];
    public $Aopen = false;
    public $userPlans = [];
    public $userJobs = [];
    public $vopen = false;
    public $addToPlanPopup = false;
    public $plansList = [];
    public $addToPlansArray = [];
    public $addToTalentPoolArray = [];
    public $addToTalentPoolPopup = false;
    public $talentPoolsList = [];


    public function mount()
    {
        $user = auth()->user();

        $companiesArrToSearch = People::whereNotNull('company_id')
            ->whereNotNull('company_name')
            ->where('company_name', '!=', "")
            ->where('company_id', '!=', $user->company_id) // Exclude the user's company
            ->distinct()
            ->orderBy('company_name', 'ASC')
            ->pluck('company_name', 'company_id')
            ->toArray();

            //dd($companiesArrToSearch);                  
        foreach($companiesArrToSearch as $companyId => $companyName){
             $this->companiesListToSearch[] = ['value' => $companyId, 'label' => $companyName];
        }
        // Code to get the companies of interest for a specific account
        $accountObj = Account::where('id', $user->account_id)->first();

        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        if($accountObj){
            if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }

        $companyIds = [];
        if($interestedCompanies && count($interestedCompanies) > 0){
            $companyIds = $interestedCompanies;
        }
        else if($interestedIndustries && count($interestedIndustries) > 0){
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if($interestedSectors && count($interestedSectors) > 0){
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
        }

        //Test
        $this->plandetails = SuccessionPlan::where('id', $this->plan)->first();

        $this->yourCompany = DB::table('companies')->where('id', $user->company_id)->value('name');

        
        $this->countries = Location::distinct()->pluck('country_name')->toArray();

        $this->pipelineid = Pipeline::where('plan_id', $this->plan)->value('id');
        // Retrieve all SuccessRequirements for the given plan in one query
        $successRequirements = SuccessRequirements::where('plan_id', $this->plan)->get();
 
        // Group or filter the results based on the 'type'
        $this->SuccessSkills = $successRequirements->where('type', 'professional_skill');
        $this->SuccessRoles = $successRequirements->where('type', 'Role');
        $this->SuccessLocation = $successRequirements->where('type', 'Location');
        $this->SuccessGender = $successRequirements->where('type', 'Gender');
        $this->SuccessStepup = $successRequirements->where('type', 'step_up');

        $companiesArr = [];
        $companiesArr = array_unique(People::whereNotNull('company_id')->pluck('company_id')->toArray());

        $this->companies = [];

        Company::where('id', '!=', $user->company_id)
            ->whereIn('id', $companiesArr)
            ->chunk(2000, function ($companies) {
                $chunkedCompanies = $companies->map(function ($company) {
                    return [
                        'value' => $company->name,
                        'label' => $company->name,
                    ];
                })->toArray();

                // Merge the chunked results into the main array
                $this->companies = array_merge($this->companies, $chunkedCompanies);
            });

        $this->functions = pipeline::whereNotNull('function')
                                ->where('function', '!=', "")
                                ->distinct()
                                ->orderBy('function', 'ASC')
                                ->pluck('function')
                                ->map(function ($function) {
                                    return [
                                        'value' => $function,
                                        'label' => $function,
                                    ];
                                })
                                ->toArray();

        $this->divisions = DB::table('pipelines')
                                ->whereNotNull('division')
                                ->where('division', '!=', "")
                                ->distinct()
                                ->orderBy('division', 'ASC')
                                ->pluck('division')
                                ->map(function ($division) {
                                    return [
                                        'value' => $division,
                                        'label' => $division,
                                    ];
                                })
                                ->toArray();


        $this->locations = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $this->pipeline = pipeline::where('plan_id', $this->plan)->pluck('people_id')->toArray();

                //---------------------------- Gender Split -------------------------------//
                $peopleCountsData = People::selectRaw('gender, COUNT(*) as total_people')
                ->where('company_id','!=',$user->company_id)
                ->groupBy('gender')->get();
                $this->genderLabels = [];
                $this->genderData = [];
                foreach ($peopleCountsData as $data) {
                    // $this->genderLabels[] = $data->gender;
                    // $this->genderData[] = $data->total_people;
        
                    $colorCode = "#F9C21A";
                    if ($data->gender == "Male") {
                        $colorCode = "#0891B2";
                    } else if ($data->gender == "Female") {
                        $colorCode = "#8B5CF6";
                    }
                    $this->genderLabels[] = $data->gender;
                    $this->genderData[] = [
                        "x" => $data->gender,
                        "y" => $data->total_people,
                        "fillColor" => $colorCode
                    ];
                }
        
                // //--------------------------- Company Split -------------------------------//
                $CompanyCountsData = People::selectRaw('company_name, COUNT(*) as total_people')->groupBy('company_name')
                                                            ->where('company_id','!=',$user->company_id)
                                                            ->orderBy('total_people', 'desc')
                                                            ->take(5)
                                                            ->get();
                                                            
                foreach ($CompanyCountsData as $data) {
                    $this->companyLabels[] = $data->company_name;
                    $this->companyData[] = $data->total_people;
                }
        
                //--------------------------- Location Split -------------------------------//
                $LocationCountsData = People::selectRaw('country, COUNT(*) as total_people')->groupBy('country')
                                                            ->where('company_id','!=',$user->company_id)
                                                            ->orderBy('total_people', 'desc')
                                                            ->take(5)
                                                            ->get();
                foreach ($LocationCountsData as $data) {
                    $this->locationLabels[] = $data->country;
                    $this->locationData[] = $data->total_people;
                }
        
                //-------------------------- Function Split -------------------------------//
                $functionCountsData = People::selectRaw('people.function, COUNT(*) as total_people')->groupBy('people.function')
                                                                ->where('company_id','!=',$user->company_id)
                                                                ->orderBy('total_people', 'desc')
                                                                ->take(5)
                                                                ->get();
                foreach ($functionCountsData as $data) {
                    $this->functionLabels[] = $data->function;
                    $this->functionData[] = $data->total_people;
                }
        
                //-------------------------- Division Split -------------------------------//
                $divisionCountsData = People::selectRaw('exco, COUNT(*) as total_people')->groupBy('exco')->get();
                foreach ($divisionCountsData as $data) {
                    $this->divisionLabels[] = $data->exco;
                    $this->divisionData[] = $data->total_people;
                }


        $totalPeopleQuery = People::whereNotIn('id', $this->pipeline)
        ->where('company_id','!=',$user->company_id)
        ->whereNotIn('status', [self::STATUS_SUBMITTED, self::STATUS_REPORTED]);

        $totalCompaniesQuery = $totalPeopleQuery->clone()->distinct('company_id');  

                $this->TotalCompanies = $totalCompaniesQuery->count('company_id');
                $this->TotalPeople = $totalPeopleQuery->count();

    }

    use WithPagination;
    public function render()
    {
        $user = auth()->user();

        $accountObj = Account::where('id', $user->account_id)->first();

        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        if($accountObj){
            if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }

        $this->pipeline = pipeline::where('plan_id', $this->plan)->pluck('people_id')->toArray();
    

        $People = People::whereNotIn('id', $this->pipeline)
                            ->where('status', '!=', 'Submitted')
                            ->where('company_id','!=',$user->company_id)
                            ->where('status', '!=', 'Reported')
                            ->select('id', 'forename','surname','gender','diverse','latest_role','company_name','country','people.function','tenure','readiness', 'other_tags');

        if ($this->sortBy && $this->sortDirection) {
            $People = $People->orderBy($this->sortBy, $this->sortDirection);
        }

        if ($this->search) {
            if ($this->forename) {
                $People = $People->where('forename', 'like', '%' . $this->forename . '%');
            }
            if ($this->surname) {
                $People = $People->where('surname', 'like', '%' . $this->surname . '%');
            }
            if ($this->gender) {
                $People = $People->where('gender', '=', $this->gender);
            }
            if ($this->role) {
                $People = $People->where('latest_role', 'like', '%' . $this->role . '%');
            }
            if ($this->previousRole) {
                $People = $People->where('parent_role', 'like', '%' . $this->previousRole . '%');
            }
            if ($this->company) {
                $People = $People->where('company_id', $this->company);
            }
            if ($this->function) {
                $People = $People->where('function', 'like', '%' . $this->function . '%');
            }
            if ($this->division) {
                $People = $People->where('division', 'like', '%' . $this->division . '%');
            }
            if ($this->location) {
                $People = $People->where('country', 'like', '%' . $this->location . '%');
            }
            if ($this->regBodies) {
                $People = $People->where('other_tags', 'like', '%' . $this->regBodies . '%');
            }
            if ($this->tenure) {
                $People = $People->where('tenure', 'like', '%' . $this->tenure . '%');
            }
        }

        if (!empty($this->searchByKeyword)) {
            $searchKeywords = explode(",", $this->searchByKeyword);
            $People = $People->where(function ($query) use ($searchKeywords) {
                foreach ($searchKeywords as $searchword) {
                    $query->orWhere(function ($subQuery) use ($searchword) {
                        $subQuery->orWhere('forename', 'like', '%' . $searchword . '%')
                            ->orWhere('surname', 'like', '%' . $searchword . '%')
                            ->orWhere('latest_role', 'like', '%' . $searchword . '%')
                            ->orWhere('company_name', 'like', '%' . $searchword . '%')
                            ->orWhere('function', 'like', '%' . $searchword . '%')
                            ->orWhere('division', 'like', '%' . $searchword . '%')
                            ->orWhere('country', 'like', '%' . $searchword . '%')
                            ->orWhere('summary', 'like', '%' . $searchword . '%')
                            ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                    });
                }
            });
        }

        $peopleData = $People->paginate($this->perPage);

        
        return view('livewire.external-people', ['People' => $peopleData]);
    }

    public function runfilters()
    {
        $this->search = true;
        $this->resetPage();
    }

    public function searchRecordByKeyword()
    {
        $this->resetPage();
    }

    public function Ordermytable($column)
    {
        // if ($column === $this->sortBy) {
        //     $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        // } else {
        //     $this->sortBy = $column;
        //     $this->sortDirection = 'asc';
        // }

        $this->sortBy = $column;
        $this->sortDirection = $sortDirection;
    }

    public function clearFilters()
    {
        $this->search = false;
        // Reset all form input properties
        $this->reset(['forename', 'surname', 'role', 'company', 'function', 'division', 'location', 'gender', 'regBodies', 'tenure', 'previousRole', 'min_exp']);
    }

    public function showAddToTalentPoolPopup($peopleId) {
        $this->selectedIndividualID = $peopleId;
        $this->talentPoolsList = getTalentPoolList($peopleId);
        $this->addToTalentPoolPopup = true;
    }

    public function addpeopleToTalentPools() {
        if(empty($this->addToTalentPoolArray)) {
            $this->dispatch('toast', 'info', 'Please select talent pool!');
            $this->skipRender();
            return;
        }

        foreach($this->addToTalentPoolArray as $key => $value) {
            if($value) {
                $JobPerson = JobPeople::where(['people_id' => $this->selectedIndividualID,'job_id' => $key])->first();
                //dd($this->selectedIndividualID);
                if($JobPerson) {
                    continue;
                }
                addToJob($this->selectedIndividualID, $key);
            }
        }
        if(!$this->disableDispatchforMultipleSelect) {
        $this->dispatch('toast', 'info', 'Added to selected talent pools!');
        $this->addToTalentPoolPopup = false;
        $this->vopen = false;
        $this->addToTalentPoolArray = [];
        }
    }

    public function addpeopleToPlans() {
       
        if(empty($this->addToPlansArray)) {
            $this->dispatch('toast', 'info', 'Please select plans!');
            $this->skipRender();
            return;
        }
        $selectedPlan = $this->plan;
        foreach($this->addToPlansArray as $key => $value) {
            if($value) {
                $PlanPerson = SuccessPeople::where(['people_id' => $this->selectedIndividualID,'plan_id' => $key])->first();
              
                if($PlanPerson) {
                  continue;
                }
                $this->plan = $key;
                $this->addToPlan($this->selectedIndividualID, false);
            }
        }
        $this->plan = $selectedPlan;
        if(!$this->disableDispatchforMultipleSelect) {
        $this->dispatch('toast', 'info', 'Added to selected plans!');
        $this->addToPlanPopup = false;
        $this->vopen = false;
        }

    }

    public function viewIndividual($id)
    {
        $this->selectedIndividualID = $id;
        $this->selectedIndividual = People::where('id', $id)->first();
        $this->selectedIndividualSkills = Skills::where('people_id', '=', $id)->get();

        // Get the career histories of the individual
        $peoplescareer = CareerHistories::where('people_id', $id)
            ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
            ->select('career_histories.*', 'companies.name as company_name')
            ->orderBy('career_histories.start_date', 'desc')
            ->get();

        $this->selectedIndividualCareerHistory = $peoplescareer;
        $user = auth()->user();
        $jobIds = JobPeople::where('people_id', $id)->pluck('job_id')->toArray();
        $this->userJobs = Job::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
        })
        ->whereIn('id', $jobIds)
        ->get();
        
        $sharedQuery = SuccessionPlan::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
        });
        
        // Get the filtered plans for `userPlans` using `planIds`.
        $planIds = SuccessPeople::where(['people_id' => $id, 'type' => 'External'])
            ->pluck('plan_id')
            ->toArray();
        
        $this->userPlans = (clone $sharedQuery)
            ->whereIn('id', $planIds)
            ->get();
        
        $this->plansList = getPlansList($id, $this->plan);
        $this->talentPoolsList = getTalentPoolList($id);

        
        $this->vopen = true;
    }

    public function showAddToPlanPopup($peopleId) {
        logCurrentDateTime("showAddToPlanPopup started");
        $this->selectedIndividualID = $peopleId;
        $this->plansList = getPlansList($peopleId, $this->plan);
        $this->addToPlanPopup = true;
        logCurrentDateTime("showAddToPlanPopup ended");

    }

    public function clearSelectedIndividuals()
    {
        $this->reset(['selectedIndividualID', 'selectedIndividual', 'selectedIndividualSkills', 'selectedIndividualCareerHistory', 'problem', 'reportDescription']);
    }

    public function AddPerson()
    {

        $this->validate(
            [
                'addLinkedinURL' => 'url'
            ],
            [
                'addLinkedinURL.url' => 'Please enter a valid LinkedIn url or leave it empty.'
            ]
        );

        //dd($this->selectedCompanies);
        $user = auth()->user();

        $companiesCheck = [
            $this->addForename,
            $this->addRole,
            $this->addFunction,
            $this->addDivision
        ];

        /*
        foreach ($companiesCheck as $company) {
            if (in_array($company, $this->companies)) {
                $this->errormessage = "Please add the company name to relevant field";
                return;
            }
        }
        */


        if ($this->addCompany === 'Internal') {
            $internalPerson = InternalPeople::create([
                'name'        => $this->addForename,
                'role'        => $this->addRole,
                'gender'      => $this->addGender,
                'function'    => $this->addFunction,
                'division'    => $this->addDivision,
                'start_date'  => $this->addStartDate,
                'location'    => $this->addLocation,
                'created_by'  => $user->id
            ]);

            $startDate = Carbon::parse($this->addStartDate);
            $currentDate = Carbon::now();
            $tenure = $currentDate->diffInYears($startDate);

            $roleai = $this->addRole;
            $locationai = $this->addLocation;
            $functionai = $this->addFunction;
            $divisionai = $this->addDivision;
            //$companyai = $itemData->company_name;

            $inputArray = [
                'role'     => $roleai,
                'location' => $locationai,
                'function' => $functionai,
                'division' => $divisionai
            ];

            // Create a formatted message for GPT-3
            $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
            foreach ($inputArray as $key => $value) {
                $message .= "$key: \"$value\"\n";
            }

            // Call GPT-3 to generate the headline
            $response = OpenAI::chat()->create([
                'model' => 'gpt-3.5-turbo',
                'messages' => [['role' => 'system', 'content' => $message]],
            ]);

            $generatedHeadline = $response->choices[0]->message->content;

            $internalPersonId = $internalPerson ? $internalPerson->id : null;

            $user = auth()->user();
            //dd($user);
            if ($user->id !== $this->plandetails->user_id) {
                $status = 'Proposed';
            } else {
                $status = 'Approved';
            }

            $SuccessPeople = SuccessPeople::create([
                'pipeline_id'        => $this->pipelineid,
                'plan_id'            => $this->plan,
                'user_id'            => $user->id,
                'people_id'          => $internalPersonId,
                'headline'           => $generatedHeadline,
                'first_name'         => $this->addForename,
                'last_name'          => $this->addSurname,
                'middle_name'        => "NA",
                'other_name'         => "NA",
                'gender'             => $this->addGender,
                'diverse'            => "NA",
                'location'           => $this->location,
                'linkedinURL'        => "NA",
                'latest_role'        => $this->addRole,
                'company_id'         => $user->company_id,
                'company_name'       => $this->yourCompany,
                'start_date'         => $this->addStartDate,
                'tenure'             => $tenure,
                'function'           => $this->addFunction,
                'division'           => $this->addDivision,
                'seniority'          => "NA",
                'exco'               => "Non Exco",
                'career_history'     => "NA",
                'educational_history' => "NA",
                'skills'             => "NA",
                'languages'          => "NA",
                'skills_match'       => 0,
                'education_match'    => 0,
                'location_match'     => 1,
                'role_match'         => 1,
                'gender_match'       => 1,
                'tenure_match'       => 1,
                'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                'type'               => "Internal",
                'notes'              => "Enter notes here",
                'recruit'            => 1,
                'status'             => $status,
            ]);

            $this->addedPerson = $SuccessPeople;

            if ($SuccessPeople->status === 'Proposed') {
                Notifications::create([
                    'type'              => "Internal-Proposed",
                    'plan_id'           => $SuccessPeople->plan_id,
                    'people_id'         => $SuccessPeople->id,
                    'entity_name'       => 'Proposed Candidate',
                    'description'       => $user->name . ' has proposed the internal candidate ' . $SuccessPeople->forename . ' ' . $SuccessPeople->latest_role . ' to your plan ',
                    'user_id'           => $user->id,
                    'user_company'      => $user->company_id
                ]);
            }

            pipeline::create([
                'id'                 => $this->pipelineid,
                'plan_id'            => $this->plan,
                'user_id'            => $user->id,
                'people_id'          => $internalPersonId,
                'headline'           => $generatedHeadline,
                'first_name'         => $this->addForename,
                'last_name'          => $this->addSurname,
                'middle_name'        => "NA",
                'other_name'         => "NA",
                'gender'             => $this->addGender,
                'diverse'            => "NA",
                'location'           => $this->location,
                'country'            => "NA",
                'city'               => "NA",
                'linkedinURL'        => "NA",
                'latest_role'        => $this->addRole,
                'company_id'         => $user->company_id,
                'company_name'       => $this->yourCompany,
                'start_date'         => $this->addStartDate,
                'tenure'             => $tenure,
                'function'           => $this->addFunction,
                'division'           => $this->addDivision,
                'seniority'          => "NA",
                'exco'               => "Non Exco",
                'career_history'     => "NA",
                'educational_history' => "NA",
                'skills'             => "NA",
                'languages'          => "NA",
                'skills_match'       => 0,
                'education_match'    => 0,
                'location_match'     => 1,
                'role_match'         => 1,
                'gender_match'       => 1,
                'tenure_match'       => 1,
                'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                'status'             => $status,
                'people_type'        => "Internal-User"
            ]);
        }

        // Adding External People into the sytem
        else {

            //dd('external people');
            $companyid = DB::table('companies')->where('name', $this->selectedCompanies)->value('id');

            if ($companyid === null) {
                $CreatedCompany = Company::create([
                    'name'          => $this->selectedCompanies,
                    'location_id'   =>  1784674685,
                    'status'        => 'submited'
                ]);

                $companyid = $CreatedCompany->id;
            }

            if ($this->addStartDate !== null) {
                $startDate = Carbon::parse($this->addStartDate);
                $currentDate = Carbon::now();
                $tenure = $currentDate->diffInYears($startDate);
            } else {
                $tenure = 0;
            }

            $ExternalPeople = People::create([

                'forename'    => $this->addForename,
                'surname'     => $this->addSurname,
                'middle_name' => $this->addMiddlename,
                'other_name'  => $this->addOtherName,
                'gender'      => $this->addGender,
                'diverse'     => "NA",
                'country'     => $this->addLocation,
                'linkedinURL' => $this->addLinkedinURL,
                'latest_role' => $this->addRole,
                'company_id'  => $companyid,
                'company_name' => $this->selectedCompanies,
                'function'    => $this->addFunction,
                'division'    => $this->addDivision,
                'tenure'      => $tenure,
                'status'      => "Submitted",
                'exco'        => "Non Exco",
                'user_id'  => $user->id,
            ]);

            $startDate = Carbon::parse($this->addStartDate);
            $currentDate = Carbon::now();
            $tenure = $currentDate->diffInYears($startDate);

            $roleai = $this->addRole;
            $locationai = $this->addLocation;
            $functionai = $this->addFunction;
            $divisionai = $this->addDivision;
            //$companyai = $itemData->company_name;

            $roleai = $this->addRole;
            $locationai = $this->addLocation;
            $tenureai = $tenure;
            $companyai = $ExternalPeople->company_name;

            $inputArray = [
                'role'     => $roleai,
                'location' => $locationai,
                'tenure'   => $tenureai,
                'company'  => $companyai,
            ];

            // Create a formatted message for GPT-3
            $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
            foreach ($inputArray as $key => $value) {
                $message .= "$key: \"$value\"\n";
            }

            // Call GPT-3 to generate the headline
            $response = OpenAI::chat()->create([
                'model' => 'gpt-3.5-turbo',
                'messages' => [['role' => 'system', 'content' => $message]],
            ]);

            $generatedHeadline = $response->choices[0]->message->content;

            $ExternalPersonId = $ExternalPeople ? $ExternalPeople->id : null;

            if ($this->addStartDate === null) {
                $this->addStartDate = now();
            };

            if ($user->id !== $this->plandetails->user_id) {
                $status = 'Proposed';
            } else {
                $status = 'Approved';
            }

            $SuccessPeople = SuccessPeople::create([
                'pipeline_id'        => $this->pipelineid,
                'plan_id'            => $this->plan,
                'user_id'            => $user->id,
                'people_id'          => $ExternalPersonId,
                'headline'           => $generatedHeadline,
                'first_name'         => $this->addForename,
                'last_name'          => $this->addSurname,
                'middle_name'        => $this->addMiddlename,
                'other_name'         => $this->addOtherName,
                'gender'             => $this->addGender,
                'diverse'            => "NA",
                'location'           => $this->addLocation,
                'linkedinURL'        => $this->addLinkedinURL,
                'latest_role'        => $this->addRole,
                'company_id'         => $companyid,
                'company_name'       => $this->selectedCompanies,
                'start_date'         => $this->addStartDate,
                'tenure'             => $tenure,
                'function'           => $this->addFunction,
                'division'           => $this->addDivision,
                'seniority'          => "NA",
                'exco'               => "Non Exco",
                'career_history'     => "NA",
                'educational_history' => "NA",
                'skills'             => "NA",
                'languages'          => "NA",
                'skills_match'       => 0,
                'education_match'    => 0,
                'location_match'     => 1,
                'role_match'         => 1,
                'gender_match'       => 1,
                'tenure_match'       => 1,
                'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                'type'               => "External",
                'status'             => $status,
                'notes'              => "Enter notes here",
                'recruit'            => 1,
            ]);

            $this->addedPerson = $SuccessPeople;

            pipeline::create([
                'id'                 => $this->pipelineid,
                'plan_id'            => $this->plan,
                'user_id'            => $user->id,
                'people_id'          => $ExternalPersonId,
                'headline'           => $generatedHeadline,
                'first_name'         => $this->addForename,
                'last_name'          => $this->addSurname,
                'middle_name'        => $this->addMiddlename,
                'other_name'         => $this->addOtherName,
                'gender'             => $this->addGender,
                'diverse'            => "NA",
                'country'            => $this->addLocation,
                'city'               => "NA",
                'location'           => $this->addLocation,
                'linkedinURL'        => $this->addLinkedinURL,
                'latest_role'        => $this->addRole,
                'company_id'         => $user->company_id,
                'company_name'       => $this->selectedCompanies,
                'start_date'         => $this->addStartDate,
                'tenure'             => $tenure,
                'function'           => $this->addFunction,
                'division'           => $this->addDivision,
                'seniority'          => "NA",
                'exco'               => "Non Exco",
                'career_history'     => "NA",
                'educational_history' => "NA",
                'skills'             => "NA",
                'languages'          => "NA",
                'skills_match'       => 0,
                'education_match'    => 0,
                'location_match'     => 1,
                'role_match'         => 1,
                'gender_match'       => 1,
                'tenure_match'       => 1,
                'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                'status'             => $status,
                'people_type'        => "External-User"
            ]);


            if ($SuccessPeople->status === 'Proposed') {
                Notifications::create([
                    'type'              => "External-Proposed",
                    'plan_id'           => $SuccessPeople->plan_id,
                    'people_id'         => $SuccessPeople->id,
                    'entity_name'       => 'Proposed Candidate',
                    'description'       => $user->name . ' has proposed ' . $SuccessPeople->forename . ' ' . $SuccessPeople->latest_role . ' from ' . $SuccessPeople->company_name . ' to your plan ',
                    'user_id'           => $user->id,
                    'user_company'      => $user->company_id
                ]);
            }
        }



        //------------------- Get the new Gender Diversity Score -----------------//
        $femaleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "female")/Count(*))*100 as female_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Female-Ratio'],
            ['score' => $femaleRatio->female_ratio]
        );


        $maleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "male")/Count(*))*100 as male_ratio'))
            ->groupBy('plan_id')
            ->first();


        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Male-Ratio'],
            ['score' => $maleRatio->male_ratio]
        );

        $InternalRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Internal-External Ratio'],
            ['score' => $InternalRatio->internal_ratio]
        );

        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Skill Score'],
            ['score' => $averageskillscore->average_skill_score]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Tenure Score'],
            ['score' => $averagetenurescore->average_tenure_score]
        );

        $this->step++;
    }

    public function addToPlan($id, $showToast = true)
    {
        $user = auth()->user();
       
        // Get the details of the external person
        $ExternalPerson = People::select(['*'])->where('id', $id)->first();

        $successionPlan = SuccessionPlan::find($this->plan);

        $tenureMatch = 0;
        if ($ExternalPerson->tenure >= $successionPlan->minimum_Experience) {
            $tenureMatch = 1;
        }



        //Get the skills of the person this will be used for the radar chart
        $PersonSkills = Skills::where('people_id', $id)->get();
        if ($ExternalPerson->addStartDate === null) {
            $ExternalPerson->start_date = now();
        };

        //Get there scores ready for the pipeline and plans
        //Role Scores
        $latestRole = $ExternalPerson->latest_role;

        $RoleScore = 0;
        if ($this->SuccessRoles->contains('name', $latestRole)) {
            $RoleScore = 1;
        } else {
            if ($this->SuccessStepup->contains('name', $latestRole)) {
                $RoleScore = 0.75;
            } else {
                $RoleScore = 0;
            }
        }

        //Skill Score
        if ($this->SuccessSkills->count() > 0) {
            $SkillScore = 0;
            foreach ($PersonSkills as $pK) {
                foreach ($this->SuccessSkills as $sK) {
                    if ($sK->name == $pK->skill_name) {
                        $SkillScore++;
                        break;
                    }
                }
            }

            $SkillScore /= $this->SuccessSkills->count();
        } else {
            $SkillScore = 1;
        }


        //Gender Match
        $Gender_Match = 0;
        if ($this->SuccessGender->contains('name', $ExternalPerson->gender)) {
            $Gender_Match = 1;
        } else {
            $Gender_Match = 0;
        }

        // Location Match
        $Location_Match = 0;
        if ($this->SuccessLocation->contains('name', $ExternalPerson->location)) {
            $Location_Match = 1;
        } else {
            $Location_Match = 0;
        }

        //Tenture Match


        $roleai = $ExternalPerson->latest_role;
        $locationai = $ExternalPerson->location;
        $functionai = $ExternalPerson->function;
        $divisionai = $ExternalPerson->division;
        $readiness = $ExternalPerson->readiness;

        $inputArray = [
            'role'     => $roleai,
            'location' => $locationai,
            'function' => $functionai,
            'division' => $divisionai
        ];

        // Create a formatted message for GPT-3
        $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
        foreach ($inputArray as $key => $value) {
            $message .= "$key: \"$value\"\n";
        }

        // Call GPT-3 to generate the headline
        $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
        ]);
        // dd($ExternalPerson->readiness);
        $generatedHeadline = $response->choices[0]->message->content;
        $newPipeline = pipeline::create([
            'plan_id'            => $this->plan,
            'user_id'            => $user->id,
            'people_id'          => $ExternalPerson->id,
            'headline'           => $generatedHeadline,
            'first_name'         => $ExternalPerson->forename,
            'last_name'          => $ExternalPerson->surname,
            'middle_name'        => $ExternalPerson->middle_name,
            'other_name'         => $ExternalPerson->other_name,
            'gender'             => $ExternalPerson->gender,
            'diverse'            => $ExternalPerson->diverse,
            'location'           => $ExternalPerson->location,
            'summary'            => $ExternalPerson->summary,
            'country'            => $ExternalPerson->country,
            'city'               => $ExternalPerson->city,
            'linkedinURL'        => $ExternalPerson->linkedinURL,
            'latest_role'        => $ExternalPerson->latest_role,
            'company_id'         => $ExternalPerson->company_id,
            'company_name'       => $ExternalPerson->company_name,
            'start_date'         => $ExternalPerson->start_date,
            'end_date'           => $ExternalPerson->end_date,
            'tenure'             => $ExternalPerson->tenure,
            'function'           => $ExternalPerson->function,
            'division'           => $ExternalPerson->division,
            'seniority'          => $ExternalPerson->seniority,
            'exco'               => $ExternalPerson->exco,
            'career_history'     => $ExternalPerson->career_history,
            'educational_history' => $ExternalPerson->educational_history,
            'skills'             => $ExternalPerson->skills,
            'languages'          => $ExternalPerson->languages,
            'readiness'          => $ExternalPerson->readiness,
            'other_tags'         => $ExternalPerson->other_tags,
            'skills_match'       => $SkillScore,
            'education_match'    => 0,
            'location_match'     => $Location_Match,
            'role_match'         => $RoleScore,
            'gender_match'       => $Gender_Match,
            'tenure_match'       => $tenureMatch,
            'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
            'people_type'        => "External-User",
        ]);

        $successPeopleData = [
            'pipeline_id'        => $newPipeline->id,
            'plan_id'            => $this->plan,
            'user_id'            => $user->id,
            'people_id'          => $ExternalPerson->id,
            'headline'           => $generatedHeadline,
            'first_name'         => $ExternalPerson->forename,
            'last_name'          => $ExternalPerson->surname,
            'middle_name'        => $ExternalPerson->middle_name,
            'other_name'         => $ExternalPerson->other_name,
            'gender'             => $ExternalPerson->gender,
            'diverse'            => $ExternalPerson->diverse,
            'location'           => $ExternalPerson->country,
            'summary'            => $ExternalPerson->summary,
            'linkedinURL'        => $ExternalPerson->linkedinURL,
            'latest_role'        => $ExternalPerson->latest_role,
            'company_id'         => $ExternalPerson->company_id,
            'company_name'       => $ExternalPerson->company_name,
            'start_date'         => $ExternalPerson->start_date,
            'end_date'           => $ExternalPerson->end_date,
            'tenure'             => $ExternalPerson->tenure,
            'function'           => $ExternalPerson->function,
            'division'           => $ExternalPerson->division,
            'seniority'          => $ExternalPerson->seniority,
            'exco'               => $ExternalPerson->exco,
            'career_history'     => $ExternalPerson->career_history,
            'educational_history' => $ExternalPerson->educational_history,
            'skills'             => $ExternalPerson->skills,
            'languages'          => $ExternalPerson->languages,
            'readiness'          => $ExternalPerson->readiness,
            'other_tags'         => $ExternalPerson->other_tags,
            'city'               => $ExternalPerson->city,
            'country'            => $ExternalPerson->country,
            'skills_match'       => $SkillScore,
            'education_match'    => 0,
            'location_match'     => $Location_Match,
            'role_match'         => $RoleScore,
            'gender_match'       => $Gender_Match,
            'tenure_match'       => $tenureMatch,
            'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
            'type'               => "External",
            'notes'              => "Enter notes here",
            'status'             => "Approved",
            'recruit'            => 1,
        ];

        if($successionPlan->user_id != auth()->user()->id) {
            $successPeopleData['status'] = "Proposed";
        }

        $SuccessPeople = SuccessPeople::create($successPeopleData);

        
        //------------------- Get the new Gender Diversity Score -----------------//
        $femaleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Female")/Count(*))*100 as female_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Female-ratio'],
            ['score' => $femaleRatio->female_ratio]
        );

        $maleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Male")/Count(*))*100 as male_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();


        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Male-Ratio'],
            ['score' => $maleRatio->male_ratio]
        );

        $InternalRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Internal-External Ratio'],
            ['score' => $InternalRatio->internal_ratio]
        );

        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Skill Score'],
            ['score' => $averageskillscore->average_skill_score]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Tenure Score'],
            ['score' => $averagetenurescore->average_tenure_score]
        );

        if ($this->SuccessSkills->isNotEmpty()) {
            // Add requirements for chart for skills radar
            $SkillFound = false;
            foreach ($this->SuccessSkills as $sK) {
                $SkillFound = false;
                foreach ($PersonSkills as $pK) {
                    if ($sK->name == $pK->skill_name) {
                        SuccessSkills::create([
                            'succession_plan_id' => $this->plan,
                            'skill_name'        => $sK->name,
                            'success_people_id' => $SuccessPeople->id,
                            'success_requirements_id' => $sK->id,
                            'score' => 1
                        ]);
                        $SkillFound = true;
                        break;
                    }
                }

                if (!$SkillFound) {
                    SuccessSkills::create([
                        'succession_plan_id' => $this->plan,
                        'success_people_id' => $SuccessPeople->id,
                        'success_requirements_id' => $sK->id,
                        'skill_name'        => $sK->name,
                        'score' => 0
                    ]);
                }
            }
        } else {
        }
        if ($showToast) {
            $this->dispatch('toast', 'info', "Added to plan!");
        }
    }

    public function addToPotentialCandidate($id) {
        $user = auth()->user();

        // Get the details of the external person
        $ExternalPerson = People::find($id);
        $successionPlan = SuccessionPlan::find($this->plan);

        $tenureMatch = 0;
        if ($ExternalPerson->tenure >= $successionPlan->minimum_Experience) {
            $tenureMatch = 1;
        }

        //Get the skills of the person this will be used for the radar chart
        $PersonSkills = Skills::where('people_id', $id)->get();
            if ($ExternalPerson->addStartDate === null) {
                $ExternalPerson->start_date = now();
            };
  

         //Role Scores
         $latestRole = $ExternalPerson->latest_role;

         $RoleScore = 0;
         if ($this->SuccessRoles->contains('name', $latestRole)) {
             $RoleScore = 1;
         } else {
             if ($this->SuccessStepup->contains('name', $latestRole)) {
                 $RoleScore = 0.75;
             } else {
                 $RoleScore = 0;
             }
         }
 
         //Skill Score
         if ($this->SuccessSkills->count() > 0) {
             $SkillScore = 0;
             foreach ($PersonSkills as $pK) {
                 foreach ($this->SuccessSkills as $sK) {
                     if ($sK->name == $pK->skill_name) {
                         $SkillScore++;
                         break;
                     }
                 }
             }
 
             $SkillScore /= $this->SuccessSkills->count();
         } else {
             $SkillScore = 1;
         }
 
 
         //Gender Match
         $Gender_Match = 0;
         if ($this->SuccessGender->contains('name', $ExternalPerson->gender)) {
             $Gender_Match = 1;
         } else {
             $Gender_Match = 0;
         }
 
         // Location Match
         $Location_Match = 0;
         if ($this->SuccessLocation->contains('name', $ExternalPerson->location)) {
             $Location_Match = 1;
         } else {
             $Location_Match = 0;
         }

        
        $roleai = $ExternalPerson->latest_role;
        $locationai = $ExternalPerson->location;
        $functionai = $ExternalPerson->function;
        $divisionai = $ExternalPerson->division;

        $inputArray = [
            'role'     => $roleai,
            'location' => $locationai,
            'function' => $functionai,
            'division' => $divisionai
        ];

        // Create a formatted message for GPT-3
        $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
        foreach ($inputArray as $key => $value) {
            $message .= "$key: \"$value\"\n";
        }

        // Call GPT-3 to generate the headline
        $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
        ]);
        // dd($ExternalPerson->readiness);
        $generatedHeadline = $response->choices[0]->message->content;
        $newPipeline = pipeline::create([
            'plan_id'            => $this->plan,
            'user_id'            => $user->id,
            'people_id'          => $ExternalPerson->id,
            'headline'           => $generatedHeadline,
            'first_name'         => $ExternalPerson->forename,
            'last_name'          => $ExternalPerson->surname,
            'middle_name'        => $ExternalPerson->middle_name,
            'other_name'         => $ExternalPerson->other_name,
            'gender'             => $ExternalPerson->gender,
            'diverse'            => $ExternalPerson->diverse,
            'location'           => $ExternalPerson->location,
            'summary'            => $ExternalPerson->summary,
            'country'            => $ExternalPerson->country,
            'city'               => $ExternalPerson->city,
            'linkedinURL'        => $ExternalPerson->linkedinURL,
            'latest_role'        => $ExternalPerson->latest_role,
            'company_id'         => $ExternalPerson->company_id,
            'company_name'       => $ExternalPerson->company_name,
            'start_date'         => $ExternalPerson->start_date,
            'end_date'           => $ExternalPerson->end_date,
            'tenure'             => $ExternalPerson->tenure,
            'function'           => $ExternalPerson->function,
            'division'           => $ExternalPerson->division,
            'seniority'          => $ExternalPerson->seniority,
            'exco'               => $ExternalPerson->exco,
            'career_history'     => $ExternalPerson->career_history,
            'educational_history' => $ExternalPerson->educational_history,
            'skills'             => $ExternalPerson->skills,
            'languages'          => $ExternalPerson->languages,
            'readiness'          => $ExternalPerson->readiness,
            'other_tags'         => $ExternalPerson->other_tags,
            'skills_match'       => $SkillScore,
            'education_match'    => 0,
            'location_match'     => $Location_Match,
            'role_match'         => $RoleScore,
            'gender_match'       => $Gender_Match,
            'tenure_match'       => $tenureMatch,
            'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
            'people_type'        => "External-User",
        ]);
        
        if(!$this->disableDispatchforMultipleSelect) {
            $this->dispatch('toast', 'info', "Added to potential candidate!");
        }

    }

    public function reportPerson($id)
    {
        $user = auth()->user();
        //dd('working');
        $reportPerson = People::findorfail($id);
        $reportPerson->update([
            'status' => 'Reported',
            'user_id' => $user->id,
            'notes' => $this->reportDescription
        ]);

        $this->reset(['selectedIndividualID', 'selectedIndividual', 'selectedIndividualSkills', 'selectedIndividualCareerHistory', 'problem', 'reportDescription']);

        //dd($reportPerson);

    }

    public function uploadCSV()
    {
        $this->validate([
            'csvFile' => 'required|mimes:csv,xlsx,xls|max:2048', // accept only csv and excel files with a max size of 2MB
        ], [
            'csvFile.required' => "Please select excel or csv file."
        ]);
        try {
            $user = auth()->user();
            $this->processExcel($this->csvFile, $user);
        } catch (Exception $e) {

            $this->dispatch('toast', 'error', $e->getMessage());
        }
    }

    public function downloadCSV()
    {
        $headerRow = ['forename', 'surname', 'other_name', 'gender', 'city', 'country', 'linkedinURL', 'role', 'start_date', 'company_name'];

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="Upload_Template.csv"',
        ];

        return Response::stream(function () use ($headerRow) {
            $handle = fopen('php://output', 'w');

            fputcsv($handle, $headerRow);

            fclose($handle);
        }, 200, $headers);
    }

    private function processExcel($file, $user)
    {
        // $excelData = Excel::toArray(new PeopleImport, $file);
        $excelData = Excel::toArray(new PeopleImport, $file->getRealPath());
        // Assuming PeopleImport is your import class for Excel, change it accordingly

        $duplicates = [];
        // $skipFirstRow = true;

        foreach ($excelData[0] as $row) {
            // dd($excelData);
            // if ($skipFirstRow) {
            //     $skipFirstRow = false; // Skip the header row
            //     continue;
            // }

            // Adjust the indices according to your Excel file structure
            $forename = $row['forename'];
            $surname = $row['surname'];
            $otherName = $row['other_name'];
            $gender = $row['gender'];
            $city = $row['city'];
            $country = $row['country'];
            $linkedinURL = $row['linkedinurl'];
            $role = $row['role'];
            $startDate = $row['start_datemdy'];
            $companyName = $row['company_name'];

            // Check if a record with the given forename and surname exists
            $existingRecord = People::where('forename', $forename)
                ->where('surname', $surname)
                ->get();

            if ($existingRecord->isNotEmpty()) {
                // Handle duplicates
                $duplicates["$forename-$surname-$role-$companyName"] = (object) [
                    'forename'    => $forename,
                    'surname'     => $surname,
                    'other_name'  => $otherName,
                    'gender'      => $gender,
                    'linkedinURL' => $linkedinURL,
                    'latest_role' => $role,
                    'companyName' => $companyName,
                    'start_date' => $startDate,
                    'duplicate'   => json_decode(json_encode($existingRecord->toArray()), false)
                ];
            } else {
                // Handle company creation if not exists
                $companyid = DB::table('companies')->where('name', $companyName)->value('id');

                if ($companyid === null) {
                    $CreatedCompany = Company::create([
                        'name'          => $companyName,
                        'location_id'   =>  1784674685,
                        'status'        => 'submitted'
                    ]);
                } else {
                    $CreatedCompany = Company::find($companyid);
                }

                $startDate = Carbon::parse($startDate);
                $currentDate = Carbon::now();
                $tenure = $currentDate->diffInYears($startDate);
                $startDate = $startDate;


                // Create new People record
                $people = People::create([
                    'forename'      => $forename,
                    'surname'       => $surname,
                    'latest_role'   => $role,
                    'gender'        => $gender,
                    'company_id'    => $CreatedCompany->id,
                    'company_name'  => $CreatedCompany->name,
                    'start_date'    => $startDate,
                    'country'       => $country,
                    'city'          => $city,
                    'linkedinURL'   => $linkedinURL,
                    'tenure'        => $tenure,
                    'exco'          => "Non Exco",
                    'status'        => 'submitted',
                    'user_id'       => $user->id,
                ]);

                // Create associated pipeline record
                Pipeline::create([
                    'id'                 => $this->pipelineid,
                    'plan_id'            => $this->plan,
                    'user_id'            => $user->id,
                    'people_id'          => $people->id,
                    'first_name'         => $people->forename,
                    'last_name'          => $people->surname,
                    'gender'             => $people->gender,
                    'diverse'            => "NA",
                    'location'           => $people->country,
                    'summary'            => $people->summary,
                    'country'            => $people->country,
                    'city'               => $people->city,
                    'linkedinURL'        => $people->linkedinURL,
                    'latest_role'        => $people->latest_role,
                    'company_id'         => $people->company_id,
                    'company_name'       => $people->company_name,
                    'start_date'         => $people->start_date,
                    'readiness'          => $people->readiness,
                    'tenure'             => $tenure,
                    'seniority'          => "NA",
                    'exco'               => "Non Exco",
                    'career_history'     => "NA",
                    'educational_history' => "NA",
                    'skills'             => "NA",
                    'languages'          => "NA",
                    'skills_match'       => 0,
                    'education_match'    => 0,
                    'location_match'     => 1,
                    'role_match'         => 1,
                    'gender_match'       => 1,
                    'tenure_match'       => 1,
                    'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
                    'people_type'        => "External-User"
                ]);
            }
        }

        // Handle duplicates or success
        $this->bopen = false;

        if (count($duplicates) > 0) {
            $this->duplicateRecords = (object) $duplicates;
            $this->duplicateUploadPopup = true;
            Log::info(json_encode($this->duplicateRecords));
            return;
        }
        // else {
        //     $this->hasCandidatesUploaded = true;
        // }
        $this->dispatch('toast', 'info',"File Uploaded Successfully.");

        // Reset error messages and file upload state
        $this->reset(['errormessage1', 'errormessage2', 'errormessage3', 'csvFile']);
    }


    public function saveSelectedRecords()
    {

        foreach ($this->duplicateRecords as $key => $duplicateRecord) {

            if (in_array($key, $this->selectedRecords)) {
                $this->createPeopleAndAddToJob($duplicateRecord);
                continue;
            }

            $isExistingUserUsed = false;

            foreach ($duplicateRecord->duplicate as $people) {
                if (in_array($people->id, $this->selectedRecords)) {
                    $isExistingUserUsed = true;
                    $this->addToPlan($people->id, false);
                }
            }

            if (!$isExistingUserUsed) {
                $this->createPeopleAndAddToJob($duplicateRecord);
            }
        }
        $this->dispatch('toast', 'info',  "File uploaded successfully");
        $this->duplicateUploadPopup = false;
        $this->reset(['errormessage1', 'errormessage2', 'errormessage3', 'csvFile']);
        $this->selectedRecords = [];
    }
    public function createPeopleAndAddToJob($record)
    {
        $createdCompany = Company::where('name', trim($record->companyName))->first();

        if (empty($createdCompany)) {
            $createdCompany = Company::create([
                'name'          => $record->companyName,
                'location_id'   =>  1784674685,
                'status'        => 'submited'
            ]);
        }

        $user = auth()->user();
        $startDate = Carbon::parse($record->start_date);
        $currentDate = Carbon::now();
        $tenure = $currentDate->diffInYears($startDate);
        // $startDate = Carbon::createFromFormat('m/d/Y', trim($record->start_date))->format('Y-m-d');
        $externalPeople = People::create([

            'forename'    => $record->forename,
            'surname'     => $record->surname,
            'other_name'  => $record->other_name,
            'gender'      => $record->gender,
            'start_date'      => $startDate,
            'diverse'     => "NA",
            'linkedinURL' => $record->linkedinURL,
            'latest_role' => $record->latest_role,
            'company_id'  => $createdCompany->id,
            'company_name' => $createdCompany->name,
            'tenure'        => $tenure,
            'exco'          => "Non Exco",
            'status'        => 'Submitted',
            'user_id'     => $user->id,
        ]);

        $this->addToPlan($externalPeople->id, false);
    }

    public function updatingPerPage($value)
    {
        $this->resetPage();
    }

    public function changeMinimumTenure($flag)
    {
        if ($flag == 'decrease') {
            $this->min_exp = $this->min_exp <= 1 ? null : ($this->min_exp - 1);
        } else if ($flag == 'increase') {
            $this->min_exp = !$this->min_exp ? 1 : ($this->min_exp + 1);
        }
    }

    public function validateStepOne()
    {
        $this->validate(
            [
                'addForename' => 'required|string',
                'addSurname' => 'required|string',
                'addRole' => 'required|string',
                'selectedCompanies' => 'required|string'
            ],
            [
                'addForename.required' => 'The forename field is required.',
                'addForename.string' => 'The forename must be a string.',
                'addSurname.required' => 'The surname field is required.',
                'addSurname.string' => 'The surname must be a string.',
                'addRole.required' => 'The role field is required.',
                'addRole.string' => 'The role must be a string.',
                'selectedCompanies.required' => 'The company field is required.',
                'selectedCompanies.string' => 'The company must be a string.'
            ]
        );

        $createdCompany = Company::where('name', $this->selectedCompanies)->first();

        if (!empty($createdCompany)) {
            $this->isPeopleAlreadyExists = People::where([
                'forename' => $this->addForename,
                'surname' => $this->addSurname,
                'company_id' => $createdCompany->id
            ])->get();

            if (count($this->isPeopleAlreadyExists) > 0) {
                $this->peopleAreadyExistsPopup = true;
            }
        }

        $this->step++;
    }

    public function validateStepTwo()
    {
        $this->step++;
    }

    public function previousStep()
    {
        // Move to the previous step
        $this->step--;
    }

    public function addPersonModalClosed()
    {
        $this->step = 1;
        $this->reset(['addForename', 'addSurname', 'addMiddlename', 'addOtherName', 'addLinkedinURL', 'addRole', 'addStartDate', 'selectedCompanies', 'addLocation', 'addGender']);
    }

    public function uploadCandidatesModalClosed()
    {
        $this->hasCandidatesUploaded = false;
        $this->reset(['errormessage1', 'errormessage2', 'errormessage3', 'csvFile']);
    }

    public function useExistingPeople()
    {
        $this->validate([
            'existingPeopleField' => 'required',
        ]);
        $this->addToPlan($this->existingPeopleField);
        $this->peopleAreadyExistsPopup = false;
        $this->Aopen = false;
        $this->step = 1;
        $this->addPersonModalClosed();
    }

    
    public function addSelectedToTalentPool()
    {  
        $this->talentPoolsList = collect(); 
        $processed = [];
        foreach ($this->selectedPerson as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->talentPoolsList = $this->talentPoolsList->merge(getTalentPoolList($peopleId));
                $processed[] = $peopleId;
            }
        }
       
        $this->talentPoolsList = $this->talentPoolsList->unique('id')->values();
        $this->addToTalentPoolPopup = true;
        $this->openTalentPopup = true;
    }

    public function addSelectedPersonToTalentPool()
    { 
        $this->disableDispatchforMultipleSelect = true;
        foreach($this->selectedPerson as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;
            $this->addpeopleToTalentPools();
        }
        $this->openTalentPopup = false;
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected talent pools!');
        $this->addToTalentPoolPopup = false;
        $this->vopen = false;
        $this->addToTalentPoolArray = [];
        $this->selectedPerson = [];
       
    }
    

    public function addSelectedToPlan()
    { 
      
        $this->plansList = collect(); 
        $processed = [];
        foreach ($this->selectedPerson as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->plansList = $this->plansList->merge(getPlansList($peopleId, $this->plan));
                $processed[] = $peopleId;
            }
        }
        
        $this->plansList = $this->plansList->unique('id')->values();
       
        $this->addToPlanPopup = true;
        $this->openPlanPopup = true;
        
    }
    public function addSelectedPersonToPlans()
    {
      $this->disableDispatchforMultipleSelect = true;
       foreach ($this->selectedPerson as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;

            $this->addpeopleToPlans();
       }
        $this->openPlanPopup = false;
        $this->selectedPerson = [];
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected plans!');
        $this->addToPlanPopup = false;
       
        $this->vopen = false;
        $this->addToPlansArray = [];
    }

    public function addSelectedToPotential()
    {
        $this->disableDispatchforMultipleSelect = true;
        foreach ($this->selectedPerson as $id) { 
            $this->addToPotentialCandidate($id);
        }
        $this->selectedPerson = [];
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to potential candidates!');
    }


    /*public function deleteSelected()
    {
        $selectedIds = $this->selectedPerson;
        foreach ($selectedIds as $id) {
            People::where('id', $id)->delete();
        }
        $this->dispatch('toast', 'success', 'Individual has deleted successfully.');
        $this->selectedPerson = [];
    }*/
    public function updatingPage() {
        $this->selectedPerson = [];
    }
}
