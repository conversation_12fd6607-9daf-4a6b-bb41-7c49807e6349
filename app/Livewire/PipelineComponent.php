<?php
 
namespace App\Livewire;

use App\Models\notifications;
use Livewire\Component;
use Carbon\Carbon;
use App\Models\SuccessionPlan;
use Livewire\WithPagination;
use App\Models\pipeline;
use App\Models\SuccessPeople;
use App\Models\InternalPeople;
use App\Models\Plans;
use App\Models\Skills;
use App\Models\InternalSkills;
use App\Models\CareerHistories;
use App\Models\SuccessSkills;
use App\Models\SuccessRequirements;
use App\Models\PlanScores;
use App\Models\Role;
use App\Models\JobPeople;
use App\Models\Job;
use App\Models\Job_Scores;
use App\Models\Company;
use App\Models\Location;
use App\Models\People;
use App\Models\UserNotes;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use OpenAI\Laravel\Facades\OpenAI;
use App\Exports\SuccessPeopleExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Account;
use App\Models\User;
use App\Mail\SendToRelationshipManager;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class PipelineComponent extends Component
{
    use WithPagination;


    // References for pipeline table
    public $plandetails;
    public $openPlanPopup=false;
    public $openTalentPopup=false;
    public $disableDispatchforMultipleSelect=false;
    public $plan;
    public $plandesc;
    public $pipeline;
    public $selectedPeople = [];
    public $selectedIndividualID;
    public $sortBy = 'skills_match';
    public $sortDirection = 'desc';
    public $search = false;
    public $searchByKeyword = '';
    public $personRoles;
    public $companies;
    public $successPersonNotes;
    public $filteredPnotes;
    public $selectedPerson = [];
    // Variables for filters
    public $forename = "";
    public $surname = "";
    public $gender = "";
    public $role;
    public $_role = [];
    public $previousRole = [];
    public $company = [];
    public $function = [];
    public $division = [];
    public $location = [];
    public $regBodies = "";
    public $moverFilter = "";
    public $tenure;
    public $totalPeople;
    public $totalCompanies;
    public $locations;
    public $functions;
    public $divisions;
    public $min_exp =   null;

    // References for charts
    public $genderLabels = [];
    public $genderData = [];
    public $totalScoreLabels = [];
    public $totalScoreData = [];
    public $companyLabels = [];
    public $companyData = [];
    public $functionLabels = [];
    public $functionData = [];
    public $divisionLabels = [];
    public $divisionData = [];
    public $roleLabels = [];
    public $roleData = [];
    public $perPage =   50; // Number of items per page
    public $page    =   1; // Current page number
    
    //References for adding to plans
    public $SuccessSkills;
    public $userPlans = [];
    public $userJobs = [];
    public $peopleType = null;
    public $addToPlanPopup = false;
    public $plansList = [];
    public $addToPlansArray = [];
    public $addToTalentPoolArray = [];
    public $addToTalentPoolPopup = false;
    public $talentPoolsList = [];
    public $sendemailopen = false;
    public $emailMessage;
    public $vopen = false;
    public $SuccessRoles;
    public $SuccessSkillsQuery;
    public $SuccessLocation;
    public $SuccessGender;
    public $SuccessStepup;


    public function mount()
    {
        $successplan = SuccessPeople::where('plan_id', $this->plan)->pluck('people_id')->toArray();
        $this->plandetails = SuccessionPlan::where('id', $this->plan)->first();
        //dd($this->plandetails);

        $this->SuccessSkills = SuccessRequirements::where('plan_id', $this->plan)
            ->where('type', 'professional_skill')
            ->get();


        $this->totalPeople = DB::table('pipelines')
            ->select(['*'])
            ->where('plan_id', $this->plan)
            ->whereNotIn('people_id', $successplan)
            ->count();

        // dd($successplan);
        $this->totalCompanies = DB::table('pipelines')
            ->select(['company_name'])
            ->where('plan_id', $this->plan)
            ->whereNotIn('people_id', $successplan)
            ->groupBy('company_name')
            ->get();
            // ->count();
        $this->totalCompanies = count($this->totalCompanies);
        $this->personRoles = Role::distinct()->pluck('title')->map(function ($role) {
            return [
                'value' => $role,
                'label' => $role,
            ];
        })->toArray();

        $companiesArr = pipeline::whereNotNull('company_id')
                                ->whereNotNull('company_name')
                                ->where('company_name', '!=', "")
                                ->distinct()
                                ->orderBy('company_name', 'ASC')
                                ->pluck('company_name', 'company_id')
                                ->toArray();
        foreach($companiesArr as $companyId => $companyName){
            $this->companies[] = ['value' => $companyId, 'label' => $companyName];
        }

        $this->functions = pipeline::whereNotNull('function')
                                ->where('function', '!=', "")
                                ->distinct()
                                ->orderBy('function', 'ASC')
                                ->pluck('function')
                                ->map(function ($function) {
                                    return [
                                        'value' => $function,
                                        'label' => $function,
                                    ];
                                })
                                ->toArray();

        $this->divisions = DB::table('pipelines')
                                ->whereNotNull('division')
                                ->where('division', '!=', "")
                                ->distinct()
                                ->orderBy('division', 'ASC')
                                ->pluck('division')
                                ->map(function ($division) {
                                    return [
                                        'value' => $division,
                                        'label' => $division,
                                    ];
                                })
                                ->toArray();


        $this->locations = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();

        $successRequirements = SuccessRequirements::where('plan_id', $this->plan)->get();

        // Group or filter the results based on the 'type'
        $this->SuccessSkillsQuery = $successRequirements->where('type', 'professional_skill');
        $this->SuccessRoles = $successRequirements->where('type', 'Role');
        $this->SuccessLocation = $successRequirements->where('type', 'Location');
        $this->SuccessGender = $successRequirements->where('type', 'Gender');
        $this->SuccessStepup = $successRequirements->where('type', 'step_up');

        // $this->getChartsData();
        // $this->dispatch('updateChart');

        // foreach ($peopleCountsData as $data) {
        //     $this->genderLabels[] = $data->gender;
        //     $this->genderData[] = $data->total_people;
        // }

        // dd($this->locations);

        //--------------------------- Company Split -------------------------------//
        // $CompanyCountsData = DB::table('pipelines')
        //     ->select('company_name', DB::raw('COUNT(*) as total_people'))
        //     ->where('plan_id', $this->plan)
        //     ->whereNotIn('people_id', $successplan)
        //     ->groupBy('company_name')
        //     ->orderBy('total_people', 'desc')
        //     ->take(5)
        //     ->get();

        // foreach ($CompanyCountsData as $data) {
        //     $this->companyLabels[] = $data->company_name;
        //     $this->companyData[] = $data->total_people;
        // }

        // //-------------------------- Function Split -------------------------------//
        // $functionCountsData = DB::table('pipelines')
        //     ->select('function', DB::raw('COUNT(*) as total_people'))
        //     ->where('plan_id', $this->plan)
        //     ->whereNotIn('people_id', $successplan)
        //     ->groupBy('function')
        //     ->orderBy('total_people', 'desc')
        //     ->get();

        // foreach ($functionCountsData as $data) {
        //     $this->functionLabels[] = $data->function;
        //     $this->functionData[] = $data->total_people;
        // }


        // //-------------------------- Division Split -------------------------------//
        // $divisionCountsData = DB::table('pipelines')
        //     ->select('location', DB::raw('COUNT(*) as locations'))
        //     ->where('plan_id', $this->plan)
        //     ->whereNotIn('people_id', $successplan)
        //     ->groupBy('location')
        //     ->orderBy('locations', 'desc')
        //     ->take(10)
        //     ->get();

        // foreach ($divisionCountsData as $data) {
        //     $this->divisionLabels[] = $data->location;
        //     $this->divisionData[] = $data->locations;
        // }

        $this->plandesc = SuccessionPlan::where('id', $this->plan)->get();
    }

    public function render()
    {
        $successplan = SuccessPeople::where('plan_id', $this->plan)->pluck('people_id')->toArray();

        // Get target companies from plan requirements
        $targetCompanies = SuccessRequirements::where('plan_id', $this->plan)
            ->where('type', 'Company')
            ->pluck('name')
            ->toArray();

        $pipelinePeople = pipeline::select('pipelines.*', 'people.company_name as current_company', 'people.latest_role as current_role')
                                    ->leftJoin('people', 'pipelines.people_id', '=', 'people.id')
                                    ->where('plan_id', $this->plan)
                                    ->whereNotIn('people_id', $successplan);

        // Prioritize candidates from target companies by adding custom sorting
        // Target company candidates appear first, ordered by company priority, then by total score
        // Uses bidirectional matching to handle variations like "Lloyd" <-> "Lloyd Banking Group"
        if (!empty($targetCompanies)) {
            $companyConditions = [];
            foreach ($targetCompanies as $index => $company) {
                // Escape company name to prevent SQL injection
                $escapedCompany = str_replace("'", "''", $company);
                
                // Only do bidirectional matching if the company name is at least 4 characters
                // This prevents overly broad matches like "Co" matching every "Company"
                if (strlen($company) >= 4) {
                    // Bidirectional LIKE: check if either name contains the other
                    $companyConditions[] = "WHEN (
                        pipelines.company_name LIKE '%{$escapedCompany}%' 
                        OR '{$escapedCompany}' LIKE CONCAT('%', pipelines.company_name, '%')
                    ) THEN {$index}";
                } else {
                    // For short names, only do standard one-way matching
                    $companyConditions[] = "WHEN pipelines.company_name LIKE '%{$escapedCompany}%' THEN {$index}";
                }
            }
            $companyCase = implode(' ', $companyConditions);
            
            $pipelinePeople = $pipelinePeople->orderByRaw("CASE {$companyCase} ELSE 999 END")
                                            ->orderBy('total_score','desc');
        } else {
            $pipelinePeople = $pipelinePeople->orderBy('total_score','desc');
        }

        if ($this->sortBy && $this->sortDirection) {
            if($this->sortBy == 'total_score'){
                $pipelinePeople = $pipelinePeople->orderBy($this->sortBy, $this->sortDirection)->orderBy('id', 'desc');
            }
            else {
                $pipelinePeople = $pipelinePeople->orderBy($this->sortBy, $this->sortDirection);
            }
        }

        // Cache location and company data to avoid repeated queries
        $this->locations = Cache::remember('locations_dropdown', 3600, function() {
            return Location::distinct()->pluck('country_name')->map(function ($country) {
                return ['value' => $country, 'label' => $country];
            })->toArray();
        });

        $this->companies = Cache::remember('companies_dropdown', 3600, function() {
            $companiesArr = pipeline::whereNotNull('company_id')
                                    ->where('company_name', '!=', "")
                                    ->whereNotNull('company_name')
                                    ->distinct()
                                    ->orderBy('company_name', 'ASC')
                                    ->pluck('company_name', 'company_id')
                                    ->toArray();
            
            $companies = [];
            foreach($companiesArr as $companyId => $companyName){
                $companies[] = ['value' => $companyId, 'label' => $companyName];
            }
            return $companies;
        });

        if ($this->search) {
            if ($this->forename) {
                $pipelinePeople = $pipelinePeople->where('pipelines.first_name', 'like', '%' . $this->forename . '%');
            }
            if ($this->surname) {
                $pipelinePeople = $pipelinePeople->where('pipelines.last_name', 'like', '%' . $this->surname . '%');
            }
            if ($this->gender) {
                $pipelinePeople = $pipelinePeople->where('pipelines.gender', '=', $this->gender);
            }
            if ($this->_role) {
                $pipelinePeople = $pipelinePeople->where('pipelines.latest_role', 'like', '%' . $this->_role . '%');
            }
            if ($this->company) {
                $pipelinePeople = $pipelinePeople->where('pipelines.company_id', $this->company);
            }
            if ($this->function) {
                $pipelinePeople = $pipelinePeople->where('pipelines.function', 'like', '%' . $this->function . '%');
            }
            if ($this->division) {
                $pipelinePeople = $pipelinePeople->where('pipelines.division', 'like', '%' . $this->division . '%');
            }
            if ($this->location) {
                $pipelinePeople = $pipelinePeople->where('pipelines.country', 'like', '%' . $this->location . '%');
            }
            if ($this->regBodies) {
                $pipelinePeople = $pipelinePeople->where('pipelines.other_tags', 'like', '%' . $this->regBodies . '%');
            }
            if ($this->moverFilter) {
                if($this->moverFilter == 'Mover'){
                    $pipelinePeople = $pipelinePeople->where('pipelines.mover', 'Mover');
                }
                
                if($this->moverFilter == 'Non Mover'){
                    $pipelinePeople = $pipelinePeople->where(function($query) {
                                                        $query->whereNull('pipelines.mover')
                                                            ->orWhereIn('pipelines.mover', ['', 'Non Mover']);
                                                    });
                }
            }
            if ($this->min_exp) {
                $pipelinePeople = $pipelinePeople->where('pipelines.tenure', '>=', $this->min_exp);
            }
        }

        if (!empty($this->searchByKeyword)) {
            $searchKeywords = explode(",", $this->searchByKeyword);
            $pipelinePeople = $pipelinePeople->where(function ($query) use ($searchKeywords) {
                foreach ($searchKeywords as $searchword) {
                    $query->orWhere(function ($subQuery) use ($searchword) {
                        $subQuery->orWhere('first_name', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.last_name', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.latest_role', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.company_name', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.function', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.division', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.country', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.summary', 'like', '%' . $searchword . '%')
                            ->orWhere('pipelines.other_tags', 'like', '%' . $searchword . '%');
                    });
                }
            });
        }

        $filteredPeople = pipeline::where('id', $this->selectedIndividualID)->get();

        if($this->selectedIndividualID){

        }
        // Getting the skills and the career histories
        $filteredPeopleid = $filteredPeople->pluck('people_id')->toArray();
        $peopleskills = [];
        if($this->selectedIndividualID && !$filteredPeople->isEmpty()){
            // Check if the person is internal
            if(str_contains($filteredPeople[0]->people_type,'Internal')){
                // An internal person has been found
                $peopleskills = InternalSkills::whereIn('internal_people', $filteredPeopleid)->get();
            }
            else{
                // The person is external
                $peopleskills = Skills::whereIn('people_id', $filteredPeopleid)->get();
            }
        }

        // Get the career histories of the individual
        $peoplescareer = [];
        $currentRole = null;
        if($this->selectedIndividualID && !$filteredPeople->isEmpty()){
            // Get the current role information
            $currentRole = (object)[
                'role' => $filteredPeople[0]->latest_role,
                'company_name' => $filteredPeople[0]->company_name,
                'is_current' => true,
                'start_date' => $filteredPeople[0]->start_date ?? now(),
                'end_date' => null
            ];
            
            if(str_contains($filteredPeople[0]->people_type,'Internal')){
                $peoplescareer = DB::table('internal_career_histories')->whereIn('people_id', $filteredPeopleid)
                ->join('companies', 'internal_career_histories.past_company_id', '=', 'companies.id')
                ->select('internal_career_histories.*', 'companies.name as company_name')
                ->orderBy('start_date','desc')
                ->get();
            }
            else{
                $peoplescareer = DB::table('career_histories')->whereIn('people_id', $filteredPeopleid)
                ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
                ->select('career_histories.*', 'companies.name as company_name')
                ->orderBy('start_date','desc')
                ->get();
            }
        }


        // $this->getChartsData();

        if($this->selectedIndividualID && !$filteredPeople->isEmpty()){
            // Cache user notes to avoid repeated queries
            $notesCacheKey = "user_notes_{$this->selectedIndividualID}";
            $this->successPersonNotes = cache()->remember($notesCacheKey, 300, function() {
                return UserNotes::select('user_notes.*', 'users.name as user_name')
                    ->where('entity_id', $this->selectedIndividualID)
                    ->where('entity_type', 'success_person')
                    ->join('users', 'user_notes.author', '=', 'users.id')
                    ->orderBy('user_notes.id', 'desc')
                    ->get();
            });
        }
        $this->getChartsData();
        $this->dispatch('updateChart');
        return view('livewire.pipeline-compenent', [
            'pipelinePeople' => $pipelinePeople->paginate(50), 
            'filteredPeople' => $filteredPeople, 
            'peopleskills' => $peopleskills, 
            'groupedPeoplesCareer' => $peoplescareer,
            'currentRole' => $currentRole,
            '']);
    }

    public function getChartsData()
    {
        // Cache chart data to avoid repeated calculations
        $chartCacheKey = "charts_data_{$this->plan}";
        $chartData = cache()->remember($chartCacheKey, 600, function() { // 10 minute cache
            $successplan = SuccessPeople::where('plan_id', $this->plan)->pluck('people_id')->toArray();

            // Single optimized query to get all chart data at once
            $allChartData = DB::table('pipelines')
                ->select([
                    'country',
                    'gender', 
                    'company_name',
                    'function',
                    DB::raw('COUNT(*) as total_count')
                ])
                ->where('plan_id', $this->plan)
                ->whereNotIn('people_id', $successplan)
                ->groupBy('country', 'gender', 'company_name', 'function')
                ->get();

            $chartQueries = [
                'divisions' => $allChartData->groupBy('country')->map(function($items) {
                    return (object)['country' => $items->first()->country, 'locations' => $items->sum('total_count')];
                })->sortByDesc('locations')->take(5)->values(),
                
                'gender' => $allChartData->groupBy('gender')->map(function($items) {
                    return (object)['gender' => $items->first()->gender, 'total_people' => $items->sum('total_count')];
                })->values(),
                
                'company' => $allChartData->groupBy('company_name')->map(function($items) {
                    return (object)['company_name' => $items->first()->company_name, 'total_people' => $items->sum('total_count')];
                })->sortByDesc('total_people')->take(5)->values(),
                
                'function' => $allChartData->groupBy('function')->map(function($items) {
                    return (object)['function' => $items->first()->function, 'total_people' => $items->sum('total_count')];
                })->sortByDesc('total_people')->take(5)->values()
            ];

            return $chartQueries;
        });

        // Reset arrays to avoid duplication
        $this->divisionLabels = [];
        $this->divisionData = [];
        $this->genderLabels = [];
        $this->genderData = [];
        $this->companyLabels = [];
        $this->companyData = [];
        $this->functionLabels = [];
        $this->functionData = [];

        // Process cached data
        foreach ($chartData['divisions'] as $data) {
            $this->divisionLabels[] = $data->country;
            $this->divisionData[] = $data->locations;
        }

        foreach ($chartData['gender'] as $data) {
            $this->genderLabels[] = $data->gender;
            $this->genderData[] = $data->total_people;
        }

        foreach ($chartData['company'] as $data) {
            $this->companyLabels[] = $data->company_name;
            $this->companyData[] = $data->total_people;
        }

        foreach ($chartData['function'] as $data) {
            $this->functionLabels[] = $data->function;
            $this->functionData[] = $data->total_people;
        }
    }

    public function searchPeople()
    {
        // dd($this->searchByKeyword);
        $successplan = SuccessPeople::where('plan_id', $this->plan)->pluck('people_id')->toArray();
        $pipelinePeople = pipeline::where('plan_id', $this->plan)
        ->whereNotIn('people_id', $successplan)
        ->orderBy($this->sortBy, $this->sortDirection);

            $searchKeywords = explode(",", $this->searchByKeyword);
            $pipelinePeople = $pipelinePeople->where(function ($query) use ($searchKeywords) {
                foreach ($searchKeywords as $searchword) {
                    $query->orWhere(function ($subQuery) use ($searchword) {
                        $subQuery->orWhere('first_name', 'like', '%' . $searchword . '%')
                            ->orWhere('middle_name', 'like', '%' . $searchword . '%')
                            ->orWhere('last_name', 'like', '%' . $searchword . '%')
                            ->orWhere('latest_role', 'like', '%' . $searchword . '%')
                            ->orWhere('company_name', 'like', '%' . $searchword . '%')
                            ->orWhere('function', 'like', '%' . $searchword . '%')
                            ->orWhere('division', 'like', '%' . $searchword . '%')
                            ->orWhere('country', 'like', '%' . $searchword . '%')
                            ->orWhere('summary', 'like', '%' . $searchword . '%')
                            ->orWhere('other_tags', 'like', '%' . $searchword . '%');
                    });
                }
            });
            $this->dispatch('updateChart');
            return $pipelinePeople;
    }

    public function runfilters()
    {
        $this->search = true;
        $this->resetPage();
        $this->dispatch('updateChart');
    }

    public function Ordermytable($column)
    {
        if ($column === $this->sortBy) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
        $this->dispatch('updateChart');
    }

    public function viewIndividual($id, $peopleType = null)
    {
       $start = microtime(true);

        $this->selectedIndividualID = $id;
        $this->peopleType = $peopleType;

        // Use cache to avoid repeated database calls for the same data
        $cacheKey = "pipeline_view_individual_{$id}_" . auth()->id();
        
        $cachedData = cache()->remember($cacheKey, 300, function() use ($id) { // 5 minute cache
            // Fetch people_id from pipeline
            $people_id = pipeline::where('id', $id)->value('people_id');
            
            if (!$people_id) {
                return ['userJobs' => collect(), 'userPlans' => collect()];
            }

            $user = auth()->user();

            // Single optimized query for jobs
            $userJobs = Job::select('jobs.*')
                ->where(function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                        ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
                })
                ->whereExists(function($query) use ($people_id) {
                    $query->select(DB::raw(1))
                        ->from('job_people')
                        ->whereColumn('job_people.job_id', 'jobs.id')
                        ->where('job_people.people_id', $people_id);
                })
                ->get();

            // Single optimized query for plans
            $userPlans = SuccessionPlan::select('succession_plans.*')
                ->where(function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                        ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
                })
                ->whereExists(function ($query) use ($people_id) {
                    $query->select(DB::raw(1))
                        ->from('success_people')
                        ->whereColumn('success_people.plan_id', 'succession_plans.id')
                        ->where('success_people.people_id', $people_id);
                })
                ->get();

            return ['userJobs' => $userJobs, 'userPlans' => $userPlans];
        });

        $this->userJobs = $cachedData['userJobs'];
        $this->userPlans = $cachedData['userPlans'];

        $end = microtime(true);
        $duration = $end - $start;
        Log::info("viewIndividual took {$duration} seconds for id {$id}, type {$peopleType}");

        $this->dispatch('updateChart');
    }

    public function ViewTalentPoolList($id)
    {
        $peopleId = pipeline::where('id','=',$id)->pluck('people_id')->first();
        $this->talentPoolsList = getTalentPoolList($peopleId);
        $this->addToTalentPoolPopup = true;
    }


    public function ViewPlanList($id)
    {
        $people_id = pipeline::where('id','=',$id)->pluck('people_id')->first();
        $this->plansList = getPlansList($people_id, $this->plan);
        $this->addToPlanPopup = true;
    }

    public function showAddToPlanPopup($id) {
        $this->selectedIndividualID = $id;
        $peopleId = pipeline::where('id','=',$id)->pluck('people_id')->first();
        $this->plansList = getPlansList($peopleId, $this->plan);
        $this->addToPlanPopup = true;
    }
    public function showAddToTalentPoolPopup($id) {
        //dd($id);
        $peopleId = pipeline::where('id','=',$id)->pluck('people_id')->first();
        $this->selectedIndividualID = $id;
        $this->talentPoolsList = getTalentPoolList($peopleId);
        $this->addToTalentPoolPopup = true;
    }

    public function addpeopleToTalentPools() {
     
        if(empty($this->addToTalentPoolArray)) {
            $this->dispatch('toast', 'info', 'Please select talent pool!');
            $this->skipRender();
            return;
        }
        $peopleId = pipeline::where('id','=',$this->selectedIndividualID)->pluck('people_id')->first();
      

        foreach($this->addToTalentPoolArray as $key => $value) {
            if($value) {           
                $JobPerson = JobPeople::where(['people_id' => $peopleId,'job_id' => $key])->first();

                if($JobPerson) {
                    continue;
                }
			
                addToJob($peopleId, $key);
                $jobID = $key;
                $femaleRatio = DB::table('job_people')
                    ->select('job_id', DB::raw('(SUM(gender = "Female")/Count(*))*100 as female_ratio'))
                    ->groupBy('job_id')
                    ->where('job_id', $jobID)
                    ->first();

                Job_Scores::updateOrInsert(
                    ['job_id' => $jobID, 'metric_name' => 'Female-ratio'],
                    ['score' => $femaleRatio->female_ratio]
                );

                $maleRatio = DB::table('job_people')
                    ->select('job_id', DB::raw('(SUM(gender = "Male")/Count(*))*100 as male_ratio'))
                    ->groupBy('job_id')
                    ->where('job_id', $jobID)
                    ->first();


                Job_Scores::updateOrInsert(
                    ['job_id' => $jobID, 'metric_name' => 'Male-Ratio'],
                    ['score' => $maleRatio->male_ratio]
                );
            }
        }
      
        if(!$this->disableDispatchforMultipleSelect) {
            $this->dispatch('toast', 'info', 'Added to selected talent pools!');
            $this->addToTalentPoolPopup = false;
            $this->vopen = false;
            $this->addToTalentPoolArray = [];
        }
    }

   
    public function addpeopleToPlans() {

        //dd($this->selectedIndividualID);
        if(empty($this->addToPlansArray)) {
            $this->dispatch('toast', 'info', 'Please select plans!');
            $this->skipRender();
            return;
        }
        $selectedPlan = $this->plan;
        $user = auth()->user();
        $peopleId = pipeline::where('id','=',$this->selectedIndividualID)->pluck('people_id')->first();

        foreach($this->addToPlansArray as $key => $value) {
            if($value) {
              
             
                $this->plan = $key;
                // Check if the person is in the pipeline for the plan that has been chosen
                $pipelinePerson = Pipeline::where(['id' => $this->selectedIndividualID,'plan_id' => $key])->first();
        
                if($pipelinePerson !== null) {
                    // The person is already in the pipeline so they should be added as a succession candidate
                    $this->addToPlan($pipelinePerson->id);
                } else {

                    $PlanPerson = SuccessPeople::where(['people_id' => $peopleId,'plan_id' => $key])->first();
               
                    if($PlanPerson) {
                      continue;
                    }
                    $pipelinePerson = Pipeline::where(['id' => $this->selectedIndividualID])->first();
                  
                    // Checking that whether the user has selected an internal individual
                    if (str_contains($pipelinePerson->people_type,"Internal")){
                        
                        $externalPerson = InternalPeople::where('id',$this->selectedIndividualID)->first();
                        $personType = "Internal-System";

                        //SKill Score for an internal Person
                        $PersonSkills = InternalSkills::where('internal_people', $pipelinePerson->people_id)->get();
                        if ($this->SuccessSkills->count() > 0) {
                            $SkillScore = 0;
                            foreach ($PersonSkills as $pK) {
                                foreach ($this->SuccessSkills as $sK) {
                                    if ($sK->name == $pK->skill_name) {
                                        $SkillScore++;
                                        break;
                                    }
                                }
                            }

                            $SkillScore /= $this->SuccessSkills->count();
                        } else {
                            $SkillScore = 1;
                        }
                    }
                    // Now for the external exclusive checks
                    else{
                        $externalPerson = People::where('id', $pipelinePerson->people_id)->first();
                        $personType = "External-System";

                        //Skill Score for an external person
                        $PersonSkills = Skills::where('people_id', $pipelinePerson->people_id)->get();
                        if ($this->SuccessSkills->count() > 0) {
                            $SkillScore = 0;
                            foreach ($PersonSkills as $pK) {
                                foreach ($this->SuccessSkills as $sK) {
                                    if ($sK->name == $pK->skill_name) {
                                        $SkillScore++;
                                        break;
                                    }
                                }
                            }

                            $SkillScore /= $this->SuccessSkills->count();
                        } else {
                            $SkillScore = 1;
                        }
                    }
              
                    // Rest of the checks work for both types of individuals
                    if(empty($externalPerson))
                        continue;
                    $successionPlan = SuccessionPlan::find($this->plan);
            
                    $tenureMatch = 0;
                    if ($externalPerson->tenure >= $successionPlan->minimum_Experience) {
                        $tenureMatch = 1;
                    }

                    if ($externalPerson->addStartDate === null) {
                        $externalPerson->start_date = now();
                    };
                    // Role Match Score

                    $latestRole = $externalPerson->latest_role;
                    $RoleScore = 0;
                    if ($this->SuccessRoles->contains('name', $latestRole)) {
                        $RoleScore = 1;
                    } else {
                        if ($this->SuccessStepup->contains('name', $latestRole)) {
                            $RoleScore = 0.75;
                        } else {
                            $RoleScore = 0;
                        }
                    }


                    // //Gender Match
                    $Gender_Match = 0;
                    if ($this->SuccessGender->contains('name', $externalPerson->gender)) {
                        $Gender_Match = 1;
                    } else {
                        $Gender_Match = 0;
                    }

                    // Location Match
                    $Location_Match = 0;
                    if ($this->SuccessLocation->contains('name', $externalPerson->location)) {
                        $Location_Match = 1;
                    } else {
                        $Location_Match = 0;
                    }
                  
                    $newPipeline = pipeline::create([
                        'plan_id'            => $this->plan,
                        'user_id'            => $user->id,
                        'people_id'          => $externalPerson->id,
                        'headline'           => "Not Applicable",
                        'first_name'         => $externalPerson->forename,
                        'last_name'          => $externalPerson->surname,
                        'middle_name'        => $externalPerson->middle_name,
                        'other_name'         => $externalPerson->other_name,
                        'gender'             => $externalPerson->gender,
                        'diverse'            => $externalPerson->diverse,
                        'location'           => $externalPerson->location,
                        'summary'            => $externalPerson->summary,
                        'country'            => $externalPerson->country,
                        'city'               => $externalPerson->city,
                        'linkedinURL'        => $externalPerson->linkedinURL,
                        'latest_role'        => $externalPerson->latest_role,
                        'company_id'         => $externalPerson->company_id,
                        'company_name'       => $externalPerson->company_name,
                        'start_date'         => $externalPerson->start_date,
                        'end_date'           => $externalPerson->end_date,
                        'tenure'             => $externalPerson->tenure,
                        'function'           => $externalPerson->function,
                        'division'           => $externalPerson->division,
                        'seniority'          => $externalPerson->seniority,
                        'exco'               => $externalPerson->exco,
                        'career_history'     => $externalPerson->career_history,
                        'educational_history'=> $externalPerson->educational_history,
                        'skills'             => $externalPerson->skills,
                        'languages'          => $externalPerson->languages,
                        'readiness'          => $externalPerson->readiness,
                        'other_tags'         => $externalPerson->other_tags,
                        'skills_match'       => $SkillScore,
                        'education_match'    => 0,
                        'location_match'     => $Location_Match,
                        'role_match'         => $RoleScore,
                        'gender_match'       => $Gender_Match,
                        'tenure_match'       => $tenureMatch,
                        'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
                        'people_type'        => $personType
                    ]);
        
                    $successPeopleData = [
                        'pipeline_id'        => $newPipeline->id,
                        'plan_id'            => $this->plan,
                        'user_id'            => $user->id,
                        'people_id'          => $externalPerson->id,
                        'headline'           => "Not Applicable",
                        'first_name'         => $externalPerson->forename,
                        'last_name'          => $externalPerson->surname,
                        'middle_name'        => $externalPerson->middle_name,
                        'other_name'         => $externalPerson->other_name,
                        'gender'             => $externalPerson->gender,
                        'diverse'            => $externalPerson->diverse,
                        'location'           => $externalPerson->country,
                        'summary'            => $externalPerson->summary,
                        'linkedinURL'        => $externalPerson->linkedinURL,
                        'latest_role'        => $externalPerson->latest_role,
                        'company_id'         => $externalPerson->company_id,
                        'company_name'       => $externalPerson->company_name,
                        'start_date'         => $externalPerson->start_date,
                        'end_date'           => $externalPerson->end_date,
                        'tenure'             => $externalPerson->tenure,
                        'function'           => $externalPerson->function,
                        'division'           => $externalPerson->division,
                        'seniority'          => $externalPerson->seniority,
                        'exco'               => $externalPerson->exco,
                        'career_history'     => $externalPerson->career_history,
                        'educational_history' => $externalPerson->educational_history,
                        'skills'             => $externalPerson->skills,
                        'languages'          => $externalPerson->languages,
                        'readiness'          => $externalPerson->readiness,
                        'other_tags'         => $externalPerson->other_tags,
                        'city'               => $externalPerson->city,
                        'country'            => $externalPerson->country,
                        'skills_match'       => $SkillScore,
                        'education_match'    => 0,
                        'location_match'     => $Location_Match,
                        'role_match'         => $RoleScore,
                        'gender_match'       => $Gender_Match,
                        'tenure_match'       => $tenureMatch,
                        'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
                        'type'               => $personType,
                        'notes'              => "Enter notes here",
                        'status'             => "Approved",
                        'recruit'            => 1,
                    ];
            
                    if($successionPlan->user_id != auth()->user()->id) {
                        $successPeopleData['status'] = "Proposed";
                    }
            
                    $SuccessPeople = SuccessPeople::create($successPeopleData);

                     //------------------- Get the new Gender Diversity Score -----------------//
                    $femaleRatio = DB::table('success_people')
                    ->select('plan_id', DB::raw('(SUM(gender = "Female")/Count(*))*100 as female_ratio'))
                    ->groupBy('plan_id')
                    ->where('plan_id', $this->plan)
                    ->first();

                PlanScores::updateOrInsert(
                    ['succession_plan_id' => $this->plan, 'metric_name' => 'Female-ratio'],
                    ['score' => $femaleRatio->female_ratio]
                );

                $maleRatio = DB::table('success_people')
                    ->select('plan_id', DB::raw('(SUM(gender = "Male")/Count(*))*100 as male_ratio'))
                    ->groupBy('plan_id')
                    ->where('plan_id', $this->plan)
                    ->first();


                PlanScores::updateOrInsert(
                    ['succession_plan_id' => $this->plan, 'metric_name' => 'Male-Ratio'],
                    ['score' => $maleRatio->male_ratio]
                );

                $InternalRatio = DB::table('success_people')
                    ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
                    ->groupBy('plan_id')
                    ->where('plan_id', $this->plan)
                    ->first();

                PlanScores::updateOrInsert(
                    ['succession_plan_id' => $this->plan, 'metric_name' => 'Internal-External Ratio'],
                    ['score' => $InternalRatio->internal_ratio]
                );

                $averageskillscore = DB::table('success_people')
                    ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
                    ->groupBy('plan_id')
                    ->where('plan_id', $this->plan)
                    ->first();

                PlanScores::updateOrInsert(
                    ['succession_plan_id' => $this->plan, 'metric_name' => 'Skill Score'],
                    ['score' => $averageskillscore->average_skill_score]
                );

                $averagetenurescore = DB::table('success_people')
                    ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
                    ->groupBy('plan_id')
                    ->where('plan_id', $this->plan)
                    ->first();

                PlanScores::updateOrInsert(
                    ['succession_plan_id' => $this->plan, 'metric_name' => 'Tenure Score'],
                    ['score' => $averagetenurescore->average_tenure_score]
                );

                if ($this->SuccessSkills->isNotEmpty()) {
                    // Add requirements for chart for skills radar
                    $SkillFound = false;
                    foreach ($this->SuccessSkills as $sK) {
                        $SkillFound = false;
                        foreach ($PersonSkills as $pK) {
                            if ($sK->name == $pK->skill_name) {
                                SuccessSkills::create([
                                    'succession_plan_id' => $this->plan,
                                    'skill_name'        => $sK->name,
                                    'success_people_id' => $SuccessPeople->id,
                                    'success_requirements_id' => $sK->id,
                                    'score' => 1
                                ]);
                                $SkillFound = true;
                                break;
                            }
                        }

                        if (!$SkillFound) {
                            SuccessSkills::create([
                                'succession_plan_id' => $this->plan,
                                'success_people_id' => $SuccessPeople->id,
                                'success_requirements_id' => $sK->id,
                                'skill_name'        => $sK->name,
                                'score' => 0
                            ]);
                        }
                    }
                } 

                }

            }
        }
        $this->plan = $selectedPlan;
        if(!$this->disableDispatchforMultipleSelect) {
            $this->dispatch('toast', 'info', 'Added to selected plans!');
            $this->addToPlanPopup = false;
            $this->vopen = false;
            $this->addToPlansArray = [];
        }

    }

    public function refreshMover($pipelinePerson){
        
        $people = People::where('id', $pipelinePerson['people_id'])->first();

        // Update pipeline
        $pipelineDataToUpdate = [
            'first_name' => $people->forename,
            'last_name' => $people->surname,
            'middle_name' => $people->middle_name,
            'other_name' => $people->other_name,
            'gender' => $people->gender,
            'diverse' => $people->diverse,
            'country' => $people->country,
            'city' => $people->city,
            'mover' => 'Non Mover',
            'linkedinURL' => $people->linkedinURL,
            'latest_role' => $people->latest_role,
            'company_id' => $people->company_id,
            'company_name' => $people->company_name,
            'exco' => $people->exco,
            'start_date' => $people->start_date,
            'end_date' => $people->end_date,
            'tenure' => $people->tenure,
            'function' => $people->function,
            'division' => $people->division,
            'seniority' => $people->seniority,
            'career_history' => $people->career_history,
            'educational_history' => $people->educational_history,
            'skills' => $people->skills,
            'languages' => $people->languages,
            'other_tags' => $people->other_tags,
            'readiness' => $people->readiness,
            'summary' => $people->summary
        ];

        pipeline::where('id', $pipelinePerson['id'])
                    ->where('plan_id', $this->plan)
                    ->update($pipelineDataToUpdate);

        $pipelineMoverCount = pipeline::where('plan_id', $this->plan)
                                ->where('mover', 'Mover')
                                ->count();

        $successPeopleMoverCount = SuccessPeople::where('plan_id', $this->plan)
                                                ->where('mover', 'Mover')
                                                ->count();

        if($pipelineMoverCount <= 0 && $successPeopleMoverCount <= 0){
            SuccessionPlan::where('id', $this->plan)->update(['mover' => 'Non Mover']);
        }

        return;
    }

    public function clearFilters()
    {
        $this->search = false;
        $this->dispatch('updateChart');
        // Reset all form input properties
        $this->reset(['forename', 'surname', 'role', 'company', 'function', 'division', 'location', 'gender', 'regBodies', 'tenure']);
    }

    public function changeMinimumTenure($flag)
    {
        if ($flag == 'decrease') {
            $this->min_exp = $this->min_exp <= 1 ? null : ($this->min_exp - 1);
        } else if ($flag == 'increase') {
            $this->min_exp = !$this->min_exp ? 1 : ($this->min_exp + 1);
        }
    }

    public function addNotes($id)
    {
        // $successPerson = SuccessPeople::findOrFail($id);
        // $successPerson->update(['notes' => $this->filteredPnotes]);

        $this->dispatch('updateChart');
        $user = auth()->user();
        $userNoteData = [
            'entity_id' => $id,
            'entity_type' => 'success_person',
            'Notes' => $this->filteredPnotes,
            'author' => $user->id
        ];
        UserNotes::insert($userNoteData);
    }

    public function addtoPlan($id)
    {
        // Function for adding the individual to the current plan the user is on
        try
        {
        $itemData = pipeline::where('id', $id)->first();
        //dd($itemData);

        //Get the skills of the person this will be used for the radar chart

        if(str_contains($itemData->people_type,'Internal')){
            $PersonSkills = InternalSkills::where('internal_people','=',$itemData->people_id)->get();
        }
        else{
            $PersonSkills = Skills::where('people_id', $id)->get();
        }

        $user = auth()->user();
        //dd($user);
        if ($user->id !== $this->plandetails->user_id) {
            $status = 'Proposed';
        } else {
            $status = 'Approved';
        }

        if ($itemData->people_type === 'External-System') {
            $type = 'External';
        } elseif ($itemData->people_type === 'External-User') {
            $type = 'External';
        }elseif ($itemData->people_type === 'External-System-Search') {
            $type = 'External';
        } else {
            $type = 'Internal';
        }
        if($itemData->skills_match == null){
            $itemData->skills_match = 0;
        } 

        $SuccessPeople = SuccessPeople::create([
            'pipeline_id'         => $itemData->id,
            'plan_id'             => $this->plan,
            'user_id'             => $itemData->user_id,
            'people_id'           => $itemData->people_id,
            'headline'            => "Not Applicable",
            'first_name'          => $itemData->first_name,
            'last_name'           => $itemData->last_name,
            'middle_name'         => $itemData->middle_name,
            'other_name'          => $itemData->other_name,
            'gender'              => $itemData->gender,
            'diverse'             => $itemData->diverse,
            'location'            => $itemData->location,
            'city'                => $itemData->city,
            'country'             => $itemData->country,
            'summary'             => $itemData->summary,
            'linkedinURL'         => $itemData->linkedinURL,
            'latest_role'         => $itemData->latest_role,
            'company_id'          => $itemData->company_id,
            'company_name'        => $itemData->company_name,
            'start_date'          => $itemData->start_date,
            'end_date'            => $itemData->end_date,
            'tenure'              => $itemData->tenure,
            'function'            => $itemData->function,
            'division'            => $itemData->division,
            'seniority'           => $itemData->seniority,
            'exco'                => $itemData->exco,
            'career_history'      => $itemData->career_history,
            'educational_history' => $itemData->educational_history,
            'skills'              => $itemData->skills,
            'languages'           => $itemData->languages,
            'skills_match'        => $itemData->skills_match,
            'education_match'     => $itemData->education_match,
            'location_match'      => $itemData->location_match,
            'role_match'          => $itemData->role_match,
            'gender_match'        => $itemData->gender_match,
            'tenure_match'        => $itemData->tenure_match,
            'total_score'         => $itemData->skills_match + $itemData->education_match + $itemData->location_match + $itemData->role_match + $itemData->gender_match + $itemData->tenure_match,
            'status'              => $status,
            'type'                => $type,
            'notes'               => "Enter notes here",
            'recruit'             => 1,
            'other_tags'          => $itemData->other_tags

        ]);
        
        $user = auth()->user();
        //dd($user);

        if ($SuccessPeople->type === 'Proposed') {
            Notifications::create([
                'type'              => "Candidate-Proposed",
                'plan_id'           => $SuccessPeople->plan_id,
                'people_id'         => $SuccessPeople->id,
                'entity_name'       => 'Proposed Candidate',
                'description'       => $user->name . ' has proposed ' . $SuccessPeople->forename . ' ' . $SuccessPeople->latest_role . ' from ' . $SuccessPeople->company_name . ' to your plan ',
                'user_id'           => $user->id,
                'user_company'      => $user->company_id
            ]);
        }

        if ($user->id !== $this->plandetails->user_id) {
            $planupdate = SuccessionPlan::findOrFail($this->plandetails->id);
            //dd($planupdate);
            $planupdate->update(['candidate_status' => 'Proposals Pending']);
        }

        //------------------- Get the new Gender Diversity Score -----------------//
        $femaleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Female")/Count(*))*100 as female_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Female-Ratio'],
            ['score' => $femaleRatio->female_ratio]
        );

        $maleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Male")/Count(*))*100 as male_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();


        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Male-Ratio'],
            ['score' => $maleRatio->male_ratio]
        );

        $InternalRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Internal-External Ratio'],
            ['score' => $InternalRatio->internal_ratio]
        );
      
        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Skill Score'],
            ['score' => $averageskillscore->average_skill_score]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan, 'metric_name' => 'Tenure Score'],
            ['score' => $averagetenurescore->average_tenure_score ?? 0]
        );

        // Add requirements for chart for skills radar
        $SkillFound = false;
        foreach ($this->SuccessSkills as $sK) {
            $SkillFound = false;
            foreach ($PersonSkills as $pK) {
                if ($sK->name == $pK->skill_name) {
                    SuccessSkills::create([
                        'succession_plan_id' => $this->plan,
                        'success_people_id' => $SuccessPeople->id,
                        'success_requirements_id' => $sK->id,
                        'skill_name'        => $sK->name,
                        'score' => 1
                    ]);
                    $SkillFound = true;
                    break;
                }
            }

            if (!$SkillFound) {
                SuccessSkills::create([
                    'succession_plan_id' => $this->plan,
                    'success_people_id' => $SuccessPeople->id,
                    'success_requirements_id' => $sK->id,
                    'skill_name'        => $sK->name,
                    'score' => 0
                ]);
            }
        }
        $this->getChartsData();
    }
    catch (Exception $e)
    {
        \Log::error($e);
        session()->flash('error', $e->getMessage());
    }
        // $this->dispatch('updateChart');
    }

    public function removepipPerson($id)
    {
        $person = pipeline::findOrFail($id);
        $person->delete();
        $this->getChartsData();
        $this->dispatch('updateChart');
    }

    public function downloadSuccessPeople($filterArr)
    {
        // Create an instance of SuccessPeopleExport with the provided filter
        $export = new SuccessPeopleExport($filterArr);

        // Check if there is any data to export
        $pipelinePeople = $export->collection();

        if ($pipelinePeople->isEmpty()) {
            // Return a response indicating no data available
            session()->flash('error', "No records found to be download!");
            // $this->dispatch('toast', 'info', "No records found to download!");
            return;
        }

        return Excel::download($export, 'pipeline.xlsx');
    }

    public function SendEmail()
    { 
        if (empty($this->emailMessage)) {
            $this->dispatch('toast', 'error', 'Email content cannot be empty.');
            return;
        }
           
        $user = auth()->user();
        $sendersname = $user->name;
        $sendersemail = $user->email;
        $company_id = $user->company_id;
        $company_name = Company::where('id', $company_id)->pluck('name')->first();
        $account_id = $user->account_id;
        $relationship_manager_ids = Account::where('id', $account_id)->pluck('relationship_manager_id')->toArray();
        $ids = collect($relationship_manager_ids)
            ->flatMap(fn($id) => explode(',', $id))
            ->filter()
            ->unique()
            ->toArray();
            
        $clientuser = [];
        if (!empty($ids)) {
            $clientuser = User::whereIn('id', $ids)->pluck('email')->toArray();
        }
     
       
     
     
        if (!empty($clientuser)) {
            try {
                $requirements = DB::table('success_requirements')
                    ->where('plan_id', $this->plan)
                    ->where('type', '!=', 'professional_skill')
                    ->pluck('name')
                    ->toArray();
              
                Mail::to($clientuser)
                    ->cc($sendersemail)
                    ->send(new SendToRelationshipManager($this->emailMessage, $sendersname, $requirements, $this->plandetails->description, $company_name, $this->plan));
               
                Log::info('Email successfully sent to Relationship Managers', ['emails' => $clientuser]);
               
                Log::info('requirement');
                Log::info('requirement');
            } catch (\Exception $e) {
                Log::error('Failed to send email to Relationship Managers', [
                    'emails' => $clientuser,
                    'error' => $e->getMessage()
                ]);
            }
        }
        $this->reset('emailMessage');
        $this->dispatch('toast', 'success', "Email successfully sent.");
        $this->sendemailopen = false;
    }

    public function addSelectedToTalentPool()
    {  
        $peopleIds = pipeline::whereIn('id', $this->selectedPerson)->pluck('people_id')->unique();
        $this->talentPoolsList = collect(); 
        $processed = [];
        foreach ($peopleIds as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->talentPoolsList = $this->talentPoolsList->merge(getTalentPoolList($peopleId));
                $processed[] = $peopleId;
            }
        }

        $this->talentPoolsList = $this->talentPoolsList->unique('id')->values();
        $this->addToTalentPoolPopup = true;
        $this->openTalentPopup = true;
    }

    public function addSelectedPersonToTalentPool()
    {  
        $this->disableDispatchforMultipleSelect = true;
        foreach($this->selectedPerson as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;
            $this->addpeopleToTalentPools();
        }
        $this->openTalentPopup = false;
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected talent pools!');
        $this->addToTalentPoolPopup = false;
        $this->vopen = false;
        $this->addToTalentPoolArray = [];
        $this->selectedPerson = [];
       
    }
    

    public function addSelectedToPlan()
    {
        $peopleIds = pipeline::whereIn('id', $this->selectedPerson)->pluck('people_id')->unique();

        $this->plansList = collect(); 
        
        $processed = [];
        
        foreach ($peopleIds as $peopleId) {
            if (!in_array($peopleId, $processed)) {
                $this->plansList = $this->plansList->merge(getPlansList($peopleId, $this->plan));
                $processed[] = $peopleId;
            }
        }
      
        $this->plansList = $this->plansList->unique('id')->values();
        $this->addToPlanPopup = true;
        $this->openPlanPopup = true;
        
    }
    public function addSelectedPersonToPlans()
    {
      $this->disableDispatchforMultipleSelect = true;
       foreach ($this->selectedPerson as $id) { 
            $this->selectedIndividualID=[];
            $this->selectedIndividualID = $id;
            $this->addpeopleToPlans();
       }
        $this->openPlanPopup = false;
        $this->selectedPerson = [];
        $this->disableDispatchforMultipleSelect = false;
        $this->dispatch('toast', 'info', 'Added to selected plans!');
        $this->addToPlanPopup = false;
       
        $this->vopen = false;
        $this->addToPlansArray = [];
    }
    public function deleteSelected()
    {
        $selectedIds = $this->selectedPerson;
        foreach ($selectedIds as $id) {
            pipeline::where('id', $id)->delete();
        }
    
        $this->selectedPerson = [];
    }
    public function updatingPage() {
        $this->selectedPerson = [];
    }
}
