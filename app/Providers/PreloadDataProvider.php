<?php

namespace App\Providers;
use App\Services\DataPreloader;

use Illuminate\Support\ServiceProvider;

class PreloadDataProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(DataPreloader::class, function ($app) {
            return new DataPreloader();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
