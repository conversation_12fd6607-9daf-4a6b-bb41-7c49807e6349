<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
		Inertia::share([
            'toast' => function () {
                return session('toast');
            },
        ]);
		
        DB::listen(function ($query) {
            logger($query->sql, $query->bindings, $query->time);
        });

        // Configure AI service rate limiters
        $this->configureAiRateLimiters();
    }

    /**
     * Configure rate limiters for AI services
     */
    protected function configureAiRateLimiters(): void
    {
        // OpenAI rate limiter
        RateLimiter::for('openai', function (Request $request) {
            $user = $request->user();
            
            if (!$user) {
                return Limit::none();
            }

            // Admin users get unlimited access
            if ($user->role === 'admin') {
                return Limit::none();
            }

            // Regular users get limited access
            $limit = config('ai.rate_limits.openai.requests_per_minute', 10);
            return Limit::perMinute($limit)->by($user->id)->response(function () {
                return response()->json([
                    'error' => 'Too many OpenAI requests. Please wait before trying again.',
                    'retry_after' => 60
                ], 429);
            });
        });

        // Anthropic rate limiter
        RateLimiter::for('anthropic', function (Request $request) {
            $user = $request->user();
            
            if (!$user) {
                return Limit::none();
            }

            // Admin users get unlimited access
            if ($user->role === 'admin') {
                return Limit::none();
            }

            // Regular users get limited access
            $limit = config('ai.rate_limits.anthropic.requests_per_minute', 8);
            return Limit::perMinute($limit)->by($user->id)->response(function () {
                return response()->json([
                    'error' => 'Too many Anthropic requests. Please wait before trying again.',
                    'retry_after' => 60
                ], 429);
            });
        });

        // Exa AI rate limiter
        RateLimiter::for('exa-ai', function (Request $request) {
            $user = $request->user();
            
            if (!$user) {
                return Limit::none();
            }

            // Admin users get unlimited access
            if ($user->role === 'admin') {
                return Limit::none();
            }

            // Regular users get limited access
            $limit = config('ai.rate_limits.exa.requests_per_minute', 5);
            return Limit::perMinute($limit)->by($user->id)->response(function () {
                return response()->json([
                    'error' => 'Too many Exa AI requests. Please wait before trying again.',
                    'retry_after' => 60
                ], 429);
            });
        });
    }
}
