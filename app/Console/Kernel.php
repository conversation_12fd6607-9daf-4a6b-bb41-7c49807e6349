<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        $schedule->job(new \App\Jobs\CleanupOldLogFiles)->monthly();

        // Monitor queue sizes and send alerts if they exceed thresholds
        // Monitor default queue with threshold of 100 jobs
        $schedule->command('queue:monitor default --max=100')->everyMinute();

        // Monitor internal search queue with lower threshold due to higher priority
        $schedule->command('queue:monitor internal_search --max=50')->everyMinute();

        // Monitor external search queue
        $schedule->command('queue:monitor external_search --max=75')->everyMinute();

        // Take Horizon snapshots for metrics (recommended by Laravel)
        $schedule->command('horizon:snapshot')->everyFiveMinutes();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
