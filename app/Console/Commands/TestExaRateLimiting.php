<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\RateLimitedExaService;
use Illuminate\Support\Facades\Http;

class TestExaRateLimiting extends Command
{
    protected $signature = 'test:exa-rate-limiting {--clear} {--simulate-load=5}';
    protected $description = 'Test Exa AI rate limiting without making real API calls';

    public function handle()
    {
        $service = new RateLimitedExaService();
        $testKey = 'test-exa-rate-limiting';
        
        if ($this->option('clear')) {
            $service->clearRateLimit($testKey);
            $this->info("✅ Cleared Exa rate limits for testing");
            return;
        }
        
        $this->info("🧪 Testing Exa AI Rate Limiting");
        $this->line("");
        
        // Show initial status
        $this->showRateLimitStatus($service, $testKey, "Initial State");
        
        // Simulate load
        $simulateLoad = (int) $this->option('simulate-load');
        $this->info("🔄 Simulating {$simulateLoad} Exa search requests...");
        
        // Mock HTTP responses to avoid real API calls
        Http::fake([
            'api.exa.ai/*' => Http::response([
                'results' => [
                    ['url' => 'https://linkedin.com/test', 'title' => 'Test Profile']
                ]
            ], 200)
        ]);
        
        $successCount = 0;
        $rateLimitedCount = 0;
        
        for ($i = 1; $i <= $simulateLoad; $i++) {
            $payload = [
                'query' => "Software Engineer at Tech Company #{$i}",
                'numResults' => 10,
                'type' => 'auto',
                'category' => 'linkedin profile'
            ];
            
            $result = $service->search($payload, $testKey);
            
            if ($result) {
                $successCount++;
                $this->line("  ✅ Request #{$i}: Success");
            } else {
                $rateLimitedCount++;
                $this->line("  ⚠️  Request #{$i}: Rate limited");
            }
            
            // Show status every 2 requests
            if ($i % 2 === 0) {
                $this->showRateLimitStatus($service, $testKey, "After {$i} requests");
            }
        }
        
        $this->line("");
        $this->info("📊 Test Results:");
        $this->line("  • Successful requests: {$successCount}");
        $this->line("  • Rate limited requests: {$rateLimitedCount}");
        
        // Final status
        $this->showRateLimitStatus($service, $testKey, "Final State");
        
        $this->line("");
        $this->info("💡 Commands to try:");
        $this->line("  • Monitor: php artisan exa:monitor-limits {$testKey}");
        $this->line("  • Clear: php artisan test:exa-rate-limiting --clear");
        $this->line("  • Heavy load: php artisan test:exa-rate-limiting --simulate-load=350");
    }
    
    private function showRateLimitStatus(RateLimitedExaService $service, string $key, string $title): void
    {
        $status = $service->getRateLimitStatus($key);
        
        $this->line("");
        $this->line("📈 {$title}:");
        $this->line("  • RPM: {$status['rpm']['current']}/{$status['rpm']['limit']} (remaining: {$status['rpm']['remaining']})");
        
        if ($status['rpm']['available_in'] > 0) {
            $this->line("  • RPM reset in: {$status['rpm']['available_in']} seconds");
        }
    }
}