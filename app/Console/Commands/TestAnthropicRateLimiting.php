<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\RateLimitedAnthropicService;
use Illuminate\Support\Facades\Http;

class TestAnthropicRateLimiting extends Command
{
    protected $signature = 'test:anthropic-rate-limiting {--clear} {--simulate-load=5}';
    protected $description = 'Test Anthropic AI rate limiting without making real API calls';

    public function handle()
    {
        $service = new RateLimitedAnthropicService();
        $testKey = 'test-anthropic-rate-limiting';
        
        if ($this->option('clear')) {
            $service->clearRateLimit($testKey);
            $this->info("✅ Cleared Anthropic rate limits for testing");
            return;
        }
        
        $this->info("🧪 Testing Anthropic AI Rate Limiting");
        $this->line("");
        
        // Show initial status
        $this->showRateLimitStatus($service, $testKey, "Initial State");
        
        // Simulate load
        $simulateLoad = (int) $this->option('simulate-load');
        $this->info("🔄 Simulating {$simulateLoad} Claude messages requests...");
        
        // Mock HTTP responses to avoid real API calls
        Http::fake([
            'api.anthropic.com/*' => Http::response([
                'content' => [
                    ['type' => 'text', 'text' => 'Test response from Claude']
                ],
                'usage' => [
                    'input_tokens' => 20,
                    'output_tokens' => 50
                ]
            ], 200)
        ]);
        
        $successCount = 0;
        $rateLimitedCount = 0;
        
        for ($i = 1; $i <= $simulateLoad; $i++) {
            $payload = [
                'model' => 'claude-3-sonnet-20240229',
                'max_tokens' => 100,
                'messages' => [
                    ['role' => 'user', 'content' => "Test message #{$i}"]
                ]
            ];
            
            $result = $service->messages($payload, $testKey);
            
            if ($result) {
                $successCount++;
                $this->line("  ✅ Request #{$i}: Success");
            } else {
                $rateLimitedCount++;
                $this->line("  ⚠️  Request #{$i}: Rate limited");
            }
            
            // Show status every 2 requests
            if ($i % 2 === 0) {
                $this->showRateLimitStatus($service, $testKey, "After {$i} requests");
            }
        }
        
        $this->line("");
        $this->info("📊 Test Results:");
        $this->line("  • Successful requests: {$successCount}");
        $this->line("  • Rate limited requests: {$rateLimitedCount}");
        
        // Final status
        $this->showRateLimitStatus($service, $testKey, "Final State");
        
        $this->line("");
        $this->info("💡 Commands to try:");
        $this->line("  • Monitor: php artisan anthropic:monitor-limits {$testKey}");
        $this->line("  • Clear: php artisan test:anthropic-rate-limiting --clear");
        $this->line("  • Heavy load: php artisan test:anthropic-rate-limiting --simulate-load=55");
    }
    
    private function showRateLimitStatus(RateLimitedAnthropicService $service, string $key, string $title): void
    {
        $status = $service->getRateLimitStatus($key);
        
        $this->line("");
        $this->line("📈 {$title}:");
        $this->line("  • RPM: {$status['rpm']['current']}/{$status['rpm']['limit']} (remaining: {$status['rpm']['remaining']})");
        $this->line("  • ITPM: {$status['itpm']['current']}/{$status['itpm']['limit']} (remaining: {$status['itpm']['remaining']})");
        $this->line("  • OTPM: {$status['otpm']['current']}/{$status['otpm']['limit']} (remaining: {$status['otpm']['remaining']})");
        
        if ($status['rpm']['available_in'] > 0) {
            $this->line("  • RPM reset in: {$status['rpm']['available_in']} seconds");
        }
        if ($status['itpm']['available_in'] > 0) {
            $this->line("  • ITPM reset in: {$status['itpm']['available_in']} seconds");
        }
        if ($status['otpm']['available_in'] > 0) {
            $this->line("  • OTPM reset in: {$status['otpm']['available_in']} seconds");
        }
    }
}