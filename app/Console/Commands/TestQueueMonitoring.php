<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SearchCandidatesJob;
use App\Models\User;
use Illuminate\Support\Facades\Queue;

class TestQueueMonitoring extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:queue-monitoring {--jobs=50 : Number of test jobs to dispatch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test queue monitoring by dispatching multiple jobs to trigger alerts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $jobCount = $this->option('jobs');
        
        $this->info("🧪 Testing Queue Monitoring System");
        $this->line("Dispatching {$jobCount} test jobs to trigger queue monitoring alerts...");
        $this->line("");

        // Get a test user
        $user = User::where('email', '<EMAIL>')->first();
        
        if (!$user) {
            $this->error("❌ Development user not found. <NAME_EMAIL> user exists.");
            return 1;
        }

        // Create synthetic plan data for testing
        $planData = [
            'plan_name' => 'Test Queue Monitoring Plan',
            'position_title' => 'Test Position',
            'department' => 'Test Department',
            'location' => 'Test Location',
            'skills' => ['Leadership', 'Management', 'Strategy'],
            'experience_years' => 5,
            'education_level' => 'Bachelor',
        ];

        $this->info("📊 Current queue status before test:");
        $this->call('queue:monitor', ['queue' => 'default']);
        $this->line("");

        // Dispatch jobs to different queues
        $this->info("🚀 Dispatching jobs...");
        
        $bar = $this->output->createProgressBar($jobCount);
        $bar->start();

        for ($i = 0; $i < $jobCount; $i++) {
            // Alternate between different search types and queues
            $searchType = ['internal', 'external', 'both'][rand(0, 2)];
            
            try {
                SearchCandidatesJob::dispatch($planData, $user, $searchType);
                $bar->advance();
            } catch (\Exception $e) {
                $this->error("\n❌ Failed to dispatch job {$i}: " . $e->getMessage());
                return 1;
            }
        }

        $bar->finish();
        $this->line("");
        $this->line("");

        $this->info("✅ Successfully dispatched {$jobCount} test jobs!");
        $this->line("");

        $this->info("📊 Queue status after dispatching jobs:");
        $this->call('queue:monitor', ['queue' => 'default']);
        $this->line("");

        $this->info("🔍 Monitoring commands you can run:");
        $this->line("• php artisan queue:monitor default --max=10");
        $this->line("• php artisan queue:monitor internal_search --max=5");
        $this->line("• php artisan queue:monitor external_search --max=5");
        $this->line("");

        $this->info("📧 If queue size exceeds thresholds, you should receive email notifications at:");
        $this->line("<EMAIL>");
        $this->line("");

        $this->info("🎯 To process these jobs, run:");
        $this->line("• php artisan queue:work (for database queues)");
        $this->line("• php artisan horizon (for Redis queues)");
        $this->line("");

        $this->warn("⚠️  Note: These are test jobs that may fail if external APIs are not configured.");
        $this->line("This is expected behavior for testing queue monitoring.");

        return 0;
    }
}
