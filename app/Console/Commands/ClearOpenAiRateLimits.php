<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\RateLimitedOpenAiService;

class ClearOpenAiRateLimits extends Command
{
    protected $signature = 'openai:clear-limits {key?} {--all}';
    protected $description = 'Clear OpenAI rate limits for testing or recovery';

    public function handle()
    {
        $service = new RateLimitedOpenAiService();
        
        if ($this->option('all')) {
            // Clear common rate limit keys
            $keys = [
                'external-search-default',
                'external-search-unknown',
                'chat-completion-default'
            ];
            
            $this->info("🧹 Clearing all common OpenAI rate limits...");
            
            foreach ($keys as $key) {
                $service->clearRateLimit($key);
                $this->line("✅ Cleared: {$key}");
            }
        } else {
            $key = $this->argument('key') ?? 'external-search-default';
            
            $this->info("🧹 Clearing OpenAI rate limits for key: {$key}");
            $service->clearRateLimit($key);
            $this->info("✅ Rate limits cleared for: {$key}");
        }
        
        $this->line("");
        $this->line("💡 Use 'php artisan openai:monitor-limits' to verify");
    }
}