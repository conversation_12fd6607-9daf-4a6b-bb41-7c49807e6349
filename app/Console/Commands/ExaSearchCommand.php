<?php

namespace App\Console\Commands;

use App\Services\AI\RateLimitedOpenAiService;
use App\Services\AI\RateLimitedExaService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ExaSearchCommand extends Command
{
    protected $signature = 'exa:search 
                            {--target-role= : Target role to search for}
                            {--target-company= : Target company to search for}
                            {--include-companies= : Comma-separated list of companies to include}
                            {--exclude-companies= : Comma-separated list of companies to exclude}
                            {--location= : Location to search in}
                            {--years-experience= : Years of experience required}
                            {--num-results=50 : Number of results to return}
                            {--max-profiles=10 : Maximum profiles to process}
                            {--batch-size=5 : Batch size for processing}
                            {--output-file= : Output file path}';

    protected $description = 'Search for external candidates using Exa AI and OpenAI with enhanced rate limiting';

    protected RateLimitedOpenAiService $openAiService;
    protected RateLimitedExaService $exaService;

    public function __construct()
    {
        parent::__construct();
        $this->openAiService = new RateLimitedOpenAiService();
        $this->exaService = new RateLimitedExaService();
    }

    public function handle()
    {
        $startTime = microtime(true);
        
        $this->info('Starting Exa AI External Search with Enhanced Rate Limiting');
        
        $targetRole = $this->option('target-role') ?? 'Software Engineer';
        $targetCompany = $this->option('target-company') ?? '';
        $includeCompanies = $this->option('include-companies') ? explode(',', $this->option('include-companies')) : [];
        $excludeCompanies = $this->option('exclude-companies') ? explode(',', $this->option('exclude-companies')) : [];
        $location = $this->option('location') ?? '';
        $yearsExperience = $this->option('years-experience') ?? '';
        $numResults = (int) $this->option('num-results');
        $maxProfiles = (int) $this->option('max-profiles');
        $batchSize = (int) $this->option('batch-size');
        $outputFile = $this->option('output-file');

        Log::info('Exa search started', [
            'target_role' => $targetRole,
            'target_company' => $targetCompany,
            'num_results' => $numResults,
            'max_profiles' => $maxProfiles,
            'batch_size' => $batchSize
        ]);

        try {
            // Step 1: Build search query
            $searchQuery = $this->buildSearchQuery($targetRole, $targetCompany, $location, $yearsExperience);
            $this->info("Search Query: {$searchQuery}");

            // Step 2: Search with Exa AI
            $this->info('Searching LinkedIn profiles with Exa AI...');
            $searchResults = $this->searchWithExa($searchQuery, $numResults);
            
            if (empty($searchResults)) {
                $this->error('No search results found');
                return Command::FAILURE;
            }

            $this->info(sprintf('Found %d LinkedIn profiles', count($searchResults)));

            // Step 3: Process profiles in batches
            $profiles = $this->processProfilesInBatches(
                $searchResults, 
                $maxProfiles, 
                $batchSize, 
                $targetRole, 
                $targetCompany,
                $includeCompanies,
                $excludeCompanies
            );

            // Step 4: Output results
            $this->outputResults($profiles, $outputFile);

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            
            $this->info(sprintf(
                'Search completed in %s seconds. Generated %d profiles.',
                $duration,
                count($profiles)
            ));

            Log::info('Exa search completed', [
                'duration' => $duration,
                'profiles_generated' => count($profiles)
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Search failed: ' . $e->getMessage());
            Log::error('Exa search failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    protected function buildSearchQuery(string $targetRole, string $targetCompany, string $location, string $yearsExperience): string
    {
        $query = "site:linkedin.com/in/ \"{$targetRole}\"";
        
        if ($targetCompany) {
            $query .= " \"{$targetCompany}\"";
        }
        
        if ($location) {
            $query .= " \"{$location}\"";
        }
        
        if ($yearsExperience) {
            $query .= " \"{$yearsExperience} years\"";
        }

        return $query;
    }

    protected function searchWithExa(string $query, int $numResults): array
    {
        try {
            $response = $this->exaService->search([
                'query' => $query,
                'num_results' => $numResults,
                'type' => 'neural',
                'include_domains' => ['linkedin.com']
            ]);

            if (!$response || !isset($response['results'])) {
                throw new \Exception('Invalid response from Exa API');
            }

            return array_map(function($result) {
                return [
                    'url' => $result['url'] ?? '',
                    'title' => $result['title'] ?? '',
                    'score' => $result['score'] ?? 0,
                    'id' => $result['id'] ?? ''
                ];
            }, $response['results']);

        } catch (\Exception $e) {
            Log::error('Exa search failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    protected function processProfilesInBatches(
        array $searchResults, 
        int $maxProfiles, 
        int $batchSize,
        string $targetRole,
        string $targetCompany,
        array $includeCompanies,
        array $excludeCompanies
    ): array {
        $profiles = [];
        $totalBatches = ceil(min($maxProfiles, count($searchResults)) / $batchSize);
        
        for ($batch = 0; $batch < $totalBatches; $batch++) {
            $batchStart = $batch * $batchSize;
            $batchEnd = min($batchStart + $batchSize, min($maxProfiles, count($searchResults)));
            
            $this->info(sprintf(
                'Processing batch %d/%d: profiles %d-%d',
                $batch + 1,
                $totalBatches,
                $batchStart + 1,
                $batchEnd
            ));

            $batchStartTime = microtime(true);
            $batchProfiles = 0;
            
            for ($i = $batchStart; $i < $batchEnd; $i++) {
                $result = $searchResults[$i];
                $url = $result['url'];
                
                $this->line("Processing content for URL: {$url}");
                
                try {
                    // Get content with Exa
                    $content = $this->getProfileContent($url);
                    
                    // Filter profile
                    if (!$this->shouldProcessProfile($content, $targetRole, $targetCompany, $includeCompanies, $excludeCompanies)) {
                        $this->line("Profile filtered out for {$url}");
                        continue;
                    }
                    
                    // Generate profile with OpenAI
                    $profile = $this->generateProfile($content, $targetRole, $targetCompany, $url);
                    
                    if ($profile) {
                        $profiles[] = $profile;
                        $batchProfiles++;
                        $this->line("✓ Profile generated for {$url}");
                    }
                    
                } catch (\Exception $e) {
                    Log::error("Error processing profile {$url}", ['error' => $e->getMessage()]);
                    $this->line("✗ Error processing {$url}: " . $e->getMessage());
                }
            }
            
            $batchTime = round(microtime(true) - $batchStartTime, 2);
            $avgTime = $batchProfiles > 0 ? round($batchTime / $batchProfiles, 2) : 0;
            $rate = $batchProfiles > 0 ? round($batchProfiles / $batchTime, 2) : 0;
            
            $this->info(sprintf(
                'Batch %d completed in %ss: %d profiles generated (%ss per item, %s items/sec)',
                $batch + 1,
                $batchTime,
                $batchProfiles,
                $avgTime,
                $rate
            ));
        }
        
        return $profiles;
    }

    protected function getProfileContent(string $url): string
    {
        try {
            $response = $this->exaService->getContents([
                'ids' => [$url],
                'text' => true
            ]);
            
            if (!$response || !isset($response['results'][0]['text'])) {
                throw new \Exception('No content returned from Exa API');
            }
            
            return $response['results'][0]['text'];
            
        } catch (\Exception $e) {
            Log::error("Failed to get content for {$url}", ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    protected function shouldProcessProfile(
        string $content, 
        string $targetRole, 
        string $targetCompany,
        array $includeCompanies,
        array $excludeCompanies
    ): bool {
        // Check for target role
        if (stripos($content, $targetRole) !== false) {
            $this->line("Target role exact match found: {$targetRole}");
        }
        
        // Check for target company
        if ($targetCompany && stripos($content, $targetCompany) !== false) {
            $this->line("Company match found: {$targetCompany}");
        }
        
        // Check include companies
        if (!empty($includeCompanies)) {
            $found = false;
            foreach ($includeCompanies as $company) {
                if (stripos($content, trim($company)) !== false) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                return false;
            }
        }
        
        // Check exclude companies
        foreach ($excludeCompanies as $company) {
            if (stripos($content, trim($company)) !== false) {
                return false;
            }
        }
        
        return true;
    }

    protected function generateProfile(string $content, string $targetRole, string $targetCompany, string $url): ?array
    {
        $prompt = $this->buildProfilePrompt($content, $targetRole, $targetCompany);
        
        try {
            $response = $this->openAiService->chatCompletion([
                'model' => 'gpt-4o',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are an expert recruiter analyzing LinkedIn profiles. Extract key information and assess candidate fit.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 2000,
                'temperature' => 0.1
            ], "profile-generation-{$targetRole}");
            
            if (!$response || !isset($response['choices'][0]['message']['content'])) {
                throw new \Exception('Invalid response from OpenAI API');
            }
            
            $content = $response['choices'][0]['message']['content'];
            
            // Try to parse as JSON
            $profileData = json_decode($content, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $profileData['source_url'] = $url;
                $profileData['generated_at'] = Carbon::now()->toISOString();
                return $profileData;
            }
            
            // Fallback to text format
            return [
                'name' => 'Unknown',
                'current_role' => $targetRole,
                'company' => $targetCompany,
                'summary' => $content,
                'source_url' => $url,
                'generated_at' => Carbon::now()->toISOString()
            ];
            
        } catch (\Exception $e) {
            Log::error("Error generating profile for {$url}", ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    protected function buildProfilePrompt(string $content, string $targetRole, string $targetCompany): string
    {
        return "Analyze this LinkedIn profile content and extract key information for a {$targetRole} role" . 
               ($targetCompany ? " at {$targetCompany}" : "") . ":\n\n{$content}\n\n" .
               "Please return a JSON object with the following fields:\n" .
               "- name: Full name\n" .
               "- current_role: Current job title\n" .
               "- company: Current company\n" .
               "- location: Location\n" .
               "- experience_years: Estimated years of experience\n" .
               "- skills: Array of key skills\n" .
               "- education: Education background\n" .
               "- summary: Brief professional summary\n" .
               "- fit_score: Score from 1-10 for role fit\n" .
               "- fit_reasoning: Brief explanation of fit score";
    }

    protected function outputResults(array $profiles, ?string $outputFile): void
    {
        $output = [
            'search_completed_at' => Carbon::now()->toISOString(),
            'total_profiles' => count($profiles),
            'profiles' => $profiles
        ];
        
        $json = json_encode($output, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        
        if ($outputFile) {
            file_put_contents($outputFile, $json);
            $this->info("Results saved to: {$outputFile}");
        } else {
            $this->line($json);
        }
    }
}