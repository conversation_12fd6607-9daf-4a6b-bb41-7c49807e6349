<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SearchCandidatesJob;
use App\Models\User;
use Illuminate\Support\Facades\Queue;

class TestRedisQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:redis-queue {--user-id=1 : User ID to use for testing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Redis queue with SearchCandidatesJob using synthetic data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Redis Queue with SearchCandidatesJob...');

        // Load synthetic data
        $planDataPath = base_path('shared/search_params_synthetic_01.json');
        if (!file_exists($planDataPath)) {
            $this->error('Synthetic data file not found: ' . $planDataPath);
            return 1;
        }

        $planData = json_decode(file_get_contents($planDataPath), true);
        if (!$planData) {
            $this->error('Failed to parse synthetic data JSON');
            return 1;
        }

        // Get user
        $userId = $this->option('user-id');
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return 1;
        }

        $this->info("Plan: {$planData['plan_name']}");
        $this->info("User: {$user->email}");
        $this->info("Queue Connection: " . config('queue.default'));

        // Test different search types
        $searchTypes = ['internal', 'external', 'both'];
        
        foreach ($searchTypes as $searchType) {
            $this->line("Dispatching SearchCandidatesJob with search type: {$searchType}");
            
            try {
                $job = SearchCandidatesJob::dispatch($planData, $user, $searchType);
                $this->info("✓ Job dispatched successfully for search type: {$searchType}");
            } catch (\Exception $e) {
                $this->error("✗ Failed to dispatch job for search type {$searchType}: " . $e->getMessage());
                return 1;
            }
        }

        $this->info('All jobs dispatched successfully!');
        $this->line('You can monitor the jobs using:');
        $this->line('- php artisan horizon (to start the worker)');
        $this->line('- Visit /horizon in your browser to see the dashboard');
        
        return 0;
    }
}
