<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\RateLimitedOpenAiService;
use Illuminate\Support\Facades\Http;

class TestRateLimiting extends Command
{
    protected $signature = 'test:rate-limiting {--clear} {--simulate-load=5}';
    protected $description = 'Test OpenAI rate limiting without making real API calls';

    public function handle()
    {
        $service = new RateLimitedOpenAiService();
        $testKey = 'test-rate-limiting';
        
        if ($this->option('clear')) {
            $service->clearRateLimit($testKey);
            $this->info("✅ Cleared rate limits for testing");
            return;
        }
        
        $this->info("🧪 Testing OpenAI Rate Limiting");
        $this->line("");
        
        // Show initial status
        $this->showRateLimitStatus($service, $testKey, "Initial State");
        
        // Simulate load
        $simulateLoad = (int) $this->option('simulate-load');
        $this->info("🔄 Simulating {$simulateLoad} requests...");
        
        // Mock HTTP responses to avoid real API calls
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    ['message' => ['content' => '{"test": "response"}']]
                ]
            ], 200)
        ]);
        
        $successCount = 0;
        $rateLimitedCount = 0;
        
        for ($i = 1; $i <= $simulateLoad; $i++) {
            $payload = [
                'model' => 'gpt-4o-mini',
                'messages' => [
                    ['role' => 'user', 'content' => "Test request #{$i}"]
                ],
                'max_tokens' => 100
            ];
            
            $result = $service->chatCompletion($payload, $testKey);
            
            if ($result) {
                $successCount++;
                $this->line("  ✅ Request #{$i}: Success");
            } else {
                $rateLimitedCount++;
                $this->line("  ⚠️  Request #{$i}: Rate limited");
            }
            
            // Show status every 2 requests
            if ($i % 2 === 0) {
                $this->showRateLimitStatus($service, $testKey, "After {$i} requests");
            }
        }
        
        $this->line("");
        $this->info("📊 Test Results:");
        $this->line("  • Successful requests: {$successCount}");
        $this->line("  • Rate limited requests: {$rateLimitedCount}");
        
        // Final status
        $this->showRateLimitStatus($service, $testKey, "Final State");
        
        $this->line("");
        $this->info("💡 Commands to try:");
        $this->line("  • Monitor: php artisan openai:monitor-limits {$testKey}");
        $this->line("  • Clear: php artisan test:rate-limiting --clear");
        $this->line("  • Heavy load: php artisan test:rate-limiting --simulate-load=25");
    }
    
    private function showRateLimitStatus(RateLimitedOpenAiService $service, string $key, string $title): void
    {
        $status = $service->getRateLimitStatus($key);
        
        $this->line("");
        $this->line("📈 {$title}:");
        $this->line("  • RPM: {$status['rpm']['current']}/{$status['rpm']['limit']} (remaining: {$status['rpm']['remaining']})");
        $this->line("  • TPM: {$status['tpm']['current']}/{$status['tpm']['limit']} (remaining: {$status['tpm']['remaining']})");
        
        if ($status['rpm']['available_in'] > 0) {
            $this->line("  • RPM reset in: {$status['rpm']['available_in']} seconds");
        }
        if ($status['tpm']['available_in'] > 0) {
            $this->line("  • TPM reset in: {$status['tpm']['available_in']} seconds");
        }
    }
}