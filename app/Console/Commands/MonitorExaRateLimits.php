<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\RateLimitedExaService;

class MonitorExaRateLimits extends Command
{
    protected $signature = 'exa:monitor-limits {key?}';
    protected $description = 'Monitor Exa AI rate limits for external search jobs';

    public function handle()
    {
        $key = $this->argument('key') ?? 'exa-search-default';
        $service = new RateLimitedExaService();
        
        $this->info("🔍 Exa AI Rate Limit Monitor");
        $this->line("Key: {$key}");
        $this->line("");
        
        $status = $service->getRateLimitStatus($key);
        
        // RPM Status
        $this->line("📊 Requests Per Minute (RPM)");
        $this->line("Current: {$status['rpm']['current']}/{$status['rpm']['limit']}");
        $this->line("Remaining: {$status['rpm']['remaining']}");
        if ($status['rpm']['available_in'] > 0) {
            $this->error("⚠️  Rate limited! Available in: {$status['rpm']['available_in']} seconds");
        } else {
            $this->info("✅ Within RPM limits");
        }
        
        $this->line("");
        
        // Show percentage usage
        $rpmPercent = round(($status['rpm']['current'] / $status['rpm']['limit']) * 100, 1);
        
        $this->line("📈 Usage Percentage");
        $this->line("RPM: {$rpmPercent}%");
        
        // Warn if approaching limits
        if ($rpmPercent > 80) {
            $this->warn("⚠️  Approaching rate limits!");
        }
        
        if ($this->option('verbose')) {
            $this->line("");
            $this->line("🔧 Rate Limit Actions");
            $this->line("Clear limits: php artisan exa:clear-limits {$key}");
            $this->line("Monitor continuously: watch -n 5 'php artisan exa:monitor-limits {$key}'");
        }
    }
}