<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SearchCandidatesJob;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Services\AI\RateLimitedOpenAiService;
use App\Services\AI\RateLimitedExaService;
use App\Services\AI\RateLimitedAnthropicService;

class RealPerformanceTestCommand extends Command
{
    protected $signature = 'test:performance 
                            {--jobs=10 : Number of jobs to dispatch}
                            {--user-id=5 : User ID to use for testing}
                            {--iterations=1 : Number of test iterations}
                            {--search-type=both : Search type (internal, external, both)}';

    protected $description = 'Performance test using actual SearchCandidatesJob with real succession plan data (LaravelDaily methodology)';

    protected $testStartTime;
    protected $testId;

    public function handle(): int
    {
        $this->testId = 'real_perf_test_' . uniqid();
        $this->testStartTime = microtime(true);

        $jobCount = (int) $this->option('jobs');
        $userId = (int) $this->option('user-id');
        $iterations = (int) $this->option('iterations');
        $searchType = $this->option('search-type');

        $this->info("🚀 Starting performance test with {$jobCount} SearchCandidatesJob instances");
        $this->info("📊 Test ID: {$this->testId}");
        $this->info("🔍 Search Type: {$searchType}");
        $this->info("📖 Using LaravelDaily methodology with real succession planning data");

        // Validate user exists
        $user = User::find($userId);
        if (!$user) {
            $this->error("❌ User with ID {$userId} not found");
            return 1;
        }

        // Load real succession plan data files
        $planDataFiles = $this->loadRealPlanData();
        if (empty($planDataFiles)) {
            $this->error('❌ No succession plan data files found in /shared directory');
            return 1;
        }

        $this->info("📁 Loaded " . count($planDataFiles) . " real succession plan data files");

        $totalResults = [];

        // Run iterations
        for ($iteration = 1; $iteration <= $iterations; $iteration++) {
            $this->info("\n🔄 Running iteration {$iteration}/{$iterations}");
            
            $results = $this->runRealPerformanceTest($jobCount, $user, $planDataFiles, $iteration, $searchType);
            $totalResults[] = $results;
            
            if ($iteration < $iterations) {
                $this->info('⏳ Waiting 60 seconds before next iteration (for real jobs to complete)...');
                sleep(60);
            }
        }

        // Display results
        $this->displayRealResults($totalResults, $jobCount, $iterations);

        return 0;
    }

    protected function runRealPerformanceTest($jobCount, $user, $planDataFiles, $iteration, $searchType): array
    {
        $iterationId = "{$this->testId}_iter_{$iteration}";
        
        // Pre-test metrics
        $preTestMetrics = $this->getSystemMetrics();
        
        $this->info("📋 Pre-test system state:");
        $this->info("   Memory usage: " . $this->formatBytes(memory_get_usage(true)));
        $this->info("   Queue size: " . $this->getQueueSize());
        $this->info("   Failed jobs: " . $this->getFailedJobsCount());

        // Measure dispatch time using Laravel Benchmark (like the article)
        $this->info("📤 Dispatching {$jobCount} SearchCandidatesJob jobs...");
        
        $jobStartTimes = [];
        $dispatchTime = \Illuminate\Support\Benchmark::measure(function () use ($jobCount, $planDataFiles, $user, $searchType, &$jobStartTimes) {
            for ($i = 1; $i <= $jobCount; $i++) {
                $planData = $planDataFiles[($i - 1) % count($planDataFiles)];
                
                // Add unique identifier to track this specific job
                $planData['performance_test_id'] = "real_job_{$i}_" . uniqid();
                $jobStartTimes[$planData['performance_test_id']] = microtime(true);
                
                // Dispatch the REAL SearchCandidatesJob
                dispatch(new SearchCandidatesJob($planData, $user, $searchType));
                
                $this->info("   → Dispatched job {$i}: {$planData['plan_name']}");
            }
        }, 1);
        
        $this->info("✅ Dispatched {$jobCount} jobs in " . round($dispatchTime, 3) . " seconds");

        // Monitor execution by watching logs and database
        $this->info("⏱️  Monitoring job execution...");
        $executionMetrics = $this->monitorRealExecution($jobStartTimes, $jobCount);

        // Post-test metrics
        $postTestMetrics = $this->getSystemMetrics();

        return [
            'iteration' => $iteration,
            'job_count' => $jobCount,
            'dispatch_time' => $dispatchTime,
            'execution_metrics' => $executionMetrics,
            'pre_test_metrics' => $preTestMetrics,
            'post_test_metrics' => $postTestMetrics,
            'search_type' => $searchType,
            'plan_files_used' => count($planDataFiles)
        ];
    }

    protected function monitorRealExecution($jobStartTimes, $expectedJobs): array
    {
        $startTime = microtime(true);
        $completedJobs = 0;
        $maxWaitTime = 3600; // 1 hour max wait for real jobs
        $checkInterval = 15; // Check every 15 seconds for real jobs
        
        $jobExecutionTimes = [];
        $jobsPerMinuteHistory = [];
        $failedJobs = 0;

        $this->info("📊 Monitoring job execution (checking every {$checkInterval}s)...");
        $this->info("💡 View live metrics at: /horizon (if accessible)");
        $this->info("📝 Watch logs: tail -f storage/logs/laravel-" . date('Y-m-d') . ".log | grep 'SEARCH JOB'");

        $initialFailedCount = $this->getFailedJobsCount();

        while ($completedJobs < $expectedJobs && (microtime(true) - $startTime) < $maxWaitTime) {
            // Check for completed jobs by reading recent log entries
            $completedInThisCheck = $this->countCompletedJobsFromLogs($jobStartTimes);
            
            // Check for failed jobs
            $currentFailedCount = $this->getFailedJobsCount();
            $failedJobs = $currentFailedCount - $initialFailedCount;

            $completedJobs = $completedInThisCheck;

            // Calculate current throughput
            $elapsedMinutes = (microtime(true) - $startTime) / 60;
            $currentJobsPerMinute = $elapsedMinutes > 0 ? ($completedJobs / $elapsedMinutes) : 0;
            $jobsPerMinuteHistory[] = $currentJobsPerMinute;

            // Progress update
            $this->info("⏱️  Time: " . round(microtime(true) - $startTime, 1) . "s | " .
                       "Completed: {$completedJobs}/{$expectedJobs} | " .
                       "Failed: {$failedJobs} | " .
                       "Rate: " . round($currentJobsPerMinute, 1) . " jobs/min | " .
                       "Queue: " . $this->getQueueSize());

            if ($completedJobs >= $expectedJobs) {
                break;
            }

            sleep($checkInterval);
        }

        $totalExecutionTime = microtime(true) - $startTime;

        if ($completedJobs < $expectedJobs) {
            $this->warn("⚠️  Only {$completedJobs}/{$expectedJobs} jobs completed within timeout");
        }

        return [
            'total_execution_time' => $totalExecutionTime,
            'completed_jobs' => $completedJobs,
            'failed_jobs' => $failedJobs,
            'jobs_per_minute' => !empty($jobsPerMinuteHistory) ? end($jobsPerMinuteHistory) : 0,
            'max_execution_time' => $totalExecutionTime,
            'jobs_per_minute_history' => $jobsPerMinuteHistory
        ];
    }

    protected function countCompletedJobsFromLogs($jobStartTimes): int
    {
        // Read recent log entries to count completed jobs from daily log file
        $logFile = storage_path('logs/laravel-' . date('Y-m-d') . '.log');
        
        if (!file_exists($logFile)) {
            return 0;
        }

        // Use tail command to get last 1000 lines efficiently without loading entire file
        $command = "tail -n 1000 " . escapeshellarg($logFile);
        $output = shell_exec($command);
        
        if (!$output) {
            return 0;
        }

        $lines = explode("\n", $output);
        $completedCount = 0;

        foreach ($lines as $line) {
            if (strpos($line, 'SEARCH JOB: Completed search for plan ID') !== false) {
                $completedCount++;
            }
        }

        return $completedCount;
    }

    protected function displayRealResults($allResults, $jobCount, $iterations): void
    {
        $this->info("\n" . str_repeat("=", 80));
        $this->info("📊 PERFORMANCE TEST RESULTS (SearchCandidatesJob)");
        $this->info(str_repeat("=", 80));

        // Calculate averages
        $avgDispatchTime = array_sum(array_column($allResults, 'dispatch_time')) / $iterations;
        $avgJobsPerMinute = array_sum(array_column(array_column($allResults, 'execution_metrics'), 'jobs_per_minute')) / $iterations;
        $avgExecutionTime = array_sum(array_column(array_column($allResults, 'execution_metrics'), 'total_execution_time')) / $iterations;
        $totalCompletedJobs = array_sum(array_column(array_column($allResults, 'execution_metrics'), 'completed_jobs'));
        $totalFailedJobs = array_sum(array_column(array_column($allResults, 'execution_metrics'), 'failed_jobs'));

        $this->info("🎯 Test Summary:");
        $this->info("   Test ID: {$this->testId}");
        $this->info("   Jobs per iteration: {$jobCount}");
        $this->info("   Total iterations: {$iterations}");
        $this->info("   Total jobs dispatched: " . ($jobCount * $iterations));
        $this->info("   Search type: " . $allResults[0]['search_type']);
        $this->info("   Queue connection: " . config('queue.default'));

        $this->info("\n⚡ Dispatch Performance (SearchCandidatesJob):");
        $this->info("   Average dispatch time: " . round($avgDispatchTime, 3) . " seconds");
        $this->info("   Jobs dispatched per second: " . round($jobCount / $avgDispatchTime, 2));

        $this->info("\n🏃 Job Execution Performance:");
        $this->info("   Total completed jobs: {$totalCompletedJobs}");
        $this->info("   Total failed jobs: {$totalFailedJobs}");
        $this->info("   Average execution time: " . round($avgExecutionTime, 2) . " seconds");
        $this->info("   Jobs per minute: " . round($avgJobsPerMinute, 2));

        // Real performance bottleneck analysis
        $this->analyzeBottlenecks($allResults, $avgDispatchTime, $avgJobsPerMinute, $totalFailedJobs);

        $this->info("\n📝 Detailed logs in: storage/logs/laravel-" . date('Y-m-d') . ".log (search for 'SEARCH JOB')");
        $this->info("🎯 Test completed in " . round(microtime(true) - $this->testStartTime, 2) . " seconds");
    }

    protected function analyzeBottlenecks($results, $avgDispatchTime, $avgJobsPerMinute, $totalFailedJobs): void
    {
        $this->info("\n🔍 PERFORMANCE BOTTLENECK ANALYSIS:");
        $this->info(str_repeat("-", 50));

        $bottlenecks = [];

        // Analyze dispatch performance for SearchCandidatesJob
        if ($avgDispatchTime > 10) {
            $bottlenecks[] = "⚠️  SLOW DISPATCH: {$avgDispatchTime}s for SearchCandidatesJob dispatch";
        }

        // Analyze job throughput (SearchCandidatesJob is complex, expect lower throughput)
        if ($avgJobsPerMinute < 5) {
            $bottlenecks[] = "🔴 VERY LOW THROUGHPUT: {$avgJobsPerMinute} jobs/min (SearchCandidatesJob with 30min timeout)";
        } elseif ($avgJobsPerMinute < 15) {
            $bottlenecks[] = "🟡 LOW THROUGHPUT: {$avgJobsPerMinute} jobs/min (target: >15/min for complex jobs)";
        }

        // Analyze failures
        if ($totalFailedJobs > 0) {
            $bottlenecks[] = "🔴 JOB FAILURES: {$totalFailedJobs} failed jobs detected";
        }

        // Worker configuration analysis
        $horizonConfig = config('horizon.environments.' . app()->environment());
        $maxProcesses = $horizonConfig['supervisor-1']['maxProcesses'] ?? 3;
        
        if ($maxProcesses < 5) {
            $bottlenecks[] = "⚠️  LOW WORKER COUNT: {$maxProcesses} workers (recommend 5-15 for SearchCandidatesJob)";
        }

        if (empty($bottlenecks)) {
            $this->info("✅ No significant bottlenecks detected for SearchCandidatesJob!");
            $this->info("💡 Your Redis queue migration handles succession planning jobs well");
        } else {
            $this->info("🚨 Performance bottlenecks identified:");
            foreach ($bottlenecks as $bottleneck) {
                $this->info("   " . $bottleneck);
            }

            $this->info("\n💡 Specific recommendations for SearchCandidatesJob performance:");
            $this->displayRecommendations($avgJobsPerMinute, $maxProcesses, $totalFailedJobs);
        }
    }

    protected function displayRecommendations($jobsPerMinute, $currentWorkers, $failedJobs): void
    {
        if ($jobsPerMinute < 10) {
            $this->info("   • Increase Horizon workers from {$currentWorkers} to 8-15 for 30-minute SearchCandidatesJob");
            $this->info("   • Consider separating internal vs external search jobs to different queues");
            $this->info("   • Monitor AI service response times (major bottleneck for SearchCandidatesJob)");
        }

        if ($failedJobs > 0) {
            $this->info("   • Check failed_jobs table: php artisan queue:failed");
            $this->info("   • Review error logs for SearchCandidatesJob failures");
            $this->info("   • Consider increasing retry_after in config/queue.php beyond 1800s");
        }

        $this->info("   • Monitor SearchCandidatesJob with: grep 'SEARCH JOB' storage/logs/laravel-" . date('Y-m-d') . ".log");
        $this->info("   • For 100+ concurrent jobs: use dedicated Redis queue server");
        $this->info("   • Optimize AI service calls in InternalPeopleSearch and ExternalPeopleSearch");
        
        // Show rate limit status for all AI services
        $this->info("");
        $this->info("🔍 AI Services Rate Limit Status:");
        
        // OpenAI Status
        $openAiService = new RateLimitedOpenAiService();
        $openAiStatus = $openAiService->getRateLimitStatus('external-search-default');
        $this->info("   🤖 OpenAI:");
        $this->info("      • RPM: {$openAiStatus['rpm']['current']}/{$openAiStatus['rpm']['limit']} ({$openAiStatus['rpm']['remaining']} remaining)");
        $this->info("      • TPM: {$openAiStatus['tpm']['current']}/{$openAiStatus['tpm']['limit']} ({$openAiStatus['tpm']['remaining']} remaining)");
        
        // Exa Status
        $exaService = new RateLimitedExaService();
        $exaStatus = $exaService->getRateLimitStatus('external-search-exa-default');
        $this->info("   🔍 Exa AI:");
        $this->info("      • RPM: {$exaStatus['rpm']['current']}/{$exaStatus['rpm']['limit']} ({$exaStatus['rpm']['remaining']} remaining)");
        
        // Anthropic Status  
        $anthropicService = new RateLimitedAnthropicService();
        $anthropicStatus = $anthropicService->getRateLimitStatus('anthropic-messages-default');
        $this->info("   🧠 Anthropic:");
        $this->info("      • RPM: {$anthropicStatus['rpm']['current']}/{$anthropicStatus['rpm']['limit']} ({$anthropicStatus['rpm']['remaining']} remaining)");
        $this->info("      • ITPM: {$anthropicStatus['itpm']['current']}/{$anthropicStatus['itpm']['limit']} ({$anthropicStatus['itpm']['remaining']} remaining)");
        $this->info("      • OTPM: {$anthropicStatus['otpm']['current']}/{$anthropicStatus['otpm']['limit']} ({$anthropicStatus['otpm']['remaining']} remaining)");
        
        // Check for any rate limits
        $rateLimited = false;
        if ($openAiStatus['rpm']['available_in'] > 0 || $openAiStatus['tpm']['available_in'] > 0) {
            $rateLimited = true;
        }
        if ($exaStatus['rpm']['available_in'] > 0) {
            $rateLimited = true;
        }
        if ($anthropicStatus['rpm']['available_in'] > 0 || $anthropicStatus['itpm']['available_in'] > 0 || $anthropicStatus['otpm']['available_in'] > 0) {
            $rateLimited = true;
        }
        
        if ($rateLimited) {
            $this->warn("   ⚠️  Rate limits detected! Use monitoring commands to check details");
            $this->info("      • php artisan openai:monitor-limits");
            $this->info("      • php artisan exa:monitor-limits");
            $this->info("      • php artisan anthropic:monitor-limits");
        } else {
            $this->info("   ✅ No rate limits detected across all AI services");
        }
    }

    protected function loadRealPlanData(): array
    {
        $files = glob(base_path('shared/search_params_*.json'));
        $data = [];

        foreach ($files as $file) {
            $content = file_get_contents($file);
            $jsonData = json_decode($content, true);
            
            if ($jsonData && isset($jsonData['plan_name'])) {
                $data[] = $jsonData;
                $this->info("📄 Loaded: " . $jsonData['plan_name']);
            }
        }

        return $data;
    }

    protected function getSystemMetrics(): array
    {
        return [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'queue_size' => $this->getQueueSize(),
            'failed_jobs' => $this->getFailedJobsCount(),
            'timestamp' => Carbon::now()
        ];
    }

    protected function getQueueSize(): int
    {
        try {
            return DB::connection('redis')->command('llen', ['queues:default']) ?? 0;
        } catch (\Exception $e) {
            return -1;
        }
    }

    protected function getFailedJobsCount(): int
    {
        try {
            return DB::table('failed_jobs')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function formatBytes($bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}