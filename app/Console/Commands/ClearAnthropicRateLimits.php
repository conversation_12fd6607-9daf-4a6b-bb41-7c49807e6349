<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\RateLimitedAnthropicService;

class ClearAnthropicRateLimits extends Command
{
    protected $signature = 'anthropic:clear-limits {key?} {--all}';
    protected $description = 'Clear Anthropic AI rate limits for testing or recovery';

    public function handle()
    {
        $service = new RateLimitedAnthropicService();
        
        if ($this->option('all')) {
            // Clear common rate limit keys
            $keys = [
                'anthropic-messages-default',
                'anthropic-claude-default',
                'external-search-anthropic-default'
            ];
            
            $this->info("🧹 Clearing all common Anthropic AI rate limits...");
            
            foreach ($keys as $key) {
                $service->clearRateLimit($key);
                $this->line("✅ Cleared: {$key}");
            }
        } else {
            $key = $this->argument('key') ?? 'anthropic-messages-default';
            
            $this->info("🧹 Clearing Anthropic AI rate limits for key: {$key}");
            $service->clearRateLimit($key);
            $this->info("✅ Rate limits cleared for: {$key}");
        }
        
        $this->line("");
        $this->line("💡 Use 'php artisan anthropic:monitor-limits' to verify");
    }
}