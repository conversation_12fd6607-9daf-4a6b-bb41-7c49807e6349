<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\RateLimitedOpenAiService;

class MonitorOpenAiRateLimits extends Command
{
    protected $signature = 'openai:monitor-limits {key?}';
    protected $description = 'Monitor OpenAI rate limits for external search jobs';

    public function handle()
    {
        $key = $this->argument('key') ?? 'external-search-default';
        $service = new RateLimitedOpenAiService();
        
        $this->info("🔍 OpenAI Rate Limit Monitor");
        $this->line("Key: {$key}");
        $this->line("");
        
        $status = $service->getRateLimitStatus($key);
        
        // RPM Status
        $this->line("📊 Requests Per Minute (RPM)");
        $this->line("Current: {$status['rpm']['current']}/{$status['rpm']['limit']}");
        $this->line("Remaining: {$status['rpm']['remaining']}");
        if ($status['rpm']['available_in'] > 0) {
            $this->error("⚠️  Rate limited! Available in: {$status['rpm']['available_in']} seconds");
        } else {
            $this->info("✅ Within RPM limits");
        }
        
        $this->line("");
        
        // TPM Status  
        $this->line("🔢 Tokens Per Minute (TPM)");
        $this->line("Current: {$status['tpm']['current']}/{$status['tpm']['limit']}");
        $this->line("Remaining: {$status['tpm']['remaining']}");
        if ($status['tpm']['available_in'] > 0) {
            $this->error("⚠️  Rate limited! Available in: {$status['tpm']['available_in']} seconds");
        } else {
            $this->info("✅ Within TPM limits");
        }
        
        $this->line("");
        
        // Show percentage usage
        $rpmPercent = round(($status['rpm']['current'] / $status['rpm']['limit']) * 100, 1);
        $tpmPercent = round(($status['tpm']['current'] / $status['tpm']['limit']) * 100, 1);
        
        $this->line("📈 Usage Percentage");
        $this->line("RPM: {$rpmPercent}%");
        $this->line("TPM: {$tpmPercent}%");
        
        // Warn if approaching limits
        if ($rpmPercent > 80 || $tpmPercent > 80) {
            $this->warn("⚠️  Approaching rate limits!");
        }
        
        if ($this->option('verbose')) {
            $this->line("");
            $this->line("🔧 Rate Limit Actions");
            $this->line("Clear limits: php artisan openai:clear-limits {$key}");
            $this->line("Monitor continuously: watch -n 5 'php artisan openai:monitor-limits {$key}'");
        }
    }
}