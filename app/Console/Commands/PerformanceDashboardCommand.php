<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PerformanceDashboardCommand extends Command
{
    protected $signature = 'test:performance-dashboard 
                            {--refresh=5 : Refresh interval in seconds}
                            {--duration=300 : Duration to monitor in seconds}';

    protected $description = 'Real-time performance monitoring dashboard for succession planning jobs';

    public function handle(): int
    {
        $refreshInterval = (int) $this->option('refresh');
        $duration = (int) $this->option('duration');
        $startTime = time();

        $this->info("🖥️  Performance Dashboard - Monitoring for {$duration} seconds");
        $this->info("🔄 Refresh rate: {$refreshInterval} seconds");
        $this->info("Press Ctrl+C to exit early\n");

        while ((time() - $startTime) < $duration) {
            $this->clearScreen();
            $this->displayDashboard();
            sleep($refreshInterval);
        }

        $this->info("\n✅ Monitoring completed");
        return 0;
    }

    protected function clearScreen(): void
    {
        if (PHP_OS_FAMILY === 'Windows') {
            system('cls');
        } else {
            system('clear');
        }
    }

    protected function displayDashboard(): void
    {
        $now = Carbon::now();
        
        $this->info("╔══════════════════════════════════════════════════════════════════════════════════╗");
        $this->info("║                    📊 SUCCESSION PLAN QUEUE PERFORMANCE DASHBOARD                    ║");
        $this->info("║                              {$now->format('Y-m-d H:i:s')}                              ║");
        $this->info("╚══════════════════════════════════════════════════════════════════════════════════╝");

        // System Overview
        $this->displaySystemOverview();

        // Queue Status
        $this->displayQueueStatus();

        // Recent Job Metrics
        $this->displayRecentJobMetrics();

        // Performance Trends
        $this->displayPerformanceTrends();

        // Bottleneck Alerts
        $this->displayBottleneckAlerts();
    }

    protected function displaySystemOverview(): void
    {
        $memoryUsage = $this->formatBytes(memory_get_usage(true));
        $memoryPeak = $this->formatBytes(memory_get_peak_usage(true));
        $queueConnection = config('queue.default');
        $redisClient = config('database.redis.default.client');
        
        $this->info("\n🖥️  SYSTEM OVERVIEW:");
        $this->info("├─ Memory Usage: {$memoryUsage} (Peak: {$memoryPeak})");
        $this->info("├─ Queue Connection: {$queueConnection}");
        $this->info("└─ Redis Client: {$redisClient}");
    }

    protected function displayQueueStatus(): void
    {
        $queueSize = $this->getQueueSize();
        $failedJobs = $this->getFailedJobsCount();
        $horizonStatus = $this->getHorizonStatus();
        
        $this->info("\n⚡ QUEUE STATUS:");
        $this->info("├─ Queue Size: {$queueSize} jobs pending");
        $this->info("├─ Failed Jobs: {$failedJobs}");
        $this->info("└─ Horizon Status: {$horizonStatus}");
    }

    protected function displayRecentJobMetrics(): void
    {
        $recentMetrics = $this->getRecentJobMetrics();
        
        $this->info("\n📈 RECENT JOB METRICS (Last 10 jobs):");
        
        if (empty($recentMetrics)) {
            $this->info("└─ No recent job data available");
            return;
        }

        $avgExecutionTime = array_sum(array_column($recentMetrics, 'execution_time')) / count($recentMetrics);
        $avgMemoryUsage = array_sum(array_column($recentMetrics, 'memory_used')) / count($recentMetrics);
        $maxExecutionTime = max(array_column($recentMetrics, 'execution_time'));
        $jobsPerMinute = count($recentMetrics) > 0 ? (count($recentMetrics) / (max(array_column($recentMetrics, 'timestamp')) - min(array_column($recentMetrics, 'timestamp')))) * 60 : 0;
        
        $this->info("├─ Jobs Completed: " . count($recentMetrics));
        $this->info("├─ Avg Execution Time: " . round($avgExecutionTime, 3) . "s");
        $this->info("├─ Max Execution Time: " . round($maxExecutionTime, 3) . "s");
        $this->info("├─ Avg Memory Usage: " . $this->formatBytes($avgMemoryUsage));
        $this->info("└─ Estimated Rate: " . round($jobsPerMinute, 1) . " jobs/min");
    }

    protected function displayPerformanceTrends(): void
    {
        $trends = $this->calculatePerformanceTrends();
        
        $this->info("\n📊 PERFORMANCE TRENDS:");
        
        if (empty($trends)) {
            $this->info("└─ Insufficient data for trend analysis");
            return;
        }

        $this->info("├─ Throughput Trend: " . $this->getTrendArrow($trends['throughput_trend']) . " " . round($trends['throughput_change'], 1) . "%");
        $this->info("├─ Memory Trend: " . $this->getTrendArrow($trends['memory_trend']) . " " . round($trends['memory_change'], 1) . "%");
        $this->info("└─ Execution Time Trend: " . $this->getTrendArrow($trends['execution_trend']) . " " . round($trends['execution_change'], 1) . "%");
    }

    protected function displayBottleneckAlerts(): void
    {
        $alerts = $this->getBottleneckAlerts();
        
        $this->info("\n🚨 BOTTLENECK ALERTS:");
        
        if (empty($alerts)) {
            $this->info("└─ ✅ No bottlenecks detected");
            return;
        }

        foreach ($alerts as $alert) {
            $this->info("├─ {$alert}");
        }
    }

    protected function getQueueSize(): int
    {
        try {
            return DB::connection('redis')->command('llen', ['queues:default']) ?? 0;
        } catch (\Exception $e) {
            return -1;
        }
    }

    protected function getFailedJobsCount(): int
    {
        try {
            return DB::table('failed_jobs')->count();
        } catch (\Exception $e) {
            return -1;
        }
    }

    protected function getHorizonStatus(): string
    {
        try {
            // Check if Horizon is running by looking for recent metrics
            $horizonConfig = config('horizon.environments.' . app()->environment());
            $maxProcesses = $horizonConfig['supervisor-1']['maxProcesses'] ?? 3;
            
            return "Active ({$maxProcesses} workers)";
        } catch (\Exception $e) {
            return "Unknown";
        }
    }

    protected function getRecentJobMetrics(): array
    {
        $cacheKeys = Cache::get('perf_test_job_keys', []);
        $recentMetrics = [];
        
        // Get last 10 job metrics
        $recentKeys = array_slice($cacheKeys, -10);
        
        foreach ($recentKeys as $key) {
            $metrics = Cache::get($key);
            if ($metrics && isset($metrics['timestamp'])) {
                $metrics['timestamp'] = Carbon::parse($metrics['timestamp'])->timestamp;
                $recentMetrics[] = $metrics;
            }
        }
        
        // Sort by timestamp
        usort($recentMetrics, function($a, $b) {
            return $a['timestamp'] - $b['timestamp'];
        });
        
        return $recentMetrics;
    }

    protected function calculatePerformanceTrends(): array
    {
        $recentMetrics = $this->getRecentJobMetrics();
        
        if (count($recentMetrics) < 4) {
            return [];
        }

        $halfPoint = floor(count($recentMetrics) / 2);
        $firstHalf = array_slice($recentMetrics, 0, $halfPoint);
        $secondHalf = array_slice($recentMetrics, $halfPoint);

        $firstAvgThroughput = count($firstHalf) / (max(array_column($firstHalf, 'timestamp')) - min(array_column($firstHalf, 'timestamp')) + 1);
        $secondAvgThroughput = count($secondHalf) / (max(array_column($secondHalf, 'timestamp')) - min(array_column($secondHalf, 'timestamp')) + 1);

        $firstAvgMemory = array_sum(array_column($firstHalf, 'memory_used')) / count($firstHalf);
        $secondAvgMemory = array_sum(array_column($secondHalf, 'memory_used')) / count($secondHalf);

        $firstAvgExecution = array_sum(array_column($firstHalf, 'execution_time')) / count($firstHalf);
        $secondAvgExecution = array_sum(array_column($secondHalf, 'execution_time')) / count($secondHalf);

        return [
            'throughput_trend' => $secondAvgThroughput > $firstAvgThroughput ? 'up' : 'down',
            'throughput_change' => $firstAvgThroughput > 0 ? (($secondAvgThroughput - $firstAvgThroughput) / $firstAvgThroughput) * 100 : 0,
            'memory_trend' => $secondAvgMemory > $firstAvgMemory ? 'up' : 'down',
            'memory_change' => $firstAvgMemory > 0 ? (($secondAvgMemory - $firstAvgMemory) / $firstAvgMemory) * 100 : 0,
            'execution_trend' => $secondAvgExecution > $firstAvgExecution ? 'up' : 'down',
            'execution_change' => $firstAvgExecution > 0 ? (($secondAvgExecution - $firstAvgExecution) / $firstAvgExecution) * 100 : 0,
        ];
    }

    protected function getBottleneckAlerts(): array
    {
        $alerts = [];
        $recentMetrics = $this->getRecentJobMetrics();
        
        if (empty($recentMetrics)) {
            return $alerts;
        }

        // Check queue size
        $queueSize = $this->getQueueSize();
        if ($queueSize > 100) {
            $alerts[] = "🟡 High queue backlog: {$queueSize} jobs pending";
        }

        // Check execution time
        $avgExecutionTime = array_sum(array_column($recentMetrics, 'execution_time')) / count($recentMetrics);
        if ($avgExecutionTime > 30) {
            $alerts[] = "🔴 Slow job execution: " . round($avgExecutionTime, 1) . "s average";
        }

        // Check memory usage
        $avgMemoryUsage = array_sum(array_column($recentMetrics, 'memory_used')) / count($recentMetrics);
        if ($avgMemoryUsage > 128 * 1024 * 1024) { // 128MB
            $alerts[] = "🟡 High memory usage: " . $this->formatBytes($avgMemoryUsage) . " per job";
        }

        // Check failed jobs
        $failedJobs = $this->getFailedJobsCount();
        if ($failedJobs > 10) {
            $alerts[] = "🔴 High failure rate: {$failedJobs} failed jobs";
        }

        return $alerts;
    }

    protected function getTrendArrow($trend): string
    {
        return $trend === 'up' ? '📈' : '📉';
    }

    protected function formatBytes($bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}