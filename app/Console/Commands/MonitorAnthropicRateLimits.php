<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\RateLimitedAnthropicService;

class MonitorAnthropicRateLimits extends Command
{
    protected $signature = 'anthropic:monitor-limits {key?}';
    protected $description = 'Monitor Anthropic AI rate limits for Claude API calls';

    public function handle()
    {
        $key = $this->argument('key') ?? 'anthropic-messages-default';
        $service = new RateLimitedAnthropicService();
        
        $this->info("🔍 Anthropic AI Rate Limit Monitor");
        $this->line("Key: {$key}");
        $this->line("");
        
        $status = $service->getRateLimitStatus($key);
        
        // RPM Status
        $this->line("📊 Requests Per Minute (RPM)");
        $this->line("Current: {$status['rpm']['current']}/{$status['rpm']['limit']}");
        $this->line("Remaining: {$status['rpm']['remaining']}");
        if ($status['rpm']['available_in'] > 0) {
            $this->error("⚠️  Rate limited! Available in: {$status['rpm']['available_in']} seconds");
        } else {
            $this->info("✅ Within RPM limits");
        }
        
        $this->line("");
        
        // ITPM Status  
        $this->line("🔢 Input Tokens Per Minute (ITPM)");
        $this->line("Current: {$status['itpm']['current']}/{$status['itpm']['limit']}");
        $this->line("Remaining: {$status['itpm']['remaining']}");
        if ($status['itpm']['available_in'] > 0) {
            $this->error("⚠️  Rate limited! Available in: {$status['itpm']['available_in']} seconds");
        } else {
            $this->info("✅ Within ITPM limits");
        }
        
        $this->line("");
        
        // OTPM Status  
        $this->line("📤 Output Tokens Per Minute (OTPM)");
        $this->line("Current: {$status['otpm']['current']}/{$status['otpm']['limit']}");
        $this->line("Remaining: {$status['otpm']['remaining']}");
        if ($status['otpm']['available_in'] > 0) {
            $this->error("⚠️  Rate limited! Available in: {$status['otpm']['available_in']} seconds");
        } else {
            $this->info("✅ Within OTPM limits");
        }
        
        $this->line("");
        
        // Show percentage usage
        $rpmPercent = round(($status['rpm']['current'] / $status['rpm']['limit']) * 100, 1);
        $itpmPercent = round(($status['itpm']['current'] / $status['itpm']['limit']) * 100, 1);
        $otpmPercent = round(($status['otpm']['current'] / $status['otpm']['limit']) * 100, 1);
        
        $this->line("📈 Usage Percentage");
        $this->line("RPM: {$rpmPercent}%");
        $this->line("ITPM: {$itpmPercent}%");
        $this->line("OTPM: {$otpmPercent}%");
        
        // Warn if approaching limits
        if ($rpmPercent > 80 || $itpmPercent > 80 || $otpmPercent > 80) {
            $this->warn("⚠️  Approaching rate limits!");
        }
        
        if ($this->option('verbose')) {
            $this->line("");
            $this->line("🔧 Rate Limit Actions");
            $this->line("Clear limits: php artisan anthropic:clear-limits {$key}");
            $this->line("Monitor continuously: watch -n 5 'php artisan anthropic:monitor-limits {$key}'");
        }
    }
}