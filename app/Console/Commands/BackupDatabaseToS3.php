<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class BackupDatabaseToS3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:database';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backs up the database and uploads it to S3';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dbName = env('DB_DATABASE');
        $dbUser = env('DB_USERNAME');
        $dbPassword = env('DB_PASSWORD');
        $backupPath = storage_path('app/backups');
        $timestamp = now()->format('Y-m-d_H-i-s');
        $backupFile = "$backupPath/{$dbName}_{$timestamp}.sql";

        // Set backup file path using timestamp for unique naming
        $this->info('Preparing database backup...');

        // Create the backup directory if it doesn't exist
        if (!file_exists($backupPath)) {
            mkdir($backupPath, 0755, true);
        }

        // Execute the mysqldump command
        $command = sprintf(
            'mysqldump --user=%s --password=%s %s > %s',
            escapeshellarg($dbUser),
            escapeshellarg($dbPassword),
            escapeshellarg($dbName),
            escapeshellarg($backupFile)
        );

        $output = null;
        $returnVar = null;
        exec($command, $output, $returnVar);

        if ($returnVar !== 0) {
            $this->error('Database backup failed!');
            return;
        }



        // Log backup success (without sensitive credentials)
        $this->info('Database backup completed successfully');
        $this->info('Backup file: ' . $backupFile);

    }
}
