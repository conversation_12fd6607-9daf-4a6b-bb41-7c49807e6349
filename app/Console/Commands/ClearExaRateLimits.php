<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AI\RateLimitedExaService;

class ClearExaRateLimits extends Command
{
    protected $signature = 'exa:clear-limits {key?} {--all}';
    protected $description = 'Clear Exa AI rate limits for testing or recovery';

    public function handle()
    {
        $service = new RateLimitedExaService();
        
        if ($this->option('all')) {
            // Clear common rate limit keys
            $keys = [
                'exa-search-default',
                'external-search-exa-default',
                'exa-contents-default'
            ];
            
            $this->info("🧹 Clearing all common Exa AI rate limits...");
            
            foreach ($keys as $key) {
                $service->clearRateLimit($key);
                $this->line("✅ Cleared: {$key}");
            }
        } else {
            $key = $this->argument('key') ?? 'exa-search-default';
            
            $this->info("🧹 Clearing Exa AI rate limits for key: {$key}");
            $service->clearRateLimit($key);
            $this->info("✅ Rate limits cleared for: {$key}");
        }
        
        $this->line("");
        $this->line("💡 Use 'php artisan exa:monitor-limits' to verify");
    }
}