<?php

namespace App\Listeners;

use Illuminate\Queue\Events\JobFailed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use App\Models\User;
use App\Notifications\JobFailedNotification;

class NotifyAdminsOfJobFailure
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \Illuminate\Queue\Events\JobFailed  $event
     * @return void
     */
    public function handle(JobFailed $event)
    {
        try {
            // Get only the specific development email user
            $developmentUser = User::where('email', '<EMAIL>')->first();

            if (!$developmentUser) {
                Log::warning('Development user not found to notify about job failure via event listener', [
                    'job' => $event->job->resolveName(),
                    'exception' => $event->exception->getMessage(),
                    'expected_email' => '<EMAIL>'
                ]);
                return;
            }

            // Extract job data from the event
            $jobData = $this->extractJobDataFromEvent($event);
            
            // Create and send notification to development user only
            $notification = new JobFailedNotification(
                $event->job->resolveName(),
                $event->exception,
                $jobData,
                [
                    'queue' => $event->connectionName,
                    'failed_at' => now()->toDateTimeString(),
                    'source' => 'event_listener'
                ]
            );

            $developmentUser->notify($notification);

            Log::info('Development team notification sent for job failure via event listener', [
                'job' => $event->job->resolveName(),
                'notified_email' => $developmentUser->email,
                'queue' => $event->connectionName
            ]);

        } catch (\Exception $notificationException) {
            // Don't let notification failures affect the main job failure handling
            Log::error('Failed to notify admins about job failure via event listener', [
                'job' => $event->job->resolveName(),
                'original_exception' => $event->exception->getMessage(),
                'notification_exception' => $notificationException->getMessage()
            ]);
        }
    }

    /**
     * Extract job data from the JobFailed event
     *
     * @param JobFailed $event
     * @return array
     */
    protected function extractJobDataFromEvent(JobFailed $event): array
    {
        try {
            $rawBody = $event->job->getRawBody();
            $payload = json_decode($rawBody, true);
            
            if (!$payload || !isset($payload['data']['command'])) {
                return [];
            }
            
            // Try to unserialize the job command to get the job data
            $command = unserialize($payload['data']['command']);
            
            // Extract planData if it exists and is accessible
            if (property_exists($command, 'planData')) {
                try {
                    // Use reflection to access protected property
                    $reflection = new \ReflectionClass($command);
                    $planDataProperty = $reflection->getProperty('planData');
                    $planDataProperty->setAccessible(true);
                    $planData = $planDataProperty->getValue($command);
                    
                    if (is_array($planData)) {
                        return $planData;
                    }
                } catch (\Exception $e) {
                    // If reflection fails, continue with empty data
                    Log::debug('Could not extract planData from job using reflection', [
                        'job' => get_class($command),
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            return [];
            
        } catch (\Exception $e) {
            Log::warning('Failed to extract job data from JobFailed event', [
                'job' => $event->job->resolveName(),
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }
}
