<?php

namespace App\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Events\PlanCreated;
use App\Models\SuccessionPlan;

class SendPlanResponse
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PlanCreated $event)
    {
        // You can notify the user or store the result somewhere
        // Example: Cache the created plan for later retrieval
        cache()->put("plan_{$event->plan->id}", $event->plan, 600); // Store for 10 min
    }
}
