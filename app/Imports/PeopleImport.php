<?php

namespace App\Imports;

use App\Models\People;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class PeopleImport implements  WithHeadingRow
{

    public function array(array $array)
    {
        return $array;
    }

    // public function collection(Collection $rows)
    // {
    //     $duplicates = [];
    //     foreach ($rows as $row) {
    //         // Adjust the keys based on your Excel file structure
    //         $forename = $row['forename'];
    //         $surname = $row['surname'];
    //         $otherName = $row['other_name'] ?? null;
    //         $gender = $row['gender'];
    //         $city = $row['city'];
    //         $country = $row['country'];
    //         $linkedinURL = $row['linkedinurl'];
    //         $role = $row['role'];
    //         $startDate = $row['start_date'];
    //         $companyName = $row['company_name'];

    //         // Check if a record with the given forename and surname exists
    //         $existingRecord = People::where('forename', $forename)
    //             ->where('surname', $surname)
    //             ->get();

    //         if ($existingRecord->isNotEmpty()) {
    //             // Handle duplicates
    //             $duplicates["$forename-$surname-$role-$companyName"] = (object) [
    //                 'forename'    => $forename,
    //                 'surname'     => $surname,
    //                 'other_name'  => $otherName,
    //                 'gender'      => $gender,
    //                 'linkedinURL' => $linkedinURL,
    //                 'latest_role' => $role,
    //                 'companyName' => $companyName,
    //                 'duplicate'   => json_decode(json_encode($existingRecord->toArray()), false)
    //             ];
    //         } else {
    //             // Handle company creation if not exists
    //             $companyid = DB::table('companies')->where('name', $companyName)->value('id');

    //             if ($companyid === null) {
    //                 $CreatedCompany = Company::create([
    //                     'name'          => $companyName,
    //                     'location_id'   =>  1784674685,
    //                     'status'        => 'submitted'
    //                 ]);
    //             } else {
    //                 $CreatedCompany = Company::find($companyid);
    //             }

    //             // Calculate tenure
    //             $startDate = Carbon::parse($startDate);
    //             $currentDate = Carbon::now();
    //             $tenure = $currentDate->diffInYears($startDate);
    //             $startDate = $startDate->format('Y-m-d');

    //             // Create new People record
    //             $people = People::create([
    //                 'forename'      => $forename,
    //                 'surname'       => $surname,
    //                 'latest_role'   => $role,
    //                 'gender'        => $gender,
    //                 'company_id'    => $CreatedCompany->id,
    //                 'company_name'  => $CreatedCompany->name,
    //                 'start_date'    => $startDate,
    //                 'country'       => $country,
    //                 'city'          => $city,
    //                 'linkedinURL'   => $linkedinURL,
    //                 'tenure'        => $tenure,
    //                 'exco'          => "Non Exco",
    //                 'status'        => 'submitted',
    //                 // Assuming you have a user_id field in your People table
    //                 'user_id'       => auth()->id(), // Use the currently authenticated user ID
    //             ]);

    //             // Create associated pipeline record
    //             Pipeline::create([
    //                 'id'                 => $this->pipelineid,
    //                 'plan_id'            => $this->plan,
    //                 'user_id'            => auth()->id(), // Use the currently authenticated user ID
    //                 'people_id'          => $people->id,
    //                 'first_name'         => $people->forename,
    //                 'last_name'          => $people->surname,
    //                 'gender'             => $people->gender,
    //                 'diverse'            => "NA",
    //                 'location'           => $people->country,
    //                 'summary'            => $people->summary,
    //                 'country'            => $people->country,
    //                 'city'               => $people->city,
    //                 'linkedinURL'        => $people->linkedinURL,
    //                 'latest_role'        => $people->latest_role,
    //                 'company_id'         => $people->company_id,
    //                 'company_name'       => $people->company_name,
    //                 'start_date'         => $people->start_date,
    //                 'readiness'          => $people->readiness,
    //                 'tenure'             => $tenure,
    //                 'seniority'          => "NA",
    //                 'exco'               => "Non Exco",
    //                 'career_history'     => "NA",
    //                 'educational_history' => "NA",
    //                 'skills'             => "NA",
    //                 'languages'          => "NA",
    //                 'skills_match'       => 0,
    //                 'education_match'    => 0,
    //                 'location_match'     => 1,
    //                 'role_match'         => 1,
    //                 'gender_match'       => 1,
    //                 'tenure_match'       => 1,
    //                 'total_score'        => 0 + 0 + 0 + 1 + 1 + 1,
    //                 'people_type'        => "External-User"
    //             ]);
    //         }
    //     }

    //     // Handle duplicates or success
    //     if (count($duplicates) > 0) {
    //         $this->duplicateRecords = (object) $duplicates;
    //         $this->duplicateUploadPopup = true;
    //         Log::info(json_encode($this->duplicateRecords));
    //         return;
    //     } else {
    //         $this->hasCandidatesUploaded = true;
    //     }
    // }
}
