<?php

namespace App\Jobs\Traits;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use App\Models\User;
use App\Notifications\JobFailedNotification;

/**
 * ExponentialBackoffRetry Trait
 * 
 * Implements exponential backoff retry logic for Laravel queue jobs
 * Following course materials patterns for retry handling
 */
trait ExponentialBackoffRetry
{
    /**
     * Base delay in seconds for first retry attempt
     */
    protected $baseDelay = 30;

    /**
     * Multiplier for exponential backoff calculation
     */
    protected $backoffMultiplier = 2;

    /**
     * Maximum number of retry attempts
     */
    protected $maxRetries = 5;

    /**
     * Maximum delay in seconds to prevent extremely long waits
     */
    protected $maxDelay = 1800; // 30 minutes

    /**
     * Maximum time window for retries in minutes
     */
    protected $maxRetryTime = 120; // 2 hours

    /**
     * Whether to add jitter to prevent thundering herd
     */
    protected $jitterEnabled = false;

    /**
     * Calculate exponential backoff delays
     * Returns array of delays in seconds for each retry attempt
     */
    public function backoff(): array
    {
        $delays = [];
        $baseDelay = max(1, $this->baseDelay); // Minimum 1 second
        $maxRetries = max(1, $this->maxRetries); // Minimum 1 retry
        
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            $delay = $baseDelay * pow($this->backoffMultiplier, $attempt - 1);
            
            // Cap delay at maximum
            $delay = min($delay, $this->maxDelay);
            
            // Add jitter if enabled (±25% randomization)
            if ($this->jitterEnabled) {
                $jitterRange = $delay * 0.25;
                $delay = $delay + mt_rand(-$jitterRange, $jitterRange);
                $delay = max(1, $delay); // Ensure positive delay
            }
            
            $delays[] = (int) $delay;
        }
        
        return $delays;
    }

    /**
     * Set the retry until timestamp
     * Jobs will not be retried after this time
     */
    public function retryUntil(): Carbon
    {
        return now()->addMinutes($this->maxRetryTime);
    }

    /**
     * Enhanced failed method with backoff logging
     */
    public function failed(?\Throwable $exception): void
    {
        $logData = [
            'job' => static::class,
            'exception' => $exception ? $exception->getMessage() : 'Unknown error',
            'max_attempts' => $this->tries ?? $this->maxRetries,
            'backoff_delays' => $this->backoff(),
            'retry_until' => $this->retryUntil()->toDateTimeString()
        ];

        // Add context data if available
        if (property_exists($this, 'planData') && isset($this->planData['plan_id'])) {
            $logData['plan_id'] = $this->planData['plan_id'];
        }
        
        if (property_exists($this, 'user') && is_object($this->user) && isset($this->user->id)) {
            $logData['user_id'] = $this->user->id;
        } elseif (property_exists($this, 'userId')) {
            $logData['user_id'] = $this->userId ?? 'unknown';
        }

        Log::error('Job failed after all retry attempts with exponential backoff', $logData);

        // Notify admin users about the job failure
        $this->notifyAdminsOfJobFailure($exception);
    }

    /**
     * Notify admin users when a job fails after all retry attempts
     *
     * @param ?\Throwable $exception
     * @return void
     */
    protected function notifyAdminsOfJobFailure(?\Throwable $exception): void
    {
        try {
            // Get only the specific development email user
            $developmentUser = User::where('email', '<EMAIL>')->first();
            
            if (!$developmentUser) {
                Log::warning('Development user not found to notify about job failure', [
                    'job' => static::class,
                    'exception' => $exception ? $exception->getMessage() : 'Unknown error',
                    'expected_email' => '<EMAIL>'
                ]);
                return;
            }

            // Prepare job data for notification
            $jobData = [];
            if (property_exists($this, 'planData') && is_array($this->planData)) {
                $jobData = $this->planData;
            }

            // Create and send notification to development user only
            $notification = new JobFailedNotification(
                static::class,
                $exception ?: new \Exception('Unknown job failure'),
                $jobData,
                [
                    'retry_attempts' => $this->tries ?? $this->maxRetries,
                    'backoff_delays' => $this->backoff(),
                    'failed_at' => now()->toDateTimeString()
                ]
            );

            $developmentUser->notify($notification);

            Log::info('Development team notification sent for job failure', [
                'job' => static::class,
                'notified_email' => $developmentUser->email,
                'plan_id' => $jobData['plan_id'] ?? null
            ]);

        } catch (\Exception $notificationException) {
            // Don't let notification failures affect the main job failure handling
            Log::error('Failed to notify admins about job failure', [
                'job' => static::class,
                'original_exception' => $exception ? $exception->getMessage() : 'Unknown error',
                'notification_exception' => $notificationException->getMessage()
            ]);
        }
    }

    /**
     * Log retry attempt information
     */
    protected function logRetryAttempt(): void
    {
        $currentAttempt = $this->attempts() ?? 1;
        $delays = $this->backoff();
        $nextDelay = $delays[$currentAttempt - 1] ?? 0;

        Log::warning('Job retry attempt with exponential backoff', [
            'job' => static::class,
            'attempt' => $currentAttempt,
            'max_attempts' => $this->tries ?? $this->maxRetries,
            'next_delay' => $nextDelay . ' seconds',
            'backoff_strategy' => [
                'base_delay' => $this->baseDelay,
                'multiplier' => $this->backoffMultiplier,
                'max_delay' => $this->maxDelay,
                'jitter_enabled' => $this->jitterEnabled
            ],
            'plan_id' => $this->planData['plan_id'] ?? 'unknown'
        ]);
    }

    // Setter methods for test customization

    public function setBaseDelay(int $seconds): self
    {
        $this->baseDelay = $seconds;
        return $this;
    }

    public function setBackoffMultiplier(float $multiplier): self
    {
        $this->backoffMultiplier = $multiplier;
        return $this;
    }

    public function setMaxRetries(int $retries): self
    {
        $this->maxRetries = $retries;
        $this->tries = $retries; // Also set Laravel's tries property
        return $this;
    }

    public function setMaxDelay(int $seconds): self
    {
        $this->maxDelay = $seconds;
        return $this;
    }

    public function setMaxRetryTime(int $minutes): self
    {
        $this->maxRetryTime = $minutes;
        return $this;
    }

    public function enableJitter(bool $enabled): self
    {
        $this->jitterEnabled = $enabled;
        return $this;
    }
}