<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Controllers\AiChatbot; // Import your controller
use App\Jobs\Traits\ExponentialBackoffRetry;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\pipeline; // Import pipeline model for direct insertion

class ExternalSearchPerplexityJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ExponentialBackoffRetry;

    // Set a longer timeout for this specific job
    public $timeout = 2400; // 40 minutes for external operations

    // Retry configuration for external API operations
    public $tries = 5;

    protected $planData;
    protected $user;

    /**
     * Create a new job instance.
     */
    public function __construct( $planData, $user)
    {
        $this->planData = $planData;
        $this->user = $user;
        
        // Set queue for external operations
        $this->onQueue('external_search');
        
        // Configure exponential backoff for external operations
        $this->setBaseDelay(60); // 1 minute base
        $this->setMaxDelay(1800); // 30 minutes max
        $this->setMaxRetryTime(240); // 4 hours total window
        $this->enableJitter(true); // Prevent thundering herd with external APIs
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        // Check if this is a follow-up to internal search
        $isFollowUp = isset($this->planData['internal_search_complete']) && $this->planData['internal_search_complete'] === true;
        $internalCandidateCount = $isFollowUp ? ($this->planData['internal_candidates_count'] ?? 0) : 0;

        if ($isFollowUp) {
            Log::info("SEARCH FLOW: External search starting after internal search with {$internalCandidateCount} candidates");
        }

        Log::info("EXTERNAL SEARCH: Starting search for plan '" . ($this->planData['plan_name'] ?? 'Unnamed Plan') . "'");

        // Check if Python external search is enabled
        $usePythonSearch = config('ai.python.enable_external_search', true);
        
        if ($usePythonSearch) {
            // Use the new Python-based implementation
            Log::info("EXTERNAL SEARCH: Using Python-based external search implementation");
            $externalSearch = new \App\Services\AI\PythonExternalSearch();
            $candidates = $externalSearch->searchExternalCandidates($this->planData, $this->user);
        } else {
            // Use the original PHP implementation
            Log::info("EXTERNAL SEARCH: Using PHP-based external search implementation");
            $AiChatbot = new AiChatbot();
            $candidates = $AiChatbot->ExternalSearchPerplexity($this->planData, $this->user);
            
            // The Python implementation handles database insertion internally
            // For the PHP implementation, we need to insert the candidates into the pipeline
            if (!empty($candidates)) {
                $this->insertCandidatesIntoPipeline($candidates);
            }
        }

        // Log completion with detailed information
        $externalCandidateCount = count($candidates);
        $executionTime = round(microtime(true) - LARAVEL_START, 2);
        
        // Standard completion log
        Log::info("EXTERNAL SEARCH: Completed search with " . $externalCandidateCount . " candidates found");
        
        // Detailed job completion log in daily file
        Log::channel('daily')->info("┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓");
        Log::channel('daily')->info("┃ LARAVEL JOB COMPLETED: ExternalSearchPerplexityJob                             ┃");
        Log::channel('daily')->info("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛");
        Log::channel('daily')->info("🔎 SEARCH JOB SUMMARY", [
            'plan_name' => $this->planData['plan_name'] ?? 'Unknown Plan',
            'plan_id' => $this->planData['plan_id'] ?? 'Unknown ID',
            'candidates_found' => $externalCandidateCount,
            'execution_time' => $executionTime . ' seconds',
            'search_type' => $usePythonSearch ? 'Python Implementation' : 'PHP Implementation',
            'target_roles' => $this->planData['target_roles'] ?? [],
            'user_id' => $this->user->id,
            'user_email' => $this->user->email,
            'timestamp' => now()->toDateTimeString()
        ]);

        if (!empty($candidates)) {
            Log::info("SEARCH FLOW: Combined pipeline now has approximately " .
                     ($internalCandidateCount + $externalCandidateCount) .
                     " candidates (" . $internalCandidateCount . " internal, " .
                     $externalCandidateCount . " external)");

            // The Python implementation already handles notifications,
            // but we only need to create a notification for the PHP implementation
            if (!$usePythonSearch) {
                // Generate notification for PHP implementation
                $notificationData = [
                    'user_id' => $this->user->id,
                    'created_at' => now(),
                ];
                DB::table('job_queues_notification')->insert($notificationData);
            }

            // Log example candidates (up to 3)
            $this->logExampleCandidates($candidates, $usePythonSearch);
        } else {
            Log::warning("EXTERNAL SEARCH: No candidates found for plan '" .
                        ($this->planData['plan_name'] ?? 'Unnamed Plan') . "'");

            // Only generate notification for PHP implementation
            if (!$usePythonSearch) {
                $notificationData = [
                    'user_id' => $this->user->id,
                    'created_at' => now(),
                ];
                DB::table('job_queues_notification')->insert($notificationData);
            }
        }
    }
    
    /**
     * Log example candidates for debugging
     * 
     * @param array $candidates The candidates to log
     * @param bool $isPythonFormat Whether the candidates are in Python format
     */
    protected function logExampleCandidates($candidates, $isPythonFormat)
    {
        $logLimit = min(count($candidates), 3);
        
        for ($i = 0; $i < $logLimit; $i++) {
            if ($isPythonFormat) {
                // Python format
                $name = $candidates[$i]['name'] ?? 'Unknown';
                $role = $candidates[$i]['role'] ?? 'No title';
                $company = $candidates[$i]['company'] ?? 'Unknown company';
                $url = $candidates[$i]['url'] ?? 'No URL';
                $score = $candidates[$i]['score'] ?? 0;
                
                Log::info("EXTERNAL CANDIDATE: {$name} - {$role} at {$company}", [
                    'linkedin_url' => $url,
                    'score' => $score
                ]);
            } else {
                // PHP format
                if (!empty($candidates[$i][0]['profile']['full_name'])) {
                    $skills = implode(", ", array_slice($candidates[$i][0]['profile']['skills'] ?? [], 0, 3));

                    Log::info("EXTERNAL CANDIDATE: " . $candidates[$i][0]['profile']['full_name'] . " - " .
                           ($candidates[$i][0]['profile']['occupation'] ?? 'No title') . " at " .
                           ($candidates[$i][0]['profile']['experiences'][0]['company'] ?? 'Unknown company'), [
                               'skills' => $skills,
                               'linkedin_url' => $candidates[$i][0]['linkedin_profile_url'] ?? 'No URL'
                           ]);
                }
            }
        }
    }

    /**
     * Insert external candidates into the pipeline table
     *
     * @param array $candidates Array of candidate profiles from ExternalSearchPerplexity
     * @return void
     */
    protected function insertCandidatesIntoPipeline($candidates)
    {
        // Import the pipeline model
        $pipelineData = [];

        foreach ($candidates as $candidate) {
            if (empty($candidate[0]['profile'])) {
                continue;
            }

            $profile = $candidate[0]['profile'];
            $experiences = $profile['experiences'] ?? [];
            $education = $profile['education'] ?? [];

            // Get first name and last name
            $firstName = $profile['first_name'] ?? '';
            $lastName = $profile['last_name'] ?? '';

            // Create entry for pipeline table
            $pipelineEntry = [
                'plan_id'            => $this->planData['plan_id'],
                'user_id'            => $this->user->id,
                'people_id'          => 0, // External candidates don't have a people_id
                'first_name'         => $firstName,
                'last_name'          => $lastName,
                'middle_name'        => null,
                'other_name'         => null,
                'gender'             => null, // Often not provided by LinkedIn
                'diverse'            => null,
                'location'           => $profile['country'] ?? '',
                'country'            => $profile['country'] ?? '',
                'city'               => $profile['city'] ?? '',
                'summary'            => $profile['summary'] ?? '',
                'linkedinURL'        => $candidate[0]['linkedin_profile_url'] ?? '',
                'latest_role'        => $profile['occupation'] ?? '',
                'company_id'         => 0, // External companies don't have a company_id
                'company_name'       => !empty($experiences) ? ($experiences[0]['company'] ?? '') : '',
                'start_date'         => null,
                'end_date'           => null,
                'tenure'             => 0,
                'function'           => null,
                'division'           => null,
                'seniority'          => null,
                'exco'               => 'Non Exco',
                'career_history'     => null,
                'educational_history' => null,
                'skills'             => implode(', ', $profile['skills'] ?? []),
                'languages'          => implode(', ', $profile['languages'] ?? []),
                'skills_match'       => 0.5, // Default score for external candidates
                'education_match'    => 0,
                'location_match'     => 1,
                'role_match'         => 1,
                'readiness'          => 'Ready',
                'other_tags'         => null,
                'gender_match'       => 0,
                'tenure_match'       => 1,
                'total_score'        => 2.5, // Default total score
                'people_type'        => 'External-AI', // Mark as coming from AI search
            ];

            $pipelineData[] = $pipelineEntry;
        }

        // Insert into pipeline table if we have candidates
        if (!empty($pipelineData)) {
            Log::info("EXTERNAL PIPELINE: Inserting " . count($pipelineData) . " external candidates into pipeline");

            try {
                // Import the pipeline model class
                $pipelineModel = new \App\Models\pipeline();

                // Insert candidates in chunks
                foreach (array_chunk($pipelineData, 50) as $chunk) {
                    $pipelineModel->insert($chunk);
                }

                Log::info("EXTERNAL PIPELINE: Successfully inserted external candidates");
            } catch (\Exception $e) {
                Log::error("EXTERNAL PIPELINE: Failed to insert candidates", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
    }
}