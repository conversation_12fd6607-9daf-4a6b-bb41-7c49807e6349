<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Mail\NewadminEmail;
use App\Jobs\Traits\ExponentialBackoffRetry;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Throwable;

class SendNewAdminEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ExponentialBackoffRetry;

    // Timeout for email operations
    public int $timeout = 300; // 5 minutes for email sending
    
    // Retry configuration for email operations
    public int $tries = 3;

    protected string $adminEmail;
    protected string $inviteUrl;
    protected string $name;

    /**
     * Create a new job instance.
     *
     * @param string $adminEmail
     * @param string $inviteUrl
     * @param string $name
     */
    public function __construct(string $adminEmail, string $inviteUrl, string $name)
    {
        $this->adminEmail = $adminEmail;
        $this->inviteUrl = $inviteUrl;
        $this->name = $name;

        // Configure exponential backoff for email operations
        $this->setBaseDelay(30); // 30 seconds base delay
        $this->setMaxDelay(300); // 5 minutes max delay
        $this->setMaxRetryTime(60); // 1 hour total retry window
    }

    /**
     * Execute the job.
     * Following course guidelines for defensive programming
     */
    public function handle(): void
    {
        Log::info("EMAIL JOB: Sending new admin invitation email", [
            'email' => $this->adminEmail,
            'name' => $this->name
        ]);

        try {
            Mail::to($this->adminEmail)->send(new NewadminEmail($this->inviteUrl, $this->name));
            
            Log::info("EMAIL JOB: Admin invitation email sent successfully", [
                'email' => $this->adminEmail,
                'name' => $this->name
            ]);
        } catch (\Exception $e) {
            Log::error("EMAIL JOB: Failed to send admin invitation email", [
                'email' => $this->adminEmail,
                'name' => $this->name,
                'error' => $e->getMessage()
            ]);
            
            // Re-throw to trigger job failure handling
            throw $e;
        }
    }

    /**
     * Handle job failure (course Lesson 6 requirement)
     * 
     * @param Throwable|null $exception
     * @return void
     */
    public function failed(?Throwable $exception): void
    {
        Log::error('EMAIL JOB: New admin email job failed after all retry attempts', [
            'job' => static::class,
            'admin_email' => $this->adminEmail,
            'name' => $this->name,
            'exception' => $exception?->getMessage(),
            'max_attempts' => $this->tries,
            'backoff_delays' => $this->backoff(),
            'retry_until' => $this->retryUntil()->toDateTimeString(),
            'trace' => $exception?->getTraceAsString()
        ]);

        // Could notify system administrators about failed admin invitations
        // This is critical since it affects user onboarding
    }

    /**
     * Set retry until timestamp (course recommendation)
     * 
     * @return Carbon
     */
    public function retryUntil(): Carbon
    {
        return now()->addHour(); // 1 hour retry window for email operations
    }
}
