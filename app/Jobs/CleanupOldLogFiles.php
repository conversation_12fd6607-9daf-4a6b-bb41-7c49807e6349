<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CleanupOldLogFiles implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $logPath = storage_path('logs');
        $cutoffDate = now()->subDays(30);

        $files = glob($logPath.'/laravel-*.log');

        foreach ($files as $file) {
            $fileDate = filemtime($file);

            if ($fileDate < $cutoffDate->timestamp) {
                unlink($file);
            }
        }
    }
}
