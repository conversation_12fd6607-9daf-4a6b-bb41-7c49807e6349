<?php

namespace App\Jobs;

use App\Services\AI\RateLimitedExaService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessExaRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $backoff = 60; // 1 minute between retries

    protected $payload;

    protected $rateLimitKey;

    protected $endpoint;

    /**
     * Create a new job instance.
     */
    public function __construct(array $payload, string $rateLimitKey, string $endpoint = 'search')
    {
        $this->payload = $payload;
        $this->rateLimitKey = $rateLimitKey;
        $this->endpoint = $endpoint;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $service = new RateLimitedExaService;

        Log::info('Processing queued Exa request', [
            'rate_limit_key' => $this->rateLimitKey,
            'endpoint' => $this->endpoint,
            'payload_size' => strlen(json_encode($this->payload)),
        ]);

        // Call the appropriate method based on endpoint
        $result = match ($this->endpoint) {
            'search' => $service->search($this->payload, $this->rateLimitKey),
            'contents' => $service->getContents($this->payload, $this->rateLimitKey),
            'findSimilarLinks' => $service->findSimilar($this->payload, $this->rateLimitKey),
            default => throw new \InvalidArgumentException("Unknown endpoint: {$this->endpoint}")
        };

        if ($result === null || $result === false) {
            Log::warning('Queued Exa request failed', [
                'rate_limit_key' => $this->rateLimitKey,
                'endpoint' => $this->endpoint,
                'result' => $result,
            ]);

            // Throw exception to trigger retry mechanism
            throw new \Exception('Exa request failed with result: '.var_export($result, true));
        }

        Log::info('Queued Exa request completed successfully', [
            'rate_limit_key' => $this->rateLimitKey,
            'endpoint' => $this->endpoint,
        ]);
    }

    /**
     * Handle a job failure.
     *
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('ProcessExaRequest job failed permanently', [
            'rate_limit_key' => $this->rateLimitKey,
            'endpoint' => $this->endpoint,
            'error' => $exception->getMessage(),
            'payload' => $this->payload,
        ]);
    }
}