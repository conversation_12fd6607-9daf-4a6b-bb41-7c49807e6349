<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\SuccessionPlan;
use App\Models\SuccessRequirements;
use App\Models\Pipeline;
use App\Models\Notifications;
use App\Models\People;
use App\Models\InternalPeople;
use App\Models\CareerHistories;
use App\Models\Skills;
use Illuminate\Support\Facades\Log;
use App\Models\Account;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;



class CreateSuccessionPlan implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $planData;
    protected $user;

    /**
     * Create a new job instance.
     *
     * @param array $planData
     * @param mixed $user
     */
    public function __construct($planData, $user)
    {
        $this->planData = $planData;
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
                // Add the plan details to the plans table
                $this->planData1 = [
                    'name'               => $this->planData['plan_name'],
                    'description'        => $this->planData['description'],
                    'minimum_Experience' => $this->planData['minimum_tenure'],
                    'step_up'            => "1",
                    'ethnicity'          => $this->planData['is_ethnicity_important'],
                    'tagged_individual'  => $this->planData['minimum_tenure'],
                    'age'                => 0,
                    'status'             => "Draft",
                    'candidate_status'   => "No Changes",
                    'user_id'            => $this->user->id,
                    "shared_with"        => "[]"
                ];

                $plan = SuccessionPlan::create($this->planData1);

                $this->planData['plan_id'] = $plan->id;

                // Create the notification that a plan has been created
                notifications::create([
                    'type'              => "Plan_Created",
                    'plan_id'           => $plan->id,
                    'entity_name'       => $this->planData['plan_name'],
                    'description'       => $this->planData['description'],
                    'user_id'           => $this->user->id,
                    'user_company'      => $this->user->company_id
                ]);

                 //------------------  Success Requirements Table ---------------//

                // Roles are seperated by commas this expands them into a list ** this field is mandatory **
                // To store the target role
                //Log::info($this->planData['acronyms']);
                $successRequirementsData = [];

                // Adding target roles
                $target_roles = $this->planData['target_roles'];
                foreach ($target_roles as $role) {
                    $successRequirementsData[] = [
                        'plan_id' => $plan->id,
                        'name'    => trim($role),
                        'type'    => 'Role',
                    ];
                }

                // Adding step-up candidates
                if  ($this->planData['step_up_candidates'] != ['none']) {
                    $step_up_candidates = $this->planData['step_up_candidates'];
                    foreach ($step_up_candidates as $stepUpCandidate) {
                        $successRequirementsData[] = [
                            'plan_id' => $plan->id,
                            'name'    => trim($stepUpCandidate),
                            'type'    => 'step_up',
                        ];
                    }
                }
                /*
                $step_up_candidates = $this->planData['step_up_candidates'];
                foreach ($step_up_candidates as $stepUpCandidate) {
                    $successRequirementsData[] = [
                        'plan_id' => $plan->id,
                        'name'    => trim($stepUpCandidate),
                        'type'    => 'step_up',
                    ];
                }
                */

                // Adding tenure requirement
                $minimum_tenure = $this->planData['minimum_tenure'];
                if ($minimum_tenure) {
                    $successRequirementsData[] = [
                        'plan_id' => $plan->id,
                        'name'    => $minimum_tenure,
                        'type'    => 'Minimum_Tenure',
                    ];
                }

                // Adding location requirements
                if ($this->planData['country']!= ['none']) {
                    $countries = $this->planData['country'];
                    foreach ($countries as $country){
                            $successRequirementsData[] = [
                                'plan_id' => $plan->id,
                                'name'    => $country,
                                'type'    => 'location',
                            ];
                    }

                }
                
                // Adding professional skills
                $skills = $this->planData['skills'];
                foreach ($skills as $skill) {
                    $successRequirementsData[] = [
                        'plan_id' => $plan->id,
                        'name'    => trim($skill),
                        'type'    => 'professional_skill',
                    ];
                }
                
                /*
                // Adding educational qualifications
                $qualificationArr = $this->planData['qualifications'];
                foreach ($qualificationArr as $qualification) {
                    $successRequirementsData[] = [
                        'plan_id' => $plan->id,
                        'name'    => trim($qualification),
                        'type'    => 'education',
                    ];
                }
                */

                // Adding gender requirement
                if ($this->planData['gender'] != "Not Required") {
                    $successRequirementsData[] = [
                        'plan_id' => $plan->id,
                        'name'    => trim($this->planData['gender']),
                        'type'    => 'Gender',
                    ];
                }

                // Adding company requirements
                if ($this->planData['companies'] != ['none']) {
                    $companiesArr = $this->planData['companies'];
                    foreach ($companiesArr as $cr) {
                        $successRequirementsData[] = [
                            'plan_id' => $plan->id,
                            'name'    => trim($cr),
                            'type'    => 'Company',
                        ];
                    }
                }

                // Batch insert all records into the database
                SuccessRequirements::insert($successRequirementsData);

                Log::info("Moving onto the filtering");
                //See if a gender was chosen
                $gen = '';
                if ($this->planData['gender'] == 'Female') {
                    $gen = 'Female';
                } elseif ($this->planData['gender'] == 'Male') {
                    $gen = 'Male';
                }

                Log::info("Identified the target gender");

                //------------------------ Make the pipeline table -------------------//

                //---- Get the data ready for the scoring ---//

                // Roles list

                $rawRoles = is_array($this->planData['target_roles']) ? $this->planData['target_roles'] : [];
                $proles = array_map('trim', $rawRoles);
                $proles = array_merge($proles, $this->planData['alternative_roles_titles']);

                // Step-up List
                if ($this->planData['step_up_candidates'] !=['none']){
                    $rawsteps = is_array($this->planData['step_up_candidates']) ? $this->planData['step_up_candidates'] : [];
                    $psteps = array_map('trim', $rawsteps);
                }
                else{
                    $rawsteps  = [];
                    $psteps = [];
                }

                /*
                $rawKeyword = is_array($this->planData['keyword']) ? $this->planData['keyword'] : [];
                $pKeyword = array_map('trim', $rawKeyword);
                */

                // Qualifcications list
                $rawEdQual = is_array($this->planData['qualifications']) ? $this->planData['qualifications'] : [];
                $pEdQual = array_map('trim', $rawEdQual);

                // Location list
                $ploc = is_array($this->planData['country']) ? $this->planData['country'] : [$this->planData['country']];

                // Skills List
                $rawskill = is_array($this->planData['skills']) ? $this->planData['skills'] : [];
                $pskills = array_map('trim', $rawskill);
                $skillCount = count($pskills);
                //Log::info($skillCount);
                
                //Log::info($this->planData['country']);
                //--------------- Start the filtering for the pipeline --------------//
                if (!in_array('none', $this->planData['country'])){
                    $scountry = is_array($this->planData['country']) ? $this->planData['country'] : [$this->planData['country']];
                }
                else{
                    $scountry = [];
                }
                //Log::info($scountry);
                
                $scities   = "";

                Log::info("Everything is an array");

                // Getting the array for the companies using the companies, sector and industry 
                $filteredCompanyIdsArr = $this->planData['companies'];

                // Filter the people table using target roles,
                if (!empty($proles)) {
                    $filteredPeople = People::query()
                        ->where('status', '!=', 'Submitted')
                        ->where('status', '!=', 'Reported')
                        ->where('company_id','!=',$this->user->company_id)
                        ->where(function ($query) use ($proles, $gen, $scountry, $this->planData) {
                            $query->where(function ($subquery) use ($proles) {
                                // Loop through each keyword and add an orWhere clause
                                foreach ($proles as $prole) {
                                    $subquery->orWhere('latest_role', 'like', '%' . $prole . '%');
                                }
                            });
                            if ($gen != '') {
                                $query->where('gender', $gen);
                            }
                            if ($this->planData['companies']!= ['none']) {
                                $query->whereIn('company_name', $this->planData['companies']);
                            }
                            if (!empty($scountry)) {
                                $query->whereIn('country', $scountry);
                            }   
                        })
                        ->get();
                    Log::info($filteredPeople);

                    $filteredPeople = $filteredPeople->map(function ($item) {
                        $item['role_score'] = 1;
                        $item['type'] = "External-System";
                        return $item;
                    });
                } else {
                    $filteredPeople = new Collection();
                }                       

                $filteredPeopleidslvl1 = $filteredPeople->pluck('id');

                /* If a step up candidate has been proposed the system will only search for step candidates as well ensuring that
                people already identified from roles do not come through and uses the same filtering logic as roles
                */
                if ($rawsteps !== []) {
                    $sfilteredPeople = People::query()
                        ->where('status', '!=', 'Submitted')
                        ->where('status', '!=', 'Reported')
                        ->where('company_id','!=',$this->user->company_id)
                        ->whereNotIn('id', $filteredPeopleidslvl1)
                        ->when($rawsteps !== [], function ($query) use ($psteps, $gen, $scountry, $scities, $this->planData) {
                            $query->where(function ($subquery) use ($psteps) {
                                // Loop through each keyword and add an orWhere clause
                                foreach ($psteps as $pstep) {
                                    $subquery->orWhere('latest_role', 'like', '%' . $pstep . '%');
                                }
                            });
                            if ($gen != '') {
                                $query->where('gender', $gen);
                            }
                            if ($this->planData['companies']!= ['none']) {
                                $query->WhereIn('company_name', $this->planData['companies']);
                            }
                            // if (!empty($this->interestedCompaniesarray)){
                            //     $query->WhereIn('company_id',$this->interestedCompaniesarray);
                            // }
                            if (!empty($scountry)) {
                                $query->WhereIn('country', $scountry);
                            }
                            
                        })
                        ->get();

                    $sfilteredPeople = $sfilteredPeople->map(function ($item) {
                        $item['role_score'] = 0.75;
                        $item['type'] = "External-System";
                        return $item;
                    });

                    Log::info($sfilteredPeople);

                    $sfilteredPeopleidslvl1 = $sfilteredPeople->pluck('id');
                } else {
                    $sfilteredPeople = [];
                    $sfilteredPeopleidslvl1 = [];
                }

                // Filter the internal people table
                $infilteredPeople = InternalPeople::query()
                ->where('company_id','=',$this->user->company_id)
                ->where('forename' ,'!=', "Vacant")
                ->when($proles !== [], function ($query) use ($proles, $gen, $scountry, $scities) {
                    $query->where(function ($subquery) use ($proles) {
                        // Loop through each keyword and add an orWhere clause
                        foreach ($proles as $prole) {
                            $subquery->orWhere('latest_role', 'like', '%' . $prole . '%');
                        }
                    });
                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($scountry)) {
                        $query->whereIn('country', $scountry);
                    }
                    if (!empty($scities)) {
                        $query->whereIn('city', $scities);
                    }
                })
                ->get(); 
            //dd(DB::getQueryLog());
            //dd($infilteredPeople);
            $infilteredPeople = $infilteredPeople->map(function ($item) {
                $item['role_score'] = 1;
                $item['type'] = "Internal-System";
                return $item;
            });

            $infilteredPeopleidslvl1 = $infilteredPeople->pluck('id');

                  // Filter for internal_step_ups
            if ($rawsteps !== []) {
                $insfilteredPeople = InternalPeople::query()
                    ->where('company_id','=',$this->user->company_id)
                    ->where('forename', '!=', "Vacant")
                    ->whereNotIn('id', $infilteredPeopleidslvl1)
                    ->when($rawsteps !== [], function ($query) use ($psteps, $gen, $scountry, $scities, $filteredCompanyIdsArr) {
                        $query->where(function ($subquery) use ($psteps) {
                            // Loop through each keyword and add an orWhere clause
                            foreach ($psteps as $pstep) {
                                $subquery->orWhere('latest_role', 'like', '%' . $pstep . '%');
                            }
                        });
                        if ($gen != '') {
                            $query->where('gender', $gen);
                        }
                        if (!empty($scountry)) {
                            $query->WhereIn('country', $scountry);
                        }
                        if (!empty($scities)) {
                            $query->WhereIn('city', $scities);
                        }
                    })
                    ->get();

                $insfilteredPeople = $insfilteredPeople->map(function ($item) {
                    $item['role_score'] = 0.75;
                    $item['type'] = "Internal-System";
                    return $item;
                });

                //dd($sfilteredPeople);
            } else {
                $insfilteredPeople = [];
            }
                // Accronym
                $pKeyword = [];
                
                //Log::info("Filtering For career History");
                if (!empty($pKeyword)) {
                    $kfilteredPeople = People::query()
                        ->where('status', '!=', 'Submitted')
                        ->where('status', '!=', 'Reported')
                        ->where('company_id','!=',$this->user->company_id)
                        ->whereNotIn('id', $filteredPeopleidslvl1)
                        ->whereNotIn('id', $sfilteredPeopleidslvl1)
                        ->when($pKeyword !== "", function ($query) use ($pKeyword, $gen, $scountry, $scities, $this->planData) {
                            $query->where(function ($subquery) use ($pKeyword) {
                                // Loop through each keyword and add an orWhere clause
                                foreach ($pKeyword as $keyword) {
                                    $subquery->orWhere('latest_role', 'like', '%' . $keyword . '%');
                                }
                            });

                            if ($gen != '') {
                                $query->where('gender', $gen);
                            }
                            if ($this->planData['companies']!= ['none']) {
                                $query->whereIn('company_name', $this->planData['companies']);
                            }
                            // if (!empty($this->interestedCompaniesarray)){
                            //     $query->WhereIn('company_id',$this->interestedCompaniesarray);
                            // }
                            if (!empty($scountry)) {
                                $query->whereIn('country', $scountry);
                            }
                            
                        })
                        ->get();
                    $kfilteredPeople = $kfilteredPeople->map(function ($item) {
                        $item['role_score'] = 0.25;
                        $item['type'] = "External-System";
                        return $item;
                    });

                    //dd($kfilteredPeople);

                    $kfilteredPeopleidslvl1 = $kfilteredPeople->pluck('id');
                } else {
                    $kfilteredPeople = [];
                    $kfilteredPeopleidslvl1 = [];
                } 

                // May be relevant candidates
                $career_history_filtered = CareerHistories::query()
                ->whereNotIn('people_id', $filteredPeopleidslvl1)
                ->whereNotIn('people_id', $sfilteredPeopleidslvl1)
                ->whereNotIn('people_id', $kfilteredPeopleidslvl1)
                ->where(function ($query) use ($proles, $psteps) {
                    $query->WhereIn('role', $proles)
                        ->orWhereIn('role', $psteps);
                })
                ->get();

                // First, get the relevant IDs from $career_history_filtered
                $filteredCareerHistoryIds = $career_history_filtered->pluck('people_id');

                if ($filteredCareerHistoryIds !== null) {
                    // Then, use these IDs to filter the people table
                    $careerPeople = People::query()
                        ->whereIn('id', $filteredCareerHistoryIds)
                        ->where('company_id','!=',$this->user->company_id)
                        ->when($filteredCareerHistoryIds  !== null, function ($query) use ($proles, $gen, $scountry, $scities, $this->planData) {
                            if ($gen != '') {
                                $query->where('gender', $gen);
                            }
                            if (!empty($scountry)) {
                                $query->WhereIn('country', $scountry);
                            }
                            if (!empty($this->planData['companies'])) {
                                $query->WhereIn('company_name', $this->planData['companies']);
                            }

                            // if (!empty($this->interestedCompaniesarray)){
                            //     $query->WhereIn('company_id',$this->interestedCompaniesarray);
                            // }

                        })
                        ->get();

                    $careerPeople = $careerPeople->map(function ($item) {
                        $item['role_score'] = 0.5;
                        $item['type'] = "External-System";
                        return $item;
                    });

                    $filteredPeople = $filteredPeople->concat($sfilteredPeople);
                    $filteredPeople = $filteredPeople->concat($infilteredPeople);
                    $filteredPeople = $filteredPeople->concat($insfilteredPeople);
                    $filteredPeople = $filteredPeople->concat($careerPeople);
                    $filteredPeople = $filteredPeople->concat($kfilteredPeople);
                } else {
                    $filteredPeople = $filteredPeople->concat($sfilteredPeople);
                    $filteredPeople = $filteredPeople->concat($infilteredPeople);
                    $filteredPeople = $filteredPeople->concat($insfilteredPeople);
                }
                //Log::info("Starting the calculations");
                //------------------------ Calculate Education Score  ------------------//

                // Query to join a filtered People table with the education requirements and summarise to get the count
                $educationCounts = People::query()
                    ->whereIn('people.educational_history', $pEdQual)
                    ->join('people as filtered_people', 'people.id', '=', 'filtered_people.id')
                    ->groupBy('people.id')
                    ->selectRaw('people.id, COUNT(filtered_people.id) as education_match_count')
                    ->get();

                // Create a map of education match counts indexed by person ID
                $educationMatchCountMap = $educationCounts->pluck('education_match_count', 'id');

                // Left join to add the education_match_count column to filtered_people records
                $filteredPeopleWithEducationCount = $filteredPeople->map(function ($person) use ($educationMatchCountMap) {
                    $person->education_match_count = $educationMatchCountMap->get($person->id, 0);
                    return $person;
                });

                //Log::info("Starting the Location Score");
                //------------------------- Calculate Location Score ---------------------//

                // Query to join the new filtered education People table with the loccation
                if (!empty($this->selectedCountries)) {

                    $filteredPeopleWithEdLocMatches = $filteredPeople->whereIn('country', $ploc);

                    // Join the location match counts with the filteredPeopleWithEducationCount collection
                    $filteredPeopleWithEdLocMatches = $filteredPeopleWithEducationCount->map(function ($person) {
                        $person->location_match_count = 1;
                        return $person;
                    });
                } else {
                    $filteredPeopleWithEdLocMatches = $filteredPeopleWithEducationCount->map(function ($person) {
                        $person->location_match_count = 1;
                        return $person;
                    });
                }

                //------------------------------ Calculate Skill Score -----------------------------//
                //Log::info("Starting the Skill Scoring");
                // Query to join a filtered people table with the skill requirements and summarise to 
                $skillsScoreCount = Skills::query()
                ->whereIn('skill_name', $pskills)
                ->groupBy('people_id')
                ->selectRaw('skills.people_id, COUNT(skills.people_id) / ? as skill_score', ['skillCount' => $skillCount])
                ->get();

                // Create a map of skill match score indexed by the person ID
                $skillsScoreMap = $skillsScoreCount->pluck('skill_score', 'people_id');

                // Join the skill score map with the filteredPeoplewithEdLocMatches collection
                $filteredPeopleWithMatches = $filteredPeopleWithEdLocMatches->map(function ($person) use ($skillsScoreMap) {
                    $person->skill_score = $skillsScoreMap->get($person->id, 0);
                    return $person;
                });

                //--------------------------------------- Gender ------------------------------------//
                //Log::info("Starting the Gender Match");
                //Log::info($this->planData['gender']);
                // Calculate and assign gender score
                $filteredPeopleWithMatches->each(function ($person) use ($this->planData) {
                    $person->gender_score = ($person->gender === $this->planData['gender']) ? 1 : 0;
                });
                //Log::info($filteredPeopleWithMatches);
                //Log::info("Starting the Tenure calculation");
                // Calculate and assign tenancy score
                if ($this->planData['minimum_tenure'] !== null) {
                    $filteredPeopleWithMatches = $filteredPeopleWithMatches->map(function ($person) use ($this->planData) {
                        $tenancyDifference = $person->tenure - $this->planData['minimum_tenure'];
                        $tenancyDifferenceAbsolute = abs($tenancyDifference);

                        if ($tenancyDifference < 0) {
                            if ($tenancyDifferenceAbsolute <= 2) {
                                $person->tenancy_score = 0;
                            } else {
                                $person->tenancy_score = 1;
                            }
                        } else {
                            $person->tenancy_score = 1;
                        }
                        return $person;
                    });

                    //$filteredPeopleWithMatches = $filteredPeopleWithMatches->where('tenancy_score','=',1);
                } else {
                    $filteredPeopleWithMatches = $filteredPeopleWithMatches->map(function ($person) {
                        $person->tenancy_score = 1;
                        return $person;
                    });
                }

                // dd($filteredPeopleWithMatches);
                //Log::info("Tenure calculation complete");

                //------------------------------  Make all the tables --------------------------------//

                // Put that data into the successionpeople list
                $dataToInsert = $filteredPeopleWithMatches->map(function ($person) use ($plan, $this->user) {
                    return [
                        'plan_id'            => $plan->id,
                        'user_id'            => $this->user->id,
                        'people_id'          => $person->id,
                        'first_name'         => $person->forename,
                        'last_name'          => $person->surname,
                        'middle_name'        => $person->middle_name,
                        'other_name'         => $person->other_name,
                        'gender'             => $person->gender,
                        'diverse'            => $person->diverse,
                        'location'           => $person->country,
                        'country'            => $person->country,
                        'city'               => $person->city,
                        'summary'            => $person->summary,
                        'linkedinURL'        => $person->linkedinURL,
                        'latest_role'        => $person->latest_role,
                        'company_id'         => $person->company_id,
                        'company_name'       => $person->company_name,
                        'start_date'         => $person->start_date,
                        'end_date'           => $person->end_date,
                        'tenure'             => $person->tenure,
                        'function'           => $person->function,
                        'division'           => $person->division,
                        'seniority'          => $person->seniority,
                        'exco'               => $person->exco,
                        'career_history'     => $person->career_history,
                        'educational_history' => $person->educational_history,
                        'skills'             => $person->skills,
                        'languages'          => $person->languages,
                        'skills_match'       => $person->skill_score,
                        'education_match'    => 0,
                        'location_match'     => $person->location_match_count,
                        'role_match'         => $person->role_score,
                        'readiness'          => $person->readiness,
                        'other_tags'         => $person->other_tags,
                        'gender_match'       => $person->gender_score,
                        'tenure_match'       => $person->tenancy_score,
                        'total_score'        => $person->skill_score + $person->education_match_count + $person->location_match_count + $person->role_score + $person->tenancy_score,
                        'people_type'        => $person->type,
                    ];
                })->toArray(); // Convert to a plain array
                
                if (!empty($dataToInsert)) {
                    // Break the data into chunks of 1000 (adjust the chunk size as needed)
                    foreach (array_chunk($dataToInsert, 1000) as $chunk) {
                        pipeline::insert($chunk);
                    }
                }


                // $this->newSkillData = [
                //     'qualifications' => [],
                //     'skills' => [],
                //     'targetRoles' => [],
                //     'stepUpCandidate' => [],
                //     'keyword'         => []
                // ];
                // $this->step++;
                // // dd($this->ethnicity);
                // $this->plan = $plan;
                // $this->open = false;

                
            Log::info('Succession plan created successfully in background', [
                'plan_id' => $plan->id,
                'user_id' => $this->userId
            ]);

        } catch (\Exception $e) {
            Log::error('Error in CreateSuccessionPlanJob', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}