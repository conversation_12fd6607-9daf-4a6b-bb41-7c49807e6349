<?php

namespace App\Jobs;

use App\Services\AI\RateLimitedOpenAiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessOpenAiRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $backoff = 60; // 1 minute between retries

    protected $payload;

    protected $rateLimitKey;

    /**
     * Create a new job instance.
     */
    public function __construct(array $payload, string $rateLimitKey)
    {
        $this->payload = $payload;
        $this->rateLimitKey = $rateLimitKey;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $service = new RateLimitedOpenAiService;

        Log::info('Processing queued OpenAI request', [
            'rate_limit_key' => $this->rateLimitKey,
            'payload_size' => strlen(json_encode($this->payload)),
        ]);

        $result = $service->chatCompletion($this->payload, $this->rateLimitKey);

        if ($result === null || $result === false) {
            Log::warning('Queued OpenAI request failed', [
                'rate_limit_key' => $this->rateLimitKey,
                'result' => $result,
            ]);

            // Throw exception to trigger retry mechanism
            throw new \Exception('OpenAI request failed with result: '.var_export($result, true));
        }

        Log::info('Queued OpenAI request completed successfully', [
            'rate_limit_key' => $this->rateLimitKey,
        ]);
    }

    /**
     * Handle a job failure.
     *
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('ProcessOpenAiRequest job failed permanently', [
            'rate_limit_key' => $this->rateLimitKey,
            'error' => $exception->getMessage(),
            'payload' => $this->payload,
        ]);
    }
}
