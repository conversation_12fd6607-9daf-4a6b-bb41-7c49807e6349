<?php

namespace App\Jobs;

use App\Jobs\Traits\ExponentialBackoffRetry;
use App\Services\AI\InternalPeopleSearch;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SearchInternalPeopleJob implements ShouldQueue
{
    use Dispatchable, ExponentialBackoffRetry, InteractsWithQueue, Queueable, SerializesModels;

    // Retry configuration for internal database operations
    public $tries = 3;

    protected $planData;

    protected $user;

    /**
     * Create a new job instance.
     */
    public function __construct($planData, $user)
    {
        $this->planData = $planData;
        $this->user = $user;

        // Set queue for internal operations
        $this->onQueue('internal_search');

        // Configure exponential backoff for internal operations
        $this->setBaseDelay(15); // 15 seconds base
        $this->setMaxDelay(300); // 5 minutes max
        $this->setMaxRetryTime(30); // 30 minutes total window
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Use the new InternalPeopleSearch service
            $internalSearchService = new InternalPeopleSearch();
            $result = $internalSearchService->searchInternalCandidates($this->planData, $this->user);
            
        } catch (\Exception $e) {
            Log::error('INTERNAL SEARCH: Job failed', [
                'plan_id' => $this->planData['plan_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e; // Re-throw to trigger job retry mechanism
        }
    }
}