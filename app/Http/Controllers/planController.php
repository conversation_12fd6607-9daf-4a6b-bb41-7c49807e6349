<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\SuccessRequirements;
use App\Models\People;
use App\Models\pipeline;
use App\Models\notifications;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class planController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
    
        return view('Plans.index', compact('user'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Plans.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {   

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = auth()->user();
        $plan = SuccessionPlan::findOrFail($id);


        return view('Plans.show', compact('plan','user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $plan = SuccessionPlan::findOrFail($id);
        $plan->delete();

        return redirect()->route('plan.index');

    }
}
