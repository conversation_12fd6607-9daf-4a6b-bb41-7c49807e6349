<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\SuccessRequirements;
use App\Models\People;
use App\Models\pipeline;
use App\Models\notifications;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AplanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // This code will check if a request or filter has been clicked otherwise it will return all the plans
        
        // The query to get the status from the request
        //$status = $request->input('status');

        // The query that will pull the plans based on the status chosen
        $plans = SuccessionPlan::all();

        $peopleCountsData = DB::table('pipelines')
        ->select('plan_id', DB::raw('COUNT(*) as total_people'))
        ->groupBy('plan_id')
        ->get();
    
        $peopleCounts = collect($peopleCountsData)->keyBy('plan_id');
        
        $femaleCountsData = DB::table('pipelines')
            ->select('plan_id', DB::raw('SUM(gender = "female") as total_females'))
            ->groupBy('plan_id')
            ->get();
        
        $femaleCounts = collect($femaleCountsData)->keyBy('plan_id');
        
        $avgTenureMatchesData = DB::table('pipelines')
            ->select('plan_id', DB::raw('AVG(tenure_match) as avg_tenure_match'))
            ->groupBy('plan_id')
            ->get();
        
        $avgTenureMatches = collect($avgTenureMatchesData)->keyBy('plan_id');
        
        $plans = $plans->map(function ($plan) use ($peopleCounts, $femaleCounts, $avgTenureMatches) {
            $plan->total_people = $peopleCounts->get($plan->id)->total_people ?? 0;
            $plan->total_females = $femaleCounts->get($plan->id)->total_females ?? 0;
            $plan->diversity_score = $plan->total_people > 0 ?
                $plan->total_females / $plan->total_people : 0;
            $plan->avg_tenure_match = $avgTenureMatches->get($plan->id)->avg_tenure_match ?? 0;
            return $plan;
        });
    
        return response()->json(['plans' => $plans]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
