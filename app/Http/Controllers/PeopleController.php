<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use OpenAI\Laravel\Facades\OpenAI;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\SuccessRequirements;
use App\Models\People;
use App\Models\pipeline;
use App\Models\notifications;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PeopleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request,string $id)
    {   
        $user = auth()->user();
        $plan = SuccessionPlan::findOrFail($id);
        
        // Get the IDs of people associated with the success plan
        //$successPeopleIds = $plan->successPeople->pluck('people_id')->toArray();

        /*

        $query = People::query();

        // Exclude people who are associated with the success plan
        $query->whereNotIn('id', $successPeopleIds);

        $people = $query->get();
        */

        return view('people.index', ['plan'=> $plan, 'user' => $user]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $plan)
    {
        $plan = SuccessionPlan::findOrFail($plan);
        $pipelineId = pipeline::where('plan_id', $plan->id)->value('id');
        $user = auth()->user();

        //Getting the new requirements for the plan
    
        $roles = explode(',', $request->input('roles'));
        foreach ($roles as $role) {
            $role = trim($role);

        // Create the requirements for the succession plan
        SuccessRequirements::create([
            'plan_id'     => $plan->id,
            'name'        => $role,
            'type'        => "Role"
            ]);
        }

        // Getting any Educational Qualifications Requirements
        if ($request->has('education')) {
            $educations = explode(',', $request->input('education'));
            foreach ($educations as $education) {
                $education = trim($education);
                SuccessRequirements::create([
                    'plan_id'     => $plan->id,
                    'name'        => $education,
                    'type'        => "education"
                    ]);
            }
        }

        // Getting any location requirements
        if ($request->has('location')) {
            $locations = explode(',', $request->input('location'));
            foreach ($locations as $location) {
                $location = trim($location);
                SuccessRequirements::create([
                        'plan_id'     => $plan->id,
                        'name'        => $location,
                        'type'        => "location"
                        ]);
            }
        }

        // Getting the people in the succession plan
        $selectedItemIds = $request->input('selected_items', []);


        // Fetch the associated fields for the selected item_ids from the pipeline table
        $selectedItemsData = People::whereIn('id', $selectedItemIds)->get();

        foreach ($selectedItemsData as $itemData) {
            
            // Generate the headline for the AI
            $roleai = $itemData->latest_role;
            $locationai = $itemData->location_id;
            $tenureai = $itemData->tenure;
            $companyai = $itemData->company_name;
            $skillsai = $itemData->skills;

            $inputArray = [
                'role'     => $roleai,
                'location' =>$locationai,
                'tenure'   =>$tenureai,
                'company'  =>$companyai,
                'skills'   =>$skillsai,
            ];

            // Create a formatted message for GPT-3
            $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
            foreach ($inputArray as $key => $value) {
                    $message .= "$key: \"$value\"\n";
                        }
            
            // Call GPT-3 to generate the headline
            $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
                        ]);
            
            $generatedHeadline = $response->choices[0]->message->content;

            SuccessPeople::create([
                'pipeline_id'        => $pipelineId,
                'plan_id'            => $plan->id,
                'user_id'            => $user->id,
                'people_id'          => $itemData->id,
                'first_name'         => $itemData->first_name,
                'last_name'          => $itemData->last_name,
                'middle_name'        => $itemData->middle_name,
                'other_name'         => $itemData->other_name,
                'gender'             => $itemData->gender,
                'diverse'            => $itemData->diverse,
                'location_id'        => $itemData->location_id,
                'linkedinURL'        => $itemData->linkedinURL,
                'latest_role'        => $itemData->latest_role,
                'company_id'         => $itemData->company_id,
                'company_name'       => $itemData->company_name,
                'start_date'         => $itemData->start_date,
                'end_date'           => $itemData->end_date,
                'tenure'             => $itemData->tenure,
                'function'           => $itemData->function,
                'division'           => $itemData->division,
                'seniority'          => $itemData->seniority,
                'career_history'     => $itemData->career_history,
                'educational_history'=> $itemData->educational_history,
                'skills'             => $itemData->skills,
                'languages'          => $itemData->languages,
                'skills_match'       => "0",
                'education_match'    => "0",
                'location_match'     => "0",
                'role_match'         => "1",
                'gender_match'       => "0",
                'tenure_match'       => "0",
                'headline'           => $generatedHeadline,
                'type'               => "External"
            ]);

            //Create the notification that the person has been added to a plan
             notifications::create([
                'type'              => "External_Added_to_plan",
                'people_id'         => $itemData->id,
                'plan_id'           => $plan->id,
                'entity_name'       => $itemData->first_name,
                'entity_company'    => $itemData->company_name,
                'description'       => "NA",
                'user_id'           => $user->id,
                'user_company'      => "1"
            ]);
        }



    
        $peopleForPlan = SuccessPeople::where('plan_id', $plan->id)->get();
       
        // Get the IDs of the people for the left join
        $peopleIds = $peopleForPlan->pluck('people_id');

        //Updated the match fields now

        $educationRequirements = SuccessRequirements::where('plan_id', $plan->id)
        ->where('type', 'education')
        ->pluck('name');

        $educationCounts = People::query()
        ->join('people as filtered_people', 'people.id', '=', 'filtered_people.id')
        ->whereIn('filtered_people.educational_history', $educationRequirements)
        ->whereIn('filtered_people.id', $peopleIds)
        ->groupBy('people.id')
        ->selectRaw('people.id, COUNT(filtered_people.id) as education_match_count')
        ->get();

        // Create a map of education match counts indexed by person ID
        $educationMatchCountMap = $educationCounts->pluck('education_match_count', 'id');

        // Update education_match attribute for each person in $peopleForPlan
        foreach ($peopleForPlan as $person) {
            $person->education_match = $educationMatchCountMap->get($person->people_id, 0);
            $person->save();
        }

        $locationRequirements = SuccessRequirements::where('plan_id', $plan->id)
        ->where('type', 'location')
        ->pluck('name');
    
        $locationCounts = People::query()
        ->join('people as filtered_people', 'people.id', '=', 'filtered_people.id')
        ->whereIn('filtered_people.location_id', $locationRequirements)
        ->whereIn('filtered_people.id', $peopleIds)
        ->groupBy('people.id')
        ->selectRaw('people.id, COUNT(filtered_people.id) as location_match_count')
        ->get();

        // Create a map of location match counts indexed by person ID
        $locationMatchCountMap = $locationCounts->pluck('location_match_count', 'id');
        
        foreach ($peopleForPlan as $person) {
            $person->location_match = $locationMatchCountMap->get($person->people_id, 0);
            $person->save();
        }


        
        return redirect()->route('plan.success_people.index', ['plan' => $plan])->with('success', 'Selected items saved successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
