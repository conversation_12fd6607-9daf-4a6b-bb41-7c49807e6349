<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\NewuserEmail;
use App\Models\Invite;


class NewuserController extends Controller
{
    public function sendInvite(Request $request, $email, $id, $company, $role){

        //dd($role);

        $invite = Invite::where([
            'email' => $email,
            'user_id' => $id,
            'company_id' => $company,
            'url' => url()->full()
        ])->first();
        
        if (!$request->hasValidSignature() || ($invite->used ?? false)) {
            return view('expire');
        }
        $user = auth()->user();

        return view('sign_up', compact('email', 'id', 'company','role'));
    }

    public function createuser(){

        return view('auth.login');
    }

    public function adminInvite(Request $request, $data){

        $data = json_decode(base64_decode($data),true);
        $data['url'] = url()->full();
        $invite = Invite::where([
            'email' => $data['email'],
            'user_id' => $data['id'],
            'company_id' => $data['company'],
            'url' => url()->full()

        ])->first();
        
        if (!$request->hasValidSignature() || ($invite->used ?? false)) {
            return view('expire');
        }
        $user = auth()->user();

        return view('admin_sign_up', compact('data'));
    }
}
