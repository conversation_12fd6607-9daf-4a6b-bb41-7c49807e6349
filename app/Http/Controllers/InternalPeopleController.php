<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\InternalPeople;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;


class InternalPeopleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        
        $user = auth()->user();
        $plans = SuccessionPlan::where('user_id', $user->id)->get();

        $internalpeople = InternalPeople::query()
        ->get();

        return view('internalpeople.index', compact('internalpeople','user'));
    }

    public function getPaginatedInternalPeoples(Request $request) {
        $requestStartTime = now()->toDateTimeString();
        $page = $request->input('page', 1);
        $perPage = 100; 
        $user = auth()->user();
        $query = $request->input('query'); // Get the search query parameter
        
        // Try to get from cache first for performance
        $cacheKey = 'paginated_people_' . $user->id . '_' . $page . '_' . md5($query ?: '');
        if (\Cache::has($cacheKey) && !$request->input('force_refresh')) {
            return \Cache::get($cacheKey);
        }
    
        // Build the query - optimize with selective columns and strict eager loading
        $itemsQuery = InternalPeople::select([
            'id', 'employee_id', 'forename', 'surname', 'gender', 'latest_role', 'location',
            'flight_risk', 'is_relocatable', 'company_id', 'company_name', 'readiness', 'potential',
            'performance', 'linkedinURL', 'function', 'division'
        ])
        ->with([
            'careerHistory:id,people_id,role,past_company_id,start_date,end_date,tenure',
            'careerHistory.company:id,name',
            'internalSkills:id,skill_name,internal_people,skill_type',
            'plan' => function ($planQuery) use ($user) {
                $planQuery->select('id', 'name', 'user_id')->where('user_id', $user->id);
            }
        ])
        ->where('company_id', $user->company_id)
        ->where('forename', '!=', 'Vacant')
        ->orderBy('updated_at', 'desc'); // Order by most recently updated
    
        // Apply search filter if query is provided
        if (!empty($query)) {
            $itemsQuery->where(function ($subQuery) use ($query) {
                $subQuery->where('forename', 'like', '%' . $query . '%')
                         ->orWhere('surname', 'like', '%' . $query . '%')
                         ->orWhereRaw("CONCAT(forename, ' ', surname) LIKE ?", ['%' . $query . '%']);
            });
        }
    
        // Paginate the filtered results
        $items = $itemsQuery->paginate($perPage, ['*'], 'page', $page);

         // Enhance each item in the paginated results
        $enhancedItems = $items->items();
        foreach ($enhancedItems as $item) {
            $user = auth()->user();
            $planIds = SuccessPeople::where(['people_id' => $item->id, 'type' => 'Internal'])->pluck('plan_id')->toArray();
            $item->internalUserPlans = SuccessionPlan::where(function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
            })
            ->whereIn('id', $planIds)
            ->select('id', 'name')
            ->get()->toArray();
        }
        $requestEndTime = now()->toDateTimeString();

    
        $response = [
            'data' => $enhancedItems,
            'current_page' => $items->currentPage(),
            'last_page' => $items->lastPage(),
            'startTime' => $requestStartTime,
            'endTime' => $requestEndTime
        ];
        
        // Cache for 5 minutes to improve performance
        \Cache::put($cacheKey, response()->json($response), now()->addMinutes(5));
        
        return response()->json($response);
    }
    

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->user();

        $internalperson = InternalPeople::create([
            'employee_id' => $request->input('empid'),
            'role'        => $request->input('role'),
            'start_date'  => $request->input('start_date'),
            'location'    => $request->input('location'),
            'created_by'  => $user->id,
        ]);

        return redirect()->route('internalpeople.index');
        
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'id' => 'required|integer|exists:internal_people,id',
            'flightRisk' => 'nullable|integer|in:0,1',
            'is_relocatable' => 'nullable|integer|in:0,1',
        ]);

        $person = InternalPeople::find($validated['id']);
        $update_field = array_key_exists('flightRisk', $validated) 
                        ? "Flight Risk" 
                        : (array_key_exists('is_relocatable', $validated) ? "Relocation" : " ");

        if (isset($validated['flightRisk'])) {
            $person->flight_risk = $validated['flightRisk'];
        }
        if (isset($validated['is_relocatable'])) {
            $person->is_relocatable = $validated['is_relocatable'];
        }

        $person->save();

        // return response()->json(['message' => $update_field.' updated successfully!']);
        return redirect()
			->route('myorg.index')
			->with('toast', [
				'type' => 'info',
				'message' => $update_field.' updated successfully!',
			]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
