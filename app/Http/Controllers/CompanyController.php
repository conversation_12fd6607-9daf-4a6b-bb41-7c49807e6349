<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Company;



class CompanyController extends Controller
{
    public function fetchCompanies(Request $request)
    {
        $query = $request->get('q', '');
        $page = $request->get('page', 1);
        $pageSize = 100;
    
        $users = Company::where('name', 'like', '%' . $query . '%')
                    ->orderBy('name')
                    ->paginate($pageSize, ['*'], 'page', $page);
    
        return response()->json([
            'data' => $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'text' => $user->name
                ];
            }),
            'has_more' => $users->hasMorePages()
        ]);
    }
}
