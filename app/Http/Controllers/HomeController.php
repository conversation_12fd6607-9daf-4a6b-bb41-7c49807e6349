<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use App\Models\notifications;
use Carbon\Carbon;
use Inertia\Inertia;
use App\Models\Account;
use App\Models\Company;
use Illuminate\Support\Facades\DB;
use App\Models\People;
use App\Models\Location;
use App\Models\Recruitment;
use App\Models\RecruitmentStage;
use App\Models\RecruitmentPipeline;


class HomeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();

        $notifications = notifications::all();
		
		
        $accountObj = Account::where('id',$user->account_id)->first();
        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        if($accountObj){
            if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }

        $companyIds = [];
        if($interestedCompanies && count($interestedCompanies) > 0){
            $companyIds = $interestedCompanies;
        }
        else if($interestedIndustries && count($interestedIndustries) > 0){
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if($interestedSectors && count($interestedSectors) > 0){
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
        }

		
		
		$genderCount = DB::table('people')
            ->select('gender', DB::raw('COUNT(*) as gender_count'))
            ->where('company_id','!=',$user->company_id)
            ->when(!empty($companyIds), function($query){
                $query->WhereIn('company_id',$companyIds);
            })
            ->where('status', '!=', 'Submitted')
            ->where('status', '!=', 'Reported')
            ->groupBy('gender')
            ->get();
			
			
			$totalPeopleCount = DB::table('people')
				->where('company_id', '!=', $user->company_id)
				->count();

			
			$topCountries = DB::table('people')
				->select('country', DB::raw('COUNT(id) as candidate_count'))
				->where('company_id', '!=', $user->company_id)
				->groupBy('country')
				->orderByDesc('candidate_count')
				->take(5)
				->get()
				->map(function ($item) use ($totalPeopleCount) {
					$percentage = ($totalPeopleCount > 0) ? ($item->candidate_count / $totalPeopleCount) * 100 : 0;
					return [
						'country' => $item->country,
						'candidate_count' => $item->candidate_count,
						'percentage' => round($percentage, 2)
					];
				});

				$userId=$user->id;
				$recruitments =[];
				  // if ($user->role != 'Viewer') {
					$recruitments = Recruitment::where(function ($query) use ($user, $userId) {
						  
								$query->where('status', '!=', 'archived')
									  ->orWhereNull('status');
								$query->where(function ($subQuery) use ($userId) {
									$subQuery->where('user_id', $userId)
											 ->orWhereJsonContains('viewer_shared_with', $userId)
											 ->orWhereJsonContains('editor_shared_with', $userId);
								});
							
				})
				->with(['recruitmentPipeline'])
				->get();
			// }
// dd($recruitments); die;	
			$Interviewcandidates = [];

			if($recruitments){
				foreach ($recruitments as $recruitment) {
					foreach ($recruitment->recruitmentPipeline as $pipeline) {
						$interviewDate = $pipeline->interview_date;
						$candidateId = $pipeline->Candidate_ID;
						$candidate = People::select('forename', 'surname')->find($candidateId);
						$fullName = $candidate ? $candidate->forename . ' ' . $candidate->surname : 'Unknown';

						if (!isset($Interviewcandidates[$interviewDate])) {
							$Interviewcandidates[$interviewDate] = [];
						}

						$Interviewcandidates[$interviewDate][] = [
							'candidate_id' => $candidateId,
							'candidate_name' => $fullName,
							'recruitmentID' => $recruitment->id,
						];
					}
				}
				
			}
		
        return Inertia::render('Home', [
			'assetBaseUrl' => asset(''),
			'notifications' => $notifications,
			'genderCount' => $genderCount,
			'topCountries' => $topCountries,
			'Interviewcandidates' => $Interviewcandidates,
		]);

    }



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
	
	 public function HandlerdeleteNotification(Request $request){
		$validated = $request->validate([
			'id' => 'required|exists:notifications,id',
		]);
		// dd($validated);
        $dnotification = notifications::findOrfail($validated['id']);
        $dnotification->delete();
		return redirect()
			->route('home.index')
			->with('toast', [
				'type' => 'success',
				'message' => 'Notification deleted successfully.',
			]);	
    }
	
	public function HandlerDeleteAllNotification()
    { 
        notifications::where('user_id', auth()->user()->id)->delete();
		return redirect()
			->route('home.index')
			->with('toast', [
				'type' => 'success',
				'message' => 'All Notification deleted successfully.',
			]);	
    }
}
