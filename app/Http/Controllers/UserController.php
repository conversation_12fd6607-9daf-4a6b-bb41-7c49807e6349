<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Company; 
use App\Models\Account; 
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('users.index');
    }

    public function endSession()
    {
        //dd("Working to sign-out");
        // Clear all data from the session
        Session::flush();
    
        // Regenerate the session ID for added security
        Session::regenerate();
    
        // Redirect to the login page or another appropriate page
        return redirect('/login');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = auth()->user();
        return view('users.create',compact('user'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //dd($request);
        
        $data = $request->validate([
            'name'              => 'required|string|max:255',
            'role'              => 'required|string|max:255',
            'team'              => 'required|string|max:255',
            'account_name'      => 'required|string|max:255',
            'account_type'      => 'required|string|max:255',
            'company'           => 'required|string|max:255',
            'company_country'   => 'required|string|max:255',
            'email'             => 'required|email|unique:users',
            'password'          => 'required|confirmed|min:8',
        ]);

        $data['password'] = Hash::make($data['password']);

        $company=Company::create([
            'corporate_hq_country' => $data['company_country'],
            'name' =>$data['company'],
        ]);

        if($data['role'] === 'Master'){
            $userallowance = ********;
        }
        else{
            $userallowance = 5;
        } 
    
        $account = Account::create([
            'company_id' => $company->id,
            'account_type' =>$data['account_type'],
            'Account_name' => $data['account_name'],
            'active_users' => 1,
            'users_limit' =>$userallowance,
            
        ]);

        User::create([
            'name'          =>  $data['name'],
            'image_url'     =>  'Not required',
            'team'          =>  $data['team'],
            'company_id'    =>  $company->id,
            'account_id'    =>  $account->id,
            'role'          => $data['role'],
            'email'         => $data['email'],
            'password'      => $data['password'],
            'profile_pic'   => 'not provided'
        ]);

        return redirect()->route('users.index')
            ->with('success', 'User added successfully.');
    }
    

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
