<?php

namespace App\Http\Controllers;

use App\Models\Recruitment;
use App\Models\RecruitmentStage;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class InterviewController extends Controller
{
    public function store(Request $request)
    { 
		 $validated = $request->validate([
			'recruitmentTitle' => 'required|string|max:255',
			'stages' => 'required|array',
			'stages.*.stage_name' => 'required|string|max:255',
			'stages.*.stage_number' => 'required|integer',
		]);

		$userId = Auth::id();

		// Save recruitment title
		$recruitment = Recruitment::create([
			'user_id' => $userId,
			'recruitment_name' => $validated['recruitmentTitle'],
			'shared_with' => '',
		]);

		foreach ($validated['stages'] as $stage) {
			RecruitmentStage::create([
				'user_id' => $userId, 
				'recruitment_project_id' => $recruitment->id, 
				'stage_name' => $stage['stage_name'],
				'stage_number' => $stage['stage_number'],
			]);
		}
		// return redirect()->route('interviews.schedule');
    }
	
	public function recruitmentSchedule(){
		$userId = Auth::id();

		// Fetch recruitments along with their associated stages
		$recruitments = Recruitment::where('user_id', $userId)
			->with('stages') // Load related RecruitmentStages
			->get();

		return Inertia::render('RecruitmentSchedule', [
			'recruitments' => $recruitments,
			'assetBaseUrl' => asset(''),
		]);
	}
}
