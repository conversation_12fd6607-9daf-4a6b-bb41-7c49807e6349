<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ChatHistory;
use Illuminate\Support\Facades\DB;

class SuccessionBuilderController extends Controller
{
    
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get the authenticated user
        $user = auth()->user();

        $topics = ChatHistory::where('user_id', $user->id)
        ->distinct('topic')
        ->pluck('topic');

        return view('Builder.index', compact('topics','user'));

    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $builder)
    {   
        // Get the authenticated user
        $user = auth()->user();
    
        // Fetch the entire chat history for the selected topic
        $selectedChatHistories = ChatHistory::where('user_id', $user->id)
                                            ->where('topic', 'LIKE', '%' . $builder . '%')
                                            ->latest()
                                            ->get();
        
        // Fetch all unique topics for the authenticated user
        $topics = ChatHistory::where('user_id', $user->id)
            ->distinct('topic')
            ->pluck('topic');
        
        // Get the last chat history for the selected topic
        $lastChatHistory = $selectedChatHistories->last();

        // Get the question from the last chat history
        $question = $lastChatHistory->question;

        // Check if the last chat history has an SQL query
        if ($lastChatHistory->response) {
            // Execute the SQL query to get the results
            $sqlQuery = $lastChatHistory->response;

            if (!empty($sqlQuery)) {
            // Execute the SQL query to get the results
                $sqlResult = \DB::select($sqlQuery);
            }
        } else {
            $sqlResult = null;
        }
    
        return view('Builder.show', [
            'topic' => $builder,
            'topics' => $topics,
            'user'  => $user,
            'chatHistories' => $selectedChatHistories,
            'question' => $question,
            'sqlResult' => $sqlResult,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
