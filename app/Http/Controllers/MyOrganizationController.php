<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\InternalPeople;
use App\Models\Organisation;
use App\Models\OrganisationPeople;
use App\Models\SuccessionPlan;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;
use App\Models\AssessmentCriteria;
use App\Models\Location;
use App\Models\InternalSkills;
use App\Models\Company;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\PeopleImport;
use App\Models\Competency;
use App\Models\InternalRequirement;
use App\Models\Role;
use App\Models\SuccessPeople;
use Illuminate\Support\Facades\DB;
use App\Models\internal_career_histories;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;

class MyOrganizationController extends Controller
{
    public function index()
    {	
		$user = auth()->user();
        $isAdminUser = User::isAdminUser();
        $isMasterUser = User::isMasterUser();
        $plans = SuccessionPlan::where('user_id', $user->id)->get();

        $organisationsObj = Organisation::where('created_by', $user->id)
            ->orWhereRaw("JSON_CONTAINS(IFNULL(NULLIF(shared, ''), '[]'), ?)", [json_encode($user->id)])
            ->orderByDesc('created_at')->paginate(9);
        $organisationArr = $organisationsObj->map(function ($organisation) {
            $genderCounts = OrganisationPeople::internalPeopleGenderCount($organisation->id);
            $totalPeople = array_sum($genderCounts);

            return [
                'organisation_id' => $organisation->id,
                'organisation_title' => $organisation->title,
                'organisation_people' => $totalPeople,
                'created_by' => $organisation->created_by,
                'female_ratio' => $totalPeople > 0 ? round(($genderCounts['Female'] ?? 0) / $totalPeople * 100) : 0,
                'male_ratio' => $totalPeople > 0 ? round(($genderCounts['Male'] ?? 0) / $totalPeople * 100) : 0,
            ];
        });
      
	  
	   $savedLabels = AssessmentCriteria::select('id as criteria_id', 'label', 'response')
        ->where('company_id', $user->company_id)
        ->where('type', '9box')
        ->orderBy('response')
        ->get()
        ->toArray();
        // $criteriaData = AssessmentCriteria::select('id as criteria_id', 'label', 'response')->get();
        
        // echo "<pre>"; print_r($savedLabels); echo "</pre>"; die;

        if(!empty($savedLabels)) {
            $gridLabels = $savedLabels;

        } else {
            $gridLabels = array_fill(0, 9, '');
        }
		
		$countries = Location::distinct()->pluck('country_name')->map(function ($country) {
            return ['value' => $country, 'label' => $country];
        })->toArray();
		
        $shareWithUsers  = User::where('company_id', $user->company_id)
        ->where('id', '!=', $user->id)->get()
        ->map(function ($user) {
            return [
                'value' => $user->id,
                'label' => $user->name,
            ];
        })
        ->toArray();


       // dd($this->downloadCSV());
		return Inertia::render('MyOrganization', [
            'organisations' => $organisationArr,
            'pagination' => '',
            'userId' => $user->id,
            'isAdminUser' => $isAdminUser,
            'isMasterUser' => $isMasterUser,
            'gridLabels' => $gridLabels,
			'assetBaseUrl' => asset(''),
			'countries' => $countries,
            'shareWithUsers' => $shareWithUsers,
        ]);
            
    }
	public function SaveNineBoxGrid(Request $request){
		
	  $user = auth()->user();
	  $validated = $request->validate([
			'gridLabels.*' => 'required|string|max:255',
		]);
	
        foreach ($validated['gridLabels'] as $index => $label) {
            AssessmentCriteria::updateOrCreate(
                [
                    'company_id' => $user->company_id,
                    'response' => $index,
                    'type' => '9box'
                ],
                [
                    'label' => $label,
                    'created_by' => $user->id
                ]
            );
        }
		
		return redirect()
			->route('myorg.index')
			->with('toast', [
				'type' => 'info',
				'message' => 'Grid labels have been saved successfully.',
			]);
	}
	
	public function SaveIndividual(Request $request){

	  
		$validated = $request->validate([
			'individualdata.forename' => 'required|string',
			'individualdata.surname' => 'required|string',
			'individualdata.gender' => 'required|string',
			'individualdata.selectedCountries' => 'required|string',
			'individualdata.role' => 'required|string',
			'individualdata.startDate' => 'required|string',
			'individualdata.linkedInUrl' => 'nullable|string',
			'individualdata.functionValue' => 'required|string',
			'individualdata.division' => 'required|string',
			'individualdata.empid' => 'nullable|unique:internal_people,employee_id',
			'individualdata.skillData.specialised' => 'required|array',
			'individualdata.skillData.common' => 'required|array',
			'individualdata.skillData.certification' => 'required|array',
		]);
		
		
        $user = auth()->user();
        $internalcompany = Company::find($user->company_id);
        $tenure = !empty($validated['individualdata']['startDate']) ? Carbon::now()->diffInYears(Carbon::parse($validated['individualdata']['startDate'])) : 0;
		
		// dd($validated['individualdata']);
        $internalPerson = new InternalPeople([
            'employee_id' =>$validated['individualdata']['empid'],
            'forename' => $validated['individualdata']['forename'],
            'surname' => $validated['individualdata']['surname'],
            'gender' => $validated['individualdata']['gender'] ?? 'Not Applicable',
            'latest_role' => $validated['individualdata']['role'],
            'linkedinURL' => $validated['individualdata']['linkedInUrl'],
            'function' => $validated['individualdata']['functionValue'] ?? 'Not Applicable',
            'division' => $validated['individualdata']['division']  ?? 'Not Applicable',
            'readiness' => 'Not Ready',
            'start_date' => $validated['individualdata']['startDate'],
            'tenure' => $tenure,
            'company_id' => $user->company_id,
            'company_name' => $internalcompany->name,
            'location' => $validated['individualdata']['selectedCountries'] ?? 'Not Applicable',
            'user_id' => $user->id
        ]);

        $internalPerson->save();

        foreach ($validated['individualdata']['skillData']['specialised'] as $skill) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $skill,
                    'internal_people' => $internalPerson->id,
                    'company_id' =>  $user->company_id,
                    'skill_type' => 'Specialised'
                ]
            );
        }

        
        foreach($validated['individualdata']['skillData']['common'] as $qualification) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $qualification,
                    'internal_people' => $internalPerson->id,
                    'company_id' =>  $user->company_id,
                    'skill_type' => 'Common'
                ]
            );
        }

        foreach ($validated['individualdata']['skillData']['certification'] as $qualification) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $qualification,
                    'internal_people' => $internalPerson->id,
                    'company_id' =>  $user->company_id,
                    'skill_type' => 'Certification'
                ]
            );
        }
        // $this->addIndividualPeople($internalPerson->id);

		return redirect()
			->route('myorg.index')
			->with('toast', [
				'type' => 'info',
				'message' => 'Individual user added successfully.',
			]);
	}
	
    public function downloadCSV()
    {
        $headerRow = ['forename', 'surname', 'role', 'start_date(m/d/y)', 'function', 'division', 'employee_id'];

        // Dummy data
        $dummyData = [
            [
                'forename' => 'John',
                'surname' => 'Doe',
                'role' => 'Manager',
                'start_date' => '2022-01-01',
                'function' => 'Management',
                'division' => 'Operations',
                'employee_id' => 'EMP001',
            ],
            [
                'forename' => 'Jane',
                'surname' => 'Smith',
                'role' => 'Developer',
                'start_date' => '2023-05-15',
                'function' => 'Development',
                'division' => 'Engineering',
                'employee_id' => 'EMP002',
            ],
        ];

        $dataRows = collect($dummyData)
            ->map(function ($person) {
                return [
                    'forename' => $person['forename'],
                    'surname' => $person['surname'],
                    'role' => $person['role'],
                    'start_date' => $person['start_date'],
                    'function' => $person['function'],
                    'division' => $person['division'],
                    'employee_id' => $person['employee_id'],
                ];
            })
            ->toArray();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="csv_template.csv"',
        ];

        return Response::stream(function () use ($headerRow, $dataRows) {
            $handle = fopen('php://output', 'w');
            fputcsv($handle, $headerRow);
            foreach ($dataRows as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
        }, 200, $headers);
    }

    public function uploadCSV(Request $request)
    {
        $user = auth()->user();
        if ($request->hasFile('csvFile')) {
            try {
				$duplicates=[];
                foreach ($request->file('csvFile') as $csvFile) {
                   $duplicates= $this->processCSV($csvFile, $user);
                }
				
				if (count($duplicates) > 0) {
					return redirect()
						->route('myorg.index')
						->with('toast', [
							'type' => 'info',
							'message' => 'Duplicate employee ids are not uploaded '.implode(",", $duplicates),
						]);
				}
				return redirect()
					->route('myorg.index')
					->with('toast', [
						'type' => 'success',
						'message' => 'File uploaded successfully.',
					]);
            } catch (\Exception $e) {
                return redirect()->back()->withErrors(['file' => 'File upload failed.']);
            }
        }
    }

    private function processCSV($csvFile, $user)
    {
        
        $excelData = Excel::toArray(new PeopleImport, $csvFile->getRealPath());
        $duplicates = [];
        $excelData = $excelData[0];
        foreach ($excelData as $key => $row) {
            $employeeId = isset($row['employee_id']) ? $row['employee_id'] : null;
            $existingRecord = InternalPeople::where('employee_id', $employeeId)->whereNotNull('employee_id')->first();
            
            if ($existingRecord) {
                $duplicates[] = $existingRecord->employee_id;
            } else {
                
                $internalcompany = Company::where('id', $user->company_id)->first();
                $roleExists = Role::where('title', $row['role'])->first();
                if (empty($roleExists)) {
                    $roleExists = new Role();
                    $roleExists->title = $row['role'];
                    $roleExists->save();
                }
             
                $internalPeople = InternalPeople::create([
                    'forename' => $row['forename'],
                    'surname' => $row['surname'],
                    'role_id' => $roleExists->id,
                    'function' => $row['function'],
                    'division' => $row['division'],
                    'start_date' => Carbon::parse($row['start_datemdy'])->format('Y-m-d'),
                    'employee_id' => $employeeId,
                    'created_by' => $user->id,
                    'company_id' => $internalcompany->id,
                    'company_name' => $internalcompany->name,
                    'user_id' => $user->id
                ]);
            
            }
            
        }
		return $duplicates;
    }

	public function SharePeopleSave(Request $request){
        $user = auth()->user();
		$validated = $request->validate([
			'selectedshareOrgansationPeoples' => 'required|array',
			'organisationId' => 'required|exists:organisations,id',
		]);

        $filteredArray = array_filter($validated['selectedshareOrgansationPeoples'], function ($value) {
            return $value === true;
        });
		
        $filteredArray = array_keys($filteredArray);
        $organisation = Organisation::find($validated['organisationId']);
        $organisation->update(['shared' => $filteredArray]);

		return redirect()
			->route('myorg.index')
			->with('toast', [
				'type' => 'success',
				'message' => 'Organisation shared successfully.',
			]);
    }
 
    public function getInternalPeople($internalPeopleArr){
        return [
            'name' => $internalPeopleArr['forename'] . ' ' . $internalPeopleArr['surname'],
            'role' => $internalPeopleArr['latest_role'] ?? 'N/A',
            'location' => $internalPeopleArr['location'] ?? 'Unknown',
            'tenure' => $internalPeopleArr['tenure'] ?? 'N/A',
            'flightRisk' => $internalPeopleArr['flight_risk'] ?? false,
            'is_relocatable' => $internalPeopleArr['is_relocatable'] ?? false,
            'plan' => $internalPeopleArr['plan'] ?? null,
            'isUpperCircle' => $internalPeopleArr['isUpperCircle'] ?? false,
            'internalUserPlans' => $internalPeopleArr['internalUserPlans'] ?? [],
            'summary' => $internalPeopleArr['summary'] ?? 'No summary available',
            'country_manager' => $internalPeopleArr['country_manager'] ?? '',
            'corporate_level' => $internalPeopleArr['corporate_level'] ?? '',
            'key_strengths' => $internalPeopleArr['key_strengths'] ?? '',
            'development_areas' => $internalPeopleArr['development_areas'] ?? '',
            'linkedinURL' => $internalPeopleArr['linkedinURL'] ?? '',
            'other_tags' => $internalPeopleArr['other_tags'] ?? '',
            'division' => $internalPeopleArr['division'] ?? '',
            'function' => $internalPeopleArr['function'] ?? '',
            'company' => $internalPeopleArr['company_name'] ?? 'Unknown',
            'skills' => $internalPeopleArr['internal_skills'] ?? [],
            'career_history' => $internalPeopleArr['career_history'] ?? [],
            'readiness' => $internalPeopleArr['readiness'] === '1' ? 'Ready Now' : 
              ($internalPeopleArr['readiness'] === '2' ? 'Nearly Ready' : 
              ($internalPeopleArr['readiness'] === '3' ? 'Not Ready' : 'Future Ready')),
            'potential' => $internalPeopleArr['potential'] ?? 'Medium',
            'performance' => $internalPeopleArr['performance'] ?? 'Medium',
            'potentialColorClass' => $internalPeopleArr['potential'] === 'High' ? 'node-potential-blue' : ($internalPeopleArr['potential'] === 'Medium' ? 'node-potential-orange' : 'node-potential-grey'),
            'readinessColorClass' => $internalPeopleArr['readiness'] === '1' ? 'node-readiness-green' : 
                        ($internalPeopleArr['readiness'] === '2' ? 'bg-[#FFF6ED] text-[#FFA347]' : 
                        ($internalPeopleArr['readiness'] === '3' ? 'node-potential-grey' : 'node-readiness-red')),
        ];
    }

   public function getDescendantsData($parentId, $organisationId)
    {

      $descendants = OrganisationPeople::where('parent_id', $parentId)
            ->where('organisation_id', $organisationId)
            ->with(['internalPeople' => function ($query) {
                            $query->with([
                                'careerHistory.company',
                                'internalSkills',
                                'notes.user',
                            ]);
                        }, 'children'])->get();


        return $descendants->map(function ($descendant) use ($organisationId) {
            $internalPerson = $descendant->internalPeople;
            if($internalPerson) { //Check if internal person is found
                $user = auth()->user();
                $planIds = SuccessPeople::where(['people_id' => $internalPerson->id, 'type' => 'Internal'])->pluck('plan_id')->toArray();
                          $matchingPlan = SuccessionPlan::where(function ($query) use ($user) {
                                $query->where('user_id', $user->id)
                                    ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
                            })
                            ->whereIn('id', $planIds)
                            ->select('id', 'name')
                            ->first(); 
                $internalPerson->plan = $matchingPlan;   
                
                $internalPerson->internalUserPlans = SuccessionPlan::where(function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                        ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
                })
                ->whereIn('id', $planIds)
                ->select('id', 'name')
                ->get()->toArray();

                $internalCompetencies = DB::table('internal_competencies')
                    ->join('competencies', 'internal_competencies.competency_id', '=', 'competencies.id')
                    ->where('internal_competencies.organisation_people_id', $descendant->id)
                    ->select('internal_competencies.id as id','competencies.label as criteria', 'internal_competencies.score', DB::raw('false as deleted'))
                    ->get()
                    ->toArray();

                $nineBoxData = InternalRequirement::where('organisation_people_id', $descendant->id)
                    ->select('assessment_criteria_id as criteria_id', 'response')
                    ->first();

                return [
                    'id' => $internalPerson->id, // Use OrganisationPeople ID
                    'internalPeople' => $this->getInternalPeople($internalPerson->toArray()), // Convert to array
                    'descendants' => $this->getDescendantsData($descendant->id, $organisationId),
                    'competencies' => $internalCompetencies,
                    'nineBoxData' => $nineBoxData,
                ];
            }
            
        })->filter();
    }

  public function viewOrganisation($id)
    {
        try {        
            $user = auth()->user(); // Get user at the beginning

            $organisation = Organisation::findOrFail($id);

            $orgCompetencies = Competency::where('org_id', $organisation->id)
                ->select('id', 'label as criteria', 'score', DB::raw('false as deleted'))
                ->get()
                ->toArray();

            $organisationPeople = OrganisationPeople::where('organisation_id', $organisation->id)
                    ->whereNull('parent_id')
                    ->with([
                        'internalPeople' => function ($query) {
                            $query->with([
                                'careerHistory.company',
                                'internalSkills',
                                'notes.user',
                            ]);
                        },
                        'children'
                    ])->get()->map(function ($organisationPerson) use ($id, $user) {
                    $internalPerson = $organisationPerson->internalPeople;

                    if($internalPerson){
                        $planIds = SuccessPeople::where(['people_id' => $internalPerson->id, 'type' => 'Internal'])->pluck('plan_id')->toArray();
                          $matchingPlan = SuccessionPlan::where(function ($query) use ($user) {
                                $query->where('user_id', $user->id)
                                    ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
                            })
                            ->whereIn('id', $planIds)
                            ->select('id', 'name')
                            ->first(); 
                        $internalPerson->plan = $matchingPlan;                        
                        $internalPerson->internalUserPlans = SuccessionPlan::where(function ($query) use ($user) {
                            $query->where('user_id', $user->id)
                                ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
                            })
                            ->whereIn('id', $planIds)
                            ->select('id', 'name')
                            ->get()->toArray();
                        $internalCompetencies = DB::table('internal_competencies')
                            ->join('competencies', 'internal_competencies.competency_id', '=', 'competencies.id')
                            ->where('internal_competencies.organisation_people_id', $organisationPerson->id)
                            ->select('internal_competencies.id as id', 'competencies.label as criteria', 'internal_competencies.score', DB::raw('false as deleted'))
                            ->get()
                            ->toArray();

                        $nineBoxData = InternalRequirement::where('organisation_people_id', $organisationPerson->id)
                            ->select('assessment_criteria_id as criteria_id', 'response')
                            ->first();

                        return [
                            'id' => $internalPerson->id, // Use OrganisationPeople ID
                            'internalPeople' => $this->getInternalPeople($internalPerson->toArray()), // Convert to array
                            'descendants' => $this->getDescendantsData($organisationPerson->id, $id),
                            'competencies' => $internalCompetencies,
                            'nineBoxData' => $nineBoxData,
                        ];
                    }
                    
                })->filter();

            $addedPeopleInOrganisation = $organisationPeople->toArray();
         
            return response()->json([
                'organisation_id' => $organisation->id,
                'organisation_title' => $organisation->title,
                'organisation_people' => $addedPeopleInOrganisation,
                'orgCompetencies' => $orgCompetencies,
                'created_by' => $organisation->created_by,
            ]);
           
        } catch (Exception $e) {
            return response()->json([
                'error' => 'Organisation not found.',
                'message' => $e->getMessage(),
            ], 404);
        }
    }

	
	public function SaveReadyFuture(Request $request){
		$validated = $request->validate([
			'id' => 'required|integer|exists:internal_people,id',
			'selected_option' => 'required|integer|in:0,1,2,3',
		]);
		$internalPerson = InternalPeople::find($validated['id']);

		if (!$internalPerson) {
			return redirect()->back()->with('toast', [
				'type' => 'error',
				'message' => 'Record not found.',
			]);
		}
		
		$internalPerson->readiness = $validated['selected_option'];
		$internalPerson->save();
		return redirect()->back()->with('toast', [
				'type' => 'success',
				'message' => 'Readiness updated successfully.',
			]);	
			
	}

    public function deleteOrganisation(Request $request)
    {
       
        $validated = $request->validate([
			'organisationId' => 'required|integer|exists:organisations,id',
		]);
        
        try {
            $organisation = Organisation::findOrFail($validated['organisationId']);
            $organisation->delete();

            return redirect()
                ->route('myorg.index')
                ->with('toast', [
                    'type' => 'success',
                    'message' => 'Organisation deleted successfully.',
                ]);
        } catch (Exception $e) {
            return redirect()
                ->route('myorg.index')
                ->with('toast', [
                    'type' => 'error',
                    'message' => 'Organisation not found.',
                ]);
        }
    }

    public function insertOrganisationPeople($organisationId, $peopleArray, $userId, $parentId = null)
    {
        foreach ($peopleArray as $person) {
            // Insert the person into OrganisationPeople
            $organisationPerson = OrganisationPeople::updateOrCreate(
                [
                    'internal_people_id' => $person['id'],
                    'organisation_id' => $organisationId,
                ],
                [
                    'parent_id' => $parentId,
                    'created_by' => $userId
                ]
            );

            if(!empty($person['nineBoxData'])){
                $nineGridData = $person['nineBoxData'];
    
                InternalRequirement::updateOrCreate(
                    [
                        'organisation_people_id' => $organisationPerson->id
                    ],
                    [
                        'assessment_criteria_id' => $nineGridData['criteria_id'],
                        'response' => $nineGridData['response']]
                );
            }
            //dd($person['competencies']);
            if (!empty($person['competencies'])) {
                foreach ($person['competencies'] as $competenciesData) { // Loop through each competency
                    $competencyRecord = Competency::where('org_id', $organisationId)
                        ->where('label', $competenciesData['criteria'])
                        ->first();
            
                    if ($competencyRecord) {
                        DB::table('internal_competencies')->updateOrInsert(
                            [
                                'organisation_people_id' => $organisationPerson->id,
                                'competency_id' => $competencyRecord->id,
                            ],
                            [
                                'score' => $competenciesData['score'],
                            ]
                        );
                    }
                }
            }            

            // If the person has descendants, process them recursively
            if (!empty($person['descendants'])) {
                $this->insertOrganisationPeople($organisationId, $person['descendants'], $userId, $organisationPerson->id);
            }
        }
    }

     public function saveOrganization(Request $request)
    {
        $user = auth()->user();
        $selectedPeopleArray = $request->input('addedPeoplesInOrganisation');
        $orgCompetencies = $request->input('orgCompetencies');
        $organisationId = $request->input('editingOrganisationId');
        $organisationName = $request->input('organisationName');

        DB::beginTransaction(); // Start transaction

        try {
            $validated = $request->validate([
                'shared' => 'nullable|array',
            ]);

            if($selectedPeopleArray == null || $selectedPeopleArray == []){ // Check if people are added to the organisation
                return redirect()
                    ->route('myorg.index')
                    ->with('toast', [
                        'type' => 'error',
                        'message' => 'Please add people to the organisation.',
                    ]);
            }

            if($organisationName == null || $organisationName == ''){ // Check if organisation name is provided
                $organisationName = 'Draft';
            }

            if ($organisationId) {
                // **Update Organisation**
                $organisation = Organisation::findOrFail($organisationId);
                $organisation->update([
                    'title' => $organisationName,
                    'shared' => implode(',', $validated['shared'] ?? []), // Convert array to a comma-separated string
                ]);

                // **Update Organisation Competencies**
                if (count($orgCompetencies) > 0) {
                    foreach ($orgCompetencies as $competency) {
                        if (!$competency['deleted']) { // Skip deleted records
                            Competency::updateOrCreate(
                                [
                                    'company_id' => $user->company_id,
                                    'org_id' => $organisationId,
                                    'label' => $competency['criteria'],
                                ],
                                [
                                    'score' => $competency['score'],
                                    'created_by' => $user->id,
                                ]
                            );
                        }
                    }
                }

                // Remove existing Organisation People and re-insert (if necessary)
                $organisation->organisationPeople()->delete();
                $this->insertOrganisationPeople($organisation->id, $selectedPeopleArray, $user->id);
               

                $message = 'Organisation updated successfully.';
            } else {
                // **Create Organisation**
                $organisation = Organisation::create([
                    'title' => $organisationName,
                    'created_by' => $user->id,
                    'shared' => implode(',', $validated['shared'] ?? []),
                ]);

                // Insert Organisation Competencies
                if (count($orgCompetencies) > 0) {
                    foreach ($orgCompetencies as $competency) {
                        if (!$competency['deleted']) {
                            Competency::create([
                                'company_id' => $user->company_id,
                                'org_id' => $organisation->id,
                                'label' => $competency['criteria'],
                                'score' => $competency['score'],
                                'created_by' => $user->id,
                            ]);
                        }
                    }
                }
            
                // Insert Organisation People
                $this->insertOrganisationPeople($organisation->id, $selectedPeopleArray, $user->id);

                $message = 'Organisation created successfully.';
            }

            DB::commit(); // Commit transaction if everything is successful

            return redirect()
                ->route('myorg.index')
                ->with('toast', [
                    'type' => 'success',
                    'message' => $message,
                ]);
        } catch (\Exception $e) {
            DB::rollBack(); // Rollback in case of an error

            return redirect()
                ->route('myorg.index')
                ->with('toast', [
                    'type' => 'error',
                    'message' => 'An error occurred: ' . $e->getMessage(),
                ]);
        }
    }
	
	public function SavePerformance(Request $request){
		$validated = $request->validate([
			'id' => 'required|integer|exists:internal_people,id',
			'selected_option' => 'required|string|max:255',
		]);
		$internalPerson = InternalPeople::find($validated['id']);
		
		if (!$internalPerson) {
			return redirect()->back()->with('toast', [
				'type' => 'error',
				'message' => 'Record not found.',
			]);
		}
		
		$internalPerson->performance = $validated['selected_option'];
		$internalPerson->save();
        
		return redirect()->back()->with('toast', [
				'type' => 'success',
				'message' => 'Performance updated successfully.',
			]);	
			
	}
    public function SaveCareerHistory(Request $request){
       
        $validated = $request->validate([
            'id' => 'required|integer|exists:internal_people,id', 
            'careerHistory' => 'nullable|array', 
            'careerHistory.*.role' => 'required|string|max:255',
            'careerHistory.*.company.name' => 'required|string|max:255', 
            'careerHistory.*.start_date' => 'required|date',
            'careerHistory.*.end_date' => 'nullable|date|after_or_equal:careerHistory.*.start_date',
        ]);
        
		$internalPeople = InternalPeople::find($validated['id']);
        internal_career_histories::where(['people_id' => $validated['id']])->delete();
        $internalPeopleId = $validated['id'];

        foreach ($validated['careerHistory'] as $index => $history) {
            $company = Company::where('name', trim($history['company']['name']))->first();

            if (empty($company)) {
                $company = Company::create([
                    'name' => $history['company']['name'],
                    'location_id' => 1784674685,
                    'status' => 'submitted',
                ]);
            }

 
            $startDate = Carbon::parse($history['start_date']);
            $endDate = !empty($history['end_date']) ? Carbon::parse($history['end_date']) : Carbon::now();
            $roleTenure = $startDate->diffInYears($endDate); 
         
            $dataToInsert = [
                'people_id'       => $internalPeopleId,
                'role'            => $history['role'],
                'past_company_id' => $company->id,
                'start_date'      => $history['start_date'],
                'end_date'        => !empty($history['end_date']) ? $history['end_date'] : null,
                'tenure'          => $roleTenure, // Tenure for this role
            ];
     
            // Insert the career history record for this role
            internal_career_histories::create($dataToInsert);

        }

        $latestCareerHistory = internal_career_histories::where(['people_id' => $internalPeopleId])->orderBy('start_date', 'desc')->first();
      
        if(!empty($latestCareerHistory)) {
            $companyRecords = internal_career_histories::where([
                'people_id' => $internalPeopleId,
                'past_company_id' => $latestCareerHistory->past_company_id
            ])->get(['start_date', 'end_date']);
            $earliestStartDate = $companyRecords->min('start_date');
            $latestEndDate = $companyRecords->max('end_date') ?? Carbon::now();

            foreach ($companyRecords as $record) {
                if (is_null($record->end_date)) {
                    $latestEndDate = Carbon::now()->toDateString();
                    break;
                } 
            }
            $tenureInCompany = Carbon::parse($earliestStartDate)->diffInYears(Carbon::parse($latestEndDate));
            $internalPeople->update(['tenure_in_company' => $tenureInCompany]);
        }

    
        return redirect()->back()->with('toast', [
            'type' => 'success',
            'message' => 'Career history updated successfully!',
        ]);	
  
			
	}


    public function UpdateCandidateProfile(Request $request){
        $validated = $request->validate([
            'id' => 'required|integer|exists:internal_people,id', 
            'formData' => 'nullable|array',
            'formData.function' => 'nullable|string|max:255',
            'formData.division' => 'nullable|string|max:255',
            //'formData.readiness' => 'nullable|string|max:255',
            'formData.linkedinURL' => 'nullable|string|max:255',
            'formData.summary' => 'nullable|string|max:1000',
            'formData.internal_skills' => 'nullable|array',
            'formData.other_tags' => 'nullable|string|max:255',
        ]);

        $user = auth()->user();
        $peopleId = $validated['id'];

        $dataToUpdate = [
            'function' => $validated['formData']['function'],
            'division' => $validated['formData']['division'],
            'other_tags' => $validated['formData']['other_tags'],
            //'readiness' => $validated['formData']['readiness'],
            'summary'   => $validated['formData']['summary'],
            'linkedinURL' => $validated['formData']['linkedinURL']
        ];

        $internalPeople = InternalPeople::find($peopleId);
        $internalPeople->update($dataToUpdate);

        // Save each skill type to the database

        InternalSkills::where(['skill_type' => 'Specialised', 'internal_people' => $peopleId])->delete();
        foreach ($validated['formData']['internal_skills']['Specialised'] as $specialised) {
            InternalSkills::create(
                [
                    'skill_name' => $specialised,
                    'internal_people' => $peopleId,
                    'company_id' => $user->company_id,
                    'skill_type' => "Specialised"
                ]
            );
        }
        InternalSkills::where(['skill_type' => 'Common', 'internal_people' => $peopleId])->delete();
        foreach ($validated['formData']['internal_skills']['Common'] as $common) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $common,
                    'internal_people' => $peopleId,
                    'company_id' => $user->company_id,
                    'skill_type' => "Common"
                ]
            );
        }

        InternalSkills::where(['skill_type' => 'Certification', 'internal_people' => $peopleId])->delete();
        foreach ($validated['formData']['internal_skills']['Certification'] as $certification) {
            InternalSkills::updateOrCreate(
                [
                    'skill_name' => $certification,
                    'internal_people' => $peopleId,
                    'company_id' => $user->company_id,
                    'skill_type' => "Certification"
                ]
            );
        }

        return redirect()->back()->with('toast', [
            'type' => 'success',
            'message' => 'Internal people updated successfully!',
        ]);	
       
 
        
    }

	public function UpdateCandidatePotential(Request $request){
		$validated = $request->validate([
			'id' => 'required|integer|exists:internal_people,id',
			'selected_option' => 'required|string|max:255',
		]);
		$internalPerson = InternalPeople::find($validated['id']);
		// dd($validated); die;
		if (!$internalPerson) {
			return redirect()->back()->with('toast', [
				'type' => 'error',
				'message' => 'Record not found.',
			]);
		}
		
		$internalPerson->potential = $validated['selected_option'];
		$internalPerson->save();
		return redirect()->back()->with('toast', [
				'type' => 'success',
				'message' => 'Potential updated successfully.',
			]);	
			
	}

    public function UpdateCandidatePopup(Request $request) {
        $validated = $request->validate([
            'id' => 'required|integer|exists:internal_people,id',
            'formData' => 'nullable|array',
            'formData.corporate_level' => 'nullable|string|max:255',
            'formData.country_manager' => 'nullable|string|max:255',
            'formData.key_strengths' => 'nullable|array',
            'formData.key_strengths.*' => 'string|max:255',
            'formData.development_areas' => 'nullable|array',
            'formData.development_areas.*' => 'string|max:255',
        ]);

        $internalPerson = InternalPeople::find($validated['id']);
    
        if (!$internalPerson) {
            return redirect()->back()->with('toast', [
                'type' => 'error',
                'message' => 'Record not found.',
            ]);
        }
    
        // Update fields if they exist in request
        if (isset($validated['formData']['country_manager'])) {
            $internalPerson->country_manager = $validated['formData']['country_manager'];
        }
    
        if (isset($validated['formData']['corporate_level'])) {
            $internalPerson->corporate_level = $validated['formData']['corporate_level'];
        }
    
        if (isset($validated['formData']['key_strengths'])) {
            $internalPerson->key_strengths = json_encode($validated['formData']['key_strengths']);
        }
    
        if (isset($validated['formData']['development_areas'])) {
            $internalPerson->development_areas = json_encode($validated['formData']['development_areas']);
        }
    
        $internalPerson->save();
    
        return redirect()->back()->with('toast', [
            'type' => 'success',
            'message' => 'Candidate details updated successfully.',
        ]);
    }

    public function saveProfile_NineBoxData(Request $request){
        $validated = $request->validate([
            'organisationId' => 'required|integer|exists:organisations,id',
            'candidateId' => 'required|integer|exists:internal_people,id',
            'nineBoxData' => 'required|array',
        ]);
    
        // Debugging to check received data
        Log::info('Received NineBox Data', $validated);

        $organisationPeople = OrganisationPeople::where('organisation_id', $validated['organisationId'])
            ->where('internal_people_id', $validated['candidateId'])
            ->first();

        if (!$organisationPeople) {
            return redirect()->back()->with('toast', [
            'type' => 'error',
            'message' => 'Organisation People record not found.',
            ]);
        }
            
        InternalRequirement::updateOrCreate(
            [
                'organisation_people_id' => $organisationPeople->id,
            ],
            [
                'assessment_criteria_id' => $validated['nineBoxData']['criteria_id'],
                'response' => $validated['nineBoxData']['response'],
            ]
        );
    
        return redirect()
        ->route('myorg.index')
        ->with('toast', [
            'type' => 'success',
            'message' => 'Nine Box data saved successfully.',
        ]);
    }

    public function saveProfile_CompetencyData(Request $request){
        $validated = $request->validate([
            'organisationId' => 'required|integer|exists:organisations,id',
            'candidateId' => 'required|integer|exists:internal_people,id',
            'competencies' => 'required|array',
        ]);
    
        // Debugging to check received data
        Log::info('Received Competency Data', $validated);

        $organisationPeople = OrganisationPeople::where('organisation_id', $validated['organisationId'])
            ->where('internal_people_id', $validated['candidateId'])
            ->first();

        if (!$organisationPeople) {
            return redirect()->back()->with('toast', [
            'type' => 'error',
            'message' => 'Organisation People record not found.',
            ]);
        }

        foreach ($validated['competencies'] as $competency) {
            $competencyRecord = Competency::where('org_id', $validated['organisationId'])
                ->where('label', $competency['criteria'])
                ->first();
    
            if ($competencyRecord) {
                DB::table('internal_competencies')->updateOrInsert(
                    [
                        'organisation_people_id' => $organisationPeople->id,
                        'competency_id' => $competencyRecord->id,
                    ],
                    [
                        'score' => $competency['score'],
                    ]
                );
            }
        }
    
        return redirect()
        ->route('myorg.index')
        ->with('toast', [
            'type' => 'success',
            'message' => 'Competency data saved successfully.',
        ]);
    }

public function PlanDevelopmentAI(Request $request)
{
    $validated = $request->validate([
        'candID' => 'required|integer|exists:internal_people,id',
        'planID' => 'required|integer|exists:succession_plans,id',
        'content' => 'required|string',
        'summary' => 'nullable|string',
        'career_history' => 'nullable|array',
        'developmentAreas' => 'nullable|array',
        'keyStrengths' => 'nullable|array',
        'role' => 'required|string',
        'skills' => 'nullable|array',
        'company' => 'required|string',
        'name' => 'required|string',
    ]);

    $user = auth()->user();

    // Get Job Description
    $jobDescription = SuccessionPlan::where('id', $validated['planID'])
        ->where('user_id', $user->id)
        ->value('job_description');

    $summary = $validated['summary'] ?? '';
    $fullName = $validated['name'] ?? '';
    // Format Career History
    $careerHistory = '';
    if (is_array($validated['career_history'])) {
        $careerHistory = implode("\n", array_map(function ($item) {
            $companyName = $item['company']['name'] ?? '';
            $role = $item['role'] ?? '';
            $start = $item['start_date'] ?? '';
            $end = $item['end_date'] ?? 'Present';
            return "Role: $role, Company: $companyName, Start: $start, End: $end";
        }, $validated['career_history']));
    }

    // Format optional fields
    $keyStrengths = is_array($validated['keyStrengths']) ? implode(', ', $validated['keyStrengths']) : '';
    $developmentAreas = is_array($validated['developmentAreas']) ? implode(', ', $validated['developmentAreas']) : '';
    $skills = is_array($validated['skills']) ? implode(', ', $validated['skills']) : '';

  
    $fullPrompt = <<<PROMPT
You are a helpful HR assistant creating career development plans based on user data.

Candidate Summary:
$summary

Career History:
$careerHistory

Role: {$validated['role']}
Company: {$validated['company']}

Job Description:
$jobDescription

Key Strengths:
$keyStrengths

Development Areas:
$developmentAreas

Skills:
$skills

User Request:
{$validated['content']}

Based on the above information, generate a personalized development plan for the candidate that aligns with the job description and highlights areas for improvement and growth. Begin the plan with a bold title: "<strong>Personalized Development Plan for $fullName</strong>"
PROMPT;


    // Call OpenAI
$response = Http::withHeaders([
    'Authorization' => 'Bearer ' . env('OPENAI_API_KEY'),
])->timeout(90)->post('https://api.openai.com/v1/chat/completions', [
    'model' => 'gpt-4o-mini-2024-07-18',
    'messages' => [
        ['role' => 'system', 'content' => 'You are a helpful HR assistant creating career development plans.'],
        ['role' => 'user', 'content' => $fullPrompt],
    ],
    'temperature' => 0.7,
]);

    if ($response->successful()) {
    $generatedContent = $response->json()['choices'][0]['message']['content'];
        
        // Clean up the content
        $generatedContent = str_replace('###', '', $generatedContent);
    $generatedContent = str_replace('---', '', $generatedContent);
    
    // If the title is not already properly formatted, fix it
        if (!str_contains($generatedContent, '<strong>Personalized Development Plan for')) {
            // Replace any existing title with the properly formatted one
            $generatedContent = preg_replace(
                '/(Personalized\s*Development\s*Plan\s*for\s*[^\n\r<]*)/i', 
                '<strong>Personalized Development Plan for ' . $fullName . '</strong>', 
                $generatedContent
            );
        }
        
        return response()->json([
            'content' => $generatedContent,
        ]);
    } else {
        return response()->json([
            'error' => 'Failed to generate development plan',
            'details' => $response->json(),
        ], 500);
    }
}

public function PlanDevelopmentDownloadWord(Request $request)
{
    $validated = $request->validate([
        'content' => 'required|string',
        'planID' => 'required|integer'
    ]);

    try {
        // Add default styles for the document
        $phpWord = new \PhpOffice\PhpWord\PhpWord();
        
        // Define styles
        $phpWord->setDefaultFontName('Arial');
        $phpWord->setDefaultFontSize(11);
        
        $section = $phpWord->addSection();
        
        // Clean and prepare HTML content
        $content = $validated['content'];
        
        // Remove excessive whitespace
        $content = preg_replace('/\s+/', ' ', $content);
        
        // Replace <br> tags with proper paragraph breaks
        $content = preg_replace('/<br\s*?\/?>/', "</p><p>", $content);
        
        // Ensure content is wrapped in paragraph tags
        if (!preg_match('/^<p>/', $content)) {
            $content = '<p>' . $content;
        }
        if (!preg_match('/<\/p>$/', $content)) {
            $content .= '</p>';
        }
        
        // Add some basic styling
        $content = '<style>
            p { margin-bottom: 10px; }
            strong, b { font-weight: bold; }
            em, i { font-style: italic; }
            ul, ol { margin-left: 20px; }
            li { margin-bottom: 5px; }
        </style>' . $content;
        
        // Load HTML content into the Word document with full HTML support
        \PhpOffice\PhpWord\Shared\Html::addHtml($section, $content);
        
        // Save to temporary file
        $tempFile = tempnam(sys_get_temp_dir(), 'word');
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save($tempFile);

        // Return Word document as download
        return response()->download($tempFile, "plan-{$validated['planID']}.docx")->deleteFileAfterSend(true);
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to generate Word document',
            'message' => $e->getMessage(),
        ], 500);
    }
}

}


