<?php

namespace App\Http\Controllers;

use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;

class PDFController extends Controller
{
    public function download($planId)
    {

        $plan = SuccessionPlan::select('succession_plans.*', 'internal_people.forename', 'internal_people.surname', 'internal_people.latest_role', 'internal_people.company_name')
                    ->leftJoin('internal_people', 'succession_plans.tagged_individual', '=', 'internal_people.id')
                    ->where('succession_plans.id', $planId)
                    ->firstOrFail();

                    //echo '<pre>'; print_r($this->plan); echo '</pre>'; // Debugging line

        $successPeople = SuccessPeople::where('plan_id', $planId)
            ->whereNotNull('people_id') // Ensures the individual is tagged
            ->get();
        
        $pdf = Pdf::loadView('pdf.plan-org-chart-pdf', compact('plan', 'successPeople')); // create this view file

        return $pdf->download('plan-details.pdf');
    }
}
