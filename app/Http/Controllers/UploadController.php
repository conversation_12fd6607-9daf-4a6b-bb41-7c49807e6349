<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use Illuminate\Support\Facades\Storage;



class UploadController extends Controller
{
    public function index(Request $request,string $id)
    {
        $plan = SuccessionPlan::findOrFail($id);
        return view('upload.index', compact("plan"));

    }

    public function uploadLocalLogo()
    {
        // Define the local path of the file
        $localFilePath = public_path('images/successionLogoSmall.png'); // Adjust the file name as needed

        // Check if the file exists
        if (!file_exists($localFilePath)) {
            return response()->json(['message' => 'File not found in public/images directory.'], 404);
        }

        // Define the S3 path where the file will be uploaded
        $filePath = 'logos/' . uniqid() . '-logo.png';

        // Upload the file to S3
        $uploaded = Storage::disk('s3')->put($filePath, file_get_contents($localFilePath));

        if ($uploaded) {
            $url = Storage::disk('s3')->url($filePath); // Get the file URL
            return response()->json(['message' => 'Logo uploaded successfully!', 'url' => $url]);
        }

        return response()->json(['message' => 'Failed to upload logo.'], 500);
    }
}
