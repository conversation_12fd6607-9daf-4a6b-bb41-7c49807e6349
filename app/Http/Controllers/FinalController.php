<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\SuccessRequirements;
use App\Models\pipeline;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

use PhpOffice\PhpPresentation\PhpPresentation;
use PhpOffice\PhpPresentation\Shape\Drawing;
use PhpOffice\PhpPresentation\Shape\RichText;
use PhpOffice\PhpPresentation\Style\Alignment;
use PhpOffice\PhpPresentation\IOFactory;
use PhpOffice\PhpPresentation\Style\Color;

class FinalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(string $id)
    {   
        $user = auth()->user();
        $plan = SuccessionPlan::findOrFail($id);


        return view('success_people.index', compact('plan','user'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $plan, string $final)
    {
        $people = SuccessPeople::findOrFail($final);

        $people->delete();

        return redirect()->route('plan.final.index', ['plan' => $plan]);
    }

    public function downloadPowerPoint(Request $request, string $id)
    {
        // Retrieve data from the database
        $finalPeople = SuccessPeople::rightJoin('succession_plans', 'success_people.plan_id', '=', 'succession_plans.id')
            ->where('succession_plans.id', $id)
            ->get(['success_people.*', 'succession_plans.name as plan_name', 'succession_plans.description']);

        // Create a PowerPoint presentation
        $ppt = new PhpPresentation();

        // Create a title slide
        $titleSlide = $ppt->getActiveSlide();

        // Add a title and subtitle
        $title = $titleSlide->createRichTextShape()
            ->setHeight(300)
            ->setWidth(600)
            ->setOffsetX(170)
            ->setOffsetY(180);
        
        $titleText = $title->createTextRun('Your Presentation Title');
        $titleText->getFont()->setBold(true)
            ->setSize(60)
            ->setColor(new Color('111111'));

        $subtitleText = $title->createTextRun('Subtitle');
        $subtitleText->getFont()->setBold(false)
            ->setSize(40)
            ->setColor(new Color('000000'));

        foreach ($finalPeople as $person) {

            // Create a slide
            $currentSlide = $ppt->createSlide();

            // Create a shape (Text) for the title
            $titleShape = $currentSlide->createRichTextShape()
            ->setHeight(100) // Adjust height for the title
            ->setWidth(600)
            ->setOffsetX(170)
            ->setOffsetY(100); // Adjust vertical position for the title

            // Add title content from the table
            $titleContent = $titleShape->createTextRun("Title: " . $person->people_id); // Use $person->people_id
            $titleContent->getFont()->setBold(true) // Example: Set text to bold
                ->setSize(30) // Adjust font size for the title
                ->setColor(new Color('11111'));

    
            // Create a shape (Text) for the content
            $contentShape = $currentSlide->createRichTextShape()
                ->setHeight(300) // Adjust height for the content
                ->setWidth(600)
                ->setOffsetX(170)
                ->setOffsetY(250); // Adjust vertical position for the content

            // Add content from the table
            $content = $contentShape->createTextRun("Content: " . $person->headline); // Use $person->latest_role
            $content->getFont()->setBold(false) // Example: Set text to non-bold
                ->setSize(24) // Adjust font size for the content
                ->setColor(new Color('11111'));
        }

        // Save the presentation to a temporary file
        $filePath = storage_path('app/temp_presentation.pptx');
        $writer = IOFactory::createWriter($ppt, 'PowerPoint2007');
        $writer->save($filePath);

        // Trigger the download
        return response()->download($filePath, 'presentation.pptx')->deleteFileAfterSend(true);
    }
}
