<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Log;
use App\Models\ChatHistory;
 
class AiController extends Controller
{   
    public function sendMessage(Request $request)
    {

        // Retrieve the database connection name (assuming you have a select field for choosing the connection)
        $connection = $request->input('connection', 'mysql');

        // Retrieve the user's question from the input
        $question = $request->input('message');

        // Check if the user has any existing chat history
        $user = auth()->user();
        $chatHistory = ChatHistory::where('user_id', $user->id)->latest()->first();

        if (!$chatHistory) {
            $chatHistory = new ChatHistory();
        }

        // If there's no existing chat history or the chat history has a different question than the current one,
        // treat the current question as the topic for this conversation
        if (!$chatHistory || $chatHistory->question !== $question) {
            $topic = $question;
        } else {
        // If the current question is the same as the previous one, use the previous topic
            $topic = $chatHistory->topic;
        }

        // Define the list of allowed tables
        $allowedTables = ['career_histories','people','skills'];

        // Retrieve all tables and their columns from the database schema
        $tables = DB::connection($connection)
            ->getDoctrineSchemaManager()
            ->listTables();
        
        // Filter the tables to include only the allowed tables
        $filteredTables = collect($tables)->filter(function ($table) use ($allowedTables) {
            return in_array($table->getName(), $allowedTables);
        });

        // Construct the prompt that will be sent to GPT
        $initialPrompt = "Given an input question, first create a syntactically correct mysql query using only the allowed tables to run, then look at the results of the query and return the answer.Start your answer with the first SQL syntax and nothing else.\n";
        $initialPrompt .= "Use the following format:\n";
        $initialPrompt .= "Input-Question: \"{$question}\"\n";

        // Construct the prompt with the allowed tables
        $filteredPrompt = "Allowed Tables:\n";

        foreach ($tables as $table) {
                $tableName = $table->getName();
                if (in_array($tableName, $allowedTables)) {
                    $columns = collect($table->getColumns())->map(function ($column) {
                        return $column->getName() . ' (' . $column->getType()->getName() . ')';
                    })->implode(', ');
        
                    $filteredPrompt .= "\"{$tableName}\" has columns: {$columns}\n";
                }
            }
        
            // Combine the initial prompt and filtered prompt
            $finalPrompt = $initialPrompt . $filteredPrompt;

        // Convert the allowedTables and filteredTables arrays to strings
        $allowedTablesStr = implode(', ', $allowedTables);
        $filteredTablesStr = $filteredTables->implode(', ');

        ////////////////////////////////////////// CHATTING WITH CHATGPT////////////////////////////////////

        // Call GPT to run the initial query prompt
        $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $finalPrompt]],
        ]);

        Log::info(json_encode($response));

        // Extract the SQL query from the API response
        $sqlQuery = $response->choices[0]->message->content;

        ////////////////////////////////////////// CHECK THE QUERY //////////////////////////////////////////

        // Check if the SQL query includes the name of any allowed table
        $containsAllowedTable = false;
        foreach ($allowedTables as $allowedTable) {
            if (stripos($sqlQuery, $allowedTable) !== false) {
                $containsAllowedTable = true;
                break;
            }
        }

        // Check to make sure the queries contain syntax which are allowed
        $disallowedQueries =[
                             "DROP",
                             "TRUNCATE",
                             "UPDATE",
                             "DELETE",
                             "INSERT",
        ];

        // Check if the SQL query is in the list of disallowed queries
        $isDisallowedQuery = false;
        foreach ($disallowedQueries as $disallowedQuery) {
            if (stripos($sqlQuery, $disallowedQuery) !== false) {
            $isDisallowedQuery = true;
            break;
            }
        }

        /////////////////////////////////////// GENERATING THE OUTPUT ///////////////////////////////////////
        $sqlResult    = null;
        $answerPrompt = '';
        $answer = $sqlQuery;

        // Check if the query is using the correct tables
        if(!$containsAllowedTable) {
            $answerPrompt = 'Invalid Query. I can only help you with people data';
        }

        // Check if the query does not contain any harmful syntax
        elseif($isDisallowedQuery){
            $answerPrompt = 'My programming does not allow me to help with this query please ask me another question.';
        }

        else {
            $sqlResult = DB::connection($connection)->select($sqlQuery);
            $answerPrompt = "Let me get that for you";
        }
        
        // Convert the SQL result to a string
        $sqlResultString = $sqlResult ? json_encode($sqlResult) : "No data found";

        // Store the user's question and the chatbot's answer in the chat history for the authenticated user
        ChatHistory::create([
        'user_id' => $user->id,
        'topic' => $topic,
        'question' => $question,
        'response' => $answer,
                ]);
        $user = auth()->user();

            return redirect()->route('builder.show', ['builder' => $topic,'user' => $user])->with('sqlResult', $sqlResult);
}
}
        /*return view('test', ['question' => $question, 
                             'initialPrompt' => $initialPrompt, 
                             'allowedTable'=>$allowedTablesStr, 
                             'filteredTable'=>$filteredTablesStr,
                             'prompt' => $finalPrompt]);
    }
}

*/