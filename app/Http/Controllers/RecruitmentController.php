<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Recruitment;
use App\Models\RecruitmentStage;
use App\Models\RecruitmentPipeline;
use Illuminate\Support\Facades\Auth;
use App\Models\SuccessPeople;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use App\Mail\RightToRectificationEmail;
use App\Models\UserNotes;
use App\Models\CareerHistories;
use App\Models\Skills;


class RecruitmentController extends Controller
{
	public function index(){
        if (!Auth::check()) {
            redirect()->route('login')->send();
        }
       
        $user = Auth::user();
        $userId = $user->id;
       
        // $recruitments = Recruitment::where('user_id', $userId)
            // ->with(['recruitments',
                    // 'stages' => function ($query) {
                            // $query->with('recruitment');
                        // },
                    // 'recruitmentPipeline'])->withCount('stages')
            // ->get();
           
            // $recruitments = Recruitment::where('user_id', $userId)
                // ->with([
                    // 'recruitments',
                    // 'stages' => function ($query) {
                        // $query->with('recruitment');
                    // },
                    // 'recruitmentPipeline'
                // ])
                // ->withCount('stages')
                // ->get();
 
            // If no recruitments found, check in the viewer_shared_with and editor_shared_with columns
            // if ($recruitments->isEmpty()) {
                $user = User::find($userId); // Retrieve the user instance
                $recruitments = Recruitment::where(function ($query) use ($userId, $user) {
                        $query->where('status', '!=', 'archived')
                              ->orWhereNull('status');
                        $query->where(function ($subQuery) use ($userId) {
                            $subQuery->where('user_id', $userId)
                                     ->orWhereJsonContains('viewer_shared_with', $userId)
                                     ->orWhereJsonContains('editor_shared_with', $userId);
                        });
                    })
                    ->with([
                        'recruitments',
                        'stages' => function ($query) {
                            $query->with('recruitment');
                        },
                        'recruitmentPipeline'
                    ])
                    ->withCount('stages')
                    ->get();
                       
            // }
 
 
// dd($recruitments); die;
 
         $planIds = [];
          $recruitmentPlanMapping = [];
          $recruitmentcandidateCount = [];
        foreach ($recruitments as $recruitment) {
           
            if (isset($recruitment->recruitmentPipeline)) {
                foreach ($recruitment->recruitmentPipeline as $pipeline) {
                    $planIds[$recruitment['id']] = $pipeline->Plan_id;
                    $recruitmentPlanMapping[$recruitment['id']] = $pipeline->Plan_id;
                    $recruitmentcandidateCount[$recruitment['id']][] = $pipeline->Candidate_ID;
 
                }
            }
        }
        $planIds = array_unique($planIds);
   
        $recruitmentModel = new Recruitment();
        $groupedPlanScores = $recruitmentModel->GetPlanScore($planIds);
 
          $recruitmentScores = [];
            foreach ($recruitmentPlanMapping as $recruitmentId => $planId) {
                if (isset($groupedPlanScores[$recruitmentId])) {
                    $recruitmentScores[$recruitmentId] = $groupedPlanScores[$recruitmentId];
                } else {
                    $recruitmentScores[$recruitmentId] = []; // No scores for this recruitment
                }
            }
 
        return Inertia::render('Recruitment', [
            'assetBaseUrl' => asset(''),
            'recruitments' => $recruitments,
            'groupedPlanScores' => $recruitmentScores,
            'candidateCount' => $recruitmentcandidateCount,
            'user_role' => $user->role,
            'user_id' => $user->id
        ]);
    }
	
	

	public function store(Request $request)
    { 
	
		 $validated = $request->validate([
			'recruitmentTitle' => 'required|string|max:255',
			'stages' => 'required|array',
			'stages.*.stage_name' => 'required|string|max:255',
			'stages.*.stage_number' => 'required|integer',
		]);

		$userId = Auth::id();

		// Save recruitment title
		$recruitment = Recruitment::create([
			'user_id' => $userId,
			'recruitment_name' => $validated['recruitmentTitle'],
			'shared_with' => '',
		]);
		
		foreach ($validated['stages'] as $stage) {
			RecruitmentStage::create([
				'user_id' => $userId, 
				'recruitment_project_id' => $recruitment->id, 
				'stage_name' => $stage['stage_name'],
				'stage_number' => $stage['stage_number'],
			]);
		}
		
		return redirect()
			->route('recruitment.index')
			->with('toast', [
				'type' => 'success',
				'message' => 'Recruitment and stages saved successfully',
			]);
		  // return redirect()->route('recruitment.index')->with([
				// 'successMessage' => 'Recruitment and stages saved successfully'
			// ]);
		// return redirect()->route('interviews.schedule');
    }
	
	public function recruitmentSchedule($id){
		if (!Auth::check()) {
			redirect()->route('login')->send();
		}
		 $user = Auth::user();
		
		 $interviewerList=[];
		 $accountId = $user->account_id;
		 $userId =$user->id;
		 // $interviewerList = User::where('account_id', $accountId)->get();
		 $interviewerList = User::where('account_id', $accountId)
					//->where('id', '!=', $userId)  // Exclude the current user
					->where('role', '!=', 'Viewer')  // Exclude users with 'Viewer' role
					->get();
		 $users = User::all();
		 $userlist=[];
			foreach ($users as $userdata) {
				$userlist[$userdata->id] = $userdata->name;
			}
			
		// dd($user); die;
		$editorUsersList = User::where('account_id', $accountId)
			->where('id', '!=', $user->id)
			->where('role', '!=', 'Viewer')
			->get();
// dd($userNames); die;
		$viewerUsersList = User::where('account_id', $accountId)
			->where('id', '!=', $user->id)
			->where('role', '=', 'Viewer')
			->get();
		 
		 
		
		// $recruitments = Recruitment::where('user_id', $userId)
					// ->where('id', $id)
					// ->with([
						// 'stages' => function ($query) {
							// $query->with('recruitment');
						// },
						// 'recruitmentPipeline'
					// ])
					// ->first();
				$recruitments = Recruitment::where('user_id', $userId)
				->where('id', $id)
				->with([
					'stages' => function ($query) {
						$query->orderBy('stage_number');  // Order stages by stage_number
						$query->with('recruitment');
					},
					'recruitmentPipeline'
				])
				->first();

			// If no recruitment found, check in the viewer_shared_with and editor_shared_with columns
			if (!$recruitments) {
				$recruitments = Recruitment::where('id', $id)
					->where(function ($query) use ($userId) {
						$query->where('user_id', $userId)
							->orWhereJsonContains('viewer_shared_with', $userId)
							->orWhereJsonContains('editor_shared_with', $userId);
					})
					->with([
						'stages' => function ($query) {
							$query->orderBy('stage_number');  // Order stages by stage_number
							$query->with('recruitment');
						},
						'recruitmentPipeline'
					])
					->first();
			}

	$groupedPeoplesCareer =  [];
		
       // dd($recruitments->stages); die;
			// Initialize arrays
			$recruitmentPipeline = [];
			$recruitmentStage = [];
			if (!empty($recruitments)) {
				foreach ($recruitments->stages as $k => $stage) {
					if ($k === 0) {
						$recruitmentPipeline[$stage->id] = $recruitments->recruitmentPipeline->filter(function ($pipeline) use ($stage) {
							return $pipeline->stageID == $stage->id || is_null($pipeline->stageID);
							// return is_null($pipeline->stageID) || $pipeline->stageID === ''; 
						})->map(function ($pipeline) {
							
							$notesdata = UserNotes::where('entity_id', $pipeline->id)
												->where('entity_type', 'recruitment_candidate')
												->orderBy('user_notes.id', 'desc')
												->get();

							$candidate = SuccessPeople::where('people_id', $pipeline->Candidate_ID)
													   ->where('plan_id', $pipeline->Plan_id)
													   ->first();
							$skills_list = $candidate ? Skills::where('people_id', $pipeline->Candidate_ID)->get() : collect();
						   
													   
							$peoplescareer = CareerHistories::where('people_id',$pipeline->Candidate_ID)
														 ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
														 ->select('career_histories.*', 'companies.name as company_name')
														 ->orderBy('career_histories.start_date', 'desc')
														 ->get();	

				$groupedPeoplesCareer = $peoplescareer->isNotEmpty() ? $peoplescareer->map(function ($careerHistory) {
										return [
											'role' => $careerHistory->role,
											'company_name' => $careerHistory->company_name,
											'start_date' => \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y'),
											'end_date' => $careerHistory->end_date ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : '',
										];
									})->toArray() : [];
														 

							return [
								'id' => $pipeline->id,
								'recruitment_project_id' => $pipeline->recruitment_project_id,
								'Plan_id' => $pipeline->Plan_id,
								'Job_id' => $pipeline->Job_id,
								'Candidate_ID' => $pipeline->Candidate_ID,
								'name' => $candidate ? $candidate->first_name.' '.$candidate->last_name : 'Unknown', 
								'candidate_details' => $candidate ? $candidate->toArray() : null,
								'skills_list' => $skills_list ? $skills_list->toArray() : null,
								'recruitmentPipelinedata' => $pipeline ? $pipeline->toArray() : null,
								'notesdata' => $notesdata ? $notesdata->toArray() : null,
								'groupedPeoplesCareer' => $groupedPeoplesCareer ? $groupedPeoplesCareer : null,
							];
						})->values()->toArray(); 
						
			
					} else {
								
						$recruitmentPipeline[$stage->id] = $recruitments->recruitmentPipeline->filter(function ($pipeline) use ($stage) {
							return $pipeline->stageID == $stage->id; 
						})->map(function ($pipeline) {
							$notesdata = UserNotes::where('entity_id', $pipeline->id)
												->where('entity_type', 'recruitment_candidate')
												->orderBy('user_notes.id', 'desc')
												->get();

							$candidate = SuccessPeople::where('people_id', $pipeline->Candidate_ID)
													   ->where('plan_id', $pipeline->Plan_id)
													   ->first();
							$skills_list = $candidate ? Skills::where('people_id', $pipeline->Candidate_ID)->get() : collect();

							$peoplescareer = CareerHistories::where('people_id',$pipeline->Candidate_ID)
														 ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
														 ->select('career_histories.*', 'companies.name as company_name')
														 ->orderBy('career_histories.start_date', 'desc')
														 ->get();
						
						
													
							$groupedPeoplesCareer = $peoplescareer->isNotEmpty() ? $peoplescareer->map(function ($careerHistory) {
									return [
										'role' => $careerHistory->role,
										'company_name' => $careerHistory->company_name,
										'start_date' => \Carbon\Carbon::parse($careerHistory->start_date)->format('M Y'),
										'end_date' => $careerHistory->end_date ? \Carbon\Carbon::parse($careerHistory->end_date)->format('M Y') : '',
									];
								})->toArray() : [];
						
						
							return [
								'id' => $pipeline->id,
								'recruitment_project_id' => $pipeline->recruitment_project_id,
								'Plan_id' => $pipeline->Plan_id,
								'Job_id' => $pipeline->Job_id,
								'Candidate_ID' => $pipeline->Candidate_ID,
								'name' => $candidate ? $candidate->first_name.' '.$candidate->last_name : 'Unknown',
								'candidate_details' => $candidate ? $candidate->toArray() : null,
								'skills_list' => $skills_list ? $skills_list->toArray() : null,
								'recruitmentPipelinedata' => $pipeline ? $pipeline->toArray() : null,
								'notesdata' => $notesdata ? $notesdata->toArray() : null,
								'groupedPeoplesCareer' => $groupedPeoplesCareer ? $groupedPeoplesCareer : null,
							];
						})->values()->toArray(); 
						
						
						
					}

					$recruitmentStage[] = [
						'stagecolumnID' => $stage->id,
						'stage_name' => $stage->stage_name,
						'totalCards' => count($recruitmentPipeline[$stage->id]) // Counting the number of pipelines in this stage
					];
				}
			}


			// Pass data to Inertia
			return Inertia::render('RecruitmentSchedule', [
				'editorUsersList' => $editorUsersList,
				'viewerUsersList' => $viewerUsersList,
				'interviewerList' => $interviewerList,
				'recruitment_pipeline' => $recruitmentPipeline,
				'recruitment_stage' => $recruitmentStage,
				'recruitment_name' => $recruitments->recruitment_name ?? ' ',
				'recruitment_id' => $recruitments->id ?? ' ',
				'viewer_shared_with' => json_decode($recruitments->viewer_shared_with) ?? ' ',
				'editor_shared_with' => json_decode($recruitments->editor_shared_with) ?? ' ',
				'assetBaseUrl' => asset(''),
				'user_role' => $user->role,
				'userlist' => $userlist,
				'recruitment_status' => $recruitments->status,
				'peoplescareer' => $groupedPeoplesCareer,
			]);
		}
		
		public function handlerUpdateStagename(Request $request){
 
			$validated = $request->validate([
				'stageId' => 'required|exists:recruitment_stages,id',
				'newStageName' => 'required|string|max:255',
				'recruitmentId' => 'required|exists:recruitments,id',
			]);

			try {
				$stage = RecruitmentStage::findOrFail($validated['stageId']);
				$stage->update([
					'stage_name' => $validated['newStageName'],
				]);
					
				
			return redirect()
				->route('recruitment.schedule', ['id' => $validated['recruitmentId']])
				->with('toast', [
					'type' => 'success',
					'message' => 'Stage name updated successfully!',
				]);
					
				// return redirect()->route('recruitment.schedule', ['id' => $validated['recruitmentId']])->with([
					// 'successMessage' => 'Stage name updated successfully!'
				// ]);
			} catch (\Exception $e) {
				return response()->json([
					'success' => false,
					'message' => 'Error updating stage name.',
					'error' => $e->getMessage(),
				], 500);
			}
		}
		
		public function handlerDeleteRecruitment(Request $request){
			// dd($request); die;
			$validated = $request->validate([
				'recruitmentId' => 'required|exists:recruitments,id',
			]);
			
			$recruitmentId = $validated['recruitmentId'];
			$userId = auth()->id();
			$recruitment = Recruitment::where('user_id', $userId)
				->where('id', $recruitmentId)
				->with([
					'stages' => function ($query) {
						$query->with('recruitment');
					},
					'recruitmentPipeline',
				])
				->first();
				
				
				if (!$recruitment) {
					
					return response()->json([
						'message' => 'Recruitment not found or does not belong to the user.',
					], 404);
					
				}else{
					
				$recruitment->deleteWithRelations();
					return redirect()
						->route('recruitment.index', ['id' => $validated['recruitmentId']])
						->with('toast', [
							'type' => 'success',
							'message' => 'Recruitment deleted successfully.',
						]);	
				}
			
			// return redirect()->route('recruitment.index', ['id' => $validated['recruitmentId']])->with([
				// 'successMessage' => 'Recruitment deleted successfully.'
			// ]);
		}	
		
		public function saveinterviewdate(Request $request){
		
			$validated = $request->validate([
				'id' => 'required|exists:RecruitmentPipeline,id',
				'recruitment_id' => 'required|exists:recruitments,id',
				'date' => 'required|string|max:255',
			]);
			
			try {
				$RecruitmentPipeline = RecruitmentPipeline::findOrFail($validated['id']);
				
				$RecruitmentPipeline->update([
					'interview_date' => $validated['date'],
				]);
				
				return redirect()
				->route('recruitment.schedule', ['id' => $validated['recruitment_id']])
				->with('toast', [
					'type' => 'success',
					'message' => 'Date has been Save successfully!',
				]);
						
				// return redirect()->route('recruitment.schedule', ['id' => $validated['recruitment_id']])->with([
					// 'successMessage' => 'Date has been Save successfully!'
				// ]);
			} catch (\Exception $e) {
				return response()->json([
					'success' => false,
					'message' => 'Error updating stage name.',
					'error' => $e->getMessage(),
				], 500);
			}
		}
		
		public function recruitmentArchive(Request $request){
			$recruitment = Recruitment::find($request->recruitmentId);
	
			if ($recruitment) {
				$recruitment->status = $request->status; // 'archived' or 'active'
				$recruitment->save();
				
				return redirect()
					->route('recruitment.index')
					->with('toast', [
						'type' => 'success',
						'message' => 'Status updated successfully!',
					]); 
					
					
					
				  // return redirect()->route('recruitment.index')->with([
						// 'successMessage' => 'Status updated successfully!'
					// ]);
			}
	
			return response()->json(['error' => 'Recruitment not found'], 404);
		}
		
		public function interviewschedulesave(Request $request){

			$request->validate([
				'formattedDate' => 'required|date',
				'interviewerID' => 'required|exists:users,id', 
				'recruitment_id' => 'required|exists:recruitments,id',
			]);
			// dd($request->interviewerID); 
			$RecruitmentPipeline = RecruitmentPipeline::findOrFail($request->id);
			$RecruitmentPipeline->update([
				'interview_date' => $request->formattedDate,
				'interviewer_id' => $request->interviewerID,
			]);
			
			return redirect()
					->route('recruitment.schedule', ['id' => $request->recruitment_id])
					->with('toast', [
						'type' => 'success',
						'message' => 'Interview Scheduled successfully!',
					]);
		}
		
		public function Handlercandidateuploadcv(Request $request){
			$request->validate([
				'files' => 'nullable|array',  // Allow files to be nullable if no new files are uploaded
				'files.*' => 'mimes:pdf,doc,docx|max:2048',  // Validate each file
				'recruitment_pipeline_id' => 'required|exists:RecruitmentPipeline,id',
				'existing_files' => 'nullable|array',  // Allow existing files to be nullable
				'existing_names' => 'nullable|array',  // Allow existing files to be nullable
			]);

		
			$fileUrls = [];
			$fileNames = [];
		// dd($request); die;
			if ($request->has('existing_files') && is_array($request->existing_files)) {
				foreach ($request->existing_files as $k=> $fileUrl) {
					$fileUrls[] = $fileUrl;  
					$fileNames[] = $request->existing_names[$k];  
				}
			}
			if ($request->hasFile('files')) {
				try {
					
					foreach ($request->file('files') as $file) {
						$filePath = $file->store('upload', 's3');
						//Storage::disk('s3')->setVisibility($filePath, 'public');
						$fileUrls[] = $filePath;
						//$fileUrls[] = Storage::disk('s3')->url($filePath);  // Add URL to the fileUrls array
						$fileNames[] = $file->getClientOriginalName();  // Store the original name of the file
					}
				} catch (\Exception $e) {
					return redirect()->back()->withErrors(['file' => 'File upload failed.']);
				}
			}

			$combinedFileUrls = $fileUrls;
			$combinedFileNames = array_merge($fileNames, $request->has('existing_files') ? [] : []);

			$pipeline = RecruitmentPipeline::findOrFail($request->recruitment_pipeline_id);
			$pipeline->Link_cv = json_encode([
				'file_urls' => $combinedFileUrls, 
				'file_names' => $combinedFileNames 
			]);
			$pipeline->save();
			if($request->has('action') && $request->action=='delete'){
				return redirect()->back()->with('toast', [
					'type' => 'success',
					'message' => 'File Deleted successfully.',
				]);
			}else{
				return redirect()->back()->with('toast', [
					'type' => 'success',
					'message' => 'File uploaded successfully.',
				]);
			}
			
		}

		public function Handlercandidatedetailsave(Request $request){

			  $validated = $request->validate([
				'formData.email' => 'nullable|email',  
				'formData.phone' => 'nullable|string|max:15', 
				'formData.relocation' => 'nullable|string|max:255',
				'formData.expectedSalary' => 'nullable|numeric', 
				'formData.address' => 'nullable|string|max:255',
				'countryManager' => 'nullable|string|max:255',
				'recruitment_id' => 'required|exists:recruitments,id',
				'NoteSave' => 'required|string',
			]);

			
			$user = Auth::user();
			
			$userNoteData = [
				'entity_id' => $request->id,
				'entity_type' => 'recruitment_candidate',
				'Notes' => $validated['NoteSave'],
				'author' => $user->id
			];

			UserNotes::create($userNoteData);
			
				// dd($userNoteData); die;
				
				$recruitmentPipeline = RecruitmentPipeline::findOrFail($request->id);
				 $recruitmentPipeline->update([
						'Email' => $validated['formData']['email'],
						'Phone_number' => $validated['formData']['phone'],
						'relocation' => $validated['formData']['relocation'],
						'expected_salary' => $validated['formData']['expectedSalary'],
						'Address' => $validated['formData']['address'],
						'country_manager' => $validated['countryManager'],
					]);
					
		
					return redirect()
						->route('recruitment.schedule', ['id' => $request->recruitment_id])
						->with('toast', [
							'type' => 'success',
							'message' => 'Candidate details saved successfully!',
						]);
				// return redirect()->route('recruitment.schedule', ['id' => $request->recruitment_id])->with([
					// 'successMessage' => 'Candidate Details saved successfully!'
				// ]);
		}
		
		public function removecandidateHandler(Request $request){
			
				$recruitmentPipeline = RecruitmentPipeline::findOrFail($request->id);
				$recruitmentPipeline->delete();

				return redirect()
						->route('recruitment.schedule', ['id' => $request->recruitment_id])
						->with('toast', [
							'type' => 'success',
							'message' => 'The Candidate Deleted successfully!',
						]);
		}
		
		public function candidateSendEmail(Request $request){
			
			$validatedData = $request->validate([
				'getemail' => 'nullable|email',
				'title' => 'required|string|max:255',
				'emailContent' => 'required|string',
			]);
// dd($validatedData); die; 
			$user = auth()->user();
			$sendersName = $user->name;

			$email = $validatedData['getemail'];
			$title = $validatedData['title'];
			$emailContent = $validatedData['emailContent'];

			if ($email) {
				Mail::to($email)->send(new RightToRectificationEmail($sendersName, $title, $emailContent));

				return redirect()->back()->with('toast', [
					'type' => 'success',
					'message' => 'Right to Rectification email has been sent.',
				]);
			} else {
				return redirect()->back()->with('toast', [
					'type' => 'error',
					'message' => 'Failed to send the email. The provided email address is invalid or missing.',
				]);
			}
		}

		public function SavecandidateNote(Request $request){
	
		    $validated = $request->validate([
				'id' => 'required|exists:RecruitmentPipeline,id',
				'note' => 'required|string',
			]);
			 $user = Auth::user();
			
			$userNoteData = [
				'entity_id' => $validated['id'],
				'entity_type' => 'recruitment_candidate',
				'Notes' => $validated['note'],
				'author' => $user->id
			];
		
			UserNotes::create($userNoteData);
			
			
			return redirect()->back()->with('toast', [
				'type' => 'success',
				'message' => 'Note has been created Successfully.',
			]);

			// return redirect()->back()->with('success', "Right to Rectification email has been sent.");
		}
		
		
			public function Saveshareproject(Request $request) {
				$recruitment = Recruitment::findOrFail($request->recruitment_id);
				$updateData = [];
				$updateData['editor_shared_with'] = json_encode($request->selectedEditorUsers); 
				$updateData['viewer_shared_with'] = json_encode($request->selectedViewerUsers);
				// dd($updateData); die;
				$recruitment->update($updateData);

				// Return success message
				return redirect()->back()->with('toast', [
					'type' => 'success',
					'message' => 'Project has been shared Successfully.',
				]);
			}

		public function SaveprogressHandler(Request $request){
			$validated = $request->validate([
				'currentStage' => 'required|string|max:255', 
				'destinationStage' => 'required|string|max:255', 
				'droppableId' => 'required|integer', 
				'ID' => 'required|integer|exists:RecruitmentPipeline,id', 
			]);
			
			
			$recruitmentPipeline = RecruitmentPipeline::findOrFail($request->ID);
			 $recruitmentPipeline->update([
					'stageID' => $validated['droppableId'],
				]);
			
			return redirect()->back()->with('toast', [
				'type' => 'success',
				'message' => "Candidate has been moved from stage {$request->currentStage} to {$request->destinationStage}.",
			]);

		}
		public function create_new_stage(Request $request)
			{
				// Validate incoming request
				$validated = $request->validate([
					'updatedStageColumns' => 'required|array',
					'recruitment_id' => 'required|exists:recruitments,id',
				]);

				$userId = Auth::id();
				$counter = 0;

				foreach ($validated['updatedStageColumns'] as $stage) {
					$counter++;

					if (array_key_exists('stagenumber', $stage)) {
						$recruitmentStage =RecruitmentStage::create([
								'user_id' => $userId,
								'recruitment_project_id' => $validated['recruitment_id'],
								'stage_name' => $stage['stage_name'],
								'stage_number' => $counter,
							]);
						// dd($validated['updatedStageColumns']);
						
						// return back()->with([
							// 'toast' => [
								// 'type' => 'success',
								// 'message' => 'Stages saved successfully',
							// ],
							// 'id' => $recruitmentStage->id, 
						// ]);
					} else {
							RecruitmentStage::where('id', $stage['stagecolumnID'])
							->where('recruitment_project_id', $validated['recruitment_id'])
							->update([
								'stage_number' => $counter,
							]);
						
					}
				}


				// return redirect()
						// ->route('recruitment.schedule', ['id' => $validated['recruitment_id']])
						// ->with('toast', [
							// 'type' => 'success',
							// 'message' => 'Stages saved successfully',
						// ]);
						
				// Redirect back with a success message
				return redirect()->back()->with('toast', [
					'type' => 'success',
					'message' => 'Stages saved successfully',
				]);
			}


		
		public function downloadCandidateCV($fileurl)
		{
			if (!auth()->check()) {
				abort(403);
			}
		
			$filePath = 'upload/' . $fileurl;

			$cleanPath = preg_replace('#(^/|(?<!:)//+)#', '/', $filePath);
			$cleanPath = ltrim($cleanPath, '/');

			if (Storage::disk('s3')->exists($cleanPath)) {
				return Storage::disk('s3')->download($cleanPath);
			} else {
				abort(404);
			}
		}
			

			
}
