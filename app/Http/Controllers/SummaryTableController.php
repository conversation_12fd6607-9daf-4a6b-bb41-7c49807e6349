<?php

namespace App\Http\Controllers;

use App\Models\InternalPeople;
use Illuminate\Http\Request;

class SummaryTableController extends Controller
{
    public function getRoles(Request $request)
    {
        $search = $request->query('search', '');

        $results = InternalPeople::select('id', 'forename', 'surname', 'latest_role')
            ->when($search, function ($query, $search) {
                $query->where('forename', 'like', "%$search%")
                    ->orWhere('surname', 'like', "%$search%");
            })
            ->limit(20) // Limit results for performance
            ->get()
            ->map(function ($person) use ($search) {
                return [
                    'value' => $person->id,
                    'label' => $person->forename . ' ' . $person->surname . ' (' . ($person->latest_role ?? 'Not Provided') . ')',
                ];
            });

        return response()->json($results);
    }
}
