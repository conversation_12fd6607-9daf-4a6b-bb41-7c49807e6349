<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use OpenAI\Laravel\Facades\OpenAI;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\SuccessRequirements;
use App\Models\pipeline;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SuccPeopController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(string $id)
    {   
        $plan = SuccessionPlan::findOrFail($id);
        $user = auth()->user();
        return view('pipeline_people.index', compact('plan','user'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $plan)
    {
        $selectedItemIds = $request->input('selected_items', []);

        // Fetch the associated fields for the selected item_ids from the pipeline table
        $selectedItemsData = pipeline::whereIn('id', $selectedItemIds)->get();

        foreach ($selectedItemsData as $itemData) {
            
            // Generate the headline for the AI
            $roleai = $itemData->latest_role;
            $locationai = $itemData->location_id;
            $tenureai = $itemData->tenure;
            $companyai = $itemData->company_name;
            $skillsai = $itemData->skills;
            
            $inputArray = [
                'role'     => $roleai,
                'location' =>$locationai,
                'tenure'   =>$tenureai,
                'company'  =>$companyai,
                'skills'   =>$skillsai,
                        ];
            
            // Create a formatted message for GPT-3
            $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
            foreach ($inputArray as $key => $value) {
                    $message .= "$key: \"$value\"\n";
                    }
                        
            // Call GPT-3 to generate the headline
            $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
                                    ]);
                        
            $generatedHeadline = $response->choices[0]->message->content;


            SuccessPeople::create([
                'pipeline_id'        => $itemData->id,
                'plan_id'            => $itemData->plan_id,
                'user_id'            => $itemData->user_id,
                'people_id'          => $itemData->people_id,
                'first_name'         => $itemData->first_name,
                'last_name'          => $itemData->last_name,
                'middle_name'        => $itemData->middle_name,
                'other_name'         => $itemData->other_name,
                'gender'             => $itemData->gender,
                'diverse'            => $itemData->diverse,
                'location_id'        => $itemData->location_id,
                'linkedinURL'        => $itemData->linkedinURL,
                'latest_role'        => $itemData->latest_role,
                'company_id'         => $itemData->company_id,
                'company_name'       => $itemData->company_name,
                'start_date'         => $itemData->start_date,
                'end_date'           => $itemData->end_date,
                'tenure'             => $itemData->tenure,
                'function'           => $itemData->function,
                'division'           => $itemData->division,
                'seniority'          => $itemData->seniority,
                'career_history'     => $itemData->career_history,
                'educational_history'=> $itemData->educational_history,
                'skills'             => $itemData->skills,
                'languages'          => $itemData->languages,
                'skills_match'       => "0",
                'education_match'    => $itemData->education_match_count,
                'location_match'     => $itemData->location_match_count,
                'role_match'         => "1",
                'gender_match'       => $itemData->gender_score,
                'tenure_match'       => $itemData->tenancy_score,
                'headline'           => $generatedHeadline,
                'type'               => "External"
            ]);
        }
    
        return redirect()->route('plan.success_people.index', ['plan' => $plan])->with('success', 'Selected items saved successfully.');

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function view_PlanOrgChart($id)
    {
        $plan = SuccessionPlan::select('succession_plans.*', 'internal_people.forename', 'internal_people.surname', 'internal_people.latest_role', 'internal_people.company_name', 'internal_people.linkedinURL')
                    ->leftJoin('internal_people', 'succession_plans.tagged_individual', '=', 'internal_people.id')
                    ->where('succession_plans.id', $id)
                    ->firstOrFail();
        $user = auth()->user();
        //print_r($plan);
        return view('plan_org_chart.index', compact('plan','user'));
    }
}
