<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use OpenAI\Laravel\Facades\OpenAI;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\InternalPeople;
use App\Models\SuccessRequirements;
use App\Models\pipeline;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class InternalSuccessorController extends Controller

{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request,string $id)
    {
        $plan = SuccessionPlan::findOrFail($id);

        $user = auth()->user();

        $query = InternalPeople::query();

        // Get the IDs of people associated with the success plan
        $successPeopleIds = $plan->successPeople->pluck('people_id')->toArray();

        // Filter by roles (comma-separated values)
        if ($request->filled('roles')) {
            $roles = explode(',', $request->input('roles'));
            $roles = array_map('trim', $roles);
        
            $query->where(function ($query) use ($roles) {
                foreach ($roles as $role) {
                    $query->orWhere('latest_role', 'LIKE', "%{$role}%");
                        }
                    });
        }
                
        // Filter by location (comma-separated values)
        if ($request->filled('location')) {
            $locations = explode(',', $request->input('location'));
            $locations = array_map('trim', $locations);
                    
            $query->where(function ($query) use ($locations) {
                foreach ($locations as $location) {
                    $query->orWhere('locations_id', 'LIKE', "%{$location}%");
                        }
                    });
        }
                
        // Filter by education (comma-separated values)
        if ($request->filled('education')) {
            $educations = explode(',', $request->input('education'));
            $educations = array_map('trim', $educations);
        
            $query->where(function ($query) use ($educations) {
            foreach ($educations as $education) {
                $query->orWhere('educational_history', 'LIKE', "%{$education}%");
                        }
                    });
        }
                
        // Filter by industry (comma-separated values)
        if ($request->filled('company')) {
            $companies = explode(',', $request->input('company'));
            $companies = array_map('trim', $companies);
            $query->where(function ($query) use ($companies) {
                foreach ($companies as $company) {
                    $query->orWhere('company_name', 'LIKE', "%{$company}%");
                        }
                    });
        }
        
        // Filter by minimum_experience
        if ($request->filled('minimum_Experience')) {
            $minExperience = $request->input('minimum_Experience');
            $query->where('tenure', '>=', $minExperience);
            }
        
        $people = $query->get();
        
        return view('internalsuccess.index', ['people' => $people,'plan'=> $plan, 'user'=>$user]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $plan)
    {
        $plan = SuccessionPlan::findOrFail($plan);
        $pipelineId = pipeline::where('plan_id', $plan->id)->value('id');
        $user = auth()->user();

        // Getting the people in the succession plan
        $selectedItemIds = $request->input('selected_items', []);

        // Fetch the associated fields for the selected item_ids from the pipeline table
        $selectedItemsData = InternalPeople::whereIn('id', $selectedItemIds)->get();

        foreach ($selectedItemsData as $itemData) {
            
            // Generate the headline for the AI
            $roleai = $itemData->role;
            $locationai = $itemData->location;
            $tenureai = $itemData->start_date;

            $inputArray = [
                'role'     => $roleai,
                'location' =>$locationai,
                'tenure'   =>$tenureai,
            ];

            // Create a formatted message for GPT-3
            $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
            foreach ($inputArray as $key => $value) {
                    $message .= "$key: \"$value\"\n";
                        }
            
            // Call GPT-3 to generate the headline
            $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
                        ]);
            
            $generatedHeadline = $response->choices[0]->message->content;

            SuccessPeople::create([
                'pipeline_id'        => $pipelineId,
                'plan_id'            => $plan->id,
                'user_id'            => $user->id,
                'people_id'          => $itemData->employee_id,
                'first_name'         => "NA",
                'last_name'          => "NA",
                'middle_name'        => "NA",
                'other_name'         => "NA",
                'gender'             => "NA",
                'diverse'            => "NA",
                'location_id'        => 1,
                'linkedinURL'        => "NA",
                'latest_role'        => $itemData->role,
                'company_id'         => 1,
                'company_name'       => "NA",
                'start_date'         => $itemData->start_date,
                'tenure'             => 0,
                'function'           => "NA",
                'division'           => "NA",
                'seniority'          => "NA",
                'career_history'     => "NA",
                'educational_history'=> "NA",
                'skills'             => "NA",
                'languages'          => "NA",
                'skills_match'       => "0",
                'education_match'    => "0",
                'location_match'     => "0",
                'role_match'         => "1",
                'gender_match'       => "0",
                'tenure_match'       => "0",
                'headline'           => $generatedHeadline,
                'type'               => "Internal"
            ]);
        }

    return redirect()->route('plan.success_people.index', ['plan' => $plan])->with('success', 'Selected items saved successfully.');

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
