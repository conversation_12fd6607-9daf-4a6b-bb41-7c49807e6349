<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\UpgradeEmail;


class UpgradeController extends Controller
{
    public function sendUpgradeRequest($Option){
        $user = auth()->user();
        $source = $user->email;
        $userChoice = $Option;

        Mail::to('<EMAIL>')->send(new UpgradeEmail($source,$userChoice));
    }
}
