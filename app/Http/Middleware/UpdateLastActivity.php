<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class UpdateLastActivity
{
    public function handle($request, Closure $next)
    {
        // if (Auth::check()) {
        //     $user = Auth::user();
        //     $user->last_activity = Carbon::now();
        //     $user->save();
        // }

        // return $next($request);
    }
}

