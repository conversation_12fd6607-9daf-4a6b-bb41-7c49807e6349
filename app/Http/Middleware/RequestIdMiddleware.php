<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class RequestIdMiddleware
{
    /**
     * Handle an incoming request and add a unique request ID.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if a request ID was provided in the headers
        $requestId = $request->header('X-Request-ID');
        
        // If not, generate a new one
        if (!$requestId) {
            $requestId = (string) Str::uuid();
            $request->headers->set('X-Request-ID', $requestId);
        }
        
        // Process the request
        $response = $next($request);
        
        // Add the request ID to the response headers as well
        $response->headers->set('X-Request-ID', $requestId);
        
        return $response;
    }
}