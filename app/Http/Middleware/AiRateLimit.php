<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

class AiRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $service
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, string $service): Response
    {
        $user = $request->user();
        
        if (!$user) {
            throw new TooManyRequestsHttpException(null, 'Authentication required for AI services');
        }

        // Admin users get unlimited access
        if ($user->role === 'admin') {
            return $next($request);
        }

        // Get rate limit configuration for the service
        $config = $this->getServiceConfig($service);
        $key = $service . ':' . $user->id;

        // Check if user has exceeded rate limit
        if (RateLimiter::tooManyAttempts($key, $config['limit'])) {
            $retryAfter = RateLimiter::availableIn($key);
            throw new TooManyRequestsHttpException($retryAfter, 'Too many AI requests. Please try again later.');
        }

        // Increment the attempt count
        RateLimiter::hit($key, $config['decay']);

        // Process the request
        $response = $next($request);

        // Add rate limit headers to response
        $response->headers->set('X-RateLimit-Limit', $config['limit']);
        $response->headers->set('X-RateLimit-Remaining', RateLimiter::remaining($key, $config['limit']));
        $response->headers->set('X-RateLimit-Reset', now()->addSeconds(RateLimiter::availableIn($key))->timestamp);

        return $response;
    }

    /**
     * Get rate limit configuration for a specific AI service.
     *
     * @param  string  $service
     * @return array
     */
    protected function getServiceConfig(string $service): array
    {
        $configs = [
            'openai' => [
                'limit' => config('ai.rate_limits.openai.requests_per_minute', 10),
                'decay' => 60, // 1 minute
            ],
            'anthropic' => [
                'limit' => config('ai.rate_limits.anthropic.requests_per_minute', 8),
                'decay' => 60, // 1 minute
            ],
            'exa-ai' => [
                'limit' => config('ai.rate_limits.exa.requests_per_minute', 5),
                'decay' => 60, // 1 minute
            ],
        ];

        return $configs[$service] ?? [
            'limit' => 5,
            'decay' => 60,
        ];
    }
}