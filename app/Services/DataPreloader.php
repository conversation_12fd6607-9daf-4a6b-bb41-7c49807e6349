<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use App\Models\People;
use App\Models\Company;
use App\Models\Account;
use App\Models\CareerHistories;

class DataPreloader
{
    public function preloadPeople($user)
    {
        // Make the key for the specific user
        $user = auth()->user();

        $cacheKey = "user_{$user->id}_interests";

        return Cache::remember($cacheKey, 3600, function () use ($user) {
                    // Code to get the companies of interest for a specific account
            $accountObj = Account::where('id', $user->account_id)->first();

            $interestedCompanies = false;
            $interestedIndustries = false;
            $interestedSectors = false;
            if($accountObj){
                if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                    $interestedCompanies = explode(",", $accountObj->company_of_interest);
                }
                else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                    $interestedIndustries = explode(",", $accountObj->industry_interest);
                }
                else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                    $interestedSectors = explode(",", $accountObj->sector_interest);
                }
            }

            $companyIds = [];
            if($interestedCompanies && count($interestedCompanies) > 0){
                $companyIds = $interestedCompanies;
            }
            else if($interestedIndustries && count($interestedIndustries) > 0){
                $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
            }
            else if($interestedSectors && count($interestedSectors) > 0){
                $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
            }

            $companiesArr = array_unique(People::whereNotNull('company_id')->pluck('company_id')->toArray());

            $interestedCompaniesarray = $companyIds;

            // Populating the companies choices drop-down
            $companies = Company::where('id', '!=', $user->company_id)
                                        ->when(!empty($companyIds),function($query) use($companyIds){
                                            $query->whereIn('id', $companyIds);
                                        })
                                        ->whereIn('id', $companiesArr)
                                        ->get()->map(function ($company) {
                return [
                    'value' => $company->name,
                    'label' => $company->name,
                    'industry' => $company->industry,
                    'sector' => $company->sector
                ];
            })->toArray();
            
            //Populating the industries drop-down
            $industries = Company::whereNotNull('industry')
                                    ->where('industry', '!=', 'NA')
                                    ->where('industry', '!=', '0')
                                    ->when(!empty($companyIds),function($query) use($companyIds){
                                        $query->whereIn('id', $companyIds);
                                    })
                                    ->distinct()
                                    ->orderBy('industry', 'ASC')
                                    ->get(['id', 'industry'])
                                    ->groupBy('industry')
                                    ->toArray();
            foreach($industries as $key => $industry){
                $industries[] = ['value' => $key, 'label' => $key];
            }

            return ['interested_companies' => $interestedCompanies,
                    'companies' => $companies,
                    'industries' => $industries,
                    ];
        }); 
    }

    public function getCachedPeople()
    {
        $chunkCount = Cache::get('people_chunks_count', 0);

        for ($i = 0; $i < $chunkCount; $i++) {
            $chunk = Cache::get("company_chunk_{$i}");
            if ($chunk) {
                $allPeople = $allPeople->merge($chunk);
            }
        }

        //dd('worked');

        return response()->json(['people' => $allPeople]);
    }

    /*

    public function preloadRoles()
    {
        return Cache::remember('user_roles', 1440, function () {
            return Role::pluck('name', 'id');
        });
    }

    */
}