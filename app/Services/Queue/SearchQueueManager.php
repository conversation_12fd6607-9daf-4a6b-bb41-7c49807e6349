<?php

namespace App\Services\Queue;

use App\Jobs\SearchInternalPeopleJob;
use App\Jobs\ExternalSearchPerplexityJob;
use App\Jobs\TalentPoolExternalSearch;
use Illuminate\Support\Facades\Log;

/**
 * SearchQueueManager
 * 
 * Manages queue dispatching for search operations following course guidelines:
 * - Internal searches use high-priority 'internal_search' queue
 * - External searches use lower-priority 'external_search' queue  
 * - Proper worker allocation favoring internal searches
 */
class SearchQueueManager
{
    /**
     * Dispatch internal search job to high-priority queue
     */
    public static function dispatchInternalSearch($planData, $user)
    {
        Log::info("QUEUE MANAGER: Dispatching internal search to high-priority internal_search queue", [
            'plan_id' => $planData['plan_id'] ?? 'unknown',
            'queue' => 'internal_search',
            'priority' => 'high'
        ]);

        return SearchInternalPeopleJob::dispatch($planData, $user)
            ->onConnection('redis_internal')
            ->onQueue('internal_search');
    }

    /**
     * Dispatch external search job to lower-priority queue
     */
    public static function dispatchExternalSearch($planData, $user)
    {
        Log::info("QUEUE MANAGER: Dispatching external search to lower-priority external_search queue", [
            'plan_id' => $planData['plan_id'] ?? 'unknown',
            'queue' => 'external_search',
            'priority' => 'low'
        ]);

        return ExternalSearchPerplexityJob::dispatch($planData, $user)
            ->onConnection('redis_external')
            ->onQueue('external_search');
    }

    /**
     * Dispatch talent pool external search to external queue
     */
    public static function dispatchTalentPoolExternalSearch($planData, $user)
    {
        Log::info("QUEUE MANAGER: Dispatching talent pool external search to external_search queue", [
            'plan_id' => $planData['plan_id'] ?? 'unknown',
            'queue' => 'external_search',
            'priority' => 'low'
        ]);

        return TalentPoolExternalSearch::dispatch($planData, $user->id)
            ->onConnection('redis_external')
            ->onQueue('external_search');
    }

    /**
     * Get queue statistics and worker information
     */
    public static function getQueueStats()
    {
        return [
            'internal_search' => [
                'connection' => 'redis_internal',
                'queue' => 'internal_search',
                'priority' => 'high',
                'timeout' => 900,
                'workers' => [
                    'production' => 6,
                    'local' => 3
                ]
            ],
            'external_search' => [
                'connection' => 'redis_external', 
                'queue' => 'external_search',
                'priority' => 'low',
                'timeout' => 2400,
                'workers' => [
                    'production' => 4,
                    'local' => 2
                ]
            ]
        ];
    }

    /**
     * Log queue configuration for debugging
     */
    public static function logQueueConfiguration()
    {
        $stats = self::getQueueStats();
        
        Log::info("QUEUE CONFIGURATION: Search queue strategy active", [
            'strategy' => 'separate_queues_for_internal_external',
            'internal_priority' => 'high',
            'external_priority' => 'low',
            'queues' => $stats
        ]);
    }
}