<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\Company;
use App\Models\Location;
use App\Models\User;
use App\Models\pipeline;
use App\Models\SuccessRequirements;

class DataPreloaderService
{
    /**
     * Cache durations in seconds
     */
    const CACHE_DURATIONS = [
        'plan_summary' => 300,      // 5 minutes
        'chart_data' => 600,        // 10 minutes
        'candidate_list' => 180,    // 3 minutes
        'user_preferences' => 1800, // 30 minutes
    ];

    /**
     * Preload essential plan data with caching
     */
    public function preloadPlanData($planId, $userId)
    {
        $cacheKey = "plan_data_{$planId}_{$userId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['plan_summary'], function () use ($planId, $userId) {
            return [
                'plan' => $this->getPlanSummary($planId),
                'requirements' => $this->getPlanRequirements($planId),
                'statistics' => $this->getPlanStatistics($planId),
                'user_permissions' => $this->getUserPermissions($planId, $userId)
            ];
        });
    }

    /**
     * Preload chart data with batched queries
     */
    public function preloadChartData($planId)
    {
        $cacheKey = "chart_data_{$planId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['chart_data'], function () use ($planId) {
            // Single query to get all chart data at once
            $chartData = DB::table('success_people')
                ->where('plan_id', $planId)
                ->select([
                    'gender',
                    'company_name',
                    'function',
                    'type',
                    'exco'
                ])
                ->get();

            return [
                'gender' => $this->processGenderData($chartData),
                'company' => $this->processCompanyData($chartData),
                'function' => $this->processFunctionData($chartData),
                'type' => $this->processTypeData($chartData),
                'exco' => $this->processExcoData($chartData)
            ];
        });
    }

    /**
     * Preload paginated candidate data
     */
    public function preloadCandidateData($planId, $page = 1, $perPage = 20)
    {
        $cacheKey = "candidates_{$planId}_page_{$page}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['candidate_list'], function () use ($planId, $page, $perPage) {
            $offset = ($page - 1) * $perPage;

            // Get success people with pagination
            $successPeople = SuccessPeople::where('plan_id', $planId)
                ->select([
                    'id',
                    'people_id',
                    'first_name',
                    'last_name',
                    'latest_role',
                    'company_name',
                    'total_score',
                    'status',
                    'gender',
                    'country',
                    'tenure'
                ])
                ->orderBy('total_score', 'desc')
                ->offset($offset)
                ->limit($perPage)
                ->get();

            // Get pipeline people
            $successArray = SuccessPeople::where('plan_id', $planId)->pluck('people_id')->toArray();
            $pipelinePeople = pipeline::where('plan_id', $planId)
                ->whereNotIn('people_id', $successArray)
                ->select([
                    'id',
                    'people_id',
                    'first_name',
                    'last_name',
                    'latest_role',
                    'company_name',
                    'total_score',
                    'gender',
                    'country',
                    'tenure'
                ])
                ->orderBy('total_score', 'desc')
                ->offset($offset)
                ->limit($perPage)
                ->get();

            return [
                'success_people' => $successPeople,
                'pipeline_people' => $pipelinePeople,
                'total_success' => SuccessPeople::where('plan_id', $planId)->count(),
                'total_pipeline' => pipeline::where('plan_id', $planId)->whereNotIn('people_id', $successArray)->count()
            ];
        });
    }

    /**
     * Preload candidate profile data
     */
    public function preloadCandidateProfile($candidateId, $candidateType = 'success')
    {
        $cacheKey = "candidate_profile_{$candidateType}_{$candidateId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['candidate_list'], function () use ($candidateId, $candidateType) {
            $model = $candidateType === 'success' ? SuccessPeople::class : pipeline::class;
            
            $candidate = $model::with(['skills', 'careerHistory'])
                ->where('id', $candidateId)
                ->first();

            if (!$candidate) {
                return null;
            }

            return [
                'candidate' => $candidate,
                'skills' => $candidate->skills ?? [],
                'career_history' => $candidate->careerHistory ?? [],
                'scores' => $this->calculateCandidateScores($candidate)
            ];
        });
    }

    /**
     * Batch load multiple candidate profiles
     */
    public function batchLoadCandidateProfiles($candidateIds, $candidateType = 'success')
    {
        $cacheKeys = array_map(fn($id) => "candidate_profile_{$candidateType}_{$id}", $candidateIds);
        $cached = Cache::many($cacheKeys);
        
        $missing = [];
        foreach ($candidateIds as $id) {
            $cacheKey = "candidate_profile_{$candidateType}_{$id}";
            if (!isset($cached[$cacheKey])) {
                $missing[] = $id;
            }
        }

        // Load missing profiles
        if (!empty($missing)) {
            $model = $candidateType === 'success' ? SuccessPeople::class : pipeline::class;
            
            $candidates = $model::with(['skills', 'careerHistory'])
                ->whereIn('id', $missing)
                ->get();

            $cacheData = [];
            foreach ($candidates as $candidate) {
                $cacheKey = "candidate_profile_{$candidateType}_{$candidate->id}";
                $cacheData[$cacheKey] = [
                    'candidate' => $candidate,
                    'skills' => $candidate->skills ?? [],
                    'career_history' => $candidate->careerHistory ?? [],
                    'scores' => $this->calculateCandidateScores($candidate)
                ];
            }

            // Cache the missing profiles
            Cache::putMany($cacheData, self::CACHE_DURATIONS['candidate_list']);
            
            // Merge with existing cached data
            $cached = array_merge($cached, $cacheData);
        }

        return $cached;
    }

    /**
     * Preload user preferences and settings
     */
    public function preloadUserPreferences($userId)
    {
        $cacheKey = "user_preferences_{$userId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['user_preferences'], function () use ($userId) {
            $user = User::with('account')->find($userId);
            
            return [
                'user' => $user,
                'companies' => $this->getUserCompanies($user),
                'industries' => $this->getUserIndustries($user),
                'colleagues' => $this->getUserColleagues($user),
                'jobs' => $this->getUserJobs($user)
            ];
        });
    }

    /**
     * Clear cache for specific plan
     */
    public function clearPlanCache($planId)
    {
        $patterns = [
            "plan_data_{$planId}_*",
            "chart_data_{$planId}",
            "candidates_{$planId}_*"
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Helper methods for data processing
     */
    private function getPlanSummary($planId)
    {
        return SuccessionPlan::find($planId);
    }

    private function getPlanRequirements($planId)
    {
        return SuccessRequirements::where('plan_id', $planId)->get()->groupBy('type');
    }

    private function getPlanStatistics($planId)
    {
        return [
            'total_candidates' => SuccessPeople::where('plan_id', $planId)->count(),
            'approved_candidates' => SuccessPeople::where('plan_id', $planId)->where('status', 'Approved')->count(),
            'pipeline_count' => pipeline::where('plan_id', $planId)->count(),
            'avg_score' => SuccessPeople::where('plan_id', $planId)->avg('total_score')
        ];
    }

    private function getUserPermissions($planId, $userId)
    {
        $plan = SuccessionPlan::find($planId);
        
        return [
            'can_edit' => $plan->user_id === $userId,
            'can_view' => true, // Add your logic here
            'is_shared' => $plan->shared_with && str_contains($plan->shared_with, $userId)
        ];
    }

    private function processGenderData($data)
    {
        $grouped = $data->groupBy('gender');
        $labels = [];
        $chartData = [];

        foreach ($grouped as $gender => $items) {
            if ($gender) {
                $colorCode = $gender === "Male" ? "#3B82F6" : "#FFA347";
                $labels[] = $gender;
                $chartData[] = [
                    "x" => $gender,
                    "y" => $items->count(),
                    "fillColor" => $colorCode
                ];
            }
        }

        return ['labels' => $labels, 'data' => $chartData];
    }

    private function processCompanyData($data)
    {
        $grouped = $data->whereNotNull('company_name')
                        ->where('company_name', '!=', '')
                        ->groupBy('company_name');
        
        $labels = [];
        $chartData = [];

        foreach ($grouped as $company => $items) {
            $labels[] = $company;
            $chartData[] = $items->count();
        }

        return ['labels' => $labels, 'data' => $chartData];
    }

    private function processFunctionData($data)
    {
        $grouped = $data->whereNotNull('function')
                        ->where('function', '!=', '')
                        ->groupBy('function');
        
        $labels = [];
        $chartData = [];

        foreach ($grouped as $function => $items) {
            $labels[] = $function;
            $chartData[] = $items->count();
        }

        return ['labels' => $labels, 'data' => $chartData];
    }

    private function processTypeData($data)
    {
        $grouped = $data->groupBy('type');
        $labels = [];
        $chartData = [];

        foreach ($grouped as $type => $items) {
            if ($type) {
                $labels[] = $type;
                $chartData[] = $items->count();
            }
        }

        return ['labels' => $labels, 'data' => $chartData];
    }

    private function processExcoData($data)
    {
        $grouped = $data->groupBy('exco');
        $labels = [];
        $chartData = [];

        foreach ($grouped as $exco => $items) {
            if ($exco) {
                $labels[] = $exco;
                $chartData[] = $items->count();
            }
        }

        return ['labels' => $labels, 'data' => $chartData];
    }

    private function calculateCandidateScores($candidate)
    {
        return [
            'skills_match' => $candidate->skills_match ?? 0,
            'role_match' => $candidate->role_match ?? 0,
            'location_match' => $candidate->location_match ?? 0,
            'gender_match' => $candidate->gender_match ?? 0,
            'tenure_match' => $candidate->tenure_match ?? 0,
            'total_score' => $candidate->total_score ?? 0
        ];
    }

    private function getUserCompanies($user)
    {
        // Implementation for getting user's companies
        return Company::where('id', '!=', $user->company_id)->take(100)->get();
    }

    private function getUserIndustries($user)
    {
        // Implementation for getting user's industries
        return Company::distinct()->pluck('industry')->filter()->take(50);
    }

    private function getUserColleagues($user)
    {
        // Implementation for getting user's colleagues
        return User::where('company_id', $user->company_id)
                  ->where('id', '!=', $user->id)
                  ->take(100)
                  ->get();
    }

    private function getUserJobs($user)
    {
        // Implementation for getting user's jobs
        return collect([]); // Placeholder
    }
}