<?php

namespace App\Services\AI;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\RateLimiter;

class RateLimitedOpenAiService
{
    protected $apiKey;

    protected $baseUrl = 'https://api.openai.com/v1';

    // Rate limiting configuration based on OpenAI limits
    protected $requestsPerMinute = 20;  // Conservative limit for Tier 1

    protected $tokensPerMinute = 10000; // Conservative limit for Tier 1

    // Circuit breaker settings
    protected $circuitBreakerThreshold = 5; // Max consecutive failures

    protected $circuitBreakerTimeout = 300; // 5 minutes

    // API tier detection
    protected $apiTiers = [
        'tier1' => ['rpm' => 20, 'tpm' => 10000],
        'tier2' => ['rpm' => 500, 'tpm' => 40000],
        'tier3' => ['rpm' => 5000, 'tpm' => 80000],
        'tier4' => ['rpm' => 10000, 'tpm' => 1000000],
    ];

    public function __construct()
    {
        $this->apiKey = config('ai.openai.api_key');
    }

    /**
     * Make a rate-limited request to OpenAI Chat Completions API
     *
     * @param  array  $payload  The request payload
     * @param  string  $rateLimitKey  Optional custom rate limit key
     * @param  int  $retryCount  Current retry count to prevent infinite recursion
     * @return array|null|false Response data, null on failure, false if circuit open
     */
    public function chatCompletion(array $payload, ?string $rateLimitKey = null, int $retryCount = 0)
    {
        // Use default rate limit key if none provided
        $rateLimitKey = $rateLimitKey ?? 'openai-chat-completion';

        // Prevent infinite retries (max 1 retry)
        if ($retryCount > 1) {
            Log::warning('Max retries exceeded for OpenAI request', ['rate_limit_key' => $rateLimitKey]);

            return null;
        }

        // Check circuit breaker
        if ($this->isCircuitOpen($rateLimitKey)) {
            Log::warning("Circuit breaker is open for key: {$rateLimitKey}");

            return false; // Return false when circuit is open
        }

        // Check if we should queue the request instead
        if (isset($payload['queue_if_limited']) && $payload['queue_if_limited']) {
            unset($payload['queue_if_limited']);

            $estimatedTokens = $this->estimateTokens($payload);
            if (! $this->checkRateLimit($rateLimitKey, $estimatedTokens)) {
                // Queue the request for later processing
                Queue::push(\App\Jobs\ProcessOpenAiRequest::class, [
                    'payload' => $payload,
                    'rate_limit_key' => $rateLimitKey,
                ]);

                return ['queued' => true];
            }
        }

        // Estimate token usage for TPM limiting
        $estimatedTokens = $this->estimateTokens($payload);

        // Check both RPM and TPM limits (try legacy approach for backwards compatibility)
        if (! $this->checkRateLimit($rateLimitKey, $estimatedTokens)) {
            Log::warning('OpenAI rate limit exceeded', [
                'rate_limit_key' => $rateLimitKey,
                'estimated_tokens' => $estimatedTokens,
            ]);

            return null;
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(300)->post($this->baseUrl.'/chat/completions', $payload);

            // Parse rate limit headers from response
            $this->parseRateLimitHeaders($response->headers(), $rateLimitKey);

            if (! $response->successful()) {
                // Handle rate limit errors specifically
                if ($response->status() === 429) {
                    $this->recordFailure($rateLimitKey);

                    // Handle rate limiting - in testing, just return null without retrying
                    if (app()->environment('testing')) {
                        $this->handleRateLimitError($rateLimitKey);

                        return null;
                    }

                    // Parse Retry-After header and wait (only in production)
                    $retryAfter = $this->parseRetryAfter($response->headers());
                    if ($retryAfter > 0) {
                        Log::warning("Rate limited, waiting {$retryAfter} seconds", [
                            'rate_limit_key' => $rateLimitKey,
                            'retry_after' => $retryAfter,
                        ]);
                        sleep($retryAfter);

                        return $this->chatCompletion($payload, $rateLimitKey, $retryCount + 1);
                    }

                    // Implement exponential backoff with actual sleep (only in production)
                    $backoffSeconds = $this->handleRateLimitErrorWithSleep($rateLimitKey);
                    if ($backoffSeconds > 0) {
                        sleep($backoffSeconds);

                        return $this->chatCompletion($payload, $rateLimitKey, $retryCount + 1);
                    }

                    return null;
                }

                $this->recordFailure($rateLimitKey);
                Log::error('OpenAI API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);

                return null;
            }

            // Record successful request
            $this->recordSuccess($rateLimitKey);

            // Get actual token usage from response
            $responseData = $response->json();
            $actualTokens = $responseData['usage']['total_tokens'] ?? $estimatedTokens;

            // Update rate limits with actual token usage
            $this->updateRateLimitCounters($rateLimitKey, $actualTokens);

            return $responseData;

        } catch (Exception $e) {
            $this->recordFailure($rateLimitKey);
            Log::error('OpenAI API request exception', [
                'message' => $e->getMessage(),
                'rate_limit_key' => $rateLimitKey,
            ]);

            return null;
        }
    }

    /**
     * Check if request is within rate limits (both RPM and TPM)
     *
     * @param  string  $key  Rate limit key
     * @param  int  $estimatedTokens  Estimated tokens for this request
     * @return bool True if within limits
     */
    protected function checkRateLimit(string $key, int $estimatedTokens): bool
    {
        $rpmKey = "openai-rpm:{$key}";
        $tpmKey = "openai-tpm:{$key}";

        // Check RPM (Requests Per Minute)
        if (RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute)) {
            return false;
        }

        // Check TPM (Tokens Per Minute)
        $currentTokens = RateLimiter::attempts($tpmKey);
        $total = $currentTokens + $estimatedTokens;

        if ($total > $this->tokensPerMinute) {
            return false;
        }

        return true;
    }

    /**
     * Check rate limits with token bucket refill logic
     *
     * @param  string  $key  Rate limit key
     * @param  int  $estimatedTokens  Estimated tokens for this request
     * @return bool True if within limits
     */
    protected function checkRateLimitWithRefill(string $key, int $estimatedTokens): bool
    {
        $now = Carbon::now();
        $bucketKey = "openai-bucket:{$key}";

        // Get current bucket state
        $bucket = Cache::get($bucketKey, [
            'tokens' => $this->tokensPerMinute,
            'requests' => $this->requestsPerMinute,
            'last_refill' => $now->timestamp,
        ]);

        // Calculate time since last refill
        $timeDiff = $now->timestamp - $bucket['last_refill'];

        if ($timeDiff > 0) {
            // Refill tokens based on time passed
            $tokensToAdd = min(
                $this->tokensPerMinute - $bucket['tokens'],
                ($this->tokensPerMinute / 60) * $timeDiff // Tokens per second * seconds
            );
            $requestsToAdd = min(
                $this->requestsPerMinute - $bucket['requests'],
                ($this->requestsPerMinute / 60) * $timeDiff // Requests per second * seconds
            );

            $bucket['tokens'] = min($this->tokensPerMinute, $bucket['tokens'] + $tokensToAdd);
            $bucket['requests'] = min($this->requestsPerMinute, $bucket['requests'] + $requestsToAdd);
            $bucket['last_refill'] = $now->timestamp;
        }

        // Check if we have enough tokens and requests
        if ($bucket['tokens'] < $estimatedTokens || $bucket['requests'] < 1) {
            Cache::put($bucketKey, $bucket, 300); // Cache for 5 minutes

            return false;
        }

        // Consume tokens and requests
        $bucket['tokens'] -= $estimatedTokens;
        $bucket['requests'] -= 1;

        Cache::put($bucketKey, $bucket, 300);

        return true;
    }

    /**
     * Update rate limit counters after successful request
     *
     * @param  string  $key  Rate limit key
     * @param  int  $actualTokens  Actual tokens used
     */
    protected function updateRateLimitCounters(string $key, int $actualTokens): void
    {
        $rpmKey = "openai-rpm:{$key}";
        $tpmKey = "openai-tpm:{$key}";

        // Increment RPM counter by 1 (one request)
        RateLimiter::hit($rpmKey);

        // Increment TPM counter by actual token usage
        for ($i = 0; $i < $actualTokens; $i++) {
            RateLimiter::hit($tpmKey);
        }

        // Update metrics
        $this->updateMetrics($key, $actualTokens, true);
    }

    /**
     * Estimate token usage for a chat completion request
     *
     * @param  array  $payload  Request payload
     * @return int Estimated tokens
     */
    protected function estimateTokens(array $payload): int
    {
        $messages = $payload['messages'] ?? [];
        $maxTokens = $payload['max_tokens'] ?? 1000;

        // Rough estimation: 4 characters per token
        $inputTokens = 0;
        foreach ($messages as $message) {
            $content = $message['content'] ?? '';
            $inputTokens += ceil(strlen($content) / 4);
        }

        // Total estimate: input + max output tokens + buffer
        return $inputTokens + $maxTokens + 100;
    }

    /**
     * Handle rate limit errors with exponential backoff
     *
     * @param  string  $key  Rate limit key
     */
    protected function handleRateLimitError(string $key): void
    {
        $backoffKey = "openai-backoff:{$key}";

        // Implement exponential backoff
        $attempts = RateLimiter::attempts($backoffKey);
        $backoffSeconds = min(60, pow(2, $attempts)); // Max 60 seconds

        RateLimiter::increment($backoffKey);

        Log::warning('Implementing exponential backoff', [
            'key' => $key,
            'attempts' => $attempts,
            'backoff_seconds' => $backoffSeconds,
        ]);

        // In a real scenario, you might want to delay the job
        // For now, just log the recommended delay
    }

    /**
     * Handle rate limit errors with actual sleep
     *
     * @param  string  $key  Rate limit key
     * @return int Backoff seconds
     */
    protected function handleRateLimitErrorWithSleep(string $key): int
    {
        $backoffKey = "openai-backoff:{$key}";

        // Implement exponential backoff
        $attempts = Cache::get($backoffKey, 0);
        $backoffSeconds = min(60, pow(2, $attempts)); // Max 60 seconds

        Cache::put($backoffKey, $attempts + 1, 300); // Cache for 5 minutes

        Log::warning('Implementing exponential backoff with sleep', [
            'key' => $key,
            'attempts' => $attempts,
            'backoff_seconds' => $backoffSeconds,
        ]);

        return $backoffSeconds;
    }

    /**
     * Clear rate limits for a specific key (useful for testing)
     *
     * @param  string  $key  Rate limit key
     */
    public function clearRateLimit(string $key): void
    {
        RateLimiter::clear("openai-rpm:{$key}");
        RateLimiter::clear("openai-tpm:{$key}");
        RateLimiter::clear("openai-backoff:{$key}");

        // Also clear cache entries
        Cache::forget("openai-bucket:{$key}");
        Cache::forget("openai-circuit:{$key}");
        Cache::forget("openai-api-limits:{$key}");
        Cache::forget("openai-metrics:{$key}");
    }

    /**
     * Set rate limiter to specific values (useful for testing)
     *
     * @param  string  $key  Rate limit key
     * @param  int  $requests  Current request count
     * @param  int  $tokens  Current token count
     */
    public function setRateLimitForTesting(string $key, int $requests = 0, int $tokens = 0): void
    {
        $rpmKey = "openai-rpm:{$key}";
        $tpmKey = "openai-tpm:{$key}";

        // Clear existing counters
        RateLimiter::clear($rpmKey);
        RateLimiter::clear($tpmKey);

        // Set to specific values by incrementing in a loop
        for ($i = 0; $i < $requests; $i++) {
            RateLimiter::hit($rpmKey);
        }

        for ($i = 0; $i < $tokens; $i++) {
            RateLimiter::hit($tpmKey);
        }
    }

    /**
     * Get current rate limit status
     *
     * @param  string  $key  Rate limit key
     * @return array Current status
     */
    public function getRateLimitStatus(string $key): array
    {
        $rpmKey = "openai-rpm:{$key}";
        $tpmKey = "openai-tpm:{$key}";
        $bucketKey = "openai-bucket:{$key}";

        // Check if we have bucket data (new system) or should use RateLimiter (legacy)
        $bucket = Cache::get($bucketKey);

        if ($bucket) {
            // Use token bucket approach
            $rpmCurrent = $this->requestsPerMinute - $bucket['requests'];
            $tpmCurrent = $this->tokensPerMinute - $bucket['tokens'];

            return [
                'rpm' => [
                    'current' => max(0, $rpmCurrent),
                    'limit' => $this->requestsPerMinute,
                    'remaining' => max(0, $bucket['requests']),
                    'available_in' => $bucket['requests'] > 0 ? 0 : $this->calculateRefillTime($key, 'requests'),
                ],
                'tpm' => [
                    'current' => max(0, $tpmCurrent),
                    'limit' => $this->tokensPerMinute,
                    'remaining' => max(0, $bucket['tokens']),
                    'available_in' => $bucket['tokens'] > 0 ? 0 : $this->calculateRefillTime($key, 'tokens'),
                ],
            ];
        } else {
            // Fallback to RateLimiter for backwards compatibility with tests
            $rpmCurrent = RateLimiter::attempts($rpmKey);
            $tpmCurrent = RateLimiter::attempts($tpmKey);

            return [
                'rpm' => [
                    'current' => $rpmCurrent,
                    'limit' => $this->requestsPerMinute,
                    'remaining' => max(0, $this->requestsPerMinute - $rpmCurrent),
                    'available_in' => RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute)
                        ? RateLimiter::availableIn($rpmKey) : 0,
                ],
                'tpm' => [
                    'current' => $tpmCurrent,
                    'limit' => $this->tokensPerMinute,
                    'remaining' => max(0, $this->tokensPerMinute - $tpmCurrent),
                    'available_in' => RateLimiter::tooManyAttempts($tpmKey, $this->tokensPerMinute)
                        ? RateLimiter::availableIn($tpmKey) : 0,
                ],
            ];
        }
    }

    /**
     * Check if circuit breaker is open
     *
     * @param  string  $key  Rate limit key
     * @return bool True if circuit is open
     */
    public function isCircuitOpen(string $key): bool
    {
        $circuitKey = "openai-circuit:{$key}";
        $circuit = Cache::get($circuitKey);

        if (! $circuit || ! isset($circuit['opens_until'])) {
            return false;
        }

        // Check if timeout period has passed
        if (Carbon::now()->timestamp > $circuit['opens_until']) {
            Cache::forget($circuitKey);

            return false;
        }

        return true;
    }

    /**
     * Record a successful request
     *
     * @param  string  $key  Rate limit key
     */
    protected function recordSuccess(string $key): void
    {
        $circuitKey = "openai-circuit:{$key}";

        // Reset failure count on success
        Cache::forget($circuitKey);

        $this->updateMetrics($key, 0, true);
    }

    /**
     * Record a failed request
     *
     * @param  string  $key  Rate limit key
     */
    protected function recordFailure(string $key): void
    {
        $circuitKey = "openai-circuit:{$key}";
        $circuit = Cache::get($circuitKey, ['failures' => 0]);

        $circuit['failures']++;

        // Open circuit if threshold reached
        if ($circuit['failures'] >= $this->circuitBreakerThreshold) {
            $circuit['opens_until'] = Carbon::now()->addSeconds($this->circuitBreakerTimeout)->timestamp;
            Log::warning("Circuit breaker opened for key: {$key}", $circuit);
        }

        Cache::put($circuitKey, $circuit, 600); // Cache for 10 minutes

        $this->updateMetrics($key, 0, false);
    }

    /**
     * Parse Retry-After header from response
     *
     * @param  array  $headers  Response headers
     * @return int Seconds to wait
     */
    protected function parseRetryAfter(array $headers): int
    {
        foreach ($headers as $key => $values) {
            if (strtolower($key) === 'retry-after') {
                $value = is_array($values) ? $values[0] : $values;

                return (int) $value;
            }
        }

        return 0;
    }

    /**
     * Parse rate limit headers from OpenAI response
     *
     * @param  array  $headers  Response headers
     * @param  string  $key  Rate limit key
     */
    protected function parseRateLimitHeaders(array $headers, string $key): void
    {
        $rateLimitData = [];

        foreach ($headers as $headerKey => $values) {
            $headerKey = strtolower($headerKey);
            $value = is_array($values) ? $values[0] : $values;

            switch ($headerKey) {
                case 'x-ratelimit-limit-requests':
                    $rateLimitData['rpm_limit'] = (int) $value;
                    break;
                case 'x-ratelimit-remaining-requests':
                    $rateLimitData['rpm_remaining'] = (int) $value;
                    break;
                case 'x-ratelimit-reset-requests':
                    $rateLimitData['rpm_reset'] = (int) $value;
                    break;
                case 'x-ratelimit-limit-tokens':
                    $rateLimitData['tpm_limit'] = (int) $value;
                    break;
                case 'x-ratelimit-remaining-tokens':
                    $rateLimitData['tpm_remaining'] = (int) $value;
                    break;
                case 'x-ratelimit-reset-tokens':
                    $rateLimitData['tpm_reset'] = (int) $value;
                    break;
            }
        }

        if (! empty($rateLimitData)) {
            // Update our internal limits based on API response
            if (isset($rateLimitData['rpm_limit'])) {
                $this->requestsPerMinute = $rateLimitData['rpm_limit'];
            }
            if (isset($rateLimitData['tpm_limit'])) {
                $this->tokensPerMinute = $rateLimitData['tpm_limit'];
            }

            // Cache the API-reported limits
            Cache::put("openai-api-limits:{$key}", $rateLimitData, 300);
        }
    }

    /**
     * Get actual API limits from headers
     *
     * @param  string  $key  Rate limit key
     * @return array Actual limits from API
     */
    public function getActualApiLimits(string $key): array
    {
        $data = Cache::get("openai-api-limits:{$key}", []);

        return [
            'rpm' => $data['rpm_limit'] ?? $this->requestsPerMinute,
            'tpm' => $data['tpm_limit'] ?? $this->tokensPerMinute,
            'remaining_requests' => $data['rpm_remaining'] ?? null,
            'remaining_tokens' => $data['tpm_remaining'] ?? null,
        ];
    }

    /**
     * Process batch requests with proper spacing
     *
     * @param  array  $requests  Array of request payloads
     * @param  string  $key  Rate limit key
     * @return array Results
     */
    public function processBatch(array $requests, string $key): array
    {
        $results = [];
        $requestsPerSecond = $this->requestsPerMinute / 60;
        $delayBetweenRequests = 1 / $requestsPerSecond; // Seconds between requests

        foreach ($requests as $i => $request) {
            if ($i > 0 && ! app()->environment('testing')) {
                // Sleep to maintain rate limit (only if not in testing)
                sleep((int) ceil($delayBetweenRequests));
            }

            $result = $this->chatCompletion($request, $key);
            $results[] = $result;

            // Stop if we get rate limited or circuit opens
            if ($result === null || $result === false) {
                break;
            }
        }

        return $results;
    }

    /**
     * Get current API tier based on limits
     *
     * @return string Current tier
     */
    public function getCurrentTier(): string
    {
        foreach ($this->apiTiers as $tier => $limits) {
            if ($this->requestsPerMinute <= $limits['rpm'] && $this->tokensPerMinute <= $limits['tpm']) {
                return $tier;
            }
        }

        return 'tier1'; // Default
    }

    /**
     * Update metrics for monitoring
     *
     * @param  string  $key  Rate limit key
     * @param  int  $tokens  Tokens used
     * @param  bool  $success  Whether request was successful
     */
    protected function updateMetrics(string $key, int $tokens, bool $success): void
    {
        $metricsKey = "openai-metrics:{$key}";
        $metrics = Cache::get($metricsKey, [
            'total_requests' => 0,
            'successful_requests' => 0,
            'rate_limited_requests' => 0,
            'total_tokens' => 0,
            'circuit_breaker_trips' => 0,
        ]);

        $metrics['total_requests']++;
        $metrics['total_tokens'] += $tokens;

        if ($success) {
            $metrics['successful_requests']++;
        } else {
            $metrics['rate_limited_requests']++;
        }

        Cache::put($metricsKey, $metrics, 3600); // Cache for 1 hour
    }

    /**
     * Get comprehensive metrics
     *
     * @param  string  $key  Rate limit key
     * @return array Metrics
     */
    public function getMetrics(string $key): array
    {
        $metricsKey = "openai-metrics:{$key}";
        $metrics = Cache::get($metricsKey, [
            'total_requests' => 0,
            'successful_requests' => 0,
            'rate_limited_requests' => 0,
            'total_tokens' => 0,
            'circuit_breaker_trips' => 0,
        ]);

        $metrics['average_tokens_per_request'] = $metrics['total_requests'] > 0
            ? $metrics['total_tokens'] / $metrics['total_requests']
            : 0;

        $metrics['current_tier'] = $this->getCurrentTier();

        $limits = $this->getActualApiLimits($key);
        $metrics['time_until_reset'] = $this->calculateRefillTime($key, 'requests');

        return $metrics;
    }

    /**
     * Calculate time until bucket refill
     *
     * @param  string  $key  Rate limit key
     * @param  string  $type  'requests' or 'tokens'
     * @return int Seconds until refill
     */
    protected function calculateRefillTime(string $key, string $type): int
    {
        $bucketKey = "openai-bucket:{$key}";
        $bucket = Cache::get($bucketKey);

        if (! $bucket) {
            return 0;
        }

        $limit = $type === 'requests' ? $this->requestsPerMinute : $this->tokensPerMinute;
        $current = $bucket[$type] ?? $limit;

        if ($current > 0) {
            return 0;
        }

        // Calculate refill rate (per second)
        $refillRate = $limit / 60;
        $tokensNeeded = 1; // Minimum needed

        return (int) ceil($tokensNeeded / $refillRate);
    }
}
