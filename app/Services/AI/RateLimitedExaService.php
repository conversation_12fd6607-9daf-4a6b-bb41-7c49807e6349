<?php

namespace App\Services\AI;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\RateLimiter;

class RateLimitedExaService
{
    protected $apiKey;
    protected $baseUrl = 'https://api.exa.ai';
    
    // Rate limiting configuration based on Exa limits (5 requests per second = 300 per minute)
    protected $requestsPerMinute = 300;
    
    // Circuit breaker settings
    protected $circuitBreakerThreshold = 5; // Max consecutive failures
    protected $circuitBreakerTimeout = 300; // 5 minutes
    
    public function __construct()
    {
        $this->apiKey = config('ai.exa.api_key');
    }
    
    /**
     * Make a rate-limited request to Exa Search API
     *
     * @param array $payload The request payload
     * @param string $rateLimitKey Optional custom rate limit key
     * @param int $retryCount Current retry count to prevent infinite recursion
     * @return array|null|false Response data, null on failure, false if circuit open
     */
    public function search(array $payload, ?string $rateLimitKey = null, int $retryCount = 0)
    {
        // Use default rate limit key if none provided
        $rateLimitKey = $rateLimitKey ?? 'exa-search';
        
        // Prevent infinite retries (max 1 retry)
        if ($retryCount > 1) {
            Log::warning('Max retries exceeded for Exa request', ['rate_limit_key' => $rateLimitKey]);
            return null;
        }
        
        // Check circuit breaker
        if ($this->isCircuitOpen($rateLimitKey)) {
            Log::warning("Circuit breaker is open for key: {$rateLimitKey}");
            return false; // Return false when circuit is open
        }
        
        // Check if we should queue the request instead
        if (isset($payload['queue_if_limited']) && $payload['queue_if_limited']) {
            unset($payload['queue_if_limited']);
            
            if (!$this->checkRateLimit($rateLimitKey)) {
                // Queue the request for later processing
                Queue::push(\App\Jobs\ProcessExaRequest::class, [
                    'payload' => $payload,
                    'rate_limit_key' => $rateLimitKey,
                    'endpoint' => 'search',
                ]);
                
                return ['queued' => true];
            }
        }
        
        // Check rate limits
        if (!$this->checkRateLimit($rateLimitKey)) {
            Log::warning("Exa API rate limit exceeded", [
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
        
        return $this->makeRequest('/search', $payload, $rateLimitKey, $retryCount);
    }
    
    /**
     * Make a rate-limited request to Exa Get Contents API
     *
     * @param array $payload The request payload
     * @param string $rateLimitKey Optional custom rate limit key
     * @param int $retryCount Current retry count to prevent infinite recursion
     * @return array|null|false Response data, null on failure, false if circuit open
     */
    public function getContents(array $payload, ?string $rateLimitKey = null, int $retryCount = 0)
    {
        // Use default rate limit key if none provided
        $rateLimitKey = $rateLimitKey ?? 'exa-contents';
        
        // Prevent infinite retries (max 1 retry)
        if ($retryCount > 1) {
            Log::warning('Max retries exceeded for Exa request', ['rate_limit_key' => $rateLimitKey]);
            return null;
        }
        
        // Check circuit breaker
        if ($this->isCircuitOpen($rateLimitKey)) {
            Log::warning("Circuit breaker is open for key: {$rateLimitKey}");
            return false; // Return false when circuit is open
        }
        
        // Check if we should queue the request instead
        if (isset($payload['queue_if_limited']) && $payload['queue_if_limited']) {
            unset($payload['queue_if_limited']);
            
            if (!$this->checkRateLimit($rateLimitKey)) {
                // Queue the request for later processing
                Queue::push(\App\Jobs\ProcessExaRequest::class, [
                    'payload' => $payload,
                    'rate_limit_key' => $rateLimitKey,
                    'endpoint' => 'contents',
                ]);
                
                return ['queued' => true];
            }
        }
        
        // Check rate limits
        if (!$this->checkRateLimit($rateLimitKey)) {
            Log::warning("Exa API rate limit exceeded", [
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
        
        return $this->makeRequest('/contents', $payload, $rateLimitKey, $retryCount);
    }
    
    /**
     * Make a rate-limited request to Exa Find Similar API
     *
     * @param array $payload The request payload
     * @param string $rateLimitKey Optional custom rate limit key
     * @param int $retryCount Current retry count to prevent infinite recursion
     * @return array|null|false Response data, null on failure, false if circuit open
     */
    public function findSimilar(array $payload, ?string $rateLimitKey = null, int $retryCount = 0)
    {
        // Use default rate limit key if none provided
        $rateLimitKey = $rateLimitKey ?? 'exa-similar';
        
        // Prevent infinite retries (max 1 retry)
        if ($retryCount > 1) {
            Log::warning('Max retries exceeded for Exa request', ['rate_limit_key' => $rateLimitKey]);
            return null;
        }
        
        // Check circuit breaker
        if ($this->isCircuitOpen($rateLimitKey)) {
            Log::warning("Circuit breaker is open for key: {$rateLimitKey}");
            return false; // Return false when circuit is open
        }
        
        // Check if we should queue the request instead
        if (isset($payload['queue_if_limited']) && $payload['queue_if_limited']) {
            unset($payload['queue_if_limited']);
            
            if (!$this->checkRateLimit($rateLimitKey)) {
                // Queue the request for later processing
                Queue::push(\App\Jobs\ProcessExaRequest::class, [
                    'payload' => $payload,
                    'rate_limit_key' => $rateLimitKey,
                    'endpoint' => 'findSimilarLinks',
                ]);
                
                return ['queued' => true];
            }
        }
        
        // Check rate limits
        if (!$this->checkRateLimit($rateLimitKey)) {
            Log::warning("Exa API rate limit exceeded", [
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
        
        return $this->makeRequest('/findSimilarLinks', $payload, $rateLimitKey, $retryCount);
    }
    
    /**
     * Make the actual HTTP request
     *
     * @param string $endpoint API endpoint
     * @param array $payload Request payload
     * @param string $rateLimitKey Rate limit key
     * @param int $retryCount Current retry count
     * @return array|null Response data or null on failure
     */
    protected function makeRequest(string $endpoint, array $payload, string $rateLimitKey, int $retryCount = 0)
    {
        try {
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(300)->post($this->baseUrl . $endpoint, $payload);
            
            // Parse rate limit headers from response
            $this->parseRateLimitHeaders($response->headers(), $rateLimitKey);
            
            if (!$response->successful()) {
                // Handle rate limit errors specifically
                if ($response->status() === 429) {
                    $this->recordFailure($rateLimitKey);
                    
                    // Handle rate limiting - in testing, just return null without retrying
                    if (app()->environment('testing')) {
                        $this->handleRateLimitError($rateLimitKey);
                        return null;
                    }
                    
                    // Parse Retry-After header and wait (only in production)
                    $retryAfter = $this->parseRetryAfter($response->headers());
                    if ($retryAfter > 0) {
                        Log::warning("Rate limited, waiting {$retryAfter} seconds", [
                            'rate_limit_key' => $rateLimitKey,
                            'retry_after' => $retryAfter,
                        ]);
                        sleep($retryAfter);
                        
                        return $this->makeRequest($endpoint, $payload, $rateLimitKey, $retryCount + 1);
                    }
                    
                    // Implement exponential backoff with actual sleep (only in production)
                    $backoffSeconds = $this->handleRateLimitErrorWithSleep($rateLimitKey);
                    if ($backoffSeconds > 0) {
                        sleep($backoffSeconds);
                        
                        return $this->makeRequest($endpoint, $payload, $rateLimitKey, $retryCount + 1);
                    }
                    
                    return null;
                }
                
                $this->recordFailure($rateLimitKey);
                Log::error("Exa API request failed", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return null;
            }
            
            // Record successful request
            $this->recordSuccess($rateLimitKey);
            
            // Update rate limits on successful request
            $this->updateRateLimitCounters($rateLimitKey);
            
            return $response->json();
            
        } catch (Exception $e) {
            $this->recordFailure($rateLimitKey);
            Log::error("Exa API request exception", [
                'message' => $e->getMessage(),
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
    }
    
    /**
     * Check if request is within rate limits
     *
     * @param string $key Rate limit key
     * @return bool True if within limits
     */
    protected function checkRateLimit(string $key): bool
    {
        $rpmKey = "exa-rpm:{$key}";
        
        // Check RPM (Requests Per Minute)
        if (RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check rate limits with token bucket refill logic
     *
     * @param string $key Rate limit key
     * @return bool True if within limits
     */
    protected function checkRateLimitWithRefill(string $key): bool
    {
        $now = Carbon::now();
        $bucketKey = "exa-bucket:{$key}";
        
        // Get current bucket state
        $bucket = Cache::get($bucketKey, [
            'requests' => $this->requestsPerMinute,
            'last_refill' => $now->timestamp,
        ]);
        
        // Calculate time since last refill
        $timeDiff = $now->timestamp - $bucket['last_refill'];
        
        if ($timeDiff > 0) {
            // Refill requests based on time passed
            $requestsToAdd = min(
                $this->requestsPerMinute - $bucket['requests'],
                ($this->requestsPerMinute / 60) * $timeDiff // Requests per second * seconds
            );
            
            $bucket['requests'] = min($this->requestsPerMinute, $bucket['requests'] + $requestsToAdd);
            $bucket['last_refill'] = $now->timestamp;
        }
        
        // Check if we have enough requests
        if ($bucket['requests'] < 1) {
            Cache::put($bucketKey, $bucket, 300); // Cache for 5 minutes
            return false;
        }
        
        // Consume request
        $bucket['requests'] -= 1;
        
        Cache::put($bucketKey, $bucket, 300);
        
        return true;
    }
    
    /**
     * Update rate limit counters after successful request
     *
     * @param string $key Rate limit key
     */
    protected function updateRateLimitCounters(string $key): void
    {
        $rpmKey = "exa-rpm:{$key}";
        
        // Increment RPM counter by 1 (one request)
        RateLimiter::hit($rpmKey);
        
        // Update metrics
        $this->updateMetrics($key, 0, true);
    }
    
    /**
     * Handle rate limit errors with exponential backoff
     *
     * @param string $key Rate limit key
     */
    protected function handleRateLimitError(string $key): void
    {
        $backoffKey = "exa-backoff:{$key}";
        
        // Implement exponential backoff
        $attempts = RateLimiter::attempts($backoffKey);
        $backoffSeconds = min(60, pow(2, $attempts)); // Max 60 seconds
        
        RateLimiter::increment($backoffKey);
        
        Log::warning("Implementing exponential backoff for Exa API", [
            'key' => $key,
            'attempts' => $attempts,
            'backoff_seconds' => $backoffSeconds
        ]);
    }
    
    /**
     * Handle rate limit errors with actual sleep
     *
     * @param string $key Rate limit key
     * @return int Backoff seconds
     */
    protected function handleRateLimitErrorWithSleep(string $key): int
    {
        $backoffKey = "exa-backoff:{$key}";
        
        // Implement exponential backoff
        $attempts = Cache::get($backoffKey, 0);
        $backoffSeconds = min(60, pow(2, $attempts)); // Max 60 seconds
        
        Cache::put($backoffKey, $attempts + 1, 300); // Cache for 5 minutes
        
        Log::warning('Implementing exponential backoff with sleep for Exa', [
            'key' => $key,
            'attempts' => $attempts,
            'backoff_seconds' => $backoffSeconds,
        ]);
        
        return $backoffSeconds;
    }
    
    /**
     * Parse Retry-After header from response
     *
     * @param array $headers Response headers
     * @return int Seconds to wait
     */
    protected function parseRetryAfter(array $headers): int
    {
        foreach ($headers as $key => $values) {
            if (strtolower($key) === 'retry-after') {
                $value = is_array($values) ? $values[0] : $values;
                return (int) $value;
            }
        }
        
        return 0;
    }
    
    /**
     * Parse rate limit headers from Exa response
     *
     * @param array $headers Response headers
     * @param string $key Rate limit key
     */
    protected function parseRateLimitHeaders(array $headers, string $key): void
    {
        $rateLimitData = [];
        
        foreach ($headers as $headerKey => $values) {
            $headerKey = strtolower($headerKey);
            $value = is_array($values) ? $values[0] : $values;
            
            switch ($headerKey) {
                case 'x-ratelimit-limit':
                case 'ratelimit-limit':
                    $rateLimitData['rpm_limit'] = (int) $value;
                    break;
                case 'x-ratelimit-remaining':
                case 'ratelimit-remaining':
                    $rateLimitData['rpm_remaining'] = (int) $value;
                    break;
                case 'x-ratelimit-reset':
                case 'ratelimit-reset':
                    $rateLimitData['rpm_reset'] = $value;
                    break;
            }
        }
        
        if (!empty($rateLimitData)) {
            // Update our internal limits based on API response
            if (isset($rateLimitData['rpm_limit'])) {
                $this->requestsPerMinute = $rateLimitData['rpm_limit'];
            }
            
            // Cache the API-reported limits
            Cache::put("exa-api-limits:{$key}", $rateLimitData, 300);
        }
    }
    
    /**
     * Check if circuit breaker is open
     *
     * @param string $key Rate limit key
     * @return bool True if circuit is open
     */
    public function isCircuitOpen(string $key): bool
    {
        $circuitKey = "exa-circuit:{$key}";
        $circuit = Cache::get($circuitKey);
        
        if (!$circuit || !isset($circuit['opens_until'])) {
            return false;
        }
        
        // Check if timeout period has passed
        if (Carbon::now()->timestamp > $circuit['opens_until']) {
            Cache::forget($circuitKey);
            return false;
        }
        
        return true;
    }
    
    /**
     * Record a successful request
     *
     * @param string $key Rate limit key
     */
    protected function recordSuccess(string $key): void
    {
        $circuitKey = "exa-circuit:{$key}";
        
        // Reset failure count on success
        Cache::forget($circuitKey);
        
        $this->updateMetrics($key, 0, true);
    }
    
    /**
     * Record a failed request
     *
     * @param string $key Rate limit key
     */
    protected function recordFailure(string $key): void
    {
        $circuitKey = "exa-circuit:{$key}";
        $circuit = Cache::get($circuitKey, ['failures' => 0]);
        
        $circuit['failures']++;
        
        // Open circuit if threshold reached
        if ($circuit['failures'] >= $this->circuitBreakerThreshold) {
            $circuit['opens_until'] = Carbon::now()->addSeconds($this->circuitBreakerTimeout)->timestamp;
            Log::warning("Circuit breaker opened for key: {$key}", $circuit);
        }
        
        Cache::put($circuitKey, $circuit, 600); // Cache for 10 minutes
        
        $this->updateMetrics($key, 0, false);
    }
    
    /**
     * Clear rate limits for a specific key (useful for testing)
     *
     * @param string $key Rate limit key
     */
    public function clearRateLimit(string $key): void
    {
        RateLimiter::clear("exa-rpm:{$key}");
        RateLimiter::clear("exa-backoff:{$key}");
        
        // Also clear cache entries
        Cache::forget("exa-bucket:{$key}");
        Cache::forget("exa-circuit:{$key}");
        Cache::forget("exa-api-limits:{$key}");
        Cache::forget("exa-metrics:{$key}");
    }
    
    /**
     * Get current rate limit status
     *
     * @param string $key Rate limit key
     * @return array Current status
     */
    public function getRateLimitStatus(string $key): array
    {
        $rpmKey = "exa-rpm:{$key}";
        $bucketKey = "exa-bucket:{$key}";
        
        // Check if we have bucket data (new system) or should use RateLimiter (legacy)
        $bucket = Cache::get($bucketKey);
        
        if ($bucket) {
            // Use token bucket approach
            $rpmCurrent = $this->requestsPerMinute - $bucket['requests'];
            
            return [
                'rpm' => [
                    'current' => max(0, $rpmCurrent),
                    'limit' => $this->requestsPerMinute,
                    'remaining' => max(0, $bucket['requests']),
                    'available_in' => $bucket['requests'] > 0 ? 0 : $this->calculateRefillTime($key, 'requests'),
                ],
            ];
        } else {
            // Fallback to RateLimiter for backwards compatibility
            $rpmCurrent = RateLimiter::attempts($rpmKey);
            
            return [
                'rpm' => [
                    'current' => $rpmCurrent,
                    'limit' => $this->requestsPerMinute,
                    'remaining' => max(0, $this->requestsPerMinute - $rpmCurrent),
                    'available_in' => RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute) 
                        ? RateLimiter::availableIn($rpmKey) : 0
                ]
            ];
        }
    }
    
    /**
     * Process batch requests with proper spacing
     *
     * @param array $requests Array of request payloads
     * @param string $key Rate limit key
     * @param string $method Method to call ('search', 'getContents', 'findSimilar')
     * @return array Results
     */
    public function processBatch(array $requests, string $key, string $method = 'search'): array
    {
        $results = [];
        $requestsPerSecond = $this->requestsPerMinute / 60;
        $delayBetweenRequests = 1 / $requestsPerSecond; // Seconds between requests
        
        foreach ($requests as $i => $request) {
            if ($i > 0 && !app()->environment('testing')) {
                // Sleep to maintain rate limit (only if not in testing)
                sleep((int) ceil($delayBetweenRequests));
            }
            
            $result = $this->$method($request, $key);
            $results[] = $result;
            
            // Stop if we get rate limited or circuit opens
            if ($result === null || $result === false) {
                break;
            }
        }
        
        return $results;
    }
    
    /**
     * Update metrics for monitoring
     *
     * @param string $key Rate limit key
     * @param int $requests Number of requests (always 1 for Exa)
     * @param bool $success Whether request was successful
     */
    protected function updateMetrics(string $key, int $requests, bool $success): void
    {
        $metricsKey = "exa-metrics:{$key}";
        $metrics = Cache::get($metricsKey, [
            'total_requests' => 0,
            'successful_requests' => 0,
            'rate_limited_requests' => 0,
            'circuit_breaker_trips' => 0,
        ]);
        
        $metrics['total_requests']++;
        
        if ($success) {
            $metrics['successful_requests']++;
        } else {
            $metrics['rate_limited_requests']++;
        }
        
        Cache::put($metricsKey, $metrics, 3600); // Cache for 1 hour
    }
    
    /**
     * Get comprehensive metrics
     *
     * @param string $key Rate limit key
     * @return array Metrics
     */
    public function getMetrics(string $key): array
    {
        $metricsKey = "exa-metrics:{$key}";
        $metrics = Cache::get($metricsKey, [
            'total_requests' => 0,
            'successful_requests' => 0,
            'rate_limited_requests' => 0,
            'circuit_breaker_trips' => 0,
        ]);
        
        $metrics['success_rate'] = $metrics['total_requests'] > 0
            ? $metrics['successful_requests'] / $metrics['total_requests']
            : 0;
        
        $metrics['time_until_reset'] = $this->calculateRefillTime($key, 'requests');
        
        return $metrics;
    }
    
    /**
     * Calculate time until bucket refill
     *
     * @param string $key Rate limit key
     * @param string $type 'requests'
     * @return int Seconds until refill
     */
    protected function calculateRefillTime(string $key, string $type): int
    {
        $bucketKey = "exa-bucket:{$key}";
        $bucket = Cache::get($bucketKey);
        
        if (!$bucket) {
            return 0;
        }
        
        $limit = $this->requestsPerMinute;
        $current = $bucket['requests'] ?? $limit;
        
        if ($current > 0) {
            return 0;
        }
        
        // Calculate refill rate (per second)
        $refillRate = $limit / 60;
        $requestsNeeded = 1; // Minimum needed
        
        return (int) ceil($requestsNeeded / $refillRate);
    }
}