<?php

namespace App\Services\AI;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ResearchFunctionality
{
    /**
     * API key for making requests to OpenAI
     */
    protected $openAiApiKey;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->openAiApiKey = config('ai.openai.api_key');
    }
    
    /**
     * Perform research on a given question using GPT-4 with web search capabilities
     * 
     * @param string $query The user's question
     * @param array $contextMessages Previous messages in the conversation
     * @return array The AI response
     */
    public function performResearch($query, $contextMessages = [])
    {
        
        // Prepare system prompt for research
        $systemPrompt = $this->prepareSystemPrompt();
        
        // Prepare the conversation context
        $messages = $this->prepareConversationContext($systemPrompt, $query, $contextMessages);
        
        $queryType = $this->analyzeQueryType($query);
        
        // Prepare the API payload
        $payload = $this->prepareApiPayload($messages);
        
        // Make the API call
        $response = $this->executeApiCall($payload);
        
        if (!$response) {
            return [
                [
                    "type" => "text",
                    "text" => "I'm sorry, I encountered an issue while researching your question. Please try again."
                ]
            ];
        }
        
        // Process and format the response
        $formattedResponse = $this->formatResponse($response);
        
        return $formattedResponse;
    }
    
    /**
     * Analyze the query type for logging purposes
     * 
     * @param string $query The user's query
     * @return string The identified query type
     */
    protected function analyzeQueryType($query)
    {
        $query = strtolower($query);
        
        // Leadership structure queries
        if (preg_match('/(who (is|are)|list|executives|leadership|board|exco|committee|c-suite|management team)/', $query)) {
            return 'leadership_structure';
        }
        
        // Talent profile queries
        if (preg_match('/(background|experience|profile|career|history|resume|cv|bio|about)/', $query)) {
            return 'talent_profile';
        }
        
        // Industry trends queries
        if (preg_match('/(trend|pattern|development|shift|movement|change|emerging|recent|current|forecast)/', $query)) {
            return 'industry_trends';
        }
        
        // Best practices queries
        if (preg_match('/(how to|best practice|approach|method|strategy|framework|process|implement|improve)/', $query)) {
            return 'best_practices';
        }
        
        // Default to general query
        return 'general';
    }
    
    /**
     * Prepare the system prompt for research
     */
    protected function prepareSystemPrompt()
    {
        return <<<EOT
<system>
You are an expert research assistant for succession planning and leadership intelligence. Your purpose is to provide thorough, accurate, and well-structured information to support executive talent management and organizational planning.
</system>

<search_guidelines>
Adapt your search and response strategy based on the query type:

1. <leadership_structure_queries>
   For questions about executives, boards, or organizational leadership:
   - Always retrieve the COMPLETE leadership structure
   - Include full names and titles for ALL members, not just top executives
   - Interpret "Who is/are in X" as "List all members of X"
   - Treat "leadership", "executive committee", "exco", "board", "C-suite" as signals to provide comprehensive lists
</leadership_structure_queries>

2. <talent_profile_queries>
   For questions about specific executive backgrounds and careers:
   - Provide comprehensive career history and education
   - Include significant achievements and leadership experience
   - Mention industry expertise and notable professional transitions
   - Note relevant skills that would be valuable for succession planning
</talent_profile_queries>

3. <industry_trends_queries>
   For questions about talent landscape and industry patterns:
   - Gather data from multiple sources including recent reports
   - Present relevant statistics and emerging patterns
   - Focus on information that impacts leadership planning and talent strategies
   - Include insights on talent mobility and leadership transitions
</industry_trends_queries>

4. <best_practices_queries>
   For questions about succession planning methods and frameworks:
   - Provide actionable frameworks and methodologies
   - Include specific strategies from successful organizations
   - Offer structured approaches with clear implementation steps
   - Emphasize practical guidance over theoretical concepts
</best_practices_queries>
</search_guidelines>

<response_format>
- Structure your response appropriately for the query type
- For leadership lists, use bullet points with name: title format
- For executive profiles, organize by career progression
- For best practices, use clear, implementable steps
- Include relevant details but avoid unnecessary introductions
- Present information in plain text without markdown formatting
- Do not include citations, links, or references to sources
- Do not include any disclaimers about information changes
</response_format>

<examples>
Query: "Who are the executive committee members at JP Morgan?"
Response type: Leadership Structure
Response:
As of [current date], JP Morgan's Executive Committee comprises:
- Jamie Dimon: Chairman and CEO
- Daniel Pinto: President and COO
- Mary Erdoes: CEO of Asset & Wealth Management
- [Complete list of all executives with their titles]

Query: "What is Jane Smith's background at Goldman Sachs?"
Response type: Talent Profile
Response:
Jane Smith has over 20 years of experience in investment banking. Her career at Goldman Sachs includes:
- Current role: Global Head of Investment Banking (since 2019)
- Previous positions: Head of EMEA Banking (2015-2019), Co-head of M&A (2010-2015)
- Education: MBA from Harvard Business School, BA in Economics from Princeton
- Notable achievements: Led the firm's expansion in sustainable finance, orchestrated major reorganization of the division that increased profitability by 30%

Query: "What are current trends in financial services leadership transitions?"
Response type: Industry Trends
Response:
Key trends in financial services leadership transitions include:
1. Increasing focus on digital transformation experience for incoming CEOs
2. Rising appointments of executives with sustainability and ESG expertise
3. Greater emphasis on diverse leadership pipelines, with 45% more women appointed to C-suite roles compared to five years ago
4. Cross-industry hiring, particularly bringing technology leaders into financial services firms

Query: "How should we identify high-potential leaders for succession planning?"
Response type: Best Practices
Response:
Effective approaches to identifying high-potential leaders include:
1. Implement a structured assessment framework combining performance metrics and leadership competencies
2. Use 360-degree feedback paired with objective talent reviews
3. Create stretch assignments to evaluate adaptability and growth potential
4. Develop clear criteria for measuring leadership capabilities aligned with future business needs
5. Establish regular talent review cadence with senior leadership involvement
</examples>
EOT;
    }
    
    /**
     * Prepare the conversation context for the API call
     */
    protected function prepareConversationContext($systemPrompt, $query, $contextMessages)
    {
        // Ensure system message is always at the beginning
        if (empty($contextMessages) || (!isset($contextMessages[0]) || $contextMessages[0]['role'] !== 'system')) {
            $messages = [['role' => 'system', 'content' => $systemPrompt]];
        } else {
            $messages = $contextMessages;
        }
        
        // Append the latest user message
        $today = date('Y-m-d');
        $messages[] = [
            'role' => 'user',
            'content' => "Please provide a concise and well-formatted response to the following request: {$query}. Today's date is {$today}."
        ];
        
        // Retain only the last few exchanges (keeping system prompt intact)
        return array_merge([$messages[0]], array_slice($messages, -7));
    }
    
    /**
     * Prepare the API payload for the research query
     */
    protected function prepareApiPayload($messages)
    {
        return [
            "model" => 'gpt-4o-mini-search-preview-2025-03-11',
            "messages" => $messages,
            "max_tokens" => 2000
        ];
    }
    
    /**
     * Execute the API call to OpenAI
     */
    protected function executeApiCall($payload)
    {
        $requestId = uniqid('api_');
        
        try {
            
            // OpenAI API - using config value for API key
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->openAiApiKey,
                'Content-Type'  => 'application/json',
            ])->timeout(60)->post('https://api.openai.com/v1/chat/completions', $payload);
            
            if (!$response->successful()) {
                Log::error("API call failed", [
                    'status' => $response->status(),
                    'error' => $response->json()['error']['message'] ?? $response->body()
                ]);
                return null;
            }
            
            
            return $response->json();
            
        } catch (\Exception $e) {
            Log::error("API exception: {$e->getMessage()}");
            return null;
        }
    }
    
    /**
     * Estimate token count for logging purposes
     * This is a rough approximation; actual token count depends on the tokenizer
     * 
     * @param array $messages The message array
     * @return int Approximate token count
     */
    protected function estimateTokenCount($messages)
    {
        $totalTokens = 0;
        
        foreach ($messages as $message) {
            // Average of 4 characters per token as a rough approximation
            $totalTokens += ceil(strlen($message['content']) / 4);
        }
        
        return $totalTokens;
    }
    
    /**
     * Format the API response for display
     */
    protected function formatResponse($data)
    {
        $responseMessage = $data['choices'][0]['message']['content'] ?? "No response received.";
        
        // Remove bold formatting and citations
        $responseMessage = preg_replace('/\*\*(.*?)\*\*/', '$1', $responseMessage);
        $responseMessage = preg_replace('/\[(.*?)\]\(.*?\)/', '$1', $responseMessage);
        
        // Format for display
        return [
            [
                "type" => "text",
                "text" => $responseMessage
            ]
        ];
    }
}