<?php

namespace App\Services\AI;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\RateLimiter;

class RateLimitedAnthropicService
{
    protected $apiKey;
    protected $baseUrl = 'https://api.anthropic.com/v1';
    
    // Rate limiting configuration based on Anthropic Tier 1 limits
    protected $requestsPerMinute = 50;     // RPM
    protected $inputTokensPerMinute = 40000; // ITPM (using Claude 3.5 Sonnet limit)
    protected $outputTokensPerMinute = 8000; // OTPM (using Claude 3.5 Sonnet limit)
    
    // Circuit breaker settings
    protected $circuitBreakerThreshold = 5; // Max consecutive failures
    protected $circuitBreakerTimeout = 300; // 5 minutes
    
    // API tier detection for Anthropic
    protected $apiTiers = [
        'tier1' => ['rpm' => 50, 'itpm' => 40000, 'otpm' => 8000],
        'tier2' => ['rpm' => 1000, 'itpm' => 80000, 'otpm' => 16000],
        'tier3' => ['rpm' => 2000, 'itpm' => 160000, 'otpm' => 32000],
        'tier4' => ['rpm' => 4000, 'itpm' => 400000, 'otpm' => 80000],
    ];
    
    public function __construct()
    {
        $this->apiKey = config('ai.anthropic.api_key');
    }
    
    /**
     * Make a rate-limited request to Anthropic Messages API
     *
     * @param array $payload The request payload
     * @param string $rateLimitKey Optional custom rate limit key
     * @param int $retryCount Current retry count to prevent infinite recursion
     * @return array|null|false Response data, null on failure, false if circuit open
     */
    public function messages(array $payload, ?string $rateLimitKey = null, int $retryCount = 0)
    {
        // Use default rate limit key if none provided
        $rateLimitKey = $rateLimitKey ?? 'anthropic-messages';
        
        // Prevent infinite retries (max 1 retry)
        if ($retryCount > 1) {
            Log::warning('Max retries exceeded for Anthropic request', ['rate_limit_key' => $rateLimitKey]);
            return null;
        }
        
        // Check circuit breaker
        if ($this->isCircuitOpen($rateLimitKey)) {
            Log::warning("Circuit breaker is open for key: {$rateLimitKey}");
            return false; // Return false when circuit is open
        }
        
        // Check if we should queue the request instead
        if (isset($payload['queue_if_limited']) && $payload['queue_if_limited']) {
            unset($payload['queue_if_limited']);
            
            $estimatedInputTokens = $this->estimateInputTokens($payload);
            $estimatedOutputTokens = $payload['max_tokens'] ?? 1000;
            
            if (!$this->checkRateLimit($rateLimitKey, $estimatedInputTokens, $estimatedOutputTokens)) {
                // Queue the request for later processing
                Queue::push(\App\Jobs\ProcessAnthropicRequest::class, [
                    'payload' => $payload,
                    'rate_limit_key' => $rateLimitKey,
                ]);
                
                return ['queued' => true];
            }
        }
        
        // Estimate token usage for ITPM/OTPM limiting
        $estimatedInputTokens = $this->estimateInputTokens($payload);
        $estimatedOutputTokens = $payload['max_tokens'] ?? 1000;
        
        // Check all rate limits (RPM, ITPM, OTPM)
        if (!$this->checkRateLimit($rateLimitKey, $estimatedInputTokens, $estimatedOutputTokens)) {
            Log::warning("Anthropic API rate limit exceeded", [
                'rate_limit_key' => $rateLimitKey,
                'estimated_input_tokens' => $estimatedInputTokens,
                'estimated_output_tokens' => $estimatedOutputTokens
            ]);
            return null;
        }
        
        try {
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'anthropic-version' => '2023-06-01'
            ])->timeout(300)->post($this->baseUrl . '/messages', $payload);
            
            if (!$response->successful()) {
                // Handle rate limit errors specifically
                if ($response->status() === 429) {
                    Log::warning("Anthropic API rate limit hit", [
                        'status' => $response->status(),
                        'body' => $response->body(),
                        'rate_limit_key' => $rateLimitKey
                    ]);
                    
                    // Implement exponential backoff
                    $this->handleRateLimitError($rateLimitKey);
                    return null;
                }
                
                Log::error("Anthropic API request failed", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return null;
            }
            
            $responseData = $response->json();
            
            // Update rate limits on successful request with actual token usage
            $actualInputTokens = $responseData['usage']['input_tokens'] ?? $estimatedInputTokens;
            $actualOutputTokens = $responseData['usage']['output_tokens'] ?? $estimatedOutputTokens;
            
            $this->updateRateLimitCounters($rateLimitKey, $actualInputTokens, $actualOutputTokens);
            
            return $responseData;
            
        } catch (Exception $e) {
            Log::error("Anthropic API request exception", [
                'message' => $e->getMessage(),
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
    }
    
    /**
     * Check if request is within rate limits (RPM, ITPM, OTPM)
     *
     * @param string $key Rate limit key
     * @param int $estimatedInputTokens Estimated input tokens for this request
     * @param int $estimatedOutputTokens Estimated output tokens for this request
     * @return bool True if within limits
     */
    protected function checkRateLimit(string $key, int $estimatedInputTokens, int $estimatedOutputTokens): bool
    {
        $rpmKey = "anthropic-rpm:{$key}";
        $itpmKey = "anthropic-itpm:{$key}";
        $otpmKey = "anthropic-otpm:{$key}";
        
        // Check RPM (Requests Per Minute)
        if (RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute)) {
            $seconds = RateLimiter::availableIn($rpmKey);
            Log::info("Anthropic RPM limit exceeded, available in {$seconds} seconds");
            return false;
        }
        
        // Check ITPM (Input Tokens Per Minute) 
        $currentInputTokens = RateLimiter::attempts($itpmKey);
        if (($currentInputTokens + $estimatedInputTokens) > $this->inputTokensPerMinute) {
            $seconds = RateLimiter::availableIn($itpmKey);
            Log::info("Anthropic ITPM limit would be exceeded", [
                'current_tokens' => $currentInputTokens,
                'estimated_tokens' => $estimatedInputTokens,
                'limit' => $this->inputTokensPerMinute,
                'available_in' => $seconds
            ]);
            return false;
        }
        
        // Check OTPM (Output Tokens Per Minute)
        $currentOutputTokens = RateLimiter::attempts($otpmKey);
        if (($currentOutputTokens + $estimatedOutputTokens) > $this->outputTokensPerMinute) {
            $seconds = RateLimiter::availableIn($otpmKey);
            Log::info("Anthropic OTPM limit would be exceeded", [
                'current_tokens' => $currentOutputTokens,
                'estimated_tokens' => $estimatedOutputTokens,
                'limit' => $this->outputTokensPerMinute,
                'available_in' => $seconds
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * Update rate limit counters after successful request
     *
     * @param string $key Rate limit key
     * @param int $actualInputTokens Actual input tokens used
     * @param int $actualOutputTokens Actual output tokens used
     */
    protected function updateRateLimitCounters(string $key, int $actualInputTokens, int $actualOutputTokens): void
    {
        $rpmKey = "anthropic-rpm:{$key}";
        $itpmKey = "anthropic-itpm:{$key}";
        $otpmKey = "anthropic-otpm:{$key}";
        
        // Increment RPM counter
        RateLimiter::increment($rpmKey);
        
        // Increment ITPM counter by actual input token usage
        RateLimiter::increment($itpmKey, $actualInputTokens);
        
        // Increment OTPM counter by actual output token usage
        RateLimiter::increment($otpmKey, $actualOutputTokens);
    }
    
    /**
     * Estimate input token usage for an Anthropic messages request
     *
     * @param array $payload Request payload
     * @return int Estimated input tokens
     */
    protected function estimateInputTokens(array $payload): int
    {
        $messages = $payload['messages'] ?? [];
        $system = $payload['system'] ?? '';
        
        // Rough estimation: 4 characters per token
        $inputTokens = ceil(strlen($system) / 4);
        
        foreach ($messages as $message) {
            $content = is_array($message['content']) 
                ? implode(' ', array_column($message['content'], 'text'))
                : $message['content'];
            $inputTokens += ceil(strlen($content) / 4);
        }
        
        // Add buffer for prompt formatting
        return $inputTokens + 50;
    }
    
    /**
     * Handle rate limit errors with exponential backoff
     *
     * @param string $key Rate limit key
     */
    protected function handleRateLimitError(string $key): void
    {
        $backoffKey = "anthropic-backoff:{$key}";
        
        // Implement exponential backoff
        $attempts = RateLimiter::attempts($backoffKey);
        $backoffSeconds = min(60, pow(2, $attempts)); // Max 60 seconds
        
        RateLimiter::increment($backoffKey);
        
        Log::warning("Implementing exponential backoff for Anthropic API", [
            'key' => $key,
            'attempts' => $attempts,
            'backoff_seconds' => $backoffSeconds
        ]);
    }
    
    /**
     * Clear rate limits for a specific key (useful for testing)
     *
     * @param string $key Rate limit key
     */
    public function clearRateLimit(string $key): void
    {
        RateLimiter::clear("anthropic-rpm:{$key}");
        RateLimiter::clear("anthropic-itpm:{$key}");
        RateLimiter::clear("anthropic-otpm:{$key}");
        RateLimiter::clear("anthropic-backoff:{$key}");
    }
    
    /**
     * Get current rate limit status
     *
     * @param string $key Rate limit key
     * @return array Current status
     */
    public function getRateLimitStatus(string $key): array
    {
        $rpmKey = "anthropic-rpm:{$key}";
        $itpmKey = "anthropic-itpm:{$key}";
        $otpmKey = "anthropic-otpm:{$key}";
        
        $rpmCurrent = RateLimiter::attempts($rpmKey);
        $itpmCurrent = RateLimiter::attempts($itpmKey);
        $otpmCurrent = RateLimiter::attempts($otpmKey);
        
        return [
            'rpm' => [
                'current' => $rpmCurrent,
                'limit' => $this->requestsPerMinute,
                'remaining' => max(0, $this->requestsPerMinute - $rpmCurrent),
                'available_in' => RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute) 
                    ? RateLimiter::availableIn($rpmKey) : 0
            ],
            'itpm' => [
                'current' => $itpmCurrent,
                'limit' => $this->inputTokensPerMinute,
                'remaining' => max(0, $this->inputTokensPerMinute - $itpmCurrent),
                'available_in' => RateLimiter::tooManyAttempts($itpmKey, $this->inputTokensPerMinute) 
                    ? RateLimiter::availableIn($itpmKey) : 0
            ],
            'otpm' => [
                'current' => $otpmCurrent,
                'limit' => $this->outputTokensPerMinute,
                'remaining' => max(0, $this->outputTokensPerMinute - $otpmCurrent),
                'available_in' => RateLimiter::tooManyAttempts($otpmKey, $this->outputTokensPerMinute) 
                    ? RateLimiter::availableIn($otpmKey) : 0
            ]
        ];
    }
}