<?php

namespace App\Services\AI;

use App\Models\Account;
use App\Models\Company;
use App\Models\People;
use App\Models\SuccessionPlan;
use App\Models\SuccessRequirements;
use App\Models\notifications;
use Illuminate\Support\Facades\Log;

class PlanDataProcessor
{
    /**
     * Process and store a succession plan based on AI-generated data
     * 
     * @param array $planData The plan data to process
     * @param object $user The current user
     * @return array Result with status and processed plan data
     */
    public function processPlan($planData, $user)
    {
        
        try {
            // Add company interest data to plan if available
            $enrichedPlanData = $this->enrichPlanData($planData, $user);
            
            // Create the plan record in the database
            $plan = $this->createPlanRecord($enrichedPlanData, $user);
            
            // Add plan ID to the plan data
            $enrichedPlanData['plan_id'] = $plan->id;
            
            // Create success requirements records
            $this->createSuccessRequirements($enrichedPlanData, $plan);
            
            // Create notification
            $this->createPlanNotification($enrichedPlanData, $plan, $user);
            
            return [
                'success' => true, 
                'plan' => $plan,
                'plan_data' => $enrichedPlanData
            ];
            
        } catch (\Exception $e) {
            Log::error("PLAN PROCESSING: Failed to process plan", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Enrich plan data with account-specific company interests
     * 
     * @param array $planData The original plan data
     * @param object $user The current user
     * @return array Enriched plan data
     */
    protected function enrichPlanData($planData, $user)
    {
        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        
        // Get user's account information
        $accountObj = Account::where('id', $user->account_id)->first();
        
        if ($accountObj) {
            // Extract company interests
            if ($accountObj->company_of_interest && $accountObj->company_of_interest != "") {
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if ($accountObj->industry_interest && $accountObj->industry_interest != "") {
                $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if ($accountObj->sector_interest && $accountObj->sector_interest != "") {
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }
        
        // Determine company IDs based on interest
        $companyIds = [];
        if ($interestedCompanies && count($interestedCompanies) > 0) {
            $companyIds = $interestedCompanies;
        }
        else if ($interestedIndustries && count($interestedIndustries) > 0) {
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if ($interestedSectors && count($interestedSectors) > 0) {
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();
        }
        
        // Get company array from people table
        $companiesArr = People::whereNotNull('company_id')
            ->distinct()
            ->pluck('company_id')
            ->toArray();
        
        // Get company information
        $companies = Company::select(['id', 'name', 'industry', 'sector'])
            ->where('id', '!=', $user->company_id)
            ->when(!empty($companyIds), function ($query) use ($companyIds) {
                $query->whereIn('id', $companyIds);
            })
            ->whereIn('id', $companiesArr)
            ->get()->map(function ($company) {
                return [
                    'value' => $company->name,
                    'label' => $company->name,
                    'industry' => $company->industry,
                    'sector' => $company->sector
                ];
            })->toArray();
        
        // Add company information to plan data
        $planData['available_companies'] = $companies;
        
        return $planData;
    }
    
    /**
     * Create the plan record in the database
     * 
     * @param array $planData The plan data
     * @param object $user The current user
     * @return SuccessionPlan The created plan
     */
    protected function createPlanRecord($planData, $user)
    {
        // Create the plan record
        return SuccessionPlan::create([
            'name'               => $planData['plan_name'],
            'description'        => $planData['description'],
            'minimum_Experience' => $planData['minimum_tenure'] ?? 0, // Default to 0 if missing
            'step_up'            => "1", // Default to enabled
            'ethnicity'          => $planData['is_ethnicity_important'] ?? 0,
            'age'                => 0, // Default to 0
            'status'             => "Draft",
            'candidate_status'   => "No Changes",
            'user_id'            => $user->id,
            "shared_with"        => "[]" // Default to empty JSON array
        ]);
    }
    
    /**
     * Create success requirements records for the plan
     * 
     * @param array $planData The plan data
     * @param SuccessionPlan $plan The created plan
     * @return void
     */
    protected function createSuccessRequirements($planData, $plan)
    {
        $successRequirementsData = [];
        
        // Add target roles
        $target_roles = $planData['target_roles'];
        foreach ($target_roles as $role) {
            $successRequirementsData[] = [
                'plan_id' => $plan->id,
                'name'    => trim($role),
                'type'    => 'Role',
            ];
        }
        
        // Add step-up candidates if specified
        if ($planData['step_up_candidates'] != ['none']) {
            $step_up_candidates = $planData['step_up_candidates'];
            foreach ($step_up_candidates as $stepUpCandidate) {
                $successRequirementsData[] = [
                    'plan_id' => $plan->id,
                    'name'    => trim($stepUpCandidate),
                    'type'    => 'step_up',
                ];
            }
        }
        
        // Add tenure requirement if specified
        $minimum_tenure = $planData['minimum_tenure'] ?? null;
        if ($minimum_tenure) {
            $successRequirementsData[] = [
                'plan_id' => $plan->id,
                'name'    => $minimum_tenure,
                'type'    => 'Minimum_Tenure',
            ];
        }
        
        // Add location requirements if specified
        if ($planData['country'] != ['none']) {
            $countries = $planData['country'];
            foreach ($countries as $country) {
                $successRequirementsData[] = [
                    'plan_id' => $plan->id,
                    'name'    => $country,
                    'type'    => 'location',
                ];
            }
        }
        
        // Add professional skills
        $skills = $planData['skills'];
        foreach ($skills as $skill) {
            $successRequirementsData[] = [
                'plan_id' => $plan->id,
                'name'    => trim($skill),
                'type'    => 'professional_skill',
            ];
        }
        
        // Add gender requirement if specified
        if ($planData['gender'] != "Not Required") {
            $successRequirementsData[] = [
                'plan_id' => $plan->id,
                'name'    => trim($planData['gender']),
                'type'    => 'Gender',
            ];
        }
        
        // Add company requirements if specified
        if ($planData['companies'] != ['none']) {
            $companiesArr = $planData['companies'];
            foreach ($companiesArr as $cr) {
                $successRequirementsData[] = [
                    'plan_id' => $plan->id,
                    'name'    => trim($cr),
                    'type'    => 'Company',
                ];
            }
        }
        
        // Batch insert all requirements
        SuccessRequirements::insert($successRequirementsData);
    }
    
    /**
     * Create notification for plan creation
     * 
     * @param array $planData The plan data
     * @param SuccessionPlan $plan The created plan
     * @param object $user The current user
     * @return void
     */
    protected function createPlanNotification($planData, $plan, $user)
    {
        notifications::create([
            'type'              => "Plan_Created",
            'plan_id'           => $plan->id,
            'entity_name'       => $planData['plan_name'],
            'description'       => $planData['description'],
            'user_id'           => $user->id,
            'user_company'      => $user->company_id
        ]);
    }
}