<?php

namespace App\Services\AI;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\RateLimiter;

class RateLimitedAnthropicService
{
    protected $apiKey;
    protected $baseUrl = 'https://api.anthropic.com/v1';
    
    // Rate limiting configuration based on Anthropic Tier 1 limits
    protected $requestsPerMinute = 50;     // RPM
    protected $inputTokensPerMinute = 40000; // ITPM (using Claude 3.5 Sonnet limit)
    protected $outputTokensPerMinute = 8000; // OTPM (using Claude 3.5 Sonnet limit)
    
    // Circuit breaker settings
    protected $circuitBreakerThreshold = 5; // Max consecutive failures
    protected $circuitBreakerTimeout = 300; // 5 minutes
    
    // API tier detection for Anthropic
    protected $apiTiers = [
        'tier1' => ['rpm' => 50, 'itpm' => 40000, 'otpm' => 8000],
        'tier2' => ['rpm' => 1000, 'itpm' => 80000, 'otpm' => 16000],
        'tier3' => ['rpm' => 2000, 'itpm' => 160000, 'otpm' => 32000],
        'tier4' => ['rpm' => 4000, 'itpm' => 400000, 'otpm' => 80000],
    ];
    
    public function __construct()
    {
        $this->apiKey = config('ai.anthropic.api_key');
    }
    
    /**
     * Make a rate-limited request to Anthropic Messages API
     *
     * @param array $payload The request payload
     * @param string $rateLimitKey Optional custom rate limit key
     * @param int $retryCount Current retry count to prevent infinite recursion
     * @return array|null|false Response data, null on failure, false if circuit open
     */
    public function messages(array $payload, ?string $rateLimitKey = null, int $retryCount = 0)
    {
        // Use default rate limit key if none provided
        $rateLimitKey = $rateLimitKey ?? 'anthropic-messages';
        
        // Prevent infinite retries (max 1 retry)
        if ($retryCount > 1) {
            Log::warning('Max retries exceeded for Anthropic request', ['rate_limit_key' => $rateLimitKey]);
            return null;
        }
        
        // Check circuit breaker
        if ($this->isCircuitOpen($rateLimitKey)) {
            Log::warning("Circuit breaker is open for key: {$rateLimitKey}");
            return false; // Return false when circuit is open
        }
        
        // Check if we should queue the request instead
        if (isset($payload['queue_if_limited']) && $payload['queue_if_limited']) {
            unset($payload['queue_if_limited']);
            
            $estimatedInputTokens = $this->estimateInputTokens($payload);
            $estimatedOutputTokens = $payload['max_tokens'] ?? 1000;
            
            if (!$this->checkRateLimit($rateLimitKey, $estimatedInputTokens, $estimatedOutputTokens)) {
                // Queue the request for later processing
                Queue::push(\App\Jobs\ProcessAnthropicRequest::class, [
                    'payload' => $payload,
                    'rate_limit_key' => $rateLimitKey,
                ]);
                
                return ['queued' => true];
            }
        }
        
        // Estimate token usage for ITPM/OTPM limiting
        $estimatedInputTokens = $this->estimateInputTokens($payload);
        $estimatedOutputTokens = $payload['max_tokens'] ?? 1000;
        
        // Check all rate limits (RPM, ITPM, OTPM)
        if (!$this->checkRateLimit($rateLimitKey, $estimatedInputTokens, $estimatedOutputTokens)) {
            Log::warning("Anthropic API rate limit exceeded", [
                'rate_limit_key' => $rateLimitKey,
                'estimated_input_tokens' => $estimatedInputTokens,
                'estimated_output_tokens' => $estimatedOutputTokens
            ]);
            return null;
        }
        
        try {
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'anthropic-version' => config('ai.anthropic.version', '2023-06-01')
            ])->timeout(300)->post($this->baseUrl . '/messages', $payload);
            
            // Parse rate limit headers from response
            $this->parseRateLimitHeaders($response->headers(), $rateLimitKey);
            
            if (!$response->successful()) {
                // Handle rate limit errors specifically
                if ($response->status() === 429) {
                    $this->recordFailure($rateLimitKey);
                    
                    // Handle rate limiting - in testing, just return null without retrying
                    if (app()->environment('testing')) {
                        $this->handleRateLimitError($rateLimitKey);
                        return null;
                    }
                    
                    // Parse Retry-After header and wait (only in production)
                    $retryAfter = $this->parseRetryAfter($response->headers());
                    if ($retryAfter > 0) {
                        Log::warning("Rate limited, waiting {$retryAfter} seconds", [
                            'rate_limit_key' => $rateLimitKey,
                            'retry_after' => $retryAfter,
                        ]);
                        sleep($retryAfter);
                        
                        return $this->messages($payload, $rateLimitKey, $retryCount + 1);
                    }
                    
                    // Implement exponential backoff with actual sleep (only in production)
                    $backoffSeconds = $this->handleRateLimitErrorWithSleep($rateLimitKey);
                    if ($backoffSeconds > 0) {
                        sleep($backoffSeconds);
                        
                        return $this->messages($payload, $rateLimitKey, $retryCount + 1);
                    }
                    
                    return null;
                }
                
                $this->recordFailure($rateLimitKey);
                Log::error("Anthropic API request failed", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return null;
            }
            
            // Record successful request
            $this->recordSuccess($rateLimitKey);
            
            $responseData = $response->json();
            
            // Update rate limits on successful request with actual token usage
            $actualInputTokens = $responseData['usage']['input_tokens'] ?? $estimatedInputTokens;
            $actualOutputTokens = $responseData['usage']['output_tokens'] ?? $estimatedOutputTokens;
            
            $this->updateRateLimitCounters($rateLimitKey, $actualInputTokens, $actualOutputTokens);
            
            return $responseData;
            
        } catch (Exception $e) {
            $this->recordFailure($rateLimitKey);
            Log::error("Anthropic API request exception", [
                'message' => $e->getMessage(),
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
    }
    
    /**
     * Check if request is within rate limits (RPM, ITPM, OTPM)
     *
     * @param string $key Rate limit key
     * @param int $estimatedInputTokens Estimated input tokens for this request
     * @param int $estimatedOutputTokens Estimated output tokens for this request
     * @return bool True if within limits
     */
    protected function checkRateLimit(string $key, int $estimatedInputTokens, int $estimatedOutputTokens): bool
    {
        $rpmKey = "anthropic-rpm:{$key}";
        $itpmKey = "anthropic-itpm:{$key}";
        $otpmKey = "anthropic-otpm:{$key}";
        
        // Check RPM (Requests Per Minute)
        if (RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute)) {
            return false;
        }
        
        // Check ITPM (Input Tokens Per Minute) 
        $currentInputTokens = RateLimiter::attempts($itpmKey);
        if (($currentInputTokens + $estimatedInputTokens) > $this->inputTokensPerMinute) {
            return false;
        }
        
        // Check OTPM (Output Tokens Per Minute)
        $currentOutputTokens = RateLimiter::attempts($otpmKey);
        if (($currentOutputTokens + $estimatedOutputTokens) > $this->outputTokensPerMinute) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check rate limits with token bucket refill logic
     *
     * @param string $key Rate limit key
     * @param int $estimatedInputTokens Estimated input tokens
     * @param int $estimatedOutputTokens Estimated output tokens
     * @return bool True if within limits
     */
    protected function checkRateLimitWithRefill(string $key, int $estimatedInputTokens, int $estimatedOutputTokens): bool
    {
        $now = Carbon::now();
        $bucketKey = "anthropic-bucket:{$key}";
        
        // Get current bucket state
        $bucket = Cache::get($bucketKey, [
            'requests' => $this->requestsPerMinute,
            'input_tokens' => $this->inputTokensPerMinute,
            'output_tokens' => $this->outputTokensPerMinute,
            'last_refill' => $now->timestamp,
        ]);
        
        // Calculate time since last refill
        $timeDiff = $now->timestamp - $bucket['last_refill'];
        
        if ($timeDiff > 0) {
            // Refill tokens based on time passed
            $requestsToAdd = min(
                $this->requestsPerMinute - $bucket['requests'],
                ($this->requestsPerMinute / 60) * $timeDiff
            );
            $inputTokensToAdd = min(
                $this->inputTokensPerMinute - $bucket['input_tokens'],
                ($this->inputTokensPerMinute / 60) * $timeDiff
            );
            $outputTokensToAdd = min(
                $this->outputTokensPerMinute - $bucket['output_tokens'],
                ($this->outputTokensPerMinute / 60) * $timeDiff
            );
            
            $bucket['requests'] = min($this->requestsPerMinute, $bucket['requests'] + $requestsToAdd);
            $bucket['input_tokens'] = min($this->inputTokensPerMinute, $bucket['input_tokens'] + $inputTokensToAdd);
            $bucket['output_tokens'] = min($this->outputTokensPerMinute, $bucket['output_tokens'] + $outputTokensToAdd);
            $bucket['last_refill'] = $now->timestamp;
        }
        
        // Check if we have enough tokens and requests
        if ($bucket['input_tokens'] < $estimatedInputTokens || 
            $bucket['output_tokens'] < $estimatedOutputTokens || 
            $bucket['requests'] < 1) {
            Cache::put($bucketKey, $bucket, 300); // Cache for 5 minutes
            return false;
        }
        
        // Consume tokens and requests
        $bucket['input_tokens'] -= $estimatedInputTokens;
        $bucket['output_tokens'] -= $estimatedOutputTokens;
        $bucket['requests'] -= 1;
        
        Cache::put($bucketKey, $bucket, 300);
        
        return true;
    }
    
    /**
     * Update rate limit counters after successful request
     *
     * @param string $key Rate limit key
     * @param int $actualInputTokens Actual input tokens used
     * @param int $actualOutputTokens Actual output tokens used
     */
    protected function updateRateLimitCounters(string $key, int $actualInputTokens, int $actualOutputTokens): void
    {
        $rpmKey = "anthropic-rpm:{$key}";
        $itpmKey = "anthropic-itpm:{$key}";
        $otpmKey = "anthropic-otpm:{$key}";
        
        // Increment RPM counter by 1 (one request)
        RateLimiter::hit($rpmKey);
        
        // Increment ITPM counter by actual input token usage
        for ($i = 0; $i < $actualInputTokens; $i++) {
            RateLimiter::hit($itpmKey);
        }
        
        // Increment OTPM counter by actual output token usage
        for ($i = 0; $i < $actualOutputTokens; $i++) {
            RateLimiter::hit($otpmKey);
        }
        
        // Update metrics
        $this->updateMetrics($key, $actualInputTokens + $actualOutputTokens, true);
    }
    
    /**
     * Estimate input token usage for an Anthropic messages request
     *
     * @param array $payload Request payload
     * @return int Estimated input tokens
     */
    protected function estimateInputTokens(array $payload): int
    {
        $messages = $payload['messages'] ?? [];
        $system = $payload['system'] ?? '';
        
        // Rough estimation: 4 characters per token
        $inputTokens = ceil(strlen($system) / 4);
        
        foreach ($messages as $message) {
            $content = is_array($message['content']) 
                ? implode(' ', array_column($message['content'], 'text'))
                : $message['content'];
            $inputTokens += ceil(strlen($content) / 4);
        }
        
        // Add buffer for prompt formatting
        return $inputTokens + 50;
    }
    
    /**
     * Handle rate limit errors with exponential backoff
     *
     * @param string $key Rate limit key
     */
    protected function handleRateLimitError(string $key): void
    {
        $backoffKey = "anthropic-backoff:{$key}";
        
        // Implement exponential backoff
        $attempts = RateLimiter::attempts($backoffKey);
        $backoffSeconds = min(60, pow(2, $attempts)); // Max 60 seconds
        
        RateLimiter::increment($backoffKey);
        
        Log::warning("Implementing exponential backoff for Anthropic API", [
            'key' => $key,
            'attempts' => $attempts,
            'backoff_seconds' => $backoffSeconds
        ]);
    }
    
    /**
     * Handle rate limit errors with actual sleep
     *
     * @param string $key Rate limit key
     * @return int Backoff seconds
     */
    protected function handleRateLimitErrorWithSleep(string $key): int
    {
        $backoffKey = "anthropic-backoff:{$key}";
        
        // Implement exponential backoff
        $attempts = Cache::get($backoffKey, 0);
        $backoffSeconds = min(60, pow(2, $attempts)); // Max 60 seconds
        
        Cache::put($backoffKey, $attempts + 1, 300); // Cache for 5 minutes
        
        Log::warning('Implementing exponential backoff with sleep for Anthropic', [
            'key' => $key,
            'attempts' => $attempts,
            'backoff_seconds' => $backoffSeconds,
        ]);
        
        return $backoffSeconds;
    }
    
    /**
     * Parse Retry-After header from response
     *
     * @param array $headers Response headers
     * @return int Seconds to wait
     */
    protected function parseRetryAfter(array $headers): int
    {
        foreach ($headers as $key => $values) {
            if (strtolower($key) === 'retry-after') {
                $value = is_array($values) ? $values[0] : $values;
                return (int) $value;
            }
        }
        
        return 0;
    }
    
    /**
     * Parse rate limit headers from Anthropic response
     *
     * @param array $headers Response headers
     * @param string $key Rate limit key
     */
    protected function parseRateLimitHeaders(array $headers, string $key): void
    {
        $rateLimitData = [];
        
        foreach ($headers as $headerKey => $values) {
            $headerKey = strtolower($headerKey);
            $value = is_array($values) ? $values[0] : $values;
            
            switch ($headerKey) {
                case 'anthropic-ratelimit-requests-limit':
                    $rateLimitData['rpm_limit'] = (int) $value;
                    break;
                case 'anthropic-ratelimit-requests-remaining':
                    $rateLimitData['rpm_remaining'] = (int) $value;
                    break;
                case 'anthropic-ratelimit-requests-reset':
                    $rateLimitData['rpm_reset'] = $value;
                    break;
                case 'anthropic-ratelimit-input-tokens-limit':
                    $rateLimitData['itpm_limit'] = (int) $value;
                    break;
                case 'anthropic-ratelimit-input-tokens-remaining':
                    $rateLimitData['itpm_remaining'] = (int) $value;
                    break;
                case 'anthropic-ratelimit-output-tokens-limit':
                    $rateLimitData['otpm_limit'] = (int) $value;
                    break;
                case 'anthropic-ratelimit-output-tokens-remaining':
                    $rateLimitData['otpm_remaining'] = (int) $value;
                    break;
            }
        }
        
        if (!empty($rateLimitData)) {
            // Update our internal limits based on API response
            if (isset($rateLimitData['rpm_limit'])) {
                $this->requestsPerMinute = $rateLimitData['rpm_limit'];
            }
            if (isset($rateLimitData['itpm_limit'])) {
                $this->inputTokensPerMinute = $rateLimitData['itpm_limit'];
            }
            if (isset($rateLimitData['otpm_limit'])) {
                $this->outputTokensPerMinute = $rateLimitData['otpm_limit'];
            }
            
            // Cache the API-reported limits
            Cache::put("anthropic-api-limits:{$key}", $rateLimitData, 300);
        }
    }
    
    /**
     * Check if circuit breaker is open
     *
     * @param string $key Rate limit key
     * @return bool True if circuit is open
     */
    public function isCircuitOpen(string $key): bool
    {
        $circuitKey = "anthropic-circuit:{$key}";
        $circuit = Cache::get($circuitKey);
        
        if (!$circuit || !isset($circuit['opens_until'])) {
            return false;
        }
        
        // Check if timeout period has passed
        if (Carbon::now()->timestamp > $circuit['opens_until']) {
            Cache::forget($circuitKey);
            return false;
        }
        
        return true;
    }
    
    /**
     * Record a successful request
     *
     * @param string $key Rate limit key
     */
    protected function recordSuccess(string $key): void
    {
        $circuitKey = "anthropic-circuit:{$key}";
        
        // Reset failure count on success
        Cache::forget($circuitKey);
        
        $this->updateMetrics($key, 0, true);
    }
    
    /**
     * Record a failed request
     *
     * @param string $key Rate limit key
     */
    protected function recordFailure(string $key): void
    {
        $circuitKey = "anthropic-circuit:{$key}";
        $circuit = Cache::get($circuitKey, ['failures' => 0]);
        
        $circuit['failures']++;
        
        // Open circuit if threshold reached
        if ($circuit['failures'] >= $this->circuitBreakerThreshold) {
            $circuit['opens_until'] = Carbon::now()->addSeconds($this->circuitBreakerTimeout)->timestamp;
            Log::warning("Circuit breaker opened for key: {$key}", $circuit);
        }
        
        Cache::put($circuitKey, $circuit, 600); // Cache for 10 minutes
        
        $this->updateMetrics($key, 0, false);
    }
    
    /**
     * Clear rate limits for a specific key (useful for testing)
     *
     * @param string $key Rate limit key
     */
    public function clearRateLimit(string $key): void
    {
        RateLimiter::clear("anthropic-rpm:{$key}");
        RateLimiter::clear("anthropic-itpm:{$key}");
        RateLimiter::clear("anthropic-otpm:{$key}");
        RateLimiter::clear("anthropic-backoff:{$key}");
        
        // Also clear cache entries
        Cache::forget("anthropic-bucket:{$key}");
        Cache::forget("anthropic-circuit:{$key}");
        Cache::forget("anthropic-api-limits:{$key}");
        Cache::forget("anthropic-metrics:{$key}");
    }
    
    /**
     * Get current rate limit status
     *
     * @param string $key Rate limit key
     * @return array Current status
     */
    public function getRateLimitStatus(string $key): array
    {
        $rpmKey = "anthropic-rpm:{$key}";
        $itpmKey = "anthropic-itpm:{$key}";
        $otpmKey = "anthropic-otpm:{$key}";
        $bucketKey = "anthropic-bucket:{$key}";
        
        // Check if we have bucket data (new system) or should use RateLimiter (legacy)
        $bucket = Cache::get($bucketKey);
        
        if ($bucket) {
            // Use token bucket approach
            $rpmCurrent = $this->requestsPerMinute - $bucket['requests'];
            $itpmCurrent = $this->inputTokensPerMinute - $bucket['input_tokens'];
            $otpmCurrent = $this->outputTokensPerMinute - $bucket['output_tokens'];
            
            return [
                'rpm' => [
                    'current' => max(0, $rpmCurrent),
                    'limit' => $this->requestsPerMinute,
                    'remaining' => max(0, $bucket['requests']),
                    'available_in' => $bucket['requests'] > 0 ? 0 : $this->calculateRefillTime($key, 'requests'),
                ],
                'itpm' => [
                    'current' => max(0, $itpmCurrent),
                    'limit' => $this->inputTokensPerMinute,
                    'remaining' => max(0, $bucket['input_tokens']),
                    'available_in' => $bucket['input_tokens'] > 0 ? 0 : $this->calculateRefillTime($key, 'input_tokens'),
                ],
                'otpm' => [
                    'current' => max(0, $otpmCurrent),
                    'limit' => $this->outputTokensPerMinute,
                    'remaining' => max(0, $bucket['output_tokens']),
                    'available_in' => $bucket['output_tokens'] > 0 ? 0 : $this->calculateRefillTime($key, 'output_tokens'),
                ],
            ];
        } else {
            // Fallback to RateLimiter for backwards compatibility
            $rpmCurrent = RateLimiter::attempts($rpmKey);
            $itpmCurrent = RateLimiter::attempts($itpmKey);
            $otpmCurrent = RateLimiter::attempts($otpmKey);
            
            return [
                'rpm' => [
                    'current' => $rpmCurrent,
                    'limit' => $this->requestsPerMinute,
                    'remaining' => max(0, $this->requestsPerMinute - $rpmCurrent),
                    'available_in' => RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute) 
                        ? RateLimiter::availableIn($rpmKey) : 0
                ],
                'itpm' => [
                    'current' => $itpmCurrent,
                    'limit' => $this->inputTokensPerMinute,
                    'remaining' => max(0, $this->inputTokensPerMinute - $itpmCurrent),
                    'available_in' => RateLimiter::tooManyAttempts($itpmKey, $this->inputTokensPerMinute) 
                        ? RateLimiter::availableIn($itpmKey) : 0
                ],
                'otpm' => [
                    'current' => $otpmCurrent,
                    'limit' => $this->outputTokensPerMinute,
                    'remaining' => max(0, $this->outputTokensPerMinute - $otpmCurrent),
                    'available_in' => RateLimiter::tooManyAttempts($otpmKey, $this->outputTokensPerMinute) 
                        ? RateLimiter::availableIn($otpmKey) : 0
                ]
            ];
        }
    }
    
    /**
     * Process batch requests with proper spacing
     *
     * @param array $requests Array of request payloads
     * @param string $key Rate limit key
     * @return array Results
     */
    public function processBatch(array $requests, string $key): array
    {
        $results = [];
        $requestsPerSecond = $this->requestsPerMinute / 60;
        $delayBetweenRequests = 1 / $requestsPerSecond; // Seconds between requests
        
        foreach ($requests as $i => $request) {
            if ($i > 0 && !app()->environment('testing')) {
                // Sleep to maintain rate limit (only if not in testing)
                sleep((int) ceil($delayBetweenRequests));
            }
            
            $result = $this->messages($request, $key);
            $results[] = $result;
            
            // Stop if we get rate limited or circuit opens
            if ($result === null || $result === false) {
                break;
            }
        }
        
        return $results;
    }
    
    /**
     * Get current API tier based on limits
     *
     * @return string Current tier
     */
    public function getCurrentTier(): string
    {
        foreach ($this->apiTiers as $tier => $limits) {
            if ($this->requestsPerMinute <= $limits['rpm'] && 
                $this->inputTokensPerMinute <= $limits['itpm'] && 
                $this->outputTokensPerMinute <= $limits['otpm']) {
                return $tier;
            }
        }
        
        return 'tier1'; // Default
    }
    
    /**
     * Update metrics for monitoring
     *
     * @param string $key Rate limit key
     * @param int $tokens Tokens used
     * @param bool $success Whether request was successful
     */
    protected function updateMetrics(string $key, int $tokens, bool $success): void
    {
        $metricsKey = "anthropic-metrics:{$key}";
        $metrics = Cache::get($metricsKey, [
            'total_requests' => 0,
            'successful_requests' => 0,
            'rate_limited_requests' => 0,
            'total_tokens' => 0,
            'circuit_breaker_trips' => 0,
        ]);
        
        $metrics['total_requests']++;
        $metrics['total_tokens'] += $tokens;
        
        if ($success) {
            $metrics['successful_requests']++;
        } else {
            $metrics['rate_limited_requests']++;
        }
        
        Cache::put($metricsKey, $metrics, 3600); // Cache for 1 hour
    }
    
    /**
     * Get comprehensive metrics
     *
     * @param string $key Rate limit key
     * @return array Metrics
     */
    public function getMetrics(string $key): array
    {
        $metricsKey = "anthropic-metrics:{$key}";
        $metrics = Cache::get($metricsKey, [
            'total_requests' => 0,
            'successful_requests' => 0,
            'rate_limited_requests' => 0,
            'total_tokens' => 0,
            'circuit_breaker_trips' => 0,
        ]);
        
        $metrics['average_tokens_per_request'] = $metrics['total_requests'] > 0
            ? $metrics['total_tokens'] / $metrics['total_requests']
            : 0;
        
        $metrics['current_tier'] = $this->getCurrentTier();
        $metrics['time_until_reset'] = $this->calculateRefillTime($key, 'requests');
        
        return $metrics;
    }
    
    /**
     * Calculate time until bucket refill
     *
     * @param string $key Rate limit key
     * @param string $type 'requests', 'input_tokens', or 'output_tokens'
     * @return int Seconds until refill
     */
    protected function calculateRefillTime(string $key, string $type): int
    {
        $bucketKey = "anthropic-bucket:{$key}";
        $bucket = Cache::get($bucketKey);
        
        if (!$bucket) {
            return 0;
        }
        
        $limits = [
            'requests' => $this->requestsPerMinute,
            'input_tokens' => $this->inputTokensPerMinute,
            'output_tokens' => $this->outputTokensPerMinute,
        ];
        
        $limit = $limits[$type] ?? $this->requestsPerMinute;
        $current = $bucket[$type] ?? $limit;
        
        if ($current > 0) {
            return 0;
        }
        
        // Calculate refill rate (per second)
        $refillRate = $limit / 60;
        $tokensNeeded = 1; // Minimum needed
        
        return (int) ceil($tokensNeeded / $refillRate);
    }
}