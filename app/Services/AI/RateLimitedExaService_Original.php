<?php

namespace App\Services\AI;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;
use Exception;

class RateLimitedExaService
{
    protected $apiKey;
    protected $baseUrl = 'https://api.exa.ai';
    
    // Rate limiting configuration based on Exa limits
    protected $requestsPerMinute = 300; // 5 requests per second = 300 per minute
    
    public function __construct()
    {
        $this->apiKey = config('ai.exa.api_key');
    }
    
    /**
     * Make a rate-limited request to Exa Search API
     *
     * @param array $payload The request payload
     * @param string $rateLimitKey Optional custom rate limit key
     * @return array|null Response data or null on failure
     */
    public function search(array $payload, string $rateLimitKey = null): ?array
    {
        // Use default rate limit key if none provided
        $rateLimitKey = $rateLimitKey ?? 'exa-search';
        
        // Check rate limits
        if (!$this->checkRateLimit($rateLimitKey)) {
            Log::warning("Exa API rate limit exceeded", [
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
        
        try {
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(300)->post($this->baseUrl . '/search', $payload);
            
            if (!$response->successful()) {
                // Handle rate limit errors specifically
                if ($response->status() === 429) {
                    Log::warning("Exa API rate limit hit", [
                        'status' => $response->status(),
                        'body' => $response->body(),
                        'rate_limit_key' => $rateLimitKey
                    ]);
                    
                    // Implement exponential backoff
                    $this->handleRateLimitError($rateLimitKey);
                    return null;
                }
                
                Log::error("Exa API request failed", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return null;
            }
            
            // Update rate limits on successful request
            $this->updateRateLimitCounters($rateLimitKey);
            
            return $response->json();
            
        } catch (Exception $e) {
            Log::error("Exa API request exception", [
                'message' => $e->getMessage(),
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
    }
    
    /**
     * Make a rate-limited request to Exa Get Contents API
     *
     * @param array $payload The request payload
     * @param string $rateLimitKey Optional custom rate limit key
     * @return array|null Response data or null on failure
     */
    public function getContents(array $payload, string $rateLimitKey = null): ?array
    {
        // Use default rate limit key if none provided
        $rateLimitKey = $rateLimitKey ?? 'exa-contents';
        
        // Check rate limits
        if (!$this->checkRateLimit($rateLimitKey)) {
            Log::warning("Exa API rate limit exceeded", [
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
        
        try {
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(300)->post($this->baseUrl . '/contents', $payload);
            
            if (!$response->successful()) {
                if ($response->status() === 429) {
                    Log::warning("Exa API rate limit hit", [
                        'status' => $response->status(),
                        'body' => $response->body(),
                        'rate_limit_key' => $rateLimitKey
                    ]);
                    
                    $this->handleRateLimitError($rateLimitKey);
                    return null;
                }
                
                Log::error("Exa API request failed", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return null;
            }
            
            // Update rate limits on successful request
            $this->updateRateLimitCounters($rateLimitKey);
            
            return $response->json();
            
        } catch (Exception $e) {
            Log::error("Exa API request exception", [
                'message' => $e->getMessage(),
                'rate_limit_key' => $rateLimitKey
            ]);
            return null;
        }
    }
    
    /**
     * Check if request is within rate limits
     *
     * @param string $key Rate limit key
     * @return bool True if within limits
     */
    protected function checkRateLimit(string $key): bool
    {
        $rpmKey = "exa-rpm:{$key}";
        
        // Check RPM (Requests Per Minute)
        if (RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute)) {
            $seconds = RateLimiter::availableIn($rpmKey);
            Log::info("Exa RPM limit exceeded, available in {$seconds} seconds");
            return false;
        }
        
        return true;
    }
    
    /**
     * Update rate limit counters after successful request
     *
     * @param string $key Rate limit key
     */
    protected function updateRateLimitCounters(string $key): void
    {
        $rpmKey = "exa-rpm:{$key}";
        
        // Increment RPM counter
        RateLimiter::increment($rpmKey);
    }
    
    /**
     * Handle rate limit errors with exponential backoff
     *
     * @param string $key Rate limit key
     */
    protected function handleRateLimitError(string $key): void
    {
        $backoffKey = "exa-backoff:{$key}";
        
        // Implement exponential backoff
        $attempts = RateLimiter::attempts($backoffKey);
        $backoffSeconds = min(60, pow(2, $attempts)); // Max 60 seconds
        
        RateLimiter::increment($backoffKey);
        
        Log::warning("Implementing exponential backoff for Exa API", [
            'key' => $key,
            'attempts' => $attempts,
            'backoff_seconds' => $backoffSeconds
        ]);
    }
    
    /**
     * Clear rate limits for a specific key (useful for testing)
     *
     * @param string $key Rate limit key
     */
    public function clearRateLimit(string $key): void
    {
        RateLimiter::clear("exa-rpm:{$key}");
        RateLimiter::clear("exa-backoff:{$key}");
    }
    
    /**
     * Get current rate limit status
     *
     * @param string $key Rate limit key
     * @return array Current status
     */
    public function getRateLimitStatus(string $key): array
    {
        $rpmKey = "exa-rpm:{$key}";
        
        $rpmCurrent = RateLimiter::attempts($rpmKey);
        
        return [
            'rpm' => [
                'current' => $rpmCurrent,
                'limit' => $this->requestsPerMinute,
                'remaining' => max(0, $this->requestsPerMinute - $rpmCurrent),
                'available_in' => RateLimiter::tooManyAttempts($rpmKey, $this->requestsPerMinute) 
                    ? RateLimiter::availableIn($rpmKey) : 0
            ]
        ];
    }
}