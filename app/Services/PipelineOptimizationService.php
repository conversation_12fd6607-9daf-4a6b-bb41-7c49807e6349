<?php

namespace App\Services;

use App\Models\pipeline;
use App\Models\SuccessPeople;
use App\Models\UserNotes;
use App\Models\Job;
use App\Models\SuccessionPlan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class PipelineOptimizationService
{
    /**
     * Get user jobs with caching
     */
    public static function getUserJobs($people_id, $user_id)
    {
        $cacheKey = "user_jobs_{$people_id}_{$user_id}";
        
        return Cache::remember($cacheKey, 300, function() use ($people_id, $user_id) {
            return Job::select('jobs.*')
                ->where(function ($query) use ($user_id) {
                    $query->where('user_id', $user_id)
                        ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user_id]);
                })
                ->whereExists(function($query) use ($people_id) {
                    $query->select(DB::raw(1))
                        ->from('job_people')
                        ->whereColumn('job_people.job_id', 'jobs.id')
                        ->where('job_people.people_id', $people_id);
                })
                ->get();
        });
    }

    /**
     * Get user plans with caching
     */
    public static function getUserPlans($people_id, $user_id)
    {
        $cacheKey = "user_plans_{$people_id}_{$user_id}";
        
        return Cache::remember($cacheKey, 300, function() use ($people_id, $user_id) {
            return SuccessionPlan::select('succession_plans.*')
                ->where(function ($query) use ($user_id) {
                    $query->where('user_id', $user_id)
                        ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user_id]);
                })
                ->whereExists(function ($query) use ($people_id) {
                    $query->select(DB::raw(1))
                        ->from('success_people')
                        ->whereColumn('success_people.plan_id', 'succession_plans.id')
                        ->where('success_people.people_id', $people_id);
                })
                ->get();
        });
    }

    /**
     * Get user notes with caching
     */
    public static function getUserNotes($entity_id)
    {
        $cacheKey = "user_notes_{$entity_id}";
        
        return Cache::remember($cacheKey, 300, function() use ($entity_id) {
            return UserNotes::select('user_notes.*', 'users.name as user_name')
                ->where('entity_id', $entity_id)
                ->where('entity_type', 'success_person')
                ->join('users', 'user_notes.author', '=', 'users.id')
                ->orderBy('user_notes.id', 'desc')
                ->get();
        });
    }

    /**
     * Clear cache for a specific pipeline item
     */
    public static function clearPipelineCache($pipeline_id, $people_id, $user_id)
    {
        $keys = [
            "pipeline_view_individual_{$pipeline_id}_{$user_id}",
            "user_jobs_{$people_id}_{$user_id}",
            "user_plans_{$people_id}_{$user_id}",
            "user_notes_{$pipeline_id}"
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Clear chart cache for a plan
     */
    public static function clearChartCache($plan_id)
    {
        Cache::forget("charts_data_{$plan_id}");
    }
}
