<?php

namespace App\Services\ErrorHandling;

use Illuminate\Support\Facades\Session;
use Throwable;

class UserErrorHandler
{
    // Error types that help determine UI presentation
    const TYPE_VALIDATION = 'validation';
    const TYPE_AUTHENTICATION = 'authentication';
    const TYPE_AUTHORIZATION = 'authorization';
    const TYPE_SERVER = 'server';
    const TYPE_NOT_FOUND = 'not_found';
    const TYPE_CONFLICT = 'conflict';
    const TYPE_EXTERNAL_SERVICE = 'external_service';
    
    /**
     * Present a user-friendly error message
     *
     * @param string $message User-friendly error message
     * @param string $type Error type that determines presentation
     * @param array $actions Possible actions user can take (optional)
     * @param string|null $errorCode Internal error code for support reference
     * @return void
     */
    public static function showError(
        string $message,
        string $type = self::TYPE_SERVER,
        array $actions = [],
        ?string $errorCode = null
    ): void {
        // Build error data structure
        $errorData = [
            'message' => $message,
            'type' => $type,
            'actions' => $actions
        ];
        
        // Add error code if provided
        if ($errorCode) {
            $errorData['code'] = $errorCode;
        }
        
        // Flash to session for display
        Session::flash('error', $message);
        Session::flash('error_data', $errorData);
    }
    
    /**
     * Handle an exception and present appropriate user message
     *
     * @param Throwable $exception The exception to handle
     * @param string|null $userMessage Custom user-friendly message (optional)
     * @param string $type Error type that determines presentation
     * @param array $actions Possible actions user can take (optional)
     * @return void
     */
    public static function handleException(
        Throwable $exception,
        ?string $userMessage = null,
        string $type = self::TYPE_SERVER,
        array $actions = []
    ): void {
        // Generate error code from exception hash
        $errorCode = 'E-' . substr(md5(get_class($exception) . $exception->getFile() . $exception->getLine()), 0, 8);
        
        // Log the technical details using ErrorLogger
        ErrorLogger::logException($exception);
        
        // Use provided message or a generic one based on exception type
        $message = $userMessage ?? self::getDefaultMessageForException($exception, $type);
        
        // Present the error to the user
        self::showError($message, $type, $actions, $errorCode);
    }
    
    /**
     * Get a default user-friendly message based on exception type
     *
     * @param Throwable $exception The exception
     * @param string $type Error type
     * @return string User-friendly message
     */
    private static function getDefaultMessageForException(Throwable $exception, string $type): string
    {
        // Default messages by error type
        $defaultMessages = [
            self::TYPE_VALIDATION => 'The information you provided is invalid.',
            self::TYPE_AUTHENTICATION => 'Authentication failed. Please check your credentials.',
            self::TYPE_AUTHORIZATION => 'You don\'t have permission to perform this action.',
            self::TYPE_NOT_FOUND => 'The requested resource could not be found.',
            self::TYPE_CONFLICT => 'This action conflicts with existing data.',
            self::TYPE_EXTERNAL_SERVICE => 'A service we depend on is currently unavailable.',
            self::TYPE_SERVER => 'An unexpected error occurred. Our team has been notified.'
        ];
        
        return $defaultMessages[$type] ?? $defaultMessages[self::TYPE_SERVER];
    }
}