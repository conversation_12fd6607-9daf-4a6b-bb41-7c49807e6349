<?php

namespace App\Services\ErrorHandling;

use Illuminate\Support\Facades\Log;
use Throwable;

class ErrorLogger
{
    // Define error categories
    const CATEGORY_API = 'api';
    const CATEGORY_DATABASE = 'database';
    const CATEGORY_SECURITY = 'security';
    const CATEGORY_PROCESSING = 'processing';
    const CATEGORY_EXTERNAL = 'external';
    const CATEGORY_UI = 'ui';
    
    /**
     * Log an error with standardized format and context
     *
     * @param string $message Error message
     * @param array $context Additional context data
     * @param string $category Error category
     * @param string $severity Error severity (emergency|alert|critical|error|warning|notice|info|debug)
     * @param string $channel Log channel to use
     * @return void
     */
    public static function log(
        string $message, 
        array $context = [], 
        string $category = self::CATEGORY_PROCESSING,
        string $severity = 'error',
        string $channel = 'daily'
    ): void {
        // Standardize context structure
        $standardContext = [
            'category' => $category,
            'timestamp' => now()->toIso8601String(),
            'request_id' => request()->header('X-Request-ID') ?? uniqid('req-'),
            'user_id' => auth()->id() ?? 'guest',
            'request_path' => request()->path(),
            'request_method' => request()->method(),
            'context' => $context
        ];
        
        // Format message with category prefix
        $formattedMessage = "[{$category}] {$message}";
        
        // Log with appropriate severity
        Log::channel($channel)->$severity($formattedMessage, $standardContext);
    }
    
    /**
     * Log an exception with stack trace and context
     *
     * @param Throwable $exception The exception to log
     * @param array $context Additional context data
     * @param string $category Error category
     * @param string $severity Error severity (emergency|alert|critical|error|warning|notice|info|debug)
     * @param string $channel Log channel to use
     * @return void
     */
    public static function logException(
        Throwable $exception,
        array $context = [],
        string $category = self::CATEGORY_PROCESSING,
        string $severity = 'error',
        string $channel = 'daily'
    ): void {
        // Add exception details to context
        $exceptionContext = [
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ];
        
        // Merge with provided context
        $mergedContext = array_merge($context, ['exception_details' => $exceptionContext]);
        
        // Log the exception
        self::log(
            "Exception: {$exception->getMessage()}",
            $mergedContext,
            $category,
            $severity,
            $channel
        );
    }
}