<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class QueueHasLongWaitTime extends Notification
{
    use Queueable;

    protected $connection;
    protected $queue;
    protected $size;

    /**
     * Create a new notification instance.
     *
     * @param string $connection
     * @param string $queue
     * @param int $size
     */
    public function __construct($connection, $queue, $size)
    {
        $this->connection = $connection;
        $this->queue = $queue;
        $this->size = $size;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->error()
            ->subject(sprintf(
                '[Alert] %s app has %s queue spike',
                config('app.name'),
                $this->queue
            ))
            ->line(sprintf(
                "'%s' queue on '%s' connection has reached %s jobs.",
                $this->queue,
                $this->connection,
                $this->size
            ))
            ->line('This indicates a potential bottleneck that may require attention.')
            ->action('View Horizon Dashboard', url('/horizon'))
            ->line('Please check the queue status and consider scaling workers if needed.')
            ->line('')
            ->line('**Queue Details:**')
            ->line("- Connection: {$this->connection}")
            ->line("- Queue: {$this->queue}")
            ->line("- Current Size: {$this->size} jobs")
            ->line('')
            ->line('**Recommended Actions:**')
            ->line('1. Check Horizon dashboard for queue metrics')
            ->line('2. Monitor job processing rates')
            ->line('3. Consider scaling workers if bottleneck persists')
            ->line('4. Review failed jobs for any blocking issues');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'connection' => $this->connection,
            'queue' => $this->queue,
            'size' => $this->size,
            'alert_type' => 'queue_spike',
            'timestamp' => now()->toDateTimeString(),
        ];
    }
}
