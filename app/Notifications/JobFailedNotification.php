<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Jobs\SearchCandidatesJob;
use App\Jobs\TalentPoolExternalSearch;
use App\Jobs\ExternalSearchPerplexityJob;

class JobFailedNotification extends Notification
{
    use Queueable;

    protected $jobClass;
    protected $exception;
    protected $jobData;
    protected $failureContext;

    /**
     * Create a new notification instance.
     *
     * @param string $jobClass
     * @param \Throwable $exception
     * @param array $jobData
     * @param array $context
     */
    public function __construct(string $jobClass, \Throwable $exception, array $jobData = [], array $context = [])
    {
        $this->jobClass = $jobClass;
        $this->exception = $exception;
        $this->jobData = $jobData;
        $this->failureContext = $context;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable): array
    {
        $channels = ['database'];
        
        if ($this->isCriticalJob()) {
            $channels[] = 'mail';
        }
        
        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->error()
            ->subject('Job Failure Alert: ' . class_basename($this->jobClass))
            ->line('A background job has failed in your application.')
            ->line('Job: ' . $this->jobClass)
            ->line('Error: ' . $this->exception->getMessage())
            ->action('View Horizon Dashboard', url('/horizon'))
            ->line('Please check the Horizon dashboard for more details.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'job_class' => $this->jobClass,
            'error_message' => $this->exception->getMessage(),
            'failed_at' => now(),
            'job_data' => $this->jobData,
            'trace' => $this->exception->getTraceAsString(),
            'failure_context' => $this->failureContext
        ];
    }

    /**
     * Determine if the failed job is critical and requires immediate email notification.
     *
     * @return bool
     */
    private function isCriticalJob(): bool
    {
        return in_array($this->jobClass, [
            SearchCandidatesJob::class,
            TalentPoolExternalSearch::class,
            ExternalSearchPerplexityJob::class,
        ]);
    }
}
