/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.jsx",
    "./resources/**/*.vue",
  ],
  theme: {
    extend: {
      height:{'192':'48rem','384':'96rem'},
      width:{'192':'48rem','384':'96rem'},
      animation: {
        fadeIn: "fadeIn 1s ease-in forwards",
        translateX: "translateX 1s ease-in forwards",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: 0 },
          "100%": { opacity: 1 }
        },
        translateX: {
          "0%": { transform: "translateX(-100%)" }, // Start position (-100%)
          "100%": { transform: "translateX(0)" },   // End position (0)
        },
    },
  },
  plugins: [
  ],
},
variants: {
  variants: {
    animation: ["motion-safe"]
  }
}
}
