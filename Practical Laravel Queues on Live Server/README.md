# Practical Laravel Queues on Live Server - Course Overview

This course covers practical implementation of Laravel queues on a live server environment.

## Course Contents

1. **Queue Workers Setup: Send Email Example** (6 min read)
   - Introduction to Laravel queues
   - Setting up database driver for queues
   - Creating a job to send verification emails
   - Running queue workers
   - Queue worker options and development tips

2. **Supervisor Configuration** (5 min read)
   - Installing Supervisor on Ubuntu and Fedora
   - Configuring Supervisor for Laravel queue workers
   - Managing processes with supervisorctl
   - Important configuration settings

3. **Laravel Horizon and Redis** (5 min read)
   - Setting up Redis for queue management
   - Installing and configuring Laravel Horizon
   - Horizon dashboard features
   - Supervisor configuration for Horizon
   - Dashboard access control

4. **<PERSON><PERSON> and Amazon SQS Queues** (7 min read)
   - Setting up Amazon SQS with Laravel
   - Creating IAM users and configuring permissions
   - Queue types: Standard vs FIFO
   - Visibility timeout configuration
   - Laravel configuration for SQS
   - Supervisor setup for SQS workers

5. **Laravel Forge and Queues** (4 min read)
   - Creating queue workers via Forge UI
   - Supervisor configuration generation
   - Setting up Laravel Horizon with Forge
   - Deployment script configuration
   - Automated worker management

6. **Handling Failed Jobs** (8 min read)
   - Understanding job failures
   - Manual job retry commands
   - Automatic retry configuration
   - Monitoring failed jobs with event listeners
   - Laravel Horizon failed job management
   - Third-party monitoring solutions (Spatie, BugSnag)

7. **Queue Spikes And Scaling** (11 min read)
   - Monitoring queue size and performance
   - Setting up queue alerts and notifications
   - Implementing scheduled monitoring
   - Scaling workers with Supervisor
   - Laravel Horizon auto-scaling features
   - Balancing strategies for queue management

## Note on Images

The course includes several screenshots and diagrams:
- Lesson 1: Mailtrap email interface, Mailtrap credentials, Jobs table
- Lesson 3: Laravel Horizon interface, Dashboard, Pending Jobs, Completed Jobs

Due to browser limitations, the actual images couldn't be downloaded directly. You may need to manually save these images from the website.