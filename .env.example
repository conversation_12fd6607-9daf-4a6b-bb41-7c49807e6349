# Application
APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:Lpj/8XWwkWil13ds9kMbzuOYkAq2zra/nd03TSs7ZIk=
APP_DEBUG=true
APP_URL=http://localhost
MIX_APP_URL=http://127.0.0.1:8000

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=SuccessDB_Local
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

# Session
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Memcached
MEMCACHED_HOST=127.0.0.1

# Redis
REDIS_CLIENT=predis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Pusher
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=local
PUSHER_APP_KEY=local
PUSHER_APP_SECRET=local
PUSHER_APP_CLUSTER=mt1
PUSHER_HOST=127.0.0.1
PUSHER_PORT=6001
PUSHER_SCHEME=http

# Vite
VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# OpenAI
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Anthropic
ANTHROPIC_API_KEY=************************************************************************************************************

# PPLX
PERPLEXITY_API_KEY=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

# Exa
EXA_API_KEY=f5294318-3ed9-4c44-9abe-40b9707c7325

# AWS
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=xW4PynBnS5GGiEevYrfTQ3wetMe79XIcI5/0QM6D
AWS_DEFAULT_REGION=us-east-1
AWS_REGION=us-east-1
AWS_BUCKET=successionplanfilestorage
AWS_USE_PATH_STYLE_ENDPOINT=false

# Mail
MAIL_MAILER=smtp
MAIL_HOST=sandbox.smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=39fdecb1d83f41
MAIL_PASSWORD=c4aff826bb0cf7
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Queue
QUEUE_CONNECTION=database
