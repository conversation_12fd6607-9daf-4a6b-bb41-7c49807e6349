# 15 - Other Queue Drivers: Redis and Laravel Horizon

**Summary of this lesson:**
- Using Redis as a more performant alternative to database queue driver
- Redis offers better performance as an in-memory database separate from your main database
- Laravel Horizon provides monitoring tools for Redis queue jobs
- Configuration of Redis as queue driver through **.env** file

In this lesson, we'll explore alternative queue drivers beyond the `database` driver we've been using so far.

We'll focus on **<PERSON>is**, one of the most popular queue drivers in Laravel development, and introduce **Laravel Horizon** for monitoring queues.

---

## Why Use Redis for Queues

For local development, the database driver works fine. But when deploying your application to production with many jobs in the queue, Redis often becomes a better solution for several reasons:

- **Better Performance**: Redis is an in-memory database that works as key-value storage, making it faster than traditional MySQL databases.

- **Reduced Database Load**: Redis runs as a separate system, so your queue operations don't add extra load to your main database.

- **Improved Reliability**: Job processing is more reliable with Redis, especially under high load.

- **Laravel Horizon Support**: With Redis, you can use [Laravel Horizon](https://laravel.com/docs/horizon), a dashboard for monitoring your queues.

Laravel Horizon provides a beautiful dashboard where you can monitor all your jobs - pending, completed, silenced, and failed jobs.

![Laravel Horizon Dashboard](images/15-horizon-dashboard.png)

---

## Setting Up Redis

Installing Redis varies depending on your operating system. Redis provides [excellent documentation](https://redis.io/docs/install/install-redis/) covering installation for all platforms.

After installing and starting the Redis server, you should see something like this in your terminal:

![Redis server running](images/15-redis-running.png)

---

## Configuring Redis as Queue Driver

To use Redis as your queue driver, update the queue connection in your `.env` file:

```env
// ...

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

// ...
```

---

## Simplified Setup Options

If you find manual Redis installation and configuration challenging, consider using services that handle this for you:

- [Laravel Forge](https://forge.laravel.com)
- [Laravel Cloud](https://cloud.laravel.com)
- [Ploi](https://ploi.io)

---

## Queues on Production Server

This is where we end the "beginner level" course on general functionality of Queues in Laravel.

But we've prepared a special follow-up **practical** course on how to set them up **on live server**.

Click here: [Practical Laravel Queues on Live Server](https://laraveldaily.com/course/laravel-queues-server)
