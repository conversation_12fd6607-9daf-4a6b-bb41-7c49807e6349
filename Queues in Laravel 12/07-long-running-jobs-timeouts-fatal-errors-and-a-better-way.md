# 07 - Long-Running Jobs: Timeouts, Fatal Errors and a Better Way

**Summary of this lesson:**
- Understanding how long-running jobs can reach timeout limits (default 60 seconds)
- Setting custom timeout periods using **--timeout** flag or the **$timeout** property
- Breaking down large jobs into smaller, more manageable jobs to avoid timeouts
- Passing only necessary data to jobs to keep payloads small and more efficient

When working with Laravel queues, you'll inevitably encounter scenarios where jobs take a long time to complete. In this lesson, we'll examine the challenges of long-running jobs, potential issues that can arise, and best practices for handling them effectively.

---

## Understanding Long-Running Jobs

Long-running jobs are queue jobs that take many seconds or even minutes to complete. These could include operations like:

- Generating reports for thousands of users
- Processing large batches of data
- Creating multiple files or documents
- Performing complex calculations

These jobs present unique challenges, particularly with timeouts and error handling.

---

## The Problem with Long-Running Jobs

Let's examine a practical example: generating monthly invoice PDFs for all customers. This could be thousands of documents that need to be created once per month.

Here's an implementation using the [LaravelDaily/laravel-invoices](https://github.com/LaravelDaily/laravel-invoices) package:

**app/Jobs/GenerateMonthlyInvoices.php:**

```php
use App\Models\User;
use LaravelDaily\Invoices\Classes\Party;
use Illuminate\Foundation\Queue\Queueable;
use LaravelDaily\Invoices\Facades\Invoice;
use Illuminate\Contracts\Queue\ShouldQueue;
use LaravelDaily\Invoices\Classes\InvoiceItem;

class GenerateMonthlyInvoices implements ShouldQueue
{
    use Queueable;

    public function __construct()
    {}

    public function handle(): void
    {
        $seller = new Party([
            'name' => 'Private Primary School',
        ]);

        $invoiceNumber = 1;

        foreach (User::where('is_admin', 0)->get() as $user) {
            $amountToPay = rand(100, 999);

            $customer = new Party([
                'name' => $user->name,
                'address' => fake()->address(),
                'custom_fields' => [
                    'email' => $user->email,
                ],
            ]);

            $item = (new InvoiceItem())
                ->title(config('app.name') . ' - invoice for ' . now()->monthName)
                ->pricePerUnit($amountToPay);

            Invoice::make()
                ->seller($seller)
                ->buyer($customer)
                ->sequence($invoiceNumber)
                ->filename('invoices/invoice_' . $invoiceNumber)
                ->addItem($item)
                ->save();

            $invoiceNumber++;
        }
    }
}
```

And an Artisan command that dispatches this job:

**app/Console/Commands/GenerateMonthlyInvoicesCommand.php:**

```php
use Illuminate\Console\Command;
use App\Jobs\GenerateMonthlyInvoices;

class GenerateMonthlyInvoicesCommand extends Command
{
    protected $signature = 'invoices:generate';

    protected $description = 'Generate monthly invoices';

    public function handle(): void
    {
        GenerateMonthlyInvoices::dispatch();
    }
}
```

---

## The Timeout Problem

When we start a queue worker and run the Artisan command, something concerning happens:

![Job timing out after 60 seconds](images/07-job-timeout.png)

The job fails after exactly 60 seconds - the default timeout for Laravel queue jobs. This timeout exists to prevent jobs from running indefinitely if something goes wrong.

In the `failed_jobs` table, we can see the timeout error clearly:

![Failed jobs table showing timeout error](images/07-timeout-error.png)

---

## Addressing Timeouts

There are two main approaches to handling timeouts in long-running jobs:

### 1. Increase the Timeout Limit

You can increase the timeout by:

**a) Adding a timeout flag when starting the worker:**

```bash
php artisan queue:work --timeout=120
```

**b) Setting a timeout property in the job class:**

```php
class GenerateMonthlyInvoices implements ShouldQueue
{
    use Queueable;

    public $timeout = 120;

    // ...
}
```

However, this approach only addresses the timeout issue. Other problems remain:

- PHP memory limits can still be reached
- Server crashes would still lose all progress
- The job is still vulnerable to other fatal errors

---

## The Better Solution: Job Chunking

The most robust solution is to break down large jobs into smaller, more manageable chunks. Instead of processing everything in one job, create multiple smaller jobs that each handle a portion of the work.

Let's refactor our invoice generation example:

- First, create a job that generates a single invoice:

```bash
php artisan make:job GenerateMonthlyInvoice
```

- Update the Artisan command to dispatch individual jobs for each user:

```php
use App\Models\User;
use Illuminate\Console\Command;
use App\Jobs\GenerateMonthlyInvoice;

class GenerateMonthlyInvoicesCommand extends Command
{
    protected $signature = 'invoices:generate';

    protected $description = 'Generate monthly invoices';

    public function handle(): void
    {
        $invoiceNumber = 1;

        foreach (User::where('is_admin', 0)->get() as $user) {
            GenerateMonthlyInvoice::dispatch($user->name, $user->email, $invoiceNumber);

            $invoiceNumber++;
        }
    }
}
```

> The invoice number here is generated in such a way only for simplicity.

- Implement the individual invoice generation job:

```php
use App\Models\User;
use LaravelDaily\Invoices\Classes\Party;
use LaravelDaily\Invoices\Facades\Invoice;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use LaravelDaily\Invoices\Classes\InvoiceItem;

class GenerateMonthlyInvoice implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly string $name, private readonly string $email, private readonly int $invoiceNumber)
    {}

    public function handle(): void
    {
        $seller = new Party([
            'name' => 'Private Primary School',
        ]);

        $amountToPay = rand(100, 999);

        $customer = new Party([
            'name' => $this->name,
            'address' => fake()->address(),
            'custom_fields' => [
                'email' => $this->email,
            ],
        ]);

        $item = (new InvoiceItem())
            ->title(config('app.name') . ' - invoice for ' . now()->monthName)
            ->pricePerUnit($amountToPay);

        Invoice::make()
            ->seller($seller)
            ->buyer($customer)
            ->sequence($this->invoiceNumber)
            ->filename('invoices/invoice_' . $this->invoiceNumber)
            ->addItem($item)
            ->save();
    }
}
```

Now when we start the queue worker and run the command, we see multiple smaller jobs being processed successfully:

![Multiple smaller jobs processing successfully](images/07-chunked-jobs.png)

---

## Key Takeaways

When working with long-running processes in Laravel queues:

- Divide large operations into smaller, focused jobs whenever possible
- Keep job payloads small by passing only the necessary data
- If you must use long-running jobs, set appropriate timeouts
- Consider using Laravel's job batching feature for related groups of jobs that need coordination
