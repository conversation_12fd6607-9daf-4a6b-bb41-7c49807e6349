# 13 - Multiple Queues and Priority Jobs

**Summary of this lesson:**
- Dispatching jobs to different queues using the **onQueue()** method
- Setting queue names in configuration with **config/queue.php**
- Running queue workers with priority order using **--queue=** flag
- Jobs in higher priority queues are processed before lower priority queues

In this lesson, we'll learn about **multiple queues** and how to set **job priorities**. Sometimes, certain jobs are more important than others and need to be processed first, even if they were dispatched later.

Think of a real-life traffic scenario: cars are waiting at a traffic light, but an ambulance needs to pass through. Everyone moves aside to let the ambulance go first because it's a priority. We can implement similar priority rules with Laravel queues.

---

## Dispatching Laravel Jobs To Different Queues

Let's look at a scenario where a user registers and triggers three different jobs. We want the last job to be processed before the other two:

```php
use App\Jobs\CreateFirstTask;
use App\Jobs\SendWelcomeMessage;
use App\Jobs\SendRegisteredUserNotification;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        // ...

        CreateFirstTask::dispatch($user->id);
        SendWelcomeMessage::dispatch($user->id);
        SendRegisteredUserNotification::dispatch($user);

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

To make the third job a priority, we can place it in a separate queue by using the `onQueue()` method with a queue name:

```php
use App\Jobs\CreateFirstTask;
use App\Jobs\SendWelcomeMessage;
use App\Jobs\SendRegisteredUserNotification;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        // ...

        CreateFirstTask::dispatch($user->id);
        SendWelcomeMessage::dispatch($user->id);
        SendRegisteredUserNotification::dispatch($user)->onQueue('priority');

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

---

## What Happens in the Database

When a user registers, all jobs are added to the queue table. In the `jobs` table, check the `queue` column - two jobs have a `default` value, and one has `priority`, which we set when dispatching the job.

![Jobs with different queue names](images/13-queue-names.png)

The `default` value comes from the configuration in the `queue.php` file:

**config/queue.php:**

```php
return [
    // ...

    'connections' => [

        'sync' => [
            'driver' => 'sync',
        ],

        'database' => [
            'driver' => 'database',
            'connection' => env('DB_QUEUE_CONNECTION'),
            'table' => env('DB_QUEUE_TABLE', 'jobs'),
            'queue' => env('DB_QUEUE', 'default'),
            'retry_after' => (int) env('DB_QUEUE_RETRY_AFTER', 90),
            'after_commit' => false,
        ],

        // ...
    ],

    // ...
];
```

---

## Running Queue Workers With Priority

To process different queues with priority, we use the `--queue=` flag when starting a queue worker. We list all queues separated by commas, in order of their priority:

```bash
php artisan queue:work --queue=priority,default
```

As shown below, the third job (from the `priority` queue) was processed first, even though it was dispatched last:

![Priority queue processing](images/13-priority-processing.png)

The queue worker will process all jobs from the `priority` queue before moving on to any jobs in the `default` queue. This ensures that your high-priority tasks are always handled first.
