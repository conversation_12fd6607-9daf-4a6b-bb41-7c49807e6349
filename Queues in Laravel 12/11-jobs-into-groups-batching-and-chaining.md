# 11 - Jobs into Groups: Batching and Chaining

**Summary of this lesson:**
- Bus::chain() runs jobs sequentially, with later jobs depending on successful completion of previous ones
- Bus::batch() runs jobs in parallel, providing better monitoring capabilities
- Adding the Batchable trait to job classes to use them in batches
- Using batch lifecycle hooks (before, progress, then, catch, finally) for monitoring and handling exceptions
- Implementing batch cancellation checks within jobs with **$this->batch()->canceled()**

In this lesson, we'll explore how to organize and **group multiple Laravel jobs** using two powerful techniques: **chaining** and **batching**. These approaches help you execute related jobs in a controlled manner with better monitoring.

---

## The Scenario

Imagine a user registers, and you want to perform several jobs afterward. For example, create the first task, then send a welcome message via the internal messaging system and notify the admin about the new users. What if you want to group all of those and monitor whether they succeed or not?

**app/Http/Controllers/Auth/RegisteredUserController.php:**

```php
use App\Jobs\CreateFirstTask;
use App\Jobs\SendWelcomeMessage;
use App\Jobs\SendRegisteredUserNotification;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        // ...

        CreateFirstTask::dispatch($user->id);
        SendWelcomeMessage::dispatch($user->id);
        SendRegisteredUserNotification::dispatch($user);

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

---

## Options For Grouping Laravel Jobs

In Laravel, there are two ways to group jobs: batching and chaining. What is the difference?

Imagine a scenario where you're firing a gun, and the bullets could fire at the same time or one after another. So, batching is firing the bullets at the same time. They all go and do their job, and you don't really care in which order they reach the target.

Chain is one after another. So, the second bullet would be fired only after the first reaches the target. The second job would be starting to perform only after the first job is finished. Let's try both in this scenario and let's start with a chain.

---

## Option 1: Bus::chain()

To [chain](https://laravel.com/docs/queues#job-chaining) jobs, you use a `chain()` method from a `Bus` facade and provide a list of jobs in an array.

**app/Http/Controllers/Auth/RegisteredUserController.php:**

```php
use App\Jobs\CreateFirstTask;
use App\Jobs\SendWelcomeMessage;
use Illuminate\Support\Facades\Bus;
use App\Jobs\SendRegisteredUserNotification;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        // ...

        Bus::chain([
            new CreateFirstTask($user->id),
            new SendWelcomeMessage($user->id),
            new SendRegisteredUserNotification($user),
        ])->dispatch();

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

> Pay attention to how jobs are called in a chain.

When the user registers, the jobs are added to the queue in the `jobs` table, and the first job is seen in the `payload`.

![Chained jobs in queue](images/11-chain-jobs-queue.png)

---

### How Job Chaining Works

When the queue worker is started, all three jobs are processed sequentially.

![Chain processing](images/11-chain-processing.png)

However, if one of the jobs fails, only that specific job is shown in the `failed_jobs` table in the `payload`.

![Chain failed job](images/11-chain-failed-job.png)

This means that when you fix the error and retry the jobs, only the failed job from the chain is retried.

![Chain retry](images/11-chain-retry.png)

With chaining, jobs will run one after the other, but the next job will only run when the previous one is successful. If any job fails, the subsequent jobs will not get started.

---

### Handling Chain Failures

As we discussed previously, job failures should generally be logged somewhere or use an external service to log the jobs. But if you want to catch the exception as it happens in the bus chain, you can do that within the `catch()` method, which accepts a callback function.

**app/Http/Controllers/Auth/RegisteredUserController.php:**

```php
use Throwable;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        // ...

        Bus::chain([
            new CreateFirstTask($user->id),
            new SendWelcomeMessage($user->id),
            new SendRegisteredUserNotification($user),
        ])
        ->catch(function (Throwable $e) {
            // do whatever you need here
        })
        ->dispatch();

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

Think of a Bus chain like a try-catch block. It tries to run jobs in sequence, and if something goes wrong, you can log errors, send notifications, or take other appropriate actions.

---

## Option 2: Bus::batch()

Batching is the second way of grouping jobs, and it offers more flexibility and monitoring capabilities. Instead of the `chain()` method, you use `batch()` on the `Bus` facade. Each job must include the `Illuminate\Bus\Batchable` trait.

---

### Preparing Jobs for Batching

First, add the `Batchable` trait to each job:

**app/Jobs/CreateFirstTask.php:**

```php
use Illuminate\Bus\Batchable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class CreateFirstTask implements ShouldQueue
{
    use Batchable;
    use Queueable;

    // ...
}
```

**app/Jobs/SendWelcomeMessage.php:**

```php
use Illuminate\Bus\Batchable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendWelcomeMessage implements ShouldQueue
{
    use Batchable;
    use Queueable;

    // ...
}
```

**app/Jobs/SendRegisteredUserNotification.php:**

```php
use Illuminate\Bus\Batchable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendRegisteredUserNotification implements ShouldQueue
{
    use Batchable;
    use Queueable;

    // ...
}
```

---

### Creating a Batch

Next, update your controller to use batch instead of chain:

**app/Http/Controllers/Auth/RegisteredUserController.php:**

```php
use Throwable;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        // ...

        Bus::batch([
            new CreateFirstTask($user->id),
            new SendWelcomeMessage($user->id),
            new SendRegisteredUserNotification($user),
        ])->dispatch();

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

---

### Database Setup for Batching

When using a `database` as a queue driver, job batches are saved to a separate table, `job_batches`. When a new Laravel project is created, this table's migration is included along with the `jobs` and `failed_jobs` tables.

If your project is older or you don't have this table, you can add it using an Artisan command:

```bash
php artisan make:queue-batches-table
```

---

### How Batching Works

Now, when a user registers, a batch is created in the `job_batches` table with a count of total jobs, and in the `jobs` table, three jobs are added to the queue.

![Batch created](images/11-batch-created.png)

![Batch jobs in queue](images/11-batch-jobs-queue.png)

When the queue worker is started, all the jobs are processed.

![Batch processing](images/11-batch-processing.png)

---

### Handling Batch Failures

What happens when one job fails? In the `job_batches` table, we can see that one job is pending and failed. The ID of the failed job is stored in the `failed_job_ids` column.

![Batch with failed job](images/11-batch-failed.png)

Unlike chains, batch jobs can run in a random order, so it's important to check batch status within each job. You can check if the batch has been canceled (which means another job in the batch failed):

**app/Jobs/CreateFirstTask.php:**

```php
use Illuminate\Bus\Batchable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class CreateFirstTask implements ShouldQueue
{
    use Batchable;
    use Queueable;

    public function __construct(private readonly int $userId)
    {}

    public function handle(): void
    {
        if ($this->batch()->canceled()) {
            return;
        }

        // ...
    }
}
```

---

### Advanced Batch Monitoring

Batches provide more monitoring possibilities through various callback methods, allowing you to execute code at different points in the batch lifecycle:

**app/Http/Controllers/Auth/RegisteredUserController.php:**

```php
use Throwable;
use Illuminate\Bus\Batch;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        // ...

        Bus::batch([
            new CreateFirstTask($user->id),
            new SendWelcomeMessage($user->id),
            new SendRegisteredUserNotification($user),
        ])->before(function (Batch $batch) {
            // The batch has been created but no jobs have been added...
        })->progress(function (Batch $batch) {
            // A single job has completed successfully...
        })->then(function (Batch $batch) {
            // All jobs completed successfully...
        })->catch(function (Batch $batch, Throwable $e) {
            // First batch job failure detected...
        })->finally(function (Batch $batch) {
            // The batch has finished executing...
        })->dispatch();

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

---

## Summary

In Laravel, you have two powerful ways to group jobs:

- **Chains** - Execute jobs sequentially, with each subsequent job depending on the success of the previous one
- **Batches** - Group jobs together with advanced monitoring and lifecycle callbacks
