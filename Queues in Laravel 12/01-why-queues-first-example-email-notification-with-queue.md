# Why Queues? First Example - Email Notification with Queue

**Summary of this lesson:**
- Explaining why queues are needed: to prevent user wait times during slow operations like email sending
- Setting up queue **database** driver in Laravel 11 (default) and migrations
- Making Notifications queueable with ShouldQueue interface and **Queueable** trait
- Processing jobs with queue worker using **php artisan queue:work**
- Additional benefits: scalability on separate servers and automatic job retrying

Welcome to the course. We will begin talking about [Laravel queues](https://laravel.com/docs/queues) from a classic simple example: sending an email.

---

## Why You Would Need Queues?

Sending an email takes time. If, for example, after registration, you need to send emails to administrators, this would fire several email notifications, and that could take a few seconds. Those couple of seconds are too long for the user to wait.

---

## Example: Email Notifications to Administrators

Imagine a typical Laravel application scenario. The user registers to your application, and after it is created in the database, a mail notification to the admins is sent.

In the registration Controller, we get all the admins and send them the `RegisteredUserNotification` notification.

```php
use Illuminate\Support\Facades\Notification;
use App\Notifications\RegisteredUserNotification;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        $admins = User::where('is_admin', true)->get();
        Notification::send($admins, new RegisteredUserNotification($user));

        event(new Registered($user));

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

The notification itself is very basic, a simple mail message.

```php
use App\Models\User;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class RegisteredUserNotification extends Notification
{
    public function __construct(private readonly User $user)
    {
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->line('Hi, ' . $notifiable->name)
            ->line('New user has registered!')
            ->line('Email: ' . $this->user->email)
            ->line('Thank you for using our application!');
    }

    public function toArray($notifiable): array
    {
        return [];
    }
}
```

When sending emails, some external service is usually used. So, when a user registers, your Laravel application must send a request to that service and send an email.

Notifications can be sent to more than one user, which could take time, and a user who tries to register with your application has to wait for everything to finish. That's why we need queues.

A queue mechanism is a system that takes all the jobs into a queue, like in a shop. People stand in the queue. It would be the same in the Laravel system: Jobs stand in the queue. So, sending an email with a notification could be considered a Job. How that queue is implemented depends on the **driver** that you use.

---

## Queue Drivers: Database

Laravel supports a few [drivers](https://laravel.com/docs/queues#driver-prerequisites), and we will start with the `database` driver, which is the default choice for new Laravel projects starting from version 11.

When a new Laravel project is created, there is a default migration to use [`database`](https://laravel.com/docs/queues#database) as a queue driver.

**database/migrations/0001_01_01_000002_create_jobs_table.php**:

```php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->id();
            $table->string('queue')->index();
            $table->longText('payload');
            $table->unsignedTinyInteger('attempts');
            $table->unsignedInteger('reserved_at')->nullable();
            $table->unsignedInteger('available_at');
            $table->unsignedInteger('created_at');
        });

        Schema::create('job_batches', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name');
            $table->integer('total_jobs');
            $table->integer('pending_jobs');
            $table->integer('failed_jobs');
            $table->longText('failed_job_ids');
            $table->mediumText('options')->nullable();
            $table->integer('cancelled_at')->nullable();
            $table->integer('created_at');
            $table->integer('finished_at')->nullable();
        });

        Schema::create('failed_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('jobs');
        Schema::dropIfExists('job_batches');
        Schema::dropIfExists('failed_jobs');
    }
};
```

If your application is older and you want to add queues with a database driver, this migration file can be added using an Artisan command.

```bash
php artisan make:queue-table
php artisan migrate
```

Now, configuring the `.env` file for this driver.

Available queue connections can be found in the `config/queue.php` config file. The default connection can be set there, or it is better to do that in the `.env` file.

**.env**:

```
// ...
BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
// ...
```

---

## ShouldQueue and Queueable

Notifications to be sent to a queue must implement a `ShouldQueue` interface and have a `Queueable` trait.

```php
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class RegisteredUserNotification extends Notification implements ShouldQueue
{
    use Queueable;

    // ...
}
```

Now, after registration, the user is instantly redirected to the dashboard without any delay.

In the database table `jobs`, we can see not-yet-executed jobs for each user. In my case, I have three admin users, so I have three jobs.

![Jobs in database table](images/jobs-in-database-table.png)

The `payload` column contains all the details about the job. It also includes columns for how many attempts will be made, when it is available, and when it was created.

![Job payload details](images/job-payload-details.png)

---

## Processing Jobs: Running Queue Worker(s)

Those jobs in the DB table are not yet executed, just queued. How do you **process** these jobs?

From the server, you need to have so-called **workers**.

There are basically three concepts about queues that you need to know:

- A **job**
- that goes into a **queue**
- and is processed by a **queue worker**

For now, we created a job as a Laravel notification and put it into a queue when we added `shouldQueue` trait. Now, we need to start a worker to process all the jobs currently in the database table.

To do that, from the terminal, we call an Artisan command:

```bash
php artisan queue:work
```

As a result, we can see that jobs are being run and done successfully.

![Queue worker processing jobs](images/queue-worker-processing.png)

Now the database `jobs` table is empty. All the jobs have been processed.

![Empty jobs table](images/empty-jobs-table.png)

And now three emails have been sent.

![Emails sent successfully](images/emails-sent.png)

That `queue:work` would work in the background for an unlimited amount of time until we suspend it from the terminal or restart the server. It would automatically listen for new jobs to appear, and then process them with the help of the queue worker.

Speaking of queue workers, we will have separate lessons about configuring them. You need to have enough workers if you have many jobs, especially for bigger systems to process jobs quicker. But in this example, you saw the **core** concepts around queues.

---

## Two More Benefits of Queues

Finally, I will mention two more benefits of using queues.

- **Scale queues on separate server(s)**. You can launch the jobs on a totally separate queue server or even a few servers. You can scale it totally separately, without loading your main application server.
- **Retrying jobs**. What if that email notification fails because of an external service failure, network connection, missing parameters, or just a 500 error in your application? It is really easy to configure the number of retries or retries until a certain period or process the failures. I will show all of that later in the course.

Basically, the queue mechanism makes the whole system really convenient for processing separate background jobs while your main application takes care of the web interface and the main functionality.

## Comments

### Important User Comments:

1. **Daniele Iseppi 3A** (2 weeks ago) had an issue with queues stopping after executing a few jobs when using `php artisan queue:work --queue=sync`. They were trying to use queues to manage system integration processes and schedule them using routes/console.php.

   **Modestas** responded explaining that routes/console.php is for the scheduler, not queues. They suggested:
   - For local development, look into the queue:listen lesson
   - For production, use a daemon to launch a queue worker
   - Create a new Command that dispatches jobs and schedule that command instead of using closures in console.php
   - This allows manual re-triggering with `php artisan trigger:sync-customers-and-shipments`