# 08 - Idempotent Jobs: Double-Check Everything to Avoid Failure

**Summary of this lesson:**
- Creating idempotent jobs that produce the same result when run multiple times
- Handling partial job execution failures (when some operations succeed before failure)
- Checking conditions before performing actions to prevent duplicate operations
- Passing IDs instead of entire models to make jobs more resilient when data changes

When working with Laravel queues, one common challenge is ensuring that jobs remain reliable regardless of **when** they're executed or **how many times** they might run.

In this lesson, we'll explore the concept of **idempotent** jobs.

---

## The Unpredictable Nature of Queue Jobs

Queue jobs don't execute immediately, and their execution timing can be unpredictable. By the time a job runs:

- Database records might have changed
- Related models could have been deleted
- Previous job attempts might have partially succeeded
- System conditions might be different

We need to handle these scenarios gracefully. Let's examine them with practical examples.

---

## Example: Message Read Status Tracker

Consider a messaging system where we need to mark messages as read and notify the sender when this happens:

```php
use App\Models\Message;
use App\Jobs\MarkMessageAsRead;

$message = Message::create([
    'sender_id' => 1,
    'recipient_id' => 4,
    'title' => 'Test',
    'message' => 'Hello World',
]);

MarkMessageAsRead::dispatch($message);
```

Our job implementation looks like this:

```php
use App\Models\Message;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Notification;
use App\Notifications\MessageIsReadNotification;

class MarkMessageAsRead implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly Message $message)
    {}

    public function handle(): void
    {
        $this->message->update(['read_at' => now()]);

        Notification::send($this->message->sender, new MessageIsReadNotification($this->message));
    }
}
```

When everything works as expected, the message gets marked as read and the notification is sent:

![Message marked as read successfully](images/08-message-read-success.png)

---

## Problem 1: Partial Job Success

What happens if one part of the job succeeds but another fails? Let's say there's a typo in the notification class name. The job will fail during execution:

![Job failed with notification error](images/08-job-failed-notification.png)

However, by this point, the `read_at` timestamp has already been updated in the database:

![Database showing read_at already updated](images/08-read-at-already-set.png)

Now, when we fix the bug and retry the job, the `read_at` timestamp will be updated again with a new value:

![Read_at timestamp updated again](images/08-read-at-updated-twice.png)

This creates inaccurate data - the message appears to have been read at a later time than it actually was.

---

## Making Our Job Idempotent

The solution is to make the job idempotent by adding a check before updating the timestamp:

```php
class MarkMessageAsRead implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly Message $message)
    {}

    public function handle(): void
    {
        if (is_null($this->message->read_at)) {
            $this->message->update(['read_at' => now()]);
        }

        Notification::send($this->message->sender, new MessageIsReadNotification($this->message));
    }
}
```

With this change, even if the job runs multiple times, the `read_at` timestamp will only be set once - the first time the message is processed.

---

## Problem 2: Missing Models

Another common issue occurs when models get deleted between the time a job is dispatched and when it runs. For example, what if the message author deletes the message after sending it?

![Model not found exception](images/08-model-not-found.png)

This would cause a `ModelNotFoundException` and the job would fail.

---

## Defensive Job Programming

To make our jobs more resilient, we should:

- Pass IDs instead of entire model instances when possible
- Check for model existence before performing operations
- Gracefully handle missing data rather than letting the job fail

Here's an improved version of our job:

```php
use App\Models\Message;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Notification;
use App\Notifications\MessageIsReadNotification;

class MarkMessageAsRead implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly int $messageId)
    {}

    public function handle(): void
    {
        $message = Message::find($this->messageId);

        if ($message) {
            if (is_null($message->read_at)) {
                $message->update(['read_at' => now()]);
            }

            Notification::send($message->sender, new MessageIsReadNotification($message));
        }
    }
}
```

This implementation:

- Accepts a message ID instead of a message instance
- Tries to find the message in the database
- Only proceeds if the message exists
- Only updates the `read_at` timestamp if it hasn't been set already
