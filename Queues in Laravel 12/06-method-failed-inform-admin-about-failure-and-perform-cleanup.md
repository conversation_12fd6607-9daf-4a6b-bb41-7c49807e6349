# 06 - Method failed(): Inform Admin About Failure and Perform Cleanup

**Summary of this lesson:**
- Implementing the **failed()** method in job classes to handle failures gracefully
- Using the method to notify administrators about job failures
- Creating custom logs or notifications when jobs fail
- Performing cleanup operations when a job fails (e.g., deleting records)

In the previous lessons, we've learned how to set up and use Laravel queues. Now, let's explore what happens when jobs fail and how we can handle these failures properly using the [failed() method](https://laravel.com/docs/queues#cleaning-up-after-failed-jobs).

---

## Implementing the failed() Method

You can define a `failed()` method directly in your job class. This method will be automatically called by <PERSON><PERSON> when the job fails to process.

Let's add a simple implementation to our `SendRegisteredUserNotification` job:

```php
use Throwable;

class SendRegisteredUserNotification implements ShouldQueue
{
    // ...

    public function failed(?Throwable $exception): void
    {
        info('Failed to process notification: ' . get_class($exception) . ' - ' . $exception->getMessage());
    }
}
```

The `failed()` method receives the exception that caused the job to fail as its parameter, giving you access to all the details about what went wrong.

---

## How It Works in Practice

When a job fails and you have the `failed()` method defined, <PERSON>vel will:

- First call your custom `failed()` method
- Then proceed with the default behavior of logging the error details

Here's what you'll see in your application logs after a job failure:

**storage/logs/laravel.log:**

```
[2025-02-19 08:16:16] local.INFO: Failed to process notification: Illuminate\View\ViewException - Undefined variable $email (View: /Users/<USER>/Herd/project/resources/views/emails/registered-user.blade.php)  

[2025-02-19 08:16:16] local.ERROR: Undefined variable $email (View: /Users/<USER>/Herd/project/resources/views/emails/registered-user.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Undefined variable $email (View: /Users/<USER>/Herd/project/resources/views/emails/registered-user.blade.php) at /Users/<USER>/Herd/project/storage/framework/views/c7f97b88c20dd50edf60abd0b881e66e.php:1)
[stacktrace]
// ...
```

The first line is our custom log message, and the second is Laravel's detailed error logging with the stack trace.

---

## Real-World Use Cases for the failed() Method

While our example simply logs the failure, in real applications you might want to:

- **Send a notification to administrators**

```php
public function failed(?Throwable $exception): void
{
    Notification::route('mail', '<EMAIL>')
        ->notify(new JobFailedNotification($this, $exception));
}
```

- **Clean up partial data**

If your job creates records or files before failing, you can use the `failed()` method to clean them up:

```php
public function failed(?Throwable $exception): void
{
    // Remove any temporary files created
    Storage::delete($this->temporaryFilePath);
    
    // Or delete a partially created record
    if ($this->createdRecordId) {
        Model::find($this->createdRecordId)->delete();
    }
}
```

- **Log to error tracking services**

For production applications, you might want to report exceptions to services like Bugsnag, Flare, or Sentry:

```php
public function failed(?Throwable $exception): void
{
    report($exception); // This will use your configured error reporting service
}
```
