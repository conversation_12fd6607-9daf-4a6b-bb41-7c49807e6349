# 03 - Regular Jobs: Creating, Dispatching and Adding Parameters

**Summary of this lesson:**
- Creating dedicated Job classes with **php artisan make:job** (includes ShouldQueue by default)
- Dispatching jobs using **JobName::dispatch()** method
- Passing parameters to jobs through constructor and using them in the handle() method
- Serializing models in job payloads for use during processing

In the first two lessons of the course, we examined examples of classes that can easily implement `ShouldQueue`.

But generally speaking, and as I mentioned previously, there are three things for queues: jobs, queues, and workers. So, a job is a global thing that you can generate and put into a queue. Let's try to generate a simple job and put it into a queue.

---

## Creating a Laravel Job

First, let's create a job using the Artisan command:

```bash
php artisan make:job SendRegisteredUserNotification
```

The main difference from classes we covered in the previous lessons is that jobs are generated by default with the `Queueable` trait added and the `ShouldQueue` interface implemented.

**app/Jobs/SendRegisteredUserNotification.php:**

```php
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendRegisteredUserNotification implements ShouldQueue
{
    use Queueable;

    public function __construct()
    {
        //
    }

    public function handle(): void
    {
        //
    }
}
```

All of the job logic is applied in the `handle()` method. Now, we can move the mail sending logic from our controller to this job.

**app/Jobs/SendRegisteredUserNotification.php:**

```php
use App\Models\User;
use App\Mail\RegisteredUserMail;
use Illuminate\Support\Facades\Mail;

class SendRegisteredUserNotification implements ShouldQueue
{
    // ...

    public function handle(): void
    {
        $admins = User::where('is_admin', true)->get();

        foreach ($admins as $admin) {
            Mail::to($admin)->send(new RegisteredUserMail());
        }
    }
}
```

Since our job now handles the queueing, the mail class should no longer contain the `ShouldQueue` interface:

**app/Mail/RegisteredUserMail.php:**

```php
class RegisteredUserMail extends Mailable implements ShouldQueue
class RegisteredUserMail extends Mailable
{
    // ...
}
```

---

## Dispatching Jobs

How do we [dispatch](https://laravel.com/docs/queues#dispatching-jobs) that job? There are various ways to do that, but one common approach is to call the job and use the `dispatch` method.

**app/Http/Controllers/Auth/RegisterUserController.php:**

```php
use App\Jobs\SendRegisteredUserNotification;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        event(new Registered($user));

        SendRegisteredUserNotification::dispatch();

        $admins = User::where('is_admin', true)->get();

        foreach ($admins as $admin) {
            Mail::to($admin)->send(new RegisteredUserMail());
        }

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

After a user registers, we can see this job queued in the `jobs` table:

![Job queued in database table](images/03-job-queued-table.png)

Once the queue worker starts, the job is processed:

![Job processed by queue worker](images/03-job-processed.png)

---

## Passing Parameters to Jobs

It's very common for jobs to accept parameters. For example, let's modify our job to accept the newly created user.

First, we update our controller to pass the user to the job:

**app/Http/Controllers/Auth/RegisterUserController.php:**

```php
class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        event(new Registered($user));

        SendRegisteredUserNotification::dispatch();
        SendRegisteredUserNotification::dispatch($user);

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

Then, inside the job, we need to accept that user as a parameter and use it when needed:

**app/Jobs/SendRegisteredUserNotification.php:**

```php
use App\Models\User;

class SendRegisteredUserNotification implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly User $user)
    {}

    public function handle(): void
    {
        $admins = User::where('is_admin', true)->get();

        foreach ($admins as $admin) {
            Mail::to($admin)->send(new RegisteredUserMail($this->user));
        }
    }
}
```

Now, our mail class must also accept the user and pass its data to the view:

**app/Mail/RegisteredUserMail.php:**

```php
use App\Models\User;

class RegisteredUserMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(private readonly User $user)
    {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Registered User Mail',
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.registered-user',
            with: [
                'name' => $this->user->name,
            ]
        );
    }

    // ...
}
```

Finally, we can use the user's name in our email template:

**resources/views/emails.registered-user.blade.php:**

```blade
Hi, a new user {{ $name }} is registered.
```

---

## How It All Works Together

To summarize the flow:

- The user is passed from the controller to the job as a parameter
- In the job, the user is passed to the mail class
- The mail class provides the user data to the view

After a user registers, we can see the queued job in the `jobs` table. Notice that the serialized user model is also shown in the `payload`:

![Job with serialized user in payload](images/03-job-serialized-user.png)

After starting the queue worker, the job is processed:

![Job with user processed](images/03-job-with-user-processed.png)

When we check the resulting email, it contains the registered user's name:

![Email with user name](images/03-email-with-user-name.png)
