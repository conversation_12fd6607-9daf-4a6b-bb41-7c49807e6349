# 05 - How to Retry the Failed Job Manually: Many Options

**Summary of this lesson:**
- Retrying a specific failed job using **php artisan queue:retry {uuid}** command
- Retrying all failed jobs with **php artisan queue:retry all**
- Setting job retry attempts using the **--tries** flag when running queue workers
- Configuring retry attempts within job classes using **$tries** property or **tries()** method

In this lesson, we will discuss how to retry a failed job in Laravel queues. There are multiple approaches to handling retries, and we'll explore each one.

---

## Manually Retrying a Failed Laravel Job

Let's begin with the scenario from our previous lesson where we had an error in our email template. The template was trying to use a variable that wasn't passed from the mail class. We've now fixed that error by passing the missing variable:

**app/Mail/RegisteredUserMail.php:**

```php
class RegisteredUserMail extends Mailable
{
    // ...

    public function content(): Content
    {
        return new Content(
            view: 'emails.registered-user',
            with: [
                'name' => $this->user->name,
                'email' => $this->user->email,
            ]
        );
    }
}
```

After fixing the code, we restart the queue worker. However, the job isn't automatically processed because it's now stored in the `failed_jobs` table instead of the `jobs` table.

To retry a specific failed job, we need to use the `queue:retry` Artisan command with the job's ID. In Laravel, each failed job has a unique identifier in the `uuid` column:

![Failed job UUID in database](images/05-failed-job-uuid.png)

We can use this UUID to retry the specific job:

```bash
php artisan queue:retry e0357724-a97b-4400-859c-9e25bd6b4e86
```

This command moves the job from the `failed_jobs` table back to the `jobs` table, where it will be picked up and processed by the queue worker:

![Job retried and processed](images/05-job-retried.png)

If you want to retry all failed jobs at once, you can use the same command with the `all` parameter:

```bash
php artisan queue:retry all
```

This will re-add all jobs from the `failed_jobs` table to the `jobs` table for processing.

---

## Configuring Automatic Retry Attempts

In many cases, jobs don't fail because of code errors but due to external factors, such as temporary API outages or network issues. For these scenarios, it's useful to configure automatic retry attempts.

### Using Command Line Options

When starting the queue worker, you can specify how many attempts should be made before a job is considered failed by using the `--tries` parameter:

```bash
php artisan queue:work --tries=3
```

With this configuration, a job will be attempted up to three times before being moved to the `failed_jobs` table:

![Multiple retry attempts](images/05-multiple-retry-attempts.png)

In the example above, I have two failing jobs, but they are only marked as failed after the third attempt.

### Setting Retry Attempts in the Job Class

Alternatively, you can define the number of retry attempts directly in your job class. There are two ways to do this:

- Using a public property:

```php
class SendRegisteredUserNotification implements ShouldQueue
{
    use Queueable;

    public $tries = 5;

    // ...
}
```

- Using a method:

```php
class SendRegisteredUserNotification implements ShouldQueue
{
    use Queueable;

    // ...

    public function tries(): int
    {
        return 5;
    }
}
```

Using the job class approach is particularly useful when different jobs require different retry policies. You might want some critical jobs to be retried more times than others.
