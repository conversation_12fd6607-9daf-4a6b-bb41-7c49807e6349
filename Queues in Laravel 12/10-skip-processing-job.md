# 10 - Skip Processing Job

**Summary of this lesson:**
- Implementing job skipping functionality with **Skip::when()** middleware
- Adding middleware in the job class using the **middleware()** method
- Creating conditions to skip job execution based on specific criteria (e.g., email verification status)
- Jobs are marked as processed but their **handle()** method is not executed when skipped

In this lesson, we'll explore how to **skip processing jobs conditionally** in Laravel queues, when queued jobs should be bypassed instead of executed.

---

## Don't Process Job On Condition

Sometimes you might need to skip processing a job based on certain conditions. For example, imagine you've delayed dispatching a job when a user registers (as we saw in a previous lesson), but by the time the job is processed, the user still hasn't verified their email, making the job unnecessary.

Laravel provides an elegant way to handle this using **job middleware**.

---

## Using Skip Middleware

Jobs can have [middleware](https://laravel.com/docs/queues#job-middleware) defined in a `middleware()` method that returns an array. To [skip](https://laravel.com/docs/queues#skipping-jobs) a job based on a condition, you can use the `Skip::when()` middleware with a boolean expression.

**app/Jobs/SendRegisteredUserNotification.php:**

```php
use Illuminate\Queue\Middleware\Skip;

class SendRegisteredUserNotification implements ShouldQueue
{
    // ...

    public function handle(): void
    {
        $admins = User::where('is_admin', true)->get();

        foreach ($admins as $admin) {
            Mail::to($admin)->send(new RegisteredUserMail($this->user));
        }
    }

    public function middleware(): array
    {
        return [
            Skip::when(is_null($this->user->email_verified_at)),
        ];
    }
}
```

---

## How Skip Works

When the job reaches the worker for processing, it first runs through the middleware chain. If the condition inside `Skip::when()` evaluates to `true`, the job will be marked as successfully processed, but everything in the `handle()` method will be skipped entirely.

In our example, if the user hasn't verified their email (`email_verified_at` is null), the notification emails to admins won't be sent, but the job will still be considered successfully completed.

This approach has several benefits:

- It prevents unnecessary processing
- The job doesn't count as failed
- You don't need to write conditional logic inside your `handle()` method

---

## Real-World Use Cases

Here are some practical scenarios where you might use the Skip middleware:

- Skip sending a reminder email if the user has already completed the action
- Skip a subscription renewal job if the user has already canceled
- Skip a data processing job if the source data no longer exists
- Skip a notification if the settings have changed since the job was dispatched

---

## Multiple Skip Conditions

You can also combine multiple Skip middleware instances or create more complex conditions:

```php
public function middleware(): array
{
    return [
        Skip::when(function () {
            return is_null($this->user->email_verified_at) 
                || $this->user->is_banned;
        }),
    ];
}
```

The Skip middleware gives you a clean, declarative way to control job execution based on your application's business logic.
