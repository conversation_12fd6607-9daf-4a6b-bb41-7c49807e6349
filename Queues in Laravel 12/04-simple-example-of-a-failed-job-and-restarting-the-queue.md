# 04 - Simple Example of a Failed Job and Restarting the Queue

**Summary of this lesson:**
- Understanding how to identify when a Laravel job fails
- Seeing what happens when code changes cause a queue job to fail (missing variable in view)
- Learning to check failed jobs in the `failed_jobs` database table and examining the error message
- Understanding why you need to restart queue workers after code changes

Let's talk about job failures and what to do when a job fails. I will start with a simple example of a pretty typical scenario of why a job would fail and what happens when it does.

---

## Understanding Failed Laravel Jobs

Imagine the scenario: We have the same notification job from our previous lesson, which sends emails to admins. When we start the queue worker, it will initially be idle as there are no jobs in the queue, and it waits for new jobs to be dispatched.

After a user registers, a job is added to the queue and is processed successfully:

![Job processed successfully](images/04-job-processed-successfully.png)

Now, let's introduce an error into our code by trying to use a variable in the email template that hasn't been passed from the mail class:

**resources/views/email/registered-user.blade.php:**

```blade
Hi, a new user {{ $name }} {{ $email }} is registered.
```

Notice that we're trying to use `$email`, but we haven't provided this variable to the view in our mail class.

---

## Observing the Failure

When a worker is started again and a user tries to register, the job status now shows as failed:

![Job failed status](images/04-job-failed-status.png)

> **Important:** You have to stop and restart the queue worker after making code changes because `queue:work` loads the Laravel application into memory only once when it starts.

---

## Tracking Failed Jobs

But how can we see the actual error? Laravel provides two main ways to track failed jobs:

- **Failed Jobs Table**: In the database, there is a table called `failed_jobs`. The failed job is recorded here, and in the `exception` column, you can see the detailed error message:

![Failed jobs table with error](images/04-failed-jobs-table.png)

- **Application Logs**: Alternatively, you can check your Laravel log files for error details.

---

In this lesson, we've seen what happens when a job fails.

This is just one part of Laravel's job failure handling system that we'll explore further in upcoming lessons.
