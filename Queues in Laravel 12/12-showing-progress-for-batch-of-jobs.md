# 12 - Showing Progress for Batch of Jobs

**Summary of this lesson:**
- Using batch information to track job progress in the application UI
- Retrieving batch information with **Bus::findBatch()** method
- Accessing batch properties like **processedJobs()**, **totalJobs**, and **progress()** to display completion status
- Implementing automatic refresh with JavaScript to see real-time progress updates

In this lesson, we'll learn how to display the progress of multiple jobs running in a batch. Laravel provides several ways to do this, but we'll focus on the most straightforward approach using **batch objects**, which contain all the information we need.

---

## Sending Laravel Jobs as Batch

Let's create an array of jobs to send welcome messages to all users in the system except for the newly registered user.

**app/Http/Controllers/Auth/RegisteredUserController.php:**

```php
use App\Jobs\SendWelcomeMessage;
use Illuminate\Support\Facades\Bus;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        event(new Registered($user));

        $jobs = [];

        foreach (User::where('id', '!=', $user->id)->pluck('id') as $recipient) {
            $jobs[] = new SendWelcomeMessage($user->id, $recipient);
        }

        $batch = Bus::batch($jobs)->dispatch();

        Auth::login($user);

        return redirect(route('dashboard', absolute: false). '?batch_id=' . $batch->id);
    }
}
```

For the job itself, we've added a small delay to simulate processing time.

**app/Jobs/SendWelcomeMessage.php:**

```php
use App\Models\Message;
use Illuminate\Bus\Batchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendWelcomeMessage implements ShouldQueue
{
    use Batchable;
    use Queueable;

    public function __construct(private readonly int $userId, private readonly int $recipientId)
    {}

    public function handle(): void
    {
        usleep(50000);

        Message::create([
            'sender_id' => $this->userId,
            'recipient_id' => $this->recipientId,
            'title' => 'Welcome!',
            'message' => 'Welcome to our world',
        ]);
    }
}
```

When a user registers, they are redirected to the dashboard with a batch ID in the URL.

![Batch ID in URL](images/12-batch-id-url.png)

---

## Showing Batch Information

We can find the batch in the database using this ID. The `Bus` facade has a [helper method](https://laravel.com/docs/queues#returning-batches-from-routes) for this.

**routes/web.php:**

```php
use Illuminate\Support\Facades\Bus;

Route::get('/dashboard', function () {
    $batch = null;

    if (request()->has('batch_id')) {
        $batch = Bus::findBatch(request()->input('batch_id'));
    }

    return view('dashboard', compact('batch'));
})->middleware(['auth', 'verified'])->name('dashboard');
```

> For simplicity, we've added this code to the routes file instead of a controller.

If the batch exists, we can display its progress in the view. Laravel provides various [methods](https://laravel.com/docs/queues#inspecting-batches) to get information about a batch.

**resources/views/dashboard.blade.php:**

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    {{ __("You're logged in!") }}

                    @isset($batch)
                        Sending welcome messages:<br />
                        {{ $batch->processedJobs() }} completed out of {{ $batch->totalJobs }} ({{ $batch->progress() }}%)
                    @endisset
                </div>
            </div>
        </div>
    </div>

    <script> {{-- [tl! add:start] --}}
        @if($batch && $batch->progress() < 100)
            window.setInterval(() => window.location.reload(), 2000)
        @endif
    </script> {{-- [tl! add:end] --}}
</x-app-layout>
```

---

## What Happens in the Database

When a user registers, a batch is created in the `job_batches` table with all the jobs. In this example, our batch has 503 pending jobs.

![Batch in database](images/12-batch-database.png)

All 503 jobs are stored in the `jobs` table.

![Jobs in queue](images/12-jobs-in-queue.png)

When the queue worker runs and the progress is less than 100%, the page refreshes every two seconds to show the updated progress.

![Progress display](images/12-progress-display.png)

---

## Conclusion

This lesson demonstrated the simplest way to show job batch progress. For a better user experience, you could use Livewire, Vue.js, React, or other frontend frameworks. The key points were:

- How to get [the batch](https://laravel.com/docs/queues#returning-batches-from-routes) using its ID
- How to access [properties and methods](https://laravel.com/docs/queues#inspecting-batches) to retrieve information about the batch
