# Other Queueable Classes: Example of Mailables and Event Listeners

**Summary of this lesson:**
- Using **Event Listeners** with ShouldQueue to process notifications
- Using **Mailables** with ShouldQueue for email sending
- Comparing processing between queued Listeners (one job for multiple emails) vs Mailables (separate job per email)
- Three Laravel classes that can implement ShouldQueue: notifications, event listeners, and mailables

In the last lesson, I showed you the `ShouldQueue` implementation on the notification email notification. Now let's explore other classes that can use queues in Laravel to further improve your application's performance.

---

## What are Other Queueable Classes?

What other classes can have `ShouldQueue` in Laravel? There are four results if we go to the Laravel documentation and search for `ShouldQueue`. One of them is just about queues in general. But three more classes can be queueable. We saw notifications in a previous lesson. Also, event listeners and mails.

Let's try the other two approaches - events with listeners and emails - to implement the same functionality we did previously.

![ShouldQueue documentation search results](images/shouldqueue-documentation-search.png)

---

## Events and Listeners Class

Laravel fires a `Registered` event when a user registers and is created. This is a perfect opportunity to implement our queuing approach.

Here's how it works in the controller by default:

**app/Http/Controllers/Auth/RegisteredUserController.php**:

```php
use Illuminate\Auth\Events\Registered;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        // ...

        event(new Registered($user));

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

We can create an event listener for this `Registered` event to handle the notification process:

```bash
php artisan make:listener RegisteredUserListener --event=Registered
```

Now, instead of handling notifications in the controller, we can move that logic to the listener's `handle()` method. This provides better separation of concerns and makes our code more maintainable.

**app/Listeners/RegisteredUserListener.php**:

```php
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Notification;
use App\Notifications\RegisteredUserNotification;

class RegisteredUserListener
{
    public function __construct()
    {
        //
    }

    public function handle(Registered $event): void
    {
        $admins = User::where('is_admin', true)->get();
        Notification::send($admins, new RegisteredUserNotification($event->user));
    }
}
```

With our listener in place, we need to adjust where the `ShouldQueue` interface is implemented. In this approach, we move it from the notification to the listener:

**app/Notifications/RegisteredUserNotification.php**:

```php
class RegisteredUserNotification extends Notification implements ShouldQueue
class RegisteredUserNotification extends Notification
{
    // ...
}
```

**app/Listeners/RegisteredUserListener.php**:

```php
use Illuminate\Contracts\Queue\ShouldQueue;

class RegisteredUserListener
class RegisteredUserListener implements ShouldQueue
{
    // ...
}
```

When a user registers now, you'll see a single job in the `jobs` database table:

![Single job in database for event listener](images/single-job-event-listener.png)

After the queue worker is started, the listener is processed. One important difference to note: in this case, one job sends all three notifications, rather than having three separate jobs.

![Queue worker processing single listener job](images/queue-worker-single-listener.png)

This demonstrates a key benefit of using event listeners with queues - the ability to batch related operations into a single job.

---

## Mail Class

The third class that can implement `ShouldQueue` is a mailable. Let's generate a mailable class to handle our email sending:

```bash
php artisan make:mail RegisteredUserMail
```

The generated mailable class includes the `Queueable` trait by default, but doesn't have the `ShouldQueue` interface. Let's add a simple mail message and implement the interface:

**app/Mail/RegisteredUserMail.php**:

```php
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class RegisteredUserMail extends Mailable
class RegisteredUserMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct()
    {
        //
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Registered User Mail',
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.registered-user',
        );
    }

    public function attachments(): array
    {
        return [];
    }
}
```

Now let's create a simple email template:

**resources/views/email/registered-user.blade.php**:

```html
Hi, a new user is registered.
```

For this example, we'll send the mail directly from the controller to demonstrate how mailable queues work:

**app/Http/Controllers/Auth/RegisteredUserController.php**:

```php
use App\Mail\RegisteredUserMail;
use App\Models\User;
use Illuminate\Support\Facades\Mail;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        event(new Registered($user));

        $admins = User::where('is_admin', true)->get();
        foreach ($admins as $admin) {
            Mail::to($admin)->send(new RegisteredUserMail());
        }

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

> Don't forget to delete the listener created earlier in this lesson. Otherwise, it will be picked up when a user registers.

When a user registers now, you'll see three records in the `jobs` table, one for each email:

![Three jobs in database for mailables](images/three-jobs-mailables.png)

When a queue worker is started, three jobs are processed, one for each email. This is different from the listener approach, where a single job handled all notifications:

![Queue worker processing three mailable jobs](images/queue-worker-three-mailables.png)

---

In summary, we've explored three different classes that can implement `ShouldQueue`:

- Notifications
- Event Listeners
- Mailables

## Comments

No comments yet for this lesson.