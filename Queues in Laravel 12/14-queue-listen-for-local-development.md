# 14 - Queue:listen for Local Development

**Summary of this lesson:**
- Using **queue:listen** for development to automatically reload code changes
- Understanding that **queue:work** boots the application once and is faster for production
- **queue:listen** boots the application with each job for better development experience

In this lesson, we'll explore a helpful <PERSON><PERSON> command `queue:listen` for local development that saves time when working with queues.

---

## The Problem with `queue:work` During Development

When developing with queues, having to restart the `queue:work` command after every code change can be frustrating.

When using `queue:work`, <PERSON><PERSON> boots the application once when the command is run. This means:

- If you change your code, the worker won't see those changes
- You need to manually restart the worker after every code modification
- It's easy to forget to restart the worker, leading to confusion when your changes don't take effect

---

## Using `queue:listen` Instead

Laravel provides a better alternative for development environments:

```bash
php artisan queue:listen
```

![Queue listen running](images/14-queue-listen.png)

It automatically picks up your code changes without manual restarts.

---

## Performance Considerations

There's a reason we have both commands:

- **queue:work**: More efficient and faster - use this in production environments
- **queue:listen**: More convenient for development but has more overhead - use only in local development
