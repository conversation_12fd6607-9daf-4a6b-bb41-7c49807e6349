# 09 - Delay Jobs if Some Condition Fails

I want to show you how to [delay job execution](https://laravel.com/docs/queues#delayed-dispatching) and **retry jobs conditionally** when working with Laravel queues.

---

## Delay Start Of A Laravel Job

Sometimes you need to schedule a job to run in the future rather than immediately. For example, after a user registers, you might want to send a notification after some time.

When dispatching a job, you can chain a `delay` method which accepts a parameter - you can even use Carbon for more readable time expressions.

In this example, the job will be processed after 15 minutes, but in reality, it could take 16 or more minutes if there are other jobs processing at that time.

**app/Http/Controllers/Auth/RegisteredUserController.php:**

```php
use App\Jobs\SendRegisteredUserNotification;

class RegisteredUserController extends Controller
{
    // ...

    public function store(Request $request): RedirectResponse
    {
        // ...

        SendRegisteredUserNotification::dispatch($user)->delay(now()->addMinutes(15));

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
```

---

## Conditional Retries With Release

What if your job needs to check a condition before it should fully execute? For example, you might want to verify that a user's email is verified before sending a notification.

If the condition isn't met on the first attempt, you can push the same job back into the queue using the `release()` method.

The `release()` method allows you to specify a delay in seconds or use Carbon methods. In this example, if the user's email isn't verified yet, the job will be pushed back to the queue with a delay of 60 seconds.

**app/Jobs/SendRegisteredUserNotification.php:**

```php
class SendRegisteredUserNotification implements ShouldQueue
{
    // ...

    public function handle(): void
    {
        if (! is_null($this->user->email_verified_at)) {
            $admins = User::where('is_admin', true)->get();

            foreach ($admins as $admin) {
                Mail::to($admin)->send(new RegisteredUserMail($this->user));
            }
        } else {
            $this->release(60);
        }
    }
}
```

---

## Setting Maximum Attempts

For the `release()` method to work properly, you must configure the maximum number of attempts. Without this setting, the job might be released indefinitely, causing potential issues.

By setting the `$tries` property, you define how many times the job can be attempted before it's considered failed. After this limit is reached, the job will be moved to the failed jobs table.

**app/Jobs/SendRegisteredUserNotification.php:**

```php
class SendRegisteredUserNotification implements ShouldQueue
{
    public $tries = 3;

    // ...

    public function handle(): void
    {
        if (! is_null($this->user->email_verified_at)) {
            $admins = User::where('is_admin', true)->get();

            foreach ($admins as $admin) {
                Mail::to($admin)->send(new RegisteredUserMail($this->user));
            }
        } else {
            $this->release(60);
        }
    }
}
```

With this configuration, the job will attempt to check if the user's email is verified up to 3 times, with a 60-second delay between each attempt.

If the email still isn't verified after the third attempt, the job will fail and be recorded in the failed jobs table.
