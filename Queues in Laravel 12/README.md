# Queues in Laravel 12

In this 1-hour course, you will learn what you need to run Laravel Jobs via Queues in the background, so your users wouldn't wait for some email to be sent or a report to be processed.

## Course Information
- 15 text lessons
- 46 min read
- Created: Apr 2025
- Framework: Laravel 12

## Topics Covered

We will touch on these topics:

- How to write code for Laravel jobs and how to put them on queue
- How to run and monitor the queues
- How to deal with the failed jobs: restart, log errors, notify the developers
- What are the extra tools like Supervisor and Laravel Horizon
- ...and more.

## Course Structure

### First Examples of Queues
1. **Why Queues? First Example - Email Notification with Queue** (7 min read)
2. **Other Queueable Classes: Example of Mailables and Event Listeners** (4 min read)
3. **Regular Jobs: Creating, Dispatching and Adding Parameters** (4 min read)

### Processing Failed Jobs
4. **Simple Example of a Failed Job and Restarting the Queue** (2 min read)
5. **How to Retry the Failed Job Manually: Many Options** (3 min read)
6. **Method failed(): Inform Admin About Failure and Perform Cleanup** (3 min read)
7. **Long-Running Jobs: Timeouts, Fatal Errors and a Better Way** (5 min read)
8. **Idempotent Jobs: Double-Check Everything to Avoid Failure** (4 min read)
9. **Delay Jobs if Some Condition Fails** (3 min read)

### Extra Jobs/Queues Features and Tools
10. **Skip Processing Job** (3 min read)
11. **Jobs into Groups: Batching and Chaining** (7 min read)
12. **Showing Progress for Batch of Jobs** (4 min read)
13. **Multiple Queues and Priority Jobs** (3 min read)
14. **Queue:listen for Local Development** (1 min read)
15. **Other Queue Drivers: Redis and Laravel Horizon** (2 min read)