<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => 'Alex',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('PASSWORD'),
            'account_id'=>1,
            'company_id' => 1,
            'role'      => 'Master',
            'team'      => 'Master',
            'image_url' =>'Not Provided',
            'profile_pic'   =>'Not Provided',
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Create an admin user.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'Admin',
            'team' => 'Admin',
        ]);
    }

    /**
     * Create a master user (highest privilege).
     */
    public function master(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'Master',
            'team' => 'Master',
        ]);
    }

    /**
     * Create a regular user.
     */
    public function user(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'User',
            'team' => 'User',
        ]);
    }
}
