<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SuccessionPlan>
 */
class SuccessionPlanFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
                'Name'        => fake()->name,
                'Description' =>fake()->sentence(3),
                'created-by'  =>fake()->name,
                'status'      =>fake()->randomElement(['active','closed']),
                'created_at' => fake()->dateTimeBetween('-1 years'),
                'updated_at' => fake()->dateTimeBetween('created_at','now')
        ];
    }
}
