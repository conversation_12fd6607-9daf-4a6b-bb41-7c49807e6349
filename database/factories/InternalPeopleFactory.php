<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InternalPeople>
 */
class InternalPeopleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'employee_id' => 'E' . $this->faker->unique()->numerify('#####'),
            'forename' => $this->faker->firstName,
            'surname' => $this->faker->lastName,
            'middle_name' => $this->faker->optional()->firstName,
            'other_name' => $this->faker->optional()->firstName,
            'gender' => $this->faker->randomElement(['Male', 'Female']),
            'role_id' => $this->faker->numberBetween(100, 110),
            'diverse' => $this->faker->boolean,
            'location' => $this->faker->city,
            'linkedinURL' => $this->faker->url,
            'reports_to' => $this->faker->optional()->numberBetween(1, 1000),
            'exco' => $this->faker->boolean,
            'company_id' => 1,
            'company_name' => $this->faker->company,
            'start_date' => $this->faker->date,
            'end_date' => $this->faker->optional()->date,
            'tenure' => $this->faker->numberBetween(1, 10),
            'tenure_in_company' => $this->faker->numberBetween(1, 10),
            'function' => $this->faker->jobTitle,
            'division' => $this->faker->word,
            'seniority' => $this->faker->randomElement(['Junior', 'Mid', 'Senior']),
            'career_history' => $this->faker->optional()->text,
            'educational_history' => $this->faker->optional()->text,
            'skills' => $this->faker->words(3, true),
            'languages' => $this->faker->words(2, true),
            'other_tags' => $this->faker->optional()->words(2, true),
            'readiness' => $this->faker->boolean,
            'user_id' => $this->faker->numberBetween(1, 500),
            'created_at' => now(),
            'updated_at' => now(),
            'summary' => $this->faker->optional()->text,
            'latest_role' => $this->faker->jobTitle,
        ];
    }
}
