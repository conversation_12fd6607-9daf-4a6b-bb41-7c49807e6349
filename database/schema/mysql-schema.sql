/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accounts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `Account_name` varchar(255) NOT NULL,
  `account_type` varchar(255) NOT NULL,
  `active_users` int(11) NOT NULL,
  `users_limit` int(11) NOT NULL,
  `relationship_manager_id` varchar(255) DEFAULT NULL,
  `plans_limit` int(11) DEFAULT NULL,
  `sector_interest` text DEFAULT NULL,
  `industry_interest` text DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `company_of_interest` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `assessment_criterias`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assessment_criterias` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint(20) unsigned NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT '1' COMMENT '1 = Assessment, 2 9Box',
  `response` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `assessment_criterias_company_id_foreign` (`company_id`),
  KEY `assessment_criterias_created_by_foreign` (`created_by`),
  CONSTRAINT `assessment_criterias_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `assessment_criterias_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `career_histories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `career_histories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `people_id` bigint(20) unsigned NOT NULL,
  `role` varchar(255) NOT NULL,
  `past_company_id` bigint(20) unsigned NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `tenure` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `Column1` int(11) DEFAULT NULL,
  `past_company` varchar(50) DEFAULT NULL,
  `past_role` varchar(128) DEFAULT NULL,
  `past_end_date` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `career_histories_people_company_index` (`people_id`,`past_company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `chat_histories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `chat_histories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `topic` varchar(255) NOT NULL,
  `question` varchar(255) NOT NULL,
  `response` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `chat_histories_user_id_foreign` (`user_id`),
  CONSTRAINT `chat_histories_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `companies` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `other_name` varchar(255) DEFAULT NULL,
  `name_abbreviation` varchar(255) DEFAULT NULL,
  `parent_name` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `corporate_hq_country` text DEFAULT NULL,
  `corporate_hq_address` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `corporate_hq_phone_number` varchar(255) DEFAULT NULL,
  `chair` varchar(255) DEFAULT NULL,
  `ceo` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `industry` varchar(255) DEFAULT NULL,
  `sector` varchar(255) DEFAULT NULL,
  `stock_symbol` varchar(255) DEFAULT NULL,
  `Annual_Revenue` bigint(20) DEFAULT NULL,
  `Annual_Net_Profit_Margin` double(8,2) DEFAULT NULL,
  `Annual_Net_Expenses` double(8,2) DEFAULT NULL,
  `Annual_YOY_Revenue_Change` double(8,2) DEFAULT NULL,
  `company_employee_count` bigint(20) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `other_name(s)` varchar(50) DEFAULT NULL,
  `parent_company` varchar(50) DEFAULT NULL,
  `annual_expenses` int(11) DEFAULT NULL,
  `company_employees_count` varchar(50) DEFAULT NULL,
  `Earnings_Before_Interest_Taxes` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `companies_name_index` (`name`),
  KEY `companies_id_index` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `internal_career_histories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `internal_career_histories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `people_id` bigint(20) unsigned NOT NULL,
  `role` varchar(255) NOT NULL,
  `past_company_id` bigint(20) unsigned NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `tenure` double DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `internal_career_histories_people_id_foreign` (`people_id`),
  KEY `internal_career_histories_past_company_id_foreign` (`past_company_id`),
  CONSTRAINT `internal_career_histories_past_company_id_foreign` FOREIGN KEY (`past_company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `internal_career_histories_people_id_foreign` FOREIGN KEY (`people_id`) REFERENCES `internal_people` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `internal_people`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `internal_people` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `employee_id` varchar(255) DEFAULT NULL,
  `forename` varchar(255) DEFAULT NULL,
  `surname` varchar(255) DEFAULT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `other_name` varchar(255) DEFAULT NULL,
  `gender` varchar(255) DEFAULT 'Not Applicable',
  `role_id` int(11) DEFAULT NULL,
  `diverse` varchar(255) DEFAULT NULL,
  `location` varchar(255) NOT NULL DEFAULT 'Not Applicable',
  `linkedinURL` varchar(255) DEFAULT NULL,
  `reports_to` bigint(20) unsigned DEFAULT NULL,
  `exco` varchar(255) DEFAULT 'Not Applicable',
  `company_id` bigint(20) unsigned NOT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `tenure` int(11) NOT NULL DEFAULT 0,
  `tenure_in_company` int(11) DEFAULT NULL,
  `function` varchar(255) DEFAULT 'Not Applicable',
  `division` varchar(255) DEFAULT 'Not Applicable',
  `seniority` varchar(255) DEFAULT NULL,
  `career_history` text DEFAULT NULL,
  `educational_history` text DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `languages` text DEFAULT NULL,
  `other_tags` text DEFAULT NULL,
  `readiness` varchar(255) DEFAULT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `summary` text DEFAULT NULL,
  `latest_role` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `internal_people_company_id_index` (`company_id`),
  KEY `internal_people_forename_index` (`forename`),
  KEY `internal_people_surname_index` (`surname`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `internal_requirement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `internal_requirement` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `organisation_people_id` bigint(20) unsigned NOT NULL,
  `assessment_criteria_id` bigint(20) unsigned NOT NULL,
  `response` varchar(255) DEFAULT NULL,
  `note` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `internal_requirement_organisation_people_id_foreign` (`organisation_people_id`),
  KEY `internal_requirement_assessment_criteria_id_foreign` (`assessment_criteria_id`),
  CONSTRAINT `internal_requirement_assessment_criteria_id_foreign` FOREIGN KEY (`assessment_criteria_id`) REFERENCES `assessment_criterias` (`id`) ON DELETE CASCADE,
  CONSTRAINT `internal_requirement_organisation_people_id_foreign` FOREIGN KEY (`organisation_people_id`) REFERENCES `organisation_peoples` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `internal_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `internal_skills` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `internal_people` bigint(20) unsigned NOT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `skill_name` varchar(255) NOT NULL,
  `skill_type` varchar(255) NOT NULL,
  `skill_rating` double(8,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `internal_skills_internal_people_foreign` (`internal_people`),
  CONSTRAINT `internal_skills_internal_people_foreign` FOREIGN KEY (`internal_people`) REFERENCES `internal_people` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `interviews`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interviews` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `stage_name` varchar(255) NOT NULL,
  `stage_number` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `invites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invites` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `url` text DEFAULT NULL,
  `used` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `job_people`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_people` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `job_id` bigint(20) unsigned NOT NULL,
  `pipeline_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `people_id` bigint(20) unsigned NOT NULL,
  `headline` text NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `other_name` varchar(255) DEFAULT NULL,
  `gender` varchar(255) DEFAULT NULL,
  `diverse` varchar(255) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `linkedinURL` varchar(255) DEFAULT NULL,
  `latest_role` varchar(255) DEFAULT NULL,
  `company_id` bigint(20) unsigned DEFAULT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `tenure` int(11) DEFAULT NULL,
  `function` varchar(255) DEFAULT NULL,
  `division` varchar(255) DEFAULT NULL,
  `seniority` varchar(255) DEFAULT NULL,
  `career_history` text DEFAULT NULL,
  `educational_history` text DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `languages` text DEFAULT NULL,
  `other_tags` text DEFAULT NULL,
  `notes` text NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `readiness` varchar(255) DEFAULT NULL,
  `summary` text DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `job_people_user_id_foreign` (`user_id`),
  KEY `job_people_job_id_foreign` (`job_id`),
  KEY `job_people_company_id_foreign` (`company_id`),
  KEY `job_people_pipeline_id_foreign` (`pipeline_id`),
  CONSTRAINT `job_people_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `job_people_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE,
  CONSTRAINT `job_people_pipeline_id_foreign` FOREIGN KEY (`pipeline_id`) REFERENCES `pipelines` (`id`) ON DELETE CASCADE,
  CONSTRAINT `job_people_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `job_queues`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_queues` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` int(11) NOT NULL DEFAULT 0,
  `reserved` tinyint(4) NOT NULL DEFAULT 0,
  `reserved_at` bigint(20) unsigned DEFAULT NULL,
  `available_at` bigint(20) unsigned NOT NULL,
  `created_at` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `job_requirements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_requirements` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `job_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `job_requirements_job_id_foreign` (`job_id`),
  CONSTRAINT `job_requirements_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `job_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `job_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `minimum_Experience` int(11) DEFAULT NULL,
  `ethnicity` tinyint(1) NOT NULL DEFAULT 0,
  `step_up` tinyint(1) NOT NULL DEFAULT 0,
  `description` text NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `status` varchar(255) NOT NULL,
  `mover` varchar(255) DEFAULT NULL,
  `candidate_status` varchar(255) NOT NULL,
  `age` double(8,2) DEFAULT NULL,
  `last_opened` datetime DEFAULT NULL,
  `shared_with` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_user_id_foreign` (`user_id`),
  CONSTRAINT `jobs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `locations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `city_name` varchar(255) NOT NULL,
  `country_name` varchar(255) NOT NULL,
  `country_code` varchar(255) DEFAULT NULL,
  `timezone` double(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `people_id` bigint(20) unsigned DEFAULT NULL,
  `plan_id` bigint(20) unsigned DEFAULT NULL,
  `entity_name` varchar(255) NOT NULL,
  `entity_company` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `user_company` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `organisation_peoples`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `organisation_peoples` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `organisation_id` bigint(20) unsigned NOT NULL,
  `internal_people_id` bigint(20) unsigned NOT NULL,
  `plan_id` int(11) DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) unsigned NOT NULL,
  `parent_id` bigint(20) unsigned DEFAULT NULL,
  `parent_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`parent_ids`)),
  PRIMARY KEY (`id`),
  KEY `organisation_peoples_created_by_foreign` (`created_by`),
  KEY `organisation_peoples_organisation_id_foreign` (`organisation_id`),
  KEY `organisation_peoples_internal_people_id_foreign` (`internal_people_id`),
  KEY `organisation_peoples_parent_id_foreign` (`parent_id`),
  CONSTRAINT `organisation_peoples_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `organisation_peoples_organisation_id_foreign` FOREIGN KEY (`organisation_id`) REFERENCES `organisations` (`id`),
  CONSTRAINT `organisation_peoples_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `organisation_peoples` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `organisations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `organisations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `shared` text DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `organisation_created_by_foreign` (`created_by`),
  CONSTRAINT `organisation_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `people`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `people` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `forename` varchar(255) DEFAULT NULL,
  `surname` varchar(255) DEFAULT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `other_name` varchar(255) DEFAULT NULL,
  `gender` varchar(255) DEFAULT NULL,
  `diverse` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `linkedinURL` varchar(255) DEFAULT NULL,
  `parent_role` varchar(255) DEFAULT NULL,
  `latest_role` varchar(255) NOT NULL,
  `exco` varchar(255) DEFAULT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `tenure` int(11) NOT NULL,
  `function` varchar(255) DEFAULT NULL,
  `division` varchar(255) DEFAULT NULL,
  `seniority` varchar(255) DEFAULT NULL,
  `career_history` text DEFAULT NULL,
  `educational_history` text DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `languages` text DEFAULT NULL,
  `other_tags` text DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  `readiness` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `summary` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `people_company_id_foreign` (`company_id`),
  KEY `people_created_at_index` (`created_at`),
  KEY `people_updated_at_index` (`updated_at`),
  KEY `idx_optimized` (`company_name`,`company_id`,`status`),
  KEY `idx_country_status_company` (`country`,`status`,`company_id`),
  KEY `idx_function_status_company` (`function`,`status`,`company_id`),
  KEY `idx_exco_status_company` (`exco`,`status`,`company_id`),
  KEY `idx_gender_status_company` (`gender`,`status`,`company_id`),
  CONSTRAINT `people_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `pipelines`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pipelines` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned DEFAULT NULL,
  `job_id` bigint(20) unsigned DEFAULT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `people_id` bigint(20) unsigned NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `other_name` varchar(255) DEFAULT NULL,
  `gender` varchar(255) DEFAULT NULL,
  `diverse` varchar(255) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `mover` varchar(255) DEFAULT NULL,
  `linkedinURL` varchar(255) DEFAULT NULL,
  `latest_role` varchar(255) NOT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `tenure` int(11) DEFAULT NULL,
  `function` varchar(255) DEFAULT NULL,
  `division` varchar(255) DEFAULT NULL,
  `seniority` varchar(255) DEFAULT NULL,
  `exco` varchar(255) DEFAULT NULL,
  `career_history` text DEFAULT NULL,
  `contact_number` text DEFAULT NULL,
  `email_address` text DEFAULT NULL,
  `educational_history` text DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `languages` text DEFAULT NULL,
  `other_tags` text DEFAULT NULL,
  `skills_match` double(8,2) DEFAULT NULL,
  `education_match` double(8,2) DEFAULT NULL,
  `location_match` double(8,2) DEFAULT NULL,
  `role_match` double(8,2) DEFAULT NULL,
  `gender_match` double(8,2) DEFAULT NULL,
  `tenure_match` double(8,2) DEFAULT NULL,
  `total_score` double(8,2) DEFAULT NULL,
  `people_type` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `summary` text DEFAULT NULL,
  `readiness` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `pipelines_function_index` (`function`),
  KEY `pipelines_division_index` (`division`),
  KEY `pipelines_company_id_index` (`company_id`),
  KEY `pipelines_company_name_index` (`company_name`),
  KEY `pipelines_plan_people_company_index` (`plan_id`,`people_id`,`company_name`),
  KEY `job_people_index` (`job_id`,`people_id`),
  KEY `company_index` (`company_name`,`company_id`),
  KEY `function_division_index` (`function`,`division`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `plan_scores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `plan_scores` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `succession_plan_id` bigint(20) unsigned NOT NULL,
  `metric_name` varchar(255) NOT NULL,
  `score` double(8,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `plan_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `plan_users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `succession_plan_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `plans_summaries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `plans_summaries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL,
  `gen_diversity` double(8,2) NOT NULL DEFAULT 0.00,
  `int_ext_diversity` double(8,2) NOT NULL DEFAULT 0.00,
  `average_tenurescore` double(8,2) NOT NULL DEFAULT 0.00,
  `average_tenure` double(8,2) NOT NULL DEFAULT 0.00,
  `readiness_score` double(8,2) NOT NULL DEFAULT 0.00,
  `skills_score` double(8,2) NOT NULL DEFAULT 0.00,
  `plan_age` double(8,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `search_histories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `search_histories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `search` text NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `skills` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `people_id` bigint(20) unsigned NOT NULL,
  `skill_name` varchar(255) NOT NULL,
  `skill_type` varchar(255) NOT NULL,
  `skill_age` int(11) DEFAULT NULL,
  `skill_rating` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `skills_people_id_index` (`people_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `success_people`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `success_people` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL,
  `pipeline_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `people_id` bigint(20) unsigned NOT NULL,
  `headline` text NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `other_name` varchar(255) DEFAULT NULL,
  `gender` varchar(255) DEFAULT NULL,
  `diverse` varchar(255) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `linkedinURL` varchar(255) DEFAULT NULL,
  `latest_role` varchar(255) NOT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `tenure` int(11) DEFAULT NULL,
  `function` varchar(255) DEFAULT NULL,
  `division` varchar(255) DEFAULT NULL,
  `seniority` varchar(255) DEFAULT NULL,
  `exco` varchar(255) DEFAULT NULL,
  `career_history` text DEFAULT NULL,
  `educational_history` text DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `languages` text DEFAULT NULL,
  `other_tags` text DEFAULT NULL,
  `skills_match` double(8,2) DEFAULT NULL,
  `education_match` double(8,2) DEFAULT NULL,
  `location_match` double(8,2) DEFAULT NULL,
  `role_match` double(8,2) DEFAULT NULL,
  `gender_match` double(8,2) DEFAULT NULL,
  `tenure_match` double(8,2) DEFAULT NULL,
  `total_score` double(8,2) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `mover` varchar(255) DEFAULT NULL,
  `U_skills_match` double(8,2) NOT NULL DEFAULT 0.00,
  `U_education_match` double(8,2) NOT NULL DEFAULT 0.00,
  `U_location_match` double(8,2) NOT NULL DEFAULT 0.00,
  `U_role_match` double(8,2) NOT NULL DEFAULT 0.00,
  `U_gender_match` double(8,2) NOT NULL DEFAULT 0.00,
  `U_tenure_match` double(8,2) NOT NULL DEFAULT 0.00,
  `U_total_score` double(8,2) NOT NULL DEFAULT 0.00,
  `type` text NOT NULL,
  `notes` text NOT NULL,
  `recruit` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `summary` text DEFAULT NULL,
  `readiness` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `success_requirements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `success_requirements` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `plan_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `success_requirements_plan_id_foreign` (`plan_id`),
  CONSTRAINT `success_requirements_plan_id_foreign` FOREIGN KEY (`plan_id`) REFERENCES `succession_plans` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `success_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `success_skills` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `succession_plan_id` bigint(20) unsigned NOT NULL,
  `success_people_id` bigint(20) unsigned NOT NULL,
  `success_requirements_id` bigint(20) unsigned NOT NULL,
  `skill_name` varchar(255) NOT NULL,
  `score` double(8,2) NOT NULL DEFAULT 0.00,
  `user_score` double(8,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `succession_plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `succession_plans` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `minimum_Experience` int(11) DEFAULT NULL,
  `tagged_individual` int(11) DEFAULT NULL,
  `ethnicity` tinyint(1) NOT NULL DEFAULT 0,
  `step_up` tinyint(1) NOT NULL DEFAULT 0,
  `description` text NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `status` varchar(255) NOT NULL,
  `mover` varchar(255) DEFAULT NULL,
  `candidate_status` varchar(255) DEFAULT NULL,
  `age` double(8,2) DEFAULT NULL,
  `last_opened` datetime DEFAULT NULL,
  `shared_with` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `succession_plans_user_id_foreign` (`user_id`),
  CONSTRAINT `succession_plans_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tasks` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `message` text NOT NULL,
  `type` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `team_members`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `team_members` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `team_roles` varchar(255) NOT NULL,
  `team_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `teams`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `teams` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `team_name` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries` (
  `sequence` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `batch_id` char(36) NOT NULL,
  `family_hash` varchar(255) DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT 1,
  `type` varchar(20) NOT NULL,
  `content` longtext NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sequence`),
  UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  KEY `telescope_entries_batch_id_index` (`batch_id`),
  KEY `telescope_entries_family_hash_index` (`family_hash`),
  KEY `telescope_entries_created_at_index` (`created_at`),
  KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) NOT NULL,
  `tag` varchar(255) NOT NULL,
  PRIMARY KEY (`entry_uuid`,`tag`),
  KEY `telescope_entries_tags_tag_index` (`tag`),
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_monitoring`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) NOT NULL,
  PRIMARY KEY (`tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `temporary_organisation_childs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `temporary_organisation_childs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `unique_id` varchar(255) DEFAULT NULL,
  `internal_people_id` bigint(20) unsigned NOT NULL,
  `parent_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `parent_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`parent_ids`)),
  PRIMARY KEY (`id`),
  KEY `temporary_organisation_childs_parent_id_foreign` (`parent_id`),
  KEY `temporary_organisation_childs_internal_people_id_foreign` (`internal_people_id`),
  CONSTRAINT `temporary_organisation_childs_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `temporary_organisation_childs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_notes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_notes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `entity_id` bigint(20) unsigned NOT NULL,
  `entity_type` varchar(255) NOT NULL,
  `Notes` text NOT NULL,
  `author` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_notes_author_foreign` (`author`),
  KEY `user_notes_entity_index` (`entity_id`,`entity_type`),
  CONSTRAINT `user_notes_author_foreign` FOREIGN KEY (`author`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `team` varchar(255) NOT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `account_id` bigint(20) unsigned NOT NULL,
  `role` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `two_factor_secret` text DEFAULT NULL,
  `two_factor_recovery_codes` text DEFAULT NULL,
  `two_factor_confirmed_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `profile_pic` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_activity` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (166,'2010_07_21_014238_create_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (167,'2010_07_21_014304_create_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (168,'2011_10_13_163556_create_accounts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (169,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (170,'2014_10_12_100000_create_password_reset_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (171,'2014_10_12_200000_add_two_factor_columns_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (172,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (173,'2019_12_14_000001_create_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (174,'2023_07_10_115102_create_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (175,'2023_07_21_014106_create_tasks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (176,'2023_07_21_014147_create_succession_plans_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (177,'2023_07_21_014320_create_people_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (178,'2023_07_21_014344_create_career_histories_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (179,'2023_07_21_014441_create_skills_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (180,'2023_08_01_101133_create_chat_histories_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (181,'2023_08_10_161135_create_success_requirements_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (182,'2023_08_15_114642_create_pipelines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (183,'2023_08_16_091014_create_success_people_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (184,'2023_08_29_180022_create_internal_people_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (185,'2023_08_29_180036_create_internal_skills_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (186,'2023_09_01_113233_create_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (187,'2023_09_04_091343_create_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (188,'2023_09_13_190345_create_team_members_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (189,'2023_10_17_095936_create_plans_summaries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (190,'2023_10_18_191119_create_plan_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (191,'2023_11_01_111832_create_plan_scores_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (192,'2023_11_14_120046_create_success_skills_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (193,'2023_11_14_120131_create_search_histories_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (194,'2023_12_07_142202_create_internal_career_histories_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (195,'2023_12_18_171135_create_user_notes_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (196,'2024_01_10_115213_create_job_people_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (197,'2024_01_12_065135_create_job_requirements_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (198,'2024_01_15_123312_create_job_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (199,'2024_04_30_122611_add_summary_column',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (200,'2024_05_24_122117_create_roles_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (201,'2024_05_28_090925_alter_internal_peoples_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (202,'2024_05_29_071439_alter_internal_peoples_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (203,'2024_05_31_122717_create_organisation_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (204,'2024_05_31_123202_create_organisation_peoples_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (205,'2024_06_11_051448_create_organisation_chils_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (206,'2024_06_12_051152_add_column_to_organisation_peoples_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (207,'2024_06_14_073731_alter_organisation_peoples_tables',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (208,'2024_06_14_075406_create_temporary_organisation_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (209,'2024_06_14_142537_alter_temporary_childs_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (210,'2024_06_21_134401_alter_summary_column',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (211,'2024_06_28_053108_alter_internal_peoples_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (212,'2024_07_05_012213_alter-success-and-pipeline',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (213,'2024_07_05_012428_alter-success-and-pipeline',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (214,'2024_07_05_132622_alter-job-people',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (215,'2024_07_08_131658_alter_job_people_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (216,'2024_07_08_133106_alter_job_people_table_add_city_counrty',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (217,'2024_07_10_155122_alter-internal_people',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (218,'2024_07_11_093213_alter_roles_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (219,'2024_07_17_093705_create_invites_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (220,'2024_07_19_125120_alter_user_table_add_l_a',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (221,'2024_07_25_110034_change_column_type_in_accounts_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (222,'2024_07_26_092837_add_columns_to_accounts_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (223,'2024_07_29_065809_alter_accounts_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (224,'2024_07_29_073915_alter_user_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (225,'2024_07_29_100352_add_column_to_accounts_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (226,'2024_07_30_142342_edit_company_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (227,'2024_07_30_143208_edit_company_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (228,'2024_07_31_113823_alter_internal_career_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (229,'2024_08_02_104845_drop_foreign_key_relation',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (230,'2024_08_05_081700_drop_relation_from_organisation_peoples',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (231,'2024_08_06_102102_remove_unique_constraint_from_users_email',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (232,'2024_08_07_053353_alter_invite_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (233,'2024_08_22_045748_alter_pipeline_table_add_status_col',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (234,'2024_08_22_050652_alter_succession_plans_table_add_mover_col',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (235,'2024_08_22_074302_alter_jobs_table_add_mover_col',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (236,'2024_08_27_111412_alter_pipeline_table_add_mover_field',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (237,'2024_08_27_111452_alter_succesion_people_table_add_mover_field',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (238,'2024_09_04_075139_add_status_column_in_job_people',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (239,'2024_09_09_045852_create_assessment_criterias_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (240,'2024_09_11_082439_create_internal_requirement_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (241,'2024_09_19_134321_update_record_in_user_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (242,'2024_09_23_072916_alter_people_table_add_notes_field',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (243,'2024_09_25_092844_alter_internal_peoples_table_add_tenure_in_company',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (244,'2024_11_08_091435_create_job_queues_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (245,'2024_11_11_082912_add_index_to_name_column_in_companies_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (246,'2024_11_11_082950_add_indexes_to_people_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (247,'2024_11_13_052208_add_index_to_companies_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (248,'2024_11_13_052455_add_index_to_internal_peoples_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (249,'2024_11_13_092247_add_indexes_to_internal_people_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (250,'2024_11_26_042753_add_force_index_to_people_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (251,'2024_11_26_044541_add_composite_index_to_people_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (252,'2024_11_26_050034_add_composite_index_for_function_to_people_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (253,'2024_11_26_050330_add_composite_index_for_exco_to_people_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (254,'2024_11_26_050524_add_composite_index_for_gender_to_people_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (255,'2024_11_26_051408_add_indexes_to_pipelines_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (256,'2024_11_27_124901_add_indexes_to_pipelines_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (257,'2024_11_27_125537_add_indexes_to_pipelines_table_add_index',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (258,'2018_08_08_100000_create_telescope_entries_table',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (259,'2024_11_19_084906_add_country_code_column_in_locations_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (260,'2024_12_05_051119_add_composite_indexes_to_pipelines_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (261,'2024_12_06_050659_add_composite_indexing_in_short_list',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (262,'2024_12_11_130912_create_interviews_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (263,'2024_12_12_060216_add_user_id_to_interviews_table',6);
