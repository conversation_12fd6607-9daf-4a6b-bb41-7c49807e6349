<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add composite index
        Schema::table('people', function (Blueprint $table) {
            // Add an index explicitly for optimization
            $table->index(['company_name', 'company_id', 'status'], 'idx_optimized');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove the composite index
        Schema::table('people', function (Blueprint $table) {
            $table->dropIndex('idx_optimized');
        });
    }
};
