<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('success_people', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('plan_id');
            $table->unsignedBigInteger('pipeline_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('people_id');
            $table->text('headline');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('middle_name')->nullable();
            $table->string('other_name')->nullable();
            $table->string('gender')->nullable();
            $table->string('diverse')->nullable();
            $table->string('location')->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->string('linkedinURL')->nullable();

            $table->string('latest_role');
            $table->unsignedbigInteger('company_id');
            $table->string('company_name');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->integer('tenure')->nullable();
            $table->string('function')->nullable();
            $table->string('division')->nullable();
            $table->string('seniority')->nullable();
            $table->string('exco')->nullable();
            $table->text('career_history')->nullable();

            $table->text('educational_history')->nullable();
            $table->text('skills')->nullable();
            $table->text('languages')->nullable();
            $table->text('other_tags')->nullable();

            $table->float('skills_match')->nullable();
            $table->float('education_match')->nullable();
            $table->float('location_match')->nullable();
            $table->float('role_match')->nullable();
            $table->float('gender_match')->nullable();
            $table->float('tenure_match')->nullable();
            $table->float('total_score')->nullable();
            $table->string('status')->nullable();

            $table->float('U_skills_match')->default(0);
            $table->float('U_education_match')->default(0);
            $table->float('U_location_match')->default(0);
            $table->float('U_role_match')->default(0);
            $table->float('U_gender_match')->default(0);
            $table->float('U_tenure_match')->default(0);
            $table->float('U_total_score')->default(0);

            $table->text('type');
            $table->text('notes');
            $table->boolean('recruit');

            $table->timestamps();
        
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('success_people');
    }
};
