<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
            $table->unsignedBigInteger('stageID')->nullable()->after('interviewer_id'); // Adjust position with 'after'
        });;
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
            $table->dropColumn('stageID');
        });
    }
};
