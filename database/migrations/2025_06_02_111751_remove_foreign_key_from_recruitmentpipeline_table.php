<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
               $table->dropForeign(['Candidate_ID']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
         Schema::table('RecruitmentPipeline', function (Blueprint $table) {
            // Add the foreign key constraint back (if needed)
            $table->foreign('Candidate_ID')
                  ->references('id')
                  ->on('people')
                  ->onDelete('cascade');
        });
    }
};
