<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('succession_plans', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->string('name');
            $table->integer('minimum_Experience')->nullable();
            $table->integer('tagged_individual')->nullable();
            $table->boolean('ethnicity')->default(false);
            $table->boolean('step_up')->default(false);
            $table->text('description');
            $table->unsignedBigInteger('user_id');
            $table->string('status');
            $table->string('candidate_status')->nullable();
            $table->float('age')->nullable();
            $table->dateTime('last_opened')->nullable();
            $table->string('shared_with')->nullable();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('succession_plans');
    }
};
