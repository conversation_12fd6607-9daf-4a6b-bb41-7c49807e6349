<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            if (!Schema::hasColumn('internal_people', 'country')) {
                $table->string('country')->nullable()->after('location');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->dropColumn('country');
        });
    }
};
