<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('internal_people', function (Blueprint $table) {
            $table->id();
            $table->string('employee_id')->nullable();
            $table->string('forename')->nullable();
            $table->string('surname')->nullable();
            $table->string('middle_name')->nullable();
            $table->string('other_name')->nullable();
            $table->string('gender')->nullable();
            $table->string('diverse')->nullable();
            $table->string('location');
            $table->string('linkedinURL')->nullable();

            $table->string('role');
            $table->unsignedbigInteger('reports_to')->nullable();
            $table->string('exco')->nullable();
            $table->unsignedbigInteger('company_id');
            $table->string('company_name')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->integer('tenure');
            $table->string('function')->nullable();
            $table->string('division')->nullable();
            $table->string('seniority')->nullable();
            $table->text('career_history')->nullable();

            $table->text('educational_history')->nullable();
            $table->text('skills')->nullable();
            $table->text('languages')->nullable();
            $table->text('other_tags')->nullable();
            $table->string('readiness')->nullable();

            $table->foreignIdFor(User::class);
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('internal_people');
    }
};
