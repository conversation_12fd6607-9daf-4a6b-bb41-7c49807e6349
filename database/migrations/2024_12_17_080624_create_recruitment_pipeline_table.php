<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('RecruitmentPipeline', function (Blueprint $table) {
            $table->id(); // Primary Key
            $table->unsignedBigInteger('recruitment_project_id');
            $table->unsignedBigInteger('Plan_id');
            $table->unsignedBigInteger('Job_id');
            $table->unsignedBigInteger('Candidate_ID');
            $table->string('Phone_number')->nullable();
            $table->string('Email')->nullable();
            $table->string('Address')->nullable();
            $table->string('Link_cv')->nullable();
            $table->string('Status')->default('pending'); // default status
            $table->string('Consent_document')->nullable();
            $table->timestamps();
  
            // Foreign key constraints
            $table->foreign('recruitment_project_id')->references('id')->on('recruitments')->onDelete('cascade');
            $table->foreign('Plan_id')->references('id')->on('succession_plans')->onDelete('cascade');
            $table->foreign('Job_id')->references('id')->on('jobs')->onDelete('cascade');
            $table->foreign('Candidate_ID')->references('id')->on('people')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('RecruitmentPipeline');
    }
};
