<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('internal_competencies', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('organisation_people_id');
            $table->unsignedBigInteger('competency_id');
            $table->string('score')->nullable();
            $table->timestamps();

            $table->foreign('organisation_people_id')
                  ->references('id')->on('organisation_peoples')
                  ->onDelete('cascade');

            $table->foreign('competency_id')
                  ->references('id')->on('competencies')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('internal_competencies');
    }
};

