<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\SuccessRequirements;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('success_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(SuccessionPlan::class);
            $table->foreignIdFor(SuccessPeople::class);
            $table->foreignIdFor(SuccessRequirements::class);
            $table->string('skill_name');
            $table->float('score')->default(0);
            $table->float('user_score')->default(0);
            $table->timestamps();
            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('success_skills');
    }
};
