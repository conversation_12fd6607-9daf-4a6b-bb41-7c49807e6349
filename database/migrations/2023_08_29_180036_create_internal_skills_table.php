<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('internal_skills', function (Blueprint $table) {
            $table->id();
            $table->unsignedbigInteger('internal_people');
            $table->unsignedbigInteger('company_id');
            $table->string('skill_name');
            $table->string('skill_type');
            $table->float('skill_rating')->nullable();
            $table->timestamps();

            $table->foreign('internal_people')->references('id')->on('internal_people')->onDelete('cascade'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('internal_skills');
    }
};
