<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans_summaries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('plan_id');
            $table->float('gen_diversity')->default(0);
            $table->float('int_ext_diversity')->default(0);
            $table->float('average_tenurescore')->default(0);
            $table->float('average_tenure')->default(0);
            $table->float('readiness_score')->default(0);
            $table->float('skills_score')->default(0);
            $table->float('plan_age')->default(0);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans_summaries');
    }
};
