<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pipelines', function (Blueprint $table) {
            // Composite indexes for performance optimization
            $table->index(['job_id', 'people_id'], 'idx_pipelines_job_people');
            $table->index(['plan_id', 'people_id'], 'idx_pipelines_plan_people');
            $table->index(['job_id', 'gender'], 'idx_pipelines_job_gender');
            $table->index(['job_id', 'company_name'], 'idx_pipelines_job_company');
            $table->index(['job_id', 'function'], 'idx_pipelines_job_function');
            $table->index(['job_id', 'country'], 'idx_pipelines_job_country');
            $table->index(['plan_id', 'total_score'], 'idx_pipelines_plan_score');
            $table->index(['plan_id', 'skills_match'], 'idx_pipelines_plan_skills');
            
            // Individual indexes for frequently queried columns
            if (!Schema::hasIndex('pipelines', 'idx_pipelines_people_id')) {
                $table->index('people_id', 'idx_pipelines_people_id');
            }
            if (!Schema::hasIndex('pipelines', 'idx_pipelines_job_id')) {
                $table->index('job_id', 'idx_pipelines_job_id');
            }
            if (!Schema::hasIndex('pipelines', 'idx_pipelines_plan_id')) {
                $table->index('plan_id', 'idx_pipelines_plan_id');
            }
        });

        Schema::table('success_people', function (Blueprint $table) {
            // Indexes for success_people table
            $table->index(['plan_id', 'people_id'], 'idx_success_people_plan_people');
            $table->index(['plan_id', 'total_score'], 'idx_success_people_plan_score');
            
            if (!Schema::hasIndex('success_people', 'idx_success_people_people_id')) {
                $table->index('people_id', 'idx_success_people_people_id');
            }
        });

        Schema::table('job_people', function (Blueprint $table) {
            // Indexes for job_people table
            $table->index(['job_id', 'people_id'], 'idx_job_people_job_people');
            
            if (!Schema::hasIndex('job_people', 'idx_job_people_job_id')) {
                $table->index('job_id', 'idx_job_people_job_id');
            }
            if (!Schema::hasIndex('job_people', 'idx_job_people_people_id')) {
                $table->index('people_id', 'idx_job_people_people_id');
            }
        });

        Schema::table('succession_plans', function (Blueprint $table) {
            // Indexes for succession_plans table
            if (!Schema::hasIndex('succession_plans', 'idx_succession_plans_user_id')) {
                $table->index('user_id', 'idx_succession_plans_user_id');
            }
        });

        Schema::table('jobs', function (Blueprint $table) {
            // Indexes for jobs table
            if (!Schema::hasIndex('jobs', 'idx_jobs_user_id')) {
                $table->index('user_id', 'idx_jobs_user_id');
            }
        });

        Schema::table('success_requirements', function (Blueprint $table) {
            // Indexes for success_requirements table
            if (!Schema::hasIndex('success_requirements', 'idx_success_requirements_plan_id')) {
                $table->index('plan_id', 'idx_success_requirements_plan_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->dropIndex('idx_pipelines_job_people');
            $table->dropIndex('idx_pipelines_plan_people');
            $table->dropIndex('idx_pipelines_job_gender');
            $table->dropIndex('idx_pipelines_job_company');
            $table->dropIndex('idx_pipelines_job_function');
            $table->dropIndex('idx_pipelines_job_country');
            $table->dropIndex('idx_pipelines_plan_score');
            $table->dropIndex('idx_pipelines_plan_skills');
            $table->dropIndex('idx_pipelines_people_id');
            $table->dropIndex('idx_pipelines_job_id');
            $table->dropIndex('idx_pipelines_plan_id');
        });

        Schema::table('success_people', function (Blueprint $table) {
            $table->dropIndex('idx_success_people_plan_people');
            $table->dropIndex('idx_success_people_plan_score');
            $table->dropIndex('idx_success_people_people_id');
        });

        Schema::table('job_people', function (Blueprint $table) {
            $table->dropIndex('idx_job_people_job_people');
            $table->dropIndex('idx_job_people_job_id');
            $table->dropIndex('idx_job_people_people_id');
        });

        Schema::table('succession_plans', function (Blueprint $table) {
            $table->dropIndex('idx_succession_plans_user_id');
        });

        Schema::table('jobs', function (Blueprint $table) {
            $table->dropIndex('idx_jobs_user_id');
        });

        Schema::table('success_requirements', function (Blueprint $table) {
            $table->dropIndex('idx_success_requirements_plan_id');
        });
    }
};
