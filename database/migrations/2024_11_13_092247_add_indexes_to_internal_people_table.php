<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->index('forename');
            $table->index('surname');
        });
    }

    public function down()
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->dropIndex(['forename']);
            $table->dropIndex(['surname']);
        });
    }
};
