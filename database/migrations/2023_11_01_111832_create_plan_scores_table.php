<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SuccessionPlan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_scores', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->foreignIdFor(SuccessionPlan::class);
            $table->string('metric_name');
            $table->float('score')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plan_scores');
    }
};
