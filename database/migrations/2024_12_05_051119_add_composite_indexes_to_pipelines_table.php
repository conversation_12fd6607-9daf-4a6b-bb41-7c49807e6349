<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('pipelines', function (Blueprint $table) {
            // Composite index for job_id and people_id
            $table->index(['job_id', 'people_id'], 'job_people_index');

            // Composite index for company_name and company_id
            $table->index(['company_name', 'company_id'], 'company_index');

            // Composite index for function and division
            $table->index(['function', 'division'], 'function_division_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->dropIndex('job_people_index');
            $table->dropIndex('company_index');
            $table->dropIndex('function_division_index');
        });
    }
};
