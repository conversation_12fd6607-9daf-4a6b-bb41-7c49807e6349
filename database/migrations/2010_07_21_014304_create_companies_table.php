<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Location;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('other_name')->nullable();
            $table->string('name_abbreviation')->nullable();
            $table->string('parent_name')->nullable();
            $table->text('description')->nullable();

            $table->text('corporate_hq_country')->nullable();
            $table->string('corporate_hq_address')->nullable();

            $table->string('website')->nullable();
            $table->string('corporate_hq_phone_number')->nullable();
            $table->string('chair')->nullable();
            $table->string('ceo')->nullable();

            $table->string('type')->nullable();
            $table->string('industry')->nullable();
            $table->string('sector')->nullable();
            $table->string('stock_symbol')->nullable();

            $table->bigInteger('Annual_Revenue')->nullable();
            $table->float('Annual_Net_Profit_Margin')->nullable();
            $table->float('Annual_Net_Expenses')->nullable();
            $table->float('Annual_YOY_Revenue_Change')->nullable();
            $table->bigInteger('company_employee_count')->nullable();
            $table->string('image')->nullable();
            $table->string('status')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
