<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_people', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('job_id');
            $table->unsignedBigInteger('pipeline_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('people_id');
            $table->text('headline');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('middle_name')->nullable();
            $table->string('other_name')->nullable();
            $table->string('gender')->nullable();
            $table->string('diverse')->nullable();
            $table->string('location')->nullable();
            $table->string('linkedinURL')->nullable();

            $table->string('latest_role')->nullable();
            $table->unsignedbigInteger('company_id')->nullable();
            $table->string('company_name')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->integer('tenure')->nullable();
            $table->string('function')->nullable();
            $table->string('division')->nullable();
            $table->string('seniority')->nullable();
            $table->text('career_history')->nullable();

            $table->text('educational_history')->nullable();
            $table->text('skills')->nullable();
            $table->text('languages')->nullable();
            $table->text('other_tags')->nullable();
            $table->text('notes');

            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade'); 
            $table->foreign('job_id')->references('id')->on('jobs')->onDelete('cascade'); 
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            $table->foreign('pipeline_id')->references('id')->on('pipelines')->onDelete('cascade'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_people');
    }
};
