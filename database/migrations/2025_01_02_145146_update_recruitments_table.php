<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
         Schema::table('recruitments', function (Blueprint $table) {
            $table->dropColumn('shared_with');
            $table->text('viewer_shared_with')->nullable();
            $table->text('editor_shared_with')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
         Schema::table('recruitments', function (Blueprint $table) {
            $table->text('shared_with')->nullable();
            $table->dropColumn('viewer_shared_with');
            $table->dropColumn('editor_shared_with');
        });
    }
};
