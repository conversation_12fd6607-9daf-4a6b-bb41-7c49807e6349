<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('temporary_organisation_chart');
        Schema::create('temporary_organisation_chart', function (Blueprint $table) {
            $table->id();
            $table->string("unique_id")->nullable();
            $table->foreignId('parent_id')->constrained('internal_people')->onDelete('cascade');
            $table->foreignId('child_id')->constrained('internal_people')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('temporary_organisation_chart');
    }
};
