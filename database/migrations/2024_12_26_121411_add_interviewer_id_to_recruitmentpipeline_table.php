<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
            $table->unsignedBigInteger('interviewer_id')->nullable()->after('Candidate_ID'); 
            $table->foreign('interviewer_id')->references('id')->on('users')->onDelete('cascade'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
            $table->dropForeign(['interviewer_id']);
            $table->dropColumn('interviewer_id');
        });
    }
};
