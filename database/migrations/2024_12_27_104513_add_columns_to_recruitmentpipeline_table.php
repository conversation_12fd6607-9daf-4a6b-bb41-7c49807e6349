<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
            $table->string('relocation')->nullable(); 
            $table->decimal('expected_salary', 15, 2)->nullable(); 
            $table->string('country_manager')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
              $table->dropColumn([
                'relocation',
                'expected_salary',
                'country_manager',
            ]);
        });
    }
};
