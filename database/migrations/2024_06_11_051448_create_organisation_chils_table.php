<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('temporary_organisation_childs');
        Schema::create('temporary_organisation_childs', function (Blueprint $table) {
            $table->id();
            $table->string("unique_id")->nullable();
            $table->foreignId('internal_people_id');
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id')->references('id')->on('temporary_organisation_childs')->onDelete('cascade');
            $table->foreign('internal_people_id')->references('id')->on('internal_people');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('temporary_organisation_childs');
    }
};
