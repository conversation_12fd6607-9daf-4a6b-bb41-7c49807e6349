<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->index('function');  // Index for 'function' column
            $table->index('division'); // Index for 'division' column
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->dropIndex(['function']); // Drop index for 'function'
            $table->dropIndex(['division']); // Drop index for 'division'
        });
    }
};
