<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pipelines', function (Blueprint $table) {
            // Add indexes for commonly queried columns
            $table->index(['plan_id', 'people_id'], 'pipelines_plan_people_idx');
            $table->index(['plan_id', 'total_score'], 'pipelines_plan_score_idx');
            $table->index(['plan_id', 'skills_match'], 'pipelines_plan_skills_idx');
            $table->index(['people_id'], 'pipelines_people_idx');
        });

        Schema::table('success_people', function (Blueprint $table) {
            $table->index(['plan_id', 'people_id'], 'success_people_plan_people_idx');
        });

        Schema::table('job_people', function (Blueprint $table) {
            $table->index(['people_id', 'job_id'], 'job_people_people_job_idx');
        });

        Schema::table('user_notes', function (Blueprint $table) {
            $table->index(['entity_id', 'entity_type'], 'user_notes_entity_idx');
        });

        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasIndex('users', 'users_deleted_at_index')) {
                $table->index(['deleted_at'], 'users_deleted_at_idx');
            }
        });

        Schema::table('job_queues_notification', function (Blueprint $table) {
            $table->index(['user_id'], 'job_queues_notification_user_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->dropIndex('pipelines_plan_people_idx');
            $table->dropIndex('pipelines_plan_score_idx');
            $table->dropIndex('pipelines_plan_skills_idx');
            $table->dropIndex('pipelines_people_idx');
        });

        Schema::table('success_people', function (Blueprint $table) {
            $table->dropIndex('success_people_plan_people_idx');
        });

        Schema::table('job_people', function (Blueprint $table) {
            $table->dropIndex('job_people_people_job_idx');
        });

        Schema::table('user_notes', function (Blueprint $table) {
            $table->dropIndex('user_notes_entity_idx');
        });

        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasIndex('users', 'users_deleted_at_idx')) {
                $table->dropIndex('users_deleted_at_idx');
            }
        });

        Schema::table('job_queues_notification', function (Blueprint $table) {
            $table->dropIndex('job_queues_notification_user_idx');
        });
    }
};
