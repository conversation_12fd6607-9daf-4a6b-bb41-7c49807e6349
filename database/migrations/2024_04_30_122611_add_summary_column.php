<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->string('summary', 2100)->nullable();
        });
        Schema::table('pipelines', function (Blueprint $table) {
            $table->string('summary', 2100)->nullable();
        });
        Schema::table('people', function (Blueprint $table) {
            $table->string('summary', 2100)->nullable();
        });
        Schema::table('success_people', function (Blueprint $table) {
            $table->string('summary', 2100)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->dropColumn('summary');
        });
        Schema::table('pipelines', function (Blueprint $table) {
            $table->dropColumn('summary');
        });
        Schema::table('people', function (Blueprint $table) {
            $table->dropColumn('summary');
        });
        Schema::table('success_people', function (Blueprint $table) {
            $table->dropColumn('summary');
        });
    }
};
