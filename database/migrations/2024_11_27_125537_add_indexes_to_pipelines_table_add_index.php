<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('pipelines', function (Blueprint $table) {
            // Adding a composite index for plan_id, people_id, and company_name
            $table->index(['plan_id', 'people_id', 'company_name'], 'pipelines_plan_people_company_index');
        });
    }

    public function down()
    {
        Schema::table('pipelines', function (Blueprint $table) {
            // Dropping the composite index if it exists
            $table->dropIndex('pipelines_plan_people_company_index');
        });
    }
};
