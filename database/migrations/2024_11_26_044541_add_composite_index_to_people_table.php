<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('people', function (Blueprint $table) {
            // Add composite index for country, status, and company_id
            $table->index(['country', 'status', 'company_id'], 'idx_country_status_company');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('people', function (Blueprint $table) {
            // Drop the composite index
            $table->dropIndex('idx_country_status_company');
        });
    }
};
