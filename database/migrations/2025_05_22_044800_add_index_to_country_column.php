<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            if (Schema::hasColumn('internal_people', 'country') && !Schema::hasIndex('internal_people', 'internal_people_country_index')) {
                $table->index('country', 'internal_people_country_index');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->dropIndex('internal_people_country_index');
        });
    }
};