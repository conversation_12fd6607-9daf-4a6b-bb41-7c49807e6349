<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->unsignedbigInteger('people_id')->nullable();
            $table->unsignedbigInteger('plan_id')->nullable();
            $table->string('entity_name');
            $table->text('entity_company')->nullable();
            $table->text('description')->nullable();
            $table->unsignedbigInteger('user_id');
            $table->unsignedbigInteger('user_company');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
