<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->boolean('flight_risk')->nullable()->default(null)->after('user_id'); // Adding 'Flight Risk'
            $table->string('potential')->nullable()->after('flight_risk'); // Adding 'potential'
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->dropColumn('flight_risk');
            $table->dropColumn('potential');
        });
    }
};
