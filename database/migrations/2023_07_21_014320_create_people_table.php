<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('people', function (Blueprint $table) {
            $table->id();
            $table->string('forename')->nullable();
            $table->string('surname')->nullable();
            $table->string('middle_name')->nullable();
            $table->string('other_name')->nullable();
            $table->string('gender')->nullable();
            $table->string('diverse')->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->string('linkedinURL')->nullable();

            $table->string('parent_role')->nullable();
            $table->string('latest_role');
            $table->string('exco')->nullable();
            $table->unsignedbigInteger('company_id');
            $table->string('company_name')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->integer('tenure');
            $table->string('function')->nullable();
            $table->string('division')->nullable();
            $table->string('seniority')->nullable();
            $table->text('career_history')->nullable();

            $table->text('educational_history')->nullable();
            $table->text('skills')->nullable();
            $table->text('languages')->nullable();
            $table->text('other_tags')->nullable();
            $table->string('status');
            $table->string('readiness')->nullable();
            $table->foreignIdFor(User::class);

            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('people');
    }
};
