<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            // Drop the 'roles' column
            $table->dropColumn('role');
            $table->integer('role_id')->after('gender');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            // Drop the 'role_id' column
            $table->dropColumn('role_id');

            // Re-add the 'roles' column
            $table->string('role')->after('gender');
        });
    }
};
