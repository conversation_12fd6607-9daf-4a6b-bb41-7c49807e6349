<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->text('summary')->nullable()->change();
        });
        Schema::table('pipelines', function (Blueprint $table) {
            $table->text('summary')->nullable()->change();;
        });
        Schema::table('people', function (Blueprint $table) {
            $table->text('summary')->nullable()->change();;
        });
        Schema::table('success_people', function (Blueprint $table) {
            $table->text('summary')->nullable()->change();;
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
