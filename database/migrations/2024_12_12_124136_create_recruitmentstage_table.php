<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recruitment_stages', function (Blueprint $table) {
			$table->id();  // Primary key
			$table->string('stage_name');
			$table->integer('stage_number');
			$table->integer('recruitment_project_id');
			$table->foreignId('user_id')->constrained()->onDelete('cascade'); // Foreign key
			$table->timestamps(); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recruitment_stages');
    }
};
