<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->string('exco')->default('Not Applicable')->change();
            $table->string('location')->default('Not Applicable')->change();
            $table->string('function')->default('Not Applicable')->change();
            $table->string('division')->default('Not Applicable')->change();
            $table->string('gender')->default('Not Applicable')->change();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
