<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->string('location')->default('Not Provided')->change();
            $table->integer('tenure')->default(0)->change();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('internal_people', function (Blueprint $table) {
            $table->string('location')->default(null)->change(); // Remove default
            $table->integer('tenure')->default(null)->change(); // Remove default
        });
    }
};
