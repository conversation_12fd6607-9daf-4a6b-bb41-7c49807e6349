<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('skills', function (Blueprint $table) {
            $table->index(['people_id'], 'skills_people_id_index');
        });

        Schema::table('career_histories', function (Blueprint $table) {
            $table->index(['people_id', 'past_company_id'], 'career_histories_people_company_index');
        });

        Schema::table('user_notes', function (Blueprint $table) {
            $table->index(['entity_id', 'entity_type'], 'user_notes_entity_index');
        });
    }

    public function down()
    {
        Schema::table('skills', function (Blueprint $table) {
            $table->dropIndex('skills_people_id_index');
        });

        Schema::table('career_histories', function (Blueprint $table) {
            $table->dropIndex('career_histories_people_company_index');
        });

        Schema::table('user_notes', function (Blueprint $table) {
            $table->dropIndex('user_notes_entity_index');
        });
    }
};
