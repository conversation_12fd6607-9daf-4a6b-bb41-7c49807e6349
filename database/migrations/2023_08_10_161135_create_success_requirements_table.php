<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('success_requirements', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->unsignedbigInteger('plan_id');
            $table->string('name');
            $table->string('type');

            $table->foreign('plan_id')->references('id')->on('succession_plans')->onDelete('cascade'); 

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('success_requirements');
    }
};
