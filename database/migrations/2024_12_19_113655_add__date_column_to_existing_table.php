<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::table('RecruitmentPipeline', function (Blueprint $table) {
			$table->string('interview_date')->nullable(); // Add your new column here
		});

    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
            $table->dropColumn('interview_date'); // Remove the date column on rollback
        });
    }
};
