<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddIndexesForPerformance extends Migration
{
    public function up(): void
    {
        $this->addIndexes('internal_people', function (Blueprint $table) {
            $this->addIndexIfNotExists($table, 'internal_people', ['forename', 'surname'], 'internal_people_forename_surname_index');
            $this->addIndexIfNotExists($table, 'internal_people', 'company_id', 'internal_people_company_id_index');
            $this->addIndexIfNotExists($table, 'internal_people', 'latest_role', 'internal_people_latest_role_index');
            $this->addIndexIfNotExists($table, 'internal_people', 'location', 'internal_people_location_index');
            $this->addIndexIfNotExists($table, 'internal_people', 'readiness', 'internal_people_readiness_index');
            $this->addIndexIfNotExists($table, 'internal_people', 'potential', 'internal_people_potential_index');
            $this->addIndexIfNotExists($table, 'internal_people', 'performance', 'internal_people_performance_index');
            $this->addIndexIfNotExists($table, 'internal_people', 'updated_at', 'internal_people_updated_at_index');
            $this->addIndexIfNotExists($table, 'internal_people', 'employee_id', 'internal_people_employee_id_index');
        });

        $this->addIndexes('organisation_peoples', function (Blueprint $table) {
            $this->addIndexIfNotExists($table, 'organisation_peoples', ['organisation_id', 'parent_id'], 'organisation_people_orgid_parentid_index');
            $this->addIndexIfNotExists($table, 'organisation_peoples', 'internal_people_id', 'organisation_people_internal_people_id_index');
        });

        $this->addIndexes('success_people', function (Blueprint $table) {
            // special case for composite index with TEXT column 'type'
            if (! $this->indexExistsRaw('success_people', 'success_people_people_id_type_index')) {
                DB::statement("ALTER TABLE `success_people` ADD INDEX `success_people_people_id_type_index` (`people_id`, `type`(191))");
            }
            $this->addIndexIfNotExists($table, 'success_people', 'plan_id', 'success_people_plan_id_index');
        });

        $this->addIndexes('internal_competencies', function (Blueprint $table) {
            $this->addIndexIfNotExists($table, 'internal_competencies', 'organisation_people_id', 'internal_competencies_organisation_people_id_index');
            $this->addIndexIfNotExists($table, 'internal_competencies', 'competency_id', 'internal_competencies_competency_id_index');
        });

        $this->addIndexes('internal_requirements', function (Blueprint $table) {
            $this->addIndexIfNotExists($table, 'internal_requirements', 'organisation_people_id', 'internal_requirements_organisation_people_id_index');
        });

        $this->addIndexes('succession_plans', function (Blueprint $table) {
            $this->addIndexIfNotExists($table, 'succession_plans', 'user_id', 'succession_plans_user_id_index');
        });
    }

    public function down(): void
    {
        $this->dropIndexes('internal_people', [
            'internal_people_forename_surname_index',
            'internal_people_company_id_index',
            'internal_people_latest_role_index',
            'internal_people_location_index',
            'internal_people_readiness_index',
            'internal_people_potential_index',
            'internal_people_performance_index',
            'internal_people_updated_at_index',
            'internal_people_employee_id_index',
        ]);

        $this->dropIndexes('organisation_peoples', [
            'organisation_people_orgid_parentid_index',
            'organisation_people_internal_people_id_index',
        ]);

        $this->dropIndexes('success_people', [
            'success_people_people_id_type_index',
            'success_people_plan_id_index',
        ]);

        $this->dropIndexes('internal_competencies', [
            'internal_competencies_organisation_people_id_index',
            'internal_competencies_competency_id_index',
        ]);

        $this->dropIndexes('internal_requirements', [
            'internal_requirements_organisation_people_id_index',
        ]);

        $this->dropIndexes('succession_plans', [
            'succession_plans_user_id_index',
        ]);
    }

    protected function addIndexes(string $tableName, callable $callback): void
    {
        if (Schema::hasTable($tableName)) {
            Schema::table($tableName, $callback);
        }
    }

    protected function dropIndexes(string $tableName, array $indexNames): void
    {
        if (Schema::hasTable($tableName)) {
            Schema::table($tableName, function (Blueprint $table) use ($tableName, $indexNames) {
                foreach ($indexNames as $indexName) {
                    if ($this->indexExistsRaw($tableName, $indexName)) {
                        $table->dropIndex($indexName);
                    }
                }
            });
        }
    }

    protected function addIndexIfNotExists(Blueprint $table, string $tableName, $columns, string $indexName): void
    {
        if (! $this->indexExistsRaw($tableName, $indexName)) {
            $table->index($columns, $indexName);
        }
    }

    protected function indexExistsRaw(string $tableName, string $indexName): bool
    {
        return DB::table('information_schema.statistics')
            ->where('table_schema', DB::getDatabaseName())
            ->where('table_name', $tableName)
            ->where('index_name', $indexName)
            ->exists();
    }
}
