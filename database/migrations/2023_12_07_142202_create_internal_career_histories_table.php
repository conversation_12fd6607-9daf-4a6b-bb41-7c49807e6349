<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('internal_career_histories', function (Blueprint $table) {
            $table->id();
            $table->unsignedbigInteger('people_id');
            $table->string('role');
            $table->unsignedbigInteger('past_company_id');
            $table->date('start_date');
            $table->date('end_date');
            $table->float('tenure');
            $table->timestamps();

            $table->foreign('people_id')->references('id')->on('internal_people')->onDelete('cascade'); 
            $table->foreign('past_company_id')->references('id')->on('companies')->onDelete('cascade'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('internal_career_histories');
    }
};
