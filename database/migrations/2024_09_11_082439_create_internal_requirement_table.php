<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('internal_requirement', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('organisation_people_id');
            $table->unsignedBigInteger('assessment_criteria_id');
            $table->string('response')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();
            $table->foreign('organisation_people_id')->references('id')->on('organisation_peoples')->onDelete('cascade');
            $table->foreign('assessment_criteria_id')->references('id')->on('assessment_criterias')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('internal_requirement');
    }
};
