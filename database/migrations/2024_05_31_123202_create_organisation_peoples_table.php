<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('organisation_peoples');
        Schema::rename('organisation', 'organisations');
        Schema::create('organisation_peoples', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organisation_id');
            $table->foreignId('internal_people_id');
            $table->string('direct_report')->nullable();
            $table->integer('plan_id')->nullable();
            $table->softDeletes(); // Add this line for soft deletes
            $table->timestamps();
            $table->foreignId('created_by'); // Add this line for the foreign key
            $table->foreign('created_by')->references('id')->on('users');
            $table->foreign('organisation_id')->references('id')->on('organisations');
            $table->foreign('internal_people_id')->references('id')->on('internal_people');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organisation_peoples');
    }
};
