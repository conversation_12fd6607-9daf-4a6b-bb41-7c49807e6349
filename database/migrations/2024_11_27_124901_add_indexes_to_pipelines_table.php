<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pipelines', function (Blueprint $table) {
            // Check if the index does not exist before adding it
            if (!$this->indexExists('pipelines', 'pipelines_company_id_index')) {
                $table->index('company_id'); // Add index for `company_id`
            }

            if (!$this->indexExists('pipelines', 'pipelines_company_name_index')) {
                $table->index('company_name'); // Add index for `company_name`
            }

            if (!$this->indexExists('pipelines', 'pipelines_function_index')) {
                $table->index('function'); // Add index for `function`
            }

            if (!$this->indexExists('pipelines', 'pipelines_division_index')) {
                $table->index('division'); // Add index for `division`
            }
        });
    }

    private function indexExists(string $table, string $indexName): bool
    {
        $databaseName = DB::getDatabaseName();
        $result = DB::select("
            SELECT COUNT(*) AS count
            FROM information_schema.statistics
            WHERE table_schema = ? AND table_name = ? AND index_name = ?
        ", [$databaseName, $table, $indexName]);

        return $result[0]->count > 0;
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->dropIndex(['company_id']);    // Drop index for `company_id`
            $table->dropIndex(['company_name']); // Drop index for `company_name`
            $table->dropIndex(['function']);     // Drop index for `function`
            $table->dropIndex(['division']);     // Drop index for `division`
        });
    }
};
