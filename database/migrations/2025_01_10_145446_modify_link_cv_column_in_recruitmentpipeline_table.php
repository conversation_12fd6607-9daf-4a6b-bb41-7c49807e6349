<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
           $table->text('Link_cv')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('RecruitmentPipeline', function (Blueprint $table) {
           $table->string('Link_cv')->change();
        });
    }
};
