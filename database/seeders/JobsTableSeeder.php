<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class JobsTableSeeder extends Seeder
{
    public function run()
    {
        DB::table('jobs')->insert([
            ['id' => 5, 'name' => 'Chief Operating Officer', 'minimum_Experience' => NULL, 'ethnicity' => 0, 'step_up' => 1, 'description' => 'Test', 'user_id' => 1, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-07-04 08:18:59', 'updated_at' => '2024-07-04 08:18:59'],
    ['id' => 6, 'name' => 'COO', 'minimum_Experience' => NULL, 'ethnicity' => 0, 'step_up' => 1, 'description' => 'Test', 'user_id' => 1, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-07-04 08:38:09', 'updated_at' => '2024-07-04 08:38:09'],
    ['id' => 28, 'name' => 'HR', 'minimum_Experience' => NULL, 'ethnicity' => 0, 'step_up' => 1, 'description' => 'HR Test', 'user_id' => 1, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-07-17 12:14:39', 'updated_at' => '2024-07-17 12:14:39'],
    ['id' => 32, 'name' => 'Chief Operating Officer', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'COO Succession Plan', 'user_id' => 1, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-08-20 17:40:26', 'updated_at' => '2024-08-20 17:40:26'],
    ['id' => 33, 'name' => 'COO', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'COO Succession Plan', 'user_id' => 1, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-08-20 17:43:29', 'updated_at' => '2024-08-20 17:43:29'],
    ['id' => 34, 'name' => 'COO', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'Group COO Succession', 'user_id' => 1, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-08-21 14:18:14', 'updated_at' => '2024-08-21 14:18:14'],
    ['id' => 39, 'name' => 'CFO', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'Chief Financial Officer - test', 'user_id' => 32, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-09-04 13:10:16', 'updated_at' => '2024-09-04 13:10:16'],
    ['id' => 40, 'name' => 'Chief Risk Officer', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'Bupa is seeking a dynamic Chief Risk Officer', 'user_id' => 37, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-09-13 08:56:22', 'updated_at' => '2024-09-13 09:00:24'],
    ['id' => 42, 'name' => 'fhfh', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'dfd', 'user_id' => 37, 'status' => 'Active', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-09-13 11:06:27', 'updated_at' => '2024-09-13 11:07:38'],
    ['id' => 43, 'name' => 'CPO', 'minimum_Experience' => NULL, 'ethnicity' => 0, 'step_up' => 1, 'description' => 'ffd', 'user_id' => 45, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-09-19 09:11:01', 'updated_at' => '2024-09-19 09:11:01'],
    ['id' => 44, 'name' => 'CIO', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'The Chief Investment Officer ', 'user_id' => 46, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-09-20 07:44:17', 'updated_at' => '2024-09-20 07:45:10'],
    ['id' => 45, 'name' => 'Marketing', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'As the Marketing Director for Samsung UK, you will be responsible for leading and driving the overall marketing strategy to enhance brand visibility and market presence...', 'user_id' => 49, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-09-23 11:03:12', 'updated_at' => '2024-09-23 11:03:12'],
    ['id' => 46, 'name' => 'IP', 'minimum_Experience' => NULL, 'ethnicity' => 0, 'step_up' => 1, 'description' => 'TEst', 'user_id' => 52, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-09-30 11:35:03', 'updated_at' => '2024-09-30 11:35:03'],
    ['id' => 47, 'name' => 'Talent Pool for Marketing Directors', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'fddf', 'user_id' => 55, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-10-01 09:46:52', 'updated_at' => '2024-10-01 09:46:52'],
    ['id' => 48, 'name' => 'fhakjsfjk', 'minimum_Experience' => NULL, 'ethnicity' => 1, 'step_up' => 1, 'description' => 'hfkhfkj', 'user_id' => 55, 'status' => 'Draft', 'mover' => NULL, 'candidate_status' => 'Nothing', 'age' => 0.00, 'last_opened' => NULL, 'shared_with' => NULL, 'created_at' => '2024-10-01 09:48:39', 'updated_at' => '2024-10-01 09:48:39'],
   
        ]);
    }
}
