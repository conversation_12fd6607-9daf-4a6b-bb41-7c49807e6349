<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $titles = ["Product Manager", "Development Manager", "Analyst"];

        $count = DB::table('roles')->count();

        if(!$count){

            foreach ($titles as $key => $title) {
                
                DB::table('roles')->insert([
                    'title' => $title
                ]);

            }
        }

        $vacantRole = DB::table('roles')->where('title', "Vacant")->count(); 

        if(empty($vacantRole)) {
            $vacantRole = DB::table('roles')->insert([
                'title' => "Vacant"
            ]);

            $role = DB::table('roles')->where('title', "Vacant")->first(); 

            DB::table('internal_people')->insert([
                'forename' => "Vacant",
                "surname" => "User",
                "gender" => "Male",
                "role_id" => $role->id,
                "employee_id" => "VEMP001",
                "company_id" => 1,
                "user_id" => 1
            ]);

        }


    }
}
