<?php

namespace Database\Seeders;

use App\Models\InternalPeople;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class InternalPeopleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Number of records to create in total
        $totalRecords = 10000;

        // Batch size for each insert
        $batchSize = 500;

        // Calculate number of batches
        $batches = ceil($totalRecords / $batchSize);

        for ($i = 0; $i < $batches; $i++) {
            InternalPeople::factory()
                ->count($batchSize)
                ->create();
        }
    }
}
