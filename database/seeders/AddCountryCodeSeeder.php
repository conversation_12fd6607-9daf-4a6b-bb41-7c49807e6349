<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Location;



class AddCountryCodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $filePath = public_path('all.json');
        $jsonData = file_get_contents($filePath);
        $data = json_decode($jsonData, true);

        if (!empty($data)) {
            foreach ($data as $item) {
                Location::where(['country_name' => $item['name']])->update(['country_code' => $item['alpha-2']]);
            }
        } else {
            $this->command->info('JSON file is empty or invalid.');
        }

        $queries = [
            "UPDATE `locations` SET `country_code` = 'BO' WHERE `country_name` = 'Bolivia';",
            "UPDATE `locations` SET `country_code` = 'BN' WHERE `country_name` = 'Brunei';",
            "UPDATE `locations` SET `country_code` = 'FK' WHERE `country_name` = 'Falkland Islands (Islas Malvinas)';",
            "UPDATE `locations` SET `country_code` = 'FM' WHERE `country_name` = 'Federated States of Micronesia';",

            "UPDATE `locations` SET `country_code` = 'IR' WHERE `country_name` = 'Iran';",
            "UPDATE `locations` SET `country_code` = 'MK' WHERE `country_name` = 'Macedonia';",
            "UPDATE `locations` SET `country_code` = 'MD' WHERE `country_name` = 'Moldova';",
            "UPDATE `locations` SET `country_code` = 'NL' WHERE `country_name` = 'Netherlands';",

            "UPDATE `locations` SET `country_code` = 'PS' WHERE `country_name` = 'Palestine';",
            "UPDATE `locations` SET `country_code` = 'PN' WHERE `country_name` = 'Pitcairn Islands';",
            "UPDATE `locations` SET `country_code` = 'RU' WHERE `country_name` = 'Russia';",
            "UPDATE `locations` SET `country_code` = 'SH' WHERE `country_name` = 'Saint Helena, Ascension, And Tristan Da Cunha';",

            "UPDATE `locations` SET `country_code` = 'MF' WHERE `country_name` = 'Saint Martin';",
            "UPDATE `locations` SET `country_code` = 'SX' WHERE `country_name` = 'Sint Maarten';",
            "UPDATE `locations` SET `country_code` = 'GS' WHERE `country_name` = 'South Georgia And South Sandwich Islands';",
            "UPDATE `locations` SET `country_code` = 'SJ' WHERE `country_name` = 'Svalbard';",

            "UPDATE `locations` SET `country_code` = 'SY' WHERE `country_name` = 'Syria';",
            "UPDATE `locations` SET `country_code` = 'TW' WHERE `country_name` = 'Taiwan';",
            "UPDATE `locations` SET `country_code` = 'TZ' WHERE `country_name` = 'Tanzania';",
            "UPDATE `locations` SET `country_code` = 'BS' WHERE `country_name` = 'The Bahamas';",

            "UPDATE `locations` SET `country_code` = 'GM' WHERE `country_name` = 'The Gambia';",
            "UPDATE `locations` SET `country_code` = 'V!' WHERE `country_name` = 'U.S. Virgin Islands';",
            "UPDATE `locations` SET `country_code` = 'GB' WHERE `country_name` = 'United Kingdom';",
            "UPDATE `locations` SET `country_code` = 'US' WHERE `country_name` = 'United States';",
            "UPDATE `locations` SET `country_code` = 'VE' WHERE `country_name` = 'Venezuela';",
        ];

        foreach ($queries as $query) {
            DB::statement($query);
        }
    }
}
