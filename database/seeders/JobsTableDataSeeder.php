<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class JobsTableDataSeeder extends Seeder
{
    public function run()
    {

        DB::insert("INSERT INTO `jobs` 
(`id`, `name`, `minimum_experience`, `ethnicity`, `step_up`, `description`, `user_id`, `status`, `mover`, `candidate_status`, `age`, `last_opened`, `shared_with`, `created_at`, `updated_at`) 
VALUES 
(49, 'fadf', NULL, 0, 1, 'fad', 55, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-01 10:05:09', '2024-10-01 10:05:09'),
(50, 'Test Job', NULL, 0, 1, 'asfdasd', 2, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-01 13:01:32', '2024-10-01 13:01:32'),
(51, 'Mar', NULL, 0, 1, 'fdfd', 56, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-01 15:34:05', '2024-10-01 15:34:05'),
(52, 'rer', NULL, 0, 1, 'fadfadf', 56, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-02 07:48:48', '2024-10-02 07:48:48'),
(53, 'Test TP', NULL, 0, 1, 'sDd', 2, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-02 10:00:05', '2024-10-02 10:00:05'),
(54, 'sad', NULL, 0, 1, 'asd', 2, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-02 10:05:46', '2024-10-02 10:05:46'),
(55, 'Actuarial', NULL, 1, 1, 'fdf', 57, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-04 09:26:40', '2024-10-04 09:26:40'),
(56, 'Marketing Directors', NULL, 1, 1, 'Marketing', 59, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-04 15:04:50', '2024-10-04 15:04:50'),
(57, 'Test TCF Debt Solutions', NULL, 0, 1, 'TCF Debt Solutions', 14, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-09 09:53:53', '2024-10-09 09:53:53'),
(58, 'asd', NULL, 0, 1, 'sDad', 61, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-09 15:14:54', '2024-10-09 15:14:54'),
(59, 'Test', NULL, 0, 1, 'asdf', 61, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-11 13:03:44', '2024-10-11 13:03:44'),
(60, 'asda', NULL, 0, 1, 'adffafd', 61, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-11 13:07:48', '2024-10-11 13:07:48'),

(61, 'sDFf', NULL, 0, 1, 'asdafsf', 2, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-11 13:10:05', '2024-10-11 13:10:05'),
(62, 'sa', NULL, 0, 1, 'asdasd', 61, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-11 15:09:59', '2024-10-11 15:09:59'),
(63, 'sfaf', NULL, 0, 1, 'adsgad', 61, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-11 15:10:55', '2024-10-11 15:10:55'),
(64, 'Finance Director', NULL, 1, 1, 'Samsung Electronics Overview....\n\n', 65, 'Active', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-14 14:58:54', '2024-10-15 14:14:52'),
(65, 'Marketing Director', NULL, 1, 1, 'Talent pool for UK', 67, 'Draft', NULL, 'Nothing', 0.00, NULL, '[14]', '2024-10-15 09:42:02', '2024-10-15 09:42:02'),
(66, 'Marketing Director', NULL, 1, 1, 'Test', 68, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-15 13:22:27', '2024-10-15 13:22:27'),
(67, 'MX - Sales', NULL, 1, 1, 'MX Sales - Channel', 1, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-15 14:11:27', '2024-10-15 14:11:27'),
(68, 'MX Sales Talent Pool', NULL, 0, 1, 'B2C', 65, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-17 14:58:59', '2024-10-17 14:58:59'),
(69, 'Marketing', NULL, 1, 1, 'Test Pool', 75, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-22 12:04:11', '2024-10-22 12:04:11'),
(70, 'Test', NULL, 1, 1, 'Test for Marketing', 75, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-25 14:35:03', '2024-10-25 14:35:03'),
(71, 'jkhh', NULL, 0, 1, 'The role of a Chief Executive Office (CEO) encompasses ', 2, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-29 09:40:33', '2024-10-29 09:40:33'),
(72, 'asdad', NULL, 0, 1, 'Chief Executive Office (CEO) Strategic Plan\n\nThe role of a Chief Executive Office (CEO) encompasses setting a clear, strategic direction for the organization, steering growth initiatives, aligning all departments toward shared objectives, and fostering an environment of accountability, innovation, and resilience. This strategic plan for the CEO outlines key pillars that address the current state, organizational challenges, market dynamics, and future aspirations. By focusing on financial growth, people development, operational excellence, and sustainability, the CEO can ensure the organization not only adapts to change but actively drives industry leadership.\n\n1. Vision and Strategic Alignment\nThe CEO\'s vision and mission must be well-articulated, acting as the guiding force for all departments. This begins with revisiting the company’s mission, vision, and values to ensure alignment with market demands and growth ambitions. The CEO will set clear, measurable goals for each department to drive collective progress. An annual strategic planning session will be established with C-level executives and department heads to assess these goals and adjust based on industry shifts, market conditions, and organizational performance. Transparency in communication will be a priority, with monthly updates and accessible documentation to keep all employees informed of the company\'s progress and strategic direction.\n\n2. Financial Performance and Growth\nTo secure financial stability and drive sustainable growth, the CEO will focus on diversifying revenue streams, optimizing pricing strategies, and increasing market share. Key steps will include deepening relationships with existing clients, entering new markets, and leveraging technology to innovate the company’s product offerings. The CEO will initiate quarterly financial reviews to assess revenue growth, profitability, and cost-\nthe CEO can ensure the organization not only adapts to change but actively drives industry leadership.', 2, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-10-29 09:43:01', '2024-10-29 09:43:01'),
(73, 'Marketing Directors', NULL, 1, 1, 'Test', 83, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-11-05 10:33:27', '2024-11-05 10:33:27'),
(74, 'Test', NULL, 1, 1, 'test', 83, 'Draft', NULL, 'Nothing', 0.00, NULL, NULL, '2024-11-05 15:31:26', '2024-11-05 15:31:26');
");

    }
}
