<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\User;



class updateUserEmail extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
         // Updating user email using Eloquent
         $user = User::where('email', '<EMAIL>')->first();

         if ($user) {
             $user->update(['email' => '<EMAIL>']);
         }

    }
}
