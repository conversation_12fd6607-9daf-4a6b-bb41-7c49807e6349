<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;



class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $users = [
            [
                'email' => "<EMAIL>", //"<EMAIL>",
                'name' => 'Tahir'
            ],
        ];

        foreach ($users as $userData) {
            $user = DB::table('users')->where('email', $userData['email'])->first();

            if (empty($user)) {
                DB::table('users')->insert([
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'password' => Hash::make('thesuccessionplan@123'), 
                    'email_verified_at' => Carbon::now(),
                    'image_url' => "Not Provided",
                    "team" => "Admin",
                    "company_id" => 1,
                    "account_id" => 1,
                    'role' => "Admin",
                    "profile_pic" => "Not Provided"
                ]);
            }
        }
    }
}
