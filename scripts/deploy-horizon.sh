#!/bin/bash

# Deployment script for Laravel Horizon Redis Queue Migration
# Based on course materials from /Users/<USER>/Desktop/courses

echo "🚀 Starting Laravel Horizon deployment..."

# Terminate existing Horizon workers gracefully
echo "📝 Terminating existing Horizon workers..."
php artisan horizon:terminate

# Install dependencies
echo "📦 Installing Redis dependencies..."
if ! command -v redis-server &> /dev/null; then
    echo "⚠️  Redis server not found. Please install Redis first:"
    echo "   Ubuntu: sudo apt-get install redis-server"
    echo "   macOS: brew install redis"
    exit 1
fi

# Check if predis is installed
if ! php -m | grep -q redis && ! composer show | grep -q predis; then
    echo "⚠️  Redis PHP extension or predis package not found."
    echo "Installing predis package..."
    composer require predis/predis
fi

# Update environment configuration
echo "⚙️  Updating environment configuration..."
if grep -q "QUEUE_CONNECTION=database" .env; then
    sed -i.bak 's/QUEUE_CONNECTION=database/QUEUE_CONNECTION=redis/' .env
    echo "✅ Updated QUEUE_CONNECTION to redis"
else
    echo "ℹ️  QUEUE_CONNECTION already set or not found in .env"
fi

# Add Redis client setting if not present
if ! grep -q "REDIS_CLIENT=" .env; then
    echo "REDIS_CLIENT=predis" >> .env
    echo "✅ Added REDIS_CLIENT=predis to .env"
fi

# Clear and cache configuration
echo "🔄 Clearing and caching configuration..."
php artisan config:clear
php artisan config:cache

# Install Horizon if not already installed
if ! php artisan list | grep -q horizon; then
    echo "📥 Installing Laravel Horizon..."
    composer require laravel/horizon
    php artisan horizon:install
else
    echo "✅ Laravel Horizon already installed"
fi

# Migrate database if needed (for Horizon tables)
echo "📊 Running database migrations..."
php artisan migrate --force

# Start Horizon
echo "🏁 Starting Laravel Horizon..."
php artisan horizon &

echo "✅ Deployment complete!"
echo ""
echo "📍 Next steps:"
echo "   1. Setup supervisor configuration (see docs/deployment/supervisor-horizon.conf)"
echo "   2. Visit /horizon to monitor your queues"
echo "   3. Test with: php artisan test:redis-queue"
echo ""
echo "📊 Monitor your queues:"
echo "   - Dashboard: http://your-domain/horizon"
echo "   - Redis CLI: redis-cli LLEN laravel_database_queues:default"
echo "   - Logs: tail -f storage/logs/laravel.log"