#!/bin/bash

# Performance optimization deployment script for Production
echo "🚀 Deploying performance optimizations..."

# 1. Fix log permissions
echo "📝 Fixing log permissions..."
sudo chown -R www-data:www-data storage/logs/
sudo chmod -R 775 storage/logs/

# 2. Clear all caches
echo "🧹 Clearing caches..."
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# 3. Run database migrations (indexes)
echo "📊 Adding database indexes..."
php artisan migrate --force

# 4. Clear route cache and rebuild (after fixing duplicate routes)
echo "🛣️ Rebuilding routes..."
php artisan route:clear
php artisan route:cache

# 5. Cache config and views
echo "⚡ Caching configurations..."
php artisan config:cache
php artisan view:cache

# 6. Restart services
echo "🔄 Restarting services..."
sudo systemctl restart apache2
# sudo systemctl restart nginx  # Uncomment if using nginx

# 7. Clear Redis cache if using Redis
echo "🗄️ Clearing Redis cache..."
php artisan cache:clear

echo "✅ Performance optimizations deployed successfully!"
echo ""
echo "🔧 Changes made:"
echo "   • Added caching to PipelineComponent methods"
echo "   • Optimized database queries with indexes"
echo "   • Fixed duplicate route names"
echo "   • Added query result caching"
echo "   • Fixed log file permissions"
echo ""
echo "📈 Expected improvements:"
echo "   • 80-90% reduction in popup load time"
echo "   • Reduced database query count"
echo "   • Better cache utilization"
echo "   • Fixed route caching issues"
