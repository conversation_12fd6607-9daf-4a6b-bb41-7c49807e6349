<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\SuccessionBuilderController;
use App\Http\Controllers\CompanyInsightController;
use App\Http\Controllers\DatacenterController;
use App\Http\Controllers\PeopleController;
use App\Http\Controllers\planController;
use App\Http\Controllers\SuccPeopController;
use App\Http\Controllers\InternalPeopleController;
use App\Http\Controllers\InternalSuccessorController;
use App\Http\Controllers\FinalController;
use App\Http\Controllers\AiController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\UpgradeController;
use App\Http\Controllers\NewuserController;
use App\Http\Controllers\JobController;
use App\Http\Controllers\UploadController;
use App\Http\Controllers\JobUploadController;
use App\Http\Controllers\shortPeopController;
use App\Http\Controllers\longPeopController;
use App\Http\Controllers\peopleJobController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\TalentPoolai;
use App\Livewire\ResetPassword;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Redirect;
use App\Http\Controllers\AiChatbot;
use App\Http\Controllers\MyOrganizationController;
use App\Http\Controllers\PDFController;
use App\Http\Controllers\RecruitmentController;
use App\Http\Controllers\SummaryTableController;
use Illuminate\Http\Request;
use App\Models\InternalPeople;
use App\Http\Middleware\StoreLastVisitedPage;
use App\Livewire\PlanOrgChart;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('auth/login');
})->middleware('guest')->name('login');
Route::get('/forgot-password', function () {
    return view('auth/forgot-password');
})->middleware('guest')->name('forgot-password');

Route::get('/reset-password/{token}', function (string $token) {
    return view('auth.passwords.reset', ['token' => $token]);
})->middleware('guest')->name('password.reset');

Route::get('/clear-cache', function () {
    Artisan::call('cache:clear');
    Artisan::call('route:clear');
    Artisan::call('config:clear');
    Artisan::call('view:clear');
    
    return Redirect::back()->with('success', 'All cache cleared successfully.');
});


// Resource Controllers
Route::middleware(['auth'])->resource('home', HomeController::class);

// Routes for Succession Planning
Route::middleware(['auth'])->resource('plan', planController::class);
Route::middleware(['auth'])->resource('plan.success_people', SuccPeopController::class);
Route::middleware(['auth'])->resource('plan.final', FinalController::class);
Route::middleware(['auth'])->resource('plan.people', PeopleController::class);
Route::middleware(['auth'])->resource('plan.upload', UploadController::class);
Route::middleware(['auth'])->resource('plan.internalsuccess', InternalSuccessorController::class);

Route::middleware(['auth'])->resource('builder', SuccessionBuilderController::class);
Route::middleware(['auth'])->resource('internalpeople', InternalPeopleController::class);
Route::middleware(['auth'])->get('/paginated-internal-peoples', [InternalPeopleController::class, 'getPaginatedInternalPeoples']);
Route::middleware(['auth'])->get('/paginated-companies', [CompanyController::class, 'fetchCompanies'])->name('company.options');


// Routes for Job Building
Route::middleware(['auth'])->resource('job', JobController::class);
Route::middleware(['auth'])->resource('job.short', shortPeopController::class);
Route::middleware(['auth'])->resource('job.long', longPeopController::class);
Route::middleware(['auth'])->resource('job.upload', JobUploadController::class);
Route::middleware(['auth'])->resource('job.people', peopleJobController::class);

// Other routes
Route::middleware(['auth'])->resource('team', TeamController::class);
Route::middleware(['auth'])->resource('users', UserController::class);
Route::middleware(['auth'])->resource('datacenter', DatacenterController::class);
Route::middleware(['auth'])->get('/datacenter-people', [DatacenterController::class, 'people'])->name('datacenter.people');
Route::middleware(['auth'])->resource('company', CompanyInsightController::class);
Route::POST('/users/end-session', [UserController::class, 'endSession'])->name('users.end-session');

// Path to Syre
Route::post('/ai-chat/send-message', [AiController::class, 'sendMessage'])
    ->middleware(['auth', 'ai.rate.limit:openai'])
    ->name('ai-chat.send-message');

// Path to function that generates powerpoint
Route::get('/plan/{id}/download-powerpoint', [FinalController::class, 'downloadPowerPoint'])->name('final.download');

// Routes for emails
Route::get('/send-request/{Option}', [UpgradeController::class, 'sendUpgradeRequest'])->name('upgrade.sendRequest');
Route::get('/invite/sendInvite/{email}/{id}/{company}/{role}', [NewuserController::class, 'sendInvite'])->name('invite.sendInvite');

Route::get('/invite/admin/{data}', [NewuserController::class, 'adminInvite'])->name('invite.adminInvite');
// Route::get('/chatbot', [AiChatbot::class, 'index'])->name('chatbot.url');

Route::post('/organisation/delete', [MyOrganizationController::class, 'deleteOrganisation'])->name('delete.organisation');

Route::post('/my-organization/save/careerhistory', [MyOrganizationController::class, 'SaveCareerHistory'])->name('saveorganization.careerhistory');

Route::post('/my-organization/update/profile', [MyOrganizationController::class, 'UpdateCandidateProfile'])->name('organization.UpdateProfile');

Route::post('/my-organization/save/potential', [MyOrganizationController::class, 'UpdateCandidatePotential'])->name('organization.UpdatePotential');

Route::post('/my-organization/save/candidatepopup', [MyOrganizationController::class, 'UpdateCandidatePopup'])->name('organization.Updatecandidatepopup');


//chatbot to create Plans
Route::middleware(['auth'])->group(function () {
    Route::get('/chatbot', [AiChatbot::class, 'index'])->name('chatbot.url');
    Route::post('/ai-chat/send-message', [AiController::class, 'sendMessage'])
        ->middleware('ai.rate.limit:openai')
        ->name('ai-chat.send-message');
    Route::get('/api/roles', [SummaryTableController::class, 'getRoles']);
    Route::get('/my-organization', [MyOrganizationController::class, 'index'])->name('myorg.index');
    // Route::get('/my-organization', [MyOrganizationController::class, 'myOrg'])->name('myorgs.myOrg');
    Route::post('/api/update-internal-people', [InternalPeopleController::class, 'update']);
    Route::get('/organisation-details/{id}', [MyOrganizationController::class, 'viewOrganisation'])->name('myorg.details');
    Route::post('/my-organization/save', [MyOrganizationController::class, 'saveOrganization'])->name('myorg.save');
    Route::post('/save-profile-ninebox', [MyOrganizationController::class, 'saveProfile_NineBoxData'])->name('myorg.profileNineBox');
    Route::post('/save-profile-competencies', [MyOrganizationController::class, 'saveProfile_CompetencyData'])->name('myorg.profileCompetency');
    Route::post('/ai-chat/create-plan', [AiController::class, 'sendMessage'])
        ->middleware('ai.rate.limit:openai')
        ->name('ai-chat.create-plan');
    //Route::get('/plan-org-chart', PlanOrgChart::class)->name('plan-org-chart');
    Route::get('/plan-org-chart/{id}', [SuccPeopController::class, 'view_PlanOrgChart'])->name('plan-org-chart.view');
    Route::get('/download-pdf/{planId}', [PDFController::class, 'download'])->name('download.pdf');
});

Route::middleware(['auth'])->get('/Create-Plans', [AiChatbot::class, 'index'])->name('myChatbot.url');

//chatbot to create Talent Pools
Route::middleware(['auth'])->group(function () {
    Route::get('/talentpoolai', [TalentPoolai::class, 'index'])->name('TalentPoolai.url');
    Route::post('/talentpoolai/send-message', [TalentPoolai::class, 'sendMessage'])
        ->middleware('ai.rate.limit:anthropic')
        ->name('talentpoolai.send-message');
    Route::get('/api/roles', [SummaryTableController::class, 'getRoles']);
});


Route::post('/my-chatbot/send-message', [AiChatbot::class, 'sendMessage'])
    ->middleware(['auth', 'ai.rate.limit:anthropic'])
    ->name('my-chatbot.send-message');
Route::get('/recruitment', [RecruitmentController::class, 'index'])->name('recruitment.index');
Route::post('/recruitment/save', [RecruitmentController::class, 'store'])->name('recruitment.store');
Route::get('/recruitment/{id}', [RecruitmentController::class, 'recruitmentSchedule'])->name('recruitment.schedule');
Route::post('/recruitment/stagename', [RecruitmentController::class, 'handlerUpdateStagename'])->name('recruitment.stagename');
Route::post('/recruitment/delete', [RecruitmentController::class, 'handlerDeleteRecruitment'])->name('recruitment.delete');
Route::post('/Create-Plans/send-message', [AiChatbot::class, 'sendMessage'])
    ->middleware(['auth', 'ai.rate.limit:anthropic'])
    ->name('my-chatbot.send-message');
Route::post('/recruitment/saveinterviewdate', [RecruitmentController::class, 'saveinterviewdate'])->name('recruitment.saveinterviewdate');
Route::post('/recruitment/archive', [RecruitmentController::class, 'recruitmentArchive'])->name('recruitment.recruitmentarchive');
Route::post('/recruitment/interviewschedule/save', [RecruitmentController::class, 'interviewschedulesave'])->name('recruitment.interviewschedulesave');
Route::post('/recruitment/candidate/uploadcv', [RecruitmentController::class, 'Handlercandidateuploadcv'])->name('recruitment.candidateuploadcv');
Route::post('/recruitment/candidatedetails/save', [RecruitmentController::class, 'Handlercandidatedetailsave'])->name('recruitment.candidatedetailsave');

Route::post('/recruitment/candidate/remove', [RecruitmentController::class, 'removecandidateHandler'])->name('recruitment.removecandidate');
Route::post('/recruitment/candidate/sendemail', [RecruitmentController::class, 'candidateSendEmail'])->name('recruitment.candidatesendemail');

Route::post('/recruitment/candidate/savenote', [RecruitmentController::class, 'SavecandidateNote'])->name('recruitment.candidatesavenote');

Route::post('/recruitment/shareproject', [RecruitmentController::class, 'Saveshareproject'])->name('recruitment.Saveshareproject');
Route::post('/recruitment/saveprogress', [RecruitmentController::class, 'SaveprogressHandler'])->name('recruitment.saveprogress');
Route::post('/recruitment/create_new_stage', [RecruitmentController::class, 'create_new_stage'])->name('recruitment.create_new_stage');


Route::post('/my-organization/nineboxgrid/save', [MyOrganizationController::class, 'SaveNineBoxGrid'])->name('myorg.savenineboxgrid');

Route::post('/home/<USER>/notification', [HomeController::class, 'HandlerdeleteNotification'])->name('home.delete_notification');
Route::post('/home/<USER>/all/notification', [HomeController::class, 'HandlerDeleteAllNotification'])->name('home.delete_allnotification');

Route::post('/my-organization/individual/save', [MyOrganizationController::class, 'SaveIndividual'])->name('myorg.saveindividual');

Route::get('/my-organization/download-csv', [MyOrganizationController::class, 'downloadCSV']);

Route::post('/my-organization/uploadCSV', [MyOrganizationController::class, 'uploadCSV']);

Route::post('/my-organization/sharepeople/save', [MyOrganizationController::class, 'SharePeopleSave']);
Route::post('/my-organization/save/ready_future', [MyOrganizationController::class, 'SaveReadyFuture']);

Route::post('/my-organization/save/performance', [MyOrganizationController::class, 'SavePerformance']);


Route::middleware(['storeLastVisitedPage', 'auth'])->group(function () {
	Route::get('/Create-Plans/{ID?}', [AiChatbot::class, 'index'])->name('myChatbot.url.custom');
});

Route::get('/download-file/{fileurl}', [RecruitmentController::class, 'downloadCandidateCV'])
    ->middleware('auth');

Route::post('/plan-development/submit', [MyOrganizationController::class, 'PlanDevelopmentAI'])->name('plan.developmentAI');   

Route::post('/plan-development/download-word', [MyOrganizationController::class, 'PlanDevelopmentDownloadWord'])->name('plan.developmentdownloadPDF');  

/*Route::get('/setup', function () {
    return view('Setup');
})->name('setup'); 
*/

