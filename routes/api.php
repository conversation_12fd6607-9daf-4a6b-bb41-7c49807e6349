<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Services\ErrorHandling\ErrorLogger;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Client-side error logging endpoint
Route::post('/log-client-error', function (Request $request) {
    $data = $request->all();
    
    // Validate required fields before accessing them
    $type = $data['type'] ?? 'Unknown Error';
    $url = $data['url'] ?? 'Unknown URL';
    
    // Log the client-side error with appropriate context
    ErrorLogger::log(
        "Client Error: {$type} on {$url}",
        $data,
        ErrorLogger::CATEGORY_UI,
        'warning'
    );
    
    return response()->json(['status' => 'logged']);
});