# Succession Plan AI

Succession Plan AI is a comprehensive web platform developed with Laravel and Livewire, designed to assist organizations in managing succession planning, talent pipelines, and related processes.

---

## Local Development Setup

### 1. Install XAMPP
- Download and install [XAMPP 8.2.12](https://www.apachefriends.org/download.html) (this version is known to work well).
- Start the following services using the XAMPP Control Panel:
  - **Apache**
  - **MySQL**

### 2. Import the Succession Plan Database
1. Open [http://localhost/phpmyadmin](http://localhost/phpmyadmin) in your browser.
2. Create a new database named `succession_plan`.
3. Go to the **Import** tab.
4. Upload and import the `.sql` file located in your Laravel project directory.

**MySQL Port Conflicts (Windows):**
- Open **Services** (`Win + R`, type `services.msc`, and press Enter).
- Locate the **MySQL** service and verify it is using port `3306` (the default MySQL port).
- If another process is using port `3306`, either stop that service or change the MySQL port in the XAMPP Control Panel > MySQL > Config > my.ini.

### 3. Update the User Seeder
- Edit `database/seeders/UserSeeder.php`.
- Replace the placeholder values for `email`, `name`, and `password` with your own details:
  ```php
  $users = [
      [
          'email' => "<EMAIL>", // Update this to your email
          'name' => 'Your Name',              // Update this to your name
          'password' => 'your_password'       // Update this to your desired password
      ],
  ];
  ```
- This ensures your admin account is created with your credentials when seeding the database.

### 4. Seed the Database
- Open a terminal in your project directory.
- Run:
  ```bash
  php artisan db:seed
  ```
- After the command completes, go to phpMyAdmin and check the `users` table in the `succession_plan` database to confirm your username appears.

### 5. Configure Environment Variables
- Obtain the original `.env` file from the admin developer.
- Copy this `.env` file into your project root directory (`successionplanai`).
- This file contains all necessary environment variables for your application to run correctly.

> **Note:** In some cases, you may need to uncomment the following lines in your `php.ini` file:
> ```
> ;extension=zip
> ;extension=fileinfo
> ;extension=gd
> ```
> Remove the `;` at the start of each line, then save the file and restart Apache from the XAMPP Control Panel.

### 6. Install Frontend Dependencies and Build Assets
- In your project directory, run:
  ```bash
  npm install
  ```
- After installation completes, build the frontend assets:
  ```bash
  npm run build
  ```

> **Note:**
> - You may see warnings about deprecated packages or vulnerabilities after running `npm install`.
> - To address common issues, you can run:
>   ```bash
>   npm audit fix
>   ```
> - For more details or to address all issues (including those that may introduce breaking changes), run:
>   ```bash
>   npm audit fix --force
>   ```
> - Review warnings and errors as needed, especially before deploying to production.

### 7. Start the Application
- In your project directory, run:
  ```bash
  php artisan serve
  ```
- Open the application in your browser using the URL shown in the terminal (usually http://127.0.0.1:8000).
- When prompted, enter the authentication code shown in your notifications to complete the login process.

---

## Contribution Guidelines

- Submit all changes via Pull Request (PR) to the `main` or `staging` branches.
- Direct pushes to `main` and `staging` are prohibited.
- Each PR must receive at least one approval before merging.
- **Paul Elliot** (`<EMAIL>`) is designated as the default reviewer for all PRs.
- Force-push operations and branch deletions are disabled on protected branches.
- Ensure every PR is linked to the appropriate Jira issue when applicable.

---

## Jira Integration Workflow

- Begin work by selecting tasks from the Jira Kanban board.
- Name feature branches after the associated Jira issue key (e.g., `feature/PROJ-123-description`).
- Reference the Jira issue key in all commit messages, PR titles, and PR descriptions.
- Bitbucket and Jira will automatically link all code activity—including branches, commits, and PRs—to the correct Jira issue.
- Development activity will be visible within the Jira issue’s development panel.
- Jira Automation rules (if configured) will update issue statuses based on Bitbucket activity (e.g., auto-transitioning issues from “To Do” to “In Progress”).

This process ensures full traceability of code changes to Jira issues and keeps the project board current.

---